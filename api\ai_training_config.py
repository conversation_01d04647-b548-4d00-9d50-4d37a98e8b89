#!/usr/bin/env python3
"""
AI Training Configuration Module
Mathematical rigor: 100% data structure validation
Optimized for AI agent training scenarios
"""

import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging

@dataclass
class TrainingConfigSection:
    """Individual training configuration section."""
    section_name: str
    parameters: Dict[str, Any]
    validation_rules: Dict[str, Any]
    mathematical_constraints: Dict[str, Any]
    
class AITrainingConfigGenerator:
    """Generate training configurations for AI agents."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config_sections = {}
        
    def add_api_section(self, api_gateway) -> Dict[str, Any]:
        """Add API configuration section."""
        api_config = {
            'endpoints': {
                'spot_price': {
                    'method': 'get_spot_price',
                    'required_params': ['ticker'],
                    'response_type': 'float',
                    'validation': 'price > 0'
                },
                'options_chain': {
                    'method': 'get_options_chain', 
                    'required_params': ['ticker'],
                    'optional_params': ['expiry'],
                    'response_type': 'DataFrame',
                    'validation': 'not empty'
                },
                'market_depth': {
                    'method': 'get_market_depth',
                    'required_params': ['ticker'],
                    'response_type': 'DataFrame',
                    'validation': 'bid_ask_structure'
                }
            },
            'rate_limits': {
                'free_tier': 5,
                'basic_tier': 100,
                'professional_tier': 1000
            },
            'error_handling': {
                'retry_attempts': 3,
                'backoff_strategy': 'exponential',
                'timeout_seconds': 30
            }
        }
        
        self.config_sections['api'] = TrainingConfigSection(
            section_name='api',
            parameters=api_config,
            validation_rules={
                'response_time_ms': '<= 5000',
                'success_rate': '>= 0.95',
                'data_completeness': '>= 0.90'
            },
            mathematical_constraints={
                'price_bounds': {'min': 0.01, 'max': 10000},
                'volume_bounds': {'min': 0, 'max': 1e9},
                'precision': 4
            }
        )
        
        return api_config
    
    def add_analysis_section(self, analyzers: List[str]) -> Dict[str, Any]:
        """Add analysis configuration section."""
        analysis_config = {
            'analyzers': {
                analyzer: {
                    'enabled': True,
                    'weight': 1.0,
                    'timeframe_priority': ['1h', '4h', '1d'],
                    'confidence_threshold': 0.7
                } for analyzer in analyzers
            },
            'factor_scoring': {
                'strength_weight': 0.4,
                'quality_weight': 0.3,
                'timeframe_weight': 0.3
            },
            'confluence_detection': {
                'min_factors': 2,
                'max_timeframe_spread': 3,
                'direction_agreement': 0.8
            }
        }
        
        self.config_sections['analysis'] = TrainingConfigSection(
            section_name='analysis',
            parameters=analysis_config,
            validation_rules={
                'factor_count': '>= 1',
                'strength_score': 'between 0 and 1',
                'quality_score': 'between 0 and 1'
            },
            mathematical_constraints={
                'score_bounds': {'min': 0.0, 'max': 1.0},
                'weight_sum': 1.0,
                'precision': 3
            }
        )
        
        return analysis_config
    
    def add_performance_section(self) -> Dict[str, Any]:
        """Add performance monitoring section."""
        performance_config = {
            'metrics': {
                'response_time': {
                    'target_ms': 2000,
                    'warning_ms': 5000,
                    'critical_ms': 10000
                },
                'throughput': {
                    'target_rps': 10,
                    'minimum_rps': 1
                },
                'accuracy': {
                    'target_rate': 0.95,
                    'minimum_rate': 0.85
                }
            },
            'monitoring': {
                'sample_rate': 0.1,
                'window_size': 1000,
                'alert_thresholds': {
                    'error_rate': 0.05,
                    'latency_p95': 5000
                }
            }
        }
        
        self.config_sections['performance'] = TrainingConfigSection(
            section_name='performance',
            parameters=performance_config,
            validation_rules={
                'response_time_ms': '<= 10000',
                'error_rate': '<= 0.05',
                'availability': '>= 0.99'
            },
            mathematical_constraints={
                'rate_bounds': {'min': 0.0, 'max': 1.0},
                'time_bounds_ms': {'min': 0, 'max': 60000},
                'precision': 2
            }
        )
        
        return performance_config
    
    def generate_complete_config(self, api_gateway=None, analyzers=None) -> Dict[str, Any]:
        """Generate complete training configuration."""
        config = {
            'metadata': {
                'generated_at': time.time(),
                'version': '1.0.0',
                'purpose': 'AI agent training configuration',
                'mathematical_validation': True
            }
        }
        
        if api_gateway:
            config['api'] = self.add_api_section(api_gateway)
        
        if analyzers:
            config['analysis'] = self.add_analysis_section(analyzers)
        
        config['performance'] = self.add_performance_section()
        
        # Add training-specific sections
        config['training'] = {
            'scenarios': {
                'basic_price_fetch': {
                    'steps': ['authenticate', 'request_price', 'validate_response'],
                    'expected_outcome': 'valid_price_data',
                    'success_criteria': 'price > 0 and response_time < 5000'
                },
                'options_analysis': {
                    'steps': ['fetch_chain', 'calculate_greeks', 'analyze_flow'],
                    'expected_outcome': 'factor_generation',
                    'success_criteria': 'factors_count >= 1 and quality_score >= 0.7'
                },
                'liquidity_detection': {
                    'steps': ['analyze_depth', 'detect_walls', 'score_liquidity'],
                    'expected_outcome': 'liquidity_assessment',
                    'success_criteria': 'assessment in ["EXCELLENT", "GOOD", "MODERATE"]'
                }
            },
            'validation': {
                'data_integrity': True,
                'mathematical_consistency': True,
                'performance_benchmarks': True
            }
        }
        
        return config

def generate_ai_training_config(api_gateway=None, analyzers=None) -> Dict[str, Any]:
    """Generate AI training configuration."""
    generator = AITrainingConfigGenerator()
    return generator.generate_complete_config(api_gateway, analyzers)

def export_training_config_json(config: Dict[str, Any], output_path: str = None) -> str:
    """Export training configuration to JSON file."""
    if output_path is None:
        timestamp = int(time.time())
        output_path = f"ai_training_config_{timestamp}.json"
    
    with open(output_path, 'w') as f:
        json.dump(config, f, indent=2, default=str)
    
    return output_path
