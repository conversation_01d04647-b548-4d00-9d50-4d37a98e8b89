#!/usr/bin/env python3
"""
CORE Flow Detection System - Analyzers Package

Clean, stripped analyzers for flow detection:
- Flow Physics: Mathematical derivatives (velocity, acceleration, jerk)
- Volume Analysis: POC/VAH/VAL, HVN detection, flow concentration
- Liquidity Analysis: Accumulation/distribution, campaign stages
- GEX Analysis: Gamma exposure, zero-gamma levels, dealer positioning

All analyzers are optimized for AI training with minimal dependencies.
"""

from analyzers.flow_physics import FlowPhysicsAnalyzer
from analyzers.volume_analysis import VolumeAnalyzer  
from analyzers.liquidity_analysis import LiquidityAnalyzer
from analyzers.gex_analysis import GEXAnalyzer

__all__ = [
    'FlowPhysicsAnalyzer',
    'VolumeAnalyzer',
    'LiquidityAnalyzer', 
    'GEXAnalyzer'
]

# Analyzer factory for easy instantiation
def create_analyzer(analyzer_type: str, config: dict = None):
    """
    Factory function to create analyzer instances.
    
    Args:
        analyzer_type: Type of analyzer ('flow_physics', 'volume', 'liquidity', 'gex')
        config: Optional configuration dictionary
        
    Returns:
        Analyzer instance
    """
    analyzers = {
        'flow_physics': FlowPhysicsAnalyzer,
        'volume': VolumeAnalyzer,
        'liquidity': LiquidityAnalyzer,
        'gex': GEXAnalyzer
    }
    
    if analyzer_type not in analyzers:
        raise ValueError(f"Unknown analyzer type: {analyzer_type}")
    
    return analyzers[analyzer_type](config)

def get_available_analyzers():
    """Get list of available analyzer types."""
    return ['flow_physics', 'volume', 'liquidity', 'gex']
