{"test_summary": {"total_tests": 10, "passed_tests": 8, "failed_tests": 2, "success_rate": 80.0, "total_execution_time": 36.521, "timestamp": "2025-06-22T22:39:34.606250"}, "detailed_results": {"Enhanced Data Agent": {"status": "FAIL", "error": "Enhanced Data Agent test failed: argument of type 'NoneType' is not iterable", "traceback": "Traceback (most recent call last):\n  File \"D:\\script-work\\CORE\\test_mcp_agent_integration.py\", line 72, in test_enhanced_data_agent\n    if has_data and self.test_ticker in result[\"data\"]:\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: argument of type 'NoneType' is not iterable\n"}, "Signal Generator": {"status": "PASS", "execution_time": 16.78, "has_real_time_capability": true, "has_enhancement": false, "signal_structure_valid": true, "confidence_value": 75.0, "details": "Real-time capable: True, Enhanced: False"}, "Chart Generator": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "has_enhanced_color_scheme": true, "details": "Real-time capable: True, Agent: True"}, "Risk Guard": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Order Router": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Anomaly Detector": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "IV Dynamics": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Flow Physics": {"status": "PARTIAL", "has_real_time_capability": false, "has_real_time_agent": false, "details": "Real-time capable: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>"}, "Math Validator": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Output Coordinator": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}}}