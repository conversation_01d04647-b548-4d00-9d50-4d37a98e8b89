#!/usr/bin/env python3
"""
Historical Statistics Builder
Updates rolling statistics for Greek/IV anomaly detection
Run nightly to maintain statistical baselines
"""

import os
import sys
import glob
import logging
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))


class HistoricalStatsBuilder:
    """Builds and maintains rolling statistics for anomaly detection"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.lookback_days = 252  # 1 year rolling window
        
    def run(self, lookback_days=252):
        """
        Build rolling statistics from all feature files
        
        Args:
            lookback_days (int): Rolling window size in days
        """
        self.lookback_days = lookback_days
        
        try:
            self.logger.info(f"Building historical stats with {lookback_days} day window")
            
            # Collect all feature files
            feature_files = glob.glob("data/features/*.parquet")
            
            if not feature_files:
                raise FileNotFoundError("No feature files found in data/features/")
            
            self.logger.info(f"Processing {len(feature_files)} feature files")
            
            # Process each file and collect data
            all_data = []
            for file_path in feature_files:
                try:
                    df = self._process_feature_file(file_path)
                    if not df.empty:
                        all_data.append(df)
                except Exception as e:
                    self.logger.warning(f"Failed to process {file_path}: {e}")
            
            if not all_data:
                raise ValueError("No valid feature data found")
            
            # Combine all data
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # Calculate rolling statistics
            stats_df = self._calculate_rolling_stats(combined_df)
            
            # Save statistics
            output_file = "data/hist_stats.parquet"
            stats_df.to_parquet(output_file)
            
            self.logger.info(f"Historical statistics saved to {output_file}")
            self.logger.info(f"Stats shape: {stats_df.shape}")
            
            return {
                "status": "SUCCESS",
                "output_file": output_file,
                "files_processed": len(feature_files),
                "stats_shape": stats_df.shape,
                "date_range": {
                    "start": combined_df['date'].min(),
                    "end": combined_df['date'].max()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Failed to build historical stats: {e}")
            return {
                "status": "ERROR",
                "error": str(e)
            }
    
    def _process_feature_file(self, file_path):
        """Process individual feature file"""
        df = pd.read_parquet(file_path)
        
        # Extract ticker from filename
        ticker = Path(file_path).stem.split('_')[0]
        
        # Select key Greek/IV features for anomaly detection
        feature_cols = [
            "iv_rank", "iv_roc",
            "gamma_calc_mean", "gamma_calc_mean_roc",
            "vanna_calc_mean", "vanna_calc_mean_roc", 
            "charm_calc_mean", "charm_calc_mean_roc"
        ]
        
        # Use available columns (some may not exist in all files)
        available_cols = ['date'] + [col for col in feature_cols if col in df.columns]
        
        if len(available_cols) <= 1:  # Only date column
            self.logger.warning(f"No Greek/IV features found in {file_path}")
            return pd.DataFrame()
        
        # Select and clean data
        df_clean = df[available_cols].copy()
        df_clean['ticker'] = ticker
        
        # Ensure date column exists and is datetime
        if 'date' not in df_clean.columns:
            if 't' in df.columns:
                df_clean['date'] = pd.to_datetime(df['t'])
            else:
                # Use index as date if no date column
                df_clean['date'] = pd.date_range(start='2023-01-01', periods=len(df_clean), freq='D')
        
        # Sort by date
        df_clean = df_clean.sort_values('date')
        
        return df_clean
    
    def _calculate_rolling_stats(self, df):
        """Calculate rolling statistics for anomaly detection"""
        # Group by ticker and calculate rolling stats
        stats_list = []
        
        for ticker in df['ticker'].unique():
            ticker_df = df[df['ticker'] == ticker].sort_values('date')
            
            # Get numeric columns only
            numeric_cols = ticker_df.select_dtypes(include=[np.number]).columns.tolist()
            numeric_cols = [col for col in numeric_cols if col not in ['ticker']]
            
            if not numeric_cols:
                continue
            
            # Calculate rolling statistics
            ticker_stats = pd.DataFrame()
            ticker_stats['date'] = ticker_df['date']
            ticker_stats['ticker'] = ticker
            
            for col in numeric_cols:
                if col in ticker_df.columns:
                    rolling_data = ticker_df[col].rolling(window=self.lookback_days, min_periods=20)
                    
                    ticker_stats[f"{col}_mean"] = rolling_data.mean()
                    ticker_stats[f"{col}_std"] = rolling_data.std()
                    ticker_stats[f"{col}_min"] = rolling_data.min()
                    ticker_stats[f"{col}_max"] = rolling_data.max()
                    ticker_stats[f"{col}_q05"] = rolling_data.quantile(0.05)
                    ticker_stats[f"{col}_q25"] = rolling_data.quantile(0.25)
                    ticker_stats[f"{col}_q75"] = rolling_data.quantile(0.75)
                    ticker_stats[f"{col}_q95"] = rolling_data.quantile(0.95)
            
            # Add current values for reference
            for col in numeric_cols:
                if col in ticker_df.columns:
                    ticker_stats[f"{col}_current"] = ticker_df[col].values
            
            stats_list.append(ticker_stats)
        
        # Combine all tickers
        if stats_list:
            combined_stats = pd.concat(stats_list, ignore_index=True)
            
            # Remove rows with all NaN (insufficient data)
            combined_stats = combined_stats.dropna(how='all', subset=[
                col for col in combined_stats.columns 
                if col not in ['date', 'ticker']
            ])
            
            return combined_stats
        else:
            return pd.DataFrame()
    
    def validate_stats(self, stats_file="data/hist_stats.parquet"):
        """Validate generated statistics file"""
        try:
            if not os.path.exists(stats_file):
                return {"valid": False, "error": "Stats file does not exist"}
            
            df = pd.read_parquet(stats_file)
            
            if df.empty:
                return {"valid": False, "error": "Stats file is empty"}
            
            # Check for required columns
            required_patterns = ['_mean', '_std', '_q05', '_q95']
            has_required = any(
                any(pattern in col for col in df.columns)
                for pattern in required_patterns
            )
            
            if not has_required:
                return {"valid": False, "error": "Missing required statistical columns"}
            
            # Check data quality
            latest_data = df.tail(10)
            nan_percentage = latest_data.isnull().sum().sum() / (latest_data.shape[0] * latest_data.shape[1])
            
            return {
                "valid": True,
                "shape": df.shape,
                "date_range": {
                    "start": df['date'].min() if 'date' in df.columns else None,
                    "end": df['date'].max() if 'date' in df.columns else None
                },
                "nan_percentage": nan_percentage,
                "columns": len(df.columns)
            }
            
        except Exception as e:
            return {"valid": False, "error": str(e)}


def main():
    """Command line interface for historical stats builder"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Build historical statistics for anomaly detection")
    parser.add_argument("--lookback", type=int, default=252, help="Rolling window size in days")
    parser.add_argument("--validate", action="store_true", help="Validate existing stats file")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    builder = HistoricalStatsBuilder()
    
    if args.validate:
        # Validate existing stats
        validation_result = builder.validate_stats()
        print("Historical Stats Validation:")
        print(f"Valid: {validation_result['valid']}")
        
        if validation_result['valid']:
            print(f"Shape: {validation_result['shape']}")
            print(f"Date Range: {validation_result['date_range']}")
            print(f"NaN Percentage: {validation_result['nan_percentage']:.2%}")
            print(f"Columns: {validation_result['columns']}")
        else:
            print(f"Error: {validation_result['error']}")
        
        return 0 if validation_result['valid'] else 1
    
    else:
        # Build stats
        result = builder.run(lookback_days=args.lookback)
        
        if result["status"] == "SUCCESS":
            print("SUCCESS: Historical statistics built")
            print(f"Output: {result['output_file']}")
            print(f"Files processed: {result['files_processed']}")
            print(f"Stats shape: {result['stats_shape']}")
            print(f"Date range: {result['date_range']['start']} to {result['date_range']['end']}")
            return 0
        else:
            print(f"ERROR: {result['error']}")
            return 1


if __name__ == "__main__":
    sys.exit(main())
