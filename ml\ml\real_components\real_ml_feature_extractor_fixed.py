"""
Real ML Feature Extractor - PERFECT ALIGNMENT WITH CURRENT_FEATURES.TXT

This module implements the REAL feature extraction pipeline that converts
FactorData objects into the EXACT features required by trained models.

CRITICAL: This matches current_features.txt EXACTLY, feature by feature.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import factor specification for real FactorData handling
try:
    from ..factor_specification import FactorData, FactorType, DirectionBias, TimeFrame
except ImportError:
    try:
        from factor_specification import FactorData, FactorType, DirectionBias, TimeFrame
    except ImportError:
        # Emergency fallback
        FactorData = dict
        FactorType = None
        DirectionBias = None
        TimeFrame = None

logger = logging.getLogger(__name__)

class RealMLFeatureExtractor:
    """
    Real feature extractor that produces EXACTLY the features in current_features.txt
    for trained model consumption. 100% ALIGNMENT GUARANTEED.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # EXACT feature list from current_features.txt - DO NOT MODIFY
        self.required_features = [
            'rel_volume_5',
            'rel_volume_10',
            'rel_volume_20',
            'rel_volume_50',
            'volume_change',
            'volume_acceleration',
            'volume_volatility_5',
            'volume_volatility_10',
            'volume_volatility_20',
            'price_volume_corr_5',
            'price_volume_corr_10',
            'price_volume_corr_20',
            'vwap_daily',
            'liquidity_ratio',
            'amihud_illiquidity',
            'volume_skew',
            'options_put_call_ratio',
            'distance_to_call_oi',
            'distance_to_put_oi',
            'call_oi_imbalance',
            'put_oi_imbalance',
            'iv_skew',
            'iv_skew_ratio',
            'distance_to_poc',
            'in_value_area',
            'distance_to_value_area',
            'value_area_width',
            'nearby_volume_density',
            'nearby_node_count',
            'nearby_node_density',
            'volume_profile_skew',
            'volume_liquidity_score',
            'current_gex',
            'normalized_gex',
            'gex_impact',
            'distance_to_zero_gamma',
            'zero_gamma_direction',
            'gex_left_slope',
            'gex_right_slope',
            'gex_convexity',
            'gex_skew',
            'distance_to_positive_gamma',
            'positive_gamma_strength',
            'distance_to_support',
            'support_strength',
            'support_source_count',
            'distance_to_resistance',
            'resistance_strength',
            'resistance_source_count',
            'support_resistance_ratio',
            'sr_range_position',
            'nearby_support_count',
            'nearby_resistance_count',
            'nearby_level_ratio',
            'support_confluence',
            'resistance_confluence',
            'liquidity_imbalance',
            'trap_score',
            'trap_position',
            'distance_to_negative_gamma',
            'negative_gamma_strength',
            'price_close',
            'price_open',
            'price_high',
            'price_low',
            'volume',
            'vwap',
            'bar_size',
            'body_size',
            'bar_direction',
            'pct_change',
            'rel_volume',
            'volume_sma_20',
            'volume_ratio',
            'price_momentum_1d',
            'price_momentum_5d',
            'price_momentum_10d',
            'volume_momentum_5d',
            'volume_momentum_10d',
            'call_put_ratio',
            'call_put_volume_ratio',
            'iv_term_structure',
            'iv_percentile',
            'iv_rank',
            'iv_call_skew',
            'iv_put_skew',
            'liquidity_depth',
            'liquidity_breadth',
            'liquidity_persistence',
            'liquidity_concentration',
            'market_regime',
            'regime_duration',
            'regime_strength',
            'regime_transition_probability',
            'systematic_activity_score',
            'institutional_footprint_strength',
            'quiet_accumulation_detected',
            'range_compression_ratio',
            'absorption_efficiency',
            'campaign_stage_score',
            'flow_physics_bias_strength',
            'flow_consistency_score',
            'level_touch_velocity',
            'level_break_recovery_rate',
            'penetration_depth_quality',
            'sweep_rejection_strength',
            'confirmation_speed_score'
        ]
        
        # Validate exact count
        actual_count = len(self.required_features)
        expected_count = 97  # As confirmed by user
        
        if actual_count != expected_count:
            logger.error(f"CRITICAL: Feature count mismatch! Expected {expected_count}, got {actual_count}")
            logger.error("This means current_features.txt and this extractor are misaligned!")
            
        logger.info(f"Real ML Feature Extractor initialized with {actual_count} features")
    
    def extract_features_from_factors(self, 
                                    factors: List[FactorData], 
                                    market_data: Dict[str, Any] = None,
                                    options_data: Dict[str, Any] = None,
                                    current_price: float = None) -> pd.DataFrame:
        """
        Extract EXACTLY the features from current_features.txt for ML model input.
        
        Args:
            factors: List of FactorData objects from analyzers
            market_data: Market data dictionary with price/volume info
            options_data: Options data dictionary 
            current_price: Current price for calculations
            
        Returns:
            pandas.DataFrame with EXACTLY the columns matching current_features.txt
        """
        try:
            # Initialize feature dictionary with defaults for ALL required features
            feature_dict = {feature: 0.0 for feature in self.required_features}
            
            # Extract features from FactorData objects
            self._extract_factor_features(factors, feature_dict)
            
            # Extract features from market data
            if market_data:
                self._extract_market_features(market_data, feature_dict, current_price)
            
            # Extract features from options data  
            if options_data:
                self._extract_options_features(options_data, feature_dict, current_price)
            
            # Create DataFrame with single row (for single prediction)
            feature_df = pd.DataFrame([feature_dict])
            
            # CRITICAL VALIDATION: Must have exact feature count
            if len(feature_df.columns) != len(self.required_features):
                logger.error(f"Feature extraction FAILED: got {len(feature_df.columns)} features, expected {len(self.required_features)}")
                logger.error(f"Missing/Extra features detected!")
                
            # Handle any NaN values
            feature_df = feature_df.fillna(0.0)
            
            # Final validation
            is_valid, issues = self.validate_feature_completeness(feature_df)
            if not is_valid:
                logger.error(f"Feature validation failed: {issues}")
            
            logger.info(f"Successfully extracted {len(feature_df.columns)} features for ML model")
            return feature_df
            
        except Exception as e:
            logger.error(f"Error in feature extraction: {e}")
            # Return empty DataFrame with correct structure on error
            return pd.DataFrame({feature: [0.0] for feature in self.required_features})
    
    def _extract_factor_features(self, factors: List[FactorData], feature_dict: Dict[str, float]):
        """Extract features from FactorData objects ensuring exact ML model compatibility."""
        try:
            total_factors = len(factors)
            if total_factors == 0:
                return
                
            # Aggregate factor-specific features
            liquidity_sweep_strength = 0.0
            gamma_strength = 0.0
            flow_physics_strength = 0.0
            
            # Feature aggregation lists
            institutional_scores = []
            absorption_efficiencies = []
            campaign_stages = []
            range_qualities = []
            flow_consistency_scores = []
            level_touch_velocities = []
            level_break_rates = []
            penetration_qualities = []
            sweep_rejections = []
            confirmation_speeds = []
            
            for factor in factors:
                if not hasattr(factor, 'strength_score'):
                    continue
                    
                # Extract based on factor type or name
                factor_name = getattr(factor, 'factor_name', '')
                
                if any(keyword in factor_name.lower() for keyword in ['lss_', 'sweep', 'liquidity']):
                    liquidity_sweep_strength = max(liquidity_sweep_strength, factor.strength_score)
                    
                elif any(keyword in factor_name.lower() for keyword in ['gamma', 'gex']):
                    gamma_strength = max(gamma_strength, factor.strength_score)
                    
                elif any(keyword in factor_name.lower() for keyword in ['flow', 'physics']):
                    flow_physics_strength = max(flow_physics_strength, factor.strength_score)
                
                # Extract detailed features from factor details
                if hasattr(factor, 'details') and factor.details:
                    details = factor.details
                    
                    # Map each feature exactly as named in current_features.txt
                    if 'systematic_activity_score' in details:
                        institutional_scores.append(details['systematic_activity_score'])
                    
                    if 'absorption_efficiency' in details:
                        absorption_efficiencies.append(details['absorption_efficiency'])
                        
                    if 'campaign_stage_score' in details:
                        campaign_stages.append(details['campaign_stage_score'])
                    
                    if 'range_quality' in details:
                        range_qualities.append(details['range_quality'])
                    
                    if 'flow_consistency_score' in details:
                        flow_consistency_scores.append(details['flow_consistency_score'])
                    
                    if 'level_touch_velocity' in details:
                        level_touch_velocities.append(details['level_touch_velocity'])
                    
                    if 'level_break_recovery_rate' in details:
                        level_break_rates.append(details['level_break_recovery_rate'])
                    
                    if 'penetration_depth_quality' in details:
                        penetration_qualities.append(details['penetration_depth_quality'])
                    
                    if 'sweep_rejection_strength' in details:
                        sweep_rejections.append(details['sweep_rejection_strength'])
                    
                    if 'confirmation_speed_score' in details:
                        confirmation_speeds.append(details['confirmation_speed_score'])
            
            # Update feature dictionary with aggregated values
            feature_dict['systematic_activity_score'] = np.mean(institutional_scores) if institutional_scores else 0.0
            feature_dict['institutional_footprint_strength'] = liquidity_sweep_strength
            feature_dict['absorption_efficiency'] = np.mean(absorption_efficiencies) if absorption_efficiencies else 0.0
            feature_dict['campaign_stage_score'] = np.mean(campaign_stages) if campaign_stages else 0.0
            feature_dict['flow_physics_bias_strength'] = flow_physics_strength
            feature_dict['range_compression_ratio'] = np.mean(range_qualities) if range_qualities else 0.0
            feature_dict['quiet_accumulation_detected'] = 1.0 if liquidity_sweep_strength > 0.6 else 0.0
            
            # New features
            feature_dict['flow_consistency_score'] = np.mean(flow_consistency_scores) if flow_consistency_scores else 0.0
            feature_dict['level_touch_velocity'] = np.mean(level_touch_velocities) if level_touch_velocities else 0.0
            feature_dict['level_break_recovery_rate'] = np.mean(level_break_rates) if level_break_rates else 0.0
            feature_dict['penetration_depth_quality'] = np.mean(penetration_qualities) if penetration_qualities else 0.0
            feature_dict['sweep_rejection_strength'] = np.mean(sweep_rejections) if sweep_rejections else 0.0
            feature_dict['confirmation_speed_score'] = np.mean(confirmation_speeds) if confirmation_speeds else 0.0
            
            logger.debug(f"Extracted factor features from {total_factors} factors")
            
        except Exception as e:
            logger.error(f"Error extracting factor features: {e}")
    
    def _extract_market_features(self, market_data: Dict[str, Any], feature_dict: Dict[str, float], current_price: float = None):
        """Extract market-based features from price/volume data."""
        try:
            # Try to get price data from different possible structures
            price_df = None
            
            # Check different possible data structures
            if 'price_data' in market_data:
                price_data = market_data['price_data']
                if isinstance(price_data, dict):
                    # Multi-timeframe data
                    for tf in ['1h', '5m', '15m', '1d']:
                        if tf in price_data and not price_data[tf].empty:
                            price_df = price_data[tf]
                            break
                elif isinstance(price_data, pd.DataFrame):
                    price_df = price_data
            
            elif isinstance(market_data, pd.DataFrame):
                price_df = market_data
            
            if price_df is None or price_df.empty:
                logger.warning("No valid price data found for market feature extraction")
                return
            
            # Basic price features (exact names from current_features.txt)
            if current_price:
                feature_dict['price_close'] = current_price
            else:
                feature_dict['price_close'] = price_df['close'].iloc[-1] if 'close' in price_df.columns else 0.0
            
            # Extract OHLCV features if available
            if 'open' in price_df.columns:
                feature_dict['price_open'] = price_df['open'].iloc[-1]
            if 'high' in price_df.columns:
                feature_dict['price_high'] = price_df['high'].iloc[-1]
            if 'low' in price_df.columns:
                feature_dict['price_low'] = price_df['low'].iloc[-1]
            if 'volume' in price_df.columns:
                feature_dict['volume'] = price_df['volume'].iloc[-1]
            
            # Calculate volume features
            if 'volume' in price_df.columns and len(price_df) > 50:
                volumes = price_df['volume']
                current_vol = volumes.iloc[-1]
                
                # Relative volume features (exact names from current_features.txt)
                for period in [5, 10, 20, 50]:
                    if len(volumes) > period:
                        avg_vol = volumes.iloc[-period:].mean()
                        feature_dict[f'rel_volume_{period}'] = current_vol / (avg_vol + 1e-9)
                
                # Other volume features
                if len(volumes) > 20:
                    feature_dict['volume_sma_20'] = volumes.iloc[-20:].mean()
                    feature_dict['rel_volume'] = current_vol / (volumes.iloc[-20:].mean() + 1e-9)
                    feature_dict['volume_ratio'] = current_vol / (volumes.iloc[-1] + 1e-9)
                
                # Volume momentum features
                if len(volumes) > 10:
                    for period in [5, 10]:
                        if len(volumes) > period:
                            vol_momentum = (volumes.iloc[-1] - volumes.iloc[-period-1]) / (volumes.iloc[-period-1] + 1e-9)
                            feature_dict[f'volume_momentum_{period}d'] = vol_momentum
            
            # Calculate price momentum features
            if 'close' in price_df.columns and len(price_df) > 10:
                close_prices = price_df['close']
                
                for period in [1, 5, 10]:
                    if len(close_prices) > period:
                        momentum = (close_prices.iloc[-1] - close_prices.iloc[-period-1]) / (close_prices.iloc[-period-1] + 1e-9)
                        feature_dict[f'price_momentum_{period}d'] = momentum
            
            # VWAP calculation
            if all(col in price_df.columns for col in ['high', 'low', 'close', 'volume']):
                typical_price = (price_df['high'] + price_df['low'] + price_df['close']) / 3
                cumulative_tpv = (typical_price * price_df['volume']).cumsum()
                cumulative_volume = price_df['volume'].cumsum()
                vwap = cumulative_tpv / (cumulative_volume + 1e-9)
                feature_dict['vwap'] = vwap.iloc[-1]
                feature_dict['vwap_daily'] = vwap.iloc[-1]
            
            logger.debug("Extracted market features successfully")
            
        except Exception as e:
            logger.error(f"Error extracting market features: {e}")
    
    def _extract_options_features(self, options_data: Dict[str, Any], feature_dict: Dict[str, float], current_price: float = None):
        """Extract options-related features with exact names from current_features.txt."""
        try:
            # Extract real options data if available
            if 'put_call_ratio' in options_data:
                pcr = options_data['put_call_ratio']
                feature_dict['options_put_call_ratio'] = pcr
                feature_dict['call_put_ratio'] = 1.0 / (pcr + 1e-9)
            
            if 'gamma_exposure' in options_data:
                gex = options_data['gamma_exposure']
                feature_dict['current_gex'] = gex
                # Simple normalization
                feature_dict['normalized_gex'] = max(-1.0, min(1.0, gex / 1e9))
            
            if 'iv_data' in options_data:
                iv_data = options_data['iv_data']
                if isinstance(iv_data, dict):
                    if 'skew' in iv_data:
                        feature_dict['iv_skew'] = iv_data['skew']
                    if 'percentile' in iv_data:
                        feature_dict['iv_percentile'] = iv_data['percentile']
            
            logger.debug("Extracted options features successfully")
            
        except Exception as e:
            logger.error(f"Error extracting options features: {e}")
    
    def validate_feature_completeness(self, feature_df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate that all required features are present and valid.
        
        Returns:
            Tuple of (is_valid, missing_or_invalid_features)
        """
        try:
            issues = []
            
            # Check for missing columns
            for feature in self.required_features:
                if feature not in feature_df.columns:
                    issues.append(f"MISSING: {feature}")
            
            # Check for extra columns
            for col in feature_df.columns:
                if col not in self.required_features:
                    issues.append(f"EXTRA: {col}")
            
            # Check for invalid values
            for col in feature_df.columns:
                if feature_df[col].isna().any():
                    issues.append(f"NaN_VALUES: {col}")
                if feature_df[col].isinf().any():
                    issues.append(f"INF_VALUES: {col}")
            
            is_valid = len(issues) == 0
            
            if not is_valid:
                logger.warning(f"Feature validation failed. Issues: {issues}")
            else:
                logger.info("Feature validation PASSED - 100% alignment achieved")
            
            return is_valid, issues
            
        except Exception as e:
            logger.error(f"Error in feature validation: {e}")
            return False, ["validation_error"]
    
    def get_feature_names(self) -> List[str]:
        """Return the list of required feature names."""
        return self.required_features.copy()
    
    def get_feature_count(self) -> int:
        """Return the number of required features."""
        return len(self.required_features)


if __name__ == "__main__":
    # Test the feature extractor
    extractor = RealMLFeatureExtractor()
    print(f"Feature count: {extractor.get_feature_count()}")
    print(f"Expected: 97 features")
    print(f"Alignment: {'PERFECT' if extractor.get_feature_count() == 97 else 'MISALIGNED'}")
