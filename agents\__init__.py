#!/usr/bin/env python3
"""
CORE Agent Package

Foundation classes for the CORE agent system.
"""

from .agent_base import (
    BaseAgent,
    AgentTask, 
    AgentResult,
    TaskPriority,
    TaskStatus
)

# Import all production agents
try:
    from .greek_enhancement_agent import GreekEnhancementAgent
except ImportError:
    GreekEnhancementAgent = None

try:
    from .enhanced_csid_agent import EnhancedCSIDAgent
except ImportError:
    EnhancedCSIDAgent = None

try:
    from .flow_physics_agent import FlowPhysicsAgent
except ImportError:
    FlowPhysicsAgent = None

try:
    from .math_validator_agent import MathValidatorAgent
except ImportError:
    MathValidatorAgent = None

try:
    from .signal_quality_agent import SignalQualityAgent
except ImportError:
    SignalQualityAgent = None

try:
    from .output_coordinator_agent import OutputCoordinatorAgent
except ImportError:
    OutputCoordinatorAgent = None

try:
    from .chart_generator_agent import ChartGeneratorAgent
except ImportError:
    ChartGeneratorAgent = None

try:
    from .data_ingestion_agent import DataIngestionAgent
except ImportError:
    DataIngestionAgent = None

# Signal Generator Agent removed - redundant with engine/signal_generator.py
# Engine SignalGenerator handles all signal generation functionality

try:
    from .risk_guard_agent import RiskGuardAgent
except ImportError:
    RiskGuardAgent = None

__all__ = [
    'BaseAgent',
    'AgentTask',
    'AgentResult', 
    'TaskPriority',
    'TaskStatus'
]

# Add available agents to __all__
if GreekEnhancementAgent:
    __all__.append('GreekEnhancementAgent')
if EnhancedCSIDAgent:
    __all__.append('EnhancedCSIDAgent')
if FlowPhysicsAgent:
    __all__.append('FlowPhysicsAgent')
if MathValidatorAgent:
    __all__.append('MathValidatorAgent')
if SignalQualityAgent:
    __all__.append('SignalQualityAgent')
if OutputCoordinatorAgent:
    __all__.append('OutputCoordinatorAgent')
if ChartGeneratorAgent:
    __all__.append('ChartGeneratorAgent')
if DataIngestionAgent:
    __all__.append('DataIngestionAgent')
# SignalGeneratorAgent removed - redundant with engine implementation
if RiskGuardAgent:
    __all__.append('RiskGuardAgent')

# Agent registry for tracking available agents
AGENT_REGISTRY = {}

# Register available agents
if GreekEnhancementAgent:
    AGENT_REGISTRY['GreekEnhancementAgent'] = GreekEnhancementAgent
if EnhancedCSIDAgent:
    AGENT_REGISTRY['EnhancedCSIDAgent'] = EnhancedCSIDAgent
if FlowPhysicsAgent:
    AGENT_REGISTRY['FlowPhysicsAgent'] = FlowPhysicsAgent
if MathValidatorAgent:
    AGENT_REGISTRY['MathValidatorAgent'] = MathValidatorAgent
if SignalQualityAgent:
    AGENT_REGISTRY['SignalQualityAgent'] = SignalQualityAgent
if OutputCoordinatorAgent:
    AGENT_REGISTRY['OutputCoordinatorAgent'] = OutputCoordinatorAgent
if ChartGeneratorAgent:
    AGENT_REGISTRY['ChartGeneratorAgent'] = ChartGeneratorAgent
if DataIngestionAgent:
    AGENT_REGISTRY['DataIngestionAgent'] = DataIngestionAgent
# SignalGeneratorAgent removed - redundant with engine implementation  
if RiskGuardAgent:
    AGENT_REGISTRY['RiskGuardAgent'] = RiskGuardAgent

def register_agent(agent_class):
    """Register an agent class in the system"""
    AGENT_REGISTRY[agent_class.__name__] = agent_class
    return agent_class

def create_agent(agent_type: str, agent_id: str = None, config: dict = None):
    """Factory function to create agent instances"""
    if agent_type not in AGENT_REGISTRY:
        raise ValueError(f"Unknown agent type: {agent_type}. Available: {list(AGENT_REGISTRY.keys())}")
    
    if agent_id:
        if config:
            return AGENT_REGISTRY[agent_type](agent_id, config)
        else:
            return AGENT_REGISTRY[agent_type](agent_id)
    else:
        if config:
            return AGENT_REGISTRY[agent_type](config=config)
        else:
            return AGENT_REGISTRY[agent_type]()

def get_available_agents():
    """Get list of available agent types"""
    return list(AGENT_REGISTRY.keys())
