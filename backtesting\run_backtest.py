import pandas as pd
from pathlib import Path
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

# Add the parent directory to the sys.path to allow imports from the 'agents' directory
sys.path.insert(0, str(Path(__file__).parent.parent))

from ultimate_orchestrator import ultimate_trading_pipeline

# --- Enums for Backtesting ---
class OrderType(Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"

class OrderStatus(Enum):
    PENDING = "PENDING"
    FILLED = "FILLED"
    REJECTED = "REJECTED"
    CANCELLED = "CANCELLED"

class RiskLevel(Enum):
    CONSERVATIVE = "CONSERVATIVE"
    MODERATE = "MODERATE"
    AGGRESSIVE = "AGGRESSIVE"

class AllocationStrategy(Enum):
    RISK_PARITY = "RISK_PARITY"
    EQUAL_WEIGHT = "EQUAL_WEIGHT"
    MOMENTUM = "MOMENTUM"

# --- Dataclasses for Backtesting ---
@dataclass
class BacktestConfig:
    start_date: datetime
    end_date: datetime
    initial_capital: float = 100000.0
    commission_rate: float = 0.001
    slippage_bps: float = 2.0
    max_position_size: float = 0.1
    risk_level: RiskLevel = RiskLevel.MODERATE
    allocation_strategy: AllocationStrategy = AllocationStrategy.RISK_PARITY

@dataclass
class Trade:
    timestamp: datetime
    symbol: str
    side: str  # "buy" or "sell"
    quantity: float
    price: float
    commission: float
    slippage: float
    order_id: str
    pnl: Optional[float] = None

@dataclass
class Order:
    order_id: str
    timestamp: datetime
    symbol: str
    side: str
    quantity: float
    order_type: OrderType
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    avg_fill_price: Optional[float] = None
    commission: float = 0.0
    slippage: float = 0.0

@dataclass
class BacktestResults:
    config: BacktestConfig
    start_date: datetime
    end_date: datetime
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    calmar_ratio: float
    total_trades: int
    win_rate: float
    avg_trade_return: float
    profit_factor: float
    final_portfolio_value: float
    peak_portfolio_value: float
    total_commission: float
    total_slippage: float
    # Add more metrics as needed

    def to_dict(self):
        return {
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
            "total_return": self.total_return,
            "annualized_return": self.annualized_return,
            "volatility": self.volatility,
            "sharpe_ratio": self.sharpe_ratio,
            "max_drawdown": self.max_drawdown,
            "calmar_ratio": self.calmar_ratio,
            "total_trades": self.total_trades,
            "win_rate": self.win_rate,
            "avg_trade_return": self.avg_trade_return,
            "profit_factor": self.profit_factor,
            "final_portfolio_value": self.final_portfolio_value,
            "peak_portfolio_value": self.peak_portfolio_value,
            "total_commission": self.total_commission,
            "total_slippage": self.total_slippage,
            "config": self.config.__dict__ # Include config details
        }

class BacktestHarness:
    """A simple backtesting harness to test the trading pipeline."""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.portfolio_value = config.initial_capital
        self.cash = config.initial_capital
        self.positions = {}
        self.orders = []
        self.trades = []
        self.portfolio_history = [] # (timestamp, value)
        self.position_history = [] # (timestamp, positions_dict, cash)
        self.peak_value = config.initial_capital
        self.current_drawdown = 0.0
        self.current_time = config.start_date # Initialize current_time

    def _load_historical_data(self, data_path: Path, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Loads historical data from the given path."""
        if not data_path.exists():
            raise FileNotFoundError(f"Historical data not found at: {data_path}")
        
        df = pd.read_parquet(data_path)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.set_index('timestamp')
        return df.loc[start_date:end_date]

    def run_backtest(self, data_loader_func, signal_generator_func, tickers: List[str]) -> BacktestResults:
        """Runs the backtest over the historical data."""
        print(f"Running backtest from {self.config.start_date} to {self.config.end_date}...")

        # Load all historical data once
        full_historical_data = data_loader_func(self.config.start_date, self.config.end_date)
        if full_historical_data.empty:
            raise ValueError("No historical data available for the specified date range.")

        # Ensure data is sorted by timestamp
        full_historical_data = full_historical_data.sort_index()

        # Iterate day by day (or bar by bar, depending on data frequency)
        # Assuming daily data for simplicity in this example
        unique_dates = full_historical_data.index.normalize().unique().sort_values()

        for current_date in unique_dates:
            self.current_time = current_date # Update current_time for state recording
            print(f"\n----- Processing {self.current_time.strftime('%Y-%m-%d')} -----")
            
            # Get data slice for the current day
            daily_data_slice = full_historical_data.loc[current_date.strftime('%Y-%m-%d')]
            if isinstance(daily_data_slice, pd.Series): # Handle single row case
                daily_data_slice = pd.DataFrame([daily_data_slice])
            
            # Generate signals for all tickers for the current day
            signals = signal_generator_func(daily_data_slice, self.current_time)

            # Process signals and execute trades
            for ticker, signal_info in signals.items():
                # Placeholder for actual trade logic based on signal_info
                # For now, a simple buy/sell based on signal_strength
                if signal_info.get('signal_strength', 0) > 0.7:
                    # Simulate a buy order
                    order = Order(
                        order_id=f"ORD_{ticker}_{self.current_time.strftime('%Y%m%d%H%M%S')}_BUY",
                        timestamp=self.current_time,
                        symbol=ticker,
                        side="buy",
                        quantity=10, # Fixed quantity for simplicity
                        order_type=OrderType.MARKET
                    )
                    self._simulate_order_fill(order, daily_data_slice[daily_data_slice['symbol'] == ticker])
                elif signal_info.get('signal_strength', 0) < 0.3:
                    # Simulate a sell order (if position exists)
                    if ticker in self.positions and self.positions[ticker]['quantity'] > 0:
                        order = Order(
                            order_id=f"ORD_{ticker}_{self.current_time.strftime('%Y%m%d%H%M%S')}_SELL",
                            timestamp=self.current_time,
                            symbol=ticker,
                            side="sell",
                            quantity=min(10, self.positions[ticker]['quantity']),
                            order_type=OrderType.MARKET
                        )
                        self._simulate_order_fill(order, daily_data_slice[daily_data_slice['symbol'] == ticker])

            # Update portfolio and record state at end of day
            self._update_portfolio_and_positions(daily_data_slice)
            self._record_state()

        # Calculate final performance metrics
        return self._calculate_final_performance()

    def _simulate_order_fill(self, order: Order, symbol_data: pd.DataFrame) -> bool:
        """Simulates an order fill with slippage and commission."""
        if symbol_data.empty:
            order.status = OrderStatus.REJECTED
            print(f"  Order {order.order_id}: REJECTED - No market data for {order.symbol}")
            return False

        fill_price = symbol_data['close'].iloc[-1]
        
        # Apply slippage
        slippage_amount = fill_price * (self.config.slippage_bps / 10000)
        if order.side == "buy":
            fill_price += slippage_amount
        else: # sell
            fill_price -= slippage_amount

        commission = order.quantity * self.config.commission_rate
        
        # Check for sufficient cash for buy orders
        if order.side == "buy":
            cost = order.quantity * fill_price + commission
            if self.cash < cost:
                order.status = OrderStatus.REJECTED
                print(f"  Order {order.order_id}: REJECTED - Insufficient cash. Needed ${cost:.2f}, Have ${self.cash:.2f}")
                return False
            self.cash -= cost
            self.positions[order.symbol] = self.positions.get(order.symbol, {'quantity': 0, 'avg_price': 0}) # Ensure dict exists
            total_cost = self.positions[order.symbol]['quantity'] * self.positions[order.symbol]['avg_price'] + order.quantity * fill_price
            total_quantity = self.positions[order.symbol]['quantity'] + order.quantity
            self.positions[order.symbol]['avg_price'] = total_cost / total_quantity
            self.positions[order.symbol]['quantity'] += order.quantity

        else: # sell
            if order.symbol not in self.positions or self.positions[order.symbol]['quantity'] < order.quantity:
                order.status = OrderStatus.REJECTED
                print(f"  Order {order.order_id}: REJECTED - Insufficient position to sell {order.symbol}")
                return False
            
            self.cash += order.quantity * fill_price - commission
            self.positions[order.symbol]['quantity'] -= order.quantity
            if self.positions[order.symbol]['quantity'] == 0:
                del self.positions[order.symbol] # Remove position if fully sold

        order.status = OrderStatus.FILLED
        order.filled_quantity = order.quantity
        order.avg_fill_price = fill_price
        order.commission = commission
        order.slippage = slippage_amount
        self.orders.append(order)
        
        trade = Trade(
            timestamp=order.timestamp,
            symbol=order.symbol,
            side=order.side,
            quantity=order.quantity,
            price=fill_price,
            commission=commission,
            slippage=slippage_amount,
            order_id=order.order_id
        )
        self.trades.append(trade)
        print(f"  Order {order.order_id}: FILLED {order.side.upper()} {order.quantity} {order.symbol} @ ${fill_price:.2f}")
        return True

    def _update_portfolio_and_positions(self, daily_data_slice: pd.DataFrame):
        """Update portfolio value and positions based on current market data."""
        current_market_prices = daily_data_slice.set_index('symbol')['close'].to_dict()

        # Update unrealized P&L and portfolio value
        current_portfolio_value = self.cash
        for symbol, pos_info in self.positions.items():
            if symbol in current_market_prices:
                current_price = current_market_prices[symbol]
                current_portfolio_value += pos_info['quantity'] * current_price
            else:
                # If no current price, use last known average price for estimation
                current_portfolio_value += pos_info['quantity'] * pos_info['avg_price']
        
        self.portfolio_value = current_portfolio_value

        # Update peak value and drawdown
        if self.portfolio_value > self.peak_value:
            self.peak_value = self.portfolio_value
        self.current_drawdown = (self.peak_value - self.portfolio_value) / self.peak_value if self.peak_value > 0 else 0.0

    def _record_state(self):
        """Records the current state of the portfolio."""
        self.portfolio_history.append((self.current_time, self.portfolio_value))
        
        # Record positions for the day
        current_positions_snapshot = {
            symbol: pos_info['quantity'] * self.portfolio_value / self.cash if self.cash > 0 else 0 # Simplified value
            for symbol, pos_info in self.positions.items()
        }
        self.position_history.append({
            'timestamp': self.current_time,
            'portfolio_value': self.portfolio_value,
            'cash': self.cash,
            'positions': current_positions_snapshot
        })

    def _calculate_final_performance(self) -> BacktestResults:
        """Calculates and returns final performance metrics."""
        total_return = (self.portfolio_value - self.config.initial_capital) / self.config.initial_capital
        
        # Simplified annualization (assuming daily data for a year)
        num_days = (self.config.end_date - self.config.start_date).days
        annualized_return = (1 + total_return)**(365 / num_days) - 1 if num_days > 0 else 0.0

        # Volatility (standard deviation of daily returns)
        daily_returns = pd.Series([val for _, val in self.portfolio_history]).pct_change().dropna()
        volatility = daily_returns.std() * np.sqrt(252) if not daily_returns.empty else 0.0 # Annualized

        # Sharpe Ratio (simplified, needs risk-free rate)
        sharpe_ratio = annualized_return / volatility if volatility > 0 else 0.0

        # Max Drawdown (already calculated)
        max_drawdown = self.current_drawdown

        # Calmar Ratio
        calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0.0

        # Trade analytics
        winning_trades = [t for t in self.trades if t.pnl is not None and t.pnl > 0]
        losing_trades = [t for t in self.trades if t.pnl is not None and t.pnl <= 0]
        
        total_trades = len(self.trades)
        win_rate = len(winning_trades) / total_trades if total_trades > 0 else 0.0
        avg_trade_return = sum(t.pnl for t in self.trades if t.pnl is not None) / total_trades if total_trades > 0 else 0.0

        total_profit = sum(t.pnl for t in winning_trades if t.pnl is not None)
        total_loss = abs(sum(t.pnl for t in losing_trades if t.pnl is not None))
        profit_factor = total_profit / total_loss if total_loss > 0 else 0.0

        total_commission = sum(t.commission for t in self.trades)
        total_slippage = sum(t.slippage for t in self.trades)

        return BacktestResults(
            config=self.config,
            start_date=self.config.start_date,
            end_date=self.config.end_date,
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            calmar_ratio=calmar_ratio,
            total_trades=total_trades,
            win_rate=win_rate,
            avg_trade_return=avg_trade_return,
            profit_factor=profit_factor,
            final_portfolio_value=self.portfolio_value,
            peak_portfolio_value=self.peak_value,
            total_commission=total_commission,
            total_slippage=total_slippage
        )

def setup_backtest_data(data_dir: Path):
    """Creates a dummy historical data file for backtesting."""
    data_dir.mkdir(parents=True, exist_ok=True)
    historical_data_path = data_dir / "historical_data.parquet"

    if not historical_data_path.exists():
        data = {
            'timestamp': pd.to_datetime([f'2024-01-{i:02d}' for i in range(1, 32)]),
            'open': [100 + i for i in range(31)],
            'high': [102 + i for i in range(31)],
            'low': [99 + i for i in range(31)],
            'close': [101 + i for i in range(31)],
            'volume': [1000 + i * 10 for i in range(31)]
        }
        df = pd.DataFrame(data)
        df.to_parquet(historical_data_path, index=False)
        print(f"Created dummy historical data at: {historical_data_path}")

if __name__ == '__main__':
    BACKTEST_DATA_DIR = Path(__file__).parent / "data"
    setup_backtest_data(BACKTEST_DATA_DIR)

    harness = BacktestHarness(
        data_path=BACKTEST_DATA_DIR / "historical_data.parquet",
        start_date=datetime(2024, 1, 1),
        end_date=datetime(2024, 1, 31)
    )
    harness.run_backtest()