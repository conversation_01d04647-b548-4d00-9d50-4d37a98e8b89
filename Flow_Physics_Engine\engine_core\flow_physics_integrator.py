"""Flow Physics Integrator with CSID Enhancement - External Engine Version

Integrates all flow physics components (velocity, acceleration, jerk) with CSID
(Cumulative Volume Delta Institutional Divergence) to provide comprehensive
flow analysis and institutional detection.

CSID Enhancement:
- Institutional divergence detection through volume delta analysis
- Smart money flow identification and measurement
- Enhanced flow confirmation through delta-price divergence analysis
"""

import sys
import os
from pathlib import Path
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass

# Import from parent directory
try:
    from ..enhanced_csid_analyzer import EnhancedCSIDAnalyzer, EnhancedCSIDResult
except ImportError:
    # Fallback for different execution contexts
    from enhanced_csid_analyzer import EnhancedCSIDAnalyzer, EnhancedCSIDResult

logger = logging.getLogger(__name__)

# Import physics analyzers from advanced directory
try:
    from dataclasses import asdict
    from ..advanced.advanced_velocity_analyzer import AdvancedVelocityAnalyzer, VelocityProfile
    from ..advanced.advanced_acceleration_analyzer import AdvancedAccelerationAnalyzer, AccelerationProfile
    from ..advanced.flow_jerk_analyzer import FlowJerkAnalyzer, JerkProfile
    from dataclasses import asdict
except ImportError as e:
    logger.warning(f"Could not import physics analyzers: {e}")
    AdvancedVelocityAnalyzer = None
    AdvancedAccelerationAnalyzer = None
    FlowJerkAnalyzer = None

# External Engine Constants
FLOW_PHYSICS_CONSTANTS = {
    'MIN_VELOCITY_THRESHOLD': 0.1,
    'INSTITUTIONAL_VELOCITY_THRESHOLD': 0.25,
    'EXTREME_VELOCITY_THRESHOLD': 1.0,
    'MIN_ACCELERATION_THRESHOLD': 0.05,
    'INSTITUTIONAL_ACCELERATION_THRESHOLD': 0.2,
    'EXTREME_ACCELERATION_THRESHOLD': 0.35,
    'MIN_JERK_THRESHOLD': 0.02,
    'REGIME_CHANGE_JERK_THRESHOLD': 0.1,
    'EXTREME_JERK_THRESHOLD': 0.3,
    'MIN_SAMPLE_PERIOD': 60,
    'DEFAULT_LOOKBACK_PERIODS': 20, # Lookback for derivative calculations
    'SMOOTHING_FACTOR': 0.3,

    # --- NEW SUGGESTED PARAMETERS ---

    # 1. Adaptability
    'VOLATILITY_ADAPTIVE_THRESHOLDS_ENABLED': True, # Master switch for volatility-based threshold adjustment
    'ASSET_CLASS_PROFILE': 'EQUITY', # ('EQUITY', 'CRYPTO', 'FOREX') - Loads different pre-tuned constants
    'MARKET_SESSION_SENSITIVITY': { # Multiplier for thresholds based on market session
        'pre_market': 0.8, 
        'market_open': 1.5, 
        'mid_day': 0.7, 
        'market_close': 1.2
    },

    # 2. Sensitivity & Granularity
    'DERIVATIVE_CALCULATION_METHOD': 'gradient', # ('gradient', 'polyfit') - Method for calculating derivatives
    'JERK_EVENT_MIN_DURATION_BARS': 2, # Minimum bars a jerk event must persist to be valid

    # 3. Risk & Confirmation
    'REGIME_CONFIDENCE_THRESHOLD': 0.65, # Minimum confidence for a regime to be considered "locked-in"
    'CONFLUENCE_REQUIREMENT_LEVEL': 3 # Number of physics signals that must align for a high-confidence alert
}

FLOW_REGIMES = {
    'ACCUMULATION': {
        'velocity': 'positive',
        'acceleration': 'positive',
        'description': 'Institutional accumulation phase'
    },
    'DISTRIBUTION': {
        'velocity': 'negative',
        'acceleration': 'negative',
        'description': 'Institutional distribution phase'
    },
    'MOMENTUM_SHIFT': {
        'velocity': 'any',
        'acceleration': 'changing_sign',
        'description': 'Flow momentum changing direction'
    },
    'REGIME_CHANGE': {
        'jerk': 'extreme',
        'description': 'Sudden change in flow dynamics'
    },
    'STEADY_FLOW': {
        'velocity': 'constant',
        'acceleration': 'near_zero',
        'description': 'Consistent flow pattern'
    }
}

@dataclass
class FlowPhysicsResult:
    """Complete flow physics analysis result with CSID enhancement."""
    timestamp: datetime
    symbol: str
    
    # Raw values
    flow_value: float
    flow_velocity: float
    flow_acceleration: float
    flow_jerk: float
    
    # CSID Enhancement - Core CSID metrics
    csid_cumulative_delta: float
    csid_velocity: float
    csid_acceleration: float
    csid_institutional_divergence: float
    
    # CSID Enhancement - Smart money detection
    smart_money_flow: float
    institutional_bias: str
    smart_money_strength: float
    
    # CSID Enhancement - Flow-delta confluence
    flow_delta_confirmation: bool
    flow_delta_confluence_strength: float
    
    # Normalized values (0-1 scale)
    normalized_velocity: float
    normalized_acceleration: float
    normalized_jerk: float
    
    # Regime detection
    current_regime: str
    regime_confidence: float
    regime_duration: Optional[timedelta]
    
    # Institutional detection (enhanced with CSID)
    institutional_activity: bool
    institutional_direction: str
    institutional_strength: float
    
    # Alerts
    momentum_shift_detected: bool
    regime_change_detected: bool
    extreme_flow_detected: bool
    csid_divergence_detected: bool
    
    # Multi-timeframe consensus
    timeframe_alignment: float
    dominant_timeframe: str
    
    # Metadata
    quality_score: float
    analysis_metadata: Dict[str, Any]


class FlowPhysicsIntegrator:
    """External Engine Flow Physics Integrator with CSID Enhancement."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the flow physics integrator with CSID enhancement."""
        self.config = config or {}
        self.constants = FLOW_PHYSICS_CONSTANTS
        self.regimes = FLOW_REGIMES
        
        # Enhanced CSID Integration
        csid_config = self.config.get('csid', {})
        self.csid_analyzer = EnhancedCSIDAnalyzer(csid_config)
        
        # State tracking
        self._regime_history = {}
        self._flow_history = {}
        self._velocity_history = {} # Add history for advanced analyzers
        self._acceleration_history = {} # Add history for advanced analyzers
        self._last_analysis = {}
        
        logger.info("External Flow Physics Integrator with CSID enhancement initialized")
        
    def analyze_factors(self, data_package: Dict[str, Any] = None, **kwargs) -> List:
        """Analyze flow physics and generate factor data."""
        if data_package is None:
            data_package = kwargs
            
        ticker = data_package.get('ticker', 'UNKNOWN')
        current_price = data_package.get('current_price', 0.0)
        mtf_data = data_package.get('mtf_data', {})
        
        factors = []
        
        try:
            # Get flow data from package
            flow_data = data_package.get('flow_data', {})
            
            # Calculate flow from price data if not provided
            if not flow_data and mtf_data:
                flow_data = self._calculate_flow_from_price_data(ticker, current_price, mtf_data)
            
            if not flow_data:
                logger.debug(f"[{ticker}] No flow data available for factor generation")
                return factors
            
            # Get price data for CSID analysis
            price_data = None
            for timeframe_key in ['5m', '15m', '1h', '4h', '1d']:
                if timeframe_key in mtf_data and isinstance(mtf_data[timeframe_key], pd.DataFrame):
                    price_data = mtf_data[timeframe_key]
                    break
            
            # Perform flow physics analysis with CSID
            result = self.analyze_flow_physics(
                ticker, 
                flow_data, 
                price_data=price_data
            )
            
            # Generate basic factors (simplified for external engine)
            if result.institutional_activity:
                factors.append({
                    'factor_name': f'FlowPhysics_Institutional_{result.institutional_direction}',
                    'strength': result.institutional_strength,
                    'direction': result.institutional_direction,
                    'csid_confirmed': result.flow_delta_confirmation
                })
            
            if result.regime_change_detected:
                factors.append({
                    'factor_name': f'FlowPhysics_RegimeChange_{result.current_regime}',
                    'strength': result.regime_confidence,
                    'regime': result.current_regime,
                    'csid_divergence': result.csid_divergence_detected
                })
            
            logger.info(f"[{ticker}] External engine generated {len(factors)} flow physics factors")
            return factors
            
        except Exception as e:
            logger.error(f"[{ticker}] Error in external engine factor analysis: {e}")
            return factors
    
    def analyze_flow_physics(self, 
                symbol: str,
                flow_data: Dict[str, Any],
                price_data: Optional[pd.DataFrame] = None) -> FlowPhysicsResult:
        """Perform comprehensive flow physics analysis with CSID enhancement."""
        try:
            timestamp = flow_data.get('timestamp', datetime.now())
            flow_value = float(flow_data.get('flow_value', 0))
            
            # Initialize history
            if symbol not in self._flow_history:
                self._flow_history[symbol] = []
                
            # Add current data point
            self._flow_history[symbol].append({
                'timestamp': timestamp,
                'flow_value': flow_value
            })
            
            # Limit history size
            max_history = self.constants['DEFAULT_LOOKBACK_PERIODS'] * 2
            if len(self._flow_history[symbol]) > max_history:
                self._flow_history[symbol] = self._flow_history[symbol][-max_history:]
                
            # --- REFACTORED DERIVATIVE CALCULATION ---
            # Use advanced analyzers instead of local calculation
            flow_df = pd.DataFrame(self._flow_history[symbol])
            flow_df['timestamp'] = pd.to_datetime(flow_df['timestamp'])

            velocity_profile = self._get_velocity_analyzer().analyze(symbol, flow_df, flow_value, timestamp)
            
            # Create velocity history for acceleration analyzer
            if symbol not in self._velocity_history: self._velocity_history[symbol] = []
            self._velocity_history[symbol].append(velocity_profile)
            velocity_df = pd.DataFrame([asdict(vp) for vp in self._velocity_history[symbol]])

            acceleration_profile = self._get_acceleration_analyzer().analyze(symbol, velocity_df, velocity_profile.smoothed_velocity, flow_value, timestamp)

            # Create acceleration history for jerk analyzer
            if symbol not in self._acceleration_history: self._acceleration_history[symbol] = []
            self._acceleration_history[symbol].append(acceleration_profile)
            acceleration_df = pd.DataFrame([asdict(ap) for ap in self._acceleration_history[symbol]])

            jerk_profile = self._get_jerk_analyzer().analyze(symbol, acceleration_df, acceleration_profile.smoothed_acceleration, velocity_profile.smoothed_velocity, flow_value, timestamp)

            derivatives = self._package_derivatives(velocity_profile, acceleration_profile, jerk_profile)
            
            # CSID analysis if price data available
            csid_result = None
            if price_data is not None and not price_data.empty:
                csid_result = self.csid_analyzer.calculate_enhanced_csid(symbol, price_data)
                
            # Detect regime with CSID enhancement
            regime_info = self._detect_regime_with_csid(derivatives, csid_result)
            
            # Detect institutional activity with CSID
            institutional_info = self._detect_institutional_activity_with_csid(
                derivatives, regime_info, csid_result
            )
            
            # Check alerts with CSID
            alerts = self._check_alerts_with_csid(derivatives, regime_info, csid_result)
            
            # Analyze flow-delta confluence
            confluence_info = self._analyze_flow_delta_confluence(derivatives, csid_result)
            
            # Calculate quality score
            quality_score = self._calculate_quality_score_with_csid(
                len(self._flow_history[symbol]),
                derivatives,
                csid_result
            )
            
            # Create result with CSID enhancement
            result = FlowPhysicsResult(
                timestamp=timestamp,
                symbol=symbol,
                flow_value=flow_value,
                flow_velocity=derivatives['velocity'],
                flow_acceleration=derivatives['acceleration'],
                flow_jerk=derivatives['jerk'],
                # CSID Enhancement fields
                csid_cumulative_delta=csid_result.cumulative_volume_delta if csid_result else 0.0,
                csid_velocity=csid_result.cvd_velocity if csid_result else 0.0,
                csid_acceleration=csid_result.cvd_acceleration if csid_result else 0.0,
                csid_institutional_divergence=csid_result.institutional_stealth_score if csid_result else 0.0,
                smart_money_flow=csid_result.smart_money_index if csid_result else 0.0,
                institutional_bias=csid_result.institutional_bias if csid_result else 'neutral',
                smart_money_strength=csid_result.smart_money_index if csid_result else 0.0,
                flow_delta_confirmation=confluence_info['confirmation'],
                flow_delta_confluence_strength=confluence_info['strength'],
                # Original fields
                normalized_velocity=derivatives['norm_velocity'],
                normalized_acceleration=derivatives['norm_acceleration'],
                normalized_jerk=derivatives['norm_jerk'],
                current_regime=regime_info['regime'],
                regime_confidence=regime_info['confidence'],
                regime_duration=regime_info.get('duration'),
                institutional_activity=institutional_info['detected'],
                institutional_direction=institutional_info['direction'],
                institutional_strength=institutional_info['strength'],
                momentum_shift_detected=alerts['momentum_shift'],
                regime_change_detected=alerts['regime_change'],
                extreme_flow_detected=alerts['extreme_flow'],
                csid_divergence_detected=alerts.get('csid_divergence', False),
                timeframe_alignment=0.7,  # Default
                dominant_timeframe='5m',  # Default
                quality_score=quality_score,
                analysis_metadata={
                    'history_size': len(self._flow_history[symbol]),
                    'derivatives': derivatives,
                    'regime_info': regime_info,
                    'institutional_info': institutional_info,
                    'alerts': alerts,
                    'csid_result': csid_result.__dict__ if csid_result else None,
                    'confluence_info': confluence_info
                }
            )
            
            self._last_analysis[symbol] = result
            return result
            
        except Exception as e:
            logger.error(f"Error in external engine flow physics analysis: {e}")
            return self._create_default_result(symbol, timestamp, flow_value)
    
    def _calculate_flow_from_price_data(self, ticker: str, current_price: float, 
                                       mtf_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate basic flow data from price data."""
        try:
            price_data = None
            for timeframe_key in ['5m', '15m', '1h', '4h', '1d']:
                if timeframe_key in mtf_data:
                    price_data = mtf_data[timeframe_key]
                    break
            
            if price_data is None or price_data.empty:
                return {}
            
            recent_bar = price_data.iloc[-1]
            flow_value = recent_bar['close'] * recent_bar['volume']
            
            return {
                'timestamp': datetime.now(),
                'flow_value': flow_value,
                'price': recent_bar['close'],
                'volume': recent_bar['volume']
            }
            
        except Exception as e:
            logger.error(f"Error calculating flow from price data: {e}")
            return {}
    
    def _calculate_all_derivatives(self, symbol: str) -> Dict[str, float]:
        """Calculate velocity, acceleration, and jerk from flow history."""
        history = self._flow_history[symbol]
        
        if len(history) < 2:
            return {
                'velocity': 0.0,
                'acceleration': 0.0,
                'jerk': 0.0,
                'norm_velocity': 0.0,
                'norm_acceleration': 0.0,
                'norm_jerk': 0.0
            }
            
        flow_values = [h['flow_value'] for h in history]
        timestamps = [h['timestamp'].timestamp() for h in history]
        
        velocity = self._calculate_derivative(flow_values, timestamps, order=1)
        acceleration = self._calculate_derivative(flow_values, timestamps, order=2)
        jerk = self._calculate_derivative(flow_values, timestamps, order=3)
        
        norm_velocity = self._normalize_value(velocity, self.constants['EXTREME_VELOCITY_THRESHOLD'])
        norm_acceleration = self._normalize_value(acceleration, self.constants['EXTREME_ACCELERATION_THRESHOLD'])
        norm_jerk = self._normalize_value(jerk, self.constants['EXTREME_JERK_THRESHOLD'])
        
        return {
            'velocity': velocity,
            'acceleration': acceleration,
            'jerk': jerk,
            'norm_velocity': norm_velocity,
            'norm_acceleration': norm_acceleration,
            'norm_jerk': norm_jerk
        }
        
    def _calculate_derivative(self, values: List[float], timestamps: List[float], order: int) -> float:
        """Calculate the nth derivative of the most recent values."""
        if len(values) < order + 1:
            return 0.0
            
        n_points = min(order + 2, len(values))
        recent_values = values[-n_points:]
        recent_times = timestamps[-n_points:]
        
        result = np.array(recent_values, dtype=float)
        time_array = np.array(recent_times, dtype=float)
        
        for _ in range(order):
            if len(result) < 2:
                return 0.0
                
            value_diff = np.diff(result)
            time_diff = np.diff(time_array[:len(result)])
            time_diff[time_diff == 0] = 1e-6
            
            result = value_diff / time_diff
            time_array = time_array[1:len(result)+1]
            
        return float(result[-1]) if len(result) > 0 else 0.0
        
    def _normalize_value(self, value: float, threshold: float) -> float:
        """Normalize a value to 0-1 scale using threshold."""
        if threshold == 0:
            return 0.0
        normalized = abs(value) / threshold
        return min(1.0, normalized)
        
    def _detect_regime_with_csid(self, derivatives: Dict[str, float], 
                               csid_result: Optional[EnhancedCSIDResult]) -> Dict[str, Any]:
        """Enhanced regime detection using CSID confirmation."""
        velocity = derivatives['velocity']
        acceleration = derivatives['acceleration']
        jerk = derivatives['jerk']
        
        if abs(jerk) > self.constants['REGIME_CHANGE_JERK_THRESHOLD']:
            regime = 'REGIME_CHANGE'
            confidence = min(1.0, abs(jerk) / self.constants['EXTREME_JERK_THRESHOLD'])
        elif (velocity > 0 and acceleration < 0) or (velocity < 0 and acceleration > 0):
            regime = 'MOMENTUM_SHIFT'
            confidence = 0.7
        elif velocity > self.constants['MIN_VELOCITY_THRESHOLD'] and acceleration > 0:
            regime = 'ACCUMULATION'
            confidence = min(1.0, (abs(velocity) + abs(acceleration)) / 2)
        elif velocity < -self.constants['MIN_VELOCITY_THRESHOLD'] and acceleration < 0:
            regime = 'DISTRIBUTION'
            confidence = min(1.0, (abs(velocity) + abs(acceleration)) / 2)
        else:
            regime = 'STEADY_FLOW'
            confidence = 0.3
        
        # CSID Enhancement
        if csid_result and csid_result.calculation_confidence > 0.5:
            csid_velocity = csid_result.cvd_velocity
            if (velocity > 0 and csid_velocity > 0) or (velocity < 0 and csid_velocity < 0):
                confidence = min(1.0, confidence * 1.1) # More conservative boost
            
        return {
            'regime': regime,
            'confidence': confidence,
            'duration': timedelta(seconds=0)
        }
        
    def _detect_institutional_activity_with_csid(self, 
                                               derivatives: Dict[str, float],
                                               regime_info: Dict[str, Any],
                                               csid_result: Optional[EnhancedCSIDResult]) -> Dict[str, Any]:
        """Enhanced institutional activity detection using CSID."""
        velocity = derivatives['velocity']
        acceleration = derivatives['acceleration']
        
        institutional_velocity = abs(velocity) > self.constants['INSTITUTIONAL_VELOCITY_THRESHOLD']
        institutional_acceleration = abs(acceleration) > self.constants['INSTITUTIONAL_ACCELERATION_THRESHOLD']
        
        detected = institutional_velocity or institutional_acceleration
        
        if not detected:
            direction = 'neutral'
            strength = 0.0
        elif regime_info['regime'] == 'ACCUMULATION' or velocity > 0:
            direction = 'accumulation'
            strength = (derivatives['norm_velocity'] + derivatives['norm_acceleration']) / 2
        elif regime_info['regime'] == 'DISTRIBUTION' or velocity < 0:
            direction = 'distribution'
            strength = (derivatives['norm_velocity'] + derivatives['norm_acceleration']) / 2
        else:
            direction = 'neutral'
            strength = max(derivatives['norm_velocity'], derivatives['norm_acceleration'])
            
        # CSID Enhancement
        if csid_result and csid_result.smart_money_index > 0.5:
            detected = True
            direction = csid_result.institutional_bias
            csid_strength = abs(csid_result.smart_money_index)
            combined_strength = (strength * 0.4) + (csid_strength * 0.6)
            strength = min(1.0, combined_strength)
            
        return {
            'detected': detected,
            'direction': direction,
            'strength': min(1.0, strength)
        }
        
    def _check_alerts_with_csid(self, 
                              derivatives: Dict[str, float],
                              regime_info: Dict[str, Any],
                              csid_result: Optional[EnhancedCSIDResult]) -> Dict[str, bool]:
        """Enhanced alert checking with CSID divergence detection."""
        alerts = {
            'momentum_shift': regime_info['regime'] == 'MOMENTUM_SHIFT',
            'regime_change': regime_info['regime'] == 'REGIME_CHANGE',
            'extreme_flow': (
                derivatives['norm_velocity'] > 0.8 or
                derivatives['norm_acceleration'] > 0.8 or
                derivatives['norm_jerk'] > 0.8
            )
        }
        
        # CSID Enhancement
        if csid_result:
            alerts['csid_divergence'] = abs(csid_result.institutional_stealth_score) > 0.3
        else:
            alerts['csid_divergence'] = False
            
        return alerts
        
    def _analyze_flow_delta_confluence(self, derivatives: Dict[str, float], 
                                     csid_result: Optional[EnhancedCSIDResult]) -> Dict[str, Any]:
        """Analyze confluence between flow physics and delta analysis."""
        if not csid_result:
            return {'confirmation': False, 'strength': 0.0}
            
        flow_velocity = derivatives['velocity']
        csid_velocity = csid_result.cvd_velocity
        
        # Check for confirmation
        confirmation = (flow_velocity > 0 and csid_velocity > 0) or (flow_velocity < 0 and csid_velocity < 0)
        
        # Calculate confluence strength
        if confirmation:
            strength = min(abs(flow_velocity), abs(csid_velocity)) / max(abs(flow_velocity), abs(csid_velocity), 1e-6)
        else:
            strength = 0.0
            
        return {
            'confirmation': confirmation,
            'strength': strength
        }
        
    def _calculate_quality_score_with_csid(self, 
                                         history_size: int,
                                         derivatives: Dict[str, float],
                                         csid_result: Optional[EnhancedCSIDResult]) -> float:
        """Calculate quality score enhanced with CSID confidence."""
        if history_size < 3:
            base_score = 0.3
        elif history_size < 10:
            base_score = 0.5
        elif history_size < 20:
            base_score = 0.7
        else:
            base_score = 0.9
            
        if any(np.isnan(v) or np.isinf(v) for v in derivatives.values()):
            base_score *= 0.7
            
        # CSID Enhancement
        if csid_result and csid_result.calculation_confidence > 0.5:
            base_score = min(1.0, base_score * 1.1)
            
        return base_score
        
    def _create_default_result(self, symbol: str, timestamp: datetime, flow_value: float) -> FlowPhysicsResult:
        """Create a default result when analysis fails."""
        return FlowPhysicsResult(
            timestamp=timestamp,
            symbol=symbol,
            flow_value=flow_value,
            flow_velocity=0.0,
            flow_acceleration=0.0,
            flow_jerk=0.0,
            csid_cumulative_delta=0.0,
            csid_velocity=0.0,
            csid_acceleration=0.0,
            csid_institutional_divergence=0.0,
            smart_money_flow=0.0,
            institutional_bias='neutral',
            smart_money_strength=0.0,
            flow_delta_confirmation=False,
            flow_delta_confluence_strength=0.0,
            normalized_velocity=0.0,
            normalized_acceleration=0.0,
            normalized_jerk=0.0,
            current_regime='UNKNOWN',
            regime_confidence=0.0,
            regime_duration=None,
            institutional_activity=False,
            institutional_direction='neutral',
            institutional_strength=0.0,
            momentum_shift_detected=False,
            regime_change_detected=False,
            extreme_flow_detected=False,
            csid_divergence_detected=False,
            timeframe_alignment=0.0,
            dominant_timeframe='unknown',
            quality_score=0.0,
            analysis_metadata={}
        )
