"""
Retrain ML Models Script

This script retrains the ML models with the current feature set to fix feature mismatch issues.
It extracts the current feature set from real data and retrains all models to ensure compatibility.
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
import argparse

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Import ML components
from ml.train_liquidity_models import LiquidityModelTrainer
from ml.ml_feature_engineering import FeatureEngineer
from ml.ml_logging import get_logger
from ml.generate_training_data import TrainingDataGenerator
from ml.feature_compatibility import align_features
from data.loaders.polygon_api import PolygonAPI
from data.standardizers.options_standardizer import OptionsStandardizer
from data.standardizers.price_standardizer import PriceStandardizer
from config import settings

# Configure logging
logger = get_logger("retrain_models")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Retrain ML models with current feature set")

    parser.add_argument('--tickers', type=str, default="SPY,QQQ,AAPL,MSFT,AMZN",
                        help='Comma-separated list of tickers to use for training (default: SPY,QQQ,AAPL,MSFT,AMZN)')

    parser.add_argument('--days', type=int, default=60,
                        help='Number of days of historical data to use (default: 60)')

    parser.add_argument('--output-dir', type=str, default=None,
                        help='Output directory for trained models (default: ml/models)')

    parser.add_argument('--use-real-data', action='store_true',
                        help='Use real data from Polygon API instead of synthetic data')

    parser.add_argument('--visualize', action='store_true',
                        help='Generate visualizations of feature importance')

    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed for reproducibility (default: 42)')

    return parser.parse_args()

def get_real_training_data(tickers: List[str], days: int) -> List[Dict[str, Any]]:
    """
    Get real training data from Polygon API.

    Args:
        tickers: List of ticker symbols
        days: Number of days of historical data

    Returns:
        List of training samples
    """
    logger.info(f"Getting real training data for {len(tickers)} tickers over {days} days")

    # Initialize API client
    api_client = PolygonAPI()

    # Initialize standardizers
    options_standardizer = OptionsStandardizer()
    price_standardizer = PriceStandardizer()

    # Create training samples
    training_samples = []

    for ticker in tickers:
        logger.info(f"Processing {ticker}")

        try:
            # Get price data
            price_data = api_client.get_historical_data(ticker, days=days)
            if price_data.empty:
                logger.warning(f"No price data found for {ticker}")
                continue

            # Standardize price data
            std_price_data = price_standardizer.standardize(price_data)

            # Get options data
            options_data = api_client.get_options_chain(ticker)

            # Standardize options data
            current_price = std_price_data['close'].iloc[-1]
            std_options_data = options_standardizer.standardize(options_data, current_price)

            # Create sample
            sample = {
                'ticker': ticker,
                'price_data': std_price_data,
                'options_data': std_options_data,
                'liquidity_levels': extract_liquidity_levels(std_price_data, std_options_data, current_price),
                'volume_profile': None,  # Will be calculated during training
                'gex_data': None  # Will be calculated during training
            }

            training_samples.append(sample)
            logger.info(f"Added training sample for {ticker}")

        except Exception as e:
            logger.error(f"Error processing {ticker}: {str(e)}")

    return training_samples

def extract_liquidity_levels(price_data: pd.DataFrame, options_data: pd.DataFrame,
                            current_price: float) -> Dict[str, List[Dict[str, Any]]]:
    """
    Extract liquidity levels from price and options data.

    Args:
        price_data: Standardized price data
        options_data: Standardized options data
        current_price: Current price

    Returns:
        Dictionary with liquidity levels
    """
    # Simple implementation to extract basic support/resistance levels
    liquidity_levels = {
        'support': [],
        'resistance': []
    }

    # Use recent lows as support
    if len(price_data) > 20:
        recent_data = price_data.iloc[-20:]
        min_price = recent_data['low'].min()
        liquidity_levels['support'].append({
            'price': min_price,
            'strength': 0.7,
            'type': 'price_action'
        })

    # Use recent highs as resistance
    if len(price_data) > 20:
        recent_data = price_data.iloc[-20:]
        max_price = recent_data['high'].max()
        liquidity_levels['resistance'].append({
            'price': max_price,
            'strength': 0.7,
            'type': 'price_action'
        })

    # Add options-based levels if available
    if options_data is not None and not options_data.empty:
        try:
            # Find strikes with highest open interest
            if 'call_oi' in options_data.columns and 'put_oi' in options_data.columns:
                # Get strikes with highest call OI (potential resistance)
                call_oi_sorted = options_data.sort_values('call_oi', ascending=False)
                if len(call_oi_sorted) > 0:
                    top_call_strike = call_oi_sorted.iloc[0]['strike']
                    if top_call_strike > current_price:
                        liquidity_levels['resistance'].append({
                            'price': top_call_strike,
                            'strength': 0.8,
                            'type': 'options_oi'
                        })

                # Get strikes with highest put OI (potential support)
                put_oi_sorted = options_data.sort_values('put_oi', ascending=False)
                if len(put_oi_sorted) > 0:
                    top_put_strike = put_oi_sorted.iloc[0]['strike']
                    if top_put_strike < current_price:
                        liquidity_levels['support'].append({
                            'price': top_put_strike,
                            'strength': 0.8,
                            'type': 'options_oi'
                        })
        except Exception as e:
            logger.warning(f"Error extracting options-based levels: {str(e)}")

    return liquidity_levels

def main():
    """Main function to retrain models."""
    args = parse_args()

    # Set up output directory
    output_dir = args.output_dir
    if output_dir is None:
        output_dir = os.path.join(current_dir, "models")

    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"Using output directory: {output_dir}")

    # Get training data
    if args.use_real_data:
        tickers = args.tickers.split(',')
        training_samples = get_real_training_data(tickers, args.days)
    else:
        # Use synthetic data generator
        logger.info("Using synthetic data generator")
        data_generator = TrainingDataGenerator(config={'seed': args.seed})
        training_samples = data_generator.generate_training_samples(
            num_samples=10,
            tickers=args.tickers.split(',')
        )

    if not training_samples:
        logger.error("No training samples available")
        return 1

    logger.info(f"Generated {len(training_samples)} training samples")

    # Create model trainer
    model_trainer = LiquidityModelTrainer(config={'models_dir': output_dir})

    # Prepare combined training data
    level_strength_features = []
    level_strength_targets = []
    price_reaction_features = []
    price_reaction_targets = []

    # Load current feature list if available
    current_features = []
    feature_list_path = os.path.join(output_dir, "feature_list.txt")
    if os.path.exists(feature_list_path):
        with open(feature_list_path, 'r') as f:
            current_features = [line.strip() for line in f if line.strip()]
        logger.info(f"Loaded {len(current_features)} current features from {feature_list_path}")

    for i, sample in enumerate(training_samples):
        # Extract features and targets
        features, targets = model_trainer.prepare_training_data(
            price_data=sample['price_data'],
            liquidity_levels=sample['liquidity_levels'],
            options_data=sample['options_data'],
            volume_profile=sample['volume_profile'],
            gex_data=sample['gex_data']
        )

        if features.empty or not targets:
            logger.warning(f"No features or targets extracted for sample {i}")
            continue

        # Align features with current feature set if available
        if current_features:
            logger.info(f"Aligning features with current feature set for sample {i}")
            features = align_features(features, current_features)

        # Add to combined datasets
        if 'level_strength' in targets:
            level_strength_features.append(features)
            level_strength_targets.append(targets['level_strength'])

        if 'price_reaction' in targets:
            price_reaction_features.append(features)
            price_reaction_targets.append(targets['price_reaction'])

    # Combine features and targets
    if level_strength_features:
        all_level_strength_features = pd.concat(level_strength_features, ignore_index=True)
        all_level_strength_targets = np.concatenate(level_strength_targets)

        # Update current features list
        current_features = all_level_strength_features.columns.tolist()
    else:
        all_level_strength_features = pd.DataFrame()
        all_level_strength_targets = np.array([])

    if price_reaction_features:
        all_price_reaction_features = pd.concat(price_reaction_features, ignore_index=True)
        all_price_reaction_targets = np.concatenate(price_reaction_targets)

        # If we don't have level strength features, use price reaction features
        if not current_features:
            current_features = all_price_reaction_features.columns.tolist()
    else:
        all_price_reaction_features = pd.DataFrame()
        all_price_reaction_targets = np.array([])

    # Train models
    logger.info("Training liquidity models")
    start_time = time.time()

    # Train level strength model
    if len(all_level_strength_targets) > 0:
        logger.info(f"Training level strength model with {len(all_level_strength_targets)} samples")
        level_strength_results = model_trainer.train_level_strength_model(
            features=all_level_strength_features,
            targets=all_level_strength_targets
        )

        logger.info(f"Level strength model metrics: "
                   f"RMSE={level_strength_results['rmse']:.4f}, "
                   f"R={level_strength_results['r2']:.4f}, "
                   f"MAE={level_strength_results['mae']:.4f}")
    else:
        logger.warning("No level strength targets available for training")

    # Train price reaction model
    if len(all_price_reaction_targets) > 0:
        logger.info(f"Training price reaction model with {len(all_price_reaction_targets)} samples")
        price_reaction_results = model_trainer.train_price_reaction_model(
            features=all_price_reaction_features,
            targets=all_price_reaction_targets
        )

        logger.info(f"Price reaction model metrics: "
                   f"RMSE={price_reaction_results['rmse']:.4f}, "
                   f"R={price_reaction_results['r2']:.4f}, "
                   f"MAE={price_reaction_results['mae']:.4f}")
    else:
        logger.warning("No price reaction targets available for training")

    # Calculate total training time
    total_time = time.time() - start_time
    logger.info(f"Total training time: {total_time:.2f} seconds")

    # Save feature list for future reference
    if current_features:
        # Save to feature_list.txt
        feature_list_path = os.path.join(output_dir, "feature_list.txt")
        with open(feature_list_path, 'w') as f:
            f.write('\n'.join(current_features))
        logger.info(f"Saved feature list to {feature_list_path}")

        # Also save to current_features.txt in the models directory for ML model compatibility
        current_features_path = os.path.join(output_dir, "current_features.txt")
        with open(current_features_path, 'w') as f:
            f.write('\n'.join(current_features))
        logger.info(f"Saved current features to {current_features_path}")

        # Copy to the standard location for feature compatibility layer
        standard_features_path = os.path.join(current_dir, "models", "current_features.txt")
        os.makedirs(os.path.dirname(standard_features_path), exist_ok=True)
        with open(standard_features_path, 'w') as f:
            f.write('\n'.join(current_features))
        logger.info(f"Saved current features to standard location: {standard_features_path}")

    logger.info("Model retraining completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
