#!/usr/bin/env python3
"""
Command Registry System
Central registry for all available commands with metadata and routing
"""

import json
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

logger = logging.getLogger(__name__)

class CommandCategory(Enum):
    """Command categories for organization"""
    TRADING = "trading"
    SYSTEM = "system"
    DATA = "data" 
    ANALYSIS = "analysis"
    MONITORING = "monitoring"
    CONFIGURATION = "configuration"

@dataclass
class CommandDefinition:
    """Definition of a registered command"""
    command_name: str
    category: CommandCategory
    description: str
    syntax: str
    parameters: List[Dict[str, Any]]
    examples: List[str]
    security_level: int
    validation_required: bool
    estimated_duration: float
    handler_function: Optional[Callable] = None
    aliases: List[str] = None
    deprecated: bool = False
    version: str = "1.0"

class CommandRegistry:
    """
    Central Command Registry
    
    Manages all available commands with:
    - Command definitions and metadata
    - Handler function registration
    - Command discovery and routing
    - Version management
    - Usage analytics
    """
    
    def __init__(self):
        self.commands = {}
        self.categories = {}
        self.aliases = {}
        self.usage_stats = {}
        
        # Initialize with core commands
        self._register_core_commands()
    
    def register_command(self, definition: CommandDefinition) -> bool:
        """Register a new command"""
        try:
            command_name = definition.command_name.lower()
            
            # Check for conflicts
            if command_name in self.commands:
                logger.warning(f"Command {command_name} already registered, overwriting")
            
            # Register main command
            self.commands[command_name] = definition
            
            # Register aliases
            if definition.aliases:
                for alias in definition.aliases:
                    alias_lower = alias.lower()
                    if alias_lower in self.aliases:
                        logger.warning(f"Alias {alias_lower} already exists, overwriting")
                    self.aliases[alias_lower] = command_name
            
            # Add to category index
            category = definition.category.value
            if category not in self.categories:
                self.categories[category] = []
            self.categories[category].append(command_name)
            
            # Initialize usage stats
            self.usage_stats[command_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0,
                'total_execution_time': 0.0,
                'last_used': None
            }
            
            logger.info(f"Command registered: {command_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register command {definition.command_name}: {e}")
            return False
    
    def get_command(self, command_name: str) -> Optional[CommandDefinition]:
        """Get command definition by name or alias"""
        command_name = command_name.lower()
        
        # Check direct command name
        if command_name in self.commands:
            return self.commands[command_name]
        
        # Check aliases
        if command_name in self.aliases:
            actual_name = self.aliases[command_name]
            return self.commands.get(actual_name)
        
        return None
    
    def search_commands(self, query: str) -> List[CommandDefinition]:
        """Search commands by name, description, or category"""
        query = query.lower()
        results = []
        
        for command_name, definition in self.commands.items():
            # Search in name
            if query in command_name:
                results.append(definition)
                continue
            
            # Search in description
            if query in definition.description.lower():
                results.append(definition)
                continue
            
            # Search in category
            if query in definition.category.value:
                results.append(definition)
                continue
        
        return results
    
    def get_commands_by_category(self, category: CommandCategory) -> List[CommandDefinition]:
        """Get all commands in a specific category"""
        category_name = category.value
        command_names = self.categories.get(category_name, [])
        return [self.commands[name] for name in command_names if name in self.commands]
    
    def update_usage_stats(self, command_name: str, execution_time: float, success: bool):
        """Update usage statistics for a command"""
        command_name = command_name.lower()
        
        # Resolve alias to actual command name
        if command_name in self.aliases:
            command_name = self.aliases[command_name]
        
        if command_name in self.usage_stats:
            stats = self.usage_stats[command_name]
            stats['total_calls'] += 1
            stats['total_execution_time'] += execution_time
            stats['last_used'] = datetime.now().isoformat()
            
            if success:
                stats['successful_calls'] += 1
            else:
                stats['failed_calls'] += 1
    
    def get_command_help(self, command_name: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive help for a command"""
        definition = self.get_command(command_name)
        
        if not definition:
            return None
        
        stats = self.usage_stats.get(definition.command_name.lower(), {})
        
        return {
            'name': definition.command_name,
            'category': definition.category.value,
            'description': definition.description,
            'syntax': definition.syntax,
            'parameters': definition.parameters,
            'examples': definition.examples,
            'security_level': definition.security_level,
            'validation_required': definition.validation_required,
            'estimated_duration': definition.estimated_duration,
            'aliases': definition.aliases or [],
            'deprecated': definition.deprecated,
            'version': definition.version,
            'usage_stats': stats
        }
    
    def get_all_commands(self) -> Dict[str, Any]:
        """Get summary of all registered commands"""
        commands_summary = {}
        
        for category in CommandCategory:
            category_commands = self.get_commands_by_category(category)
            commands_summary[category.value] = [
                {
                    'name': cmd.command_name,
                    'description': cmd.description,
                    'syntax': cmd.syntax,
                    'deprecated': cmd.deprecated
                }
                for cmd in category_commands
            ]
        
        return commands_summary
    
    def _register_core_commands(self):
        """Register core system commands"""
        
        # Trading Commands
        trading_commands = [
            CommandDefinition(
                command_name="buy",
                category=CommandCategory.TRADING,
                description="Execute a buy order for specified symbol",
                syntax="buy <symbol> [quantity] [options]",
                parameters=[
                    {"name": "symbol", "type": "string", "required": True, "description": "Trading symbol"},
                    {"name": "quantity", "type": "number", "required": False, "description": "Number of shares"},
                    {"name": "options", "type": "string", "required": False, "description": "Additional options"}
                ],
                examples=[
                    "buy AAPL",
                    "buy AAPL 100",
                    "buy AAPL 100 market"
                ],
                security_level=3,
                validation_required=True,
                estimated_duration=2.0,
                aliases=["purchase", "long"]
            ),
            
            CommandDefinition(
                command_name="sell",
                category=CommandCategory.TRADING,
                description="Execute a sell order for specified symbol",
                syntax="sell <symbol> [quantity] [options]",
                parameters=[
                    {"name": "symbol", "type": "string", "required": True, "description": "Trading symbol"},
                    {"name": "quantity", "type": "number", "required": False, "description": "Number of shares"},
                    {"name": "options", "type": "string", "required": False, "description": "Additional options"}
                ],
                examples=[
                    "sell AAPL",
                    "sell AAPL 50",
                    "sell AAPL all"
                ],
                security_level=3,
                validation_required=True,
                estimated_duration=2.0,
                aliases=["short"]
            ),
            
            CommandDefinition(
                command_name="close",
                category=CommandCategory.TRADING,
                description="Close position for specified symbol",
                syntax="close <symbol>",
                parameters=[
                    {"name": "symbol", "type": "string", "required": True, "description": "Trading symbol"}
                ],
                examples=[
                    "close AAPL",
                    "close all"
                ],
                security_level=3,
                validation_required=True,
                estimated_duration=2.0,
                aliases=["exit"]
            )
        ]
        
        # System Commands
        system_commands = [
            CommandDefinition(
                command_name="status",
                category=CommandCategory.SYSTEM,
                description="Get status of system components",
                syntax="status [component]",
                parameters=[
                    {"name": "component", "type": "string", "required": False, "description": "Specific component"}
                ],
                examples=[
                    "status",
                    "status monitoring",
                    "status agents"
                ],
                security_level=1,
                validation_required=False,
                estimated_duration=1.0,
                aliases=["stat", "info"]
            ),
            
            CommandDefinition(
                command_name="start",
                category=CommandCategory.SYSTEM,
                description="Start system component",
                syntax="start <component>",
                parameters=[
                    {"name": "component", "type": "string", "required": True, "description": "Component to start"}
                ],
                examples=[
                    "start monitoring",
                    "start agents",
                    "start data_feed"
                ],
                security_level=2,
                validation_required=True,
                estimated_duration=5.0
            ),
            
            CommandDefinition(
                command_name="stop",
                category=CommandCategory.SYSTEM,
                description="Stop system component",
                syntax="stop <component>",
                parameters=[
                    {"name": "component", "type": "string", "required": True, "description": "Component to stop"}
                ],
                examples=[
                    "stop monitoring",
                    "stop agents",
                    "stop data_feed"
                ],
                security_level=2,
                validation_required=True,
                estimated_duration=3.0,
                aliases=["halt"]
            )
        ]
        
        # Data Commands
        data_commands = [
            CommandDefinition(
                command_name="get",
                category=CommandCategory.DATA,
                description="Retrieve data for specified target",
                syntax="get <data_type> [parameters]",
                parameters=[
                    {"name": "data_type", "type": "string", "required": True, "description": "Type of data to retrieve"},
                    {"name": "parameters", "type": "string", "required": False, "description": "Additional parameters"}
                ],
                examples=[
                    "get market_data AAPL",
                    "get portfolio",
                    "get performance last_week"
                ],
                security_level=1,
                validation_required=False,
                estimated_duration=1.0,
                aliases=["fetch", "retrieve"]
            ),
            
            CommandDefinition(
                command_name="update",
                category=CommandCategory.DATA,
                description="Update data sources",
                syntax="update <target>",
                parameters=[
                    {"name": "target", "type": "string", "required": True, "description": "Target to update"}
                ],
                examples=[
                    "update market_data",
                    "update portfolio",
                    "update all"
                ],
                security_level=1,
                validation_required=False,
                estimated_duration=2.0,
                aliases=["refresh"]
            )
        ]
        
        # Analysis Commands
        analysis_commands = [
            CommandDefinition(
                command_name="analyze",
                category=CommandCategory.ANALYSIS,
                description="Perform analysis on specified target",
                syntax="analyze <target> [parameters]",
                parameters=[
                    {"name": "target", "type": "string", "required": True, "description": "Analysis target"},
                    {"name": "parameters", "type": "string", "required": False, "description": "Analysis parameters"}
                ],
                examples=[
                    "analyze portfolio",
                    "analyze AAPL technical",
                    "analyze risk exposure"
                ],
                security_level=1,
                validation_required=False,
                estimated_duration=3.0
            ),
            
            CommandDefinition(
                command_name="calculate",
                category=CommandCategory.ANALYSIS,
                description="Perform calculations",
                syntax="calculate <calculation> [parameters]",
                parameters=[
                    {"name": "calculation", "type": "string", "required": True, "description": "Type of calculation"},
                    {"name": "parameters", "type": "string", "required": False, "description": "Calculation parameters"}
                ],
                examples=[
                    "calculate roi",
                    "calculate var portfolio",
                    "calculate sharpe_ratio"
                ],
                security_level=1,
                validation_required=False,
                estimated_duration=1.0,
                aliases=["compute"]
            )
        ]
        
        # Register all commands
        all_commands = trading_commands + system_commands + data_commands + analysis_commands
        
        for command in all_commands:
            self.register_command(command)
        
        logger.info(f"Registered {len(all_commands)} core commands")
    
    def export_command_registry(self) -> Dict[str, Any]:
        """Export complete command registry"""
        return {
            'commands': {name: asdict(cmd) for name, cmd in self.commands.items()},
            'categories': self.categories,
            'aliases': self.aliases,
            'usage_stats': self.usage_stats,
            'export_timestamp': datetime.now().isoformat()
        }

# Global registry instance
_global_registry = None

def get_command_registry() -> CommandRegistry:
    """Get global command registry instance"""
    global _global_registry
    if _global_registry is None:
        _global_registry = CommandRegistry()
    return _global_registry

if __name__ == "__main__":
    # Test command registry
    registry = CommandRegistry()
    
    # Test command lookup
    buy_cmd = registry.get_command("buy")
    if buy_cmd:
        print(f"Buy command: {buy_cmd.description}")
        print(f"Syntax: {buy_cmd.syntax}")
    
    # Test search
    trading_commands = registry.search_commands("trading")
    print(f"\nFound {len(trading_commands)} trading commands")
    
    # Test help
    help_info = registry.get_command_help("buy")
    if help_info:
        print(f"\nHelp for 'buy': {help_info['description']}")
    
    # Test category listing
    all_commands = registry.get_all_commands()
    for category, commands in all_commands.items():
        print(f"\n{category.upper()}: {len(commands)} commands")
