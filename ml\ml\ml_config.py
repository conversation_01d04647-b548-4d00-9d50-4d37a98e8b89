"""
ML Configuration Module

This module provides utilities for loading ML configuration.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

# Setup logger
logger = logging.getLogger("ml_config")

# Default configurations
DEFAULT_CONFIG = {
    "pattern_recognition": {
        "model_type": "transformer",
        "sequence_length": 50,
        "batch_size": 32,
        "input_size": 64,
        "thresholds": {
            "double_top": 0.7,
            "double_bottom": 0.7,
            "head_shoulders": 0.8,
            "inv_head_shoulders": 0.8,
            "triangle": 0.6,
            "wedge": 0.6,
            "channel": 0.6,
            "flag": 0.7,
            "bull_flag": 0.7,
            "bear_flag": 0.7,
            "bull_trap": 0.75,
            "bear_trap": 0.75,
            "breakout": 0.65,
            "breakdown": 0.65
        },
        "feature_config": {
            "feature_groups": [
                "price", "volume", "volatility", "momentum"
            ]
        }
    },
    "predictive_analytics": {
        "model_type": "seq2seq",
        "sequence_length": 50,
        "prediction_horizon": 10,
        "batch_size": 32,
        "input_size": 64,
        "output_size": 1,
        "feature_config": {
            "feature_groups": [
                "price", "volume", "volatility", "momentum", "mean_reversion"
            ]
        }
    },
    "dashboard_integration": {
        "enabled": True,
        "refresh_interval": 60,
        "default_models": ["pattern_recognition", "price_prediction"]
    }
}

# Global config cache
_config_cache = {}

def get_ml_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Get ML configuration.
    
    Args:
        config_path: Optional path to configuration file
        
    Returns:
        Configuration dictionary
    """
    global _config_cache
    
    # Return cached config if available and no specific path requested
    if not config_path and _config_cache:
        return _config_cache
    
    # Load config from file if provided
    if config_path and os.path.exists(config_path):
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            logger.info(f"Loaded ML configuration from {config_path}")
            
            # Cache the loaded config
            if not config_path:
                _config_cache = config
                
            return config
        except Exception as e:
            logger.error(f"Error loading ML configuration from {config_path}: {e}")
    
    # Return default config
    logger.info("Using default ML configuration")
    
    # Cache the default config
    if not config_path:
        _config_cache = DEFAULT_CONFIG
        
    return DEFAULT_CONFIG

def get_component_config(component_name: str, config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Get configuration for a specific component.
    
    Args:
        component_name: Name of the component
        config_path: Optional path to configuration file
        
    Returns:
        Component configuration dictionary
    """
    config = get_ml_config(config_path)
    return config.get(component_name, {})

def save_ml_config(config: Dict[str, Any], config_path: str) -> bool:
    """
    Save ML configuration to file.
    
    Args:
        config: Configuration dictionary
        config_path: Path to save configuration file
        
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # Write config to file
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"Saved ML configuration to {config_path}")
        
        # Update cache
        global _config_cache
        _config_cache = config
        
        return True
    except Exception as e:
        logger.error(f"Error saving ML configuration to {config_path}: {e}")
        return False

def update_component_config(component_name: str, component_config: Dict[str, Any], 
                         config_path: Optional[str] = None) -> bool:
    """
    Update configuration for a specific component.
    
    Args:
        component_name: Name of the component
        component_config: Component configuration dictionary
        config_path: Optional path to configuration file
        
    Returns:
        True if updated successfully, False otherwise
    """
    # Get current config
    config = get_ml_config(config_path)
    
    # Update component config
    config[component_name] = component_config
    
    # Save if path provided, otherwise just update cache
    if config_path:
        return save_ml_config(config, config_path)
    else:
        global _config_cache
        _config_cache = config
        return True

def reset_config_cache() -> None:
    """Reset the configuration cache."""
    global _config_cache
    _config_cache = {}
    logger.info("Reset ML configuration cache")
