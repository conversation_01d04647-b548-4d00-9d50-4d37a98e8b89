#!/usr/bin/env python3
"""
Simple HTTP API Server for MCP Data
Direct implementation using your existing API gateway
"""

import os
import sys
import json
import logging
from flask import Flask, request, jsonify
from pathlib import Path

# Add the api directory to the path
sys.path.append(str(Path(__file__).parent))

try:
    from unified_api_gateway import get_api_gateway
    API_AVAILABLE = True
    print("SUCCESS: API Gateway available")
except ImportError as e:
    API_AVAILABLE = False
    print(f"ERROR: API Gateway not available: {e}")

# Create Flask app
app = Flask(__name__)

# Global API gateway instance
api_gateway = None

def initialize_api():
    """Initialize the API gateway"""
    global api_gateway
    if API_AVAILABLE:
        try:
            api_gateway = get_api_gateway()
            if api_gateway.ping():
                print("SUCCESS: API Gateway initialized and responding")
                return True
        except Exception as e:
            print(f"ERROR: Failed to initialize API Gateway: {e}")
    return False

@app.route('/')
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "ok", 
        "service": "MCP Direct HTTP API",
        "api_available": API_AVAILABLE,
        "api_ready": api_gateway is not None
    })

@app.route('/bars')
def get_bars():
    """Get bars/price data"""
    ticker = request.args.get('tk') or request.args.get('ticker')
    timeframe = request.args.get('tf') or request.args.get('timeframe', '1')
    
    if not ticker:
        return jsonify({"error": "Missing ticker parameter"}), 400
    
    if not api_gateway:
        return jsonify({"error": "API Gateway not available"}), 503
    
    try:
        # Get price data from the API gateway
        price_data = api_gateway.get_price_data(ticker)
        
        # Convert DataFrame to list of dicts if needed
        if hasattr(price_data, 'to_dict'):
            data = price_data.to_dict('records')
        elif isinstance(price_data, list):
            data = price_data
        else:
            data = []
        
        return jsonify({
            "data": data,
            "ticker": ticker,
            "timeframe": timeframe,
            "count": len(data)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/options')
def get_options():
    """Get options chain data"""
    ticker = request.args.get('tk') or request.args.get('ticker')
    expiry = request.args.get('expiry')
    
    if not ticker:
        return jsonify({"error": "Missing ticker parameter"}), 400
    
    if not api_gateway:
        return jsonify({"error": "API Gateway not available"}), 503
    
    try:
        # Get options chain from the API gateway
        options_data = api_gateway.get_options_chain(ticker, expiry=expiry)
        
        # Convert DataFrame to list of dicts if needed
        if hasattr(options_data, 'to_dict'):
            data = options_data.to_dict('records')
        elif isinstance(options_data, list):
            data = options_data
        else:
            data = []
        
        return jsonify({
            "data": data,
            "ticker": ticker,
            "expiry": expiry,
            "count": len(data)
        })
        
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/bars')
def get_bars_api():
    """Alternative API endpoint for bars"""
    return get_bars()

@app.route('/api/options') 
def get_options_api():
    """Alternative API endpoint for options"""
    return get_options()

@app.route('/spot/<ticker>')
def get_spot_price(ticker):
    """Get current spot price"""
    if not api_gateway:
        return jsonify({"error": "API Gateway not available"}), 503
    
    try:
        price = api_gateway.get_spot_price(ticker)
        return jsonify({
            "ticker": ticker,
            "price": price
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

def start_direct_api(port: int = 8000):
    """Start the direct HTTP API server"""
    print("Starting MCP Direct HTTP API")
    print("=" * 40)
    
    # Initialize API
    if initialize_api():
        print(f"SUCCESS: Starting HTTP server on port {port}")
        print(f"SUCCESS: Endpoints available at http://localhost:{port}")
        print("Available endpoints:")
        print(f"  GET /                    - Health check")
        print(f"  GET /bars?tk=AAPL&tf=1   - Price/bars data")
        print(f"  GET /options?tk=AAPL     - Options chain")
        print(f"  GET /spot/AAPL           - Current price")
        print(f"  GET /api/bars            - Alternative bars endpoint")
        print(f"  GET /api/options         - Alternative options endpoint")
        print()
        
        # Start Flask server
        app.run(host='0.0.0.0', port=port, debug=False)
    else:
        print("ERROR: Failed to initialize API - check your configuration")
        print("Make sure POLYGON_API_KEY is set if using Polygon data")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP Direct HTTP API")
    parser.add_argument("--port", type=int, default=8000, help="HTTP server port")
    
    args = parser.parse_args()
    
    start_direct_api(args.port)
