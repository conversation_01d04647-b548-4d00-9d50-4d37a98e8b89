[{"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:34:47.224394", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:34:47.224354", "success": false, "latency_ms": 2544.898499996634, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:34:47.224882", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:34:47.224870", "success": false, "latency_ms": 0.011900003300979733, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:34:47.225689", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:34:47.225678", "success": false, "latency_ms": 0.008699993486516178, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:39:47.227517", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:39:47.227450", "success": false, "latency_ms": 0.09459999273531139, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:39:47.230061", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:39:47.230044", "success": false, "latency_ms": 0.021499989088624716, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:39:47.230666", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:39:47.230657", "success": false, "latency_ms": 0.008400005754083395, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:44:47.231897", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:44:47.231868", "success": false, "latency_ms": 0.03369999467395246, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:44:47.233112", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:44:47.233098", "success": false, "latency_ms": 0.015400000847876072, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:44:47.233892", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:44:47.233880", "success": false, "latency_ms": 0.00890000956133008, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:44:47.311424", "result": {"timestamp": "2025-06-24T09:44:47.311333", "total_execution_time_ms": 76.49040000978857, "budget_compliance": false, "error": "cannot import name 'get_config' from 'config.settings' (D:\\script-work\\CORE\\config\\settings.py)", "overall_success": false}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:49:47.313269", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:49:47.313231", "success": false, "latency_ms": 0.060699996538460255, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:49:47.314401", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:49:47.314383", "success": false, "latency_ms": 0.018399994587525725, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:49:47.315278", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:49:47.315267", "success": false, "latency_ms": 0.0137000170070678, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:54:47.316650", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:54:47.316616", "success": false, "latency_ms": 0.02880001557059586, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:54:47.317702", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:54:47.317693", "success": false, "latency_ms": 0.007800001185387373, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:54:47.318541", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:54:47.318527", "success": false, "latency_ms": 0.007399998139590025, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:59:47.320053", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:59:47.320032", "success": false, "latency_ms": 0.03130000550299883, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:59:47.321237", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:59:47.321228", "success": false, "latency_ms": 0.008500006515532732, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:59:47.322185", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:59:47.322175", "success": false, "latency_ms": 0.007599999662488699, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:59:49.036785", "result": {"timestamp": "2025-06-24T09:59:49.036755", "total_execution_time_ms": 1713.6382999888156, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:04:49.038855", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:04:49.038833", "success": false, "latency_ms": 0.03429999924264848, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:04:49.040174", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:04:49.040162", "success": false, "latency_ms": 0.014199991710484028, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:04:49.041250", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:04:49.041241", "success": false, "latency_ms": 0.010500021744519472, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:09:49.042626", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:09:49.042603", "success": false, "latency_ms": 0.03390002530068159, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:09:49.044145", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:09:49.044135", "success": false, "latency_ms": 0.00869997893460095, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:09:49.045412", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:09:49.045401", "success": false, "latency_ms": 0.008000002708286047, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:14:49.047428", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:14:49.047401", "success": false, "latency_ms": 0.037200021324679255, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:14:49.049290", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:14:49.049276", "success": false, "latency_ms": 0.013299984857439995, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:14:49.050974", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:14:49.050950", "success": false, "latency_ms": 0.031800009310245514, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:14:49.053114", "result": {"timestamp": "2025-06-24T10:14:49.053090", "total_execution_time_ms": 0.026699999580159783, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:19:49.056011", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:19:49.055978", "success": false, "latency_ms": 0.043199979700148106, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:19:49.058920", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:19:49.058903", "success": false, "latency_ms": 0.016300007700920105, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:19:49.061586", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:19:49.061569", "success": false, "latency_ms": 0.014499993994832039, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:24:49.064122", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:24:49.064099", "success": false, "latency_ms": 0.045900000259280205, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:24:49.066510", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:24:49.066498", "success": false, "latency_ms": 0.010300020221620798, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:24:49.068193", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:24:49.068183", "success": false, "latency_ms": 0.008500006515532732, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:29:49.070494", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:29:49.070471", "success": false, "latency_ms": 0.03199998172931373, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:29:49.072429", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:29:49.072417", "success": false, "latency_ms": 0.010399991879239678, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:29:49.074102", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:29:49.074091", "success": false, "latency_ms": 0.009100011084228754, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093444", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:29:49.075821", "result": {"timestamp": "2025-06-24T10:29:49.075810", "total_execution_time_ms": 0.013299984857439995, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}]