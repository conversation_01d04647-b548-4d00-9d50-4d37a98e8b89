{"test_execution_time": "2025-06-26T08:08:47.207808", "overall_metrics": {"total_tests": 12, "passed_tests": 12, "failed_tests": 0, "success_rate_percentage": 100.0, "overall_status": "EXCELLENT"}, "performance_metrics": {"avg_response_time_seconds": 2.726, "max_response_time_seconds": 10.181, "min_response_time_seconds": 2.035, "total_response_measurements": 12}, "category_results": {"Infrastructure Tests": ["TestResult(test_name='Server Availability', passed=True, execution_time=2.0680110454559326, error_message=None, response_data={'status': 'healthy', 'service': 'schwab-http-mcp-server', 'timestamp': **********.5540514, 'uptime_seconds': 168.34725832939148}, performance_metrics={'status_code': 200})", "TestResult(test_name='Health Endpoint', passed=True, execution_time=2.0481531620025635, error_message=None, response_data={'status': 'healthy', 'service': 'schwab-http-mcp-server', 'timestamp': **********.6046457, 'uptime_seconds': 170.39785194396973}, performance_metrics={'response_size': 122})"], "Basic Endpoint Tests": ["TestResult(test_name='Quotes Endpoint (SPY)', passed=True, execution_time=2.052042245864868, error_message=None, response_data={'symbol': 'SPY', 'last_price': 463.25, 'bid': 463.2, 'ask': 463.3, 'volume': 45250000, 'timestamp': **********.656875}, performance_metrics={'data_size': 119})", "TestResult(test_name='Quotes Endpoint (AAPL)', passed=True, execution_time=2.034630537033081, error_message=None, response_data={'symbol': 'AAPL', 'last_price': 195.89, 'bid': 195.85, 'ask': 195.95, 'volume': 28340000, 'timestamp': **********.6916652}, performance_metrics={'data_size': 123})", "TestResult(test_name='Quotes Endpoint (MSFT)', passed=True, execution_time=2.0356249809265137, error_message=None, response_data={'symbol': 'MSFT', 'last_price': 442.78, 'bid': 442.7, 'ask': 442.85, 'volume': 18920000, 'timestamp': 1750939704.7273521}, performance_metrics={'data_size': 122})", "TestResult(test_name='Options Endpoint (SPY)', passed=True, execution_time=2.0366015434265137, error_message=None, response_data={'symbol': 'SPY', 'expiration_date': '2025-01-17', 'calls': [{'strike': 460.0, 'bid': 8.5, 'ask': 8.7, 'last': 8.6, 'volume': 1250, 'open_interest': 15420}], 'puts': [{'strike': 460.0, 'bid': 4.8, 'ask': 5.0, 'last': 4.9, 'volume': 890, 'open_interest': 18920}], 'timestamp': 1750939706.764075}, performance_metrics={'data_size': 294})", "TestResult(test_name='Market Hours Endpoint', passed=True, execution_time=2.0545740127563477, error_message=None, response_data={'market': 'US_EQUITY', 'date': '2025-06-26', 'is_open': True, 'session_hours': {'regular': {'start': '09:30:00', 'end': '16:00:00'}}, 'timestamp': **********.8172383}, performance_metrics=None)", "TestResult(test_name='Metrics Endpoint', passed=True, execution_time=2.0488436222076416, error_message=None, response_data={'performance': {'total_requests': 6, 'error_count': 0, 'error_rate_percentage': 0.0, 'requests_per_second': 0.03}, 'system': {'uptime_seconds': 182.66, 'status': 'operational', 'server_type': 'schwab_http_mcp'}, 'endpoints': {'health': 'operational', 'quotes': 'operational', 'options': 'operational', 'market_hours': 'operational', 'metrics': 'operational'}, 'timestamp': **********.8678868}, performance_metrics=None)"], "Performance Tests": ["TestResult(test_name='Response Time', passed=True, execution_time=10.181029319763184, error_message=None, response_data=None, performance_metrics={'avg_response_time': 2.036205863952637, 'max_response_time': 2.054544687271118, 'min_response_time': 2.017782211303711, 'total_calls': 5})", "TestResult(test_name='Concurrent Requests', passed=True, execution_time=2.053070068359375, error_message=None, response_data=None, performance_metrics={'success_count': 3, 'total_requests': 3, 'success_rate': 100.0})"], "Error Handling Tests": ["TestResult(test_name='Invalid Symbol Handling', passed=True, execution_time=2.0496392250061035, error_message=None, response_data=None, performance_metrics={'status_code': 500})", "TestResult(test_name='Invalid Endpoint Handling', passed=True, execution_time=2.0539398193359375, error_message=None, response_data=None, performance_metrics={'status_code': 404})"]}, "detailed_test_results": [{"test_name": "Server Availability", "passed": true, "execution_time": 2.0680110454559326, "error_message": null, "performance_metrics": {"status_code": 200}}, {"test_name": "Health Endpoint", "passed": true, "execution_time": 2.0481531620025635, "error_message": null, "performance_metrics": {"response_size": 122}}, {"test_name": "Quotes Endpoint (SPY)", "passed": true, "execution_time": 2.052042245864868, "error_message": null, "performance_metrics": {"data_size": 119}}, {"test_name": "Quotes Endpoint (AAPL)", "passed": true, "execution_time": 2.034630537033081, "error_message": null, "performance_metrics": {"data_size": 123}}, {"test_name": "Quotes Endpoint (MSFT)", "passed": true, "execution_time": 2.0356249809265137, "error_message": null, "performance_metrics": {"data_size": 122}}, {"test_name": "Options Endpoint (SPY)", "passed": true, "execution_time": 2.0366015434265137, "error_message": null, "performance_metrics": {"data_size": 294}}, {"test_name": "Market Hours Endpoint", "passed": true, "execution_time": 2.0545740127563477, "error_message": null, "performance_metrics": null}, {"test_name": "Metrics Endpoint", "passed": true, "execution_time": 2.0488436222076416, "error_message": null, "performance_metrics": null}, {"test_name": "Response Time", "passed": true, "execution_time": 10.181029319763184, "error_message": null, "performance_metrics": {"avg_response_time": 2.036205863952637, "max_response_time": 2.054544687271118, "min_response_time": 2.017782211303711, "total_calls": 5}}, {"test_name": "Concurrent Requests", "passed": true, "execution_time": 2.053070068359375, "error_message": null, "performance_metrics": {"success_count": 3, "total_requests": 3, "success_rate": 100.0}}, {"test_name": "Invalid Symbol Handling", "passed": true, "execution_time": 2.0496392250061035, "error_message": null, "performance_metrics": {"status_code": 500}}, {"test_name": "Invalid Endpoint Handling", "passed": true, "execution_time": 2.0539398193359375, "error_message": null, "performance_metrics": {"status_code": 404}}], "recommendations": ["System performing well - continue monitoring"]}