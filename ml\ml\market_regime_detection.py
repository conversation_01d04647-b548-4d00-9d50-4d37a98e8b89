"""
Market Regime Detection Module

This module provides functionality for detecting market regimes and extracting
regime-specific features for liquidity analysis.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import time
from datetime import datetime
from enum import Enum
import warnings

# Try to import sklearn components
try:
    from sklearn.cluster import KMeans, DBSCAN
    from sklearn.mixture import GaussianMixture
    from sklearn.preprocessing import StandardScaler
    from sklearn.decomposition import PCA
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    warnings.warn("scikit-learn not available, some market regime detection methods will be disabled")

# Try to import statsmodels for regime switching models
try:
    import statsmodels.api as sm
    from statsmodels.tsa.regime_switching.markov_regression import MarkovRegression
    HAS_STATSMODELS = True
except ImportError:
    HAS_STATSMODELS = False
    warnings.warn("statsmodels not available, Markov regime switching will be disabled")

# Internal imports
from ml_logging import get_logger

# Setup logger
logger = get_logger('market_regime_detection')


class MarketRegime(Enum):
    """Enum for different market regimes."""
    TRENDING_UP = 'trending_up'
    TRENDING_DOWN = 'trending_down'
    RANGING = 'ranging'
    VOLATILE = 'volatile'
    CHOPPY = 'choppy'
    LOW_VOLATILITY = 'low_volatility'
    HIGH_VOLATILITY = 'high_volatility'
    UNKNOWN = 'unknown'


class MarketRegimeDetector:
    """
    Market regime detector for liquidity analysis.

    This class provides methods for detecting market regimes using various
    techniques including statistical methods, clustering, and Markov models.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the market regime detector.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Set default configuration
        self.detection_methods = self.config.get('detection_methods', [
            'statistical', 'clustering', 'markov_switching'
        ])

        # Initialize models
        self.models = {}
        self.initialized = False

        # Configuration parameters
        self.lookback_window = self.config.get('lookback_window', 100)
        self.min_samples = self.config.get('min_samples', 30)
        self.n_regimes = self.config.get('n_regimes', 4)
        self.random_state = self.config.get('random_state', 42)

        # Thresholds for statistical detection
        self.trend_threshold = self.config.get('trend_threshold', 0.6)
        self.volatility_threshold = self.config.get('volatility_threshold', 1.5)
        self.range_threshold = self.config.get('range_threshold', 0.3)

        # Initialize models if sklearn is available
        if HAS_SKLEARN:
            self._initialize_models()

        logger.info("Market regime detector initialized")

    def _initialize_models(self):
        """Initialize the detection models."""
        try:
            # Initialize clustering models
            if 'clustering' in self.detection_methods:
                self.models['kmeans'] = {
                    'model': KMeans(
                        n_clusters=self.n_regimes,
                        random_state=self.random_state
                    ),
                    'trained': False
                }

                self.models['gmm'] = {
                    'model': GaussianMixture(
                        n_components=self.n_regimes,
                        random_state=self.random_state
                    ),
                    'trained': False
                }

            # Initialize Markov switching model if statsmodels is available
            if 'markov_switching' in self.detection_methods and HAS_STATSMODELS:
                # This will be initialized when data is available
                self.models['markov_switching'] = {
                    'model': None,
                    'trained': False
                }

            self.initialized = True
            logger.info("Market regime detection models initialized")

        except Exception as e:
            logger.error(f"Error initializing market regime detection models: {str(e)}")
            self.initialized = False

    def extract_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract features for market regime detection.

        Args:
            price_data: DataFrame with OHLCV price data

        Returns:
            DataFrame with extracted features
        """
        if len(price_data) < self.min_samples:
            logger.warning(f"Not enough data for feature extraction: {len(price_data)} < {self.min_samples}")
            return pd.DataFrame()

        try:
            # Calculate returns
            returns = price_data['close'].pct_change().dropna()

            # Calculate volatility features
            volatility = returns.rolling(window=20).std().dropna()

            # Calculate trend features
            price = price_data['close']
            sma20 = price.rolling(window=20).mean().dropna()
            sma50 = price.rolling(window=50).mean().dropna()

            # Ensure all series have the same length
            min_length = min(len(returns), len(volatility), len(sma20), len(sma50))

            # Create feature DataFrame
            features = pd.DataFrame({
                'returns': returns.iloc[-min_length:].values,
                'volatility': volatility.iloc[-min_length:].values,
                'sma20_dist': (price.iloc[-min_length:].values - sma20.iloc[-min_length:].values) / price.iloc[-min_length:].values,
                'sma_ratio': sma20.iloc[-min_length:].values / sma50.iloc[-min_length:].values
            })

            # Add additional features
            features['range_ratio'] = (price_data['high'].iloc[-min_length:].values - price_data['low'].iloc[-min_length:].values) / price_data['close'].iloc[-min_length:].values
            features['volume_change'] = price_data['volume'].pct_change().iloc[-min_length:].values

            # Fill NaN values
            features = features.fillna(0)

            return features

        except Exception as e:
            logger.error(f"Error extracting features for market regime detection: {str(e)}")
            return pd.DataFrame()

    def detect_regime_statistical(self, features: pd.DataFrame) -> MarketRegime:
        """
        Detect market regime using statistical methods.

        Args:
            features: DataFrame with extracted features

        Returns:
            Detected market regime
        """
        if len(features) < self.min_samples:
            return MarketRegime.UNKNOWN

        try:
            # Get the most recent values
            recent_features = features.iloc[-self.lookback_window:]

            # Calculate trend strength
            trend_strength = recent_features['sma_ratio'].mean()
            trend_direction = recent_features['sma20_dist'].mean()

            # Calculate volatility
            volatility = recent_features['volatility'].mean()
            range_ratio = recent_features['range_ratio'].mean()

            # Determine regime based on rules
            if volatility > self.volatility_threshold:
                if trend_strength > self.trend_threshold:
                    return MarketRegime.TRENDING_UP if trend_direction > 0 else MarketRegime.TRENDING_DOWN
                else:
                    return MarketRegime.VOLATILE
            else:
                if range_ratio < self.range_threshold:
                    return MarketRegime.LOW_VOLATILITY
                elif trend_strength > self.trend_threshold:
                    return MarketRegime.TRENDING_UP if trend_direction > 0 else MarketRegime.TRENDING_DOWN
                else:
                    return MarketRegime.RANGING

        except Exception as e:
            logger.error(f"Error detecting regime using statistical methods: {str(e)}")
            return MarketRegime.UNKNOWN

    def train_models(self, features: pd.DataFrame) -> Dict[str, Any]:
        """
        Train market regime detection models.

        Args:
            features: DataFrame with extracted features

        Returns:
            Dictionary with training results
        """
        if not HAS_SKLEARN:
            logger.warning("scikit-learn not available, cannot train models")
            return {'success': False, 'error': 'scikit-learn not available'}

        if len(features) < self.min_samples:
            logger.warning(f"Not enough data for training: {len(features)} < {self.min_samples}")
            return {'success': False, 'error': 'Not enough data'}

        start_time = time.time()
        results = {}

        try:
            # Standardize features
            scaler = StandardScaler()
            scaled_features = scaler.fit_transform(features)

            # Train each model
            for method, model_info in self.models.items():
                if method == 'markov_switching':
                    if HAS_STATSMODELS:
                        # Train Markov switching model
                        try:
                            # Use returns as the target variable
                            returns = features['returns'].values

                            # Create and fit Markov switching model
                            model = MarkovRegression(
                                returns, k_regimes=self.n_regimes,
                                trend='c', switching_variance=True
                            )
                            model_fit = model.fit()

                            # Store the fitted model
                            self.models[method]['model'] = model_fit
                            self.models[method]['trained'] = True
                            results[method] = {'success': True}

                        except Exception as e:
                            logger.error(f"Error training Markov switching model: {str(e)}")
                            results[method] = {'success': False, 'error': str(e)}
                    else:
                        results[method] = {'success': False, 'error': 'statsmodels not available'}
                else:
                    # Train scikit-learn models
                    model = model_info['model']

                    if hasattr(model, 'fit'):
                        model.fit(scaled_features)
                        model_info['trained'] = True
                        model_info['scaler'] = scaler
                        results[method] = {'success': True}
                    else:
                        logger.warning(f"Model {method} does not have fit method")
                        results[method] = {'success': False, 'error': 'No fit method'}

            elapsed = time.time() - start_time
            logger.info(f"Trained market regime detection models in {elapsed:.2f} seconds")

            return {
                'success': True,
                'elapsed_time': elapsed,
                'results': results
            }

        except Exception as e:
            logger.error(f"Error training market regime detection models: {str(e)}")
            return {'success': False, 'error': str(e)}

    def detect_regime(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Detect market regime using all available methods.

        Args:
            price_data: DataFrame with OHLCV price data

        Returns:
            Dictionary with detected regimes and confidence scores
        """
        start_time = time.time()

        # Extract features
        features = self.extract_features(price_data)

        if len(features) < self.min_samples:
            logger.warning(f"Not enough data for regime detection: {len(features)} < {self.min_samples}")
            return {
                'regime': MarketRegime.UNKNOWN.value,
                'confidence': 0.0,
                'method': 'insufficient_data'
            }

        try:
            # Detect regime using statistical methods
            statistical_regime = self.detect_regime_statistical(features)

            # Initialize results
            regimes = {regime.value: 0.0 for regime in MarketRegime}
            regimes[statistical_regime.value] += 1.0

            # Use clustering methods if available
            if HAS_SKLEARN and 'clustering' in self.detection_methods:
                # Train models if not already trained
                if not all(model_info.get('trained', False) for method, model_info in self.models.items()
                          if method in ['kmeans', 'gmm']):
                    self.train_models(features)

                # Standardize features
                recent_features = features.iloc[-1:].values

                # Detect regime using KMeans
                if 'kmeans' in self.models and self.models['kmeans'].get('trained', False):
                    scaler = self.models['kmeans'].get('scaler')
                    if scaler is not None:
                        scaled_features = scaler.transform(recent_features)
                        kmeans_cluster = self.models['kmeans']['model'].predict(scaled_features)[0]

                        # Map cluster to regime
                        kmeans_regime = self._map_cluster_to_regime('kmeans', kmeans_cluster, features)
                        regimes[kmeans_regime.value] += 0.8

                # Detect regime using GMM
                if 'gmm' in self.models and self.models['gmm'].get('trained', False):
                    scaler = self.models['gmm'].get('scaler')
                    if scaler is not None:
                        scaled_features = scaler.transform(recent_features)
                        gmm_cluster = self.models['gmm']['model'].predict(scaled_features)[0]

                        # Map cluster to regime
                        gmm_regime = self._map_cluster_to_regime('gmm', gmm_cluster, features)
                        regimes[gmm_regime.value] += 0.8

            # Use Markov switching if available
            if HAS_STATSMODELS and 'markov_switching' in self.detection_methods:
                if 'markov_switching' in self.models and self.models['markov_switching'].get('trained', False):
                    model = self.models['markov_switching']['model']

                    # Get smoothed probabilities
                    smoothed_probs = model.smoothed_marginal_probabilities
                    if smoothed_probs is not None and len(smoothed_probs) > 0:
                        # Get the most likely regime for the last observation
                        last_probs = smoothed_probs[-1]
                        markov_regime_idx = np.argmax(last_probs)

                        # Map regime index to MarketRegime
                        markov_regime = self._map_markov_to_regime(markov_regime_idx, model)
                        regimes[markov_regime.value] += 0.9

            # Determine the most likely regime
            most_likely_regime = max(regimes.items(), key=lambda x: x[1])
            regime_name = most_likely_regime[0]
            confidence = most_likely_regime[1] / sum(regimes.values()) if sum(regimes.values()) > 0 else 0.0

            elapsed = time.time() - start_time
            logger.info(f"Detected market regime: {regime_name} with confidence {confidence:.2f} in {elapsed:.2f} seconds")

            return {
                'regime': regime_name,
                'confidence': confidence,
                'all_regimes': regimes,
                'processing_time': elapsed,
                'method': 'ensemble'
            }

        except Exception as e:
            logger.error(f"Error detecting market regime: {str(e)}")
            return {
                'regime': MarketRegime.UNKNOWN.value,
                'confidence': 0.0,
                'method': 'error',
                'error': str(e)
            }

    def _map_cluster_to_regime(self, model_name: str, cluster_idx: int, features: pd.DataFrame) -> MarketRegime:
        """
        Map cluster index to market regime.

        Args:
            model_name: Name of the clustering model
            cluster_idx: Cluster index
            features: Feature DataFrame used for clustering

        Returns:
            Corresponding market regime
        """
        try:
            # Get cluster centers
            if model_name == 'kmeans':
                centers = self.models[model_name]['model'].cluster_centers_
            elif model_name == 'gmm':
                centers = self.models[model_name]['model'].means_
            else:
                return MarketRegime.UNKNOWN

            # Get center for this cluster
            center = centers[cluster_idx]

            # Determine regime based on center characteristics
            # Assuming features are in the order: returns, volatility, sma20_dist, sma_ratio, range_ratio, volume_change

            # Extract center values
            returns = center[0]
            volatility = center[1]
            sma20_dist = center[2]
            sma_ratio = center[3]
            range_ratio = center[4]

            # Determine regime based on center values
            if volatility > self.volatility_threshold:
                if abs(returns) > 0.01:  # Strong directional movement
                    return MarketRegime.TRENDING_UP if returns > 0 else MarketRegime.TRENDING_DOWN
                else:
                    return MarketRegime.VOLATILE
            else:
                if range_ratio < self.range_threshold:
                    return MarketRegime.LOW_VOLATILITY
                elif sma_ratio > 1.01:  # Uptrend
                    return MarketRegime.TRENDING_UP
                elif sma_ratio < 0.99:  # Downtrend
                    return MarketRegime.TRENDING_DOWN
                else:
                    return MarketRegime.RANGING

        except Exception as e:
            logger.error(f"Error mapping cluster to regime: {str(e)}")
            return MarketRegime.UNKNOWN

    def _map_markov_to_regime(self, regime_idx: int, model: Any) -> MarketRegime:
        """
        Map Markov regime index to market regime.

        Args:
            regime_idx: Regime index from Markov model
            model: Fitted Markov model

        Returns:
            Corresponding market regime
        """
        try:
            # Get regime parameters
            params = model.params

            # Extract regime-specific parameters
            if hasattr(params, 'sigma2'):
                # Get volatilities for each regime
                volatilities = params.sigma2

                # Get mean returns for each regime
                if hasattr(params, 'const'):
                    means = params.const

                    # Determine regime based on mean and volatility
                    mean = means[regime_idx]
                    vol = volatilities[regime_idx]

                    if vol > np.mean(volatilities) * 1.5:  # High volatility
                        return MarketRegime.VOLATILE if abs(mean) < 0.001 else (
                            MarketRegime.TRENDING_UP if mean > 0 else MarketRegime.TRENDING_DOWN
                        )
                    else:  # Low volatility
                        if abs(mean) < 0.0005:  # Very small mean
                            return MarketRegime.RANGING
                        else:
                            return MarketRegime.TRENDING_UP if mean > 0 else MarketRegime.TRENDING_DOWN

            # Default mapping based on index
            if regime_idx == 0:
                return MarketRegime.TRENDING_UP
            elif regime_idx == 1:
                return MarketRegime.TRENDING_DOWN
            elif regime_idx == 2:
                return MarketRegime.RANGING
            elif regime_idx == 3:
                return MarketRegime.VOLATILE
            else:
                return MarketRegime.UNKNOWN

        except Exception as e:
            logger.error(f"Error mapping Markov regime to market regime: {str(e)}")
            return MarketRegime.UNKNOWN

    def extract_regime_features(self, price_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Extract regime-specific features for liquidity analysis.

        Args:
            price_data: DataFrame with OHLCV price data

        Returns:
            Dictionary with regime-specific features
        """
        # Detect current regime
        regime_result = self.detect_regime(price_data)
        regime = regime_result['regime']

        # Extract base features
        features = self.extract_features(price_data)

        if len(features) < self.min_samples:
            return {
                'regime': regime,
                'features': {},
                'confidence': regime_result.get('confidence', 0.0)
            }

        try:
            # Calculate regime-specific features
            regime_features = {}

            # Common features
            recent_volatility = features['volatility'].iloc[-20:].mean()
            recent_range = features['range_ratio'].iloc[-20:].mean()

            # Regime-specific features
            if regime == MarketRegime.TRENDING_UP.value:
                # Trend strength and consistency
                regime_features['trend_strength'] = features['sma_ratio'].iloc[-20:].mean()
                regime_features['trend_consistency'] = (features['returns'].iloc[-20:] > 0).mean()
                regime_features['pullback_potential'] = 1.0 - (features['sma20_dist'].iloc[-5:].mean() /
                                                             features['sma20_dist'].iloc[-20:].max())

            elif regime == MarketRegime.TRENDING_DOWN.value:
                # Trend strength and consistency
                regime_features['trend_strength'] = 2.0 - features['sma_ratio'].iloc[-20:].mean()
                regime_features['trend_consistency'] = (features['returns'].iloc[-20:] < 0).mean()
                regime_features['bounce_potential'] = 1.0 - (abs(features['sma20_dist'].iloc[-5:].mean()) /
                                                           abs(features['sma20_dist'].iloc[-20:].min()))

            elif regime == MarketRegime.RANGING.value:
                # Range characteristics
                regime_features['range_width'] = features['range_ratio'].iloc[-20:].mean()
                regime_features['range_position'] = (price_data['close'].iloc[-1] - price_data['low'].iloc[-20:].min()) / \
                                                  (price_data['high'].iloc[-20:].max() - price_data['low'].iloc[-20:].min())
                regime_features['breakout_potential'] = features['volatility'].iloc[-5:].mean() / \
                                                      features['volatility'].iloc[-20:].mean()

            elif regime == MarketRegime.VOLATILE.value:
                # Volatility characteristics
                regime_features['volatility_intensity'] = features['volatility'].iloc[-10:].mean() / \
                                                        features['volatility'].iloc[-50:].mean()
                regime_features['directional_bias'] = features['returns'].iloc[-10:].mean() / \
                                                    features['volatility'].iloc[-10:].mean()
                regime_features['volatility_trend'] = features['volatility'].iloc[-5:].mean() / \
                                                    features['volatility'].iloc[-20:].mean()

            # Add common features
            regime_features['recent_volatility'] = recent_volatility
            regime_features['recent_range'] = recent_range
            regime_features['volume_trend'] = features['volume_change'].iloc[-10:].mean()

            return {
                'regime': regime,
                'features': regime_features,
                'confidence': regime_result.get('confidence', 0.0)
            }

        except Exception as e:
            logger.error(f"Error extracting regime-specific features: {str(e)}")
            return {
                'regime': regime,
                'features': {},
                'confidence': regime_result.get('confidence', 0.0),
                'error': str(e)
            }
