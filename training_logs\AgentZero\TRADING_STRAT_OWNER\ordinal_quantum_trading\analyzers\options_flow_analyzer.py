"""
Options Flow Analyzer Module

This module provides the OptionsFlowAnalyzer class that integrates with the 
existing options flow analyzer to detect institutional activity through
options flow patterns.

Author: Trading Strategy Team
Version: 1.0
Date: May 15, 2025
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime
import sys
import os

# Configure logging
logger = logging.getLogger("enhanced_money_flow.options_flow_analyzer")

# Add parent directory to path to allow imports from main project
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Complete options flow analysis implementation
class BaseOptionsFlowAnalyzer:
    """
    Complete options flow analyzer for institutional money flow detection.
    """

    def __init__(self, config=None):
        self.config = config or {}
        self.min_volume_threshold = self.config.get('min_volume_threshold', 100)
        self.min_oi_threshold = self.config.get('min_oi_threshold', 50)
        self.unusual_volume_multiplier = self.config.get('unusual_volume_multiplier', 2.0)

    def analyze(self, options_data, underlying_data=None):
        """Analyze options flow for institutional patterns."""
        try:
            if options_data.empty:
                return {
                    "flow_patterns": [],
                    "liquidity_impacts": [],
                    "hedging_signals": [],
                    "volatility_signals": []
                }

            # Analyze different flow patterns
            flow_patterns = self._detect_flow_patterns(options_data)
            liquidity_impacts = self._analyze_liquidity_impacts(options_data)
            hedging_signals = self._detect_hedging_signals(options_data)
            volatility_signals = self._analyze_volatility_signals(options_data)

            return {
                "flow_patterns": flow_patterns,
                "liquidity_impacts": liquidity_impacts,
                "hedging_signals": hedging_signals,
                "volatility_signals": volatility_signals
            }

        except Exception as e:
            logger.error(f"Error in options flow analysis: {e}")
            return {
                "flow_patterns": [],
                "liquidity_impacts": [],
                "hedging_signals": [],
                "volatility_signals": [],
                "error": str(e)
            }

    def _detect_flow_patterns(self, options_data):
        """Detect institutional flow patterns."""
        patterns = []

        try:
            # Group by strike and expiration
            for strike in options_data['strike_price'].unique():
                strike_data = options_data[options_data['strike_price'] == strike]

                calls = strike_data[strike_data['option_type'] == 'call']
                puts = strike_data[strike_data['option_type'] == 'put']

                if not calls.empty and not puts.empty:
                    call_volume = calls['volume'].sum()
                    put_volume = puts['volume'].sum()

                    # Detect unusual activity
                    if call_volume > self.min_volume_threshold * self.unusual_volume_multiplier:
                        patterns.append({
                            'type': 'unusual_call_volume',
                            'strike': strike,
                            'volume': call_volume,
                            'confidence': min(0.9, call_volume / (self.min_volume_threshold * 10))
                        })

                    if put_volume > self.min_volume_threshold * self.unusual_volume_multiplier:
                        patterns.append({
                            'type': 'unusual_put_volume',
                            'strike': strike,
                            'volume': put_volume,
                            'confidence': min(0.9, put_volume / (self.min_volume_threshold * 10))
                        })

                    # Detect potential straddles/strangles
                    if (call_volume > self.min_volume_threshold and
                        put_volume > self.min_volume_threshold and
                        abs(call_volume - put_volume) / max(call_volume, put_volume) < 0.3):

                        patterns.append({
                            'type': 'straddle_strangle',
                            'strike': strike,
                            'call_volume': call_volume,
                            'put_volume': put_volume,
                            'confidence': 0.7
                        })

        except Exception as e:
            logger.error(f"Error detecting flow patterns: {e}")

        return patterns

    def _analyze_liquidity_impacts(self, options_data):
        """Analyze liquidity impacts from options flow."""
        impacts = []

        try:
            # Analyze by strike price
            strike_impacts = options_data.groupby('strike_price').agg({
                'volume': 'sum',
                'open_interest': 'sum'
            }).reset_index()

            for _, row in strike_impacts.iterrows():
                if row['volume'] > self.min_volume_threshold:
                    impact_strength = min(1.0, row['volume'] / (self.min_volume_threshold * 5))

                    impacts.append({
                        'strike': row['strike_price'],
                        'volume': row['volume'],
                        'open_interest': row['open_interest'],
                        'impact_strength': impact_strength,
                        'type': 'volume_concentration'
                    })

        except Exception as e:
            logger.error(f"Error analyzing liquidity impacts: {e}")

        return impacts

    def _detect_hedging_signals(self, options_data):
        """Detect institutional hedging signals."""
        signals = []

        try:
            # Look for protective put buying
            puts = options_data[options_data['option_type'] == 'put']
            if not puts.empty:
                put_volume = puts['volume'].sum()
                avg_put_volume = puts['volume'].mean()

                if put_volume > self.min_volume_threshold * 2:
                    signals.append({
                        'type': 'protective_put_buying',
                        'volume': put_volume,
                        'avg_volume': avg_put_volume,
                        'confidence': min(0.8, put_volume / (self.min_volume_threshold * 5))
                    })

            # Look for covered call writing
            calls = options_data[options_data['option_type'] == 'call']
            if not calls.empty:
                call_oi = calls['open_interest'].sum()
                call_volume = calls['volume'].sum()

                if call_oi > self.min_oi_threshold * 3 and call_volume > self.min_volume_threshold:
                    signals.append({
                        'type': 'covered_call_writing',
                        'open_interest': call_oi,
                        'volume': call_volume,
                        'confidence': 0.6
                    })

        except Exception as e:
            logger.error(f"Error detecting hedging signals: {e}")

        return signals

    def _analyze_volatility_signals(self, options_data):
        """Analyze volatility-related signals."""
        signals = []

        try:
            # Analyze implied volatility if available
            if 'implied_volatility' in options_data.columns:
                avg_iv = options_data['implied_volatility'].mean()
                iv_std = options_data['implied_volatility'].std()

                # Look for IV skew
                calls = options_data[options_data['option_type'] == 'call']
                puts = options_data[options_data['option_type'] == 'put']

                if not calls.empty and not puts.empty:
                    call_iv = calls['implied_volatility'].mean()
                    put_iv = puts['implied_volatility'].mean()

                    iv_skew = put_iv - call_iv

                    if abs(iv_skew) > 0.05:  # 5% skew threshold
                        signals.append({
                            'type': 'iv_skew',
                            'skew_value': iv_skew,
                            'call_iv': call_iv,
                            'put_iv': put_iv,
                            'direction': 'put_skew' if iv_skew > 0 else 'call_skew',
                            'confidence': min(0.9, abs(iv_skew) / 0.2)
                        })

            # Volume-based volatility signals
            total_volume = options_data['volume'].sum()
            if total_volume > self.min_volume_threshold * 5:
                signals.append({
                    'type': 'high_volume_activity',
                    'total_volume': total_volume,
                    'confidence': min(0.8, total_volume / (self.min_volume_threshold * 20))
                })

        except Exception as e:
            logger.error(f"Error analyzing volatility signals: {e}")

        return signals

    def analyze_options_flow(self, options_data: pd.DataFrame, underlying_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Convenience method for direct options flow analysis.

        Args:
            options_data: Options data DataFrame
            underlying_data: Underlying price data DataFrame

        Returns:
            Options flow analysis results
        """
        return self.analyze(options_data, underlying_data)


class OptionsFlowAnalyzer:
    """
    Analyzer for options flow that integrates with the existing options flow analyzer.
    Detects institutional activity through options flow patterns.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the options flow analyzer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Initialize base options flow analyzer
        self.base_analyzer = BaseOptionsFlowAnalyzer(self.config)
        
        # Cache for analysis results
        self.cached_results = {}
        
        logger.info("Initialized options flow analyzer")
    
    def analyze(self, options_data: pd.DataFrame,
               price_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Analyze options flow to detect institutional activity.
        
        Args:
            options_data: DataFrame with options data
            price_data: Optional DataFrame with price data
            
        Returns:
            Dictionary with options flow analysis results
        """
        # Generate cache key
        if options_data is not None and not options_data.empty:
            # Use hash of options data as cache key
            cache_key = hash(pd.util.hash_pandas_object(options_data).sum())
            
            # Check cache
            if cache_key in self.cached_results:
                logger.info("Using cached options flow analysis")
                return self.cached_results[cache_key]
        
        logger.info("Analyzing options flow")
        
        try:
            # Run base analysis
            base_results = self.base_analyzer.analyze(options_data, price_data)
            
            # Add additional analysis
            enhanced_results = self._enhance_flow_analysis(base_results, options_data, price_data)
            
            # Cache results
            if options_data is not None and not options_data.empty:
                self.cached_results[cache_key] = enhanced_results
            
            logger.info("Options flow analysis completed")
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Error in options flow analysis: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            
            # Return empty results on error
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "flow_patterns": [],
                "liquidity_impacts": [],
                "hedging_signals": [],
                "volatility_signals": []
            }
    
    def _enhance_flow_analysis(self, base_results: Dict[str, Any],
                              options_data: pd.DataFrame,
                              price_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Enhance options flow analysis with additional insights.
        
        Args:
            base_results: Results from base options flow analyzer
            options_data: DataFrame with options data
            price_data: Optional DataFrame with price data
            
        Returns:
            Enhanced options flow analysis results
        """
        # Start with base results
        enhanced_results = base_results.copy()
        
        # Add timestamp
        enhanced_results["timestamp"] = datetime.now().isoformat()
        
        # Add additional analysis if options data available
        if options_data is not None and not options_data.empty:
            # Calculate put/call ratio
            enhanced_results["put_call_ratio"] = self._calculate_put_call_ratio(options_data)
            
            # Analyze skew
            enhanced_results["skew_analysis"] = self._analyze_skew(options_data, price_data)
            
            # Analyze institutional activity
            enhanced_results["institutional_activity"] = self._analyze_institutional_activity(
                base_results.get("flow_patterns", []))
            
            # Analyze dark pool activity if data available
            if "dark_pool" in options_data.columns or "dark_pool_volume" in options_data.columns:
                enhanced_results["dark_pool_analysis"] = self._analyze_dark_pool(options_data)
        
        return enhanced_results
    
    def _calculate_put_call_ratio(self, options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Calculate put/call ratio analysis.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            Dictionary with put/call ratio analysis
        """
        # Initialize results
        pcr_results = {
            "volume_pcr": 0.0,
            "oi_pcr": 0.0,
            "weighted_pcr": 0.0,
            "signal": "neutral"
        }
        
        try:
            # Extract call/put data
            calls = options_data[options_data['option_type'].str.lower() == 'call']
            puts = options_data[options_data['option_type'].str.lower() == 'put']
            
            # Calculate volume PCR
            if 'volume' in options_data.columns:
                total_call_volume = calls['volume'].sum() if not calls.empty else 0
                total_put_volume = puts['volume'].sum() if not puts.empty else 0
                
                if total_call_volume > 0:
                    pcr_results["volume_pcr"] = total_put_volume / total_call_volume
                
            # Calculate OI PCR
            if 'open_interest' in options_data.columns:
                total_call_oi = calls['open_interest'].sum() if not calls.empty else 0
                total_put_oi = puts['open_interest'].sum() if not puts.empty else 0
                
                if total_call_oi > 0:
                    pcr_results["oi_pcr"] = total_put_oi / total_call_oi
            
            # Calculate weighted PCR (gives more weight to recent volume)
            pcr_results["weighted_pcr"] = (pcr_results["volume_pcr"] * 0.7 + 
                                         pcr_results["oi_pcr"] * 0.3)
            
            # Determine signal
            if pcr_results["weighted_pcr"] > 1.2:
                pcr_results["signal"] = "bearish"
            elif pcr_results["weighted_pcr"] < 0.8:
                pcr_results["signal"] = "bullish"
            else:
                pcr_results["signal"] = "neutral"
                
            # Add interpretation
            pcr_results["interpretation"] = self._interpret_pcr(pcr_results["weighted_pcr"])
            
        except Exception as e:
            logger.error(f"Error calculating put/call ratio: {str(e)}")
            pcr_results["error"] = str(e)
        
        return pcr_results
    
    def _interpret_pcr(self, pcr_value: float) -> str:
        """
        Interpret the put/call ratio value.
        
        Args:
            pcr_value: Put/call ratio value
            
        Returns:
            Interpretation string
        """
        if pcr_value > 1.5:
            return "Extremely bearish sentiment; potential contrarian bullish signal"
        elif pcr_value > 1.2:
            return "Bearish sentiment; investors buying more puts than calls"
        elif pcr_value > 0.95:
            return "Slightly bearish sentiment; balanced with bearish tilt"
        elif pcr_value > 0.8:
            return "Neutral sentiment; balanced put/call activity"
        elif pcr_value > 0.5:
            return "Slightly bullish sentiment; balanced with bullish tilt"
        elif pcr_value > 0.3:
            return "Bullish sentiment; investors buying more calls than puts"
        else:
            return "Extremely bullish sentiment; potential contrarian bearish signal"
    
    def _analyze_skew(self, options_data: pd.DataFrame,
                     price_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Analyze options skew.
        
        Args:
            options_data: DataFrame with options data
            price_data: Optional DataFrame with price data
            
        Returns:
            Dictionary with skew analysis
        """
        skew_results = {
            "skew_value": 0.0,
            "skew_signal": "neutral",
            "term_structure": "normal"
        }
        
        try:
            # Get current price
            current_price = 0.0
            if price_data is not None and not price_data.empty:
                current_price = float(price_data['close'].iloc[-1])
            
            # Check if we have IV data
            if 'implied_volatility' not in options_data.columns:
                return skew_results
            
            # Extract call/put data
            calls = options_data[options_data['option_type'].str.lower() == 'call']
            puts = options_data[options_data['option_type'].str.lower() == 'put']
            
            if calls.empty or puts.empty or current_price <= 0:
                return skew_results
            
            # Calculate skew (25-delta put IV / 25-delta call IV)
            # For simplicity, estimate 25-delta options as those [PARTIAL]10% OTM
            otm_call_strike = current_price * 1.1
            otm_put_strike = current_price * 0.9
            
            # Find nearest strikes
            calls_near_strike = calls.iloc[(calls['strike_price'] - otm_call_strike).abs().argsort()[:5]]
            puts_near_strike = puts.iloc[(puts['strike_price'] - otm_put_strike).abs().argsort()[:5]]
            
            if not calls_near_strike.empty and not puts_near_strike.empty:
                avg_call_iv = calls_near_strike['implied_volatility'].mean()
                avg_put_iv = puts_near_strike['implied_volatility'].mean()
                
                if avg_call_iv > 0:
                    skew_value = avg_put_iv / avg_call_iv
                    skew_results["skew_value"] = skew_value
                    
                    # Determine skew signal
                    if skew_value > 1.15:
                        skew_results["skew_signal"] = "bearish"
                    elif skew_value < 0.85:
                        skew_results["skew_signal"] = "bullish"
                    else:
                        skew_results["skew_signal"] = "neutral"
            
            # Analyze term structure if expiration data available
            if 'expiration' in options_data.columns or 'days_to_expiry' in options_data.columns:
                skew_results["term_structure"] = self._analyze_term_structure(options_data)
            
            # Add interpretation
            skew_results["interpretation"] = self._interpret_skew(
                skew_results["skew_value"], skew_results["skew_signal"], 
                skew_results["term_structure"])
            
        except Exception as e:
            logger.error(f"Error analyzing skew: {str(e)}")
            skew_results["error"] = str(e)
        
        return skew_results
    
    def _analyze_term_structure(self, options_data: pd.DataFrame) -> str:
        """
        Analyze volatility term structure.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            Term structure type ('normal', 'inverted', 'flat')
        """
        try:
            # Get expiration dates
            if 'expiration' in options_data.columns:
                options_data['expiry_date'] = pd.to_datetime(options_data['expiration'])
                options_data['days_to_expiry'] = (options_data['expiry_date'] - pd.Timestamp.now()).dt.days + 1
            
            # Group by expiry and calculate average IV
            expiry_iv = options_data.groupby('days_to_expiry')['implied_volatility'].mean().reset_index()
            expiry_iv = expiry_iv.sort_values('days_to_expiry')
            
            if len(expiry_iv) < 2:
                return "unknown"
            
            # Check if IV increases or decreases with time
            short_term_iv = expiry_iv.iloc[0]['implied_volatility']
            long_term_iv = expiry_iv.iloc[-1]['implied_volatility']
            
            if long_term_iv > short_term_iv * 1.1:
                return "normal"
            elif short_term_iv > long_term_iv * 1.1:
                return "inverted"
            else:
                return "flat"
                
        except Exception as e:
            logger.error(f"Error analyzing term structure: {str(e)}")
            return "unknown"
    
    def _interpret_skew(self, skew_value: float, skew_signal: str, term_structure: str) -> str:
        """
        Interpret the skew analysis.
        
        Args:
            skew_value: Skew value
            skew_signal: Skew signal
            term_structure: Term structure type
            
        Returns:
            Interpretation string
        """
        interpretation = ""
        
        # Interpret skew
        if skew_signal == "bearish":
            interpretation += "Puts are relatively expensive compared to calls, indicating market fears of downside risk. "
        elif skew_signal == "bullish":
            interpretation += "Calls are relatively expensive compared to puts, unusual pattern suggesting potential upside expectations. "
        else:
            interpretation += "Balanced pricing between calls and puts, indicating neutral market sentiment. "
        
        # Interpret term structure
        if term_structure == "normal":
            interpretation += "Normal term structure with higher IV for longer-dated options, indicating typical market conditions."
        elif term_structure == "inverted":
            interpretation += "Inverted term structure with higher IV for short-dated options, often indicating near-term uncertainty or expected catalysts."
        elif term_structure == "flat":
            interpretation += "Flat term structure with similar IV across expirations, suggesting consistent expected volatility over time."
        
        return interpretation
    
    def _analyze_institutional_activity(self, flow_patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze institutional activity from flow patterns.
        
        Args:
            flow_patterns: List of flow pattern dictionaries
            
        Returns:
            Dictionary with institutional activity analysis
        """
        activity_results = {
            "bullish_activity": 0,
            "bearish_activity": 0,
            "unusual_activity": False,
            "block_trades": 0,
            "sweeps": 0,
            "net_direction": "neutral",
            "confidence": 0.0
        }
        
        if not flow_patterns:
            return activity_results
        
        # Count patterns by type and direction
        for pattern in flow_patterns:
            pattern_type = pattern.get("pattern_type", "")
            direction = pattern.get("direction", "")
            
            if direction == "bullish":
                activity_results["bullish_activity"] += 1
            elif direction == "bearish":
                activity_results["bearish_activity"] += 1
                
            if pattern_type == "unusual_activity":
                activity_results["unusual_activity"] = True
            elif pattern_type == "block_trade":
                activity_results["block_trades"] += 1
            elif pattern_type == "sweep":
                activity_results["sweeps"] += 1
        
        # Determine net direction
        total_activity = activity_results["bullish_activity"] + activity_results["bearish_activity"]
        if total_activity > 0:
            bull_ratio = activity_results["bullish_activity"] / total_activity
            
            if bull_ratio > 0.6:
                activity_results["net_direction"] = "bullish"
                activity_results["confidence"] = min(1.0, bull_ratio * 1.5)
            elif bull_ratio < 0.4:
                activity_results["net_direction"] = "bearish"
                activity_results["confidence"] = min(1.0, (1 - bull_ratio) * 1.5)
            else:
                activity_results["net_direction"] = "neutral"
                activity_results["confidence"] = 0.5
        
        # Add interpretation
        activity_results["interpretation"] = self._interpret_institutional_activity(activity_results)
        
        return activity_results
    
    def _interpret_institutional_activity(self, activity_results: Dict[str, Any]) -> str:
        """
        Interpret institutional activity analysis.
        
        Args:
            activity_results: Institutional activity analysis results
            
        Returns:
            Interpretation string
        """
        direction = activity_results["net_direction"]
        confidence = activity_results["confidence"]
        block_trades = activity_results["block_trades"]
        sweeps = activity_results["sweeps"]
        unusual = activity_results["unusual_activity"]
        
        interpretation = ""
        
        # Interpret direction and confidence
        if direction == "bullish":
            if confidence > 0.8:
                interpretation += "Strong bullish institutional positioning detected. "
            else:
                interpretation += "Moderate bullish institutional positioning detected. "
        elif direction == "bearish":
            if confidence > 0.8:
                interpretation += "Strong bearish institutional positioning detected. "
            else:
                interpretation += "Moderate bearish institutional positioning detected. "
        else:
            interpretation += "Neutral institutional positioning with mixed signals. "
        
        # Interpret specific activity types
        if block_trades > 0:
            interpretation += f"Found {block_trades} block trades, indicating large institutional positions. "
        
        if sweeps > 0:
            interpretation += f"Detected {sweeps} sweep orders, suggesting urgent institutional accumulation. "
        
        if unusual:
            interpretation += "Unusual options activity detected, potentially indicating informed trading."
        
        return interpretation
    
    def _analyze_dark_pool(self, options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze dark pool activity in options data.
        
        Args:
            options_data: DataFrame with options data including dark pool info
            
        Returns:
            Dictionary with dark pool analysis
        """
        dark_pool_results = {
            "dark_pool_volume": 0,
            "dark_pool_ratio": 0.0,
            "significant_levels": [],
            "interpretation": ""
        }
        
        try:
            # Check if we have dark pool data
            dp_col = None
            if 'dark_pool' in options_data.columns:
                dp_col = 'dark_pool'
            elif 'dark_pool_volume' in options_data.columns:
                dp_col = 'dark_pool_volume'
            else:
                return dark_pool_results
            
            # Calculate total dark pool volume
            dark_pool_volume = options_data[dp_col].sum()
            dark_pool_results["dark_pool_volume"] = dark_pool_volume
            
            # Calculate dark pool ratio if we have total volume
            if 'volume' in options_data.columns:
                total_volume = options_data['volume'].sum()
                if total_volume > 0:
                    dark_pool_results["dark_pool_ratio"] = dark_pool_volume / total_volume
            
            # Find significant dark pool levels
            # Group by strike price and sum dark pool volume
            strike_dp = options_data.groupby('strike_price')[dp_col].sum().reset_index()
            
            # Find strikes with high dark pool activity
            if len(strike_dp) > 0:
                mean_dp = strike_dp[dp_col].mean()
                std_dp = strike_dp[dp_col].std()
                threshold = mean_dp + 2 * std_dp  # 2 standard deviations above mean
                
                # Get significant levels
                significant_levels = strike_dp[strike_dp[dp_col] > threshold]
                
                if not significant_levels.empty:
                    dark_pool_results["significant_levels"] = [
                        {
                            "strike": float(row['strike_price']),
                            "dark_pool_volume": int(row[dp_col]),
                            "significance": float((row[dp_col] - mean_dp) / std_dp if std_dp > 0 else 0)
                        }
                        for _, row in significant_levels.iterrows()
                    ]
            
            # Add interpretation
            dark_pool_results["interpretation"] = self._interpret_dark_pool(dark_pool_results)
            
        except Exception as e:
            logger.error(f"Error analyzing dark pool activity: {str(e)}")
            dark_pool_results["error"] = str(e)
        
        return dark_pool_results
    
    def _interpret_dark_pool(self, dark_pool_results: Dict[str, Any]) -> str:
        """
        Interpret dark pool analysis.
        
        Args:
            dark_pool_results: Dark pool analysis results
            
        Returns:
            Interpretation string
        """
        ratio = dark_pool_results.get("dark_pool_ratio", 0.0)
        significant_levels = dark_pool_results.get("significant_levels", [])
        
        interpretation = ""
        
        # Interpret overall dark pool activity
        if ratio > 0.4:
            interpretation += "Very high dark pool activity detected, indicating significant institutional interest. "
        elif ratio > 0.25:
            interpretation += "Elevated dark pool activity, suggesting increased institutional positioning. "
        elif ratio > 0.1:
            interpretation += "Moderate dark pool activity consistent with normal institutional participation. "
        else:
            interpretation += "Limited dark pool activity detected. "
        
        # Interpret significant levels
        if significant_levels:
            interpretation += f"Found {len(significant_levels)} price levels with significant dark pool activity, "
            
            if len(significant_levels) == 1:
                level = significant_levels[0]
                interpretation += f"particularly at strike ${level['strike']:.2f} with {level['significance']:.1f} sigma significance."
            else:
                top_level = max(significant_levels, key=lambda x: x["significance"])
                interpretation += f"with highest activity at strike ${top_level['strike']:.2f}."
        
        return interpretation


    def detect_gamma_squeeze_signals(self, options_data: pd.DataFrame,
                                   price_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Detect potential gamma squeeze conditions.
        
        Args:
            options_data: DataFrame with options data
            price_data: Optional DataFrame with price data
            
        Returns:
            Dictionary with gamma squeeze analysis
        """
        gamma_results = {
            "gamma_exposure": 0.0,
            "max_pain": 0.0,
            "squeeze_probability": 0.0,
            "critical_strikes": [],
            "signal": "neutral",
            "interpretation": ""
        }
        
        try:
            # Get current price
            current_price = 0.0
            if price_data is not None and not price_data.empty:
                current_price = float(price_data['close'].iloc[-1])
            
            # Calculate gamma exposure by strike
            if 'gamma' in options_data.columns and 'open_interest' in options_data.columns:
                # Calculate dealer gamma exposure
                gamma_exposure_by_strike = self._calculate_gamma_exposure(options_data, current_price)
                gamma_results["gamma_exposure"] = gamma_exposure_by_strike.get("net_exposure", 0.0)
                
                # Find critical strikes
                gamma_results["critical_strikes"] = gamma_exposure_by_strike.get("critical_strikes", [])
                
                # Calculate max pain
                gamma_results["max_pain"] = self._calculate_max_pain(options_data)
                
                # Assess squeeze probability
                gamma_results["squeeze_probability"] = self._assess_squeeze_probability(
                    gamma_exposure_by_strike, current_price, gamma_results["max_pain"])
                
                # Determine signal
                if gamma_results["squeeze_probability"] > 0.7:
                    gamma_results["signal"] = "high_squeeze_risk"
                elif gamma_results["squeeze_probability"] > 0.4:
                    gamma_results["signal"] = "moderate_squeeze_risk"
                else:
                    gamma_results["signal"] = "low_squeeze_risk"
            
            # Add interpretation
            gamma_results["interpretation"] = self._interpret_gamma_signals(gamma_results)
            
        except Exception as e:
            logger.error(f"Error detecting gamma squeeze signals: {str(e)}")
            gamma_results["error"] = str(e)
        
        return gamma_results
    
    def _calculate_gamma_exposure(self, options_data: pd.DataFrame,
                                current_price: float) -> Dict[str, Any]:
        """
        Calculate gamma exposure by strike.
        
        Args:
            options_data: DataFrame with options data
            current_price: Current underlying price
            
        Returns:
            Dictionary with gamma exposure analysis
        """
        exposure_results = {
            "net_exposure": 0.0,
            "call_exposure": 0.0,
            "put_exposure": 0.0,
            "critical_strikes": [],
            "exposure_by_strike": {}
        }
        
        try:
            # Group by strike and option type
            strike_groups = options_data.groupby(['strike_price', 'option_type'])
            
            total_call_exposure = 0.0
            total_put_exposure = 0.0
            exposure_by_strike = {}
            
            for (strike, option_type), group in strike_groups:
                # Calculate total gamma * open interest for this strike/type
                gamma_oi = (group['gamma'] * group['open_interest']).sum()
                
                # Convert to dealer exposure (opposite of customer position)
                # Dealers are short options, so gamma exposure is negative
                dealer_exposure = -gamma_oi * 100  # Convert to shares
                
                if option_type.lower() == 'call':
                    total_call_exposure += dealer_exposure
                else:
                    total_put_exposure += dealer_exposure
                
                # Store by strike
                if strike not in exposure_by_strike:
                    exposure_by_strike[strike] = {'call': 0.0, 'put': 0.0}
                
                exposure_by_strike[strike][option_type.lower()] = dealer_exposure
            
            exposure_results["call_exposure"] = total_call_exposure
            exposure_results["put_exposure"] = total_put_exposure
            exposure_results["net_exposure"] = total_call_exposure + total_put_exposure
            exposure_results["exposure_by_strike"] = exposure_by_strike
            
            # Find critical strikes (high exposure levels)
            critical_strikes = []
            for strike, exposures in exposure_by_strike.items():
                total_strike_exposure = abs(exposures['call']) + abs(exposures['put'])
                
                if total_strike_exposure > abs(exposure_results["net_exposure"]) * 0.1:
                    critical_strikes.append({
                        "strike": float(strike),
                        "call_exposure": exposures['call'],
                        "put_exposure": exposures['put'],
                        "total_exposure": total_strike_exposure,
                        "distance_from_spot": abs(strike - current_price) / current_price if current_price > 0 else 0
                    })
            
            # Sort by total exposure
            critical_strikes.sort(key=lambda x: x["total_exposure"], reverse=True)
            exposure_results["critical_strikes"] = critical_strikes[:10]  # Top 10
            
        except Exception as e:
            logger.error(f"Error calculating gamma exposure: {str(e)}")
            exposure_results["error"] = str(e)
        
        return exposure_results
    
    def _calculate_max_pain(self, options_data: pd.DataFrame) -> float:
        """
        Calculate max pain level (strike with maximum option value loss).
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            Max pain strike price
        """
        try:
            # Get unique strikes
            strikes = options_data['strike_price'].unique()
            
            max_pain_strike = 0.0
            min_total_value = float('inf')
            
            for strike in strikes:
                total_value = 0.0
                
                # Calculate total option value at this strike
                for _, option in options_data.iterrows():
                    option_strike = option['strike_price']
                    open_interest = option['open_interest']
                    option_type = option['option_type'].lower()
                    
                    # Calculate intrinsic value if strike equals this test strike
                    if option_type == 'call' and strike > option_strike:
                        intrinsic_value = strike - option_strike
                    elif option_type == 'put' and strike < option_strike:
                        intrinsic_value = option_strike - strike
                    else:
                        intrinsic_value = 0.0
                    
                    total_value += intrinsic_value * open_interest
                
                # Track minimum total value (max pain)
                if total_value < min_total_value:
                    min_total_value = total_value
                    max_pain_strike = strike
            
            return float(max_pain_strike)
            
        except Exception as e:
            logger.error(f"Error calculating max pain: {str(e)}")
            return 0.0
    
    def _assess_squeeze_probability(self, gamma_exposure: Dict[str, Any],
                                  current_price: float, max_pain: float) -> float:
        """
        Assess probability of gamma squeeze.
        
        Args:
            gamma_exposure: Gamma exposure analysis
            current_price: Current underlying price
            max_pain: Max pain level
            
        Returns:
            Squeeze probability (0.0 to 1.0)
        """
        try:
            probability = 0.0
            
            # Factor 1: High net gamma exposure (negative for dealers)
            net_exposure = gamma_exposure.get("net_exposure", 0.0)
            if net_exposure < 0:  # Dealers short gamma
                exposure_factor = min(1.0, abs(net_exposure) / 1000000)  # Scale factor
                probability += exposure_factor * 0.4
            
            # Factor 2: Distance from max pain
            if current_price > 0 and max_pain > 0:
                distance_factor = abs(current_price - max_pain) / current_price
                if distance_factor > 0.05:  # More than 5% from max pain
                    probability += min(0.3, distance_factor * 3)
            
            # Factor 3: Concentration of critical strikes
            critical_strikes = gamma_exposure.get("critical_strikes", [])
            if critical_strikes:
                # Check if there are strikes close to current price with high exposure
                close_strikes = [s for s in critical_strikes if s["distance_from_spot"] < 0.05]
                if close_strikes:
                    concentration_factor = len(close_strikes) / max(len(critical_strikes), 1)
                    probability += concentration_factor * 0.3
            
            return min(1.0, probability)
            
        except Exception as e:
            logger.error(f"Error assessing squeeze probability: {str(e)}")
            return 0.0
    
    def _interpret_gamma_signals(self, gamma_results: Dict[str, Any]) -> str:
        """
        Interpret gamma squeeze analysis.
        
        Args:
            gamma_results: Gamma squeeze analysis results
            
        Returns:
            Interpretation string
        """
        signal = gamma_results.get("signal", "neutral")
        probability = gamma_results.get("squeeze_probability", 0.0)
        max_pain = gamma_results.get("max_pain", 0.0)
        critical_strikes = gamma_results.get("critical_strikes", [])
        
        interpretation = ""
        
        # Interpret signal strength
        if signal == "high_squeeze_risk":
            interpretation += f"HIGH GAMMA SQUEEZE RISK ({probability:.1%} probability). "
            interpretation += "Dealers are heavily short gamma, creating potential for rapid price acceleration on breakouts. "
        elif signal == "moderate_squeeze_risk":
            interpretation += f"MODERATE GAMMA SQUEEZE RISK ({probability:.1%} probability). "
            interpretation += "Some gamma exposure present that could amplify price movements. "
        else:
            interpretation += f"LOW GAMMA SQUEEZE RISK ({probability:.1%} probability). "
            interpretation += "Limited gamma exposure, normal market dynamics expected. "
        
        # Add max pain context
        if max_pain > 0:
            interpretation += f"Max pain level at ${max_pain:.2f} suggests magnetic price attraction. "
        
        # Add critical strikes information
        if critical_strikes:
            top_strike = critical_strikes[0]
            interpretation += f"Key gamma level at ${top_strike['strike']:.2f} with significant dealer exposure."
        
        return interpretation
    
    def analyze_options_sentiment(self, options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze overall options sentiment and positioning.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            Dictionary with sentiment analysis
        """
        sentiment_results = {
            "overall_sentiment": "neutral",
            "smart_money_flow": "neutral",
            "retail_activity": "moderate",
            "institutional_positioning": "neutral",
            "contrarian_signals": [],
            "confidence": 0.0,
            "interpretation": ""
        }
        
        try:
            # Analyze volume patterns
            volume_analysis = self._analyze_volume_patterns(options_data)
            
            # Analyze size-based flows (institutional vs retail)
            size_analysis = self._analyze_trade_sizes(options_data)
            
            # Look for contrarian signals
            contrarian_signals = self._detect_contrarian_signals(options_data)
            
            # Combine analyses
            sentiment_results.update({
                "overall_sentiment": self._determine_overall_sentiment(volume_analysis, size_analysis),
                "smart_money_flow": size_analysis.get("institutional_direction", "neutral"),
                "retail_activity": size_analysis.get("retail_activity_level", "moderate"),
                "institutional_positioning": size_analysis.get("institutional_sentiment", "neutral"),
                "contrarian_signals": contrarian_signals,
                "confidence": self._calculate_sentiment_confidence(volume_analysis, size_analysis)
            })
            
            # Add interpretation
            sentiment_results["interpretation"] = self._interpret_sentiment(sentiment_results)
            
        except Exception as e:
            logger.error(f"Error analyzing options sentiment: {str(e)}")
            sentiment_results["error"] = str(e)
        
        return sentiment_results
    
    def _analyze_volume_patterns(self, options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze volume patterns in options data.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            Dictionary with volume pattern analysis
        """
        patterns = {
            "call_volume": 0,
            "put_volume": 0,
            "volume_ratio": 0.0,
            "unusual_volume": False,
            "volume_trend": "neutral"
        }
        
        try:
            if 'volume' not in options_data.columns:
                return patterns
            
            # Calculate call/put volumes
            calls = options_data[options_data['option_type'].str.lower() == 'call']
            puts = options_data[options_data['option_type'].str.lower() == 'put']
            
            call_volume = calls['volume'].sum() if not calls.empty else 0
            put_volume = puts['volume'].sum() if not puts.empty else 0
            
            patterns["call_volume"] = call_volume
            patterns["put_volume"] = put_volume
            
            # Calculate ratio
            if put_volume > 0:
                patterns["volume_ratio"] = call_volume / put_volume
            
            # Check for unusual volume
            total_volume = call_volume + put_volume
            if 'avg_volume' in options_data.columns:
                avg_total = options_data['avg_volume'].sum()
                if avg_total > 0 and total_volume > avg_total * 2:
                    patterns["unusual_volume"] = True
            
            # Determine trend
            if patterns["volume_ratio"] > 1.5:
                patterns["volume_trend"] = "bullish"
            elif patterns["volume_ratio"] < 0.67:
                patterns["volume_trend"] = "bearish"
            else:
                patterns["volume_trend"] = "neutral"
            
        except Exception as e:
            logger.error(f"Error analyzing volume patterns: {str(e)}")
            patterns["error"] = str(e)
        
        return patterns
    
    def _analyze_trade_sizes(self, options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Analyze trade sizes to distinguish institutional vs retail activity.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            Dictionary with trade size analysis
        """
        size_analysis = {
            "large_trades": 0,
            "small_trades": 0,
            "institutional_direction": "neutral",
            "retail_activity_level": "moderate",
            "institutional_sentiment": "neutral"
        }
        
        try:
            if 'volume' not in options_data.columns:
                return size_analysis
            
            # Define thresholds
            large_trade_threshold = 100  # contracts
            
            # Analyze trade sizes
            large_trades = options_data[options_data['volume'] >= large_trade_threshold]
            small_trades = options_data[options_data['volume'] < large_trade_threshold]
            
            size_analysis["large_trades"] = len(large_trades)
            size_analysis["small_trades"] = len(small_trades)
            
            # Analyze institutional direction (large trades)
            if not large_trades.empty:
                large_calls = large_trades[large_trades['option_type'].str.lower() == 'call']
                large_puts = large_trades[large_trades['option_type'].str.lower() == 'put']
                
                large_call_volume = large_calls['volume'].sum()
                large_put_volume = large_puts['volume'].sum()
                
                if large_call_volume > large_put_volume * 1.5:
                    size_analysis["institutional_direction"] = "bullish"
                elif large_put_volume > large_call_volume * 1.5:
                    size_analysis["institutional_direction"] = "bearish"
                else:
                    size_analysis["institutional_direction"] = "neutral"
            
            # Assess retail activity level
            total_volume = options_data['volume'].sum()
            small_volume = small_trades['volume'].sum() if not small_trades.empty else 0
            
            if total_volume > 0:
                retail_ratio = small_volume / total_volume
                if retail_ratio > 0.7:
                    size_analysis["retail_activity_level"] = "high"
                elif retail_ratio < 0.3:
                    size_analysis["retail_activity_level"] = "low"
                else:
                    size_analysis["retail_activity_level"] = "moderate"
            
            # Determine institutional sentiment
            size_analysis["institutional_sentiment"] = size_analysis["institutional_direction"]
            
        except Exception as e:
            logger.error(f"Error analyzing trade sizes: {str(e)}")
            size_analysis["error"] = str(e)
        
        return size_analysis
    
    def _detect_contrarian_signals(self, options_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Detect contrarian signals in options data.
        
        Args:
            options_data: DataFrame with options data
            
        Returns:
            List of contrarian signal dictionaries
        """
        signals = []
        
        try:
            # Signal 1: Extreme put/call ratios
            pcr_analysis = self._calculate_put_call_ratio(options_data)
            pcr_value = pcr_analysis.get("weighted_pcr", 0.0)
            
            if pcr_value > 1.5:
                signals.append({
                    "type": "extreme_bearish_sentiment",
                    "value": pcr_value,
                    "signal": "bullish_contrarian",
                    "description": "Extremely high put/call ratio may indicate oversold conditions"
                })
            elif pcr_value < 0.3:
                signals.append({
                    "type": "extreme_bullish_sentiment",
                    "value": pcr_value,
                    "signal": "bearish_contrarian",
                    "description": "Extremely low put/call ratio may indicate overbought conditions"
                })
            
            # Signal 2: Unusual volume in far OTM options
            if 'strike_price' in options_data.columns:
                current_price = options_data['strike_price'].median()  # Rough estimate
                
                # Find far OTM calls and puts
                far_otm_calls = options_data[
                    (options_data['option_type'].str.lower() == 'call') &
                    (options_data['strike_price'] > current_price * 1.2)
                ]
                
                far_otm_puts = options_data[
                    (options_data['option_type'].str.lower() == 'put') &
                    (options_data['strike_price'] < current_price * 0.8)
                ]
                
                if not far_otm_calls.empty and far_otm_calls['volume'].sum() > options_data['volume'].sum() * 0.2:
                    signals.append({
                        "type": "excessive_far_otm_call_buying",
                        "value": far_otm_calls['volume'].sum(),
                        "signal": "speculative_bullish",
                        "description": "High volume in far OTM calls suggests speculative positioning"
                    })
                
                if not far_otm_puts.empty and far_otm_puts['volume'].sum() > options_data['volume'].sum() * 0.2:
                    signals.append({
                        "type": "excessive_far_otm_put_buying",
                        "value": far_otm_puts['volume'].sum(),
                        "signal": "speculative_bearish",
                        "description": "High volume in far OTM puts suggests speculative positioning"
                    })
            
        except Exception as e:
            logger.error(f"Error detecting contrarian signals: {str(e)}")
        
        return signals
    
    def _determine_overall_sentiment(self, volume_analysis: Dict[str, Any],
                                   size_analysis: Dict[str, Any]) -> str:
        """
        Determine overall sentiment from volume and size analyses.
        
        Args:
            volume_analysis: Volume pattern analysis
            size_analysis: Trade size analysis
            
        Returns:
            Overall sentiment string
        """
        volume_trend = volume_analysis.get("volume_trend", "neutral")
        institutional_direction = size_analysis.get("institutional_direction", "neutral")
        
        # Weight institutional direction more heavily
        if institutional_direction == "bullish" and volume_trend in ["bullish", "neutral"]:
            return "bullish"
        elif institutional_direction == "bearish" and volume_trend in ["bearish", "neutral"]:
            return "bearish"
        elif volume_trend == "bullish" and institutional_direction == "neutral":
            return "mildly_bullish"
        elif volume_trend == "bearish" and institutional_direction == "neutral":
            return "mildly_bearish"
        else:
            return "neutral"
    
    def _calculate_sentiment_confidence(self, volume_analysis: Dict[str, Any],
                                      size_analysis: Dict[str, Any]) -> float:
        """
        Calculate confidence in sentiment analysis.
        
        Args:
            volume_analysis: Volume pattern analysis
            size_analysis: Trade size analysis
            
        Returns:
            Confidence level (0.0 to 1.0)
        """
        confidence = 0.5  # Base confidence
        
        # Increase confidence for consistent signals
        volume_trend = volume_analysis.get("volume_trend", "neutral")
        institutional_direction = size_analysis.get("institutional_direction", "neutral")
        
        if volume_trend == institutional_direction and volume_trend != "neutral":
            confidence += 0.3
        
        # Increase confidence for unusual volume
        if volume_analysis.get("unusual_volume", False):
            confidence += 0.2
        
        # Increase confidence for high institutional activity
        large_trades = size_analysis.get("large_trades", 0)
        total_trades = large_trades + size_analysis.get("small_trades", 0)
        
        if total_trades > 0 and large_trades / total_trades > 0.3:
            confidence += 0.2
        
        return min(1.0, confidence)
    
    def _interpret_sentiment(self, sentiment_results: Dict[str, Any]) -> str:
        """
        Interpret sentiment analysis results.
        
        Args:
            sentiment_results: Sentiment analysis results
            
        Returns:
            Interpretation string
        """
        overall_sentiment = sentiment_results.get("overall_sentiment", "neutral")
        smart_money_flow = sentiment_results.get("smart_money_flow", "neutral")
        retail_activity = sentiment_results.get("retail_activity_level", "moderate")
        confidence = sentiment_results.get("confidence", 0.0)
        contrarian_signals = sentiment_results.get("contrarian_signals", [])
        
        interpretation = ""
        
        # Interpret overall sentiment
        if overall_sentiment == "bullish":
            interpretation += f"BULLISH sentiment detected with {confidence:.0%} confidence. "
        elif overall_sentiment == "bearish":
            interpretation += f"BEARISH sentiment detected with {confidence:.0%} confidence. "
        elif overall_sentiment in ["mildly_bullish", "mildly_bearish"]:
            interpretation += f"{overall_sentiment.replace('_', ' ').title()} sentiment with {confidence:.0%} confidence. "
        else:
            interpretation += f"NEUTRAL sentiment with mixed signals ({confidence:.0%} confidence). "
        
        # Add smart money context
        if smart_money_flow != "neutral":
            interpretation += f"Smart money appears to be flowing {smart_money_flow}. "
        
        # Add retail activity context
        if retail_activity == "high":
            interpretation += "High retail participation detected, which may increase volatility. "
        elif retail_activity == "low":
            interpretation += "Low retail participation suggests institutional-driven moves. "
        
        # Add contrarian signals
        if contrarian_signals:
            interpretation += f"WARNING: {len(contrarian_signals)} contrarian signal(s) detected - "
            for signal in contrarian_signals[:2]:  # Show top 2
                interpretation += f"{signal['description']}. "
        
        return interpretation

    def clear_cache(self):
        """Clear the analysis cache."""
        self.cached_results.clear()
        logger.info("Options flow analyzer cache cleared")

    def get_analysis_summary(self, options_data: pd.DataFrame,
                           price_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Get a comprehensive summary of all options analysis.
        
        Args:
            options_data: DataFrame with options data
            price_data: Optional DataFrame with price data
            
        Returns:
            Dictionary with comprehensive analysis summary
        """
        try:
            # Run all analyses
            main_analysis = self.analyze(options_data, price_data)
            gamma_analysis = self.detect_gamma_squeeze_signals(options_data, price_data)
            sentiment_analysis = self.analyze_options_sentiment(options_data)
            
            # Compile summary
            summary = {
                "timestamp": datetime.now().isoformat(),
                "data_quality": {
                    "total_contracts": len(options_data) if options_data is not None else 0,
                    "call_contracts": len(options_data[options_data['option_type'].str.lower() == 'call']) if options_data is not None else 0,
                    "put_contracts": len(options_data[options_data['option_type'].str.lower() == 'put']) if options_data is not None else 0,
                    "has_price_data": price_data is not None and not price_data.empty
                },
                "key_findings": {
                    "overall_sentiment": sentiment_analysis.get("overall_sentiment", "neutral"),
                    "put_call_ratio": main_analysis.get("put_call_ratio", {}).get("weighted_pcr", 0.0),
                    "gamma_squeeze_risk": gamma_analysis.get("signal", "neutral"),
                    "institutional_positioning": sentiment_analysis.get("institutional_positioning", "neutral"),
                    "unusual_activity": main_analysis.get("institutional_activity", {}).get("unusual_activity", False)
                },
                "risk_alerts": [],
                "main_analysis": main_analysis,
                "gamma_analysis": gamma_analysis,
                "sentiment_analysis": sentiment_analysis
            }
            
            # Generate risk alerts
            risk_alerts = []
            
            # Check for high gamma squeeze risk
            if gamma_analysis.get("signal") == "high_squeeze_risk":
                risk_alerts.append({
                    "type": "gamma_squeeze",
                    "severity": "high",
                    "message": "High gamma squeeze risk detected - potential for rapid price acceleration"
                })
            
            # Check for extreme sentiment
            pcr = main_analysis.get("put_call_ratio", {}).get("weighted_pcr", 0.0)
            if pcr > 1.5:
                risk_alerts.append({
                    "type": "extreme_bearish_sentiment",
                    "severity": "medium",
                    "message": f"Extreme bearish sentiment (PCR: {pcr:.2f}) - potential contrarian opportunity"
                })
            elif pcr < 0.3:
                risk_alerts.append({
                    "type": "extreme_bullish_sentiment",
                    "severity": "medium",
                    "message": f"Extreme bullish sentiment (PCR: {pcr:.2f}) - potential contrarian opportunity"
                })
            
            # Check for unusual activity
            if main_analysis.get("institutional_activity", {}).get("unusual_activity", False):
                risk_alerts.append({
                    "type": "unusual_activity",
                    "severity": "medium",
                    "message": "Unusual options activity detected - potential informed trading"
                })
            
            summary["risk_alerts"] = risk_alerts
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating analysis summary: {str(e)}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "data_quality": {"total_contracts": 0},
                "key_findings": {},
                "risk_alerts": [],
                "main_analysis": {},
                "gamma_analysis": {},
                "sentiment_analysis": {}
            }


# Convenience function for quick analysis
def analyze_options_flow(options_data: pd.DataFrame,
                        price_data: Optional[pd.DataFrame] = None,
                        config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Convenience function for quick options flow analysis.
    
    Args:
        options_data: DataFrame with options data
        price_data: Optional DataFrame with price data
        config: Optional configuration dictionary
        
    Returns:
        Dictionary with options flow analysis results
    """
    analyzer = OptionsFlowAnalyzer(config)
    return analyzer.get_analysis_summary(options_data, price_data)


# Add the missing method to the main OptionsFlowAnalyzer class
def _add_missing_method():
    """Add the missing analyze_options_flow method to OptionsFlowAnalyzer class."""
    def analyze_options_flow(self, options_data: pd.DataFrame, underlying_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Convenience method for direct options flow analysis.

        Args:
            options_data: Options data DataFrame
            underlying_data: Underlying price data DataFrame

        Returns:
            Options flow analysis results
        """
        return self.analyze(options_data, underlying_data)

    # Add method to class
    OptionsFlowAnalyzer.analyze_options_flow = analyze_options_flow

# Apply the fix
_add_missing_method()


if __name__ == "__main__":
    # Example usage and testing
    logger.info("Options Flow Analyzer module loaded successfully")
    
    # Create sample data for testing
    sample_options_data = pd.DataFrame({
        'option_type': ['call', 'put', 'call', 'put'] * 25,
        'strike_price': [100, 100, 105, 95] * 25,
        'volume': np.random.randint(10, 1000, 100),
        'open_interest': np.random.randint(100, 5000, 100),
        'implied_volatility': np.random.uniform(0.15, 0.45, 100),
        'gamma': np.random.uniform(0.001, 0.05, 100),
        'expiration': ['2025-06-20'] * 100
    })
    
    sample_price_data = pd.DataFrame({
        'close': [102.5],
        'timestamp': [datetime.now()]
    })
    
    # Test the analyzer
    try:
        result = analyze_options_flow(sample_options_data, sample_price_data)
        logger.info("Test analysis completed successfully")
        print(f"Analysis completed with {len(result.get('risk_alerts', []))} risk alerts")
        
    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

    def analyze_options_flow(self, options_data: pd.DataFrame, underlying_data: pd.DataFrame = None) -> Dict[str, Any]:
        """
        Convenience method for direct options flow analysis.

        Args:
            options_data: Options data DataFrame
            underlying_data: Underlying price data DataFrame

        Returns:
            Options flow analysis results
        """
        return self.analyze(options_data, underlying_data)
