"""
Volume Profile Analyzer - Routing to Enhanced Volume Analyzer

This module routes all VolumeProfileAnalyzer requests to the EnhancedVolumeAnalyzer
which provides superior functionality and is significantly better.

The EnhancedVolumeAnalyzer includes:
- Advanced volume node detection using statistical methods
- Point of Control (POC) and Value Area calculations  
- Volume anomaly detection
- Volume trend analysis
- Multi-timeframe volume analysis
- Complete orchestrator integration
- Error-free implementation
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging

logger = logging.getLogger(__name__)

try:
    from enhanced_volume_analyzer import EnhancedVolumeAnalyzer
    
    # Route VolumeProfileAnalyzer to EnhancedVolumeAnalyzer
    class VolumeProfileAnalyzer(EnhancedVolumeAnalyzer):
        """
        VolumeProfileAnalyzer that routes to EnhancedVolumeAnalyzer.
        
        This ensures backward compatibility while using the superior
        EnhancedVolumeAnalyzer implementation.
        """
        
        def __init__(self, config=None, system_config=None, api_gateway_instance=None):
            """Initialize using EnhancedVolumeAnalyzer."""
            logger.info("VolumeProfileAnalyzer routing to EnhancedVolumeAnalyzer")
            super().__init__(config, system_config, api_gateway_instance)
            
    logger.info("VolumeProfileAnalyzer successfully routed to EnhancedVolumeAnalyzer")
    
except ImportError as e:
    logger.error(f"Failed to import EnhancedVolumeAnalyzer: {e}")
    
    # Fallback minimal implementation
    class VolumeProfileAnalyzer:
        """Fallback VolumeProfileAnalyzer implementation."""
        
        def __init__(self, config=None, system_config=None, api_gateway_instance=None):
            self.config = config or {}
            self.system_config = system_config or {}
            self.api_gateway = api_gateway_instance
            logger.warning("Using fallback VolumeProfileAnalyzer - EnhancedVolumeAnalyzer not available")
            
        def analyze_factors(self, data_package=None, **kwargs):
            """Fallback analyze_factors method."""
            logger.warning("VolumeProfileAnalyzer fallback - returning empty factors")
            return []
