"""
Base Analyzer Class for Factor-Based Trading System

Primary base class that all analyzers should inherit from to ensure
consistent interface with the LiquiditySystemOrchestrator.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
import pandas as pd
import logging

# Import factor specifications
try:
    from factor_specification import FactorData, TimeFrame, DirectionBias, validate_factor
except ImportError:
    try:
        from .factor_specification import FactorData, TimeFrame, DirectionBias, validate_factor
    except ImportError:
        # Fallback definitions
        from dataclasses import dataclass
        from enum import Enum
        
        class TimeFrame(Enum):
            MIN_1 = "1m"
            MIN_5 = "5m"
            MIN_15 = "15m"
            MIN_30 = "30m"
            HOUR_1 = "1h"
            HOUR_4 = "4h"
            DAY_1 = "1d"
            WEEK_1 = "1w"
        
        class DirectionBias(Enum):
            BULLISH = "bullish"
            BEARISH = "bearish"
            NEUTRAL = "neutral"
            
        @dataclass
        class FactorData:
            factor_name: str
            ticker: str
            timestamp: datetime
            direction_bias: DirectionBias
            strength_score: float
            timeframe: TimeFrame = TimeFrame.MIN_15
            analyzer_name: str = ""
            reason_short: str = ""
            reason_long: str = ""
            data_quality_score: float = 1.0
            details: Dict[str, Any] = None
            
            def __post_init__(self):
                if self.details is None:
                    self.details = {}
                    
        def validate_factor(factor):
            """Simple factor validation"""
            if not isinstance(factor, (dict, FactorData)):
                return False, "Factor must be dict or FactorData"
            return True, "Valid"


class BaseAnalyzer(ABC):
    """
    Base class for all factor analyzers.
    
    Provides standardized interface for the LiquiditySystemOrchestrator
    including proper API gateway integration and configuration management.
    """
    
    def __init__(self, 
                 config: Optional[Dict[str, Any]] = None,
                 system_config: Optional[Dict[str, Any]] = None,
                 api_gateway_instance=None):
        """
        Initialize analyzer with enhanced interface.
        
        Args:
            config: Analyzer-specific configuration dictionary
            system_config: Global system configuration from orchestrator
            api_gateway_instance: Shared API gateway instance for data fetching
        """
        self.config = config or self._get_default_config()
        self.system_config = system_config or {}
        self.api_gateway = api_gateway_instance
        
        # Set up logging
        self.logger = logging.getLogger(f"analyzer.{self.__class__.__name__}")
        
        # Initialize analyzer state
        self.is_initialized = False
        self.last_analysis_time = None
        self.analysis_count = 0
        self.error_count = 0
        
        # Performance tracking
        self.performance_stats = {
            'total_runtime': 0.0,
            'average_runtime': 0.0,
            'factor_generation_rate': 0.0,
            'error_rate': 0.0
        }
        
        # Initialize the analyzer
        self._initialize()
        
    def _initialize(self):
        """Initialize the analyzer with enhanced logging and validation."""
        try:
            self._validate_config()
            self.is_initialized = True
            self.logger.info(f"[{self.__class__.__name__}] Initialized with enhanced interface")
        except Exception as e:
            self.logger.error(f"[{self.__class__.__name__}] Initialization failed: {str(e)}")
            self.error_count += 1
            raise
    
    def _validate_config(self):
        """Validate analyzer configuration."""
        if not isinstance(self.config, dict):
            raise ValueError("Config must be a dictionary")
            
        # Validate required configuration keys
        required_keys = self._get_required_config_keys()
        for key in required_keys:
            if key not in self.config:
                self.logger.warning(f"Missing required config key: {key}")
    
    def _get_required_config_keys(self) -> List[str]:
        """Get list of required configuration keys."""
        return []  # Override in subclasses
    
    @abstractmethod
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for this analyzer."""
        pass
    
    @abstractmethod
    def analyze_factors(self, data_package: Dict[str, Any]) -> List[FactorData]:
        """
        Analyze market data and generate factors (STANDARDIZED INTERFACE).
        
        Args:
            data_package: Dictionary containing:
                - ticker: Symbol being analyzed
                - price_data: Dict with OHLCV data for multiple timeframes
                - options_data: Optional options chain data
                - market_data: Additional market context
                
        Returns:
            List of FactorData objects representing trading factors
        """
        pass
    
    def get_supported_timeframes(self) -> List[TimeFrame]:
        """Get list of timeframes this analyzer supports."""
        return [TimeFrame.MIN_15, TimeFrame.HOUR_1, TimeFrame.HOUR_4]
    
    def get_analyzer_info(self) -> Dict[str, Any]:
        """Get analyzer information and statistics."""
        return {
            'name': self.__class__.__name__,
            'version': getattr(self, 'VERSION', '1.0.0'),
            'is_initialized': self.is_initialized,
            'analysis_count': self.analysis_count,
            'error_count': self.error_count,
            'performance_stats': self.performance_stats,
            'supported_timeframes': [tf.value for tf in self.get_supported_timeframes()],
            'config': self.config
        }
    
    def reset_stats(self):
        """Reset performance statistics."""
        self.analysis_count = 0
        self.error_count = 0
        self.performance_stats = {
            'total_runtime': 0.0,
            'average_runtime': 0.0,
            'factor_generation_rate': 0.0,
            'error_rate': 0.0
        }
        self.logger.info(f"[{self.__class__.__name__}] Statistics reset")
    
    def _update_performance_stats(self, runtime: float, factor_count: int):
        """Update performance statistics."""
        self.analysis_count += 1
        self.performance_stats['total_runtime'] += runtime
        self.performance_stats['average_runtime'] = (
            self.performance_stats['total_runtime'] / self.analysis_count
        )
        
        if runtime > 0:
            self.performance_stats['factor_generation_rate'] = factor_count / runtime
            
        self.performance_stats['error_rate'] = (
            self.error_count / self.analysis_count if self.analysis_count > 0 else 0.0
        )
    
    def safe_analyze(self, data_package: Dict[str, Any]) -> List[FactorData]:
        """
        Thread-safe wrapper for analyze_factors with error handling and performance tracking.
        """
        if not self.is_initialized:
            self.logger.error(f"[{self.__class__.__name__}] Analyzer not initialized")
            return []
            
        start_time = datetime.now()
        
        try:
            factors = self.analyze_factors(data_package)
            
            # Validate factors
            valid_factors = []
            for factor in factors:
                is_valid, reason = validate_factor(factor)
                if is_valid:
                    valid_factors.append(factor)
                else:
                    self.logger.warning(f"Invalid factor discarded: {reason}")
            
            # Update performance stats
            runtime = (datetime.now() - start_time).total_seconds()
            self._update_performance_stats(runtime, len(valid_factors))
            self.last_analysis_time = datetime.now()
            
            return valid_factors
            
        except Exception as e:
            self.error_count += 1
            self.logger.error(f"[{self.__class__.__name__}] Analysis failed: {str(e)}")
            return []
    
    def __str__(self):
        return f"{self.__class__.__name__}(initialized={self.is_initialized}, " \
               f"analyses={self.analysis_count}, errors={self.error_count})"
    
    def __repr__(self):
        return self.__str__()


# Export the main classes for convenience
__all__ = [
    'BaseAnalyzer',
    'FactorData',
    'TimeFrame', 
    'DirectionBias',
    'validate_factor'
]
