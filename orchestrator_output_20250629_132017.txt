Ultimate Orchestrator Test Output
Timestamp: 2025-06-29 13:20:17.709347
Command: py ultimate_orchestrator.py MSFT
Return Code: 0
============================================================
STDOUT:
ULTIMATE TRADING INTELLIGENCE: MSFT
======================================================================
0: DATA INITIALIZATION: Single-source fetch from Schwab MCP...
Training logged: {
  "inputs": {
    "ticker_list": [
      "MSFT"
    ],
    "source": "schwab"
  },
  "outputs": {
    "status": "OK",
    "meta": {
      "MSFT": {
        "bars": 0,
        "opts": 14,
        "source": "schwab",
        "bars_file": "data\\live\\2025-06-29\\MSFT_bars.parquet",
        "opts_file": "data\\live\\2025-06-29\\MSFT_options.parquet"
      }
    },
    "source_used": "schwab",
    "successful_tickers": [
      "MSFT"
    ],
    "output_dir": "data\\live\\2025-06-29"
  }
}
   OK Bars data: 0 records
   OK Options data: 14 records
   OK Single-source data initialization complete (localhost:8005)
1: B-Series: Building Greek features with ROC derivatives...
 Pipeline failed: Feature building failed: Missing required columns: ['o', 'h', 'l', 'c', 'v']

============================================================
STDERR:
2025-06-29 13:20:12,585 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
[MSFT] Schwab MCP bars endpoint failed: 404
Failed to build features for MSFT: Missing required columns: ['o', 'h', 'l', 'c', 'v']
Traceback (most recent call last):
  File "D:\script-work\CORE\ultimate_orchestrator.py", line 1234, in <module>
    result = ultimate_trading_pipeline(ticker)
  File "D:\script-work\CORE\ultimate_orchestrator.py", line 234, in ultimate_trading_pipeline
    raise Exception(f"Feature building failed: {feature_result.get('error')}")
Exception: Feature building failed: Missing required columns: ['o', 'h', 'l', 'c', 'v']
