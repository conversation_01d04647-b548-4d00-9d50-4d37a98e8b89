#!/usr/bin/env python3
"""
Liquidity Agent - Specialized agent for liquidity sweep detection and execution

This agent focuses specifically on liquidity-based trading signals and works
alongside Agent Zero to provide specialized liquidity expertise.
"""

import os
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

# Optional imports for enhanced functionality
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

@dataclass
class LiquidityEvent:
    """Represents a detected liquidity event"""
    ticker: str
    timestamp: datetime
    event_type: str  # 'sweep', 'absorption', 'pocket', 'imbalance'
    confidence: float
    volume_impact: float
    price_level: float
    direction: str  # 'bullish', 'bearish', 'neutral'
    urgency: str  # 'immediate', 'short_term', 'medium_term'
    
@dataclass
class LiquidityRecommendation:
    """Trading recommendation based on liquidity analysis"""
    action: str  # 'buy', 'sell', 'hold', 'avoid'
    confidence: float
    entry_price: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    position_size: float
    timeframe: str
    reasoning: str

class LiquidityAgent:
    """
    Specialized agent for liquidity-based trading decisions.
    
    Focuses on:
    - Liquidity sweep detection
    - Volume absorption patterns
    - Liquidity pocket identification
    - Smart money flow tracking
    """
    
    def __init__(self, config: Optional[Dict] = None):
        # Handle both dict config and string name for compatibility
        if isinstance(config, str):
            self.name = config
            self.config = {}
        else:
            self.config = config or {}
            self.name = "LiquidityAgent"
        self.version = "1.0.0"
        
        # Agent parameters
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
        self.volume_threshold = self.config.get('volume_threshold', 1.5)  # 1.5x average volume
        self.sweep_detection_window = self.config.get('sweep_window', 20)  # bars
        
        # Performance tracking
        self.trade_history: List[Dict] = []
        self.current_positions: Dict[str, Dict] = {}
        self.performance_stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'liquidity_events_detected': 0,
            'accuracy_score': 0.0
        }
        
        print(f"{self.name} v{self.version} initialized")
        print(f"   Confidence threshold: {self.confidence_threshold}")
        print(f"   Volume threshold: {self.volume_threshold}x")
        
    def analyze_liquidity_event(self, ticker: str, market_data: Dict, signal_data: Dict) -> Optional[LiquidityEvent]:
        """
        Analyze market data for liquidity events
        """
        try:
            # Validate inputs
            if not market_data or not signal_data:
                return None
                
            # Extract key data with defaults
            current_price = market_data.get('current_price', 0)
            volume_data = market_data.get('volume_profile', {})
            price_action = market_data.get('price_action', {})
            
            if current_price <= 0:
                return None
            
            # Liquidity sweep detection
            sweep_confidence = self._detect_liquidity_sweep(market_data, signal_data)
            
            # Volume absorption analysis
            absorption_score = self._analyze_volume_absorption(volume_data)
            
            # Smart money flow detection
            flow_direction = self._detect_smart_money_flow(market_data, signal_data)
            
            # Combine scores for overall confidence
            overall_confidence = (sweep_confidence * 0.4 + 
                                absorption_score * 0.3 + 
                                abs(flow_direction) * 0.3)
            
            if overall_confidence >= self.confidence_threshold:
                event_type = self._classify_liquidity_event(sweep_confidence, absorption_score, flow_direction)
                direction = 'bullish' if flow_direction > 0 else 'bearish' if flow_direction < 0 else 'neutral'
                urgency = self._determine_urgency(overall_confidence, absorption_score)
                
                event = LiquidityEvent(
                    ticker=ticker,
                    timestamp=datetime.now(),
                    event_type=event_type,
                    confidence=overall_confidence,
                    volume_impact=absorption_score,
                    price_level=current_price,
                    direction=direction,
                    urgency=urgency
                )
                
                self.performance_stats['liquidity_events_detected'] += 1
                return event
                
        except Exception as e:
            print(f"Error analyzing liquidity event for {ticker}: {e}")
            return None
    
    def _detect_liquidity_sweep(self, market_data: Dict, signal_data: Dict) -> float:
        """
        Detect liquidity sweep patterns
        Returns confidence score 0.0 - 1.0
        """
        try:
            # Look for price action beyond significant levels with volume
            price_movement = signal_data.get('strength', 0)
            volume_surge = market_data.get('volume_ratio', 1.0)
            
            # Check for sweep characteristics:
            # 1. Price breaks key level
            # 2. Volume increases significantly
            # 3. Quick reversal or continuation
            
            sweep_score = 0.0
            
            # Volume surge indicator
            if volume_surge > self.volume_threshold:
                sweep_score += 0.3
                
            # Price momentum indicator
            if abs(price_movement) > 0.6:
                sweep_score += 0.4
                
            # Execution quality (from signal data)
            execution_quality = signal_data.get('confidence', 0.5)
            sweep_score += execution_quality * 0.3
            
            return min(sweep_score, 1.0)
            
        except Exception:
            return 0.0
    
    def _analyze_volume_absorption(self, volume_data: Dict) -> float:
        """
        Analyze volume absorption patterns
        Returns absorption score 0.0 - 1.0
        """
        try:
            current_volume = volume_data.get('current', 0)
            avg_volume = volume_data.get('average', 1)
            
            if avg_volume == 0:
                return 0.0
                
            volume_ratio = current_volume / avg_volume
            
            # Strong absorption typically shows 2x+ volume
            if volume_ratio >= 2.0:
                return min(volume_ratio / 3.0, 1.0)  # Cap at 1.0
            elif volume_ratio >= 1.5:
                return 0.6
            else:
                return max(0.0, (volume_ratio - 1.0) / 0.5 * 0.4)
                
        except Exception:
            return 0.0
    
    def _detect_smart_money_flow(self, market_data: Dict, signal_data: Dict) -> float:
        """
        Detect smart money flow direction
        Returns flow strength: -1.0 (bearish) to +1.0 (bullish)
        """
        try:
            direction = signal_data.get('direction', 'neutral')
            strength = signal_data.get('strength', 0.5)
            
            # Convert direction to numeric
            if direction == 'bullish' or direction == 'buy':
                flow_direction = strength
            elif direction == 'bearish' or direction == 'sell':
                flow_direction = -strength
            else:
                flow_direction = 0.0
                
            # Factor in market structure
            price_structure = market_data.get('trend', 'neutral')
            if price_structure == 'uptrend' and flow_direction > 0:
                flow_direction *= 1.2
            elif price_structure == 'downtrend' and flow_direction < 0:
                flow_direction *= 1.2
                
            return max(-1.0, min(1.0, flow_direction))
            
        except Exception:
            return 0.0
    
    def _classify_liquidity_event(self, sweep_confidence: float, absorption_score: float, flow_direction: float) -> str:
        """Classify the type of liquidity event"""
        
        if sweep_confidence > 0.7:
            return 'sweep'
        elif absorption_score > 0.7:
            return 'absorption'
        elif abs(flow_direction) > 0.6:
            return 'imbalance'
        else:
            return 'pocket'
    
    def _determine_urgency(self, confidence: float, absorption: float) -> str:
        """Determine urgency level of the liquidity event"""
        
        urgency_score = (confidence + absorption) / 2
        
        if urgency_score >= 0.8:
            return 'immediate'
        elif urgency_score >= 0.6:
            return 'short_term'
        else:
            return 'medium_term'
    
    def generate_recommendation(self, liquidity_event: LiquidityEvent, market_context: Dict) -> LiquidityRecommendation:
        """
        Generate trading recommendation based on liquidity event
        """
        try:
            current_price = market_context.get('current_price', 0)
            atr = market_context.get('atr', current_price * 0.02)  # Default 2% ATR
            
            # Determine action based on event
            if liquidity_event.confidence >= 0.8 and liquidity_event.urgency == 'immediate':
                action = 'buy' if liquidity_event.direction == 'bullish' else 'sell'
                confidence = liquidity_event.confidence
                
                # Calculate risk management levels
                if action == 'buy':
                    entry_price = current_price
                    stop_loss = current_price - (atr * 1.5)
                    take_profit = current_price + (atr * 2.5)
                else:
                    entry_price = current_price
                    stop_loss = current_price + (atr * 1.5)
                    take_profit = current_price - (atr * 2.5)
                    
                # Position sizing based on confidence
                position_size = min(confidence * 0.02, 0.015)  # Max 1.5% risk
                
                timeframe = 'intraday' if liquidity_event.urgency == 'immediate' else 'short_term'
                
                reasoning = f"Liquidity {liquidity_event.event_type} detected with {confidence:.1%} confidence. " \
                           f"Volume impact: {liquidity_event.volume_impact:.2f}x. Direction: {liquidity_event.direction}."
                
            else:
                action = 'hold'
                confidence = liquidity_event.confidence
                entry_price = None
                stop_loss = None
                take_profit = None
                position_size = 0.0
                timeframe = 'wait'
                reasoning = f"Liquidity event confidence ({confidence:.1%}) below execution threshold."
            
            return LiquidityRecommendation(
                action=action,
                confidence=confidence,
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                position_size=position_size,
                timeframe=timeframe,
                reasoning=reasoning
            )
            
        except Exception as e:
            print(f"Error generating recommendation: {e}")
            return LiquidityRecommendation(
                action='hold',
                confidence=0.0,
                entry_price=None,
                stop_loss=None,
                take_profit=None,
                position_size=0.0,
                timeframe='error',
                reasoning=f"Error in recommendation generation: {e}"
            )
    
    def process_signal(self, ticker: str, market_data: Dict, signal_data: Dict, agent_zero_decision: Optional[Dict] = None) -> Dict:
        """
        Main processing function - analyze liquidity and generate recommendation
        """
        try:
            # Analyze for liquidity events
            liquidity_event = self.analyze_liquidity_event(ticker, market_data, signal_data)
            
            if liquidity_event:
                # Generate recommendation
                recommendation = self.generate_recommendation(liquidity_event, market_data)
                
                # Consider Agent Zero's input if available
                if agent_zero_decision:
                    recommendation = self._reconcile_with_agent_zero(recommendation, agent_zero_decision)
                
                result = {
                    'agent': self.name,
                    'ticker': ticker,
                    'timestamp': datetime.now().isoformat(),
                    'liquidity_event': {
                        'type': liquidity_event.event_type,
                        'confidence': liquidity_event.confidence,
                        'direction': liquidity_event.direction,
                        'urgency': liquidity_event.urgency,
                        'volume_impact': liquidity_event.volume_impact
                    },
                    'recommendation': {
                        'action': recommendation.action,
                        'confidence': recommendation.confidence,
                        'entry_price': recommendation.entry_price,
                        'stop_loss': recommendation.stop_loss,
                        'take_profit': recommendation.take_profit,
                        'position_size': recommendation.position_size,
                        'timeframe': recommendation.timeframe,
                        'reasoning': recommendation.reasoning
                    },
                    'agent_zero_input': agent_zero_decision is not None
                }
                
                # Log for performance tracking
                self._log_decision(result)
                
                return result
            else:
                return {
                    'agent': self.name,
                    'ticker': ticker,
                    'timestamp': datetime.now().isoformat(),
                    'liquidity_event': None,
                    'recommendation': {
                        'action': 'hold',
                        'confidence': 0.0,
                        'reasoning': 'No significant liquidity events detected'
                    },
                    'agent_zero_input': agent_zero_decision is not None
                }
                
        except Exception as e:
            print(f"Error processing signal for {ticker}: {e}")
            return {
                'agent': self.name,
                'ticker': ticker,
                'timestamp': datetime.now().isoformat(),
                'error': str(e),
                'recommendation': {
                    'action': 'hold',
                    'confidence': 0.0,
                    'reasoning': f'Processing error: {e}'
                }
            }
    
    def _reconcile_with_agent_zero(self, liquidity_rec: LiquidityRecommendation, agent_zero_decision: Dict) -> LiquidityRecommendation:
        """
        Reconcile liquidity agent recommendation with Agent Zero's decision
        """
        try:
            agent_zero_action = agent_zero_decision.get('action', 'hold')
            agent_zero_confidence = agent_zero_decision.get('confidence', 0.0)
            
            # If both agents agree, increase confidence
            if liquidity_rec.action == agent_zero_action:
                combined_confidence = min((liquidity_rec.confidence + agent_zero_confidence) / 2 * 1.1, 1.0)
                liquidity_rec.confidence = combined_confidence
                liquidity_rec.reasoning += f" Agent Zero agrees with {agent_zero_confidence:.1%} confidence."
            
            # If agents disagree, be more conservative
            elif liquidity_rec.action != agent_zero_action and liquidity_rec.action != 'hold':
                if agent_zero_confidence > 0.7:
                    # Agent Zero has high confidence, defer to it
                    liquidity_rec.action = 'hold'
                    liquidity_rec.confidence *= 0.5
                    liquidity_rec.reasoning += f" Conflicting signal from Agent Zero ({agent_zero_action}), staying cautious."
                else:
                    # Lower confidence from disagreement
                    liquidity_rec.confidence *= 0.8
                    liquidity_rec.reasoning += f" Agent Zero suggests {agent_zero_action}, proceeding with caution."
            
            return liquidity_rec
            
        except Exception as e:
            print(f"Error reconciling with Agent Zero: {e}")
            return liquidity_rec
    
    def _log_decision(self, decision_result: Dict):
        """Log decision for performance tracking"""
        try:
            self.trade_history.append({
                'timestamp': decision_result['timestamp'],
                'ticker': decision_result['ticker'],
                'action': decision_result['recommendation']['action'],
                'confidence': decision_result['recommendation']['confidence'],
                'liquidity_event_type': decision_result.get('liquidity_event', {}).get('type'),
                'reasoning': decision_result['recommendation']['reasoning']
            })
            
            self.performance_stats['total_trades'] += 1
            
        except Exception as e:
            print(f"Error logging decision: {e}")
    
    def get_performance_summary(self) -> Dict:
        """Get performance summary"""
        if self.performance_stats['total_trades'] > 0:
            win_rate = self.performance_stats['winning_trades'] / self.performance_stats['total_trades']
        else:
            win_rate = 0.0
            
        return {
            'agent': self.name,
            'version': self.version,
            'total_trades': self.performance_stats['total_trades'],
            'win_rate': win_rate,
            'total_pnl': self.performance_stats['total_pnl'],
            'liquidity_events_detected': self.performance_stats['liquidity_events_detected'],
            'accuracy_score': self.performance_stats['accuracy_score'],
            'last_updated': datetime.now().isoformat()
        }

if __name__ == "__main__":
    # Test the liquidity agent
    agent = LiquidityAgent()
    
    # Sample test data
    test_market_data = {
        'current_price': 150.25,
        'volume_profile': {'current': 2500000, 'average': 1200000},
        'price_action': {'high': 151.0, 'low': 149.5},
        'atr': 2.5,
        'trend': 'uptrend'
    }
    
    test_signal_data = {
        'direction': 'bullish',
        'strength': 0.75,
        'confidence': 0.8
    }
    
    result = agent.process_signal('AAPL', test_market_data, test_signal_data)
    print(json.dumps(result, indent=2))