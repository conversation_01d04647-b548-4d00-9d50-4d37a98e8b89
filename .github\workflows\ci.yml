name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  pre-commit-checks:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: Install pre-commit
      run: pip install pre-commit
    - name: Run pre-commit
      run: pre-commit run --all-files

  dependency-guard:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: Check version drift
      run: |
        pip install -r requirements_lock.txt
        python ci/check_version_drift.py

  performance-budget:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-python@v4
      with:
        python-version: 3.11
    - name: Install dependencies
      run: pip install -r requirements_lock.txt
    - name: Performance regression test
      run: python ci/performance_regression_test.py

  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov
        if [ -f requirements_lock.txt ]; then pip install -r requirements_lock.txt; fi

    - name: Run Tests and Generate Coverage Report
      run: |
        # This command assumes all test files have been moved into the 'tests/' directory
        pytest -q --cov=agents --cov=engine --cov=analyzers --cov-report=xml tests/
        
    - name: Contract matrix
      run: python ci/run_contract_tasks.py --only C-04
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
