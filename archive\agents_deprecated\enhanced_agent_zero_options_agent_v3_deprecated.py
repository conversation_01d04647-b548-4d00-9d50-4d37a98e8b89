#!/usr/bin/env python3
"""
ENHANCED AGENT ZERO OPTIONS AGENT - PHASE 3 IMPLEMENTATION
Complete Options Agent with Greek Engine integration and sophisticated intelligence
"""

import sys
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import agent framework
from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus, TaskPriority

# Import validated options intelligence
from options_intelligence_services import (
    OptionsIntelligenceService, 
    OptionsDataContract, 
    MarketContextContract,
    TradeAnalysisResult,
    PositionSizingResult,
    ExitStrategyResult
)

# Import continuous learning framework
from options_intelligence_learning_framework import (
    OptionsIntelligenceLearningFramework,
    OptionsLearningMetrics,
    OptionsTrainingData
)

logger = logging.getLogger(__name__)

class EnhancedAgentZeroOptionsAgent(BaseAgent):
    """
    Enhanced Agent Zero Options Agent with complete Greek Engine integration
    
    Capabilities:
    - Uses actual Greek Engine for precise calculations
    - Sophisticated trade analysis and classification
    - Risk-aware position sizing with multiple factors
    - Comprehensive exit strategy with time/IV/Greek-based rules
    - Complete integration with Agent Zero decision pipeline
    """
    
    def __init__(self, agent_id: str = "enhanced_agent_zero_options", config: dict = None):
        super().__init__(agent_id, config)
        
        # Initialize options intelligence service
        self.options_intelligence = OptionsIntelligenceService()
        
        # Initialize continuous learning framework
        self.learning_framework = OptionsIntelligenceLearningFramework()
        
        # Learning state
        self.learning_enabled = True
        self.continuous_improvement = True
        
        logger.info(f"Enhanced Agent Zero Options Agent initialized with continuous learning")
        logger.info(f"Learning iteration: {self.learning_framework.current_iteration}")
        # Configuration parameters
        self.max_position_size = 10.0
        self.min_position_size = 0.1
        self.default_confidence_threshold = 0.60
        
        logger.info(f"Enhanced Agent Zero Options Agent initialized: {self.agent_id}")
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """Validate task inputs meet agent requirements"""
        try:
            inputs = task.inputs
            
            # Required fields validation
            required_fields = [
                'ticker', 'underlying_price', 'strike', 'expiration_date',
                'option_type', 'implied_volatility', 'agent_zero_signal'
            ]
            
            for field in required_fields:
                if field not in inputs:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Signal validation
            valid_signals = ['buy_calls', 'buy_puts', 'hold', 'avoid']
            if inputs['agent_zero_signal'] not in valid_signals:
                logger.error(f"Invalid agent zero signal: {inputs['agent_zero_signal']}")
                return False
            
            # Numeric validations
            if inputs['underlying_price'] <= 0:
                logger.error("Invalid underlying price")
                return False
            
            if inputs['strike'] <= 0:
                logger.error("Invalid strike price")
                return False
            
            if not (0.01 <= inputs['implied_volatility'] <= 3.0):
                logger.error("Invalid implied volatility")
                return False
            
            # Option type validation
            if inputs['option_type'].lower() not in ['call', 'put']:
                logger.error("Invalid option type")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Input validation failed: {e}")
            return False
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        try:
            # Check required output fields
            required_outputs = ['final_action', 'execution_quality', 'comprehensive_analysis']
            completeness_score = sum(1 for field in required_outputs if field in outputs) / len(required_outputs)
            quality_metrics['output_completeness'] = completeness_score
            
            # Validate trade specification if applicable
            if outputs.get('final_action') in ['buy_calls', 'buy_puts']:
                trade_fields = ['strike', 'position_size', 'trade_analysis', 'exit_strategy', 'greeks_analysis']
                trade_completeness = sum(1 for field in trade_fields if field in outputs) / len(trade_fields)
                quality_metrics['trade_specification_quality'] = trade_completeness
                
                # Validate position size bounds
                position_size = outputs.get('position_size', 0)
                if self.min_position_size <= position_size <= self.max_position_size:
                    quality_metrics['position_size_validity'] = 1.0
                else:
                    quality_metrics['position_size_validity'] = 0.0
                
                # Validate exit strategy completeness
                exit_strategy = outputs.get('exit_strategy', {})
                exit_completeness = 1.0 if all(key in exit_strategy for key in 
                    ['max_days_in_trade', 'profit_targets', 'stop_losses']) else 0.0
                quality_metrics['exit_strategy_quality'] = exit_completeness
                
            else:
                # Non-trade actions still get full credit
                quality_metrics['trade_specification_quality'] = 1.0
                quality_metrics['position_size_validity'] = 1.0
                quality_metrics['exit_strategy_quality'] = 1.0
            
            # Calculate overall quality
            overall_quality = sum(quality_metrics.values()) / len(quality_metrics)
            quality_metrics['overall_quality'] = overall_quality
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Output validation failed: {e}")
            return {'overall_quality': 0.0, 'validation_error': 1.0}
    
    def execute_task(self, task: AgentTask) -> AgentResult:
        """Execute comprehensive options analysis with Greek integration"""
        start_time = time.time()
        
        try:
            inputs = task.inputs
            
            logger.info(f"Processing options analysis for {inputs.get('ticker', 'UNKNOWN')}: {inputs.get('agent_zero_signal', 'UNKNOWN')}")
            
            # Step 1: Create data contracts
            option_data = self._create_option_data_contract(inputs)
            market_context = self._create_market_context_contract(inputs)
            
            # CONTINUOUS LEARNING: Get dynamic adjustments based on learning
            learning_adjustments = {}
            if self.learning_enabled:
                learning_data = self.learning_framework.get_dynamic_adjustments({
                    'option_data': option_data.__dict__,
                    'market_context': market_context.__dict__,
                    'agent_zero_signal': inputs.get('agent_zero_signal')
                })
                learning_adjustments = learning_data.get('adjustments', {})
                logger.info(f"Learning adjustments applied: {len(learning_adjustments)} factors")
            
            # Record decision point
            self.record_decision_point(
                decision_type="enhanced_options_analysis_start",
                context={
                    'ticker': inputs.get('ticker'),
                    'signal': inputs.get('agent_zero_signal'),
                    'strike': inputs.get('strike'),
                    'underlying_price': inputs.get('underlying_price'),
                    'learning_enabled': self.learning_enabled,
                    'learning_adjustments': learning_adjustments
                },
                choice_made="begin_comprehensive_analysis",
                rationale=f"Agent Zero signal: {inputs.get('agent_zero_signal')} with learning adjustments"
            )
            
            # Step 2: Handle non-directional signals immediately
            agent_zero_signal = inputs['agent_zero_signal']
            if agent_zero_signal in ['avoid', 'hold']:
                result = self._handle_non_directional_signal(agent_zero_signal, inputs.get('ticker'))
                execution_time = time.time() - start_time
                
                return AgentResult(
                    task_id=task.task_id,
                    agent_id=self.agent_id,
                    status=TaskStatus.COMPLETED,
                    outputs=result,
                    execution_time=execution_time,
                    quality_metrics={}
                )
            
            # Step 3: Get Greeks analysis using actual Greek Engine
            greeks = self.options_intelligence.greek_adapter.calculate_greeks(option_data)
            
            # Step 4: Comprehensive trade analysis
            trade_analysis = self.options_intelligence.analyze_trade_type(
                option_data, greeks, market_context
            )
            
            # Step 5: Position sizing with Greek-based risk management + Learning adjustments
            agent_zero_confidence = inputs.get('agent_zero_confidence', 0.75)
            base_position_size = inputs.get('base_position_size', 1.0)
            
            # Apply learning adjustments to position sizing
            if self.learning_enabled and learning_adjustments:
                # Adjust confidence based on learning
                confidence_multiplier = learning_adjustments.get('confidence_multiplier', 1.0)
                agent_zero_confidence *= confidence_multiplier
                
                # Adjust position size based on learning
                position_adjustment = learning_adjustments.get('position_size_adjustment', 1.0)
                base_position_size *= position_adjustment
                
                logger.info(f"Learning adjustments: confidence={confidence_multiplier:.3f}, position={position_adjustment:.3f}")
            
            position_sizing = self.options_intelligence.calculate_position_size(
                base_size=base_position_size,
                option_data=option_data,
                greeks=greeks,
                market_context=market_context,
                agent_zero_confidence=agent_zero_confidence
            )
            
            # Step 6: Generate comprehensive exit strategy
            exit_strategy = self.options_intelligence.generate_exit_strategy(
                option_data, greeks, market_context, trade_analysis
            )
            
            # Step 7: Create comprehensive recommendation
            comprehensive_analysis = self._create_comprehensive_analysis(
                option_data, greeks, trade_analysis, position_sizing, exit_strategy, market_context
            )
            
            # Step 8: Generate final recommendation
            final_recommendation = self._generate_final_recommendation(
                agent_zero_signal, comprehensive_analysis, inputs
            )
            
            # CONTINUOUS LEARNING: Capture training data for learning
            if self.learning_enabled:
                training_context = {
                    'input_data': inputs,
                    'option_data': option_data.__dict__,
                    'market_context': market_context.__dict__,
                    'greeks': greeks.__dict__,
                    'learning_adjustments': learning_adjustments
                }
                
                prediction_data = {
                    'trade_analysis': trade_analysis.__dict__,
                    'position_sizing': position_sizing.__dict__,
                    'exit_strategy': exit_strategy.__dict__,
                    'final_recommendation': final_recommendation,
                    'roi_validation': final_recommendation.get('roi_validation', False)
                }
                
                # Capture for continuous learning
                training_file = self.learning_framework.capture_training_data(
                    input_context=training_context,
                    prediction=prediction_data,
                    actual_result=None  # Will be updated when market outcome is known
                )
                
                # Add learning metrics to final recommendation
                final_recommendation['learning_metrics'] = self.learning_framework.get_dynamic_adjustments(training_context)
                final_recommendation['training_file'] = training_file
                
                logger.info(f"Learning data captured: {training_file}")
            
            execution_time = time.time() - start_time
            
            # Record completion decision point
            self.record_decision_point(
                decision_type="enhanced_options_analysis_complete",
                context={
                    'trade_analysis': trade_analysis.trade_classification,
                    'position_size': position_sizing.recommended_size,
                    'max_hold_days': exit_strategy.max_days_in_trade
                },
                choice_made=final_recommendation.get('final_action'),
                rationale=f"Comprehensive analysis complete: {trade_analysis.trade_classification}"
            )
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs=final_recommendation,
                execution_time=execution_time,
                quality_metrics={}
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Enhanced options analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )
    
    def _create_option_data_contract(self, inputs: Dict[str, Any]) -> OptionsDataContract:
        """Create options data contract from task inputs"""
        return OptionsDataContract(
            underlying_price=float(inputs['underlying_price']),
            strike=float(inputs['strike']),
            expiration_date=inputs['expiration_date'],
            option_type=inputs['option_type'].lower(),
            implied_volatility=float(inputs['implied_volatility']),
            risk_free_rate=float(inputs.get('risk_free_rate', 0.05)),
            dividend_yield=float(inputs.get('dividend_yield', 0.0)),
            symbol=inputs.get('ticker', 'UNKNOWN')
        )
    
    def _create_market_context_contract(self, inputs: Dict[str, Any]) -> MarketContextContract:
        """Create market context contract from task inputs"""
        return MarketContextContract(
            iv_rank=float(inputs.get('iv_rank', 50.0)),
            iv_trend=inputs.get('iv_trend', 'stable'),
            price_trend=inputs.get('price_trend', 'neutral'),
            volatility_regime=inputs.get('volatility_regime', 'normal_vol'),
            days_to_earnings=inputs.get('days_to_earnings'),
            support_level=inputs.get('support_level'),
            resistance_level=inputs.get('resistance_level'),
            historical_vol_20d=inputs.get('historical_vol_20d', 0.20)
        )
    
    def _handle_non_directional_signal(self, signal: str, ticker: str) -> Dict[str, Any]:
        """Handle avoid/hold signals from Agent Zero"""
        if signal == 'avoid':
            return {
                'final_action': 'avoid',
                'reason': 'Agent Zero recommends avoiding - no options analysis needed',
                'ticker': ticker,
                'execution_quality': 'n/a',
                'comprehensive_analysis': {
                    'signal_processing': 'Agent Zero avoid signal processed',
                    'analysis_performed': False,
                    'recommendation': 'No trade - avoid signal'
                }
            }
        elif signal == 'hold':
            return {
                'final_action': 'hold',
                'reason': 'Agent Zero neutral - no directional options trade recommended',
                'ticker': ticker,
                'execution_quality': 'n/a',
                'comprehensive_analysis': {
                    'signal_processing': 'Agent Zero hold signal processed',
                    'analysis_performed': False,
                    'recommendation': 'No trade - neutral market'
                }
            }
    
    def _create_comprehensive_analysis(self, option_data: OptionsDataContract,
                                     greeks,
                                     trade_analysis: TradeAnalysisResult,
                                     position_sizing: PositionSizingResult,
                                     exit_strategy: ExitStrategyResult,
                                     market_context: MarketContextContract) -> Dict[str, Any]:
        """Create comprehensive analysis package"""
        
        return {
            'option_specification': {
                'ticker': option_data.symbol,
                'underlying_price': option_data.underlying_price,
                'strike': option_data.strike,
                'expiration_date': option_data.expiration_date,
                'option_type': option_data.option_type,
                'implied_volatility': option_data.implied_volatility
            },
            'greeks_analysis': {
                'delta': greeks.delta,
                'gamma': greeks.gamma,
                'theta': greeks.theta,
                'vega': greeks.vega,
                'rho': greeks.rho,
                'delta_roc': greeks.delta_roc,
                'gamma_roc': greeks.gamma_roc,
                'theta_roc': greeks.theta_roc,
                'vega_roc': greeks.vega_roc,
                'calculation_quality': greeks.calculation_quality,
                'mathematical_validity': greeks.mathematical_validity,
                'anomaly_count': greeks.anomaly_count
            },
            'trade_analysis': {
                'classification': trade_analysis.trade_classification,
                'risk_profile': trade_analysis.risk_profile,
                'leverage_factor': trade_analysis.leverage_factor,
                'daily_theta_cost': trade_analysis.daily_theta_cost,
                'gamma_acceleration': trade_analysis.gamma_acceleration,
                'iv_sensitivity': trade_analysis.iv_sensitivity,
                'probability_estimate': trade_analysis.probability_estimate,
                'breakeven_move_required': trade_analysis.breakeven_move_required,
                'max_profit_potential': trade_analysis.max_profit_potential,
                'risk_reward_ratio': trade_analysis.risk_reward_ratio
            },
            'position_sizing': {
                'recommended_size': position_sizing.recommended_size,
                'size_reasoning': position_sizing.size_reasoning,
                'risk_adjustments': position_sizing.risk_adjustments,
                'confidence_scaling': position_sizing.confidence_scaling
            },
            'exit_strategy': {
                'max_days_in_trade': exit_strategy.max_days_in_trade,
                'profit_targets': exit_strategy.profit_targets,
                'stop_losses': exit_strategy.stop_losses,
                'time_exits': exit_strategy.time_exits,
                'iv_exits': exit_strategy.iv_exits,
                'emergency_exits': exit_strategy.emergency_exits,
                'priority_exit_sequence': exit_strategy.priority_exit_sequence,
                'monitoring_alerts': exit_strategy.exit_monitoring_alerts
            },
            'market_environment': {
                'iv_rank': market_context.iv_rank,
                'iv_trend': market_context.iv_trend,
                'price_trend': market_context.price_trend,
                'volatility_regime': market_context.volatility_regime,
                'days_to_earnings': market_context.days_to_earnings
            },
            'risk_assessment': {
                'overall_risk_level': self._assess_overall_risk_level(trade_analysis, greeks),
                'primary_risks': self._identify_primary_risks(trade_analysis, greeks, market_context),
                'risk_mitigation': self._suggest_risk_mitigation(trade_analysis, exit_strategy),
                'execution_risks': self._assess_execution_risks(option_data, greeks)
            }
        }
    
    def _generate_final_recommendation(self, agent_zero_signal: str,
                                     comprehensive_analysis: Dict[str, Any],
                                     inputs: Dict[str, Any]) -> Dict[str, Any]:
        """Generate final recommendation with all analysis"""
        
        try:
            trade_analysis = comprehensive_analysis['trade_analysis']
            position_sizing = comprehensive_analysis['position_sizing']
            exit_strategy = comprehensive_analysis['exit_strategy']
            greeks_analysis = comprehensive_analysis['greeks_analysis']
            risk_assessment = comprehensive_analysis['risk_assessment']
            
            # Determine execution quality
            execution_quality = self._determine_execution_quality(comprehensive_analysis)
            
            # Generate reasoning
            reasoning = self._generate_comprehensive_reasoning(
                agent_zero_signal, comprehensive_analysis
            )
            
            # Create final recommendation
            recommendation = {
                'final_action': agent_zero_signal,
                'ticker': inputs.get('ticker'),
                'execution_quality': execution_quality,
                
                # Core trade specification
                'strike': comprehensive_analysis['option_specification']['strike'],
                'option_type': comprehensive_analysis['option_specification']['option_type'],
                'position_size': position_sizing['recommended_size'],
                'expiration_date': comprehensive_analysis['option_specification']['expiration_date'],
                
                # Greek-based analysis
                'estimated_delta': greeks_analysis['delta'],
                'daily_theta_cost': greeks_analysis['theta'],
                'iv_sensitivity': greeks_analysis['vega'],
                'gamma_risk': greeks_analysis['gamma'],
                
                # Trade characteristics
                'trade_classification': trade_analysis['classification'],
                'leverage_factor': trade_analysis['leverage_factor'],
                'probability_estimate': trade_analysis['probability_estimate'],
                'breakeven_move_pct': trade_analysis['breakeven_move_required'],
                
                # Exit intelligence
                'max_hold_days': exit_strategy['max_days_in_trade'],
                'profit_targets': exit_strategy['profit_targets'],
                'stop_losses': exit_strategy['stop_losses'],
                'primary_exit_rule': exit_strategy['priority_exit_sequence'][0] if exit_strategy['priority_exit_sequence'] else 'time_limit',
                
                # Risk assessment
                'overall_risk_level': risk_assessment['overall_risk_level'],
                'primary_risks': risk_assessment['primary_risks'],
                'execution_risks': risk_assessment['execution_risks'],
                
                # Complete analysis package
                'comprehensive_analysis': comprehensive_analysis,
                'reasoning': reasoning,
                'analysis_timestamp': datetime.now().isoformat(),
                'greek_engine_quality': greeks_analysis['calculation_quality']
            }
            
            return recommendation
            
        except Exception as e:
            logger.error(f"Final recommendation generation failed: {e}")
            return {
                'final_action': 'avoid',
                'reason': f'Analysis generation failed: {e}',
                'execution_quality': 'poor',
                'comprehensive_analysis': comprehensive_analysis
            }
    
    def _assess_overall_risk_level(self, trade_analysis: TradeAnalysisResult, greeks) -> str:
        """Assess overall risk level of the trade"""
        risk_factors = 0
        
        # Leverage risk
        if trade_analysis.leverage_factor > 10:
            risk_factors += 2
        elif trade_analysis.leverage_factor > 5:
            risk_factors += 1
        
        # Probability risk
        if trade_analysis.probability_estimate < 0.3:
            risk_factors += 2
        elif trade_analysis.probability_estimate < 0.5:
            risk_factors += 1
        
        # Theta risk
        if abs(greeks.theta) > 2.0:
            risk_factors += 1
        
        # Gamma risk
        if greeks.gamma > 0.1:
            risk_factors += 1
        
        if risk_factors >= 4:
            return 'high'
        elif risk_factors >= 2:
            return 'moderate'
        else:
            return 'low'
    
    def _identify_primary_risks(self, trade_analysis: TradeAnalysisResult, 
                              greeks, market_context: MarketContextContract) -> List[str]:
        """Identify primary risk factors"""
        risks = []
        
        if trade_analysis.leverage_factor > 10:
            risks.append('high_leverage')
        
        if abs(greeks.theta) > 2.0:
            risks.append('significant_time_decay')
        
        if trade_analysis.probability_estimate < 0.3:
            risks.append('low_probability_trade')
        
        if market_context.iv_rank > 80:
            risks.append('expensive_iv_environment')
        
        if greeks.vega > 0.05:
            risks.append('high_iv_sensitivity')
        
        return risks
    
    def _suggest_risk_mitigation(self, trade_analysis: TradeAnalysisResult,
                               exit_strategy: ExitStrategyResult) -> List[str]:
        """Suggest risk mitigation strategies"""
        mitigations = []
        
        if trade_analysis.leverage_factor > 10:
            mitigations.append('Reduce position size due to high leverage')
        
        if exit_strategy.max_days_in_trade <= 7:
            mitigations.append('Quick exit required due to time decay')
        
        if trade_analysis.probability_estimate < 0.3:
            mitigations.append('Small position size for speculation')
        
        mitigations.append('Monitor all exit conditions closely')
        
        return mitigations
    
    def _assess_execution_risks(self, option_data: OptionsDataContract, greeks) -> List[str]:
        """Assess execution-specific risks"""
        risks = []
        
        if abs(greeks.delta) < 0.1:
            risks.append('Low delta - may have wide spreads')
        
        # Add more execution risk assessments as needed
        return risks
    
    def _determine_execution_quality(self, comprehensive_analysis: Dict[str, Any]) -> str:
        """Determine overall execution quality"""
        risk_level = comprehensive_analysis['risk_assessment']['overall_risk_level']
        trade_classification = comprehensive_analysis['trade_analysis']['classification']
        
        if risk_level == 'low' and 'itm' in trade_classification:
            return 'excellent'
        elif risk_level == 'moderate':
            return 'good'
        elif risk_level == 'high' and 'speculation' not in trade_classification:
            return 'moderate'
        else:
            return 'poor'
    
    def _generate_comprehensive_reasoning(self, agent_zero_signal: str,
                                        comprehensive_analysis: Dict[str, Any]) -> str:
        """Generate comprehensive reasoning for the recommendation"""
        
        trade_analysis = comprehensive_analysis['trade_analysis']
        position_sizing = comprehensive_analysis['position_sizing']
        exit_strategy = comprehensive_analysis['exit_strategy']
        greeks = comprehensive_analysis['greeks_analysis']
        
        reasoning = (
            f"Agent Zero {agent_zero_signal} signal processed through comprehensive options analysis. "
            f"Trade classified as {trade_analysis['classification']} with {trade_analysis['leverage_factor']:.1f}x leverage "
            f"and {trade_analysis['probability_estimate']:.0%} estimated probability. "
            f"Position sized to {position_sizing['recommended_size']:.1f} contracts based on "
            f"{len(position_sizing['size_reasoning'])} risk factors. "
            f"Greeks: Delta {greeks['delta']:.3f}, Theta {greeks['theta']:.3f}, Vega {greeks['vega']:.3f}. "
            f"Exit strategy: maximum {exit_strategy['max_days_in_trade']} days with "
            f"{len(exit_strategy['profit_targets'])} profit targets and "
            f"{len(exit_strategy['stop_losses'])} stop losses."
        )
        
        return reasoning

# Task factory function for easy integration
def create_enhanced_options_task(ticker: str, underlying_price: float, strike: float,
                               expiration_date: str, option_type: str, implied_volatility: float,
                               agent_zero_signal: str, agent_zero_confidence: float = 0.75,
                               **kwargs) -> AgentTask:
    """Factory function to create enhanced options analysis task"""
    
    task_id = f"ENH-OPT-{ticker}-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    # Combine required and optional inputs
    inputs = {
        'ticker': ticker,
        'underlying_price': underlying_price,
        'strike': strike,
        'expiration_date': expiration_date,
        'option_type': option_type,
        'implied_volatility': implied_volatility,
        'agent_zero_signal': agent_zero_signal,
        'agent_zero_confidence': agent_zero_confidence,
        **kwargs  # Additional market context parameters
    }
    
    return AgentTask(
        task_id=task_id,
        task_type="enhanced_agent_zero_options_analysis",
        agent_type="EnhancedAgentZeroOptionsAgent",
        priority=TaskPriority.CRITICAL,
        inputs=inputs,
        workflow_file="enhanced_options_workflow.md",
        quality_standards="enhanced_options_standards.md",
        performance_targets={
            'max_execution_time': 250,  # ms - more complex analysis
            'analysis_completeness': 0.99,
            'greek_calculation_accuracy': 0.999
        },
        dependencies=[],
        training_data_tags=['enhanced_options', 'greek_integration', 'comprehensive_analysis'],
        timestamp=datetime.now(),
        correlation_id=f"enhanced_options_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )

# Validation function
def validate_enhanced_agent():
    """Validate the Enhanced Agent Zero Options Agent"""
    print("=" * 60)
    print("ENHANCED AGENT ZERO OPTIONS AGENT VALIDATION")
    print("=" * 60)
    
    try:
        # Initialize agent
        agent = EnhancedAgentZeroOptionsAgent()
        print("[OK] Enhanced Agent initialized")
        
        # Create test task
        task = create_enhanced_options_task(
            ticker='SPY',
            underlying_price=455.0,
            strike=460.0,
            expiration_date='2025-01-17',
            option_type='call',
            implied_volatility=0.25,
            agent_zero_signal='buy_calls',
            agent_zero_confidence=0.75,
            iv_rank=45.0,
            price_trend='bullish',
            volatility_regime='normal_vol'
        )
        print("[OK] Test task created")
        
        # Process task
        result = agent.process_task(task)
        print(f"[OK] Task processed: {result.status.value}")
        
        if result.status == TaskStatus.COMPLETED:
            outputs = result.outputs
            print(f"[OK] Final Action: {outputs.get('final_action')}")
            print(f"[OK] Position Size: {outputs.get('position_size', 0):.1f} contracts")
            print(f"[OK] Trade Type: {outputs.get('trade_classification')}")
            print(f"[OK] Max Hold Days: {outputs.get('max_hold_days')}")
            print(f"[OK] Execution Quality: {outputs.get('execution_quality')}")
            print(f"[OK] Delta: {outputs.get('estimated_delta', 0):.3f}")
            print(f"[OK] Analysis Quality: {outputs.get('greek_engine_quality', 0):.3f}")
            
            # Validate comprehensive analysis
            comp_analysis = outputs.get('comprehensive_analysis', {})
            if comp_analysis:
                print(f"[OK] Comprehensive analysis included: {len(comp_analysis)} sections")
            
            print("\n[SUCCESS] Enhanced Agent validation completed successfully")
            return True
        else:
            print(f"[ERROR] Task failed: {result.error_details}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Validation failed: {e}")
        return False

if __name__ == "__main__":
    validate_enhanced_agent()
