"""API robustness package."""

# Export the unified API gateway for easy import
import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

from .unified_api_gateway import UnifiedAPIGateway, get_api_gateway

__all__ = ['UnifiedAPIGateway', 'get_api_gateway']
