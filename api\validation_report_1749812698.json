{"validation_timestamp": **********.139135, "overall_status": "GOOD", "test_summary": {"total_tests": 8, "passed_tests": 7, "success_rate": 0.875, "overall_grade": "GOOD"}, "detailed_results": [{"timestamp": **********.4474034, "import_tests": {"unified_api_gateway": {"status": "success", "error": null}, "comprehensive_api_tester": {"status": "success", "error": null}, "ai_training_config": {"status": "success", "error": null}, "modules.rate_limiter": {"status": "success", "error": null}, "modules.api_cache": {"status": "success", "error": null}, "modules.api_health_monitor": {"status": "success", "error": null}, "modules.endpoint_registry": {"status": "success", "error": null}}, "success_count": 7, "total_tests": 7, "success_rate": 1.0}, {"timestamp": **********.6139915, "startup_test": {"process_started": true, "ready_signal_received": false, "startup_time": 66.50314044952393}, "server_ready": false}, {"timestamp": **********.1270056, "api_tests": {"gateway_ping": {"success": true, "timestamp": **********.5056782}, "spot_price": {"success": true, "price": 199.2, "valid_range": true}, "options_chain": {"success": true, "data_size": 2512}}, "component_count": 1}, {"timestamp": **********.1381295, "rate_limiter_tests": {"initialization": true, "token_acquisition": true, "statistics_available": true, "tier": "free"}}], "recommendations": ["Minor issues detected - review failed components"]}