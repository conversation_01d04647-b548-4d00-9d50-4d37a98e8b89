#!/usr/bin/env python3
"""
Auto Broker Adapter Agent - Tradier Sandbox Integration
Converts risk-guarded execution plans into paper trading orders with slippage controls.
"""

import os
import json
import re
import requests
import time
from pathlib import Path
from datetime import date
from typing import Dict, Any, Tuple
from agents.agent_base import BaseAgent


class AutoBrokerAdapterAgent(BaseAgent):
    """Auto broker adapter for Tradier sandbox paper trading"""
    
    task_id = "R-01"
    URL = os.getenv("TRADIER_URL", "https://sandbox.tradier.com/v1")
    HDR = {
        "Authorization": os.getenv("TRADIER_TOKEN"),
        "Accept": "application/json"
    }
    
    def __init__(self):
        super().__init__("auto_broker_adapter")
        self.max_slippage_cents = 5  # 5 cent slippage tolerance
    
    def execute_task(self, task: 'AgentTask') -> 'AgentResult':
        """Execute task using standardized interface"""
        return super().execute_task(task)
    
    def validate_inputs(self, inputs: Dict[str, Any]) -> bool:
        """Validate execution plan and unified analysis inputs"""
        required_keys = ['execution_plan_md', 'unified_analysis_json']
        for key in required_keys:
            if key not in inputs:
                return False
        return True
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> bool:
        """Validate fill outputs meet requirements"""
        if 'fill_path' not in outputs:
            return False
        
        try:
            fill_path = Path(outputs['fill_path'])
            if not fill_path.exists():
                return False
            
            # Validate fill JSON structure
            fill_data = json.loads(fill_path.read_text())
            return 'fill_data' in fill_data and 'metadata' in fill_data
        except:
            return False
        
    def _validate_environment(self) -> None:
        """Validate required environment variables"""
        if not os.getenv("TRADIER_TOKEN"):
            raise ValueError("TRADIER_TOKEN environment variable required")
        
        if not os.getenv("TRADIER_URL"):
            raise ValueError("TRADIER_URL environment variable required")
        
        if not self.HDR["Authorization"]:
            raise ValueError("Invalid TRADIER_TOKEN format")
    
    def _parse_plan(self, plan_text: str) -> Tuple[int, float, str, str, str]:
        """Parse execution plan for order parameters"""
        try:
            # Extract order details using regex
            strike_match = re.search(r"Strike\s*:\s*\$?(\d+\.?\d*)", plan_text)
            expiry_match = re.search(r"Expiry\s*:\s*([\d-]+)", plan_text)
            qty_match = re.search(r"Quantity\s*:\s*(\d+)", plan_text)
            limit_match = re.search(r"Limit Price\s*:\s*\$?(\d+\.?\d*)", plan_text)
            
            if not all([strike_match, expiry_match, qty_match, limit_match]):
                raise ValueError("Missing required fields in execution plan")
            
            strike = strike_match.group(1)
            expiry = expiry_match.group(1)
            qty = int(qty_match.group(1))
            limit_price = float(limit_match.group(1))
            
            # Default to buy_to_open for calls
            side = "buy_to_open"
            
            return qty, limit_price, strike, expiry, side
            
        except (AttributeError, ValueError) as e:
            raise ValueError(f"Failed to parse execution plan: {e}")
    
    def _build_option_symbol(self, ticker: str, expiry: str, strike: str, 
                           option_type: str = "C") -> str:
        """Build option symbol in OCC format"""
        # Convert expiry from YYYY-MM-DD to YYMMDD
        clean_expiry = expiry.replace('-', '')
        if len(clean_expiry) == 8:  # YYYYMMDD
            clean_expiry = clean_expiry[2:]  # Take last 6 digits (YYMMDD)
        
        # Format strike price (8 digits total, 3 decimal places)
        # Examples: 210 -> 00210000, 210.50 -> 00210500, 25.75 -> 00025750
        strike_float = float(strike)
        strike_int = int(strike_float * 1000)  # Convert to integer (multiply by 1000 for 3 decimal places)
        strike_formatted = f"{strike_int:08d}"  # Pad to 8 digits
        
        # Build symbol: TICKER + EXPIRY + TYPE + STRIKE
        symbol = f"{ticker}{clean_expiry}{option_type}{strike_formatted}"
        
        return symbol
    
    def _submit_order(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Submit order to Tradier sandbox API"""
        try:
            response = requests.post(
                f"{self.URL}/accounts/demo/orders",
                headers=self.HDR,
                data=payload,
                timeout=8
            )
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.Timeout:
            raise RuntimeError("Order submission timeout - API response >8s")
        except requests.exceptions.HTTPError as e:
            status_code = getattr(e.response, 'status_code', 'unknown') if e.response else 'unknown'
            error_text = getattr(e.response, 'text', 'unknown') if e.response else 'unknown'
            raise RuntimeError(f"API error: {status_code} - {error_text}")
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"Network error during order submission: {e}")
    
    def _validate_fill(self, fill_data: Dict[str, Any], expected_price: float) -> None:
        """Validate fill data and check slippage"""
        # Check required fields
        required_fields = ["status", "price"]
        missing_fields = [field for field in required_fields if field not in fill_data]
        if missing_fields:
            raise ValueError(f"Fill missing required fields: {missing_fields}")
        
        # Check fill status
        valid_statuses = ["filled", "ok", "pending", "accepted"]
        if fill_data.get("status") not in valid_statuses:
            raise ValueError(f"Invalid fill status: {fill_data.get('status')}")
        
        # Check slippage
        fill_price = float(fill_data.get("price", expected_price))
        slippage = abs(expected_price - fill_price)
        slippage_cents = slippage * 100
        
        if slippage_cents > self.max_slippage_cents:
            raise ValueError(
                f"Slippage {slippage_cents:.2f} exceeds {self.max_slippage_cents} limit"
            )
    
    def _save_fill(self, fill_data: Dict[str, Any], ticker: str) -> str:
        """Save fill data to file system"""
        # Create output directory
        out_dir = Path(f"fills/{date.today().isoformat()}/{ticker}")
        out_dir.mkdir(parents=True, exist_ok=True)
        
        # Save fill data
        fill_file = out_dir / "fill.json"
        
        # Add metadata
        fill_with_metadata = {
            "fill_data": fill_data,
            "metadata": {
                "timestamp": time.time(),
                "date": date.today().isoformat(),
                "ticker": ticker,
                "agent": self.task_id
            }
        }
        
        fill_file.write_text(json.dumps(fill_with_metadata, indent=2))
        
        return str(fill_file)
    
    def execute(self, plan_path: str, ua_path: str) -> str:
        """Execute broker order from risk-guarded plan"""
        start_time = time.time()
        
        try:
            # Validate environment
            self._validate_environment()
            
            # Load input files
            plan_text = Path(plan_path).read_text()
            unified_analysis = json.loads(Path(ua_path).read_text())
            
            # Parse execution plan
            qty, limit_price, strike, expiry, side = self._parse_plan(plan_text)
            
            # Build option symbol
            ticker = unified_analysis["ticker"]
            symbol = self._build_option_symbol(ticker, expiry, strike)
            
            # Build order payload
            payload = {
                "class": "option",
                "symbol": symbol,
                "side": side,
                "quantity": qty,
                "type": "limit", 
                "price": limit_price,
                "duration": "day"
            }
            
            # Submit order
            fill_data = self._submit_order(payload)
            
            # Validate fill
            self._validate_fill(fill_data, limit_price)
            
            # Save fill data
            fill_path = self._save_fill(fill_data, ticker)
            
            # Performance tracking
            execution_time = (time.time() - start_time) * 1000
            if execution_time > 1000:  # 1 second limit
                print(f"WARNING: Order execution took {execution_time:.0f}ms (>1000ms target)")
            
            print(f"Order filled successfully: {fill_path}")
            print(f"Symbol: {symbol}, Qty: {qty}, Price: ${limit_price}")
            
            return fill_path
            
        except Exception as e:
            print(f"Order execution failed: {e}")
            raise


if __name__ == "__main__":
    # Test execution
    agent = AutoBrokerAdapterAgent()
    print(f"Auto Broker Adapter Agent initialized")
    print(f"Tradier URL: {agent.URL}")
    print(f"Token configured: {'Yes' if os.getenv('TRADIER_TOKEN') else 'No'}")
