#!/usr/bin/env python3
"""
Operational Dashboards
Real-time trading dashboard, risk management console, system health overview, and performance analytics display
"""

import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import logging

@dataclass
class DashboardWidget:
    """Dashboard widget configuration"""
    widget_id: str
    widget_type: str  # 'chart', 'metric', 'table', 'gauge', 'status'
    title: str
    data_source: str
    refresh_interval: int  # seconds
    config: Dict[str, Any]

@dataclass
class DashboardLayout:
    """Dashboard layout configuration"""
    dashboard_id: str
    title: str
    description: str
    widgets: List[DashboardWidget]
    layout_config: Dict[str, Any]

class OperationalDashboards:
    """
    Comprehensive operational dashboards for trading system monitoring
    Real-time data visualization with mathematical precision
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Dashboard configurations
        self.dashboards = {}
        self.widget_data_cache = {}
        self.update_frequencies = {}
        
        # Data sources
        self.data_sources = {
            'system_health': None,
            'trading_performance': None,
            'risk_metrics': None,
            'execution_quality': None,
            'alert_system': None
        }
        
        self.logger = logging.getLogger(__name__)
        
        # Initialize default dashboards
        self._initialize_default_dashboards()
    
    def register_data_source(self, source_name: str, source_instance):
        """Register a data source for dashboard widgets"""
        self.data_sources[source_name] = source_instance
        self.logger.info(f"Registered data source: {source_name}")
    
    def _initialize_default_dashboards(self):
        """Initialize default dashboard configurations"""
        
        # Real-time Trading Dashboard
        self.dashboards['trading'] = DashboardLayout(
            dashboard_id='trading',
            title='Real-Time Trading Dashboard',
            description='Live trading performance and execution monitoring',
            widgets=[
                DashboardWidget(
                    widget_id='pnl_chart',
                    widget_type='chart',
                    title='P&L Chart',
                    data_source='trading_performance',
                    refresh_interval=5,
                    config={
                        'chart_type': 'line',
                        'time_range': '1d',
                        'metrics': ['realized_pnl', 'unrealized_pnl', 'total_pnl']
                    }
                ),
                DashboardWidget(
                    widget_id='daily_stats',
                    widget_type='metric',
                    title='Daily Statistics',
                    data_source='trading_performance',
                    refresh_interval=10,
                    config={
                        'metrics': ['trades_today', 'pnl_today', 'win_rate', 'avg_trade_pnl']
                    }
                ),
                DashboardWidget(
                    widget_id='active_positions',
                    widget_type='table',
                    title='Active Positions',
                    data_source='trading_performance',
                    refresh_interval=5,
                    config={
                        'columns': ['ticker', 'position_size', 'entry_price', 'current_price', 'unrealized_pnl', 'strategy']
                    }
                ),
                DashboardWidget(
                    widget_id='execution_quality',
                    widget_type='gauge',
                    title='Execution Quality',
                    data_source='execution_quality',
                    refresh_interval=30,
                    config={
                        'metric': 'fill_rate',
                        'ranges': [
                            {'min': 0, 'max': 0.8, 'color': 'red'},
                            {'min': 0.8, 'max': 0.95, 'color': 'yellow'},
                            {'min': 0.95, 'max': 1.0, 'color': 'green'}
                        ]
                    }
                )
            ],
            layout_config={
                'grid_columns': 3,
                'grid_rows': 2,
                'widget_positions': {
                    'pnl_chart': {'row': 0, 'col': 0, 'span_col': 2},
                    'daily_stats': {'row': 0, 'col': 2, 'span_col': 1},
                    'active_positions': {'row': 1, 'col': 0, 'span_col': 2},
                    'execution_quality': {'row': 1, 'col': 2, 'span_col': 1}
                }
            }
        )
        
        # Risk Management Console
        self.dashboards['risk'] = DashboardLayout(
            dashboard_id='risk',
            title='Risk Management Console',
            description='Real-time risk monitoring and control',
            widgets=[
                DashboardWidget(
                    widget_id='var_gauge',
                    widget_type='gauge',
                    title='Value at Risk (95%)',
                    data_source='risk_metrics',
                    refresh_interval=60,
                    config={
                        'metric': 'var_95_percentage',
                        'ranges': [
                            {'min': 0, 'max': 0.03, 'color': 'green'},
                            {'min': 0.03, 'max': 0.05, 'color': 'yellow'},
                            {'min': 0.05, 'max': 0.10, 'color': 'red'}
                        ]
                    }
                ),
                DashboardWidget(
                    widget_id='position_exposure',
                    widget_type='chart',
                    title='Position Exposure',
                    data_source='risk_metrics',
                    refresh_interval=30,
                    config={
                        'chart_type': 'pie',
                        'metric': 'position_breakdown'
                    }
                ),
                DashboardWidget(
                    widget_id='risk_limits',
                    widget_type='table',
                    title='Risk Limit Status',
                    data_source='risk_metrics',
                    refresh_interval=30,
                    config={
                        'columns': ['limit_type', 'current_value', 'limit_value', 'utilization', 'status']
                    }
                ),
                DashboardWidget(
                    widget_id='correlation_matrix',
                    widget_type='chart',
                    title='Position Correlation Matrix',
                    data_source='risk_metrics',
                    refresh_interval=300,
                    config={
                        'chart_type': 'heatmap',
                        'metric': 'correlation_matrix'
                    }
                )
            ],
            layout_config={
                'grid_columns': 2,
                'grid_rows': 2,
                'widget_positions': {
                    'var_gauge': {'row': 0, 'col': 0, 'span_col': 1},
                    'position_exposure': {'row': 0, 'col': 1, 'span_col': 1},
                    'risk_limits': {'row': 1, 'col': 0, 'span_col': 1},
                    'correlation_matrix': {'row': 1, 'col': 1, 'span_col': 1}
                }
            }
        )
        
        # System Health Overview
        self.dashboards['system'] = DashboardLayout(
            dashboard_id='system',
            title='System Health Overview',
            description='Real-time system performance and health monitoring',
            widgets=[
                DashboardWidget(
                    widget_id='system_resources',
                    widget_type='chart',
                    title='System Resources',
                    data_source='system_health',
                    refresh_interval=5,
                    config={
                        'chart_type': 'line',
                        'time_range': '1h',
                        'metrics': ['cpu_usage', 'memory_usage', 'disk_usage']
                    }
                ),
                DashboardWidget(
                    widget_id='agent_status',
                    widget_type='status',
                    title='Agent Status',
                    data_source='system_health',
                    refresh_interval=10,
                    config={
                        'status_type': 'agent_health'
                    }
                ),
                DashboardWidget(
                    widget_id='api_connections',
                    widget_type='status',
                    title='API Connections',
                    data_source='system_health',
                    refresh_interval=15,
                    config={
                        'status_type': 'api_health'
                    }
                ),
                DashboardWidget(
                    widget_id='response_times',
                    widget_type='chart',
                    title='Response Times',
                    data_source='system_health',
                    refresh_interval=30,
                    config={
                        'chart_type': 'bar',
                        'metric': 'component_response_times'
                    }
                )
            ],
            layout_config={
                'grid_columns': 2,
                'grid_rows': 2,
                'widget_positions': {
                    'system_resources': {'row': 0, 'col': 0, 'span_col': 2},
                    'agent_status': {'row': 1, 'col': 0, 'span_col': 1},
                    'api_connections': {'row': 1, 'col': 1, 'span_col': 1},
                    'response_times': {'row': 2, 'col': 0, 'span_col': 2}
                }
            }
        )
        
        # Performance Analytics Display
        self.dashboards['analytics'] = DashboardLayout(
            dashboard_id='analytics',
            title='Performance Analytics',
            description='Detailed performance analysis and strategy metrics',
            widgets=[
                DashboardWidget(
                    widget_id='strategy_performance',
                    widget_type='table',
                    title='Strategy Performance',
                    data_source='trading_performance',
                    refresh_interval=60,
                    config={
                        'columns': ['strategy', 'total_trades', 'win_rate', 'profit_factor', 'sharpe_ratio', 'max_drawdown', 'total_pnl']
                    }
                ),
                DashboardWidget(
                    widget_id='roi_distribution',
                    widget_type='chart',
                    title='ROI Distribution',
                    data_source='trading_performance',
                    refresh_interval=300,
                    config={
                        'chart_type': 'histogram',
                        'metric': 'trade_roi_distribution'
                    }
                ),
                DashboardWidget(
                    widget_id='cumulative_returns',
                    widget_type='chart',
                    title='Cumulative Returns',
                    data_source='trading_performance',
                    refresh_interval=60,
                    config={
                        'chart_type': 'line',
                        'time_range': '30d',
                        'metric': 'cumulative_returns'
                    }
                ),
                DashboardWidget(
                    widget_id='execution_metrics',
                    widget_type='metric',
                    title='Execution Metrics',
                    data_source='execution_quality',
                    refresh_interval=60,
                    config={
                        'metrics': ['avg_slippage', 'fill_rate', 'avg_fill_time', 'market_impact']
                    }
                )
            ],
            layout_config={
                'grid_columns': 2,
                'grid_rows': 2,
                'widget_positions': {
                    'strategy_performance': {'row': 0, 'col': 0, 'span_col': 2},
                    'roi_distribution': {'row': 1, 'col': 0, 'span_col': 1},
                    'cumulative_returns': {'row': 1, 'col': 1, 'span_col': 1},
                    'execution_metrics': {'row': 2, 'col': 0, 'span_col': 2}
                }
            }
        )
    
    def get_dashboard_data(self, dashboard_id: str) -> Dict[str, Any]:
        """Get complete dashboard data"""
        if dashboard_id not in self.dashboards:
            raise ValueError(f"Dashboard not found: {dashboard_id}")
        
        dashboard = self.dashboards[dashboard_id]
        dashboard_data = {
            'dashboard_info': asdict(dashboard),
            'widget_data': {},
            'last_updated': datetime.now().isoformat()
        }
        
        # Get data for each widget
        for widget in dashboard.widgets:
            try:
                widget_data = self._get_widget_data(widget)
                dashboard_data['widget_data'][widget.widget_id] = widget_data
            except Exception as e:
                self.logger.error(f"Error getting data for widget {widget.widget_id}: {e}")
                dashboard_data['widget_data'][widget.widget_id] = {'error': str(e)}
        
        return dashboard_data
    
    def _get_widget_data(self, widget: DashboardWidget) -> Dict[str, Any]:
        """Get data for a specific widget"""
        data_source = self.data_sources.get(widget.data_source)
        if not data_source:
            return {'error': f'Data source not available: {widget.data_source}'}
        
        if widget.widget_type == 'metric':
            return self._get_metric_widget_data(widget, data_source)
        elif widget.widget_type == 'chart':
            return self._get_chart_widget_data(widget, data_source)
        elif widget.widget_type == 'table':
            return self._get_table_widget_data(widget, data_source)
        elif widget.widget_type == 'gauge':
            return self._get_gauge_widget_data(widget, data_source)
        elif widget.widget_type == 'status':
            return self._get_status_widget_data(widget, data_source)
        else:
            return {'error': f'Unknown widget type: {widget.widget_type}'}
    
    def _get_metric_widget_data(self, widget: DashboardWidget, data_source) -> Dict[str, Any]:
        """Get data for metric widgets"""
        metrics = widget.config.get('metrics', [])
        
        if widget.data_source == 'trading_performance':
            summary = data_source.get_performance_summary()
            daily_stats = summary.get('daily_stats', {})
            
            metric_data = {}
            for metric in metrics:
                if metric == 'trades_today':
                    metric_data[metric] = daily_stats.get('trades_today', 0)
                elif metric == 'pnl_today':
                    metric_data[metric] = daily_stats.get('pnl_today', 0.0)
                elif metric == 'win_rate':
                    total_trades = summary.get('total_trades', 0)
                    if total_trades > 0:
                        # Calculate win rate from strategy data
                        win_rate = 0.0
                        total_wins = 0
                        for strategy_data in summary.get('strategies', {}).values():
                            total_wins += strategy_data.get('winning_trades', 0)
                        metric_data[metric] = total_wins / total_trades
                    else:
                        metric_data[metric] = 0.0
                elif metric == 'avg_trade_pnl':
                    total_trades = summary.get('total_trades', 0)
                    if total_trades > 0:
                        total_pnl = sum(s.get('total_pnl', 0) for s in summary.get('strategies', {}).values())
                        metric_data[metric] = total_pnl / total_trades
                    else:
                        metric_data[metric] = 0.0
            
            return {'metrics': metric_data}
        
        elif widget.data_source == 'execution_quality':
            if hasattr(data_source, 'execution_quality_history') and data_source.execution_quality_history:
                latest = data_source.execution_quality_history[-1]
                
                metric_data = {}
                for metric in metrics:
                    if hasattr(latest, metric):
                        metric_data[metric] = getattr(latest, metric)
                
                return {'metrics': metric_data}
        
        return {'metrics': {metric: 0.0 for metric in metrics}}
    
    def _get_chart_widget_data(self, widget: DashboardWidget, data_source) -> Dict[str, Any]:
        """Get data for chart widgets"""
        chart_type = widget.config.get('chart_type', 'line')
        
        if widget.data_source == 'trading_performance' and widget.widget_id == 'pnl_chart':
            # Get P&L time series data
            summary = data_source.get_performance_summary()
            
            # Generate sample time series (in production, would use actual data)
            now = datetime.now()
            time_points = [now - timedelta(hours=i) for i in range(24, 0, -1)]
            
            return {
                'chart_type': chart_type,
                'data': {
                    'labels': [t.strftime('%H:%M') for t in time_points],
                    'datasets': [
                        {
                            'label': 'Total P&L',
                            'data': [np.random.normal(100, 50) for _ in time_points],
                            'color': 'blue'
                        },
                        {
                            'label': 'Realized P&L',
                            'data': [np.random.normal(80, 30) for _ in time_points],
                            'color': 'green'
                        },
                        {
                            'label': 'Unrealized P&L',
                            'data': [np.random.normal(20, 20) for _ in time_points],
                            'color': 'orange'
                        }
                    ]
                }
            }
        
        elif widget.data_source == 'system_health' and widget.widget_id == 'system_resources':
            # Get system resource time series
            if hasattr(data_source, 'health_history') and data_source.health_history:
                recent_data = list(data_source.health_history)[-60:]  # Last 60 data points
                
                return {
                    'chart_type': chart_type,
                    'data': {
                        'labels': [d.timestamp.strftime('%H:%M:%S') for d in recent_data],
                        'datasets': [
                            {
                                'label': 'CPU Usage (%)',
                                'data': [d.cpu_usage for d in recent_data],
                                'color': 'red'
                            },
                            {
                                'label': 'Memory Usage (%)',
                                'data': [d.memory_usage for d in recent_data],
                                'color': 'blue'
                            },
                            {
                                'label': 'Disk Usage (%)',
                                'data': [d.disk_usage for d in recent_data],
                                'color': 'green'
                            }
                        ]
                    }
                }
        
        return {'chart_type': chart_type, 'data': {'labels': [], 'datasets': []}}
    
    def _get_table_widget_data(self, widget: DashboardWidget, data_source) -> Dict[str, Any]:
        """Get data for table widgets"""
        columns = widget.config.get('columns', [])
        
        if widget.data_source == 'trading_performance' and widget.widget_id == 'active_positions':
            # Get active positions data
            if hasattr(data_source, 'current_positions'):
                rows = []
                for ticker, position in data_source.current_positions.items():
                    row = {
                        'ticker': ticker,
                        'position_size': position.get('position_size', 0),
                        'entry_price': position.get('entry_price', 0.0),
                        'current_price': position.get('current_price', 0.0),
                        'unrealized_pnl': position.get('unrealized_pnl', 0.0),
                        'strategy': position.get('strategy', 'unknown')
                    }
                    rows.append(row)
                
                return {'columns': columns, 'rows': rows}
        
        elif widget.data_source == 'trading_performance' and widget.widget_id == 'strategy_performance':
            # Get strategy performance data
            summary = data_source.get_performance_summary()
            rows = []
            
            for strategy_name, strategy_data in summary.get('strategies', {}).items():
                row = {
                    'strategy': strategy_name,
                    'total_trades': strategy_data.get('total_trades', 0),
                    'win_rate': f"{strategy_data.get('win_rate', 0.0):.1%}",
                    'profit_factor': f"{strategy_data.get('profit_factor', 0.0):.2f}",
                    'sharpe_ratio': f"{strategy_data.get('sharpe_ratio', 0.0):.2f}",
                    'max_drawdown': f"{strategy_data.get('max_drawdown', 0.0):.1%}",
                    'total_pnl': f"${strategy_data.get('total_pnl', 0.0):.2f}"
                }
                rows.append(row)
            
            return {'columns': columns, 'rows': rows}
        
        return {'columns': columns, 'rows': []}
    
    def _get_gauge_widget_data(self, widget: DashboardWidget, data_source) -> Dict[str, Any]:
        """Get data for gauge widgets"""
        metric = widget.config.get('metric')
        ranges = widget.config.get('ranges', [])
        
        if widget.data_source == 'execution_quality' and metric == 'fill_rate':
            if hasattr(data_source, 'execution_quality_history') and data_source.execution_quality_history:
                latest = data_source.execution_quality_history[-1]
                value = getattr(latest, metric, 0.0)
                
                return {
                    'metric': metric,
                    'value': value,
                    'ranges': ranges,
                    'unit': '%'
                }
        
        elif widget.data_source == 'risk_metrics' and metric == 'var_95_percentage':
            if hasattr(data_source, 'risk_metrics_history') and data_source.risk_metrics_history:
                latest = data_source.risk_metrics_history[-1]
                var_95 = abs(latest.var_95)
                portfolio_value = latest.portfolio_value
                value = var_95 / portfolio_value if portfolio_value > 0 else 0.0
                
                return {
                    'metric': metric,
                    'value': value,
                    'ranges': ranges,
                    'unit': '%'
                }
        
        return {'metric': metric, 'value': 0.0, 'ranges': ranges, 'unit': ''}
    
    def _get_status_widget_data(self, widget: DashboardWidget, data_source) -> Dict[str, Any]:
        """Get data for status widgets"""
        status_type = widget.config.get('status_type')
        
        if widget.data_source == 'system_health':
            if status_type == 'agent_health':
                if hasattr(data_source, 'agent_statuses'):
                    statuses = []
                    for agent_id, agent in data_source.agent_statuses.items():
                        statuses.append({
                            'name': agent_id,
                            'status': agent.status,
                            'last_heartbeat': agent.last_heartbeat.isoformat(),
                            'response_time': f"{agent.response_time:.2f}s",
                            'success_rate': f"{agent.success_rate:.1%}"
                        })
                    
                    return {'status_type': status_type, 'items': statuses}
            
            elif status_type == 'api_health':
                if hasattr(data_source, 'api_statuses'):
                    statuses = []
                    for api_name, api in data_source.api_statuses.items():
                        statuses.append({
                            'name': api_name,
                            'status': api.status,
                            'response_time': f"{api.response_time:.2f}s",
                            'success_rate': f"{api.success_rate:.1%}",
                            'rate_limit': api.rate_limit_remaining
                        })
                    
                    return {'status_type': status_type, 'items': statuses}
        
        return {'status_type': status_type, 'items': []}
    
    def get_dashboard_list(self) -> List[Dict[str, str]]:
        """Get list of available dashboards"""
        return [
            {
                'dashboard_id': dashboard_id,
                'title': dashboard.title,
                'description': dashboard.description
            }
            for dashboard_id, dashboard in self.dashboards.items()
        ]

if __name__ == "__main__":
    # Test the operational dashboards
    dashboards = OperationalDashboards()
    
    # Get dashboard list
    dashboard_list = dashboards.get_dashboard_list()
    print("Available Dashboards:")
    print(json.dumps(dashboard_list, indent=2))
    
    # Get trading dashboard data (would work with actual data sources)
    print("\nTrading Dashboard Structure:")
    trading_data = dashboards.get_dashboard_data('trading')
    print(json.dumps(trading_data['dashboard_info'], indent=2, default=str))
