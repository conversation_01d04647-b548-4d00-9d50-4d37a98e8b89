"""
Strategy Optimizer for ML Trading Models

This module implements optimization algorithms for trading strategies.
"""

import os
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Union, Optional, Tuple

# Import from project root
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml.ml_model_registry import get_model_registry

# Set up logging
logger = logging.getLogger(__name__)

class StrategyOptimizer:
    """
    Optimizer for trading strategies.
    
    This class implements algorithms for optimizing trading strategy parameters
    based on historical performance.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the strategy optimizer.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.model_registry = get_model_registry()
        
        # Set default values
        self.agent_type = self.config.get('agent_type', 'ppo')
        self.learning_rate = self.config.get('learning_rate', 0.001)
        self.num_epochs = self.config.get('num_epochs', 100)
        self.batch_size = self.config.get('batch_size', 64)
        
        # Internal state
        self.model = None
        self.is_trained = False
        self.training_metrics = {}
    
    def train(self, 
             training_data: pd.DataFrame, 
             validation_data: Optional[pd.DataFrame] = None) -> Dict[str, List[float]]:
        """
        Train the optimizer on historical data.
        
        Args:
            training_data: Training data
            validation_data: Optional validation data
            
        Returns:
            Dictionary of training metrics
        """
        # For unit testing, implement a simplified version
        # In a real implementation, this would use a proper RL framework
        
        # Initialize metrics
        epochs = []
        train_rewards = []
        val_rewards = []
        
        # Simple simulation of training process
        for epoch in range(self.num_epochs):
            # Simulate training
            train_reward = 0.5 + 0.3 * (1 - np.exp(-0.05 * epoch)) + np.random.normal(0, 0.05)
            
            # Simulate validation if data is provided
            if validation_data is not None:
                val_reward = 0.4 + 0.25 * (1 - np.exp(-0.04 * epoch)) + np.random.normal(0, 0.07)
            else:
                val_reward = None
            
            # Store metrics
            epochs.append(epoch)
            train_rewards.append(train_reward)
            if val_reward is not None:
                val_rewards.append(val_reward)
            
            # Log progress
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: train_reward={train_reward:.4f}" + 
                           (f", val_reward={val_reward:.4f}" if val_reward is not None else ""))
        
        # Store metrics
        self.training_metrics = {
            "epochs": epochs,
            "train_rewards": train_rewards
        }
        
        if val_rewards:
            self.training_metrics["val_rewards"] = val_rewards
        
        # Mark as trained
        self.is_trained = True
        
        return self.training_metrics
    
    def optimize_parameters(self, 
                           parameters: Dict[str, float], 
                           constraints: Dict[str, Tuple[float, float]] = None) -> Dict[str, float]:
        """
        Optimize strategy parameters.
        
        Args:
            parameters: Initial parameters
            constraints: Parameter constraints as (min, max) tuples
            
        Returns:
            Optimized parameters
        """
        if not self.is_trained:
            logger.warning("Optimizer not trained")
            return parameters
        
        # For unit testing, implement a simplified version
        # In a real implementation, this would use the trained model
        
        # Set default constraints
        if constraints is None:
            constraints = {}
            for key, value in parameters.items():
                constraints[key] = (value * 0.5, value * 1.5)
        
        # Simple optimization (random perturbations)
        optimized = {}
        for key, value in parameters.items():
            min_val, max_val = constraints.get(key, (value * 0.5, value * 1.5))
            
            # Perturb value
            new_value = value + np.random.normal(0, (max_val - min_val) * 0.1)
            
            # Clip to constraints
            new_value = max(min_val, min(max_val, new_value))
            
            optimized[key] = new_value
        
        return optimized
    
    def evaluate(self, parameters: Dict[str, float], test_data: pd.DataFrame) -> Dict[str, float]:
        """
        Evaluate parameters on test data.
        
        Args:
            parameters: Strategy parameters
            test_data: Test data
            
        Returns:
            Evaluation metrics
        """
        # For unit testing, implement a simplified version
        # In a real implementation, this would use a proper evaluation framework
        
        # Generate random evaluation metrics
        reward = np.random.normal(0.6, 0.1)
        sharpe = np.random.normal(1.2, 0.2)
        max_drawdown = np.random.normal(0.2, 0.05)
        win_rate = np.random.normal(0.55, 0.05)
        
        evaluation_metrics = {
            "reward": reward,
            "sharpe_ratio": sharpe,
            "max_drawdown": max_drawdown,
            "win_rate": win_rate
        }
        
        # Shadow mode logging - capture strategy optimization results
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            signal_data = {
                'confidence': min(win_rate, 1.0),  # Win rate as confidence
                'strength': min(abs(reward), 1.0),  # Reward magnitude as strength
                'execution_recommendation': 'execute' if reward > 0.5 else 'avoid'
            }
            
            math_data = {
                'accuracy_score': min(win_rate, 1.0),  # Win rate as accuracy
                'precision': 0.001
            }
            
            market_context = {
                'system': 'ml_strategy_optimizer',
                'parameters': parameters,
                'optimization_metrics': evaluation_metrics,
                'test_data_size': len(test_data) if hasattr(test_data, '__len__') else 'unknown'
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'strategy_optimization', 'metrics': evaluation_metrics},
                outcome=reward,  # Reward as outcome
                market_context=market_context
            )
            logger.info("Shadow mode: Strategy optimization logged")
            
        except Exception as e:
            logger.warning(f"Shadow mode logging failed: {e}")
        
        return evaluation_metrics
    
    def save(self, version: Optional[str] = None) -> str:
        """
        Save the optimizer to the model registry.
        
        Args:
            version: Optional version string
            
        Returns:
            Model ID
        """
        # Create state to save
        state = {
            "config": self.config,
            "is_trained": self.is_trained,
            "training_metrics": self.training_metrics
        }
        
        # Register model
        model_id = self.model_registry.register_model(
            model=state,
            model_name="strategy_optimizer",
            model_type="optimizer",
            metadata={"agent_type": self.agent_type},
            version=version
        )
        
        return model_id
    
    def load(self, model_id: str) -> bool:
        """
        Load the optimizer from the model registry.
        
        Args:
            model_id: ID of the model to load
            
        Returns:
            True if successful, False otherwise
        """
        try:
            state, metadata = self.model_registry.load_model(model_id, "optimizer")
            
            # Update state
            self.config = state.get("config", self.config)
            self.agent_type = self.config.get('agent_type', 'ppo')
            self.learning_rate = self.config.get('learning_rate', 0.001)
            self.num_epochs = self.config.get('num_epochs', 100)
            self.batch_size = self.config.get('batch_size', 64)
            
            self.is_trained = state.get("is_trained", False)
            self.training_metrics = state.get("training_metrics", {})
            
            return True
            
        except Exception as e:
            logger.error(f"Error loading optimizer: {e}")
            return False