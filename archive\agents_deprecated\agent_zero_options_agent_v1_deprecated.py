#!/usr/bin/env python3
"""
Agent Zero Options Intelligence Agent
Advanced options analysis and selection for Agent Zero trading decisions

Follows established agent protocol with BaseAgent inheritance,
standardized task processing, and training data capture.
"""

import sys
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import time
from datetime import datetime

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent))
from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus, TaskPriority

logger = logging.getLogger(__name__)

class AgentZeroOptionsAgent(BaseAgent):
    """
    Agent Zero Options Intelligence Agent
    
    Specialized agent for converting Agent Zero directional signals
    into specific, executable options trades with optimal strike selection.
    
    Mathematical Foundation:
    - Delta targeting with 0.25-0.75 range preferences
    - Liquidity scoring with multi-component weighting
    - Risk assessment with Greeks exposure analysis
    - Execution quality optimization
    
    Performance Standards:
    - Execution time: <150ms target
    - Strike selection accuracy: 95%
    - Liquidity assessment precision: 99%
    """
    
    task_id = "AZ-OPT"
    
    def __init__(self, agent_id: str = "agent_zero_options_agent", config: dict = None):
        super().__init__(agent_id, config)
        
        # Initialize Data Ingestion Agent for proper data access
        try:
            from agents.data_ingestion_agent import LiveDataGatewayAgent
            self.data_agent = LiveDataGatewayAgent()
            self.has_data_agent = True
            self.logger.info("Options Agent: Using Data Ingestion Agent for options/market data")
        except ImportError:
            self.logger.error("Data Ingestion Agent not available - Options Agent requires it")
            self.has_data_agent = False
        
        # Options intelligence configuration
        self.preferred_delta_range = (0.25, 0.75)
        self.max_bid_ask_spread_pct = 0.15
        self.min_liquidity_score = 0.30
        self.min_volume = 10
        self.min_open_interest = 50
        
        # Delta targeting by direction
        self.delta_targets = {
            'bullish_calls': (0.40, 0.60),    # Slight OTM to ATM
            'bearish_puts': (-0.60, -0.40),   # Slight OTM to ATM  
            'neutral': (0.45, 0.55)           # ATM only
        }
        
        # Liquidity component weights
        self.liquidity_weights = {
            'volume_component': 0.30,
            'open_interest_component': 0.30,
            'spread_component': 0.25,
            'price_level_component': 0.15
        }
        
        logger.info(f"Agent Zero Options Agent initialized: {self.agent_id}")
    
    async def get_options_chain(self, ticker: str) -> Dict[str, Any]:
        """Get options chain data through Data Ingestion Agent ONLY"""
        
        try:
            if not self.has_data_agent:
                raise ValueError("No data agent available")
                
            # Use Enhanced Data Agent through Data Ingestion Agent for options data
            if hasattr(self.data_agent, 'enhanced_agent') and self.data_agent.enhanced_agent:
                options_result = self.data_agent.enhanced_agent.get_options_data(ticker)
                
                if options_result and options_result.get('data'):
                    self.logger.info(f"Retrieved options chain for {ticker} via Enhanced Data Agent")
                    return options_result['data']
            
            # Fallback to standard data ingestion execution
            result = self.data_agent.execute([ticker], source="auto", data_type="options")
            if result.get('status') == 'OK':
                self.logger.info(f"Retrieved options chain for {ticker} via Data Ingestion Agent")
                return result
                
            self.logger.warning(f"No options chain available for {ticker}")
            return {}
            
        except Exception as e:
            self.logger.error(f"Failed to get options chain for {ticker}: {e}")
            return {}
    
    async def get_market_data(self, ticker: str) -> Dict[str, Any]:
        """Get live market data through Data Ingestion Agent ONLY"""
        
        try:
            if not self.has_data_agent:
                raise ValueError("No data agent available")
                
            # Use Enhanced Data Agent through Data Ingestion Agent
            if hasattr(self.data_agent, 'enhanced_agent') and self.data_agent.enhanced_agent:
                market_data_result = self.data_agent.enhanced_agent.get_market_data(ticker)
                
                if market_data_result and market_data_result.get('data'):
                    self.logger.info(f"Retrieved market data for {ticker} via Enhanced Data Agent")
                    return market_data_result['data']
            
            # Fallback to standard data ingestion execution
            result = self.data_agent.execute([ticker], source="auto")
            if result.get('status') == 'OK':
                self.logger.info(f"Retrieved market data for {ticker} via Data Ingestion Agent")
                return result
            
            raise ValueError(f"Data agent execution failed: {result}")
            
        except Exception as e:
            self.logger.error(f"Failed to get market data for {ticker}: {e}")
            return {}
    
    async def execute_enhanced(self, ticker: str, agent_zero_signal: str) -> Dict[str, Any]:
        """
        Execute enhanced options analysis using live data access
        
        Args:
            ticker: Stock symbol
            agent_zero_signal: Agent Zero directional signal ('buy_calls', 'buy_puts', 'hold', 'avoid')
            
        Returns:
            Dict: Complete options analysis with recommended strikes and contracts
        """
        start_time = time.time()
        
        try:
            if not self.has_data_agent:
                raise ValueError("Data Ingestion Agent required but not available")
                
            self.logger.info(f"Starting enhanced options analysis for {ticker} with signal: {agent_zero_signal}")
            
            # Get live market data and options chain
            market_data = await self.get_market_data(ticker)
            options_chain = await self.get_options_chain(ticker)
            
            if not market_data:
                raise ValueError("No market data available")
            if not options_chain:
                raise ValueError("No options chain available")
            
            # Extract underlying price
            underlying_price = market_data.get('last_price', market_data.get('price', 0.0))
            if underlying_price <= 0:
                raise ValueError(f"Invalid underlying price: {underlying_price}")
            
            # Execute options analysis using existing logic
            analysis_result = self._analyze_options_chain(
                ticker=ticker,
                options_chain=options_chain,
                underlying_price=underlying_price,
                agent_zero_signal=agent_zero_signal
            )
            
            execution_time = time.time() - start_time
            
            # Performance validation
            if execution_time * 1000 > 150:
                self.logger.warning(f"Performance target exceeded: {execution_time*1000:.1f}ms > 150ms")
            
            # Add execution metadata
            analysis_result.update({
                'execution_time_ms': execution_time * 1000,
                'data_sources': {
                    'market_data': bool(market_data),
                    'options_chain': bool(options_chain),
                    'underlying_price': underlying_price
                },
                'timestamp': time.time(),
                'agent_id': self.agent_id,
                'task_id': self.task_id,
                'status': 'SUCCESS'
            })
            
            return analysis_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Enhanced options analysis failed for {ticker}: {e}")
            
            return {
                'ticker': ticker,
                'agent_zero_signal': agent_zero_signal,
                'status': 'FAILED',
                'error': str(e),
                'execution_time_ms': execution_time * 1000,
                'timestamp': time.time(),
                'agent_id': self.agent_id,
                'task_id': self.task_id
            }
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """Validate task inputs meet agent requirements"""
        try:
            inputs = task.inputs
            
            # Required fields validation - only ticker and signal needed now
            required_fields = ['ticker', 'agent_zero_signal']
            for field in required_fields:
                if field not in inputs:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            # Signal validation
            valid_signals = ['buy_calls', 'buy_puts', 'hold', 'avoid']
            if inputs['agent_zero_signal'] not in valid_signals:
                self.logger.error(f"Invalid agent zero signal: {inputs['agent_zero_signal']}")
                return False
            
            # Ticker validation
            ticker = inputs['ticker']
            if not isinstance(ticker, str) or len(ticker.strip()) == 0:
                self.logger.error("Invalid ticker symbol")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Input validation failed: {e}")
            return False
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        try:
            # Check required output fields
            required_outputs = ['final_action', 'execution_quality']
            completeness_score = sum(1 for field in required_outputs if field in outputs) / len(required_outputs)
            quality_metrics['output_completeness'] = completeness_score
            
            # Validate execution quality if trade recommended
            if outputs.get('final_action') in ['buy_calls', 'buy_puts']:
                trade_quality_fields = ['strike', 'premium', 'liquidity_score', 'risk_metrics']
                trade_completeness = sum(1 for field in trade_quality_fields if field in outputs) / len(trade_quality_fields)
                quality_metrics['trade_specification_quality'] = trade_completeness
                
                # Validate numerical ranges
                if 'liquidity_score' in outputs:
                    liquidity_score = outputs['liquidity_score']
                    if 0.0 <= liquidity_score <= 1.0:
                        quality_metrics['liquidity_score_validity'] = 1.0
                    else:
                        quality_metrics['liquidity_score_validity'] = 0.0
                
                # Validate premium > 0
                if 'premium' in outputs:
                    premium = outputs['premium']
                    if premium > 0:
                        quality_metrics['premium_validity'] = 1.0
                    else:
                        quality_metrics['premium_validity'] = 0.0
            else:
                quality_metrics['trade_specification_quality'] = 1.0  # N/A for non-trade actions
                quality_metrics['liquidity_score_validity'] = 1.0
                quality_metrics['premium_validity'] = 1.0
            
            # Calculate overall quality
            overall_quality = sum(quality_metrics.values()) / len(quality_metrics)
            quality_metrics['overall_quality'] = overall_quality
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            return {'overall_quality': 0.0, 'validation_error': 1.0}
    
    def execute_task(self, task: AgentTask) -> AgentResult:
        """Execute options intelligence analysis with enhanced live data access"""
        start_time = time.time()
        
        try:
            inputs = task.inputs
            
            # Extract inputs - only ticker and signal needed now
            ticker = inputs['ticker']
            agent_zero_signal = inputs['agent_zero_signal']
            
            if not self.has_data_agent:
                raise ValueError("Data Ingestion Agent required but not available")
            
            self.logger.info(f"Processing options analysis for {ticker}: {agent_zero_signal}")
            
            # Record decision point
            self.record_decision_point(
                decision_type="options_analysis_start",
                context={'ticker': ticker, 'signal': agent_zero_signal},
                choice_made="begin_analysis",
                rationale=f"Agent Zero signal: {agent_zero_signal}"
            )
            
            # Use enhanced live data execution
            import asyncio
            enhanced_result = asyncio.run(self.execute_enhanced(ticker, agent_zero_signal))
            
            execution_time = time.time() - start_time
            
            # Validate performance target (<150ms)
            if execution_time * 1000 > 150:
                self.logger.warning(f"Performance target exceeded: {execution_time*1000:.1f}ms > 150ms")
            
            quality_metrics = self.validate_outputs({"analysis_result": enhanced_result})
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED if enhanced_result.get('status') == 'SUCCESS' else TaskStatus.FAILED,
                outputs={"options_analysis": enhanced_result},
                execution_time=execution_time,
                quality_metrics=quality_metrics
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Options analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Options analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )
    
    def _handle_non_directional_signal(self, signal: str, ticker: str) -> Dict[str, Any]:
        """Handle avoid/hold signals from Agent Zero"""
        if signal == 'avoid':
            return {
                'final_action': 'avoid',
                'reason': 'Agent Zero recommends avoiding - no options analysis needed',
                'ticker': ticker,
                'execution_quality': 'n/a'
            }
        elif signal == 'hold':
            return {
                'final_action': 'hold',
                'reason': 'Agent Zero neutral - no directional options trade recommended',
                'ticker': ticker,
                'execution_quality': 'n/a'
            }
    
    def _analyze_options_chain(self, options_chain: Dict, underlying_price: float, 
                             signal: str) -> Dict[str, Any]:
        """Comprehensive options chain analysis"""
        try:
            calls = options_chain.get('calls', [])
            puts = options_chain.get('puts', [])
            
            # Analyze each side
            call_analysis = self._analyze_option_side(calls, underlying_price, 'call')
            put_analysis = self._analyze_option_side(puts, underlying_price, 'put')
            
            # Assess overall environment
            liquidity_assessment = self._assess_options_liquidity(calls + puts)
            iv_environment = self._assess_iv_environment(calls + puts)
            
            return {
                'call_analysis': call_analysis,
                'put_analysis': put_analysis,
                'liquidity_assessment': liquidity_assessment,
                'iv_environment': iv_environment,
                'total_strikes_analyzed': len(calls) + len(puts)
            }
            
        except Exception as e:
            self.logger.error(f"Options chain analysis failed: {e}")
            return {'error': str(e)}
    
    def _analyze_option_side(self, options: List[Dict], underlying_price: float, 
                           option_type: str) -> List[Dict]:
        """Analyze calls or puts with metrics and scoring"""
        analyzed_options = []
        
        for option in options:
            try:
                # Extract basic data
                strike = float(option.get('strike', 0))
                bid = float(option.get('bid', 0))
                ask = float(option.get('ask', 0))
                volume = int(option.get('volume', 0))
                open_interest = int(option.get('open_interest', 0))
                
                # Calculate derived metrics
                mid_price = (bid + ask) / 2 if ask > bid else bid
                spread_pct = ((ask - bid) / mid_price) if mid_price > 0 else 1.0
                
                # Calculate moneyness
                if option_type == 'call':
                    moneyness = underlying_price / strike if strike > 0 else 0
                    intrinsic_value = max(0, underlying_price - strike)
                else:  # put
                    moneyness = strike / underlying_price if underlying_price > 0 else 0
                    intrinsic_value = max(0, strike - underlying_price)
                
                time_value = max(0, mid_price - intrinsic_value)
                
                # Estimate delta (simplified)
                delta = self._estimate_delta(underlying_price, strike, option_type)
                
                # Calculate liquidity score
                liquidity_score = self._calculate_option_liquidity_score(
                    volume, open_interest, spread_pct, mid_price
                )
                
                analyzed_option = {
                    'strike': strike,
                    'bid': bid,
                    'ask': ask,
                    'mid_price': mid_price,
                    'volume': volume,
                    'open_interest': open_interest,
                    'spread_pct': spread_pct,
                    'moneyness': moneyness,
                    'intrinsic_value': intrinsic_value,
                    'time_value': time_value,
                    'estimated_delta': delta,
                    'liquidity_score': liquidity_score,
                    'option_type': option_type,
                    'viable': self._is_option_viable(liquidity_score, spread_pct, volume)
                }
                
                analyzed_options.append(analyzed_option)
                
            except Exception as e:
                self.logger.warning(f"Failed to analyze option {option}: {e}")
                continue
        
        # Sort by liquidity score (best first)
        analyzed_options.sort(key=lambda x: x['liquidity_score'], reverse=True)
        
        return analyzed_options
    
    def _estimate_delta(self, underlying_price: float, strike: float, option_type: str) -> float:
        """Simplified delta estimation"""
        try:
            if option_type == 'call':
                # Call delta approximation
                if underlying_price >= strike:
                    # ITM call: delta increases as goes deeper ITM
                    excess = (underlying_price - strike) / strike
                    return min(0.95, 0.50 + excess * 0.3)
                else:
                    # OTM call: delta decreases as goes further OTM  
                    shortfall = (strike - underlying_price) / underlying_price
                    return max(0.05, 0.50 - shortfall * 0.3)
            else:
                # Put delta approximation (negative values)
                if strike >= underlying_price:
                    # ITM put: delta becomes more negative as goes deeper ITM
                    excess = (strike - underlying_price) / underlying_price
                    return max(-0.95, -0.50 - excess * 0.3)
                else:
                    # OTM put: delta approaches zero as goes further OTM
                    shortfall = (underlying_price - strike) / strike
                    return min(-0.05, -0.50 + shortfall * 0.3)
                    
        except Exception:
            # Fallback to neutral deltas
            return 0.50 if option_type == 'call' else -0.50
    
    def _calculate_option_liquidity_score(self, volume: int, open_interest: int, 
                                        spread_pct: float, mid_price: float) -> float:
        """Calculate comprehensive liquidity score"""
        try:
            # Volume component (30%)
            volume_score = min(1.0, volume / 100) * self.liquidity_weights['volume_component']
            
            # Open interest component (30%)
            oi_score = min(1.0, open_interest / 500) * self.liquidity_weights['open_interest_component']
            
            # Spread component (25%) - lower spread is better
            spread_score = max(0, (0.15 - spread_pct) / 0.15) * self.liquidity_weights['spread_component']
            
            # Price level component (15%) - higher premium generally more liquid
            price_score = min(1.0, mid_price / 5.0) * self.liquidity_weights['price_level_component']
            
            total_score = volume_score + oi_score + spread_score + price_score
            return max(0.0, min(1.0, total_score))
            
        except Exception:
            return 0.0
    
    def _is_option_viable(self, liquidity_score: float, spread_pct: float, volume: int) -> bool:
        """Determine if option meets minimum viability criteria"""
        return (
            liquidity_score >= self.min_liquidity_score and
            spread_pct <= self.max_bid_ask_spread_pct and
            volume >= self.min_volume
        )
    
    def _assess_options_liquidity(self, all_options: List[Dict]) -> Dict[str, Any]:
        """Assess overall options market liquidity"""
        try:
            if not all_options:
                return {'quality': 'poor', 'reason': 'No options data'}
            
            # Calculate average liquidity score
            liquidity_scores = [opt.get('liquidity_score', 0) for opt in all_options]
            avg_liquidity = np.mean(liquidity_scores) if liquidity_scores else 0
            
            # Count viable options
            viable_count = sum(1 for opt in all_options if opt.get('viable', False))
            viable_pct = viable_count / len(all_options) if all_options else 0
            
            # Classify quality
            if avg_liquidity > 0.6 and viable_pct > 0.7:
                quality = 'excellent'
            elif avg_liquidity > 0.4 and viable_pct > 0.5:
                quality = 'good'
            elif avg_liquidity > 0.2 and viable_pct > 0.3:
                quality = 'moderate'
            else:
                quality = 'poor'
            
            return {
                'quality': quality,
                'avg_liquidity_score': avg_liquidity,
                'viable_options_pct': viable_pct,
                'total_options': len(all_options),
                'viable_options': viable_count
            }
            
        except Exception as e:
            return {'quality': 'unknown', 'reason': f'Assessment failed: {e}'}
    
    def _assess_iv_environment(self, all_options: List[Dict]) -> Dict[str, Any]:
        """Assess implied volatility environment"""
        try:
            if not all_options:
                return {'environment': 'unknown', 'reason': 'No options data'}
            
            # Use time value as IV proxy
            time_values = [opt.get('time_value', 0) for opt in all_options if opt.get('time_value', 0) > 0]
            
            if not time_values:
                return {'environment': 'unknown', 'reason': 'No time value data'}
            
            avg_time_value = np.mean(time_values)
            
            # Simple IV classification
            if avg_time_value > 3.0:
                environment = 'high_iv'
                strategy_bias = 'sell_premium'
            elif avg_time_value < 1.0:
                environment = 'low_iv' 
                strategy_bias = 'buy_premium'
            else:
                environment = 'normal_iv'
                strategy_bias = 'neutral'
            
            return {
                'environment': environment,
                'avg_time_value': avg_time_value,
                'strategy_bias': strategy_bias
            }
            
        except Exception as e:
            return {'environment': 'unknown', 'reason': f'IV analysis failed: {e}'}
    
    def _select_optimal_option(self, options_analysis: Dict, signal: str, 
                             underlying_price: float) -> Dict[str, Any]:
        """Select optimal option based on Agent Zero signal"""
        try:
            if signal == 'buy_calls':
                return self._select_optimal_calls(options_analysis['call_analysis'], underlying_price)
            elif signal == 'buy_puts':
                return self._select_optimal_puts(options_analysis['put_analysis'], underlying_price)
            else:
                return {'action': 'hold', 'reason': f'No selection logic for signal: {signal}'}
                
        except Exception as e:
            return {'action': 'avoid', 'reason': f'Option selection failed: {e}'}
    
    def _select_optimal_calls(self, call_analysis: List[Dict], underlying_price: float) -> Dict[str, Any]:
        """Select optimal call option for bullish signal"""
        try:
            # Filter viable calls
            viable_calls = [call for call in call_analysis if call.get('viable', False)]
            
            if not viable_calls:
                return {'action': 'avoid', 'reason': 'No viable call options found'}
            
            # Score each viable call
            scored_calls = []
            target_delta_min, target_delta_max = self.delta_targets['bullish_calls']
            
            for call in viable_calls:
                delta = call['estimated_delta']
                liquidity = call['liquidity_score']
                moneyness = call['moneyness']
                
                # Delta score (prefer target range)
                if target_delta_min <= delta <= target_delta_max:
                    delta_score = 1.0
                else:
                    # Penalty for being outside target range
                    if delta < target_delta_min:
                        delta_score = max(0, delta / target_delta_min)
                    else:
                        delta_score = max(0, target_delta_max / delta)
                
                # Moneyness score (prefer ATM to slight OTM for calls)
                if 0.95 <= moneyness <= 1.05:  # ATM
                    moneyness_score = 1.0
                elif 0.90 <= moneyness <= 1.10:  # Close to ATM
                    moneyness_score = 0.8
                else:
                    moneyness_score = max(0.3, 1.0 - abs(moneyness - 1.0))
                
                # Composite score
                composite_score = (
                    delta_score * 0.40 +
                    liquidity * 0.35 +
                    moneyness_score * 0.25
                )
                
                call['composite_score'] = composite_score
                scored_calls.append(call)
            
            # Select best call
            best_call = max(scored_calls, key=lambda x: x['composite_score'])
            
            return {
                'action': 'buy_calls',
                'selected_option': best_call,
                'alternatives_considered': len(scored_calls),
                'selection_score': best_call['composite_score']
            }
            
        except Exception as e:
            return {'action': 'avoid', 'reason': f'Call selection failed: {e}'}
    
    def _select_optimal_puts(self, put_analysis: List[Dict], underlying_price: float) -> Dict[str, Any]:
        """Select optimal put option for bearish signal"""
        try:
            # Filter viable puts
            viable_puts = [put for put in put_analysis if put.get('viable', False)]
            
            if not viable_puts:
                return {'action': 'avoid', 'reason': 'No viable put options found'}
            
            # Score each viable put
            scored_puts = []
            target_delta_min, target_delta_max = self.delta_targets['bearish_puts']
            
            for put in viable_puts:
                delta = put['estimated_delta']  # Negative for puts
                liquidity = put['liquidity_score']
                moneyness = put['moneyness']
                
                # Delta score (prefer target range - remember deltas are negative)
                if target_delta_min <= delta <= target_delta_max:
                    delta_score = 1.0
                else:
                    # Penalty for being outside target range
                    if delta > target_delta_max:  # Less negative than desired
                        delta_score = max(0, abs(delta) / abs(target_delta_max))
                    else:  # More negative than desired
                        delta_score = max(0, abs(target_delta_min) / abs(delta))
                
                # Moneyness score (prefer ATM to slight OTM for puts)
                if 0.95 <= moneyness <= 1.05:  # ATM
                    moneyness_score = 1.0
                elif 0.90 <= moneyness <= 1.10:  # Close to ATM
                    moneyness_score = 0.8
                else:
                    moneyness_score = max(0.3, 1.0 - abs(moneyness - 1.0))
                
                # Composite score
                composite_score = (
                    delta_score * 0.40 +
                    liquidity * 0.35 +
                    moneyness_score * 0.25
                )
                
                put['composite_score'] = composite_score
                scored_puts.append(put)
            
            # Select best put
            best_put = max(scored_puts, key=lambda x: x['composite_score'])
            
            return {
                'action': 'buy_puts',
                'selected_option': best_put,
                'alternatives_considered': len(scored_puts),
                'selection_score': best_put['composite_score']
            }
            
        except Exception as e:
            return {'action': 'avoid', 'reason': f'Put selection failed: {e}'}
    
    def _generate_final_recommendation(self, agent_zero_signal: str, optimal_selection: Dict, 
                                     options_analysis: Dict) -> Dict[str, Any]:
        """Generate final options recommendation"""
        try:
            # Check if selection failed
            if optimal_selection.get('action') == 'avoid':
                return {
                    'final_action': 'avoid',
                    'reason': optimal_selection.get('reason', 'Option selection failed'),
                    'execution_quality': 'poor'
                }
            
            # Check overall liquidity quality
            liquidity_quality = options_analysis.get('liquidity_assessment', {}).get('quality', 'poor')
            
            if liquidity_quality == 'poor':
                return {
                    'final_action': 'avoid',
                    'reason': 'Poor overall options liquidity - execution risk too high',
                    'execution_quality': 'poor'
                }
            
            # Generate trade recommendation
            selected_option = optimal_selection['selected_option']
            
            # Calculate risk metrics
            risk_metrics = self._calculate_risk_metrics(selected_option)
            
            # Assess execution quality
            execution_quality = self._assess_execution_quality(selected_option, liquidity_quality)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(agent_zero_signal, selected_option, optimal_selection)
            
            return {
                'final_action': optimal_selection['action'],
                'ticker': selected_option.get('ticker', 'unknown'),
                'strike': selected_option['strike'],
                'option_type': selected_option['option_type'],
                'premium': selected_option['mid_price'],
                'bid': selected_option['bid'],
                'ask': selected_option['ask'],
                'estimated_delta': selected_option['estimated_delta'],
                'liquidity_score': selected_option['liquidity_score'],
                'spread_pct': selected_option['spread_pct'],
                'selection_score': optimal_selection['selection_score'],
                'risk_metrics': risk_metrics,
                'execution_quality': execution_quality,
                'alternatives_considered': optimal_selection['alternatives_considered'],
                'reasoning': reasoning,
                'options_liquidity_quality': liquidity_quality
            }
            
        except Exception as e:
            return {
                'final_action': 'avoid',
                'reason': f'Recommendation generation failed: {e}',
                'execution_quality': 'poor'
            }
    
    def _calculate_risk_metrics(self, option: Dict) -> Dict[str, Any]:
        """Calculate risk metrics for selected option"""
        try:
            return {
                'estimated_delta': option['estimated_delta'],
                'premium_at_risk': option['mid_price'],
                'spread_cost_pct': option['spread_pct'],
                'liquidity_risk': 1.0 - option['liquidity_score'],  # Higher score = lower risk
                'moneyness': option['moneyness'],
                'time_value_exposure': option.get('time_value', 0)
            }
        except Exception as e:
            return {'calculation_error': str(e)}
    
    def _assess_execution_quality(self, option: Dict, overall_liquidity: str) -> str:
        """Assess overall execution quality"""
        try:
            liquidity_score = option['liquidity_score']
            spread_pct = option['spread_pct']
            
            # Quality scoring
            if liquidity_score > 0.7 and spread_pct < 0.08 and overall_liquidity == 'excellent':
                return 'excellent'
            elif liquidity_score > 0.5 and spread_pct < 0.12 and overall_liquidity in ['excellent', 'good']:
                return 'good'
            elif liquidity_score > 0.3 and spread_pct < 0.15:
                return 'moderate'
            else:
                return 'poor'
                
        except Exception:
            return 'unknown'
    
    def _generate_reasoning(self, signal: str, option: Dict, selection: Dict) -> str:
        """Generate human-readable reasoning for the selection"""
        try:
            option_type = "call" if signal == 'buy_calls' else "put"
            strike = option['strike']
            delta = option['estimated_delta']
            liquidity = option['liquidity_score']
            score = selection['selection_score']
            
            reasoning = (
                f"Agent Zero {signal} signal converted to {option_type} option selection. "
                f"Selected ${strike} strike with delta {delta:.2f}, "
                f"liquidity score {liquidity:.2f}, overall selection score {score:.2f}. "
                f"Considered {selection['alternatives_considered']} viable alternatives."
            )
            
            return reasoning
            
        except Exception:
            return "Option selection completed with standard criteria."

# Create task factory function for easy integration
def create_agent_zero_options_task(ticker: str, options_chain: Dict, 
                                 underlying_price: float, agent_zero_signal: str,
                                 correlation_id: str = None) -> AgentTask:
    """Factory function to create Agent Zero options analysis task"""
    
    task_id = f"AZ-OPT-{ticker}-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    return AgentTask(
        task_id=task_id,
        task_type="agent_zero_options_analysis",
        agent_type="AgentZeroOptionsAgent",
        priority=TaskPriority.CRITICAL,
        inputs={
            'ticker': ticker,
            'options_chain': options_chain,
            'underlying_price': underlying_price,
            'agent_zero_signal': agent_zero_signal
        },
        workflow_file="agent_zero_options_workflow.md",
        quality_standards="options_execution_standards.md",
        performance_targets={
            'max_execution_time': 150,  # ms
            'strike_selection_accuracy': 0.95,
            'liquidity_assessment_precision': 0.99
        },
        dependencies=[],
        training_data_tags=['options_selection', 'strike_optimization', 'execution_quality'],
        timestamp=datetime.now(),
        correlation_id=correlation_id or f"agent_zero_options_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )

if __name__ == "__main__":
    # Example usage
    agent = AgentZeroOptionsAgent()
    print(f"Agent Zero Options Agent ready: {agent.agent_id}")
