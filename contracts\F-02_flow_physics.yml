task_id: F-02
name: Flow Physics + CSID Integrator
version: 1.0.0
description: |
  Advanced flow physics analysis with velocity, acceleration, jerk calculations.
  Provides institutional flow detection and regime classification with mathematical rigor.
  
mathematical_foundation:
  - Flow velocity: d(flow_value)/dt using central difference approximation
  - Flow acceleration: d(flow_value)/dt with 1e-10 precision tolerance
  - Flow jerk: d(flow_value)/dt for regime change detection
  - Institutional activity: Threshold-based pattern recognition
  - Regime classification: Multi-derivative pattern analysis

inputs:
  required:
    - price_5m_parquet  # 5-minute bars with timestamp, flow_value columns
    - ticker            # symbol for analysis
  optional:
    - lookback_periods  # default: 50 periods
    - quality_threshold # default: 0.6

outputs:
  files:
    - path: flow_phys/{{date}}/{{ticker}}_flowphysics.json
      must_exist: true
      schema:
        flow_velocity: float          # Raw velocity calculation
        flow_acceleration: float      # Raw acceleration calculation  
        flow_jerk: float             # Raw jerk calculation
        current_regime: string       # ACCUMULATION|DISTRIBUTION|MOMENTUM_SHIFT|REGIME_CHANGE|STEADY_FLOW
        institutional_activity: boolean # Institutional flow detected
        institutional_direction: string # accumulation|distribution|neutral
        quality_score: float         # >0.6 required for success
        regime_confidence: float     # Confidence in regime classification
        
validation_requirements:
  mathematical_precision: 1e-10   # Flow physics calculations
  derivative_continuity: true     # Velocity->acceleration->jerk consistency
  bounds_checking: true          # All derivatives finite and bounded
  error_propagation_max: 1e-8    # Maximum cumulative error

success_criteria:
  performance:
    max_runtime_ms: 120          # Stricter than F-01 due to complexity
    memory_limit_mb: 75          # Higher limit for derivative calculations
  quality:
    quality_score_min: 0.6       # Minimum quality threshold
    institutional_detection_accuracy: 0.8
    regime_classification_confidence: 0.7
  testing:
    code_coverage_min: 0.95      # Comprehensive testing required
    unit_tests_pass: true
    integration_tests_pass: true
    mathematical_precision_tests: true

dependencies:
  - data_acquisition             # Phase 1
  - mathematical_validation      # Phase 2

training_data_capture:
  decision_points:
    - derivative_calculation_methods
    - regime_classification_logic
    - institutional_threshold_applications
    - alert_condition_evaluations
  performance_metrics:
    - calculation_precision_maintenance
    - execution_efficiency_optimization
    - error_recovery_strategies

agent_zero_integration:
  learning_objectives:
    - Flow physics mathematical relationships
    - Regime transition pattern recognition
    - Institutional vs retail flow characteristics
    - Multi-derivative signal fusion techniques
  training_features:
    - Velocity-acceleration-jerk relationships
    - Regime stability patterns
    - Institutional activity signatures
    - Quality score optimization factors