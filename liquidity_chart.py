#!/usr/bin/env python3
"""
Liquidity Profile Chart with Greek Anomalies
Shows WHERE THE MONEY IS - institutional liquidity pools + anomaly detection
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class LiquidityProfileChart:
    """
    Professional liquidity chart showing:
    1. Liquidity Profile (where institutional money sits)
    2. Greek Anomalies overlay
    3. Bid/Ask liquidity walls
    4. Smart money flow zones
    5. Market maker positioning
    """
    
    def __init__(self):
        self.colors = {
            'background': '#1a1a1a',     # Dark
            'text': '#ffffff',           # White
            'grid': '#333333',           # Dark gray
            'liquidity_high': '#00C851', # Green - Deep liquidity
            'liquidity_medium': '#FFBB33', # Orange - Medium liquidity
            'liquidity_low': '#FF4444',  # Red - Thin liquidity
            'greek_anomaly': '#E91E63',  # Pink - Anomaly alerts
            'institutional': '#9C27B0',  # Purple - Smart money
            'market_maker': '#00BCD4',   # Cyan - MM positioning
            'support': '#4CAF50',        # Support levels
            'resistance': '#F44336'      # Resistance levels
        }
    
    def generate_liquidity_profile_data(self, ticker: str) -> Dict[str, Any]:
        """Generate realistic liquidity profile data showing where money sits"""
        # Simulate current market conditions
        current_price = 450.0 if ticker == 'SPY' else 185.0 if ticker == 'AAPL' else 380.0
        
        # Price levels around current (institutional focus zones)
        price_levels = np.linspace(current_price * 0.95, current_price * 1.05, 50)
        
        # Liquidity depth at each level (where institutional money waits)
        # Higher values = more institutional money at that level
        liquidity_depth = []
        support_resistance = []
        
        for price in price_levels:
            distance_from_current = abs(price - current_price) / current_price
            
            # Model institutional behavior:
            # 1. Heavy liquidity at round numbers
            # 2. Support/resistance from previous trading
            # 3. Options strikes create liquidity walls
            
            base_liquidity = np.random.normal(100, 30)
            
            # Round number effect (institutional preference)
            if price % 5 == 0:  # $5 increments
                base_liquidity *= 2.5
            if price % 10 == 0:  # $10 increments  
                base_liquidity *= 1.8
            
            # Distance decay (more liquidity near current price)
            distance_factor = np.exp(-distance_from_current * 10)
            base_liquidity *= (0.3 + 0.7 * distance_factor)
            
            # Add institutional clustering
            if np.random.random() < 0.15:  # 15% chance of institutional cluster
                base_liquidity *= np.random.uniform(3, 8)  # Big money cluster
            
            liquidity_depth.append(max(0, base_liquidity))
            
            # Identify support/resistance based on liquidity concentration
            if base_liquidity > 300:
                if price < current_price:
                    support_resistance.append(('support', price, base_liquidity))
                else:
                    support_resistance.append(('resistance', price, base_liquidity))
        
        # Greek anomalies (where options activity creates unusual conditions)
        greek_anomalies = []
        for _ in range(np.random.randint(3, 8)):  # 3-8 anomalies
            anomaly_price = np.random.choice(price_levels)
            anomaly_type = np.random.choice(['gamma_squeeze', 'vanna_flip', 'charm_decay', 'delta_hedge'])
            anomaly_strength = np.random.uniform(0.6, 1.0)
            
            greek_anomalies.append({
                'price': anomaly_price,
                'type': anomaly_type,
                'strength': anomaly_strength,
                'impact': 'bullish' if anomaly_strength > 0.8 else 'bearish' if anomaly_strength < 0.7 else 'neutral'
            })
        
        # Market maker positioning (where they defend levels)
        mm_levels = []
        for _ in range(np.random.randint(2, 5)):  # 2-5 MM levels
            mm_price = current_price + np.random.uniform(-current_price*0.02, current_price*0.02)
            mm_size = np.random.uniform(500, 2000)  # MM position size
            mm_type = np.random.choice(['defense', 'accumulation', 'distribution'])
            
            mm_levels.append({
                'price': mm_price,
                'size': mm_size,
                'type': mm_type
            })
        
        return {
            'ticker': ticker,
            'current_price': current_price,
            'price_levels': price_levels,
            'liquidity_depth': liquidity_depth,
            'support_resistance': support_resistance,
            'greek_anomalies': greek_anomalies,
            'market_maker_levels': mm_levels,
            'max_liquidity': max(liquidity_depth),
            'avg_liquidity': np.mean(liquidity_depth)
        }
    
    def create_liquidity_profile_chart(self, ticker: str, data: Dict[str, Any], timestamp: str) -> Optional[str]:
        """Create the main liquidity profile chart showing where the money is"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 10), 
                                          gridspec_kw={'width_ratios': [3, 1]})
            fig.patch.set_facecolor(self.colors['background'])
            
            liquidity_data = self.generate_liquidity_profile_data(ticker)
            
            # LEFT PANEL: Price vs Time with Liquidity Overlay
            # Simulate price action over time
            time_points = pd.date_range(end=datetime.now(), periods=100, freq='5min')
            price_action = []
            current = liquidity_data['current_price']
            
            for i in range(100):
                # Random walk with mean reversion
                change = np.random.normal(0, current * 0.002)  # 0.2% moves
                current += change
                # Mean revert toward original price
                current += (liquidity_data['current_price'] - current) * 0.02
                price_action.append(current)
            
            # Plot price action
            ax1.plot(time_points, price_action, color=self.colors['text'], linewidth=2, alpha=0.8)
            
            # Overlay liquidity levels as horizontal zones
            for i, (price, liquidity) in enumerate(zip(liquidity_data['price_levels'], 
                                                     liquidity_data['liquidity_depth'])):
                # Normalize liquidity to alpha transparency
                alpha = min(0.8, liquidity / liquidity_data['max_liquidity'])
                
                # Color based on liquidity depth
                if liquidity > liquidity_data['avg_liquidity'] * 2:
                    color = self.colors['liquidity_high']
                elif liquidity > liquidity_data['avg_liquidity']:
                    color = self.colors['liquidity_medium'] 
                else:
                    color = self.colors['liquidity_low']
                
                # Draw liquidity zone
                ax1.axhline(y=price, color=color, alpha=alpha, linewidth=3)
            
            # Mark support/resistance levels
            for level_type, price, strength in liquidity_data['support_resistance']:
                color = self.colors['support'] if level_type == 'support' else self.colors['resistance']
                ax1.axhline(y=price, color=color, linewidth=4, alpha=0.8)
                ax1.text(time_points[5], price, f'{level_type.upper()}: ${price:.2f}', 
                        color=color, fontweight='bold', va='bottom')
            
            # Mark Greek anomalies
            for anomaly in liquidity_data['greek_anomalies']:
                ax1.scatter(time_points[np.random.randint(20, 80)], anomaly['price'], 
                           s=200, c=self.colors['greek_anomaly'], alpha=0.8, 
                           marker='*', edgecolor='white', linewidth=2)
                ax1.text(time_points[np.random.randint(20, 80)], anomaly['price'], 
                        f"{anomaly['type']}\n{anomaly['strength']:.1f}", 
                        color=self.colors['greek_anomaly'], fontsize=8, ha='center')
            
            # Mark Market Maker levels
            for mm in liquidity_data['market_maker_levels']:
                ax1.axhline(y=mm['price'], color=self.colors['market_maker'], 
                           linewidth=2, linestyle='--', alpha=0.7)
                ax1.text(time_points[-10], mm['price'], f"MM {mm['type']}", 
                        color=self.colors['market_maker'], fontsize=8)
            
            # Current price line
            ax1.axhline(y=liquidity_data['current_price'], color='white', 
                       linewidth=3, label=f'Current: ${liquidity_data["current_price"]:.2f}')
            
            ax1.set_title(f'{ticker} - LIQUIDITY PROFILE: Where The Money Is', 
                         color=self.colors['text'], fontsize=16, fontweight='bold')
            ax1.set_xlabel('Time', color=self.colors['text'])
            ax1.set_ylabel('Price ($)', color=self.colors['text'])
            ax1.grid(True, alpha=0.3, color=self.colors['grid'])
            ax1.legend()
            
            # RIGHT PANEL: Liquidity Depth Profile (Horizontal Bar Chart)
            liquidity_values = liquidity_data['liquidity_depth']
            price_levels = liquidity_data['price_levels']
            
            # Create color mapping for liquidity bars
            colors = []
            for liquidity in liquidity_values:
                if liquidity > liquidity_data['avg_liquidity'] * 2:
                    colors.append(self.colors['liquidity_high'])
                elif liquidity > liquidity_data['avg_liquidity']:
                    colors.append(self.colors['liquidity_medium'])
                else:
                    colors.append(self.colors['liquidity_low'])
            
            # Horizontal bar chart showing liquidity at each price level
            ax2.barh(price_levels, liquidity_values, color=colors, alpha=0.8, height=1.0)
            
            # Highlight current price
            current_idx = np.argmin(np.abs(np.array(price_levels) - liquidity_data['current_price']))
            ax2.barh(price_levels[current_idx], liquidity_values[current_idx], 
                    color='white', alpha=0.9, height=1.5)
            
            ax2.set_title('Liquidity Depth\n(Money Concentration)', 
                         color=self.colors['text'], fontsize=14, fontweight='bold')
            ax2.set_xlabel('Liquidity Depth', color=self.colors['text'])
            ax2.set_ylabel('Price ($)', color=self.colors['text'])
            ax2.grid(True, alpha=0.3, color=self.colors['grid'], axis='x')
            
            # Add liquidity statistics
            stats_text = f"""LIQUIDITY STATISTICS:
            
Max Depth: {liquidity_data['max_liquidity']:.0f}
Avg Depth: {liquidity_data['avg_liquidity']:.0f}
Support Levels: {len([x for x in liquidity_data['support_resistance'] if x[0] == 'support'])}
Resistance Levels: {len([x for x in liquidity_data['support_resistance'] if x[0] == 'resistance'])}
Greek Anomalies: {len(liquidity_data['greek_anomalies'])}
MM Levels: {len(liquidity_data['market_maker_levels'])}"""
            
            ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                    color=self.colors['text'], fontsize=9, va='top',
                    bbox=dict(boxstyle='round', facecolor=self.colors['background'], 
                             alpha=0.8, edgecolor=self.colors['grid']))
            
            # Styling
            for ax in [ax1, ax2]:
                ax.tick_params(colors=self.colors['text'])
                ax.set_facecolor(self.colors['background'])
            
            plt.tight_layout()
            
            # Save chart
            output_dir = Path("visual_output")
            output_dir.mkdir(exist_ok=True)
            filename = f"{ticker}_liquidity_profile_{timestamp}.png"
            filepath = output_dir / filename
            
            plt.savefig(filepath, dpi=150, facecolor=self.colors['background'],
                       bbox_inches='tight')
            plt.close()
            
            return str(filepath)
            
        except Exception as e:
            print(f"Liquidity profile chart error: {e}")
            return None

def add_liquidity_chart_to_visualizer():
    """Add the liquidity chart to the existing trading visualizer"""
    # Import the main visualizer
    from trading_visualizer import TradingVisualizer
    
    # Add liquidity chart method
    def generate_liquidity_charts(self, ticker: str, analysis_data: Dict[str, Any]) -> List[str]:
        """Generate liquidity profile charts"""
        chart_files = []
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # Create liquidity profile chart
            liquidity_chart = LiquidityProfileChart()
            chart_path = liquidity_chart.create_liquidity_profile_chart(ticker, analysis_data, timestamp)
            
            if chart_path:
                chart_files.append(chart_path)
                print(f"  Generated liquidity profile chart: {Path(chart_path).name}")
            
            return chart_files
            
        except Exception as e:
            print(f"Liquidity chart generation error: {e}")
            return []
    
    # Add method to TradingVisualizer class
    TradingVisualizer.generate_liquidity_charts = generate_liquidity_charts
    
    return TradingVisualizer

def test_liquidity_chart():
    """Test the liquidity profile chart"""
    print("Testing Liquidity Profile Chart...")
    
    # Create sample data
    sample_data = {
        'agent_zero_decision': {
            'final_decision': 'BULLISH',
            'confidence': 87.3,
            'strength': 'STRONG'
        }
    }
    
    # Generate chart
    liquidity_chart = LiquidityProfileChart()
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chart_path = liquidity_chart.create_liquidity_profile_chart("SPY", sample_data, timestamp)
    
    if chart_path:
        print(f"SUCCESS: Liquidity chart generated: {chart_path}")
        
        # Auto-open
        import webbrowser
        try:
            webbrowser.open(f"file://{Path(chart_path).absolute()}")
            print("Opening chart in browser...")
        except:
            print(f"Manual open: {chart_path}")
    else:
        print("FAILED: No chart generated")

if __name__ == "__main__":
    test_liquidity_chart()
