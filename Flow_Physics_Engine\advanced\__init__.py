"""Enhanced Flow Physics Package

This package provides advanced flow analysis using physics-based models:
- Velocity analysis (first derivative of flow)
- Acceleration analysis (second derivative of flow)
- Jerk analysis (third derivative of flow)
- Integrated flow physics for institutional detection

The physics-based approach helps identify:
1. Institutional flow patterns through velocity changes
2. Momentum shifts through acceleration patterns
3. Regime changes through jerk (sudden acceleration changes)
4. Hidden accumulation/distribution patterns
"""

from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)

# Version info
__version__ = "1.0.0"
__author__ = "Liquidity Factor Script Team"

# Physics constants for flow analysis - Market-adaptive thresholds
FLOW_PHYSICS_CONSTANTS = {
    # Velocity thresholds (flow rate of change)
    'MIN_VELOCITY_THRESHOLD': 0.1,
    'INSTITUTIONAL_VELOCITY_THRESHOLD': 0.25,  # Market-adaptive: lower threshold for real detection
    'EXTREME_VELOCITY_THRESHOLD': 1.0,

    # Acceleration thresholds (velocity rate of change)
    'MIN_ACCELERATION_THRESHOLD': 0.05,
    'INSTITUTIONAL_ACCELERATION_THRESHOLD': 0.2,
    'EXTREME_ACCELERATION_THRESHOLD': 0.35,  # Market-adaptive: more sensitive threshold

    # Jerk thresholds (acceleration rate of change)
    'MIN_JERK_THRESHOLD': 0.02,
    'REGIME_CHANGE_JERK_THRESHOLD': 0.1,
    'EXTREME_JERK_THRESHOLD': 0.3,

    # Time constants
    'MIN_SAMPLE_PERIOD': 60,  # seconds
    'DEFAULT_LOOKBACK_PERIODS': 20,
    'SMOOTHING_FACTOR': 0.3
}

# Flow regimes based on physics
FLOW_REGIMES = {
    'ACCUMULATION': {
        'velocity': 'positive',
        'acceleration': 'positive',
        'description': 'Institutional accumulation phase'
    },
    'DISTRIBUTION': {
        'velocity': 'negative',
        'acceleration': 'negative',
        'description': 'Institutional distribution phase'
    },
    'MOMENTUM_SHIFT': {
        'velocity': 'any',
        'acceleration': 'changing_sign',
        'description': 'Flow momentum changing direction'
    },
    'REGIME_CHANGE': {
        'jerk': 'extreme',
        'description': 'Sudden change in flow dynamics'
    },
    'STEADY_FLOW': {
        'velocity': 'constant',
        'acceleration': 'near_zero',
        'description': 'Consistent flow pattern'
    }
}

# Import components when available
__all__ = [
    'FLOW_PHYSICS_CONSTANTS',
    'FLOW_REGIMES',
    'FlowPhysicsIntegrator',
    'AdvancedVelocityAnalyzer',
    'AdvancedAccelerationAnalyzer',
    'FlowJerkAnalyzer'
]

# Lazy imports to avoid circular dependencies
_COMPONENTS = {
    'FlowPhysicsIntegrator': 'flow_physics_integrator',
    'AdvancedVelocityAnalyzer': 'advanced_velocity_analyzer',
    'AdvancedAccelerationAnalyzer': 'advanced_acceleration_analyzer',
    'FlowJerkAnalyzer': 'flow_jerk_analyzer'
}

def __getattr__(name):
    """Lazy loading of components."""
    if name in _COMPONENTS:
        try:
            module_name = _COMPONENTS[name]
            # Import from the current package using relative import
            from . import flow_physics_integrator, advanced_velocity_analyzer, advanced_acceleration_analyzer, flow_jerk_analyzer

            module_map = {
                'flow_physics_integrator': flow_physics_integrator,
                'advanced_velocity_analyzer': advanced_velocity_analyzer,
                'advanced_acceleration_analyzer': advanced_acceleration_analyzer,
                'flow_jerk_analyzer': flow_jerk_analyzer
            }

            if module_name in module_map:
                return getattr(module_map[module_name], name)
            else:
                logger.warning(f"Module {module_name} not found in module map")
                return None

        except ImportError as e:
            logger.warning(f"Could not import {name}: {e}")
            return None
    raise AttributeError(f"module {__name__!r} has no attribute {name!r}")


def get_flow_physics_config(custom_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Get flow physics configuration with optional custom overrides.

    Args:
        custom_config: Optional custom configuration to override defaults

    Returns:
        Complete configuration dictionary
    """
    config = {
        'constants': FLOW_PHYSICS_CONSTANTS.copy(),
        'regimes': FLOW_REGIMES.copy(),
        'analysis': {
            'enable_velocity': True,
            'enable_acceleration': True,
            'enable_jerk': True,
            'multi_timeframe': True,
            'timeframes': ['1m', '5m', '15m', '1h'],
            'lookback_periods': {
                '1m': 60,
                '5m': 48,
                '15m': 32,
                '1h': 24
            }
        },
        'detection': {
            'institutional_flow': True,
            'regime_changes': True,
            'momentum_shifts': True,
            'hidden_accumulation': True
        },
        'visualization': {
            'plot_velocity': True,
            'plot_acceleration': True,
            'plot_jerk': True,
            'plot_regimes': True,
            'color_scheme': 'physics'
        }
    }

    if custom_config:
        # Deep merge custom config
        _deep_merge(config, custom_config)

    return config


def _deep_merge(base: dict, update: dict) -> dict:
    """Deep merge two dictionaries."""
    for key, value in update.items():
        if key in base and isinstance(base[key], dict) and isinstance(value, dict):
            _deep_merge(base[key], value)
        else:
            base[key] = value
    return base


def validate_flow_data(flow_data: Dict[str, Any]) -> bool:
    """Validate flow data for physics analysis.

    Args:
        flow_data: Flow data dictionary

    Returns:
        True if data is valid for physics analysis
    """
    required_fields = ['timestamp', 'flow_value']

    if not isinstance(flow_data, dict):
        return False

    # Check required fields
    for field in required_fields:
        if field not in flow_data:
            logger.error(f"Missing required field: {field}")
            return False

    # Validate flow value is numeric
    try:
        float(flow_data['flow_value'])
    except (TypeError, ValueError):
        logger.error("Flow value must be numeric")
        return False

    return True


def calculate_flow_derivative(flow_series: List[float],
                            time_series: List[float],
                            order: int = 1) -> List[float]:
    """Calculate the nth derivative of flow.

    Args:
        flow_series: List of flow values
        time_series: List of timestamps (in seconds)
        order: Derivative order (1=velocity, 2=acceleration, 3=jerk)

    Returns:
        List of derivative values
    """
    import numpy as np

    if len(flow_series) < order + 1:
        return []

    result = np.array(flow_series, dtype=float)
    time_array = np.array(time_series, dtype=float)

    for _ in range(order):
        # Calculate differences
        flow_diff = np.diff(result)
        time_diff = np.diff(time_array[:len(result)])

        # Avoid division by zero
        time_diff[time_diff == 0] = 1e-6

        # Calculate derivative
        result = flow_diff / time_diff
        time_array = time_array[1:len(result)+1]

    return result.tolist()
