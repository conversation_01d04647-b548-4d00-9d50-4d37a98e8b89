[tool.ruff]
target-version = "py39"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]
"test_*.py" = ["B011"]

[tool.ruff.isort]
known-first-party = ["agents", "engine", "analyzers", "api", "utils"]

[tool.pylint.messages_control]
disable = [
    "missing-docstring",
    "too-few-public-methods",
    "import-error",
]

[tool.pylint.design]
min-public-methods = 1
max-attributes = 10
max-locals = 20
