# Multi-Ticker Orchestrator Documentation

## Overview
The `multi_orchestrator.py` script executes the CORE pipeline for multiple tickers simultaneously with mathematical risk management controls.

## Mathematical Foundation

### Risk Management Formulas
- **Per-trade Risk**: `Account_Equity  0.01` (Kelly Criterion 1% rule)
- **Daily Loss Cap**: `Account_Equity  0.03` (3% maximum daily exposure)
- **Position Size**: `Risk_Amount  (Entry_Price - Stop_Loss)`
- **Stop Loss**: `Entry_Price  0.95` (5% stop loss default)

### Statistical Controls
- Input validation with array length verification
- Price validation (positive values, logical target > entry)
- Risk state persistence across executions
- Timeout protection (300s per ticker)

## Usage

### Command Line Interface
```bash
python multi_orchestrator.py \
    --tickers ${TICKER},TSLA,NVDA \
    --option_prices 1.50,2.10,3.25 \
    --target_prices 5.00,6.00,10.40 \
    --source mcp-http \
    --account_equity 25000
```

### Environment Setup
```bash
export ACCOUNT_EQUITY=25000
python multi_orchestrator.py --tickers ${TICKER},TSLA --option_prices 150.0,200.0 --target_prices 160.0,220.0
```

### Batch Execution Scripts
- **Windows**: `run_multi_batch.bat`
- **Unix/Linux**: `run_multi_batch.sh`

## Architecture

### Core Components

#### RiskManager Class
- Implements mathematical risk controls
- Tracks daily P&L in `risk_state/daily_loss.json`
- Calculates position sizing using risk formulas
- Enforces daily trading limits

#### MultiTickerOrchestrator Class
- Manages batch execution workflow
- Validates input parameters mathematically
- Executes subprocess isolation for each ticker
- Generates structured execution reports

### Data Flow
1. **Input Validation**  Array length, price validation
2. **Risk Assessment**  Daily loss check, position sizing
3. **Subprocess Execution**  Isolated ticker analysis
4. **Result Aggregation**  Batch summary generation
5. **Report Generation**  JSON output with statistics

## Risk Controls

### Daily Risk Management
- Tracks cumulative daily losses
- Prevents new trades if daily cap exceeded
- Persists risk state across script executions

### Position Sizing Algorithm
```python
def calculate_position_size(entry_price, stop_loss):
    risk_per_share = entry_price - stop_loss
    shares = int(per_trade_risk / risk_per_share)
    return max(1, shares)  # Minimum 1 share
```

### Input Validation
- Array length consistency check
- Positive price validation
- Empty input detection
- Logical price relationship verification

## Output Structure

### Batch Report Format
```json
{
  "success": true,
  "total_tickers": 3,
  "successful_trades": 2,
  "skipped_trades": 0,
  "failed_trades": 1,
  "batch_execution_time": 45.3,
  "results": [...]
}
```

### Individual Ticker Results
```json
{
  "ticker": "${TICKER}",
  "success": true,
  "execution_time": 12.5,
  "stdout": "Analysis output...",
  "stderr": "",
  "return_code": 0
}
```

## Error Handling

### Subprocess Failures
- Captures stdout/stderr from failed executions
- Continues batch processing after individual failures
- Reports detailed error information

### Timeout Protection
- 300-second timeout per ticker analysis
- Graceful handling of stuck processes
- Resource cleanup on timeout

### Risk State Corruption
- Creates risk_state directory if missing
- Initializes empty loss tracking on file corruption
- Atomic file updates for consistency

## Testing

### Test Suite Coverage
- Mathematical formula validation
- Position sizing edge cases
- Input validation scenarios
- Subprocess execution mocking
- CLI argument parsing

### Execution
```bash
python test_multi_orchestrator.py
```

### Expected Results
- 10 test cases
- 100% success rate
- Mathematical accuracy validation

## Performance Considerations

### Subprocess Isolation
- Each ticker runs in separate process
- Prevents cross-contamination of analysis
- Enables parallel execution potential

### Memory Management
- Subprocess stdout/stderr capture
- Temporary file cleanup
- Risk state file size management

### Execution Time
- Typical: 15-30 seconds per ticker
- Timeout: 300 seconds maximum
- Batch overhead: <5 seconds

## AI Agent Training Notes

### Modular Design
- Clear separation of risk management and execution logic
- Standardized input/output formats
- Comprehensive error handling patterns

### Data Flow Documentation
- Mathematical formulas clearly documented
- Risk calculations with statistical basis
- Input validation with edge case coverage

### Extension Points
- Risk management formulas configurable
- Position sizing algorithms pluggable
- Data source abstraction layer

### Monitoring Integration
- Structured JSON logging
- Performance metrics capture
- Error categorization and reporting

## Dependencies
- Standard library only (no external packages)
- Compatible with Python 3.7+
- Cross-platform execution support

## Configuration
- Environment variable support (`ACCOUNT_EQUITY`)
- CLI argument override capability
- Risk parameter modification via class initialization
