#!/usr/bin/env python3
"""
Unit Tests for Agent Zero Core Decision Processor
Validates mathematical precision and decision logic.
"""

import pytest
from dataclasses import replace
from datetime import datetime
from AGENT_ZERO_TREE.core.decision_processor import AgentZeroCore, DecisionInput, DecisionOutput

@pytest.fixture
def core_processor():
    """Provides a default instance of the AgentZeroCore."""
    return AgentZeroCore()

@pytest.fixture
def base_decision_input():
    """Provides a standard, valid DecisionInput object."""
    return DecisionInput(
        signal_confidence=0.8,
        signal_strength=0.7,
        execution_recommendation='execute',
        math_accuracy=0.9,
        math_precision=0.001,
        market_context={'volatility': 'normal'},
        timestamp=datetime.now()
    )

def test_initialization(core_processor):
    """Test that the AgentZeroCore initializes with correct defaults."""
    assert core_processor.decision_count == 0
    assert core_processor.total_execution_time == 0.0
    assert 'signal_confidence' in core_processor.weights
    assert 'execute_threshold' in core_processor.thresholds

def test_process_decision_output_structure(core_processor, base_decision_input):
    """Verify the output of process_decision has the correct structure and types."""
    decision = core_processor.process_decision(base_decision_input)
    assert isinstance(decision, DecisionOutput)
    assert isinstance(decision.action, str)
    assert isinstance(decision.confidence, float)
    assert isinstance(decision.composite_score, float)
    assert isinstance(decision.reasoning, list)
    assert decision.execution_time_ms > 0

def test_input_validation(core_processor, base_decision_input):
    """Test the _validate_inputs method with both valid and invalid data."""
    # Should pass
    core_processor._validate_inputs(base_decision_input)

    # Test invalid signal_confidence
    with pytest.raises(AssertionError, match="Signal confidence out of bounds"):
        invalid_input = replace(base_decision_input, signal_confidence=1.1)
        core_processor._validate_inputs(invalid_input)

    # Test invalid execution_recommendation
    with pytest.raises(AssertionError, match="Invalid execution recommendation"):
        invalid_input = replace(base_decision_input, execution_recommendation='invalid_action')
        core_processor._validate_inputs(invalid_input)

@pytest.mark.parametrize("inputs, expected_score", [
    ({'signal_confidence': 0.8, 'signal_strength': 0.7, 'execution_recommendation': 'execute', 'math_accuracy': 0.9, 'math_precision': 0.001}, 0.81),
    ({'signal_confidence': 0.2, 'signal_strength': 0.3, 'execution_recommendation': 'avoid', 'math_accuracy': 0.5, 'math_precision': 0.05}, 0.1925),
    ({'signal_confidence': 0.6, 'signal_strength': 0.5, 'execution_recommendation': 'hold', 'math_accuracy': 0.8, 'math_precision': 0.005}, 0.575),
])
def test_composite_score_calculation(core_processor, base_decision_input, inputs, expected_score):
    """Verify the weighted composite score is calculated correctly."""
    for key, value in inputs.items():
        setattr(base_decision_input, key, value)
    
    score = core_processor._calculate_composite_score(base_decision_input)
    assert score == pytest.approx(expected_score, abs=1e-4)

@pytest.mark.parametrize("score, expected_action", [
    (0.90, 'execute'),
    (0.75, 'execute'),
    (0.74, 'hold'),
    (0.50, 'hold'),
    (0.25, 'hold'),
    (0.24, 'avoid'),
    (0.10, 'avoid'),
])
def test_determine_action(core_processor, score, expected_action):
    """Test the action determination based on thresholds."""
    action = core_processor._determine_action(score)
    assert action == expected_action

def test_confidence_calculation_adjustments(core_processor, base_decision_input):
    """Test that confidence is adjusted based on math accuracy and precision."""
    # High accuracy should boost confidence
    base_decision_input.math_accuracy = 0.95
    decision_high_acc = core_processor.process_decision(base_decision_input)
    
    # Low precision should reduce confidence
    base_decision_input.math_accuracy = 0.8 # reset
    base_decision_input.math_precision = 0.02
    decision_low_prec = core_processor.process_decision(base_decision_input)

    assert decision_high_acc.confidence > decision_high_acc.composite_score
    assert decision_low_prec.confidence < decision_low_prec.composite_score