[{"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:59:31.124964", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:59:31.124747", "success": true, "latency_ms": 2345.9780999983195, "quality_score": 0.92, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:59:31.178133", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:59:31.177784", "success": true, "latency_ms": 52.21910000545904, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:59:31.273027", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:59:31.272771", "success": true, "latency_ms": 94.01159998378716, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:04:31.339054", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:04:31.338591", "success": true, "latency_ms": 63.86789999669418, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:04:31.401052", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:04:31.400600", "success": true, "latency_ms": 60.832399991340935, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:04:31.455331", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:04:31.454997", "success": true, "latency_ms": 53.170900006080046, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:09:31.530320", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:09:31.529665", "success": true, "latency_ms": 71.34520000545308, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:09:31.600672", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:09:31.600437", "success": true, "latency_ms": 68.32399999257177, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:09:31.638042", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:09:31.637864", "success": true, "latency_ms": 36.383200000273064, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:09:31.723692", "result": {"timestamp": "2025-06-24T12:09:31.723522", "total_execution_time_ms": 84.54590002656914, "budget_compliance": false, "error": "cannot import name 'get_config' from 'config.settings' (D:\\script-work\\CORE\\config\\settings.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:14:31.876397", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:14:31.875585", "success": true, "latency_ms": 149.2220000072848, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:14:31.997248", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:14:31.996981", "success": true, "latency_ms": 118.53249999694526, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:14:32.120252", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:14:32.119916", "success": true, "latency_ms": 121.59409999730997, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:19:32.167725", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:19:32.167438", "success": true, "latency_ms": 45.34199999761768, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:19:32.234396", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:19:32.234101", "success": true, "latency_ms": 64.46960000903346, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:19:32.295327", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:19:32.295077", "success": true, "latency_ms": 59.372999996412545, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:24:32.357022", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:24:32.356794", "success": true, "latency_ms": 59.335900004953146, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:24:32.410422", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:24:32.410066", "success": true, "latency_ms": 51.83679997571744, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:24:32.473295", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:24:32.472899", "success": true, "latency_ms": 61.167299980297685, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:24:33.954732", "result": {"timestamp": "2025-06-24T12:24:33.954612", "total_execution_time_ms": 1479.8448999936227, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:29:34.028020", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:29:34.027626", "success": true, "latency_ms": 69.29360001231544, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:29:34.143050", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:29:34.142659", "success": true, "latency_ms": 113.17920000874437, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:29:34.209090", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:29:34.208649", "success": true, "latency_ms": 64.32559998938814, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:34:34.272589", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:34:34.272106", "success": true, "latency_ms": 60.23289999575354, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:34:34.364531", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:34:34.364124", "success": true, "latency_ms": 90.04070001537912, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:34:34.484958", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:34:34.483756", "success": true, "latency_ms": 117.55920000723563, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:39:34.580954", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:39:34.580510", "success": true, "latency_ms": 86.5297999989707, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:39:34.609220", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:39:34.608838", "success": true, "latency_ms": 26.396600005682558, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:39:34.672318", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:39:34.671848", "success": true, "latency_ms": 60.47579998266883, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:39:34.674173", "result": {"timestamp": "2025-06-24T12:39:34.674012", "total_execution_time_ms": 0.01659998088143766, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:44:34.741651", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:44:34.741334", "success": true, "latency_ms": 64.07520000357181, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:44:34.827837", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:44:34.827501", "success": true, "latency_ms": 83.96359998732805, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:44:34.893663", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:44:34.893299", "success": true, "latency_ms": 63.43089998699725, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:49:34.987653", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:49:34.987337", "success": true, "latency_ms": 90.55250001256354, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:49:35.064669", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:49:35.064369", "success": true, "latency_ms": 74.60160000482574, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:49:35.129048", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:49:35.128671", "success": true, "latency_ms": 61.63509999169037, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:54:35.164023", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:54:35.163759", "success": true, "latency_ms": 31.060999986948445, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:54:35.224307", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:54:35.223991", "success": true, "latency_ms": 57.201000017812476, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:54:35.256709", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:54:35.256381", "success": true, "latency_ms": 29.553899978054687, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:54:35.258980", "result": {"timestamp": "2025-06-24T12:54:35.258894", "total_execution_time_ms": 0.020400009816512465, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:59:35.355543", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:59:35.355212", "success": true, "latency_ms": 93.22390001034364, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:59:35.433340", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:59:35.433007", "success": true, "latency_ms": 74.41110000945628, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:59:35.479429", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:59:35.479122", "success": true, "latency_ms": 42.44950000429526, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:04:35.544053", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:04:35.543691", "success": true, "latency_ms": 60.38470001658425, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:04:35.633594", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:04:35.633087", "success": true, "latency_ms": 86.78450001752935, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:04:35.724784", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:04:35.724417", "success": true, "latency_ms": 88.07090000482276, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:09:35.818717", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:09:35.818231", "success": true, "latency_ms": 89.88009998574853, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:09:35.895408", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:09:35.895091", "success": true, "latency_ms": 73.99070000974461, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:09:35.965905", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:09:35.965467", "success": true, "latency_ms": 66.9779000163544, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:09:35.970682", "result": {"timestamp": "2025-06-24T13:09:35.970567", "total_execution_time_ms": 0.02680000034160912, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:14:36.043334", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:14:36.042904", "success": true, "latency_ms": 67.55060001160018, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:14:36.087323", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:14:36.086889", "success": true, "latency_ms": 40.54410001845099, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:14:36.144951", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:14:36.144622", "success": true, "latency_ms": 54.11150000873022, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:19:36.235353", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:19:36.234909", "success": true, "latency_ms": 84.88200002466328, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:19:36.307597", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:19:36.307233", "success": true, "latency_ms": 69.04319999739528, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:19:36.439681", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:19:36.439290", "success": true, "latency_ms": 127.89440000778995, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:24:36.499229", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:24:36.498913", "success": true, "latency_ms": 54.362399998353794, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:24:36.578988", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:24:36.578682", "success": true, "latency_ms": 75.82709999405779, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:24:36.655561", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:24:36.655182", "success": true, "latency_ms": 69.38620001892559, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:24:36.661340", "result": {"timestamp": "2025-06-24T13:24:36.661220", "total_execution_time_ms": 0.03359999391250312, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:29:36.729182", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:29:36.728801", "success": true, "latency_ms": 60.77730000833981, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:29:36.812983", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:29:36.812677", "success": true, "latency_ms": 77.84149999497458, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:29:36.877267", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:29:36.876993", "success": true, "latency_ms": 60.19329998525791, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:34:36.957899", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:34:36.957461", "success": true, "latency_ms": 75.35159998224117, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:34:37.009212", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:34:37.008886", "success": true, "latency_ms": 47.13630001060665, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:34:37.082244", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:34:37.081951", "success": true, "latency_ms": 67.83479999285191, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:39:37.144302", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:39:37.143868", "success": true, "latency_ms": 56.088600016664714, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:39:37.244145", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:39:37.243740", "success": true, "latency_ms": 95.45749999233522, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:39:37.343299", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:39:37.342940", "success": true, "latency_ms": 95.29040000052191, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:39:37.347113", "result": {"timestamp": "2025-06-24T13:39:37.346964", "total_execution_time_ms": 0.030199997127056122, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:44:37.436445", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:44:37.436149", "success": true, "latency_ms": 84.71379999537021, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:44:37.492625", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:44:37.492296", "success": true, "latency_ms": 51.85029999120161, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:44:37.543291", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:44:37.543060", "success": true, "latency_ms": 45.854899974074215, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:49:37.616983", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:49:37.616612", "success": true, "latency_ms": 67.66130001051351, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:49:37.679237", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:49:37.678816", "success": true, "latency_ms": 56.80290001328103, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:49:37.788867", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:49:37.788389", "success": true, "latency_ms": 104.58179999841377, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:54:37.844247", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:54:37.843979", "success": true, "latency_ms": 49.95539999799803, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:54:37.945557", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:54:37.945027", "success": true, "latency_ms": 97.34419998130761, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:54:38.020270", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:54:38.019707", "success": true, "latency_ms": 70.01840000157245, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:54:38.024339", "result": {"timestamp": "2025-06-24T13:54:38.024174", "total_execution_time_ms": 0.019400002202019095, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:59:38.067339", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:59:38.066908", "success": true, "latency_ms": 37.59720001835376, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:59:38.162926", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:59:38.162610", "success": true, "latency_ms": 90.83729999838397, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:59:38.254552", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:59:38.254153", "success": true, "latency_ms": 86.43929997924715, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:04:38.327301", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:04:38.326757", "success": true, "latency_ms": 66.99039999512024, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:04:38.419095", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:04:38.418709", "success": true, "latency_ms": 86.90649998607114, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:04:38.493955", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:04:38.493559", "success": true, "latency_ms": 69.69619999290444, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:09:38.550699", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:09:38.550250", "success": true, "latency_ms": 50.776799995219335, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:09:38.632897", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:09:38.632509", "success": true, "latency_ms": 77.6659999974072, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:09:38.702137", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:09:38.701800", "success": true, "latency_ms": 63.632600009441376, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T14:09:38.707152", "result": {"timestamp": "2025-06-24T14:09:38.706997", "total_execution_time_ms": 0.0501000031363219, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:14:38.756866", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:14:38.756580", "success": true, "latency_ms": 44.336499995552, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:14:38.815671", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:14:38.815353", "success": true, "latency_ms": 53.415600006701425, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:14:38.859647", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:14:38.859338", "success": true, "latency_ms": 39.12890001083724, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:19:38.937680", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:19:38.937337", "success": true, "latency_ms": 71.97090002591722, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:19:39.029209", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:19:39.028925", "success": true, "latency_ms": 85.83709999220446, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:19:39.103801", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:19:39.103487", "success": true, "latency_ms": 69.19800001196563, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:24:39.149929", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:24:39.149565", "success": true, "latency_ms": 39.71749998163432, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:24:39.209289", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:24:39.209014", "success": true, "latency_ms": 54.98819999047555, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:24:39.252024", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:24:39.251751", "success": true, "latency_ms": 37.414199992781505, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T14:24:39.256301", "result": {"timestamp": "2025-06-24T14:24:39.256224", "total_execution_time_ms": 0.01689998316578567, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:29:39.323162", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:29:39.322947", "success": true, "latency_ms": 61.666800000239164, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:29:39.403198", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:29:39.402942", "success": true, "latency_ms": 75.76920001883991, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:29:39.481976", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:29:39.481529", "success": true, "latency_ms": 73.99390000500716, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:53:05.763028", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:53:05.748189", "success": true, "latency_ms": 533.2447000255343, "quality_score": 0.98, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:53:05.998122", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:53:05.972033", "success": true, "latency_ms": 167.49620001064613, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:53:06.304883", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:53:06.278885", "success": true, "latency_ms": 203.91209999797866, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:58:06.431128", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:58:06.430794", "success": true, "latency_ms": 91.81670000543818, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:58:06.537128", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:58:06.536738", "success": true, "latency_ms": 99.61659999680705, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:58:06.581852", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:58:06.581530", "success": true, "latency_ms": 38.13510001054965, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T14:58:06.587971", "result": {"timestamp": "2025-06-24T14:58:06.587842", "total_execution_time_ms": 0.04730001091957092, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:03:06.687232", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:03:06.686756", "success": true, "latency_ms": 91.38399999937974, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:03:06.748530", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:03:06.748212", "success": true, "latency_ms": 53.947600012179464, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:03:06.818398", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:03:06.818070", "success": true, "latency_ms": 60.01409998862073, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:08:07.342018", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:08:07.319270", "success": true, "latency_ms": 342.9835999850184, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:08:07.687575", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:08:07.639835", "success": true, "latency_ms": 274.15780001319945, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:08:08.160537", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:08:08.117098", "success": true, "latency_ms": 403.5317999951076, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:13:08.322053", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:13:08.320982", "success": true, "latency_ms": 101.1362999852281, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:13:08.385587", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:13:08.385227", "success": true, "latency_ms": 48.823999997694045, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:13:08.495487", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:13:08.495039", "success": true, "latency_ms": 102.12009999668226, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T15:13:08.504706", "result": {"timestamp": "2025-06-24T15:13:08.504562", "total_execution_time_ms": 0.04119999357499182, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:18:08.616080", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:18:08.615617", "success": true, "latency_ms": 99.23819999676198, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:18:08.698711", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:18:08.698458", "success": true, "latency_ms": 75.81189999473281, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:18:08.786686", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:18:08.786318", "success": true, "latency_ms": 81.93440001923591, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:23:09.027903", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:23:09.014521", "success": true, "latency_ms": 200.5887000123039, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:23:09.546504", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:23:09.427350", "success": true, "latency_ms": 323.30479999654926, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:23:09.929126", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:23:09.926790", "success": true, "latency_ms": 302.9916000086814, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:28:09.997987", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:28:09.997644", "success": true, "latency_ms": 51.79250001674518, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:28:10.064905", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:28:10.064390", "success": true, "latency_ms": 58.984999981475994, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:28:10.124815", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:28:10.124378", "success": true, "latency_ms": 53.55250000138767, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T15:28:10.131448", "result": {"timestamp": "2025-06-24T15:28:10.131356", "total_execution_time_ms": 0.02269999822601676, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:33:10.211890", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:33:10.211501", "success": true, "latency_ms": 72.42300000507385, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:33:10.321345", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:33:10.320901", "success": true, "latency_ms": 100.57980002602562, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:33:10.412952", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:33:10.412609", "success": true, "latency_ms": 83.19740000297315, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:38:10.533124", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:38:10.530648", "success": true, "latency_ms": 92.25919999880716, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:38:10.635223", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:38:10.633326", "success": true, "latency_ms": 76.53510000091046, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:38:10.954425", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:38:10.847901", "success": true, "latency_ms": 192.25349999032915, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:43:11.126883", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:43:11.126329", "success": true, "latency_ms": 78.91189999645576, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:43:11.211778", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:43:11.211472", "success": true, "latency_ms": 77.43520001531579, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:43:11.290575", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:43:11.290113", "success": true, "latency_ms": 67.07109999842942, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T15:43:11.296882", "result": {"timestamp": "2025-06-24T15:43:11.296699", "total_execution_time_ms": 0.02169999061152339, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:48:11.387047", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:48:11.386546", "success": true, "latency_ms": 81.12009998876601, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:48:11.480583", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:48:11.480207", "success": true, "latency_ms": 86.50530001614243, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:48:11.564028", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:48:11.563807", "success": true, "latency_ms": 76.3464999909047, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:53:11.656524", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:53:11.655156", "success": true, "latency_ms": 68.75149998813868, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:53:11.743225", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:53:11.742315", "success": true, "latency_ms": 65.31920001725666, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:53:11.837672", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:53:11.830119", "success": true, "latency_ms": 70.2480000036303, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:58:11.980451", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:58:11.979937", "success": true, "latency_ms": 123.56139998883009, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:58:12.062462", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:58:12.062117", "success": true, "latency_ms": 70.63499998184852, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:58:12.216391", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:58:12.216084", "success": true, "latency_ms": 145.98180001485161, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T15:58:12.228336", "result": {"timestamp": "2025-06-24T15:58:12.227226", "total_execution_time_ms": 0.028900016332045197, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T16:03:12.310952", "result": {"ticker": "SPY", "timestamp": "2025-06-24T16:03:12.310650", "success": true, "latency_ms": 69.74279999849387, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T16:03:12.415656", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T16:03:12.415167", "success": true, "latency_ms": 96.09389997785911, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}, {"session_id": "20250624_115928", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T16:03:12.480158", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T16:03:12.479747", "success": true, "latency_ms": 57.9509999952279, "quality_score": 0.995, "data_source": "broker_api_integration", "enhancement": "real_bid_ask_data", "has_real_bid_ask": true, "bars_count": 100, "opts_count": 50, "error": null}}]