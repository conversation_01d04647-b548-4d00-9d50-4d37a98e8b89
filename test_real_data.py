#!/usr/bin/env python3
"""
Test script to verify if MCP server is using real Schwab data
"""

import requests
import json

def test_bars_endpoint():
    """Test the bars endpoint for real data"""
    print("Testing bars endpoint for real data...")
    
    try:
        response = requests.get('http://localhost:8005/bars?tk=MSFT&tf=1')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Raw response type: {type(data)}")
            print(f"Raw response: {data[:2] if isinstance(data, list) else data}")

            # Handle both list and dict responses
            if isinstance(data, list):
                bars = data
            else:
                bars = data.get('bars', [])

            print(f"Bars received: {len(bars)}")

            if bars:
                print("First bar:", bars[0])
                print("Last bar:", bars[-1])
                
                # Check if data looks real (timestamps should be realistic)
                first_timestamp = bars[0].get('t', 0)
                last_timestamp = bars[-1].get('t', 0)
                
                print(f"Timestamp range: {first_timestamp} to {last_timestamp}")
                print(f"Time difference: {last_timestamp - first_timestamp} seconds")
                
                # Real data should have varying prices
                prices = [bar.get('c', 0) for bar in bars[:10]]
                print(f"First 10 closing prices: {prices}")
                
                # Check for mock data patterns
                if all(abs(prices[i] - prices[0]) < 1.0 for i in range(len(prices))):
                    print("⚠️  Prices are too similar - likely mock data")
                else:
                    print("✓ Price variation looks realistic")
            else:
                print("No bars received")
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing bars endpoint: {e}")

def test_quotes_endpoint():
    """Test the quotes endpoint for real data"""
    print("\nTesting quotes endpoint for real data...")
    
    try:
        response = requests.get('http://localhost:8005/quotes/MSFT')
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Quote data:", data)
        else:
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"Error testing quotes endpoint: {e}")

if __name__ == "__main__":
    test_bars_endpoint()
    test_quotes_endpoint()
