#!/usr/bin/env python3
"""
Test for Hard-Coded Data Loading Issue
Run the ultimate orchestrator and capture all output to find "loaded 1 price bar" message
"""

import sys
import subprocess
import re
from datetime import datetime

def run_ultimate_orchestrator_test():
    """Run ultimate orchestrator and capture output"""
    print("TESTING ULTIMATE ORCHESTRATOR FOR HARD-CODED DATA")
    print("=" * 60)
    
    try:
        # Run the ultimate orchestrator with MSFT
        cmd = ["py", "ultimate_orchestrator.py", "MSFT"]
        
        print(f"Running: {' '.join(cmd)}")
        print("Capturing output for hard-coded data analysis...")
        
        # Run and capture both stdout and stderr
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120,  # 2 minute timeout
            cwd="."
        )
        
        # Combine stdout and stderr
        full_output = result.stdout + "\n" + result.stderr
        
        print(f"\nReturn code: {result.returncode}")
        print(f"Output length: {len(full_output)} characters")
        
        # Search for hard-coded data patterns
        hard_coded_patterns = [
            r"loaded 1 price bar",
            r"1 price bar",
            r"loaded 1.*bar",
            r"only.*1.*bar",
            r"single.*bar",
            r"1.*data.*point",
            r"insufficient.*data",
            r"need.*\d+.*got.*1"
        ]
        
        found_issues = []
        
        for pattern in hard_coded_patterns:
            matches = re.findall(pattern, full_output, re.IGNORECASE)
            if matches:
                found_issues.extend([(pattern, match) for match in matches])
        
        # Print results
        if found_issues:
            print(f"\n⚠️  HARD-CODED DATA ISSUES FOUND:")
            for pattern, match in found_issues:
                print(f"   Pattern: {pattern}")
                print(f"   Match: {match}")
        else:
            print(f"\n✅ No hard-coded data patterns found")
        
        # Look for data loading messages
        data_loading_patterns = [
            r"loaded.*\d+.*bar",
            r"fetched.*\d+.*bar",
            r"got.*\d+.*bar",
            r"\d+.*price.*bar",
            r"data.*\d+.*record"
        ]
        
        data_messages = []
        for pattern in data_loading_patterns:
            matches = re.findall(pattern, full_output, re.IGNORECASE)
            if matches:
                data_messages.extend(matches)
        
        if data_messages:
            print(f"\n📊 DATA LOADING MESSAGES:")
            for msg in data_messages:
                print(f"   {msg}")
        
        # Save full output for analysis
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"orchestrator_output_{timestamp}.txt"
        
        with open(output_file, 'w') as f:
            f.write(f"Ultimate Orchestrator Test Output\n")
            f.write(f"Timestamp: {datetime.now()}\n")
            f.write(f"Command: {' '.join(cmd)}\n")
            f.write(f"Return Code: {result.returncode}\n")
            f.write("=" * 60 + "\n")
            f.write("STDOUT:\n")
            f.write(result.stdout)
            f.write("\n" + "=" * 60 + "\n")
            f.write("STDERR:\n")
            f.write(result.stderr)
        
        print(f"\n📁 Full output saved to: {output_file}")
        
        return len(found_issues) == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Test timed out after 2 minutes")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main test function"""
    success = run_ultimate_orchestrator_test()
    
    if success:
        print("\n🎉 No hard-coded data issues detected!")
        return 0
    else:
        print("\n⚠️  Hard-coded data issues found - investigation needed")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
