"""
IV Skew Analyzer Module

This module analyzes implied volatility skew from options data to identify
potential price levels where institutional activity may be concentrated.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any
import logging
from datetime import datetime, timedelta
# Import these modules only when needed in specific methods
# to avoid unused import warnings
# from scipy.interpolate import griddata, interp2d
# from scipy.stats import zscore
# import matplotlib.pyplot as plt
# from matplotlib import cm
# from mpl_toolkits.mplot3d import Axes3D

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("iv_skew_analyzer")

class IVSkewAnalyzer:
    """
    Analyzer for options implied volatility skew patterns.
    Uses real options data to identify potential support/resistance
    levels based on IV skew anomalies.
    """

    def __init__(self, options_data: pd.DataFrame = None, current_price: float = 0):
        """
        Initialize the IVSkewAnalyzer with options data.

        Args:
            options_data: DataFrame with options chain data
            current_price: Current price of the underlying asset
        """
        self.options_data = options_data
        self.current_price = current_price
        self.skew_data = None
        self.term_structure_data = None
        self.volatility_surface = None
        self.skew_ratio_data = None
        self.volatility_anomalies = None
        self.results = None
        self.logger = logging.getLogger("iv_skew_analyzer")

        # Store analysis configurations
        self.config = {
            'min_iv_diff': 0.05,  # Minimum IV difference to consider significant
            'strike_distance': 0.1,  # Relative distance from ATM to focus analysis
            'normalize_iv': True,  # Whether to normalize IVs for comparison
            'smoothing_factor': 2,  # Factor for smoothing the IV curves
            'anomaly_threshold': 2.0,  # Z-score threshold for volatility anomalies
            'surface_grid_density': 50,  # Grid density for volatility surface
            'term_structure_min_expiries': 3,  # Minimum number of expiries for term structure
            'skew_ratio_threshold': 0.2  # Threshold for significant skew ratio changes
        }

        if self.options_data is not None:
            self._prepare_data()

    def _prepare_data(self):
        """Prepare and validate options data for analysis."""
        if self.options_data is None or len(self.options_data) == 0:
            self.logger.warning("No options data available")
            return False

        try:
            # Make a copy to avoid modifying the original
            options_df = self.options_data.copy()

            # Check for required columns
            iv_columns = [col for col in options_df.columns if 'iv' in col.lower() or 'implied_volatility' in col.lower()]

            if len(iv_columns) == 0:
                # No IV columns, check if we can derive it
                # Check for either 'type' or 'option_type' column
                type_col = None
                if 'type' in options_df.columns:
                    type_col = 'type'
                elif 'option_type' in options_df.columns:
                    type_col = 'option_type'
                else:
                    # Try to map common column names for type
                    type_cols = ['optionType', 'put_call', 'putCall', 'contract_type']
                    for alt_col in type_cols:
                        if alt_col in options_df.columns:
                            options_df['type'] = options_df[alt_col]
                            type_col = 'type'
                            break

                # If we still don't have a type column, try to infer from other columns
                if type_col is None:
                    # Check for call_* and put_* columns
                    if any(col.startswith('call_') for col in options_df.columns) and any(col.startswith('put_') for col in options_df.columns):
                        # Create a type column based on call_* and put_* columns
                        self.logger.info("Inferring option types from call_* and put_* columns")
                        options_df['type'] = None
                        for col in options_df.columns:
                            if col.startswith('call_') and pd.notna(options_df[col]).any():
                                mask = pd.notna(options_df[col])
                                options_df.loc[mask, 'type'] = 'call'
                            elif col.startswith('put_') and pd.notna(options_df[col]).any():
                                mask = pd.notna(options_df[col])
                                options_df.loc[mask, 'type'] = 'put'
                        type_col = 'type'
                    # Check for ticker column that might contain option type info
                    elif 'ticker' in options_df.columns:
                        self.logger.info("Attempting to extract option types from ticker symbols")

                        # Define a function to extract option type from ticker
                        def extract_option_type_from_ticker(ticker):
                            if ticker is None:
                                return None

                            ticker_str = str(ticker).upper()

                            # Standard Polygon format: O:AAPL210917C00145000
                            if 'O:' in ticker_str:
                                option_part = ticker_str.split('O:')[1]
                                if 'C' in option_part and not 'P' in option_part:
                                    return 'call'
                                elif 'P' in option_part and not 'C' in option_part:
                                    return 'put'

                            # Alternative formats
                            elif ticker_str.endswith('C'):
                                return 'call'
                            elif ticker_str.endswith('P'):
                                return 'put'

                            # Check for C or P followed by digits
                            for i, char in enumerate(ticker_str):
                                if char == 'C' and i > 0 and i < len(ticker_str) - 1:
                                    if ticker_str[i+1].isdigit():
                                        return 'call'
                                elif char == 'P' and i > 0 and i < len(ticker_str) - 1:
                                    if ticker_str[i+1].isdigit():
                                        return 'put'

                            return None

                        options_df['type'] = options_df['ticker'].apply(extract_option_type_from_ticker)

                        if options_df['type'].notna().any():
                            type_col = 'type'
                            self.logger.info(f"Successfully extracted option types from {options_df['type'].notna().sum()} ticker symbols")
                        else:
                            self.logger.warning("Could not extract option types from ticker symbols")
                    # Check for contract_type column
                    elif 'contract_type' in options_df.columns:
                        self.logger.info("Using contract_type column for option types")

                        # Map contract_type values to 'call' or 'put'
                        def standardize_contract_type(contract_type):
                            if contract_type is None:
                                return None

                            contract_str = str(contract_type).upper()
                            if 'CALL' in contract_str or contract_str == 'C':
                                return 'call'
                            elif 'PUT' in contract_str or contract_str == 'P':
                                return 'put'
                            return None

                        options_df['type'] = options_df['contract_type'].apply(standardize_contract_type)

                        if options_df['type'].notna().any():
                            type_col = 'type'
                            self.logger.info(f"Successfully mapped {options_df['type'].notna().sum()} contract types")
                        else:
                            self.logger.warning("Could not map contract_type values to call/put")
                    else:
                        # Create artificial rows for both call and put as a last resort
                        self.logger.warning("Creating artificial call/put rows as no option type information is available")
                        new_rows = []
                        for _, row in options_df.iterrows():
                            call_row = row.copy()
                            call_row['type'] = 'call'
                            put_row = row.copy()
                            put_row['type'] = 'put'
                            new_rows.append(call_row)
                            new_rows.append(put_row)
                        options_df = pd.DataFrame(new_rows)
                        type_col = 'type'

                # Check for strike column
                strike_col = None
                for col_name in ['strike_price', 'strike', 'strikePrice', 'exercise_price']:
                    if col_name in options_df.columns:
                        strike_col = col_name
                        break

                if strike_col is not None and type_col is not None:
                    # Create placeholder columns for IV
                    options_df['call_iv'] = options_df.apply(
                        lambda row: self._estimate_iv(row) if str(row[type_col]).lower() == 'call' else np.nan,
                        axis=1
                    )
                    options_df['put_iv'] = options_df.apply(
                        lambda row: self._estimate_iv(row) if str(row[type_col]).lower() == 'put' else np.nan,
                        axis=1
                    )
                else:
                    self.logger.warning("Cannot find or derive implied volatility data")
                    return False

            # Handle different column naming conventions
            if 'call_iv' in options_df.columns and 'put_iv' in options_df.columns:
                # Data is already in the expected format
                pass
            elif any('implied_volatility' in col.lower() for col in options_df.columns):
                # Convert to the expected format
                iv_col = next(col for col in options_df.columns if 'implied_volatility' in col.lower())

                # Check for either 'type' or 'option_type' column
                type_col = None
                if 'type' in options_df.columns:
                    type_col = 'type'
                elif 'option_type' in options_df.columns:
                    type_col = 'option_type'

                if type_col is not None:
                    # Split by option type
                    call_mask = options_df[type_col].str.lower() == 'call'
                    put_mask = options_df[type_col].str.lower() == 'put'

                    options_df.loc[call_mask, 'call_iv'] = options_df.loc[call_mask, iv_col]
                    options_df.loc[put_mask, 'put_iv'] = options_df.loc[put_mask, iv_col]
                else:
                    self.logger.warning("Cannot determine option types for IV data")
                    return False
            else:
                self.logger.warning("Unsupported IV data format")
                return False

            # Get strikes and IVs for skew analysis
            call_data = options_df.dropna(subset=['call_iv']).copy()
            put_data = options_df.dropna(subset=['put_iv']).copy()

            # Check for strike_price column and standardize if needed
            strike_col = None
            for col_name in ['strike_price', 'strike', 'strikePrice', 'exercise_price']:
                if col_name in options_df.columns:
                    strike_col = col_name
                    break

            if strike_col is None:
                self.logger.warning("Missing strike price information")
                return False

            # Ensure both call_data and put_data have strike_price column
            if strike_col != 'strike_price':
                call_data['strike_price'] = call_data[strike_col]
                put_data['strike_price'] = put_data[strike_col]
                options_df['strike_price'] = options_df[strike_col]  # Add to main dataframe too
                self.logger.info(f"Mapped {strike_col} to strike_price")

            # Group by strike and take mean if multiple contracts per strike
            call_skew = call_data.groupby('strike_price')['call_iv'].mean().reset_index()
            put_skew = put_data.groupby('strike_price')['put_iv'].mean().reset_index()

            # Store results
            self.skew_data = {
                'strikes': sorted(list(set(call_skew['strike_price'].tolist() + put_skew['strike_price'].tolist()))),
                'call_ivs': call_skew.set_index('strike_price')['call_iv'].to_dict(),
                'put_ivs': put_skew.set_index('strike_price')['put_iv'].to_dict()
            }

            return True

        except Exception as e:
            self.logger.error(f"Error preparing options data: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return False

    def _estimate_iv(self, row):
        """
        Estimate implied volatility if not directly available.
        This is a simplified approximation based on moneyness and other factors.

        Args:
            row: Row of options data

        Returns:
            Estimated implied volatility value
        """
        # This is a very simplified approximation
        # In a real implementation, you'd use an option pricing model

        base_iv = 0.30  # Base IV of 30%

        # Apply skew based on moneyness
        if 'strike_price' in row:
            moneyness = row['strike_price'] / self.current_price - 1.0

            # Apply smile effect
            smile_effect = 0.05 * (moneyness ** 2)

            # Apply skew effect (puts typically have higher IV than calls)
            skew_effect = 0.0

            # Check for option type in either 'type' or 'option_type' column
            option_type = None
            if 'type' in row and row['type'] is not None:
                option_type = str(row['type']).lower()
            elif 'option_type' in row and row['option_type'] is not None:
                option_type = str(row['option_type']).lower()

            if option_type == 'put':
                skew_effect = 0.05 * abs(min(0, moneyness))  # Higher IV for OTM puts
            elif option_type == 'call':
                skew_effect = 0.03 * max(0, moneyness)  # Slight increase for OTM calls

            return base_iv + smile_effect + skew_effect

        return base_iv  # Default value

    def _find_skew_anomalies(self):
        """
        Find anomalies in the IV skew that might indicate liquidity levels.

        Returns:
            List of skew anomalies
        """
        if not self.skew_data:
            return []

        anomalies = []

        # Get sorted strikes and corresponding IVs
        strikes = sorted(self.skew_data['strikes'])
        call_ivs = [self.skew_data['call_ivs'].get(strike, np.nan) for strike in strikes]
        put_ivs = [self.skew_data['put_ivs'].get(strike, np.nan) for strike in strikes]

        # Convert to numpy arrays and handle NaNs
        strikes_arr = np.array(strikes)
        call_ivs_arr = np.array(call_ivs)
        put_ivs_arr = np.array(put_ivs)

        # Smooth the IV curves if needed
        if self.config['smoothing_factor'] > 0:
            try:
                from scipy.ndimage import gaussian_filter1d

                # Create masks for valid data
                call_mask = [PARTIAL]np.isnan(call_ivs_arr)
                put_mask = [PARTIAL]np.isnan(put_ivs_arr)

                # Only smooth valid data
                if np.sum(call_mask) > self.config['smoothing_factor'] * 3:
                    call_ivs_smooth = np.array(call_ivs_arr)
                    call_ivs_smooth[call_mask] = gaussian_filter1d(
                        call_ivs_arr[call_mask],
                        sigma=self.config['smoothing_factor']
                    )
                    call_ivs_arr = call_ivs_smooth

                if np.sum(put_mask) > self.config['smoothing_factor'] * 3:
                    put_ivs_smooth = np.array(put_ivs_arr)
                    put_ivs_smooth[put_mask] = gaussian_filter1d(
                        put_ivs_arr[put_mask],
                        sigma=self.config['smoothing_factor']
                    )
                    put_ivs_arr = put_ivs_smooth

            except ImportError:
                self.logger.warning("scipy not available for smoothing, using raw IV curves")

        # Find the ATM strike (closest to current price)
        # We calculate this but don't need to store it in a variable
        _ = np.abs(strikes_arr - self.current_price).argmin()

        # Calculate IV differential (put IV - call IV) for each strike
        iv_diff = put_ivs_arr - call_ivs_arr

        # Find significant differentials
        for i in range(1, len(strikes) - 1):
            # Skip strikes too far from current price
            if abs(strikes[i] / self.current_price - 1.0) > self.config['strike_distance']:
                continue

            # Skip if we don't have both call and put IVs
            if np.isnan(call_ivs_arr[i]) or np.isnan(put_ivs_arr[i]):
                continue

            # Check for high put skew (potential support)
            if (iv_diff[i] > self.config['min_iv_diff'] and
                iv_diff[i] > iv_diff[i-1] and
                iv_diff[i] > iv_diff[i+1] and
                strikes[i] < self.current_price):

                # Calculate strength based on the IV differential
                max_diff = np.nanmax(iv_diff)
                strength = iv_diff[i] / max_diff if max_diff > 0 else 0.5

                anomalies.append({
                    'type': 'high_put_skew',
                    'strike': strikes[i],
                    'iv_diff': float(iv_diff[i]),
                    'put_iv': float(put_ivs_arr[i]),
                    'call_iv': float(call_ivs_arr[i]),
                    'strength': float(min(1.0, strength))
                })

            # Check for high call skew (potential resistance)
            if (iv_diff[i] < -self.config['min_iv_diff'] and
                iv_diff[i] < iv_diff[i-1] and
                iv_diff[i] < iv_diff[i+1] and
                strikes[i] > self.current_price):

                # Calculate strength based on the IV differential
                min_diff = np.nanmin(iv_diff)
                strength = abs(iv_diff[i] / min_diff) if min_diff < 0 else 0.5

                anomalies.append({
                    'type': 'high_call_skew',
                    'strike': strikes[i],
                    'iv_diff': float(iv_diff[i]),
                    'put_iv': float(put_ivs_arr[i]),
                    'call_iv': float(call_ivs_arr[i]),
                    'strength': float(min(1.0, strength))
                })

        # Sort by strength
        anomalies = sorted(anomalies, key=lambda x: x['strength'], reverse=True)

        return anomalies

    def analyze(self) -> Dict[str, Any]:
        """
        Analyze IV skew to identify potential liquidity levels.
        Incorporates all analysis methods for a comprehensive view.

        Returns:
            Dictionary with skew analysis results and liquidity levels
        """
        if self.skew_data is None and not self._prepare_data():
            self.logger.warning("No valid options data available for IV skew analysis")
            return {'liquidity_levels': [], 'skew_patterns': []}

        try:
            # Find basic skew anomalies
            skew_anomalies = self._find_skew_anomalies()

            # Convert to liquidity levels
            liquidity_levels = []

            for anomaly in skew_anomalies:
                if anomaly['type'] == 'high_put_skew':
                    # High put skew often indicates support
                    liquidity_levels.append({
                        'price': anomaly['strike'],
                        'type': 'support',
                        'strength': anomaly['strength'],
                        'source': 'iv_skew',
                        'details': {
                            'iv_diff': anomaly['iv_diff'],
                            'put_iv': anomaly['put_iv'],
                            'call_iv': anomaly['call_iv'],
                            'skew_type': 'high_put_skew'
                        }
                    })
                elif anomaly['type'] == 'high_call_skew':
                    # High call skew often indicates resistance
                    liquidity_levels.append({
                        'price': anomaly['strike'],
                        'type': 'resistance',
                        'strength': anomaly['strength'],
                        'source': 'iv_skew',
                        'details': {
                            'iv_diff': anomaly['iv_diff'],
                            'put_iv': anomaly['put_iv'],
                            'call_iv': anomaly['call_iv'],
                            'skew_type': 'high_call_skew'
                        }
                    })

            # Run additional analyses if we have enough data
            additional_analyses = {}

            # Analyze term structure
            term_structure = self.analyze_term_structure()
            if term_structure:
                additional_analyses['term_structure'] = term_structure

            # Calculate skew ratio
            skew_ratio = self.calculate_skew_ratio()
            if skew_ratio:
                additional_analyses['skew_ratio'] = skew_ratio
                # Add liquidity levels from skew ratio analysis
                if 'liquidity_levels' in skew_ratio:
                    liquidity_levels.extend(skew_ratio['liquidity_levels'])

            # Generate volatility surface
            volatility_surface = self.generate_volatility_surface()
            if volatility_surface:
                additional_analyses['volatility_surface'] = {
                    'summary': {
                        'num_strikes': len(volatility_surface['strikes']),
                        'num_expiries': len(volatility_surface['expiries']),
                        'min_strike': min(volatility_surface['strikes']),
                        'max_strike': max(volatility_surface['strikes'])
                    }
                }

                # Detect volatility anomalies if surface was generated
                volatility_anomalies = self.detect_volatility_anomalies()
                if volatility_anomalies:
                    additional_analyses['volatility_anomalies'] = volatility_anomalies

                    # Add liquidity levels from volatility anomalies
                    if 'liquidity_levels' in volatility_anomalies:
                        liquidity_levels.extend(volatility_anomalies['liquidity_levels'])
            else:
                # Log this information
                self.logger.warning("Volatility surface generation failed")

                # Add a placeholder for compatibility
                additional_analyses['volatility_surface_failed'] = {
                    'reason': 'Failed to generate volatility surface',
                    'possible_causes': [
                        'Insufficient option data',
                        'Missing call/put type identification',
                        'Missing implied volatility data'
                    ]
                }

            # Determine overall market sentiment
            sentiment = "neutral"
            sentiment_sources = []

            # Check skew ratio for sentiment
            if skew_ratio and 'sentiment' in skew_ratio:
                sentiment = skew_ratio['sentiment']
                sentiment_sources.append('skew_ratio')

            # Check term structure for sentiment
            if term_structure and 'shape' in term_structure:
                if term_structure['shape'] == 'upward_sloping':
                    # Normal market conditions
                    if sentiment != "bearish":
                        sentiment = "neutral"
                        sentiment_sources.append('term_structure')
                elif term_structure['shape'] == 'downward_sloping':
                    # Inverted term structure often indicates market stress
                    sentiment = "bearish"
                    sentiment_sources.append('term_structure')

            # Sort liquidity levels by strength
            liquidity_levels = sorted(liquidity_levels, key=lambda x: x['strength'], reverse=True)

            # Store results
            self.results = {
                'liquidity_levels': liquidity_levels,
                'skew_patterns': skew_anomalies,
                'additional_analyses': additional_analyses,
                'market_sentiment': {
                    'sentiment': sentiment,
                    'sources': sentiment_sources
                }
            }

            return self.results

        except Exception as e:
            self.logger.error(f"Error analyzing IV skew: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {'liquidity_levels': [], 'skew_patterns': []}

    def visualize(self, output_file=None, show_plot=False):
        """
        Visualize IV skew and identified liquidity levels.

        Args:
            output_file: Output file path for the visualization
            show_plot: Whether to display the plot

        Returns:
            Dictionary with visualization data and plot objects
        """
        # Analyze if not already done
        if self.results is None:
            self.analyze()

        # Return visualization data
        return self.results

    def visualize_skew(self, ax=None):
        """
        Visualize IV skew on the provided axis.

        Args:
            ax: Matplotlib axis to plot on
        """
        import matplotlib.pyplot as plt

        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))

        if self.skew_data is None:
            ax.text(0.5, 0.5, 'IV Skew Data Not Available',
                    horizontalalignment='center',
                    verticalalignment='center',
                    transform=ax.transAxes)
            return

    def visualize_term_structure(self, ax=None):
        """
        Visualize the term structure of implied volatility.

        Args:
            ax: Matplotlib axis to plot on
        """
        import matplotlib.pyplot as plt

        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))

        if self.term_structure_data is None:
            # Try to analyze term structure if not already done
            if self.analyze_term_structure() is None:
                ax.text(0.5, 0.5, 'Term Structure Data Not Available',
                        horizontalalignment='center',
                        verticalalignment='center',
                        transform=ax.transAxes)
                return

        try:
            # Get ATM term structure data
            atm_data = self.term_structure_data['atm_term_structure']

            if not atm_data:
                ax.text(0.5, 0.5, 'Insufficient Term Structure Data',
                        horizontalalignment='center',
                        verticalalignment='center',
                        transform=ax.transAxes)
                return

            # Extract data for plotting
            expiries = [item['expiry'] for item in atm_data]
            call_ivs = [item['call_iv'] for item in atm_data]
            put_ivs = [item['put_iv'] for item in atm_data]
            avg_ivs = [item['avg_iv'] for item in atm_data]

            # Plot term structure curves
            ax.plot(expiries, call_ivs, 'g-', marker='o', markersize=4,
                   linewidth=1.5, label='Call IV')
            ax.plot(expiries, put_ivs, 'r-', marker='s', markersize=4,
                   linewidth=1.5, label='Put IV')
            ax.plot(expiries, avg_ivs, 'b-', marker='^', markersize=4,
                   linewidth=1.5, label='Average IV')

            # Highlight term structure shape
            shape = self.term_structure_data.get('shape', 'unknown')
            ax.text(0.02, 0.95, f"Term Structure: {shape}",
                   transform=ax.transAxes, fontsize=10,
                   bbox=dict(facecolor='white', alpha=0.7))

            # Customize plot
            ax.set_title('Implied Volatility Term Structure', fontsize=12)
            ax.set_xlabel('Expiration', fontsize=10)
            ax.set_ylabel('Implied Volatility (%)', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.legend(loc='best', fontsize=8)

        except Exception as e:
            self.logger.error(f"Error visualizing term structure: {str(e)}")
            ax.text(0.5, 0.5, f'Error in term structure visualization:\n{str(e)}',
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)

    def visualize_skew(self, ax=None):
        """
        Visualize IV skew on the provided axis.

        Args:
            ax: Matplotlib axis to plot on
        """
        import matplotlib.pyplot as plt

        if ax is None:
            _, ax = plt.subplots(figsize=(10, 6))

        if self.skew_data is None:
            ax.text(0.5, 0.5, 'IV Skew Data Not Available',
                    horizontalalignment='center',
                    verticalalignment='center',
                    transform=ax.transAxes)
            return

        try:
            # Get strikes and IVs
            strikes = sorted(self.skew_data['strikes'])
            call_ivs = [self.skew_data['call_ivs'].get(strike, np.nan) for strike in strikes]
            put_ivs = [self.skew_data['put_ivs'].get(strike, np.nan) for strike in strikes]

            # Plot call IV curve
            ax.plot(strikes, call_ivs, 'g-', marker='o', markersize=4,
                   linewidth=1.5, label='Call IV')

            # Plot put IV curve
            ax.plot(strikes, put_ivs, 'r-', marker='s', markersize=4,
                   linewidth=1.5, label='Put IV')

            # Add current price line
            ax.axvline(x=self.current_price, color='blue', linestyle='--', linewidth=1.0)

            # Highlight skew patterns
            if self.results and 'skew_patterns' in self.results:
                for pattern in self.results['skew_patterns']:
                    if pattern['type'] == 'high_put_skew':
                        # Highlight area of high put skew
                        ax.axvspan(pattern['strike'] * 0.98, pattern['strike'] * 1.02,
                                  color='red', alpha=0.2)
                        ax.text(pattern['strike'], max(pattern['put_iv'], pattern['call_iv']) * 1.05,
                               f"Put Skew ({pattern['strength']:.2f})",
                               horizontalalignment='center',
                               fontsize=8, color='red')
                    elif pattern['type'] == 'high_call_skew':
                        # Highlight area of high call skew
                        ax.axvspan(pattern['strike'] * 0.98, pattern['strike'] * 1.02,
                                  color='green', alpha=0.2)
                        ax.text(pattern['strike'], max(pattern['put_iv'], pattern['call_iv']) * 1.05,
                               f"Call Skew ({pattern['strength']:.2f})",
                               horizontalalignment='center',
                               fontsize=8, color='green')

            # Customize plot
            ax.set_title('Implied Volatility Skew Analysis', fontsize=12)
            ax.set_xlabel('Strike Price ($)', fontsize=10)
            ax.set_ylabel('Implied Volatility (%)', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.legend(loc='best', fontsize=8)

        except Exception as e:
            self.logger.error(f"Error visualizing IV skew: {str(e)}")
            ax.text(0.5, 0.5, f'Error in IV skew visualization:\n{str(e)}',
                   horizontalalignment='center',
                   verticalalignment='center',
                   transform=ax.transAxes)

    def analyze_term_structure(self):
        """
        Analyze the term structure of implied volatility across different expirations.
        This helps identify expected future liquidity and market sentiment.

        Returns:
            Dictionary with term structure analysis results
        """
        if self.options_data is None or len(self.options_data) == 0:
            self.logger.warning("No options data available for term structure analysis")
            return None

        try:
            # Check if we have expiration data
            expiry_cols = [col for col in self.options_data.columns
                          if 'expiry' in col.lower() or 'expiration' in col.lower()
                          or 'dte' in col.lower() or 'days_to_expiry' in col.lower()]

            if not expiry_cols:
                self.logger.warning("No expiration data found for term structure analysis")
                return None

            # Use the first expiry column found
            expiry_col = expiry_cols[0]
            self.logger.info(f"Using '{expiry_col}' as expiry column for term structure analysis")

            # Ensure strike_price column exists
            strike_col = None
            for col_name in ['strike_price', 'strike', 'strikePrice', 'exercise_price']:
                if col_name in self.options_data.columns:
                    strike_col = col_name
                    break

            if strike_col is None:
                self.logger.error("No strike price column found for term structure analysis")
                return None

            # Create a temporary DataFrame for analysis to avoid modifying the original
            temp_data = self.options_data.copy()

            if strike_col != 'strike_price':
                # Create a copy with standardized column names
                temp_data['strike_price'] = temp_data[strike_col]
                self.logger.info(f"Mapped {strike_col} to strike_price for term structure analysis")

            # Group by expiration and strike
            grouped_data = temp_data.groupby([expiry_col, 'strike_price'])

            # Calculate average IV for each expiration and strike
            term_structure = {}

            for (expiry, strike), group in grouped_data:
                # Get call and put IVs - check for either 'type' or 'option_type' column
                type_col = 'type' if 'type' in group.columns else 'option_type' if 'option_type' in group.columns else None

                if type_col is None:
                    self.logger.warning("Cannot find option type column in term structure analysis")
                    continue

                call_data = group[group[type_col].str.lower() == 'call']
                put_data = group[group[type_col].str.lower() == 'put']

                call_iv = call_data['call_iv'].mean() if 'call_iv' in call_data.columns and not call_data.empty else np.nan
                put_iv = put_data['put_iv'].mean() if 'put_iv' in put_data.columns and not put_data.empty else np.nan

                # Store in term structure
                if expiry not in term_structure:
                    term_structure[expiry] = {'strikes': [], 'call_ivs': [], 'put_ivs': []}

                term_structure[expiry]['strikes'].append(strike)
                term_structure[expiry]['call_ivs'].append(call_iv)
                term_structure[expiry]['put_ivs'].append(put_iv)

            # Sort data for each expiry
            for expiry in term_structure:
                # Sort by strike
                sorted_indices = np.argsort(term_structure[expiry]['strikes'])
                term_structure[expiry]['strikes'] = [term_structure[expiry]['strikes'][i] for i in sorted_indices]
                term_structure[expiry]['call_ivs'] = [term_structure[expiry]['call_ivs'][i] for i in sorted_indices]
                term_structure[expiry]['put_ivs'] = [term_structure[expiry]['put_ivs'][i] for i in sorted_indices]

            # Calculate ATM IV term structure
            atm_term_structure = []
            sorted_expiries = sorted(term_structure.keys())

            for expiry in sorted_expiries:
                strikes = term_structure[expiry]['strikes']
                call_ivs = term_structure[expiry]['call_ivs']
                put_ivs = term_structure[expiry]['put_ivs']

                # Find closest strike to current price
                closest_idx = np.abs(np.array(strikes) - self.current_price).argmin()
                closest_strike = strikes[closest_idx]

                atm_call_iv = call_ivs[closest_idx]
                atm_put_iv = put_ivs[closest_idx]

                # Calculate average ATM IV
                atm_iv = np.nanmean([atm_call_iv, atm_put_iv])

                atm_term_structure.append({
                    'expiry': expiry,
                    'strike': closest_strike,
                    'call_iv': atm_call_iv,
                    'put_iv': atm_put_iv,
                    'avg_iv': atm_iv
                })

            # Detect term structure shape
            if len(atm_term_structure) >= 2:
                # Sort by expiry
                atm_term_structure = sorted(atm_term_structure, key=lambda x: x['expiry'])

                # Calculate differences between consecutive expiries
                iv_diffs = [atm_term_structure[i+1]['avg_iv'] - atm_term_structure[i]['avg_iv']
                           for i in range(len(atm_term_structure)-1)]

                # Determine shape
                if all(diff > 0 for diff in iv_diffs):
                    shape = "upward_sloping"  # Normal market conditions
                elif all(diff < 0 for diff in iv_diffs):
                    shape = "downward_sloping"  # Inverted, often indicates market stress
                elif len(iv_diffs) >= 2 and iv_diffs[0] < 0 and iv_diffs[-1] > 0:
                    shape = "u_shaped"  # Complex market expectations
                elif len(iv_diffs) >= 2 and iv_diffs[0] > 0 and iv_diffs[-1] < 0:
                    shape = "humped"  # Uncertainty in medium term
                else:
                    shape = "mixed"  # No clear pattern
            else:
                shape = "insufficient_data"

            # Store results
            self.term_structure_data = {
                'full_data': term_structure,
                'atm_term_structure': atm_term_structure,
                'shape': shape
            }

            return self.term_structure_data

        except Exception as e:
            self.logger.error(f"Error analyzing term structure: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None
    def generate_volatility_surface(self):
        """
        Generate a volatility surface mapping strikes and expirations to IV values.
        This provides a comprehensive view of market expectations and liquidity.

        Returns:
            Dictionary with volatility surface data
        """
        if self.options_data is None or len(self.options_data) == 0:
            self.logger.warning("No options data available for volatility surface generation")
            return None

        try:
            # Check if we have expiration data
            expiry_cols = [col for col in self.options_data.columns
                          if 'expiry' in col.lower() or 'expiration' in col.lower()
                          or 'dte' in col.lower() or 'days_to_expiry' in col.lower()]

            # Log available columns for debugging
            self.logger.info(f"Available columns for expiry detection: {self.options_data.columns.tolist()}")

            if not expiry_cols:
                self.logger.warning("No expiration data found for volatility surface generation")

                # Try to derive expiration data from other columns
                if 'expiration_date' not in self.options_data.columns and 'expiration' in self.options_data.columns:
                    self.logger.info("Using 'expiration' column as expiration date")
                    self.options_data['expiration_date'] = self.options_data['expiration']
                    expiry_cols = ['expiration_date']
                elif 'DTE' in self.options_data.columns:
                    self.logger.info("Using 'DTE' column as days to expiry")
                    expiry_cols = ['DTE']
                else:
                    # If we still can't find expiration data, create a simplified surface with a single expiration
                    self.logger.info("Creating simplified volatility surface with single expiration")
                    self.options_data['expiration_date'] = pd.Timestamp.now() + pd.Timedelta(days=30)  # Default to 30 days
                    expiry_cols = ['expiration_date']

            # Use the first expiry column found
            expiry_col = expiry_cols[0]
            self.logger.info(f"Using '{expiry_col}' as expiry column for volatility surface")

            # Extract required data - check for either 'type' or 'option_type' column
            type_col = None
            if 'type' in self.options_data.columns:
                type_col = 'type'
            elif 'option_type' in self.options_data.columns:
                type_col = 'option_type'

            # Check for Polygon API format where option_type might be in the ticker symbol
            if type_col is None and 'ticker' in self.options_data.columns:
                self.logger.info("Attempting to extract option type from ticker symbols")
                # Extract option type from ticker (e.g., O:AAPL210917C00145000)
                # C for call, P for put in the format

                # Enhanced pattern matching for Polygon option tickers
                def extract_option_type(ticker):
                    if ticker is None:
                        return None

                    ticker_str = str(ticker).upper()

                    # Standard Polygon format: O:AAPL210917C00145000
                    if 'O:' in ticker_str:
                        # Extract the part after O: which contains the option details
                        option_part = ticker_str.split('O:')[1]

                        # Look for C or P in the option part (typically after date)
                        if 'C' in option_part and not 'P' in option_part:
                            return 'call'
                        elif 'P' in option_part and not 'C' in option_part:
                            return 'put'

                    # Alternative format: AAPL_210917C145
                    elif '_' in ticker_str:
                        parts = ticker_str.split('_')[1]
                        if 'C' in parts and not 'P' in parts:
                            return 'call'
                        elif 'P' in parts and not 'C' in parts:
                            return 'put'

                    # Format with explicit C or P at the end: AAPL210917C
                    elif ticker_str.endswith('C'):
                        return 'call'
                    elif ticker_str.endswith('P'):
                        return 'put'

                    # Format with C or P followed by strike: AAPL210917C145
                    for i, char in enumerate(ticker_str):
                        if char == 'C' and i > 0 and i < len(ticker_str) - 1:
                            # Check if followed by digits (strike price)
                            if ticker_str[i+1].isdigit():
                                return 'call'
                        elif char == 'P' and i > 0 and i < len(ticker_str) - 1:
                            # Check if followed by digits (strike price)
                            if ticker_str[i+1].isdigit():
                                return 'put'

                    # Check for contract_type field in JSON format
                    if isinstance(ticker, dict) and 'contract_type' in ticker:
                        contract_type = str(ticker['contract_type']).upper()
                        if 'CALL' in contract_type or contract_type == 'C':
                            return 'call'
                        elif 'PUT' in contract_type or contract_type == 'P':
                            return 'put'

                    # Last resort: simple check for C or P in the string
                    if 'CALL' in ticker_str:
                        return 'call'
                    elif 'PUT' in ticker_str:
                        return 'put'

                    self.logger.debug(f"Could not determine option type from ticker: {ticker_str}")
                    return None

                self.options_data['type'] = self.options_data['ticker'].apply(extract_option_type)

                if self.options_data['type'].notna().any():
                    type_col = 'type'
                    self.logger.info(f"Successfully extracted option types from {self.options_data['type'].notna().sum()} ticker symbols")

                    # Log the distribution of call/put options
                    call_count = (self.options_data['type'] == 'call').sum()
                    put_count = (self.options_data['type'] == 'put').sum()
                    self.logger.info(f"Extracted {call_count} calls and {put_count} puts from ticker symbols")

            if type_col is None:
                self.logger.warning("Cannot find option type column for volatility surface")
                return None

            # Ensure strike_price column exists
            strike_col = None
            for col_name in ['strike_price', 'strike', 'strikePrice', 'exercise_price']:
                if col_name in self.options_data.columns:
                    strike_col = col_name
                    break

            if strike_col is None:
                self.logger.error("No strike price column found for volatility surface generation")
                return None

            # Create a temporary DataFrame for analysis to avoid modifying the original
            temp_data = self.options_data.copy()

            if strike_col != 'strike_price':
                # Create a copy with standardized column names
                temp_data['strike_price'] = temp_data[strike_col]
                self.logger.info(f"Mapped {strike_col} to strike_price for volatility surface")

            # Create surface data with required columns
            surface_data = temp_data[['strike_price', expiry_col, type_col]].copy()

            # Rename option_type to type for consistency in the rest of the method
            if type_col == 'option_type':
                surface_data.rename(columns={'option_type': 'type'}, inplace=True)

            # Add IV data
            if 'call_iv' in self.options_data.columns and 'put_iv' in self.options_data.columns:
                # Data already has separate call and put IV columns
                surface_data['iv'] = np.nan

                # Check for either 'type' or 'option_type' column
                type_col = None
                if 'type' in surface_data.columns:
                    type_col = 'type'
                elif 'option_type' in surface_data.columns:
                    type_col = 'option_type'

                if type_col is None:
                    self.logger.warning("Cannot find option type column for volatility surface")
                    return None

                call_mask = surface_data[type_col].str.lower() == 'call'
                put_mask = surface_data[type_col].str.lower() == 'put'

                # Use the temp_data DataFrame which has the same indices as surface_data
                surface_data.loc[call_mask, 'iv'] = temp_data.loc[call_mask.index, 'call_iv']
                surface_data.loc[put_mask, 'iv'] = temp_data.loc[put_mask.index, 'put_iv']
            elif 'implied_volatility' in temp_data.columns:
                # Single IV column
                surface_data['iv'] = temp_data['implied_volatility']
            else:
                self.logger.warning("No IV data found for volatility surface generation")
                return None

            # Drop rows with missing IV
            surface_data = surface_data.dropna(subset=['iv'])

            if len(surface_data) == 0:
                self.logger.warning("No valid IV data for volatility surface generation")
                return None

            # Convert expiry to numeric if needed
            if not np.issubdtype(surface_data[expiry_col].dtype, np.number):
                # Try to convert to days to expiry
                try:
                    if isinstance(surface_data[expiry_col].iloc[0], str):
                        # Assume date string format
                        surface_data['dte'] = pd.to_datetime(surface_data[expiry_col]) - datetime.now()
                        surface_data['dte'] = surface_data['dte'].dt.days
                    else:
                        # Assume datetime object
                        surface_data['dte'] = (surface_data[expiry_col] - datetime.now()).dt.days

                    # Replace expiry_col with dte for surface generation
                    expiry_col = 'dte'
                except Exception as e:
                    self.logger.warning(f"Could not convert expiry to numeric: {str(e)}")
                    # Create a mapping of unique expiries to numeric values
                    unique_expiries = surface_data[expiry_col].unique()
                    expiry_map = {exp: i for i, exp in enumerate(sorted(unique_expiries))}
                    surface_data['expiry_numeric'] = surface_data[expiry_col].map(expiry_map)
                    expiry_col = 'expiry_numeric'

            # Group by strike, expiry and option type to get average IV
            grouped = surface_data.groupby(['strike_price', expiry_col, 'type'])['iv'].mean().reset_index()

            # Create separate surfaces for calls and puts
            call_data = grouped[grouped['type'] == 'call']
            put_data = grouped[grouped['type'] == 'put']

            # Prepare grid for interpolation
            strikes = sorted(grouped['strike_price'].unique())
            expiries = sorted(grouped[expiry_col].unique())

            # Create meshgrid for surface
            strike_grid, expiry_grid = np.meshgrid(strikes, expiries)

            # Initialize IV grids
            call_iv_grid = np.zeros_like(strike_grid) * np.nan
            put_iv_grid = np.zeros_like(strike_grid) * np.nan

            # Fill in known points
            for _, row in call_data.iterrows():
                strike_idx = strikes.index(row['strike_price'])
                expiry_idx = expiries.index(row[expiry_col])
                call_iv_grid[expiry_idx, strike_idx] = row['iv']

            for _, row in put_data.iterrows():
                strike_idx = strikes.index(row['strike_price'])
                expiry_idx = expiries.index(row[expiry_col])
                put_iv_grid[expiry_idx, strike_idx] = row['iv']

            # Interpolate missing values
            try:
                from scipy.interpolate import griddata

                # Get non-NaN points for interpolation
                call_points = []
                call_values = []
                for i in range(len(expiries)):
                    for j in range(len(strikes)):
                        if not np.isnan(call_iv_grid[i, j]):
                            call_points.append([expiries[i], strikes[j]])
                            call_values.append(call_iv_grid[i, j])

                put_points = []
                put_values = []
                for i in range(len(expiries)):
                    for j in range(len(strikes)):
                        if not np.isnan(put_iv_grid[i, j]):
                            put_points.append([expiries[i], strikes[j]])
                            put_values.append(put_iv_grid[i, j])

                # Log the number of data points available for interpolation
                self.logger.info(f"Volatility surface interpolation: {len(call_points)} call points, {len(put_points)} put points")

                # Check if we have enough data for interpolation
                if len(call_points) < 4 and len(put_points) < 4:
                    self.logger.warning("Insufficient data points for volatility surface interpolation")
                    self.logger.info("Using simple nearest-neighbor interpolation instead")

                    # Use a simpler approach with nearest-neighbor interpolation
                    call_iv_grid_interp = call_iv_grid.copy()
                    put_iv_grid_interp = put_iv_grid.copy()

                    # Fill missing values with nearest available value
                    for i in range(len(expiries)):
                        for j in range(len(strikes)):
                            # For call IVs
                            if np.isnan(call_iv_grid[i, j]) and len(call_points) > 0:
                                # Find nearest point
                                dists = [(expiries[i] - p[0])**2 + (strikes[j] - p[1])**2 for p in call_points]
                                nearest_idx = np.argmin(dists)
                                call_iv_grid_interp[i, j] = call_values[nearest_idx]

                            # For put IVs
                            if np.isnan(put_iv_grid[i, j]) and len(put_points) > 0:
                                # Find nearest point
                                dists = [(expiries[i] - p[0])**2 + (strikes[j] - p[1])**2 for p in put_points]
                                nearest_idx = np.argmin(dists)
                                put_iv_grid_interp[i, j] = put_values[nearest_idx]
                else:
                    # Only interpolate if we have enough points
                    if len(call_points) > 3:
                        try:
                            call_iv_grid_interp = griddata(
                                np.array(call_points),
                                np.array(call_values),
                                (expiry_grid, strike_grid),
                                method='cubic',
                                fill_value=np.nan
                            )

                            # Fall back to linear for any remaining NaNs
                            mask = np.isnan(call_iv_grid_interp)
                            if np.any(mask) and len(call_points) > 2:
                                call_iv_grid_interp[mask] = griddata(
                                    np.array(call_points),
                                    np.array(call_values),
                                    (expiry_grid[mask], strike_grid[mask]),
                                    method='linear',
                                    fill_value=np.nan
                                )
                        except Exception as e:
                            self.logger.warning(f"Error in call IV interpolation: {str(e)}")
                            call_iv_grid_interp = call_iv_grid
                    else:
                        call_iv_grid_interp = call_iv_grid

                    if len(put_points) > 3:
                        try:
                            put_iv_grid_interp = griddata(
                                np.array(put_points),
                                np.array(put_values),
                                (expiry_grid, strike_grid),
                                method='cubic',
                                fill_value=np.nan
                            )

                            # Fall back to linear for any remaining NaNs
                            mask = np.isnan(put_iv_grid_interp)
                            if np.any(mask) and len(put_points) > 2:
                                put_iv_grid_interp[mask] = griddata(
                                    np.array(put_points),
                                    np.array(put_values),
                                    (expiry_grid[mask], strike_grid[mask]),
                                    method='linear',
                                    fill_value=np.nan
                                )
                        except Exception as e:
                            self.logger.warning(f"Error in put IV interpolation: {str(e)}")
                            put_iv_grid_interp = put_iv_grid
                    else:
                        put_iv_grid_interp = put_iv_grid

            except ImportError:
                self.logger.warning("scipy.interpolate not available, using raw IV grids")
                call_iv_grid_interp = call_iv_grid
                put_iv_grid_interp = put_iv_grid
            except Exception as e:
                self.logger.error(f"Error during volatility surface interpolation: {str(e)}")
                call_iv_grid_interp = call_iv_grid
                put_iv_grid_interp = put_iv_grid

            # Calculate skew surface (put IV - call IV)
            skew_surface = put_iv_grid_interp - call_iv_grid_interp

            # Store results
            self.volatility_surface = {
                'strikes': strikes,
                'expiries': expiries,
                'expiry_col': expiry_col,
                'strike_grid': strike_grid,
                'expiry_grid': expiry_grid,
                'call_iv_surface': call_iv_grid_interp,
                'put_iv_surface': put_iv_grid_interp,
                'skew_surface': skew_surface
            }

            return self.volatility_surface

        except Exception as e:
            self.logger.error(f"Error generating volatility surface: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None
    def calculate_skew_ratio(self):
        """
        Calculate the put/call volatility skew ratio, which is a strong indicator
        of market sentiment and potential liquidity levels.

        Returns:
            Dictionary with skew ratio analysis results
        """
        if self.options_data is None or len(self.options_data) == 0:
            self.logger.warning("No options data available for skew ratio calculation")
            return None

        try:
            # Check if we have the necessary IV data
            if 'call_iv' not in self.options_data.columns or 'put_iv' not in self.options_data.columns:
                # Try to prepare data if not already done
                if not self._prepare_data():
                    self.logger.warning("Cannot calculate skew ratio without IV data")
                    return None

            # Ensure we have skew data
            if self.skew_data is None:
                self.logger.warning("No skew data available for ratio calculation")
                return None

            # Get strikes and IVs
            strikes = sorted(self.skew_data['strikes'])
            call_ivs = [self.skew_data['call_ivs'].get(strike, np.nan) for strike in strikes]
            put_ivs = [self.skew_data['put_ivs'].get(strike, np.nan) for strike in strikes]

            # Convert to numpy arrays
            strikes_arr = np.array(strikes)
            call_ivs_arr = np.array(call_ivs)
            put_ivs_arr = np.array(put_ivs)

            # Calculate moneyness (strike / current_price)
            moneyness = strikes_arr / self.current_price

            # Calculate moneyness levels for analysis (no need to store ATM index)

            # Calculate skew ratios at different moneyness levels
            skew_ratios = []

            # Define moneyness levels to analyze
            moneyness_levels = [0.90, 0.95, 1.0, 1.05, 1.10]

            for level in moneyness_levels:
                # Find closest strike to this moneyness level
                idx = np.abs(moneyness - level).argmin()

                if idx < len(strikes_arr) and not np.isnan(call_ivs_arr[idx]) and not np.isnan(put_ivs_arr[idx]):
                    ratio = put_ivs_arr[idx] / call_ivs_arr[idx]

                    skew_ratios.append({
                        'moneyness': level,
                        'strike': strikes_arr[idx],
                        'put_iv': put_ivs_arr[idx],
                        'call_iv': call_ivs_arr[idx],
                        'ratio': ratio,
                        'is_significant': abs(ratio - 1.0) > self.config.get('skew_ratio_threshold', 0.2)
                    })

            # Calculate overall skew ratio (average of OTM puts to OTM calls)
            otm_put_indices = np.where((moneyness < 1.0) & [PARTIAL]np.isnan(put_ivs_arr))[0]
            otm_call_indices = np.where((moneyness > 1.0) & [PARTIAL]np.isnan(call_ivs_arr))[0]

            if len(otm_put_indices) > 0 and len(otm_call_indices) > 0:
                avg_otm_put_iv = np.mean(put_ivs_arr[otm_put_indices])
                avg_otm_call_iv = np.mean(call_ivs_arr[otm_call_indices])
                overall_ratio = avg_otm_put_iv / avg_otm_call_iv
            else:
                overall_ratio = np.nan

            # Determine market sentiment based on skew ratio
            if not np.isnan(overall_ratio):
                if overall_ratio > 1.3:
                    sentiment = "bearish"  # High put premium indicates fear
                elif overall_ratio < 0.7:
                    sentiment = "bullish"  # Low put premium indicates complacency
                else:
                    sentiment = "neutral"
            else:
                sentiment = "unknown"

            # Calculate liquidity implications
            liquidity_levels = []

            # Check for significant skew ratio changes across strikes
            for i in range(1, len(skew_ratios)):
                prev_ratio = skew_ratios[i-1]['ratio']
                curr_ratio = skew_ratios[i]['ratio']

                # Detect significant changes in skew ratio
                if abs(curr_ratio - prev_ratio) > self.config.get('skew_ratio_threshold', 0.2):
                    # Significant change in skew ratio indicates potential liquidity level
                    strike_mid = (skew_ratios[i]['strike'] + skew_ratios[i-1]['strike']) / 2

                    if curr_ratio > prev_ratio:
                        # Increasing put/call ratio often indicates support
                        liquidity_levels.append({
                            'price': strike_mid,
                            'type': 'support',
                            'strength': min(1.0, abs(curr_ratio - prev_ratio) / 0.5),
                            'source': 'skew_ratio',
                            'details': {
                                'lower_strike': skew_ratios[i-1]['strike'],
                                'upper_strike': skew_ratios[i]['strike'],
                                'lower_ratio': prev_ratio,
                                'upper_ratio': curr_ratio
                            }
                        })
                    else:
                        # Decreasing put/call ratio often indicates resistance
                        liquidity_levels.append({
                            'price': strike_mid,
                            'type': 'resistance',
                            'strength': min(1.0, abs(curr_ratio - prev_ratio) / 0.5),
                            'source': 'skew_ratio',
                            'details': {
                                'lower_strike': skew_ratios[i-1]['strike'],
                                'upper_strike': skew_ratios[i]['strike'],
                                'lower_ratio': prev_ratio,
                                'upper_ratio': curr_ratio
                            }
                        })

            # Store results
            self.skew_ratio_data = {
                'ratios': skew_ratios,
                'overall_ratio': overall_ratio,
                'sentiment': sentiment,
                'liquidity_levels': liquidity_levels
            }

            return self.skew_ratio_data

        except Exception as e:
            self.logger.error(f"Error calculating skew ratio: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None
    def detect_volatility_anomalies(self):
        """
        Detect anomalies in the volatility surface that might indicate
        institutional positioning or potential liquidity traps.

        Returns:
            Dictionary with volatility anomaly detection results
        """
        if self.options_data is None or len(self.options_data) == 0:
            self.logger.warning("No options data available for volatility anomaly detection")
            return None

        try:
            # Check if we have volatility surface data
            if self.volatility_surface is None:
                # Generate volatility surface if not already done
                if self.generate_volatility_surface() is None:
                    self.logger.warning("Cannot detect anomalies without volatility surface")
                    return None

            # Extract data from volatility surface
            strikes = self.volatility_surface['strikes']
            expiries = self.volatility_surface['expiries']
            call_iv_surface = self.volatility_surface['call_iv_surface']
            put_iv_surface = self.volatility_surface['put_iv_surface']
            skew_surface = self.volatility_surface['skew_surface']

            # Initialize anomalies list
            anomalies = []

            # Calculate z-scores for call and put IV surfaces
            from scipy.stats import zscore

            # Flatten arrays and remove NaNs for z-score calculation
            call_iv_flat = call_iv_surface.flatten()
            put_iv_flat = put_iv_surface.flatten()
            skew_flat = skew_surface.flatten()

            call_iv_valid = call_iv_flat[[PARTIAL]np.isnan(call_iv_flat)]
            put_iv_valid = put_iv_flat[[PARTIAL]np.isnan(put_iv_flat)]
            skew_valid = skew_flat[[PARTIAL]np.isnan(skew_flat)]

            if len(call_iv_valid) > 0:
                call_iv_mean = np.mean(call_iv_valid)
                call_iv_std = np.std(call_iv_valid)
            else:
                call_iv_mean = 0
                call_iv_std = 0

            if len(put_iv_valid) > 0:
                put_iv_mean = np.mean(put_iv_valid)
                put_iv_std = np.std(put_iv_valid)
            else:
                put_iv_mean = 0
                put_iv_std = 0

            if len(skew_valid) > 0:
                skew_mean = np.mean(skew_valid)
                skew_std = np.std(skew_valid)
            else:
                skew_mean = 0
                skew_std = 0

            # Threshold for anomaly detection
            threshold = self.config.get('anomaly_threshold', 2.0)

            # Detect anomalies in call IV surface
            for i in range(len(expiries)):
                for j in range(len(strikes)):
                    call_iv = call_iv_surface[i, j]
                    put_iv = put_iv_surface[i, j]
                    skew_val = skew_surface[i, j]

                    if np.isnan(call_iv) or np.isnan(put_iv) or np.isnan(skew_val):
                        continue

                    # Calculate z-scores
                    if call_iv_std > 0:
                        call_z = (call_iv - call_iv_mean) / call_iv_std
                    else:
                        call_z = 0

                    if put_iv_std > 0:
                        put_z = (put_iv - put_iv_mean) / put_iv_std
                    else:
                        put_z = 0

                    if skew_std > 0:
                        skew_z = (skew_val - skew_mean) / skew_std
                    else:
                        skew_z = 0

                    # Check for anomalies
                    is_anomaly = False
                    anomaly_type = ""

                    if abs(call_z) > threshold:
                        is_anomaly = True
                        anomaly_type = "call_iv"
                    elif abs(put_z) > threshold:
                        is_anomaly = True
                        anomaly_type = "put_iv"
                    elif abs(skew_z) > threshold:
                        is_anomaly = True
                        anomaly_type = "skew"

                    if is_anomaly:
                        anomalies.append({
                            'strike': strikes[j],
                            'expiry': expiries[i],
                            'call_iv': call_iv,
                            'put_iv': put_iv,
                            'skew': skew_val,
                            'call_z': call_z,
                            'put_z': put_z,
                            'skew_z': skew_z,
                            'type': anomaly_type,
                            'strength': max(abs(call_z), abs(put_z), abs(skew_z)) / threshold
                        })

            # Sort anomalies by strength
            anomalies = sorted(anomalies, key=lambda x: x['strength'], reverse=True)

            # Convert anomalies to liquidity levels
            liquidity_levels = []

            for anomaly in anomalies:
                # Determine if this is likely support or resistance
                if anomaly['type'] == 'call_iv' and anomaly['call_z'] > 0:
                    # Unusually high call IV often indicates resistance
                    level_type = 'resistance'
                elif anomaly['type'] == 'put_iv' and anomaly['put_z'] > 0:
                    # Unusually high put IV often indicates support
                    level_type = 'support'
                elif anomaly['type'] == 'skew' and anomaly['skew_z'] > 0:
                    # Unusually high skew (put IV > call IV) often indicates support
                    level_type = 'support'
                elif anomaly['type'] == 'skew' and anomaly['skew_z'] < 0:
                    # Unusually low skew (call IV > put IV) often indicates resistance
                    level_type = 'resistance'
                else:
                    # Default to neutral if unclear
                    level_type = 'neutral'

                liquidity_levels.append({
                    'price': anomaly['strike'],
                    'type': level_type,
                    'strength': min(1.0, anomaly['strength'] / 2),
                    'source': 'volatility_anomaly',
                    'details': {
                        'expiry': anomaly['expiry'],
                        'call_iv': anomaly['call_iv'],
                        'put_iv': anomaly['put_iv'],
                        'skew': anomaly['skew'],
                        'anomaly_type': anomaly['type']
                    }
                })

            # Store results
            self.volatility_anomalies = {
                'anomalies': anomalies,
                'liquidity_levels': liquidity_levels
            }

            return self.volatility_anomalies

        except Exception as e:
            self.logger.error(f"Error detecting volatility anomalies: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return None