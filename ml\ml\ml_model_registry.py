"""
Model Registry for ML Models

This module provides a registry for storing and retrieving ML models.
"""

import os
import json
import pickle
import logging
from typing import Dict, Any, Tuple, Optional, List
from datetime import datetime

# Set up logging
logger = logging.getLogger(__name__)

class ModelRegistry:
    """
    Registry for storing and retrieving ML models.
    
    This registry handles model versioning, metadata, and serialization.
    """
    
    def __init__(self, base_dir: str = None):
        """
        Initialize the model registry.
        
        Args:
            base_dir: Base directory for storing models
        """
        self.base_dir = base_dir or os.path.join(os.path.dirname(os.path.abspath(__file__)), "models")
        os.makedirs(self.base_dir, exist_ok=True)
        
        # Cache of loaded models
        self.model_cache = {}
    
    def register_model(self, 
                      model: Any, 
                      model_name: str,
                      model_type: str,
                      metadata: Dict[str, Any] = None,
                      version: str = None) -> str:
        """
        Register a model in the registry.
        
        Args:
            model: Model object to register
            model_name: Name of the model
            model_type: Type of the model
            metadata: Additional metadata
            version: Version string (default: timestamp)
            
        Returns:
            Model ID
        """
        # Create version if not provided
        version = version or datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create model ID
        model_id = f"{model_name}_{version}"
        
        # Create metadata
        metadata = metadata or {}
        metadata.update({
            "model_name": model_name,
            "model_type": model_type,
            "version": version,
            "created_at": datetime.now().isoformat(),
        })
        
        # Create model directory
        model_dir = os.path.join(self.base_dir, model_name)
        os.makedirs(model_dir, exist_ok=True)
        
        # Save model
        model_path = os.path.join(model_dir, f"{model_id}.pkl")
        with open(model_path, "wb") as f:
            pickle.dump(model, f)
        
        # Save metadata
        metadata_path = os.path.join(model_dir, f"{model_id}.json")
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        
        logger.info(f"Registered model {model_id}")
        return model_id
    
    def load_model(self, model_id: str, model_type: str = None) -> Tuple[Any, Dict[str, Any]]:
        """
        Load a model from the registry.
        
        Args:
            model_id: ID of the model to load
            model_type: Type of the model (for verification)
            
        Returns:
            Tuple of (model, metadata)
        """
        # Check cache
        if model_id in self.model_cache:
            return self.model_cache[model_id]
        
        # Parse model ID to get model name and version
        parts = model_id.split("_")
        model_name = parts[0]
        
        # Find model file
        model_dir = os.path.join(self.base_dir, model_name)
        model_path = os.path.join(model_dir, f"{model_id}.pkl")
        metadata_path = os.path.join(model_dir, f"{model_id}.json")
        
        # Check if model exists
        if not os.path.exists(model_path) or not os.path.exists(metadata_path):
            raise ValueError(f"Model {model_id} not found")
        
        # Load model
        with open(model_path, "rb") as f:
            model = pickle.load(f)
        
        # Load metadata
        with open(metadata_path, "r") as f:
            metadata = json.load(f)
        
        # Verify model type if provided
        if model_type and metadata.get("model_type") != model_type:
            raise ValueError(f"Model {model_id} is not of type {model_type}")
        
        # Cache model
        self.model_cache[model_id] = (model, metadata)
        
        logger.info(f"Loaded model {model_id}")
        return model, metadata
    
    def list_models(self, model_name: str = None, model_type: str = None) -> List[Dict[str, Any]]:
        """
        List models in the registry.
        
        Args:
            model_name: Filter by model name
            model_type: Filter by model type
            
        Returns:
            List of model metadata
        """
        models = []
        
        # Get model directories
        if model_name:
            model_dirs = [os.path.join(self.base_dir, model_name)]
        else:
            model_dirs = [os.path.join(self.base_dir, d) for d in os.listdir(self.base_dir) 
                         if os.path.isdir(os.path.join(self.base_dir, d))]
        
        # Iterate through model directories
        for model_dir in model_dirs:
            if not os.path.exists(model_dir):
                continue
                
            # Get metadata files
            metadata_files = [f for f in os.listdir(model_dir) if f.endswith(".json")]
            
            # Load metadata
            for metadata_file in metadata_files:
                metadata_path = os.path.join(model_dir, metadata_file)
                
                try:
                    with open(metadata_path, "r") as f:
                        metadata = json.load(f)
                    
                    # Filter by model type
                    if model_type and metadata.get("model_type") != model_type:
                        continue
                    
                    models.append(metadata)
                except Exception as e:
                    logger.error(f"Error loading metadata {metadata_path}: {e}")
        
        # Sort by creation time (newest first)
        models.sort(key=lambda m: m.get("created_at", ""), reverse=True)
        
        return models
    
    def get_latest_model(self, model_name: str, model_type: str = None) -> Optional[str]:
        """
        Get the latest version of a model.
        
        Args:
            model_name: Name of the model
            model_type: Type of the model
            
        Returns:
            Model ID or None if not found
        """
        models = self.list_models(model_name, model_type)
        
        if not models:
            return None
        
        return models[0].get("model_name") + "_" + models[0].get("version")
    
    def delete_model(self, model_id: str) -> bool:
        """
        Delete a model from the registry.
        
        Args:
            model_id: ID of the model to delete
            
        Returns:
            True if successful, False otherwise
        """
        # Parse model ID to get model name
        parts = model_id.split("_")
        model_name = parts[0]
        
        # Find model files
        model_dir = os.path.join(self.base_dir, model_name)
        model_path = os.path.join(model_dir, f"{model_id}.pkl")
        metadata_path = os.path.join(model_dir, f"{model_id}.json")
        
        # Check if model exists
        if not os.path.exists(model_path) or not os.path.exists(metadata_path):
            logger.warning(f"Model {model_id} not found")
            return False
        
        # Remove from cache
        if model_id in self.model_cache:
            del self.model_cache[model_id]
        
        # Delete files
        try:
            os.remove(model_path)
            os.remove(metadata_path)
            logger.info(f"Deleted model {model_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting model {model_id}: {e}")
            return False


# Global registry instance
_registry = None

def get_model_registry() -> ModelRegistry:
    """
    Get the global model registry.
    
    Returns:
        Model registry
    """
    global _registry
    
    if _registry is None:
        _registry = ModelRegistry()
    
    return _registry
