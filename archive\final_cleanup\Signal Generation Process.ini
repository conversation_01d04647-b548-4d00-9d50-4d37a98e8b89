Signal Generation Process
 1. DATA INGESTION
    MCP API  Real-time Data Fetch
    Data Structure  Standardization
    MTF Aggregator  Timeframe Alignment

 2. PARA<PERSON>EL ANALYSIS (4 Cores)
    Flow Physics  Derivatives Calculation
    Sweep Analyzer  Pattern Detection
    Volume Analyzer  Profile Analysis
    GEX Analyzer  Gamma Analysis

 3. FACTOR VALIDATION
    Quality Check  Data integrity
    Strength Filter  Minimum thresholds
    Direction Assessment  Bias determination

 4. CONFLUENCE EVALUATION
    Agreement Count  3 of 4 minimum
    Strength Assessment  Factor importance
    Confidence Calculation  Signal strength

 5. ML ENHANCEMENT
    Pattern Matching  Historical context
    Timing Estimation  Phase analysis
    Threshold Adaptation  Dynamic calibration

 6. VALIDATION CHARTS (3)
    Volume + Flow Chart  Script accuracy check
    Liquidity + GEX Chart  Level confirmation
    Confluence Dashboard  Factor verification

 7. UNIFIED REPORT
    All analysis combined  Single report
    Action summary  Clear direction
    Confidence ranges  Risk assessment

 8. SIGNAL OUTPUT
     Direction  BULLISH/BEARISH/NEUTRAL
     Confidence Range  70-85%
     Timing Range  5-8 days
     Supporting Evidence  Factor list