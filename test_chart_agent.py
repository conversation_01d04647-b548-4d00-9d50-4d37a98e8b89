#!/usr/bin/env python3
"""
Test Chart Generator Agent Execute Method
"""

import sys
import traceback
import pandas as pd
import numpy as np
from datetime import datetime

def test_chart_agent():
    """Test chart agent execute method"""
    print("Testing Chart Generator Agent...")
    
    try:
        from agents.chart_generator_agent import ChartGeneratorAgent
        
        # Create agent
        chart_agent = ChartGeneratorAgent('test_chart_agent')
        
        # Create test data
        dates = pd.date_range(start='2024-01-01', periods=100, freq='15min')
        test_data = pd.DataFrame({
            'timestamp': dates,
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(110, 120, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.uniform(1000000, 5000000, 100)
        })
        
        # Create chart data
        chart_data = {
            'ticker': 'MSFT',
            'market_data': {
                'current_price': 105.0,
                'mtf_data': {
                    '15m': test_data
                }
            },
            'confluence_result': {
                'confluence_probability': 0.75,
                'signal_strength': 0.80,
                'direction': 'bullish'
            },
            'analysis_factors': {
                'flow_analysis': {'strength': 0.7},
                'volume_analysis': {'strength': 0.8}
            },
            'specialized_army': {
                'fvg_analysis': {'signal': 'bullish'},
                'mean_reversion': {'signal': 'neutral'}
            }
        }
        
        # Test execute method
        result = chart_agent.execute(chart_data)
        
        print(f"✅ Chart Agent execute successful")
        print(f"   Result type: {type(result)}")
        if isinstance(result, dict):
            print(f"   Keys: {list(result.keys())}")
            
            # Check for hard-coded data loading messages
            if 'error' in result:
                error_msg = result['error']
                if 'loaded 1' in error_msg.lower() or '1 price bar' in error_msg.lower():
                    print(f"⚠️  HARD-CODED DATA DETECTED: {error_msg}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"❌ Chart Agent failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("CHART GENERATOR AGENT TEST")
    print("=" * 40)
    
    success = test_chart_agent()
    
    if success:
        print("\n🎉 Chart Agent test passed!")
        return 0
    else:
        print("\n❌ Chart Agent test failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
