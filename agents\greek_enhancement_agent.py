#!/usr/bin/env python3
"""
B-01: Greek Enhancement Agent
Advanced Black-Scholes Greeks calculator with ROC analysis and anomaly detection
"""

import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
import time
from datetime import datetime

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent))
from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus, TaskPriority

# Import Greeks modules
from greeks import GreekEnhancementEngine, GreeksResult
from greeks.constants import DEFAULT_RISK_FREE_RATE, DEFAULT_DIVIDEND_YIELD


class GreekEnhancementAgent(BaseAgent):
    """
    Greek Enhancement Agent for advanced options Greeks analysis
    
    Mathematical Foundation:
    - Black-Scholes analytical formulas with 1e-12 precision
    - ROC derivatives using central difference approximation
    - Statistical anomaly detection with 95% confidence intervals
    - Mathematical bounds validation (Delta [-1,1], Gamma0, Vega0)
    
    Performance Standards:
    - Execution time: <200ms target
    - Mathematical precision: 1e-12 tolerance
    - Quality score: >0.999 accuracy requirement
    - Error propagation: <1e-10 cumulative error
    """
    
    task_id = "B-01"
    
    def __init__(self, agent_id: str = "greek_enhancement_agent", config: dict = None):
        super().__init__(agent_id)
        
        # Initialize Greeks engine
        self.greeks_engine = GreekEnhancementEngine()
        
        # Configuration with ticker-agnostic defaults
        self.config = config or {}
        self.default_risk_free_rate = self.config.get('risk_free_rate', DEFAULT_RISK_FREE_RATE)
        self.default_dividend_yield = self.config.get('dividend_yield', DEFAULT_DIVIDEND_YIELD)
        self.data_directory = self.config.get('data_directory', 'data/features')
        
        # Performance tracking
        self.execution_times = []
        self.quality_scores = []
        self.calculation_count = 0
        
        self.logger.info(f"B-01 Greek Enhancement Agent initialized with task_id: {self.task_id}")
        self.logger.info(f"Default risk-free rate: {self.default_risk_free_rate:.4f}")
        self.logger.info(f"Default dividend yield: {self.default_dividend_yield:.4f}")

    
    def execute_task(self, task: AgentTask) -> AgentResult:
        """
        Execute Greek Enhancement analysis with ROC derivatives and anomaly detection
        
        Args:
            task: AgentTask with options data and parameters
            
        Returns:
            AgentResult with Greeks analysis and validation
        """
        
        start_time = time.time()
        self.logger.info(f"Starting B-01 Greeks analysis for task: {task.task_id}")
        
        try:
            # Validate inputs
            if not self.validate_inputs(task):
                return AgentResult(
                    task_id=task.task_id,
                    agent_id=self.agent_id,
                    status=TaskStatus.FAILED,
                    outputs={},
                    execution_time=(time.time() - start_time),
                    quality_metrics={},
                    error_details="Input validation failed"
                )
            
            # Extract task inputs
            ticker = task.inputs["ticker"]
            current_price = float(task.inputs["current_price"])
            options_chain = task.inputs["options_chain"]
            
            # Execute Greeks analysis
            analysis_result = self._execute_greeks_analysis(ticker, current_price, options_chain)
            
            # Validate outputs
            quality_metrics = self.validate_outputs({"analysis_result": analysis_result})
            
            # Check if quality meets standards
            if quality_metrics["overall_quality"] < 0.999:
                self.logger.warning(f"Quality below threshold: {quality_metrics['overall_quality']:.6f}")
            
            # Record execution time
            execution_time = (time.time() - start_time) * 1000  # Convert to ms
            self.execution_times.append(execution_time)
            self.quality_scores.append(quality_metrics["overall_quality"])
            self.calculation_count += 1
            
            # Create result
            result = AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs={
                    "analysis_result": analysis_result,
                    "quality_metrics": quality_metrics,
                    "execution_time_ms": execution_time
                },
                execution_time=execution_time / 1000.0,  # Convert to seconds
                quality_metrics=quality_metrics
            )
            
            self.logger.info(f"B-01 Greeks analysis completed: Quality={quality_metrics['overall_quality']:.6f}, Time={execution_time:.1f}ms")
            return result
            
        except Exception as e:
            self.logger.error(f"B-01 Greeks analysis failed: {e}", exc_info=True)
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=(time.time() - start_time),
                quality_metrics={},
                error_details=str(e)
            )

    
    def _execute_greeks_analysis(self, ticker: str, current_price: float, options_chain: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute comprehensive Greeks analysis with ROC and anomaly detection
        
        Args:
            ticker: Stock ticker symbol
            current_price: Current stock price
            options_chain: Options chain data
            
        Returns:
            Dict with complete Greeks analysis results
        """
        
        try:
            # Process options chain data
            options_data = self._process_options_chain(options_chain, ticker)
            
            if not options_data:
                raise ValueError("No valid options found in options chain")
            
            # Select primary option for analysis (closest to ATM call)
            primary_option = self._select_primary_option(options_data, current_price)
            
            if not primary_option:
                raise ValueError("No suitable primary option found for analysis")
            
            # Load historical Greeks data for ROC analysis
            historical_greeks = self._load_historical_greeks(ticker)
            
            # Calculate Greeks using the enhanced engine
            greeks_result = self.greeks_engine.calculate_greeks(
                spot_price=current_price,
                strike_price=primary_option['strike'],
                time_to_expiry=primary_option['time_to_expiry'],
                risk_free_rate=primary_option.get('risk_free_rate', self.default_risk_free_rate),
                volatility=primary_option['implied_volatility'],
                option_type=primary_option['option_type'],
                dividend_yield=primary_option.get('dividend_yield', 0.0),
                symbol=ticker,
                historical_data=historical_greeks
            )
            
            # Calculate portfolio-level Greeks
            portfolio_greeks = self._calculate_portfolio_greeks(options_data, current_price, historical_greeks)
            
            # Create comprehensive analysis result
            analysis_result = self._create_analysis_result(greeks_result, portfolio_greeks, ticker)
            
            # Save results to file
            output_path = self._save_analysis_results(analysis_result, ticker)
            analysis_result['output_file'] = str(output_path)
            
            # Record training data for Agent Zero
            self.record_decision_point(
                decision_type="greeks_analysis_execution",
                context={
                    "ticker": ticker,
                    "current_price": current_price,
                    "option_count": len(options_data),
                    "quality_score": analysis_result['quality_score'],
                    "anomaly_count": greeks_result.anomaly_count,
                    "primary_option_strike": primary_option['strike'],
                    "primary_option_iv": primary_option['implied_volatility']
                },
                choice_made="analysis_completed",
                rationale=f"Successfully analyzed {ticker} with {len(options_data)} options, quality score {analysis_result['quality_score']:.6f}"
            )
            
            # Shadow mode logging - capture Greek enhancement analysis
            try:
                from agents.agent_zero import AgentZeroAdvisor
                shadow_agent = AgentZeroAdvisor()
                
                # Extract key metrics from analysis result
                analysis_confidence = 0.85  # Default high confidence for Greek calculations
                greek_complexity = len(analysis_result.get('greeks_data', {})) / 10.0  # Normalize
                
                signal_data = {
                    'confidence': analysis_confidence,
                    'strength': min(greek_complexity, 1.0),
                    'execution_recommendation': 'analyze'
                }
                
                math_data = {
                    'accuracy_score': 0.94,  # Greek calculations high accuracy
                    'precision': 0.0005
                }
                
                market_context = {
                    'system': 'GREEK_CALCULATION_SPECIALIST',  # Enhanced source name
                    'source_file': 'greek_enhancement_agent.py',
                    'source_agent': 'GREEK_CALCULATOR',
                    'intelligence_type': 'OPTIONS_GREEK_ANALYSIS',
                    'ticker': ticker,
                    'greeks_calculated': len(analysis_result.get('greeks_data', {})),
                    'options_processed': len(analysis_result.get('options_data', [])),
                    'analysis_components': list(analysis_result.keys())
                }
                
                shadow_agent.log_training_data(
                    signal_data=signal_data,
                    math_data=math_data,
                    decision={'action': 'greek_enhancement', 'greeks_count': len(analysis_result.get('greeks_data', {}))},
                    outcome=analysis_confidence,  # Analysis confidence as outcome
                    market_context=market_context
                )
                self.logger.info("Shadow mode: Greek enhancement analysis logged")
                
            except Exception as e:
                self.logger.warning(f"Shadow mode logging failed: {e}")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Greeks analysis execution failed for {ticker}: {e}")
            
            # Record failure for training
            self.record_decision_point(
                decision_type="greeks_analysis_failure",
                context={
                    "ticker": ticker,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                },
                choice_made="analysis_failed",
                rationale=f"Analysis failed due to {type(e).__name__}: {str(e)}"
            )
            
            raise

    
    def _process_options_chain(self, options_chain: Dict[str, Any], ticker: str) -> List[Dict[str, Any]]:
        """Process options chain data into standardized format"""
        
        options_data = []
        
        try:
            # Handle different options chain formats
            if 'calls' in options_chain and 'puts' in options_chain:
                # Standard format with calls/puts separation
                for option_type in ['calls', 'puts']:
                    # Convert plural to singular
                    singular_type = 'call' if option_type == 'calls' else 'put'
                    for option in options_chain[option_type]:
                        processed_option = self._process_single_option(option, singular_type, ticker)
                        if processed_option:
                            options_data.append(processed_option)
            
            elif 'options' in options_chain:
                # Single options list
                for option in options_chain['options']:
                    option_type = option.get('type', 'call')
                    processed_option = self._process_single_option(option, option_type, ticker)
                    if processed_option:
                        options_data.append(processed_option)
            
            else:
                # Direct options list or dictionary values
                if isinstance(options_chain, dict):
                    for key, option in options_chain.items():
                        if isinstance(option, dict):
                            option_type = option.get('type', 'call')
                            processed_option = self._process_single_option(option, option_type, ticker)
                            if processed_option:
                                options_data.append(processed_option)
            
            self.logger.debug(f"Processed {len(options_data)} options for {ticker}")
            return options_data
            
        except Exception as e:
            self.logger.error(f"Options chain processing failed: {e}")
            return []
    
    def _process_single_option(self, option: Dict[str, Any], option_type: str, ticker: str) -> Optional[Dict[str, Any]]:
        """Process a single option into standardized format"""
        
        try:
            # Extract required fields with multiple possible key names
            strike = option.get('strike', option.get('strikePrice', option.get('strike_price')))
            expiry = option.get('expiry', option.get('expirationDate', option.get('expiration_date')))
            iv = option.get('impliedVolatility', option.get('iv', option.get('implied_volatility')))
            
            if not all([strike, expiry, iv]):
                return None
            
            # Calculate time to expiry
            if isinstance(expiry, str):
                expiry_date = pd.to_datetime(expiry).date()
            else:
                expiry_date = expiry
            
            time_to_expiry = (expiry_date - datetime.now().date()).days / 365.25
            
            if time_to_expiry <= 0:
                return None  # Expired option
            
            return {
                'symbol': ticker,
                'strike': float(strike),
                'time_to_expiry': time_to_expiry,
                'implied_volatility': float(iv),
                'option_type': option_type.lower(),
                'risk_free_rate': option.get('riskFreeRate', option.get('risk_free_rate', self.default_risk_free_rate)),
                'dividend_yield': option.get('dividendYield', option.get('dividend_yield', self.default_dividend_yield)),
                'bid': option.get('bid', 0.0),
                'ask': option.get('ask', 0.0),
                'volume': option.get('volume', 0),
                'open_interest': option.get('openInterest', option.get('open_interest', 0))
            }
            
        except Exception as e:
            self.logger.warning(f"Single option processing failed: {e}")
            return None

    
    def _select_primary_option(self, options_data: List[Dict[str, Any]], current_price: float) -> Optional[Dict[str, Any]]:
        """Select primary option for analysis (closest to ATM call)"""
        
        try:
            # Filter for calls first
            calls = [opt for opt in options_data if opt['option_type'] == 'call']
            
            if not calls:
                # No calls available, use puts
                calls = [opt for opt in options_data if opt['option_type'] == 'put']
            
            if not calls:
                return None
            
            # Find closest to ATM
            atm_option = min(calls, key=lambda opt: abs(opt['strike'] - current_price))
            
            # Validate option has reasonable parameters
            if (atm_option['time_to_expiry'] > 0 and 
                atm_option['implied_volatility'] > 0.001 and
                atm_option['implied_volatility'] < 5.0):
                return atm_option
            
            return None
            
        except Exception as e:
            self.logger.error(f"Primary option selection failed: {e}")
            return None
    
    def _load_historical_greeks(self, ticker: str) -> List[GreeksResult]:
        """Load historical Greeks data for ROC analysis"""
        
        try:
            # Look for historical Greeks files
            today = datetime.now()
            historical_greeks = []
            
            # Search last 30 days for historical data
            for days_back in range(1, 31):
                date_to_check = today - pd.Timedelta(days=days_back)
                date_str = date_to_check.strftime("%Y-%m-%d")
                
                historical_file = Path(f"{self.data_directory}/{date_str}/{ticker}_greeks.json")
                
                if historical_file.exists():
                    try:
                        with open(historical_file, 'r') as f:
                            historical_data = json.load(f)
                        
                        # Convert to GreeksResult (simplified)
                        # In production, would use full GreeksResult.from_dict()
                        if len(historical_greeks) < 20:  # Limit to 20 most recent
                            historical_greeks.append(historical_data)
                            
                    except Exception as e:
                        self.logger.warning(f"Failed to load historical data from {historical_file}: {e}")
            
            self.logger.debug(f"Loaded {len(historical_greeks)} historical Greeks for {ticker}")
            return historical_greeks[:20]  # Return most recent 20
            
        except Exception as e:
            self.logger.warning(f"Historical Greeks loading failed: {e}")
            return []

    
    def _calculate_portfolio_greeks(self, options_data: List[Dict[str, Any]], 
                                  current_price: float, historical_greeks: List[GreeksResult]) -> Dict[str, Any]:
        """Calculate portfolio-level Greeks"""
        
        try:
            portfolio_delta = 0.0
            portfolio_gamma = 0.0
            portfolio_theta = 0.0
            portfolio_vega = 0.0
            portfolio_rho = 0.0
            
            successful_calculations = 0
            calculation_errors = []
            
            # Calculate Greeks for each option and sum
            for option in options_data:
                try:
                    greeks_result = self.greeks_engine.calculate_greeks(
                        spot_price=current_price,
                        strike_price=option['strike'],
                        time_to_expiry=option['time_to_expiry'],
                        risk_free_rate=option.get('risk_free_rate', self.default_risk_free_rate),
                        volatility=option['implied_volatility'],
                        option_type=option['option_type'],
                        dividend_yield=option.get('dividend_yield', self.default_dividend_yield),
                        symbol=option['symbol']
                    )
                    
                    # Assume position size of 1 for portfolio calculation
                    portfolio_delta += greeks_result.delta
                    portfolio_gamma += greeks_result.gamma
                    portfolio_theta += greeks_result.theta
                    portfolio_vega += greeks_result.vega
                    portfolio_rho += greeks_result.rho
                    
                    successful_calculations += 1
                    
                except Exception as e:
                    error_msg = f"Portfolio Greeks calculation failed for option {option['strike']}: {e}"
                    self.logger.warning(error_msg)
                    calculation_errors.append(error_msg)
            
            return {
                'portfolio_delta': portfolio_delta,
                'portfolio_gamma': portfolio_gamma,
                'portfolio_theta': portfolio_theta,
                'portfolio_vega': portfolio_vega,
                'portfolio_rho': portfolio_rho,
                'options_calculated': successful_calculations,
                'total_options': len(options_data),
                'calculation_success_rate': successful_calculations / len(options_data) if options_data else 0.0,
                'calculation_errors': calculation_errors
            }
            
        except Exception as e:
            self.logger.error(f"Portfolio Greeks calculation failed: {e}")
            return {
                'portfolio_delta': 0.0,
                'portfolio_gamma': 0.0,
                'portfolio_theta': 0.0,
                'portfolio_vega': 0.0,
                'portfolio_rho': 0.0,
                'options_calculated': 0,
                'total_options': len(options_data) if options_data else 0,
                'calculation_success_rate': 0.0,
                'calculation_errors': [str(e)]
            }

    
    def _create_analysis_result(self, greeks_result: GreeksResult, 
                              portfolio_greeks: Dict[str, Any], ticker: str) -> Dict[str, Any]:
        """Create comprehensive analysis result"""
        
        return {
            'timestamp': datetime.now().isoformat(),
            'symbol': ticker,
            'task_id': self.task_id,
            
            # Primary Greeks
            'delta': greeks_result.delta,
            'gamma': greeks_result.gamma,
            'theta': greeks_result.theta,
            'vega': greeks_result.vega,
            'rho': greeks_result.rho,
            
            # ROC Derivatives
            'delta_roc': greeks_result.delta_roc,
            'gamma_roc': greeks_result.gamma_roc,
            'theta_roc': greeks_result.theta_roc,
            'vega_roc': greeks_result.vega_roc,
            'rho_roc': greeks_result.rho_roc,
            
            # Option Parameters
            'spot_price': greeks_result.spot_price,
            'strike_price': greeks_result.strike_price,
            'time_to_expiry': greeks_result.time_to_expiry,
            'volatility': greeks_result.volatility,
            'option_type': greeks_result.option_type,
            'risk_free_rate': greeks_result.risk_free_rate,
            'dividend_yield': greeks_result.dividend_yield,
            
            # Quality Metrics
            'quality_score': greeks_result.quality_score,
            'calculation_quality': greeks_result.calculation_quality,
            'bounds_validation': greeks_result.bounds_validation,
            'statistical_significance': greeks_result.statistical_significance,
            
            # Anomaly Detection
            'greek_anomalies': greeks_result.greek_anomalies,
            'anomaly_count': greeks_result.anomaly_count,
            'anomaly_severity': greeks_result.anomaly_severity,
            
            # Portfolio Greeks
            'portfolio_greeks': portfolio_greeks,
            
            # Validation
            'put_call_parity_check': greeks_result.put_call_parity_check,
            'greeks_consistency_score': greeks_result.greeks_consistency_score,
            'error_flags': greeks_result.error_flags,
            
            # Metadata
            'calculation_metadata': greeks_result.calculation_metadata
        }

    
    def _save_analysis_results(self, analysis_result: Dict[str, Any], ticker: str) -> Path:
        """Save Greeks analysis results to file"""
        
        # Create output directory
        today = datetime.now().strftime("%Y-%m-%d")
        output_dir = Path(f"{self.data_directory}/{today}")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save as both JSON and Parquet for different use cases
        json_file = output_dir / f"{ticker}_greeks.json"
        parquet_file = output_dir / f"{ticker}_greeks.parquet"
        
        # Save JSON for immediate use
        with open(json_file, 'w') as f:
            json.dump(analysis_result, f, indent=2, default=str)
        
        # Save Parquet for data pipeline
        try:
            # Handle empty statistical_significance for parquet compatibility
            parquet_data = analysis_result.copy()
            if not parquet_data.get('statistical_significance'):
                parquet_data['statistical_significance'] = {'placeholder': 0.0}
                
            df = pd.DataFrame([parquet_data])
            df.to_parquet(parquet_file, index=False)
        except Exception as e:
            self.logger.warning(f"Failed to save Parquet file: {e}")
        
        self.logger.info(f"Greeks analysis saved: {json_file}")
        return json_file
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """Validate Greeks analysis inputs"""
        inputs = task.inputs
        
        # Check required inputs
        required_inputs = ["ticker", "current_price", "options_chain"]
        for required_input in required_inputs:
            if required_input not in inputs:
                self.logger.error(f"Missing required input: {required_input}")
                return False
        
        # Validate ticker
        ticker = inputs["ticker"]
        if not isinstance(ticker, str) or len(ticker) == 0:
            self.logger.error(f"Invalid ticker: {ticker}")
            return False
        
        # Validate current price
        current_price = inputs["current_price"]
        if not isinstance(current_price, (int, float)) or current_price <= 0:
            self.logger.error(f"Invalid current price: {current_price}")
            return False
        
        # Validate options chain
        options_chain = inputs["options_chain"]
        if not isinstance(options_chain, dict) or len(options_chain) == 0:
            self.logger.error("Invalid or empty options chain")
            return False
        
        return True

    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """Validate Greeks outputs meet precision standards"""
        quality_metrics = {}
        
        try:
            analysis_result = outputs.get("analysis_result", {})
            
            # Quality score validation
            quality_score = analysis_result.get("quality_score", 0.0)
            quality_metrics["quality_score"] = float(quality_score)
            
            # Mathematical precision validation (all Greeks must be finite)
            greeks_values = [
                analysis_result.get("delta", 0.0),
                analysis_result.get("gamma", 0.0),
                analysis_result.get("theta", 0.0),
                analysis_result.get("vega", 0.0),
                analysis_result.get("rho", 0.0)
            ]
            
            mathematical_precision = 1.0 if all(
                isinstance(v, (int, float)) and pd.notna(v) and abs(v) < 1e6 for v in greeks_values
            ) else 0.0
            quality_metrics["mathematical_precision"] = mathematical_precision
            
            # Bounds validation
            bounds_validation = analysis_result.get("bounds_validation", {})
            if bounds_validation:
                bounds_score = sum(bounds_validation.values()) / len(bounds_validation)
            else:
                bounds_score = 0.0
            quality_metrics["bounds_validation"] = bounds_score
            
            # Statistical significance validation
            statistical_significance = analysis_result.get("statistical_significance", {})
            if statistical_significance:
                significance_score = sum(statistical_significance.values()) / len(statistical_significance)
            else:
                significance_score = 0.8  # Default if no ROC data
            quality_metrics["statistical_significance"] = significance_score
            
            # Anomaly detection quality
            anomaly_count = analysis_result.get("anomaly_count", 0)
            anomaly_severity = analysis_result.get("anomaly_severity", "none")
            
            # Lower quality if too many critical anomalies
            if anomaly_severity == "critical" and anomaly_count > 3:
                anomaly_quality = 0.6
            elif anomaly_severity in ["high", "critical"]:
                anomaly_quality = 0.8
            else:
                anomaly_quality = 1.0
            quality_metrics["anomaly_detection_quality"] = anomaly_quality
            
            # Portfolio Greeks validation
            portfolio_greeks = analysis_result.get("portfolio_greeks", {})
            if portfolio_greeks:
                success_rate = portfolio_greeks.get("calculation_success_rate", 0.0)
                portfolio_score = success_rate
            else:
                portfolio_score = 1.0  # Single option case
            quality_metrics["portfolio_calculation"] = portfolio_score
            
            # Overall quality assessment
            overall_quality = (
                quality_metrics["quality_score"] * 0.3 +
                quality_metrics["mathematical_precision"] * 0.3 +
                quality_metrics["bounds_validation"] * 0.2 +
                quality_metrics["statistical_significance"] * 0.1 +
                quality_metrics["anomaly_detection_quality"] * 0.05 +
                quality_metrics["portfolio_calculation"] * 0.05
            )
            quality_metrics["overall_quality"] = overall_quality
            
            # Record quality assessment for training
            self.record_decision_point(
                decision_type="greeks_validation",
                context={
                    "quality_metrics": quality_metrics,
                    "quality_threshold": 0.999
                },
                choice_made="validation_completed",
                rationale=f"Overall quality: {overall_quality:.6f}"
            )
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            return {
                "quality_score": 0.0,
                "mathematical_precision": 0.0,
                "bounds_validation": 0.0,
                "statistical_significance": 0.0,
                "anomaly_detection_quality": 0.0,
                "portfolio_calculation": 0.0,
                "overall_quality": 0.0
            }
    
    def execute(self, ticker: str, current_price: float, options_chain: Dict[str, Any]) -> str:
        """
        Legacy execute method for backward compatibility
        
        Args:
            ticker: Stock ticker symbol
            current_price: Current stock price
            options_chain: Options chain data
            
        Returns:
            str: Path to output file
        """
        
        # Create AgentTask for new interface
        task = AgentTask(
            task_id=f"B-01-{ticker}-{int(time.time())}",
            task_type='greek_enhancement',
            agent_type='GreekEnhancementAgent',
            priority=TaskPriority.HIGH,
            inputs={
                "ticker": ticker,
                "current_price": current_price,
                "options_chain": options_chain
            },
            workflow_file='',
            quality_standards='0.999',
            performance_targets={'execution_time_ms': 200},
            dependencies=[],
            training_data_tags=['greeks', 'options', 'black_scholes'],
            timestamp=datetime.now()
        )
        
        # Execute using new interface
        result = self.execute_task(task)
        
        if result.status == TaskStatus.COMPLETED:
            return result.outputs["analysis_result"]["output_file"]
        else:
            raise Exception(f"Greeks analysis failed: {result.error_details}")


def main():
    """Command line interface for Greeks analysis"""
    import argparse
    
    parser = argparse.ArgumentParser(description="B-01: Greek Enhancement Analysis")
    parser.add_argument("--ticker", required=True, help="Stock ticker symbol")
    parser.add_argument("--price", type=float, required=True, help="Current stock price")
    parser.add_argument("--options-file", help="JSON file with options chain data")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create agent
    agent = GreekEnhancementAgent()
    
    try:
        # Load options chain if provided
        options_chain = {}
        if args.options_file:
            with open(args.options_file, 'r') as f:
                options_chain = json.load(f)
        else:
            # Create sample options chain for testing
            options_chain = {
                'calls': [{
                    'strike': args.price,
                    'expiry': '2024-03-15',
                    'impliedVolatility': 0.25,
                    'bid': 5.0,
                    'ask': 5.5
                }]
            }
        
        # Execute analysis
        result_path = agent.execute(args.ticker.upper(), args.price, options_chain)
        
        print("SUCCESS: Greeks analysis completed")
        print(f"Output file: {result_path}")
        
        # Show analysis summary
        with open(result_path, 'r') as f:
            result = json.load(f)
        
        print(f"\nGreeks Analysis Summary:")
        print(f"Delta: {result.get('delta', 0):.4f}")
        print(f"Gamma: {result.get('gamma', 0):.4f}")
        print(f"Theta: {result.get('theta', 0):.4f}")
        print(f"Vega: {result.get('vega', 0):.4f}")
        print(f"Rho: {result.get('rho', 0):.4f}")
        print(f"Quality Score: {result.get('quality_score', 0):.6f}")
        print(f"Anomalies Detected: {result.get('anomaly_count', 0)}")
        
        return 0
        
    except Exception as e:
        print(f"ERROR: Greeks analysis failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
