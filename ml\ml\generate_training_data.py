"""
Generate Training Data for Liquidity Models

This module provides functionality to generate synthetic training data
for liquidity models, including price data, options data, volume profile,
GEX data, and liquidity levels with known strengths and reactions.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Tuple
import random

# Import ML components
from ml_logging import get_logger

# Setup logger
logger = get_logger('generate_training_data')

class TrainingDataGenerator:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """
    Generates synthetic training data for liquidity models.

    This class provides methods to generate synthetic price data, options data,
    volume profile, GEX data, and liquidity levels with known strengths and
    reactions for training liquidity models.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the training data generator.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.seed = self.config.get('seed', 42)

        # Set random seed for reproducibility
        np.random.seed(self.seed)
        random.seed(self.seed)

        logger.info("Initialized TrainingDataGenerator")

    def generate_price_data(self,
                           ticker: str = 'SPY',
                           start_date: str = '2020-01-01',
                           end_date: str = '2023-01-01',
                           timeframe: str = '1d') -> pd.DataFrame:
        """
        Generate synthetic price data.

        Args:
            ticker: Ticker symbol
            start_date: Start date for price data
            end_date: End date for price data
            timeframe: Timeframe for price data

        Returns:
            DataFrame with OHLCV price data
        """
        logger.info(f"Generating synthetic price data for {ticker} from {start_date} to {end_date}")

        # Parse dates
        start = datetime.strptime(start_date, '%Y-%m-%d')
        end = datetime.strptime(end_date, '%Y-%m-%d')

        # Generate date range
        if timeframe == '1d':
            dates = pd.date_range(start=start, end=end, freq='B')  # Business days
        elif timeframe == '1h':
            dates = pd.date_range(start=start, end=end, freq='H')
        elif timeframe == '4h':
            dates = pd.date_range(start=start, end=end, freq='4H')
        else:
            dates = pd.date_range(start=start, end=end, freq='D')  # Default to daily

        # Generate price data
        n = len(dates)

        # Initial price
        initial_price = 100.0

        # Generate returns with some autocorrelation
        returns = np.random.normal(0, 0.01, n)
        for i in range(1, n):
            returns[i] = 0.1 * returns[i-1] + 0.9 * returns[i]  # Add autocorrelation

        # Calculate prices
        closes = initial_price * np.cumprod(1 + returns)

        # Generate OHLC data
        opens = closes * np.random.normal(1, 0.005, n)
        highs = np.maximum(opens, closes) * np.random.normal(1.01, 0.01, n)
        lows = np.minimum(opens, closes) * np.random.normal(0.99, 0.01, n)

        # Generate volume data with some clustering
        volumes = np.random.normal(1000000, 200000, n)
        for i in range(1, n):
            volumes[i] = 0.7 * volumes[i-1] + 0.3 * volumes[i]  # Add autocorrelation

        # Create DataFrame
        price_data = pd.DataFrame({
            'timestamp': dates,
            'open': opens,
            'high': highs,
            'low': lows,
            'close': closes,
            'volume': volumes
        })

        # Set index to timestamp
        price_data.set_index('timestamp', inplace=True)

        logger.info(f"Generated price data with {len(price_data)} rows")
        return price_data

    def generate_options_data(self,
                             price_data: pd.DataFrame,
                             num_strikes: int = 20,
                             num_expirations: int = 4) -> pd.DataFrame:
        """
        Generate synthetic options data.

        Args:
            price_data: DataFrame with OHLCV price data
            num_strikes: Number of strike prices
            num_expirations: Number of expiration dates

        Returns:
            DataFrame with options data
        """
        logger.info("Generating synthetic options data")

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Generate strike prices (centered around current price)
        strike_prices = np.linspace(current_price * 0.8, current_price * 1.2, num_strikes)

        # Generate expiration dates
        today = price_data.index[-1].date()
        expiration_dates = [today + timedelta(days=30*i) for i in range(1, num_expirations+1)]

        # Generate options data
        options_data = []

        for expiry in expiration_dates:
            for strike in strike_prices:
                # Calculate distance from ATM
                moneyness = strike / current_price - 1

                # Generate OI with higher values near ATM
                call_oi = max(0, np.random.normal(1000, 500) * (1 - abs(moneyness) * 5))
                put_oi = max(0, np.random.normal(1000, 500) * (1 - abs(moneyness) * 5))

                # Generate IV with smile pattern
                base_iv = 0.3
                iv_skew = 0.05 * moneyness  # Negative for calls, positive for puts
                call_iv = base_iv - iv_skew + np.random.normal(0, 0.02)
                put_iv = base_iv + iv_skew + np.random.normal(0, 0.02)

                # Add to options data
                options_data.append({
                    'expiration_date': expiry,
                    'strike_price': strike,
                    'call_oi': call_oi,
                    'put_oi': put_oi,
                    'call_iv': call_iv,
                    'put_iv': put_iv
                })

        # Create DataFrame
        options_df = pd.DataFrame(options_data)

        logger.info(f"Generated options data with {len(options_df)} rows")
        return options_df

    def generate_volume_profile(self, price_data: pd.DataFrame, num_nodes: int = 50) -> Dict[str, Any]:
        """
        Generate synthetic volume profile data.

        Args:
            price_data: DataFrame with OHLCV price data
            num_nodes: Number of volume nodes

        Returns:
            Dictionary with volume profile data
        """
        logger.info("Generating synthetic volume profile data")

        # Get price range
        min_price = price_data['low'].min()
        max_price = price_data['high'].max()
        current_price = price_data['close'].iloc[-1]

        # Generate price levels
        price_levels = np.linspace(min_price, max_price, num_nodes)

        # Generate volume with normal distribution around current price
        volumes = np.zeros(num_nodes)
        for i, price in enumerate(price_levels):
            # Distance from current price
            distance = abs(price - current_price) / current_price

            # Volume decreases with distance from current price
            volume = np.random.normal(10000, 2000) * np.exp(-5 * distance)
            volumes[i] = max(0, volume)

        # Find POC (point of control)
        poc_idx = np.argmax(volumes)
        poc_price = price_levels[poc_idx]

        # Find value area (70% of volume)
        total_volume = np.sum(volumes)
        value_area_volume = 0.7 * total_volume

        # Sort indices by volume
        sorted_indices = np.argsort(volumes)[::-1]

        # Find value area bounds
        cumulative_volume = 0
        value_area_indices = []

        for idx in sorted_indices:
            value_area_indices.append(idx)
            cumulative_volume += volumes[idx]

            if cumulative_volume >= value_area_volume:
                break

        # Get value area bounds
        va_low = price_levels[min(value_area_indices)]
        va_high = price_levels[max(value_area_indices)]

        # Create nodes
        nodes = []
        for i in range(num_nodes):
            nodes.append({
                'price': float(price_levels[i]),
                'volume': float(volumes[i])
            })

        # Create volume profile
        volume_profile = {
            'nodes': nodes,
            'profile_metrics': {
                'poc_price': float(poc_price),
                'value_area_low': float(va_low),
                'value_area_high': float(va_high),
                'total_volume': float(total_volume)
            }
        }

        logger.info(f"Generated volume profile with {len(nodes)} nodes")
        return volume_profile

    def generate_gex_data(self, price_data: pd.DataFrame, options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Generate synthetic GEX data.

        Args:
            price_data: DataFrame with OHLCV price data
            options_data: DataFrame with options data

        Returns:
            Dictionary with GEX data
        """
        logger.info("Generating synthetic GEX data")

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Generate GEX profile
        strike_prices = options_data['strike_price'].unique()
        strike_prices.sort()

        # Generate GEX values with some structure
        gex_values = []
        for strike in strike_prices:
            # Distance from current price
            moneyness = strike / current_price - 1

            # GEX tends to be positive above current price and negative below
            base_gex = 1e6 * moneyness

            # Add some noise
            gex = base_gex + np.random.normal(0, 5e5)
            gex_values.append(gex)

        # Interpolate GEX at current price
        gex_at_current = np.interp(current_price, strike_prices, gex_values)

        # Determine GEX impact
        if gex_at_current > 5e5:
            impact = 'positive'
        elif gex_at_current < -5e5:
            impact = 'negative'
        else:
            impact = 'neutral'

        # Find zero-gamma levels
        zero_crossings = []
        for i in range(1, len(gex_values)):
            if gex_values[i-1] * gex_values[i] <= 0:
                # Linear interpolation to find exact zero crossing
                x1, x2 = strike_prices[i-1], strike_prices[i]
                y1, y2 = gex_values[i-1], gex_values[i]

                if y1 == y2:
                    zero_price = x1
                else:
                    zero_price = x1 - y1 * (x2 - x1) / (y2 - y1)

                zero_crossings.append({
                    'price': float(zero_price),
                    'type': 'zero_gamma',
                    'strength': 0.5 + 0.3 * random.random()  # Random strength between 0.5 and 0.8
                })

        # Find positive and negative gamma zones
        liquidity_levels = zero_crossings.copy()

        for i in range(len(gex_values)):
            if gex_values[i] > 1e6:
                liquidity_levels.append({
                    'price': float(strike_prices[i]),
                    'type': 'positive_gamma',
                    'strength': 0.6 + 0.3 * random.random()  # Random strength between 0.6 and 0.9
                })
            elif gex_values[i] < -1e6:
                liquidity_levels.append({
                    'price': float(strike_prices[i]),
                    'type': 'negative_gamma',
                    'strength': 0.6 + 0.3 * random.random()  # Random strength between 0.6 and 0.9
                })

        # Create GEX data
        gex_data = {
            'gex_at_current': float(gex_at_current),
            'impact': impact,
            'gex_profile': {
                'prices': strike_prices.tolist(),
                'values': gex_values
            },
            'liquidity_levels': liquidity_levels
        }

        logger.info(f"Generated GEX data with {len(strike_prices)} price levels")
        return gex_data

    def generate_liquidity_levels(self,
                                price_data: pd.DataFrame,
                                volume_profile: Dict[str, Any],
                                gex_data: Dict[str, Any],
                                options_data: pd.DataFrame,
                                num_levels: int = 10) -> Dict[str, List[Dict[str, Any]]]:
        """
        Generate synthetic liquidity levels with known strengths and reactions.

        Args:
            price_data: DataFrame with OHLCV price data
            volume_profile: Dictionary with volume profile data
            gex_data: Dictionary with GEX data
            options_data: DataFrame with options data
            num_levels: Number of liquidity levels to generate

        Returns:
            Dictionary with liquidity levels
        """
        logger.info("Generating synthetic liquidity levels")

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Collect potential liquidity levels from different sources
        potential_levels = []

        # 1. Volume profile levels
        if volume_profile and 'profile_metrics' in volume_profile:
            metrics = volume_profile['profile_metrics']

            # POC
            if 'poc_price' in metrics:
                potential_levels.append({
                    'price': metrics['poc_price'],
                    'source': 'volume_profile',
                    'type': 'support' if metrics['poc_price'] < current_price else 'resistance',
                    'base_strength': 0.8
                })

            # Value area bounds
            if 'value_area_low' in metrics:
                potential_levels.append({
                    'price': metrics['value_area_low'],
                    'source': 'volume_profile',
                    'type': 'support',
                    'base_strength': 0.7
                })

            if 'value_area_high' in metrics:
                potential_levels.append({
                    'price': metrics['value_area_high'],
                    'source': 'volume_profile',
                    'type': 'resistance',
                    'base_strength': 0.7
                })

        # 2. GEX levels
        if gex_data and 'liquidity_levels' in gex_data:
            for level in gex_data['liquidity_levels']:
                level_price = level.get('price', 0)
                level_type = level.get('type', '')

                if level_type == 'zero_gamma':
                    # Zero gamma can be either support or resistance
                    level_type = 'support' if level_price < current_price else 'resistance'
                    base_strength = 0.6
                elif level_type == 'positive_gamma':
                    # Positive gamma is usually support
                    level_type = 'support'
                    base_strength = 0.7
                elif level_type == 'negative_gamma':
                    # Negative gamma is usually resistance
                    level_type = 'resistance'
                    base_strength = 0.7
                else:
                    # Unknown type
                    level_type = 'support' if level_price < current_price else 'resistance'
                    base_strength = 0.5

                potential_levels.append({
                    'price': level_price,
                    'source': 'gex',
                    'type': level_type,
                    'base_strength': base_strength
                })

        # 3. Options OI levels
        if not options_data.empty:
            # Group by strike price
            strike_oi = options_data.groupby('strike_price').agg({
                'call_oi': 'sum',
                'put_oi': 'sum'
            }).reset_index()

            # Find strikes with high OI
            for _, row in strike_oi.nlargest(5, 'call_oi').iterrows():
                potential_levels.append({
                    'price': row['strike_price'],
                    'source': 'options_oi',
                    'type': 'resistance',
                    'base_strength': 0.6 + 0.2 * random.random()  # Random strength between 0.6 and 0.8
                })

            for _, row in strike_oi.nlargest(5, 'put_oi').iterrows():
                potential_levels.append({
                    'price': row['strike_price'],
                    'source': 'options_oi',
                    'type': 'support',
                    'base_strength': 0.6 + 0.2 * random.random()  # Random strength between 0.6 and 0.8
                })

        # 4. Price action levels (recent highs and lows)
        recent_data = price_data.iloc[-20:]

        # Recent high
        recent_high = recent_data['high'].max()
        potential_levels.append({
            'price': recent_high,
            'source': 'price_action',
            'type': 'resistance',
            'base_strength': 0.6
        })

        # Recent low
        recent_low = recent_data['low'].min()
        potential_levels.append({
            'price': recent_low,
            'source': 'price_action',
            'type': 'support',
            'base_strength': 0.6
        })

        # Filter out duplicates and sort by price
        unique_levels = {}
        for level in potential_levels:
            price = level['price']
            price_key = round(price, 2)  # Round to 2 decimal places for deduplication

            if price_key not in unique_levels:
                unique_levels[price_key] = level
            else:
                # If duplicate, keep the one with higher base strength
                if level['base_strength'] > unique_levels[price_key]['base_strength']:
                    unique_levels[price_key] = level

        # Convert back to list and sort by price
        sorted_levels = sorted(unique_levels.values(), key=lambda x: x['price'])

        # Select a subset of levels if there are too many
        if len(sorted_levels) > num_levels:
            # Ensure we have a mix of support and resistance
            supports = [level for level in sorted_levels if level['type'] == 'support']
            resistances = [level for level in sorted_levels if level['type'] == 'resistance']

            # Select top levels by base strength
            supports = sorted(supports, key=lambda x: x['base_strength'], reverse=True)[:num_levels//2]
            resistances = sorted(resistances, key=lambda x: x['base_strength'], reverse=True)[:num_levels//2]

            selected_levels = supports + resistances
        else:
            selected_levels = sorted_levels

        # Add additional attributes to levels
        support_levels = []
        resistance_levels = []

        for level in selected_levels:
            # Add random variation to strength
            strength = min(1.0, level['base_strength'] + 0.1 * random.random())

            # Add sources
            sources = [level['source']]

            # Add random additional sources
            if random.random() < 0.3:
                sources.append('volume')
            if random.random() < 0.3:
                sources.append('price_action')

            # Add reaction (how price reacts when it reaches this level)
            # Positive reaction means bounce/rejection, negative means break
            if level['type'] == 'support':
                # Stronger levels have higher probability of bounce
                bounce_prob = 0.5 + 0.4 * strength
                reaction = 1.0 if random.random() < bounce_prob else -1.0

                # Add to support levels
                support_levels.append({
                    'price': level['price'],
                    'type': 'support',
                    'strength': strength,
                    'sources': sources,
                    'reaction': reaction
                })
            else:
                # Stronger levels have higher probability of rejection
                rejection_prob = 0.5 + 0.4 * strength
                reaction = -1.0 if random.random() < rejection_prob else 1.0

                # Add to resistance levels
                resistance_levels.append({
                    'price': level['price'],
                    'type': 'resistance',
                    'strength': strength,
                    'sources': sources,
                    'reaction': reaction
                })

        # Sort levels by price
        support_levels.sort(key=lambda x: x['price'])
        resistance_levels.sort(key=lambda x: x['price'])

        # Create liquidity levels dictionary
        liquidity_levels = {
            'support': support_levels,
            'resistance': resistance_levels
        }

        logger.info(f"Generated {len(support_levels)} support levels and {len(resistance_levels)} resistance levels")
        return liquidity_levels

    def generate_training_dataset(self,
                                ticker: str = 'SPY',
                                num_samples: int = 100,
                                start_date: str = '2020-01-01',
                                timeframe: str = '1d') -> List[Dict[str, Any]]:
        """
        Generate a complete training dataset with multiple samples.

        Args:
            ticker: Ticker symbol
            num_samples: Number of samples to generate
            start_date: Start date for price data
            timeframe: Timeframe for price data

        Returns:
            List of dictionaries with training data samples
        """
        logger.info(f"Generating {num_samples} training samples for {ticker}")

        # Parse start date
        start = datetime.strptime(start_date, '%Y-%m-%d')

        # Generate samples
        samples = []

        for i in range(num_samples):
            # Generate sample date (advancing by timeframe)
            if timeframe == '1d':
                sample_start = start + timedelta(days=i*30)  # 30 days between samples
                sample_end = sample_start + timedelta(days=365)  # 1 year of data
            elif timeframe == '1h':
                sample_start = start + timedelta(hours=i*24)  # 24 hours between samples
                sample_end = sample_start + timedelta(days=30)  # 30 days of data
            elif timeframe == '4h':
                sample_start = start + timedelta(hours=i*24)  # 24 hours between samples
                sample_end = sample_start + timedelta(days=60)  # 60 days of data
            else:
                sample_start = start + timedelta(days=i*30)  # Default to 30 days between samples
                sample_end = sample_start + timedelta(days=365)  # 1 year of data

            # Generate price data
            price_data = self.generate_price_data(
                ticker=ticker,
                start_date=sample_start.strftime('%Y-%m-%d'),
                end_date=sample_end.strftime('%Y-%m-%d'),
                timeframe=timeframe
            )

            # Generate options data
            options_data = self.generate_options_data(price_data)

            # Generate volume profile
            volume_profile = self.generate_volume_profile(price_data)

            # Generate GEX data
            gex_data = self.generate_gex_data(price_data, options_data)

            # Generate liquidity levels
            liquidity_levels = self.generate_liquidity_levels(
                price_data=price_data,
                volume_profile=volume_profile,
                gex_data=gex_data,
                options_data=options_data
            )

            # Add to samples
            samples.append({
                'price_data': price_data,
                'options_data': options_data,
                'volume_profile': volume_profile,
                'gex_data': gex_data,
                'liquidity_levels': liquidity_levels
            })

            logger.info(f"Generated sample {i+1}/{num_samples}")

        logger.info(f"Generated {len(samples)} training samples")
        return samples
