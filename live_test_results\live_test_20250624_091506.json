[{"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:06.283623", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:15:06.283584", "success": true, "latency_ms": 150, "quality_score": 0.95, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:06.284154", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:15:06.284133", "success": true, "latency_ms": 150, "quality_score": 0.95, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:06.284925", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:15:06.284908", "success": true, "latency_ms": 150, "quality_score": 0.95, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:15:06.285442", "result": {"timestamp": "2025-06-24T09:15:06.285422", "total_execution_time_ms": 35000, "budget_compliance": true, "budget_limit_ms": 45000, "ticker_results": {"SPY": {"success": true, "execution_time": 11000, "output_files": ["SPY_analysis.json"], "quality_metrics": {"overall": 0.92}}, "QQQ": {"success": true, "execution_time": 11000, "output_files": ["QQQ_analysis.json"], "quality_metrics": {"overall": 0.92}}, "AAPL": {"success": true, "execution_time": 11000, "output_files": ["AAPL_analysis.json"], "quality_metrics": {"overall": 0.92}}}, "overall_success": true}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:11.286457", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:15:11.286424", "success": true, "latency_ms": 170, "quality_score": 0.9299999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:11.287289", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:15:11.287274", "success": true, "latency_ms": 170, "quality_score": 0.9299999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:11.288153", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:15:11.288132", "success": true, "latency_ms": 170, "quality_score": 0.9299999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:16.289752", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:15:16.289724", "success": true, "latency_ms": 190, "quality_score": 0.9099999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:16.290605", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:15:16.290590", "success": true, "latency_ms": 190, "quality_score": 0.9099999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:15:16.291381", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:15:16.291367", "success": true, "latency_ms": 190, "quality_score": 0.9099999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_091506", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:15:16.292174", "result": {"timestamp": "2025-06-24T09:15:16.292157", "total_execution_time_ms": 39000, "budget_compliance": true, "budget_limit_ms": 45000, "ticker_results": {"SPY": {"success": true, "execution_time": 12000, "output_files": ["SPY_analysis.json"], "quality_metrics": {"overall": 0.92}}, "QQQ": {"success": true, "execution_time": 12000, "output_files": ["QQQ_analysis.json"], "quality_metrics": {"overall": 0.92}}, "AAPL": {"success": true, "execution_time": 12000, "output_files": ["AAPL_analysis.json"], "quality_metrics": {"overall": 0.92}}}, "overall_success": true}}]