#!/usr/bin/env python3
"""
CORE Flow Detection System - Flow Physics Analyzer

Stripped, optimized flow physics engine focusing on mathematical derivatives:
- Velocity (1st derivative) - Flow direction and momentum  
- Acceleration (2nd derivative) - Momentum changes
- Jerk (3rd derivative) - Regime transitions

Clean implementation with CSID integration for institutional flow detection.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass

# Import from CORE data layer
from data.factor_spec import FactorData, DirectionBias, TimeFrame, create_factor
from config.constants import FLOW_PHYSICS

@dataclass
class FlowResult:
    """Clean flow analysis result."""
    velocity: float
    acceleration: float
    jerk: float
    regime: str
    institutional_detected: bool
    institutional_direction: str
    flow_strength: float
    csid_confirmation: bool

class FlowPhysicsAnalyzer:
    """
    Core flow physics analyzer - mathematical derivatives engine.
    Clean, focused implementation for institutional flow detection.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Core thresholds from constants
        self.velocity_threshold = FLOW_PHYSICS['MIN_VELOCITY_THRESHOLD']
        self.institutional_threshold = FLOW_PHYSICS['INSTITUTIONAL_VELOCITY_THRESHOLD']
        self.acceleration_threshold = FLOW_PHYSICS['ACCELERATION_STABILITY_THRESHOLD']
        self.jerk_threshold = FLOW_PHYSICS['JERK_NOISE_THRESHOLD']
        
        # Flow history for derivative calculation
        self._flow_history = {}
        self._csid_analyzer = CSIDAnalyzer()
    
    def analyze_factors(self, data_package: Dict[str, Any]) -> List[FactorData]:
        """
        Main analyzer interface - generate flow physics factors.
        
        Args:
            data_package: {ticker, current_price, mtf_data, timestamp}
            
        Returns:
            List[FactorData]: Flow physics factors
        """
        ticker = data_package.get('ticker', 'UNKNOWN')
        current_price = data_package.get('current_price', 0.0)
        mtf_data = data_package.get('mtf_data', {})
        timestamp = data_package.get('timestamp', datetime.now())
        
        factors = []
        
        try:
            # Get primary timeframe data
            price_data = self._get_primary_timeframe_data(mtf_data)
            if price_data is None or price_data.empty:
                return factors
            
            # Calculate flow value
            flow_value = self._calculate_flow_value(price_data, current_price)
            
            # Perform flow physics analysis
            flow_result = self._analyze_flow_physics(ticker, flow_value, price_data, timestamp)
            
            # Generate factors from analysis
            factors.extend(self._create_flow_factors(
                ticker, flow_result, timestamp, current_price
            ))
            
            return factors
            
        except Exception as e:
            print(f"Flow physics error for {ticker}: {e}")
            return factors
    
    def _get_primary_timeframe_data(self, mtf_data: Dict[str, pd.DataFrame]) -> Optional[pd.DataFrame]:
        """Get best available timeframe data."""
        # Prefer 15m, fallback to available
        preferred_order = ['15m', '1h', '30m', '5m', '1m', '4h']
        
        for tf in preferred_order:
            if tf in mtf_data and not mtf_data[tf].empty:
                return mtf_data[tf]
        
        # Return any available data
        for tf_data in mtf_data.values():
            if not tf_data.empty:
                return tf_data
        
        return None
    
    def _calculate_flow_value(self, price_data: pd.DataFrame, current_price: float) -> float:
        """Calculate current flow value from price and volume."""
        try:
            if price_data.empty:
                return 0.0
            
            recent_bar = price_data.iloc[-1]
            
            # Flow = Price * Volume * Direction_Factor
            price_change = (recent_bar['close'] - recent_bar['open']) / recent_bar['open']
            direction_factor = np.tanh(price_change * 10)  # Smooth direction weighting
            
            flow_value = recent_bar['close'] * recent_bar['volume'] * direction_factor
            
            return float(flow_value)
            
        except Exception as e:
            print(f"Flow calculation error: {e}")
            return 0.0
    
    def _analyze_flow_physics(self, ticker: str, flow_value: float, 
                            price_data: pd.DataFrame, timestamp: datetime) -> FlowResult:
        """Core flow physics analysis using derivatives."""
        
        # Update flow history
        self._update_flow_history(ticker, flow_value, timestamp)
        
        # Calculate derivatives
        velocity = self._calculate_velocity(ticker)
        acceleration = self._calculate_acceleration(ticker)
        jerk = self._calculate_jerk(ticker)
        
        # Detect regime
        regime = self._detect_flow_regime(velocity, acceleration, jerk)
        
        # Detect institutional activity
        institutional_detected, institutional_direction = self._detect_institutional_flow(
            velocity, acceleration, regime
        )
        
        # Calculate flow strength
        flow_strength = self._calculate_flow_strength(velocity, acceleration, jerk)
        
        # CSID confirmation
        csid_confirmation = self._get_csid_confirmation(ticker, price_data, velocity)
        
        return FlowResult(
            velocity=velocity,
            acceleration=acceleration,
            jerk=jerk,
            regime=regime,
            institutional_detected=institutional_detected,
            institutional_direction=institutional_direction,
            flow_strength=flow_strength,
            csid_confirmation=csid_confirmation
        )
    
    def _update_flow_history(self, ticker: str, flow_value: float, timestamp: datetime):
        """Update flow history for derivative calculations."""
        if ticker not in self._flow_history:
            self._flow_history[ticker] = []
        
        self._flow_history[ticker].append({
            'timestamp': timestamp,
            'value': flow_value
        })
        
        # Keep only recent history (100 points max)
        max_history = 100
        if len(self._flow_history[ticker]) > max_history:
            self._flow_history[ticker] = self._flow_history[ticker][-max_history:]
    
    def _calculate_velocity(self, ticker: str) -> float:
        """Calculate flow velocity (1st derivative)."""
        history = self._flow_history.get(ticker, [])
        
        if len(history) < 2:
            return 0.0
        
        return self._calculate_derivative(history, order=1)
    
    def _calculate_acceleration(self, ticker: str) -> float:
        """Calculate flow acceleration (2nd derivative)."""
        history = self._flow_history.get(ticker, [])
        
        if len(history) < 3:
            return 0.0
        
        return self._calculate_derivative(history, order=2)
    
    def _calculate_jerk(self, ticker: str) -> float:
        """Calculate flow jerk (3rd derivative)."""
        history = self._flow_history.get(ticker, [])
        
        if len(history) < 4:
            return 0.0
        
        return self._calculate_derivative(history, order=3)
    
    def _calculate_derivative(self, history: List[Dict], order: int) -> float:
        """Calculate nth derivative of flow history."""
        if len(history) < order + 1:
            return 0.0
        
        # Use recent points for calculation
        n_points = min(order + 3, len(history))
        recent_history = history[-n_points:]
        
        values = np.array([h['value'] for h in recent_history], dtype=float)
        times = np.array([h['timestamp'].timestamp() for h in recent_history], dtype=float)
        
        # Calculate derivatives iteratively
        for _ in range(order):
            if len(values) < 2:
                return 0.0
            
            # Calculate differences
            value_diff = np.diff(values)
            time_diff = np.diff(times)
            
            # Avoid division by zero
            time_diff[time_diff == 0] = 1e-6
            
            # Update arrays
            values = value_diff / time_diff
            times = times[:-1]
        
        return float(values[-1]) if len(values) > 0 else 0.0
    
    def _detect_flow_regime(self, velocity: float, acceleration: float, jerk: float) -> str:
        """Detect current flow regime based on derivatives."""
        
        # Check for regime change (high jerk)
        if abs(jerk) > self.jerk_threshold * 5:
            return "REGIME_CHANGE"
        
        # Check for momentum shift
        if (velocity > 0 and acceleration < -self.acceleration_threshold) or \
           (velocity < 0 and acceleration > self.acceleration_threshold):
            return "MOMENTUM_SHIFT"
        
        # Check for accumulation
        if velocity > self.velocity_threshold and acceleration > 0:
            return "ACCUMULATION"
        
        # Check for distribution  
        if velocity < -self.velocity_threshold and acceleration < 0:
            return "DISTRIBUTION"
        
        # Steady flow
        if abs(acceleration) < self.acceleration_threshold and abs(jerk) < self.jerk_threshold:
            return "STEADY_FLOW"
        
        return "TRANSITIONAL"
    
    def _detect_institutional_flow(self, velocity: float, acceleration: float, regime: str) -> Tuple[bool, str]:
        """Detect institutional flow activity."""
        
        # Institutional velocity threshold
        institutional_velocity = abs(velocity) > self.institutional_threshold
        
        # Steady acceleration indicates controlled activity
        controlled_acceleration = abs(acceleration) < self.acceleration_threshold * 2
        
        # Institutional patterns
        institutional_detected = institutional_velocity and controlled_acceleration
        
        if not institutional_detected:
            return False, "neutral"
        
        # Determine direction
        if regime in ["ACCUMULATION"] or velocity > 0:
            direction = "accumulation"
        elif regime in ["DISTRIBUTION"] or velocity < 0:
            direction = "distribution"
        else:
            direction = "neutral"
        
        return True, direction
    
    def _calculate_flow_strength(self, velocity: float, acceleration: float, jerk: float) -> float:
        """Calculate overall flow strength score."""
        
        # Normalize components
        norm_velocity = min(1.0, abs(velocity) / (self.institutional_threshold * 3))
        norm_acceleration = min(1.0, abs(acceleration) / (self.acceleration_threshold * 3))
        norm_jerk = min(1.0, abs(jerk) / (self.jerk_threshold * 3))
        
        # Weight components (velocity most important)
        strength = (norm_velocity * 0.6) + (norm_acceleration * 0.3) + (norm_jerk * 0.1)
        
        return min(1.0, strength)
    
    def _get_csid_confirmation(self, ticker: str, price_data: pd.DataFrame, velocity: float) -> bool:
        """Get CSID confirmation for flow direction."""
        try:
            csid_result = self._csid_analyzer.calculate_csid(ticker, price_data)
            
            # Check if CSID velocity confirms flow velocity
            velocity_agreement = (velocity > 0 and csid_result.delta_velocity > 0) or \
                               (velocity < 0 and csid_result.delta_velocity < 0)
            
            # Require minimum CSID confidence
            min_confidence = 0.5
            
            return velocity_agreement and csid_result.confidence_score > min_confidence
            
        except Exception:
            return False
    
    def _create_flow_factors(self, ticker: str, flow_result: FlowResult, 
                           timestamp: datetime, current_price: float) -> List[FactorData]:
        """Create FactorData objects from flow analysis."""
        factors = []
        
        try:
            # Primary flow factor
            if flow_result.flow_strength > 0.4:
                direction = DirectionBias.BULLISH if flow_result.velocity > 0 else DirectionBias.BEARISH
                
                factor = create_factor(
                    analyzer_name="FlowPhysics",
                    factor_name=f"Flow_Velocity_{flow_result.regime}",
                    direction=direction,
                    strength=flow_result.flow_strength,
                    timeframe=TimeFrame.MIN_15,
                    timestamp=timestamp,
                    confidence_score=0.8 if flow_result.csid_confirmation else 0.6,
                    raw_value=flow_result.velocity,
                    metadata={
                        'velocity': flow_result.velocity,
                        'acceleration': flow_result.acceleration,
                        'jerk': flow_result.jerk,
                        'regime': flow_result.regime,
                        'csid_confirmed': flow_result.csid_confirmation
                    }
                )
                factors.append(factor)
            
            # Institutional activity factor
            if flow_result.institutional_detected:
                direction = DirectionBias.BULLISH if flow_result.institutional_direction == "accumulation" else DirectionBias.BEARISH
                
                factor = create_factor(
                    analyzer_name="FlowPhysics",
                    factor_name=f"Institutional_{flow_result.institutional_direction}",
                    direction=direction,
                    strength=min(0.9, flow_result.flow_strength * 1.2),
                    timeframe=TimeFrame.MIN_15,
                    timestamp=timestamp,
                    confidence_score=0.9 if flow_result.csid_confirmation else 0.7,
                    raw_value=flow_result.velocity,
                    metadata={
                        'institutional_direction': flow_result.institutional_direction,
                        'regime': flow_result.regime,
                        'csid_confirmed': flow_result.csid_confirmation
                    }
                )
                factors.append(factor)
            
            # Regime change factor
            if flow_result.regime in ["REGIME_CHANGE", "MOMENTUM_SHIFT"]:
                direction = DirectionBias.NEUTRAL
                
                factor = create_factor(
                    analyzer_name="FlowPhysics",
                    factor_name=f"Flow_{flow_result.regime}",
                    direction=direction,
                    strength=0.8,
                    timeframe=TimeFrame.MIN_15,
                    timestamp=timestamp,
                    confidence_score=0.7,
                    raw_value=flow_result.jerk,
                    metadata={
                        'regime': flow_result.regime,
                        'jerk': flow_result.jerk
                    }
                )
                factors.append(factor)
            
        except Exception as e:
            print(f"Error creating flow factors: {e}")
        
        return factors

# Simplified CSID Analyzer for integration
class CSIDAnalyzer:
    """Simplified CSID analyzer for flow confirmation."""
    
    def __init__(self):
        self._history = {}
    
    def calculate_csid(self, ticker: str, price_data: pd.DataFrame):
        """Calculate basic CSID metrics."""
        try:
            if price_data.empty:
                return self._default_result()
            
            # Calculate volume delta
            volume_delta = self._calculate_volume_delta(price_data)
            
            # Track cumulative delta
            cumulative_delta = self._update_cumulative_delta(ticker, volume_delta)
            
            # Calculate delta velocity
            delta_velocity = self._calculate_delta_velocity(ticker)
            
            # Simple confidence based on data quality
            confidence_score = min(1.0, len(price_data) / 50) * 0.8
            
            return type('CSIDResult', (), {
                'delta_velocity': delta_velocity,
                'confidence_score': confidence_score,
                'cumulative_delta': cumulative_delta
            })()
            
        except Exception:
            return self._default_result()
    
    def _calculate_volume_delta(self, price_data: pd.DataFrame) -> float:
        """Simple volume delta calculation."""
        try:
            recent_bar = price_data.iloc[-1]
            price_range = recent_bar['high'] - recent_bar['low']
            if price_range == 0:
                return 0.0
            
            price_movement = recent_bar['close'] - recent_bar['open']
            volume_delta = recent_bar['volume'] * (price_movement / price_range)
            
            return float(volume_delta)
        except Exception:
            return 0.0
    
    def _update_cumulative_delta(self, ticker: str, volume_delta: float) -> float:
        """Update cumulative delta for ticker."""
        if ticker not in self._history:
            self._history[ticker] = []
        
        self._history[ticker].append(volume_delta)
        
        # Keep last 50 points
        if len(self._history[ticker]) > 50:
            self._history[ticker] = self._history[ticker][-50:]
        
        return sum(self._history[ticker])
    
    def _calculate_delta_velocity(self, ticker: str) -> float:
        """Calculate delta velocity."""
        history = self._history.get(ticker, [])
        
        if len(history) < 2:
            return 0.0
        
        recent_delta = sum(history[-5:]) if len(history) >= 5 else sum(history)
        previous_delta = sum(history[-10:-5]) if len(history) >= 10 else 0
        
        return recent_delta - previous_delta
    
    def _default_result(self):
        """Default CSID result."""
        return type('CSIDResult', (), {
            'delta_velocity': 0.0,
            'confidence_score': 0.1,
            'cumulative_delta': 0.0
        })()
