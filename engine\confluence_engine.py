#!/usr/bin/env python3
"""
CORE Flow Detection System - Confluence Engine

Stripped, optimized confluence detection focused on 3-of-4 agreement logic.
Mathematical precision with minimal complexity for AI training.

Based on proven Liquidity_Sweep template, stripped to essentials.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass

# Import from CORE data layer
from data.factor_spec import FactorData, DirectionBias, TimeFrame
from config.constants import CONFLUENCE_ENGINE

@dataclass
class ConfluenceResult:
    """Clean confluence analysis result."""
    direction: DirectionBias
    strength: float
    confidence: float
    agreement_count: int
    agreement_factors: List[str]
    disagreement_factors: List[str]
    analysis_quality: float

class ConfluenceEngine:
    """
    Core confluence engine - 3-of-4 analyzer agreement logic.
    Mathematical precision with statistical validation.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Core thresholds from constants
        self.min_agreement_count = CONFLUENCE_ENGINE['MIN_AGREEMENT_COUNT']
        self.min_factor_strength = CONFLUENCE_ENGINE['MIN_FACTOR_STRENGTH']
        self.min_quality_threshold = CONFLUENCE_ENGINE['MIN_QUALITY_THRESHOLD']
        self.direction_confidence_threshold = CONFLUENCE_ENGINE['DIRECTION_CONFIDENCE_THRESHOLD']
        
    def analyze_confluence(self, all_factors: List[FactorData]) -> ConfluenceResult:
        """
        Main confluence analysis - 3-of-4 agreement with quality validation.
        
        Args:
            all_factors: Combined factors from all 4 analyzers
            
        Returns:
            ConfluenceResult: Direction consensus with confidence metrics
        """
        try:
            # Filter valid factors
            valid_factors = self._filter_valid_factors(all_factors)
            
            if len(valid_factors) < self.min_agreement_count:
                return self._no_consensus_result(valid_factors)
            
            # Group factors by analyzer
            analyzer_groups = self._group_factors_by_analyzer(valid_factors)
            
            # Calculate analyzer consensus
            analyzer_consensus = self._calculate_analyzer_consensus(analyzer_groups)
            
            # Determine overall direction
            direction_result = self._determine_direction_consensus(analyzer_consensus)
            
            # Calculate confluence strength
            strength = self._calculate_confluence_strength(analyzer_consensus, direction_result['direction'])
            
            # Calculate confidence score
            confidence = self._calculate_confidence_score(analyzer_consensus, direction_result)
            
            # Analyze agreement quality
            quality_score = self._analyze_agreement_quality(analyzer_consensus, valid_factors)
            
            return ConfluenceResult(
                direction=direction_result['direction'],
                strength=strength,
                confidence=confidence,
                agreement_count=direction_result['agreement_count'],
                agreement_factors=direction_result['agreement_factors'],
                disagreement_factors=direction_result['disagreement_factors'],
                analysis_quality=quality_score
            )
            
        except Exception as e:
            print(f"Confluence analysis error: {e}")
            return self._error_result()
    
    def _filter_valid_factors(self, factors: List[FactorData]) -> List[FactorData]:
        """Filter factors meeting minimum quality requirements."""
        valid_factors = []
        
        for factor in factors:
            try:
                # Check minimum strength
                if factor.strength_score < self.min_factor_strength:
                    continue
                
                # Check confidence score
                if hasattr(factor, 'confidence_score') and factor.confidence_score < 0.4:
                    continue
                
                # Check valid direction
                if factor.direction_bias not in [DirectionBias.BULLISH, DirectionBias.BEARISH, DirectionBias.NEUTRAL]:
                    continue
                
                valid_factors.append(factor)
                
            except Exception as e:
                print(f"Factor validation error: {e}")
                continue
        
        return valid_factors
    
    def _group_factors_by_analyzer(self, factors: List[FactorData]) -> Dict[str, List[FactorData]]:
        """Group factors by their source analyzer."""
        groups = {}
        
        for factor in factors:
            analyzer_name = factor.analyzer_name
            if analyzer_name not in groups:
                groups[analyzer_name] = []
            groups[analyzer_name].append(factor)
        
        return groups
    
    def _calculate_analyzer_consensus(self, analyzer_groups: Dict[str, List[FactorData]]) -> Dict[str, Dict[str, Any]]:
        """Calculate consensus direction and strength for each analyzer."""
        consensus = {}
        
        for analyzer_name, factors in analyzer_groups.items():
            try:
                # Calculate direction scores
                bullish_score = sum(f.strength_score for f in factors if f.direction_bias == DirectionBias.BULLISH)
                bearish_score = sum(f.strength_score for f in factors if f.direction_bias == DirectionBias.BEARISH)
                neutral_score = sum(f.strength_score for f in factors if f.direction_bias == DirectionBias.NEUTRAL)
                
                total_score = bullish_score + bearish_score + neutral_score
                
                if total_score == 0:
                    continue
                
                # Determine dominant direction
                if bullish_score > bearish_score and bullish_score > neutral_score:
                    direction = DirectionBias.BULLISH
                    strength = bullish_score / total_score
                elif bearish_score > bullish_score and bearish_score > neutral_score:
                    direction = DirectionBias.BEARISH
                    strength = bearish_score / total_score
                else:
                    direction = DirectionBias.NEUTRAL
                    strength = max(neutral_score, max(bullish_score, bearish_score)) / total_score
                
                # Calculate quality metrics
                factor_count = len(factors)
                avg_confidence = np.mean([getattr(f, 'confidence_score', 0.5) for f in factors])
                
                consensus[analyzer_name] = {
                    'direction': direction,
                    'strength': strength,
                    'factor_count': factor_count,
                    'avg_confidence': avg_confidence,
                    'bullish_score': bullish_score,
                    'bearish_score': bearish_score,
                    'neutral_score': neutral_score,
                    'total_score': total_score
                }
                
            except Exception as e:
                print(f"Analyzer consensus error for {analyzer_name}: {e}")
                continue
        
        return consensus
    
    def _determine_direction_consensus(self, analyzer_consensus: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """Determine overall direction consensus using 3-of-4 rule."""
        
        # Count directions
        direction_votes = {
            DirectionBias.BULLISH: [],
            DirectionBias.BEARISH: [],
            DirectionBias.NEUTRAL: []
        }
        
        for analyzer_name, consensus in analyzer_consensus.items():
            direction = consensus['direction']
            strength = consensus['strength']
            
            # Weight vote by strength and confidence
            vote_weight = strength * consensus['avg_confidence']
            
            direction_votes[direction].append({
                'analyzer': analyzer_name,
                'strength': strength,
                'weight': vote_weight,
                'confidence': consensus['avg_confidence']
            })
        
        # Calculate weighted scores
        bullish_weight = sum(vote['weight'] for vote in direction_votes[DirectionBias.BULLISH])
        bearish_weight = sum(vote['weight'] for vote in direction_votes[DirectionBias.BEARISH])
        neutral_weight = sum(vote['weight'] for vote in direction_votes[DirectionBias.NEUTRAL])
        
        # Determine winning direction
        if bullish_weight > bearish_weight and bullish_weight > neutral_weight:
            winning_direction = DirectionBias.BULLISH
            agreement_analyzers = [vote['analyzer'] for vote in direction_votes[DirectionBias.BULLISH]]
            disagreement_analyzers = [vote['analyzer'] for vote in direction_votes[DirectionBias.BEARISH] + direction_votes[DirectionBias.NEUTRAL]]
        elif bearish_weight > bullish_weight and bearish_weight > neutral_weight:
            winning_direction = DirectionBias.BEARISH
            agreement_analyzers = [vote['analyzer'] for vote in direction_votes[DirectionBias.BEARISH]]
            disagreement_analyzers = [vote['analyzer'] for vote in direction_votes[DirectionBias.BULLISH] + direction_votes[DirectionBias.NEUTRAL]]
        else:
            winning_direction = DirectionBias.NEUTRAL
            agreement_analyzers = [vote['analyzer'] for vote in direction_votes[DirectionBias.NEUTRAL]]
            disagreement_analyzers = [vote['analyzer'] for vote in direction_votes[DirectionBias.BULLISH] + direction_votes[DirectionBias.BEARISH]]
        
        return {
            'direction': winning_direction,
            'agreement_count': len(agreement_analyzers),
            'agreement_factors': agreement_analyzers,
            'disagreement_factors': disagreement_analyzers,
            'bullish_weight': bullish_weight,
            'bearish_weight': bearish_weight,
            'neutral_weight': neutral_weight
        }
    
    def _calculate_confluence_strength(self, analyzer_consensus: Dict[str, Dict[str, Any]], 
                                     direction: DirectionBias) -> float:
        """Calculate overall confluence strength score."""
        
        if not analyzer_consensus:
            return 0.0
        
        try:
            # Get analyzers agreeing with the direction
            agreeing_analyzers = [
                consensus for consensus in analyzer_consensus.values() 
                if consensus['direction'] == direction
            ]
            
            if not agreeing_analyzers:
                return 0.0
            
            # Calculate strength based on:
            # 1. Number of agreeing analyzers
            # 2. Average strength of agreeing analyzers
            # 3. Confidence levels
            
            agreement_ratio = len(agreeing_analyzers) / len(analyzer_consensus)
            avg_strength = np.mean([a['strength'] for a in agreeing_analyzers])
            avg_confidence = np.mean([a['avg_confidence'] for a in agreeing_analyzers])
            
            # Combined strength score
            confluence_strength = (agreement_ratio * 0.4) + (avg_strength * 0.4) + (avg_confidence * 0.2)
            
            return min(1.0, confluence_strength)
            
        except Exception as e:
            print(f"Strength calculation error: {e}")
            return 0.0
    
    def _calculate_confidence_score(self, analyzer_consensus: Dict[str, Dict[str, Any]], 
                                  direction_result: Dict[str, Any]) -> float:
        """Calculate confidence in the confluence result."""
        
        try:
            # Factors affecting confidence:
            # 1. Agreement count (more = better)
            # 2. Margin of victory (clear winner vs close)
            # 3. Average analyzer confidence
            # 4. Data quality
            
            agreement_count = direction_result['agreement_count']
            total_analyzers = len(analyzer_consensus)
            
            # Agreement factor
            agreement_factor = agreement_count / max(1, total_analyzers)
            
            # Margin factor (how decisive was the win)
            weights = [
                direction_result['bullish_weight'],
                direction_result['bearish_weight'], 
                direction_result['neutral_weight']
            ]
            weights.sort(reverse=True)
            
            if len(weights) >= 2 and weights[1] > 0:
                margin_factor = (weights[0] - weights[1]) / weights[0]
            else:
                margin_factor = 1.0
            
            # Average confidence of all analyzers
            if analyzer_consensus:
                avg_analyzer_confidence = np.mean([a['avg_confidence'] for a in analyzer_consensus.values()])
            else:
                avg_analyzer_confidence = 0.5
            
            # Combine factors
            confidence = (agreement_factor * 0.4) + (margin_factor * 0.3) + (avg_analyzer_confidence * 0.3)
            
            return min(1.0, max(0.0, confidence))
            
        except Exception as e:
            print(f"Confidence calculation error: {e}")
            return 0.5
    
    def _analyze_agreement_quality(self, analyzer_consensus: Dict[str, Dict[str, Any]], 
                                 all_factors: List[FactorData]) -> float:
        """Analyze the quality of the agreement."""
        
        try:
            if not analyzer_consensus or not all_factors:
                return 0.0
            
            # Quality factors:
            # 1. Number of participating analyzers
            # 2. Factor diversity (different types of factors)
            # 3. Time consistency
            # 4. Statistical significance
            
            # Analyzer participation
            analyzer_participation = len(analyzer_consensus) / 4.0  # 4 core analyzers
            
            # Factor diversity (different factor names)
            factor_names = set(f.factor_name for f in all_factors)
            diversity_score = min(1.0, len(factor_names) / 8.0)  # Normalize to 8 expected types
            
            # Average factor strength
            avg_factor_strength = np.mean([f.strength_score for f in all_factors])
            
            # Time recency (prefer recent factors)
            if all_factors:
                timestamps = [f.timestamp for f in all_factors if hasattr(f, 'timestamp')]
                if timestamps:
                    latest_time = max(timestamps)
                    time_scores = [(latest_time - ts).total_seconds() / 3600 for ts in timestamps]  # Hours ago
                    recency_score = max(0.0, 1.0 - (np.mean(time_scores) / 24))  # Decay over 24 hours
                else:
                    recency_score = 0.5
            else:
                recency_score = 0.0
            
            # Combine quality factors
            quality_score = (
                analyzer_participation * 0.3 +
                diversity_score * 0.25 +
                avg_factor_strength * 0.25 +
                recency_score * 0.2
            )
            
            return min(1.0, max(0.0, quality_score))
            
        except Exception as e:
            print(f"Quality analysis error: {e}")
            return 0.5
    
    def _no_consensus_result(self, factors: List[FactorData]) -> ConfluenceResult:
        """Return result when no consensus is possible."""
        return ConfluenceResult(
            direction=DirectionBias.NEUTRAL,
            strength=0.0,
            confidence=0.1,
            agreement_count=0,
            agreement_factors=[],
            disagreement_factors=[f.analyzer_name for f in factors],
            analysis_quality=0.1
        )
    
    def _error_result(self) -> ConfluenceResult:
        """Return result when analysis fails."""
        return ConfluenceResult(
            direction=DirectionBias.NEUTRAL,
            strength=0.0,
            confidence=0.0,
            agreement_count=0,
            agreement_factors=[],
            disagreement_factors=[],
            analysis_quality=0.0
        )
