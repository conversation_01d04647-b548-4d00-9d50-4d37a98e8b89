#!/usr/bin/env python3
"""
CORE Flow Detection System - Configuration Package

All system configuration and constants.
"""

from .constants import *
from .settings import (
    get_config, get_analyzer_config, update_config,
    SYSTEM_CONFIG, API_CONFIG, ANAL<PERSON><PERSON><PERSON>_CONFIG, 
    CONFLUENCE_CONFIG, S<PERSON><PERSON><PERSON>_CONFIG, DATA_CONFIG,
    OUTPUT_CONFIG, ML_CONFIG, TESTING_CONFIG
)

__all__ = [
    'get_config',
    'get_analyzer_config', 
    'update_config',
    'SYSTEM_CONFIG',
    'API_CONFIG',
    'ANALYZER_CONFIG',
    'CONFLUENCE_CONFIG', 
    '<PERSON><PERSON><PERSON><PERSON>_CONFIG',
    '<PERSON><PERSON>A_CONFIG',
    'OUTPUT_CONFIG',
    'ML_CONFIG',
    'TESTING_CONFIG',
    'FLOW_PHYSICS',
    'VOLUME_ANALYSIS',
    'LIQUIDITY_ANALYSIS',
    'GEX_ANALYSIS',
    'CONFLUENCE_ENGINE',
    '<PERSON><PERSON><PERSON><PERSON>_GENERATOR',
    '<PERSON><PERSON><PERSON>_QUALITY',
    'TIMEFRAMES',
    '<PERSON><PERSON>_CONSTANTS',
    'PERFORMANCE',
    'VALIDATION'
]
