@echo off
REM Simple MCP Server Starter - No Unicode Issues

echo.
echo ================================================================
echo              Liquidity Sweep MCP Server
echo ================================================================
echo.

cd /d "D:\script-work\Liquidity_Sweep\api_robustness"

echo Starting MCP Server...
echo Press Ctrl+C to stop the server
echo.

py mcp_server_production.py --config "mcp_installation\config\config.json" --log-level INFO

echo.
echo MCP Server stopped.
pause
