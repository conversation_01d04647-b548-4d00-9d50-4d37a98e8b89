"""
Liquidity Features Package

This package contains specialized feature extraction modules for liquidity analysis.
"""
import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))



# Import key components for easier access
try:
    from order_book_features import OrderBookFeatureExtractor
except ImportError:
    pass
