#!/usr/bin/env python3
"""
CORE Flow Detection System - Signal Generator

Stripped, optimized signal generation from confluence results.
Clean decision logic with range-based confidence scores.

Based on proven Liquidity_Sweep signal engine, stripped to essentials.
"""

import uuid
from enum import Enum
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

# Import from CORE components
from data.factor_spec import DirectionBias, TimeFrame
from config.constants import SIGNAL_GENERATOR
from engine.confluence_engine import ConfluenceResult

class SignalType(Enum):
    """Core signal types."""
    FLOW_BULLISH = "flow_bullish"
    FLOW_BEARISH = "flow_bearish" 
    RANGE_BOUND = "range_bound"
    BREAKOUT_PENDING = "breakout_pending"
    NO_SIGNAL = "no_signal"

class SignalStrength(Enum):
    """Signal strength levels."""
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    EXTREME = 4

@dataclass
class StrategySignal:
    """Clean strategy signal output."""
    signal_id: str
    timestamp: datetime
    ticker: str
    signal_type: SignalType
    direction: DirectionBias
    strength: SignalStrength
    confidence: float
    entry_confidence: float
    risk_level: str
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'signal_id': self.signal_id,
            'timestamp': self.timestamp.isoformat(),
            'ticker': self.ticker,
            'signal_type': self.signal_type.value,
            'direction': self.direction.value,
            'strength': self.strength.value,
            'strength_name': self.strength.name,
            'confidence': self.confidence,
            'entry_confidence': self.entry_confidence,
            'risk_level': self.risk_level,
            'metadata': self.metadata
        }

class SignalGenerator:
    """
    Core signal generator - confluence results to strategy signals.
    Simple decision logic with mathematical precision.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Core thresholds from constants
        self.min_signal_confidence = SIGNAL_GENERATOR['MIN_SIGNAL_CONFIDENCE']
        self.strong_signal_threshold = SIGNAL_GENERATOR['STRONG_SIGNAL_THRESHOLD']
        self.extreme_signal_threshold = SIGNAL_GENERATOR['EXTREME_SIGNAL_THRESHOLD']
        self.min_agreement_for_signal = SIGNAL_GENERATOR['MIN_AGREEMENT_FOR_SIGNAL']
        self.risk_thresholds = SIGNAL_GENERATOR['RISK_LEVEL_THRESHOLDS']
    
    def generate_signal(self, ticker: str, confluence_result: ConfluenceResult, 
                       current_price: float, additional_context: Dict[str, Any] = None) -> StrategySignal:
        """
        Generate strategy signal from confluence analysis.
        
        Args:
            ticker: Symbol being analyzed
            confluence_result: Output from confluence engine
            current_price: Current market price
            additional_context: Optional additional market context
            
        Returns:
            StrategySignal: Final trading signal
        """
        try:
            additional_context = additional_context or {}
            
            # Determine signal type and direction
            signal_type, direction = self._determine_signal_type(confluence_result)
            
            # Calculate signal strength
            strength = self._calculate_signal_strength(confluence_result)
            
            # Calculate entry confidence
            entry_confidence = self._calculate_entry_confidence(confluence_result, additional_context)
            
            # Determine risk level
            risk_level = self._determine_risk_level(confluence_result, strength)
            
            # Create metadata
            metadata = self._create_signal_metadata(confluence_result, additional_context, current_price)
            
            return StrategySignal(
                signal_id=str(uuid.uuid4()),
                timestamp=datetime.now(),
                ticker=ticker,
                signal_type=signal_type,
                direction=direction,
                strength=strength,
                confidence=confluence_result.confidence,
                entry_confidence=entry_confidence,
                risk_level=risk_level,
                metadata=metadata
            )
            
        except Exception as e:
            print(f"Signal generation error for {ticker}: {e}")
            return self._no_signal_result(ticker)
    
    def _determine_signal_type(self, confluence_result: ConfluenceResult) -> tuple[SignalType, DirectionBias]:
        """Determine signal type and direction from confluence."""
        
        # Check minimum requirements
        if confluence_result.confidence < self.min_signal_confidence:
            return SignalType.NO_SIGNAL, DirectionBias.NEUTRAL
        
        if confluence_result.agreement_count < self.min_agreement_for_signal:
            return SignalType.NO_SIGNAL, DirectionBias.NEUTRAL
        
        # Determine signal based on direction and quality
        direction = confluence_result.direction
        strength = confluence_result.strength
        quality = confluence_result.analysis_quality
        
        # Strong directional signals
        if direction == DirectionBias.BULLISH and strength > 0.6 and quality > 0.6:
            return SignalType.FLOW_BULLISH, DirectionBias.BULLISH
        
        if direction == DirectionBias.BEARISH and strength > 0.6 and quality > 0.6:
            return SignalType.FLOW_BEARISH, DirectionBias.BEARISH
        
        # Range-bound conditions
        if direction == DirectionBias.NEUTRAL or strength < 0.4:
            return SignalType.RANGE_BOUND, DirectionBias.NEUTRAL
        
        # Pending breakout (moderate directional signal)
        if strength > 0.4 and quality > 0.5:
            return SignalType.BREAKOUT_PENDING, direction
        
        return SignalType.NO_SIGNAL, DirectionBias.NEUTRAL
    
    def _calculate_signal_strength(self, confluence_result: ConfluenceResult) -> SignalStrength:
        """Calculate signal strength level."""
        
        # Combine multiple strength factors
        confluence_strength = confluence_result.strength
        confidence = confluence_result.confidence
        quality = confluence_result.analysis_quality
        agreement_factor = confluence_result.agreement_count / 4.0  # Normalize to 4 analyzers
        
        # Weighted strength score
        strength_score = (
            confluence_strength * 0.4 +
            confidence * 0.3 +
            quality * 0.2 +
            agreement_factor * 0.1
        )
        
        # Map to strength levels
        if strength_score >= self.extreme_signal_threshold:
            return SignalStrength.EXTREME
        elif strength_score >= self.strong_signal_threshold:
            return SignalStrength.STRONG
        elif strength_score >= 0.5:
            return SignalStrength.MODERATE
        else:
            return SignalStrength.WEAK
    
    def _calculate_entry_confidence(self, confluence_result: ConfluenceResult, 
                                  additional_context: Dict[str, Any]) -> float:
        """Calculate confidence for entry timing."""
        
        base_confidence = confluence_result.confidence
        
        # Adjust based on additional context
        adjustments = 0.0
        
        # Market conditions adjustment
        market_volatility = additional_context.get('market_volatility', 0.5)
        if market_volatility < 0.3:  # Low volatility = higher confidence
            adjustments += 0.1
        elif market_volatility > 0.7:  # High volatility = lower confidence
            adjustments -= 0.1
        
        # Time of day adjustment (placeholder for ML enhancement)
        current_hour = datetime.now().hour
        if 9 <= current_hour <= 16:  # Market hours
            adjustments += 0.05
        
        # Agreement quality adjustment
        if confluence_result.agreement_count >= 3:
            adjustments += 0.1
        
        # Analysis quality bonus
        if confluence_result.analysis_quality > 0.8:
            adjustments += 0.05
        
        entry_confidence = base_confidence + adjustments
        return max(0.0, min(1.0, entry_confidence))
    
    def _determine_risk_level(self, confluence_result: ConfluenceResult, 
                            strength: SignalStrength) -> str:
        """Determine risk level for the signal."""
        
        confidence = confluence_result.confidence
        quality = confluence_result.analysis_quality
        
        # Risk assessment matrix
        risk_score = (confidence * 0.6) + (quality * 0.4)
        
        # Adjust for signal strength
        if strength == SignalStrength.EXTREME:
            risk_score += 0.1
        elif strength == SignalStrength.WEAK:
            risk_score -= 0.1
        
        # Map to risk levels
        if risk_score >= self.risk_thresholds['LOW']:
            return 'LOW'
        elif risk_score >= self.risk_thresholds['MEDIUM']:
            return 'MEDIUM'
        else:
            return 'HIGH'
    
    def _create_signal_metadata(self, confluence_result: ConfluenceResult, 
                              additional_context: Dict[str, Any], 
                              current_price: float) -> Dict[str, Any]:
        """Create comprehensive signal metadata."""
        
        return {
            # Confluence details
            'confluence_strength': confluence_result.strength,
            'confluence_confidence': confluence_result.confidence,
            'agreement_count': confluence_result.agreement_count,
            'analysis_quality': confluence_result.analysis_quality,
            'agreeing_analyzers': confluence_result.agreement_factors,
            'disagreeing_analyzers': confluence_result.disagreement_factors,
            
            # Market context
            'current_price': current_price,
            'generation_timestamp': datetime.now().isoformat(),
            
            # Additional context
            'market_context': additional_context,
            
            # Signal generation parameters
            'min_signal_confidence': self.min_signal_confidence,
            'min_agreement_required': self.min_agreement_for_signal,
            
            # Quality indicators
            'data_quality_score': confluence_result.analysis_quality,
            'signal_reliability': 'HIGH' if confluence_result.confidence > 0.8 else 'MEDIUM' if confluence_result.confidence > 0.6 else 'LOW'
        }
    
    def _no_signal_result(self, ticker: str) -> StrategySignal:
        """Return no-signal result."""
        return StrategySignal(
            signal_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            ticker=ticker,
            signal_type=SignalType.NO_SIGNAL,
            direction=DirectionBias.NEUTRAL,
            strength=SignalStrength.WEAK,
            confidence=0.0,
            entry_confidence=0.0,
            risk_level='HIGH',
            metadata={'reason': 'Insufficient confluence or confidence'}
        )
    
    def generate_batch_signals(self, signal_requests: List[Dict[str, Any]]) -> List[StrategySignal]:
        """Generate multiple signals efficiently."""
        signals = []
        
        for request in signal_requests:
            try:
                signal = self.generate_signal(
                    ticker=request['ticker'],
                    confluence_result=request['confluence_result'],
                    current_price=request['current_price'],
                    additional_context=request.get('additional_context')
                )
                signals.append(signal)
                
            except Exception as e:
                print(f"Batch signal generation error: {e}")
                signals.append(self._no_signal_result(request.get('ticker', 'UNKNOWN')))
        
        return signals
    
    def validate_signal(self, signal: StrategySignal) -> Dict[str, Any]:
        """Validate signal quality and consistency."""
        
        validation_results = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        try:
            # Check signal consistency
            if signal.signal_type == SignalType.NO_SIGNAL and signal.confidence > 0.5:
                validation_results['warnings'].append("High confidence with no signal")
            
            if signal.strength == SignalStrength.EXTREME and signal.confidence < 0.7:
                validation_results['warnings'].append("Extreme strength with low confidence")
            
            if signal.risk_level == 'LOW' and signal.confidence < 0.6:
                validation_results['warnings'].append("Low risk with low confidence")
            
            # Check required fields
            required_fields = ['signal_id', 'ticker', 'signal_type', 'direction']
            for field in required_fields:
                if not hasattr(signal, field) or getattr(signal, field) is None:
                    validation_results['errors'].append(f"Missing required field: {field}")
                    validation_results['is_valid'] = False
            
            # Range checks
            if not (0.0 <= signal.confidence <= 1.0):
                validation_results['errors'].append("Confidence out of range [0,1]")
                validation_results['is_valid'] = False
            
            if not (0.0 <= signal.entry_confidence <= 1.0):
                validation_results['errors'].append("Entry confidence out of range [0,1]")
                validation_results['is_valid'] = False
            
        except Exception as e:
            validation_results['errors'].append(f"Validation error: {e}")
            validation_results['is_valid'] = False
        
        return validation_results
