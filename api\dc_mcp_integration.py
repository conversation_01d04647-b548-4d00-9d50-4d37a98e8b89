#!/usr/bin/env python3
"""
Desktop Commander MCP Integration Script
Place this in your Desktop Commander scripts directory
"""

import subprocess
import sys
import os

def run_mcp_command(command="status"):
    """Run MCP command through Desktop Commander."""
    mcp_script = r"D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py"
    
    try:
        result = subprocess.run(
            ["py", mcp_script, command],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        print(result.stdout)
        if result.stderr:
            print("Warnings:", result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error running MCP command: {e}")
        return False

if __name__ == "__main__":
    command = sys.argv[1] if len(sys.argv) > 1 else "status"
    success = run_mcp_command(command)
    sys.exit(0 if success else 1)
