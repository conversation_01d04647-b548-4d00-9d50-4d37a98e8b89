#!/usr/bin/env python3
"""
Fair Value Gap (FVG) Specialist Agent
Gap Detection + Fill Probability + Time Decay Analysis

Implementation Date: 2025-06-24
Mathematical Foundation: 67% Base Probability (Empirically Validated)
Precision Standard: Gap detection with volume confirmation
Performance Budget: 5 seconds execution
"""

import math
import time
import uuid
import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import numpy as np
from datetime import datetime, timedelta

from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus, TaskPriority

logger = logging.getLogger(__name__)

@dataclass
class FVGConfig:
    """Configuration for Fair Value Gap Specialist Agent"""
    # Performance parameters
    timeout_ms: int = 5000
    precision_tolerance: float = 1e-10
    
    # FVG detection parameters
    base_probability: float = 67.0  # EMPIRICAL CONSTANT - NEVER CHANGE
    volume_threshold_multiplier: float = 1.5  # Institutional volume threshold
    
    # Time decay parameters
    time_decay_per_session: float = 0.05  # 5% probability reduction per session
    distance_penalty_factor: float = 20.0  # Distance-based probability reduction
    
    # Gap validation parameters
    min_gap_size_atr: float = 0.1  # Minimum gap size relative to ATR
    max_gap_age_sessions: int = 20  # Maximum sessions to track gaps
    
    # Clustering parameters
    cluster_tolerance_pct: float = 0.5  # 0.5% price range for clustering

@dataclass
class FairValueGap:
    """Data structure for individual Fair Value Gap"""
    gap_id: str
    gap_type: str  # 'bullish' or 'bearish'
    creation_time: datetime
    session_created: int
    
    # Gap boundaries
    gap_top: float
    gap_bottom: float
    gap_midpoint: float
    gap_size: float
    
    # Volume confirmation
    creation_volume: float
    average_volume: float
    volume_confirmed: bool
    
    # Probability tracking
    initial_probability: float
    current_probability: float
    distance_factor: float
    time_decay_factor: float
    
    # Status tracking
    is_filled: bool = False
    fill_price: float = 0.0
    fill_time: Optional[datetime] = None
    sessions_elapsed: int = 0

class FVGSpecialist(BaseAgent):
    """
    Fair Value Gap Specialist Agent
    
    Implements mathematical precision gap analysis using:
    - Candle-based gap detection algorithm
    - Volume confirmation (institutional threshold)
    - Time decay probability adjustment (5% per session)
    - Distance factor calculations
    - Gap clustering analysis
    
    Mathematical Requirements:
    - Base probability: 67% (empirically validated)
    - Volume threshold: 1.5x average volume
    - Time decay: Linear reduction over sessions
    - Distance penalty: Exponential with price distance
    """
    
    def __init__(self, config: Optional[FVGConfig] = None):
        super().__init__(agent_id="fvg_specialist")
        self.config = config or FVGConfig()
        self.logger = logger
        
        # Active gaps tracking
        self.active_gaps: List[FairValueGap] = []
        self.gap_history: List[FairValueGap] = []
        
        # Performance tracking
        self.gap_detection_metrics = {}
        self.probability_accuracy_metrics = {}
        
        self.logger.info("FVG Specialist Agent initialized")
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """
        Validate task inputs for FVG analysis
        
        Args:
            task: AgentTask with market data inputs
            
        Returns:
            bool: True if inputs valid for gap detection
        """
        try:
            inputs = task.inputs
            
            # Required fields validation
            if 'market_data' not in inputs:
                self.logger.error("Missing required field: market_data")
                return False
            
            market_data = inputs['market_data']
            
            # Market data structure validation
            required_fields = ['high', 'low', 'close', 'volume', 'timestamp']
            for field in required_fields:
                if field not in market_data:
                    self.logger.error(f"Missing market data field: {field}")
                    return False
            
            # Minimum data length (need at least 3 candles for gap detection)
            min_length = 10
            for field in ['high', 'low', 'close', 'volume']:
                if len(market_data[field]) < min_length:
                    self.logger.error(f"Insufficient data length for {field}: {len(market_data[field])} < {min_length}")
                    return False
            
            # Numerical validation
            for field in ['high', 'low', 'close', 'volume']:
                data_array = np.array(market_data[field])
                if np.any(np.isnan(data_array)) or np.any(np.isinf(data_array)):
                    self.logger.error(f"Invalid numerical data in {field}")
                    return False
                
                if np.any(data_array <= 0):
                    self.logger.error(f"Non-positive values in {field}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Input validation failed: {e}")
            return False
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """
        Validate outputs meet FVG analysis standards
        
        Args:
            outputs: Task execution outputs
            
        Returns:
            Dict[str, float]: Quality metrics for validation
        """
        quality_metrics = {}
        
        try:
            # Required output fields
            required_fields = [
                'active_fvgs', 'nearest_fvg_up', 'nearest_fvg_down',
                'fvg_fill_probability', 'gap_size_atr', 'volume_confirmation',
                'time_decay_factor', 'distance_factor', 'confluence_zones', 'fvg_signal'
            ]
            
            completeness = sum(1 for field in required_fields if field in outputs) / len(required_fields)
            quality_metrics['completeness'] = completeness
            
            # Probability validation (must be within 15-95% range)
            if 'fvg_fill_probability' in outputs:
                prob = outputs['fvg_fill_probability']
                if 15.0 <= prob <= 95.0:
                    quality_metrics['probability_bounds'] = 1.0
                else:
                    quality_metrics['probability_bounds'] = 0.0
            else:
                quality_metrics['probability_bounds'] = 0.0
            
            # Gap detection validation
            if 'active_fvgs' in outputs:
                active_gaps = outputs['active_fvgs']
                if isinstance(active_gaps, list):
                    # Validate gap structure
                    gap_validity = 1.0
                    for gap in active_gaps:
                        if not self._validate_gap_structure(gap):
                            gap_validity = 0.5
                            break
                    quality_metrics['gap_structure_validity'] = gap_validity
                else:
                    quality_metrics['gap_structure_validity'] = 0.0
            else:
                quality_metrics['gap_structure_validity'] = 0.0
            
            # Volume confirmation validation
            if 'volume_confirmation' in outputs:
                quality_metrics['volume_analysis'] = 1.0
            else:
                quality_metrics['volume_analysis'] = 0.0
            
            # Mathematical precision validation
            precision_score = 1.0
            for field in ['nearest_fvg_up', 'nearest_fvg_down', 'gap_size_atr']:
                if field in outputs and isinstance(outputs[field], (int, float)):
                    value = outputs[field]
                    if math.isnan(value) or math.isinf(value):
                        precision_score = 0.0
                        break
            
            quality_metrics['mathematical_precision'] = precision_score
            
            # Overall quality score
            avg_quality = sum(quality_metrics.values()) / len(quality_metrics)
            quality_metrics['overall_quality'] = avg_quality
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            return {'error': 0.0, 'overall_quality': 0.0}
    
    def _validate_gap_structure(self, gap: Dict[str, Any]) -> bool:
        """Validate individual gap data structure"""
        required_gap_fields = ['gap_type', 'gap_top', 'gap_bottom', 'current_probability']
        return all(field in gap for field in required_gap_fields)
    
    def execute(self, ticker: str) -> Dict[str, Any]:
        """
        Execute FVG analysis for ultimate orchestrator compatibility

        Args:
            ticker: Stock symbol to analyze

        Returns:
            Dict: FVG analysis results
        """
        try:
            # Get market data from data ingestion agent
            from agents.data_ingestion_agent import DataIngestionAgent
            data_agent = DataIngestionAgent("data_ingestion_agent")
            data_result = data_agent.execute([ticker], source="schwab")

            if not data_result or 'data' not in data_result:
                return {'error': 'Failed to get market data'}

            # Extract price data
            ticker_data = data_result['data'].get(ticker, {})
            if 'bars' not in ticker_data:
                return {'error': 'No price bars data available'}

            bars_df = ticker_data['bars']
            if bars_df.empty:
                return {'error': 'Empty price data'}

            # Convert DataFrame to expected market_data format
            market_data = {
                'high': bars_df['h'].tolist() if 'h' in bars_df.columns else bars_df['high'].tolist(),
                'low': bars_df['l'].tolist() if 'l' in bars_df.columns else bars_df['low'].tolist(),
                'close': bars_df['c'].tolist() if 'c' in bars_df.columns else bars_df['close'].tolist(),
                'volume': bars_df['v'].tolist() if 'v' in bars_df.columns else bars_df['volume'].tolist(),
                'timestamp': bars_df['t'].tolist() if 't' in bars_df.columns else list(range(len(bars_df)))
            }

            # Create task with market data
            task_data = {
                'symbol': ticker,
                'task_type': 'fvg_analysis',
                'timestamp': datetime.now().isoformat(),
                'market_data': market_data
            }

            # Create AgentTask
            task = AgentTask(
                task_id=str(uuid.uuid4()),
                task_type='fvg_analysis',
                agent_type='fvg_specialist',
                priority=TaskPriority.NORMAL,
                inputs=task_data,
                workflow_file='fvg_analysis_workflow.json',
                quality_standards='67% base probability with volume confirmation',
                performance_targets={'execution_time': 5.0, 'precision': 0.999},
                dependencies=[],
                training_data_tags=['fvg', 'fair_value_gap'],
                timestamp=datetime.now()
            )

            # Execute the task
            result = self.execute_task(task)

            if result.status == TaskStatus.COMPLETED:
                return result.outputs
            else:
                return {'error': f'FVG analysis failed: {result.outputs.get("error", "Unknown error")}'}

        except Exception as e:
            self.logger.error(f"FVG execute failed: {e}")
            return {'error': str(e)}

    def execute_task(self, task: AgentTask) -> AgentResult:
        """
        Execute Fair Value Gap analysis
        
        Args:
            task: AgentTask containing market data
            
        Returns:
            AgentResult: Complete FVG analysis with probabilities
        """
        start_time = time.time()
        
        try:
            market_data = task.inputs['market_data']
            
            # Convert to numpy arrays for mathematical operations
            high = np.array(market_data['high'], dtype=np.float64)
            low = np.array(market_data['low'], dtype=np.float64)
            close = np.array(market_data['close'], dtype=np.float64)
            volume = np.array(market_data['volume'], dtype=np.float64)
            timestamps = market_data.get('timestamp', [])
            
            current_price = close[-1]
            current_session = len(close) - 1
            
            # Step 1: Update existing gaps (time decay, fill status)
            self._update_existing_gaps(high, low, close, current_session)
            
            # Step 2: Detect new Fair Value Gaps
            new_gaps = self._detect_new_gaps(high, low, close, volume, timestamps, current_session)
            
            # Step 3: Add new gaps to active tracking
            for gap in new_gaps:
                self.active_gaps.append(gap)
            
            # Step 4: Calculate average volume for institutional threshold
            avg_volume = np.mean(volume[-20:]) if len(volume) >= 20 else np.mean(volume)
            
            # Step 5: Calculate ATR for gap size normalization
            atr_value = self._calculate_atr(high, low, close, 14)
            
            # Step 6: Find nearest gaps for current price
            nearest_up, nearest_down = self._find_nearest_gaps(current_price)
            
            # Step 7: Calculate overall fill probability
            fill_probability = self._calculate_overall_fill_probability(current_price, atr_value)
            
            # Step 8: Analyze gap clustering and confluence zones
            confluence_zones = self._analyze_gap_clusters()
            
            # Step 9: Calculate time decay and distance factors
            time_decay_factor, distance_factor = self._calculate_decay_factors(current_price)
            
            # Step 10: Generate FVG signal
            fvg_signal = self._generate_fvg_signal(current_price, nearest_up, nearest_down)
            
            # Step 11: Prepare active gaps data for output
            active_gaps_data = [self._gap_to_dict(gap) for gap in self.active_gaps]
            
            # Compile results
            outputs = {
                'active_fvgs': active_gaps_data,
                'nearest_fvg_up': nearest_up if nearest_up else 0.0,
                'nearest_fvg_down': nearest_down if nearest_down else 0.0,
                'fvg_fill_probability': float(fill_probability),
                'gap_size_atr': float(atr_value),
                'volume_confirmation': bool(volume[-1] > avg_volume * self.config.volume_threshold_multiplier),
                'time_decay_factor': float(time_decay_factor),
                'distance_factor': float(distance_factor),
                'confluence_zones': confluence_zones,
                'fvg_signal': fvg_signal,
                'total_active_gaps': len(self.active_gaps),
                'new_gaps_detected': len(new_gaps),
                'average_volume': float(avg_volume),
                'institutional_volume_threshold': float(avg_volume * self.config.volume_threshold_multiplier),
                'current_price': float(current_price),
                'mathematical_validation': True,
                'calculation_timestamp': datetime.now().isoformat()
            }
            
            execution_time = time.time() - start_time
            
            # Performance validation
            if execution_time > (self.config.timeout_ms / 1000.0):
                self.logger.warning(f"Execution time {execution_time:.3f}s exceeded budget {self.config.timeout_ms/1000.0}s")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs=outputs,
                execution_time=execution_time,
                quality_metrics={}  # Will be populated by validate_outputs
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"FVG analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )    
    def _update_existing_gaps(self, high: np.ndarray, low: np.ndarray, 
                            close: np.ndarray, current_session: int):
        """
        Update existing gaps with fill status and time decay
        
        Args:
            high: High price array
            low: Low price array  
            close: Close price array
            current_session: Current session number
        """
        gaps_to_remove = []
        
        for gap in self.active_gaps:
            # Update sessions elapsed
            gap.sessions_elapsed = current_session - gap.session_created
            
            # Check if gap is filled
            if not gap.is_filled:
                if gap.gap_type == 'bullish':
                    # Bullish gap filled when price goes back down into gap
                    if low[-1] <= gap.gap_top:
                        gap.is_filled = True
                        gap.fill_price = min(gap.gap_top, low[-1])
                        gap.fill_time = datetime.now()
                elif gap.gap_type == 'bearish':
                    # Bearish gap filled when price goes back up into gap
                    if high[-1] >= gap.gap_bottom:
                        gap.is_filled = True
                        gap.fill_price = max(gap.gap_bottom, high[-1])
                        gap.fill_time = datetime.now()
            
            # Apply time decay to probability
            time_decay = gap.sessions_elapsed * self.config.time_decay_per_session
            gap.time_decay_factor = time_decay
            
            # Calculate current probability with time decay
            gap.current_probability = max(
                gap.initial_probability - time_decay - gap.distance_factor,
                15.0  # Minimum probability floor
            )
            
            # Remove gaps that are too old or filled
            if (gap.sessions_elapsed > self.config.max_gap_age_sessions or 
                gap.is_filled):
                gaps_to_remove.append(gap)
                self.gap_history.append(gap)
        
        # Remove old/filled gaps from active tracking
        for gap in gaps_to_remove:
            if gap in self.active_gaps:
                self.active_gaps.remove(gap)
    
    def _detect_new_gaps(self, high: np.ndarray, low: np.ndarray, 
                        close: np.ndarray, volume: np.ndarray,
                        timestamps: List, current_session: int) -> List[FairValueGap]:
        """
        Detect new Fair Value Gaps using 3-candle pattern
        
        Gap Detection Algorithm:
        - Bearish FVG: candle1_low > candle3_high (with volume confirmation)
        - Bullish FVG: candle1_high < candle3_low (with volume confirmation)
        
        Args:
            high: High price array
            low: Low price array
            close: Close price array
            volume: Volume array
            timestamps: Timestamp array
            current_session: Current session number
            
        Returns:
            List[FairValueGap]: Newly detected gaps
        """
        new_gaps = []
        
        # Need at least 3 candles for gap detection
        if len(high) < 3:
            return new_gaps
        
        try:
            # Get the last 3 candles for analysis
            candle1_high = high[-3]  # Two candles ago
            candle1_low = low[-3]
            candle1_volume = volume[-3]
            
            candle2_high = high[-2]  # One candle ago (middle candle)
            candle2_low = low[-2]
            candle2_volume = volume[-2]
            
            candle3_high = high[-1]  # Current candle
            candle3_low = low[-1]
            candle3_volume = volume[-1]
            
            # Calculate average volume for confirmation
            avg_volume = np.mean(volume[-20:]) if len(volume) >= 20 else np.mean(volume)
            institutional_threshold = avg_volume * self.config.volume_threshold_multiplier
            
            # Detect Bearish Fair Value Gap
            # Pattern: candle1_low > candle3_high (gap between them)
            if candle1_low > candle3_high:
                gap_volume = max(candle1_volume, candle2_volume, candle3_volume)
                volume_confirmed = gap_volume > institutional_threshold
                
                if volume_confirmed:
                    gap_id = f"bearish_fvg_{current_session}_{time.time()}"
                    gap_size = candle1_low - candle3_high
                    
                    # Calculate initial probability
                    initial_prob = self._calculate_initial_probability(
                        gap_size, close[-1], candle1_low, candle3_high
                    )
                    
                    bearish_gap = FairValueGap(
                        gap_id=gap_id,
                        gap_type='bearish',
                        creation_time=datetime.now(),
                        session_created=current_session,
                        gap_top=candle1_low,
                        gap_bottom=candle3_high,
                        gap_midpoint=(candle1_low + candle3_high) / 2.0,
                        gap_size=gap_size,
                        creation_volume=gap_volume,
                        average_volume=avg_volume,
                        volume_confirmed=volume_confirmed,
                        initial_probability=initial_prob,
                        current_probability=initial_prob,
                        distance_factor=0.0,
                        time_decay_factor=0.0
                    )
                    
                    new_gaps.append(bearish_gap)
            
            # Detect Bullish Fair Value Gap  
            # Pattern: candle1_high < candle3_low (gap between them)
            if candle1_high < candle3_low:
                gap_volume = max(candle1_volume, candle2_volume, candle3_volume)
                volume_confirmed = gap_volume > institutional_threshold
                
                if volume_confirmed:
                    gap_id = f"bullish_fvg_{current_session}_{time.time()}"
                    gap_size = candle3_low - candle1_high
                    
                    # Calculate initial probability
                    initial_prob = self._calculate_initial_probability(
                        gap_size, close[-1], candle3_low, candle1_high
                    )
                    
                    bullish_gap = FairValueGap(
                        gap_id=gap_id,
                        gap_type='bullish',
                        creation_time=datetime.now(),
                        session_created=current_session,
                        gap_top=candle3_low,
                        gap_bottom=candle1_high,
                        gap_midpoint=(candle3_low + candle1_high) / 2.0,
                        gap_size=gap_size,
                        creation_volume=gap_volume,
                        average_volume=avg_volume,
                        volume_confirmed=volume_confirmed,
                        initial_probability=initial_prob,
                        current_probability=initial_prob,
                        distance_factor=0.0,
                        time_decay_factor=0.0
                    )
                    
                    new_gaps.append(bullish_gap)
            
            return new_gaps
            
        except Exception as e:
            self.logger.error(f"Gap detection failed: {e}")
            return new_gaps
    
    def _calculate_initial_probability(self, gap_size: float, current_price: float,
                                     gap_top: float, gap_bottom: float) -> float:
        """
        Calculate initial fill probability for new gap
        
        Formula:
        base_probability = 67.0 (EMPIRICAL CONSTANT)
        distance_factor = abs(current_price - gap_midpoint) / current_price
        adjusted_probability = base_probability - (distance_factor * 20)
        
        Args:
            gap_size: Size of the gap
            current_price: Current market price
            gap_top: Top boundary of gap
            gap_bottom: Bottom boundary of gap
            
        Returns:
            float: Initial probability (15-95% range)
        """
        try:
            # Base probability (EMPIRICAL CONSTANT - NEVER CHANGE)
            base_prob = self.config.base_probability  # 67.0%
            
            # Calculate gap midpoint
            gap_midpoint = (gap_top + gap_bottom) / 2.0
            
            # Calculate distance factor
            distance_factor = abs(current_price - gap_midpoint) / current_price
            distance_penalty = distance_factor * self.config.distance_penalty_factor
            
            # Adjust probability
            adjusted_prob = base_prob - distance_penalty
            
            # Ensure probability stays within bounds
            final_prob = max(min(adjusted_prob, 95.0), 15.0)
            
            return float(final_prob)
            
        except Exception as e:
            self.logger.error(f"Probability calculation failed: {e}")
            return self.config.base_probability
    
    def _calculate_atr(self, high: np.ndarray, low: np.ndarray, 
                      close: np.ndarray, length: int) -> float:
        """Calculate Average True Range for gap size normalization"""
        try:
            tr_values = np.zeros(len(high))
            
            # First TR value
            tr_values[0] = high[0] - low[0]
            
            # Calculate True Range for each period
            for i in range(1, len(high)):
                tr1 = high[i] - low[i]
                tr2 = abs(high[i] - close[i-1])
                tr3 = abs(low[i] - close[i-1])
                tr_values[i] = max(tr1, tr2, tr3)
            
            # Return average of last 'length' periods
            return float(np.mean(tr_values[-length:]))
            
        except Exception as e:
            self.logger.error(f"ATR calculation failed: {e}")
            return float(np.mean(high[-length:] - low[-length:]))
    
    def _find_nearest_gaps(self, current_price: float) -> Tuple[Optional[float], Optional[float]]:
        """
        Find nearest unfilled gaps above and below current price
        
        Args:
            current_price: Current market price
            
        Returns:
            Tuple[Optional[float], Optional[float]]: (nearest_up, nearest_down)
        """
        nearest_up = None
        nearest_down = None
        
        for gap in self.active_gaps:
            if gap.is_filled:
                continue
            
            gap_level = gap.gap_midpoint
            
            # Gap above current price
            if gap_level > current_price:
                if nearest_up is None or gap_level < nearest_up:
                    nearest_up = gap_level
            
            # Gap below current price
            elif gap_level < current_price:
                if nearest_down is None or gap_level > nearest_down:
                    nearest_down = gap_level
        
        return nearest_up, nearest_down
    
    def _calculate_overall_fill_probability(self, current_price: float, atr_value: float) -> float:
        """
        Calculate overall probability of nearest gap being filled
        
        Args:
            current_price: Current market price
            atr_value: ATR for normalization
            
        Returns:
            float: Overall fill probability
        """
        if not self.active_gaps:
            return 0.0
        
        # Find the gap with highest probability
        max_probability = 0.0
        
        for gap in self.active_gaps:
            if not gap.is_filled:
                # Update distance factor for current price
                gap_midpoint = gap.gap_midpoint
                distance = abs(current_price - gap_midpoint) / current_price
                gap.distance_factor = distance * self.config.distance_penalty_factor
                
                # Recalculate current probability
                gap.current_probability = max(
                    gap.initial_probability - gap.time_decay_factor - gap.distance_factor,
                    15.0
                )
                
                max_probability = max(max_probability, gap.current_probability)
        
        return float(max_probability)
    
    def _analyze_gap_clusters(self) -> List[float]:
        """
        Analyze gap clustering and identify confluence zones
        
        Returns:
            List[float]: Price levels with multiple gap confluence
        """
        confluence_zones = []
        
        if len(self.active_gaps) < 2:
            return confluence_zones
        
        # Group gaps by price proximity
        gap_levels = [(gap.gap_midpoint, gap) for gap in self.active_gaps if not gap.is_filled]
        gap_levels.sort(key=lambda x: x[0])  # Sort by price level
        
        # Find clusters within tolerance percentage
        tolerance = self.config.cluster_tolerance_pct / 100.0
        
        clusters = []
        current_cluster = [gap_levels[0]]
        
        for i in range(1, len(gap_levels)):
            current_level = gap_levels[i][0]
            prev_level = gap_levels[i-1][0]
            
            # Check if within cluster tolerance
            if abs(current_level - prev_level) / prev_level <= tolerance:
                current_cluster.append(gap_levels[i])
            else:
                # Start new cluster
                if len(current_cluster) >= 2:
                    clusters.append(current_cluster)
                current_cluster = [gap_levels[i]]
        
        # Add final cluster if it has multiple gaps
        if len(current_cluster) >= 2:
            clusters.append(current_cluster)
        
        # Calculate confluence zone levels
        for cluster in clusters:
            cluster_levels = [level for level, gap in cluster]
            confluence_level = np.mean(cluster_levels)
            confluence_zones.append(float(confluence_level))
        
        return confluence_zones
    
    def _calculate_decay_factors(self, current_price: float) -> Tuple[float, float]:
        """
        Calculate average time decay and distance factors
        
        Args:
            current_price: Current market price
            
        Returns:
            Tuple[float, float]: (avg_time_decay, avg_distance_factor)
        """
        if not self.active_gaps:
            return 0.0, 0.0
        
        total_time_decay = 0.0
        total_distance_factor = 0.0
        active_count = 0
        
        for gap in self.active_gaps:
            if not gap.is_filled:
                total_time_decay += gap.time_decay_factor
                total_distance_factor += gap.distance_factor
                active_count += 1
        
        if active_count == 0:
            return 0.0, 0.0
        
        avg_time_decay = total_time_decay / active_count
        avg_distance_factor = total_distance_factor / active_count
        
        return float(avg_time_decay), float(avg_distance_factor)
    
    def _generate_fvg_signal(self, current_price: float, 
                           nearest_up: Optional[float], 
                           nearest_down: Optional[float]) -> str:
        """
        Generate FVG-based signal
        
        Args:
            current_price: Current market price
            nearest_up: Nearest resistance gap
            nearest_down: Nearest support gap
            
        Returns:
            str: Signal ('RESISTANCE', 'SUPPORT', 'NEUTRAL')
        """
        try:
            # Signal logic based on gap proximity and probability
            if nearest_up and nearest_down:
                distance_up = nearest_up - current_price
                distance_down = current_price - nearest_down
                
                # Closer to resistance gap
                if distance_up < distance_down:
                    return "RESISTANCE"
                # Closer to support gap
                else:
                    return "SUPPORT"
            
            elif nearest_up:
                return "RESISTANCE"
            
            elif nearest_down:
                return "SUPPORT"
            
            else:
                return "NEUTRAL"
                
        except Exception as e:
            self.logger.error(f"FVG signal generation failed: {e}")
            return "NEUTRAL"
    
    def _gap_to_dict(self, gap: FairValueGap) -> Dict[str, Any]:
        """Convert FairValueGap to dictionary for output"""
        return {
            'gap_id': gap.gap_id,
            'gap_type': gap.gap_type,
            'creation_time': gap.creation_time.isoformat(),
            'gap_top': float(gap.gap_top),
            'gap_bottom': float(gap.gap_bottom),
            'gap_midpoint': float(gap.gap_midpoint),
            'gap_size': float(gap.gap_size),
            'volume_confirmed': gap.volume_confirmed,
            'initial_probability': float(gap.initial_probability),
            'current_probability': float(gap.current_probability),
            'distance_factor': float(gap.distance_factor),
            'time_decay_factor': float(gap.time_decay_factor),
            'sessions_elapsed': gap.sessions_elapsed,
            'is_filled': gap.is_filled,
            'fill_price': float(gap.fill_price) if gap.fill_price else 0.0
        }