#!/usr/bin/env python3
"""
Enhanced Flow Physics Suite - Demo Runner Script

This script demonstrates how to use the enhanced flow physics analyzers
to analyze market flow patterns using physics-based models.

The suite provides:
- Velocity analysis (first derivative of flow)
- Acceleration analysis (second derivative of flow)  
- Jerk analysis (third derivative of flow)
- Integrated flow physics for institutional detection
"""

import sys
import os
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

# Add parent directories to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))))

# Import the enhanced flow physics components
try:
    from src.Liquidity_Reports.analyzers.enhanced_flow_physics import (
        FlowPhysicsIntegrator,
        AdvancedVelocityAnalyzer,
        AdvancedAccelerationAnalyzer,
        FlowJerkAnalyzer,
        get_flow_physics_config,
        FLOW_PHYSICS_CONSTANTS,
        FLOW_REGIMES
    )
    print(" Successfully imported enhanced flow physics components")
except ImportError as e:
    print(f" Error importing flow physics components: {e}")
    print("Make sure you're running from the correct directory")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def generate_sample_flow_data(hours: int = 24) -> pd.DataFrame:
    """
    Generate sample flow data for demonstration.
    
    This simulates institutional flow patterns with:
    - Base flow with noise
    - Institutional accumulation periods
    - Momentum shifts
    - Regime changes
    """
    # Time series
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=hours)
    timestamps = pd.date_range(start=start_time, end=end_time, freq='5min')
    
    # Generate base flow with patterns
    n_points = len(timestamps)
    t = np.linspace(0, hours, n_points)
    
    # Base flow pattern (sinusoidal with trend)
    base_flow = 1000000 * (1 + 0.5 * np.sin(2 * np.pi * t / 24))  # Daily cycle
    trend = 500000 * t / hours  # Upward trend
    
    # Add institutional patterns
    institutional_flow = np.zeros(n_points)
    
    # Accumulation period (hours 8-12)
    acc_mask = (t >= 8) & (t <= 12)
    institutional_flow[acc_mask] = 2000000 * np.exp((t[acc_mask] - 8) / 4)
    
    # Distribution period (hours 16-18)
    dist_mask = (t >= 16) & (t <= 18)
    institutional_flow[dist_mask] = -1500000 * np.exp((t[dist_mask] - 16) / 2)
    
    # Regime change (sudden spike at hour 14)
    regime_idx = np.argmin(np.abs(t - 14))
    institutional_flow[regime_idx:regime_idx+3] = 5000000
    
    # Combine flows
    total_flow = base_flow + trend + institutional_flow
    
    # Add realistic noise
    noise = np.random.normal(0, 100000, n_points)
    total_flow += noise
    
    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'flow_value': total_flow,
        'volume': np.abs(total_flow) / 100,  # Simulated volume
        'price': 100 + np.cumsum(total_flow / 1e8)  # Price impact
    })
    
    return df


def run_flow_physics_analysis(flow_data: pd.DataFrame) -> Dict[str, Any]:
    """
    Run the complete flow physics analysis suite.
    """
    print("\n" + "="*60)
    print("RUNNING ENHANCED FLOW PHYSICS ANALYSIS")
    print("="*60)
    
    # Get configuration
    config = get_flow_physics_config()
    print(f"\n Configuration loaded with {len(config['constants'])} physics constants")
    
    # Initialize analyzers
    try:
        velocity_analyzer = AdvancedVelocityAnalyzer(config)
        acceleration_analyzer = AdvancedAccelerationAnalyzer(config)
        jerk_analyzer = FlowJerkAnalyzer(config)
        integrator = FlowPhysicsIntegrator(config)
        
        print(" All analyzers initialized successfully")
    except Exception as e:
        print(f" Error initializing analyzers: {e}")
        return {}
    
    # Prepare data for analysis
    analysis_data = {
        'flow_series': flow_data['flow_value'].tolist(),
        'time_series': flow_data['timestamp'].astype(int) // 10**9,  # Convert to seconds
        'volume_series': flow_data['volume'].tolist(),
        'price_series': flow_data['price'].tolist()
    }
    
    results = {}
    
    # 1. Velocity Analysis
    print("\n Running Velocity Analysis...")
    try:
        velocity_results = velocity_analyzer.analyze(analysis_data)
        results['velocity'] = velocity_results
        
        if velocity_results:
            print(f"   Average Velocity: {velocity_results.get('average_velocity', 0):.2f}")
            print(f"   Peak Velocity: {velocity_results.get('peak_velocity', 0):.2f}")
            print(f"   Institutional Velocity Detected: {velocity_results.get('institutional_detected', False)}")
    except Exception as e:
        print(f"    Velocity analysis failed: {e}")
    
    # 2. Acceleration Analysis
    print("\n Running Acceleration Analysis...")
    try:
        acceleration_results = acceleration_analyzer.analyze(analysis_data)
        results['acceleration'] = acceleration_results
        
        if acceleration_results:
            print(f"   Average Acceleration: {acceleration_results.get('average_acceleration', 0):.2f}")
            print(f"   Peak Acceleration: {acceleration_results.get('peak_acceleration', 0):.2f}")
            print(f"   Momentum Shifts Detected: {acceleration_results.get('momentum_shifts', 0)}")
    except Exception as e:
        print(f"    Acceleration analysis failed: {e}")
    
    # 3. Jerk Analysis
    print("\n Running Jerk Analysis...")
    try:
        jerk_results = jerk_analyzer.analyze(analysis_data)
        results['jerk'] = jerk_results
        
        if jerk_results:
            print(f"   Average Jerk: {jerk_results.get('average_jerk', 0):.2f}")
            print(f"   Peak Jerk: {jerk_results.get('peak_jerk', 0):.2f}")
            print(f"   Regime Changes Detected: {jerk_results.get('regime_changes', 0)}")
    except Exception as e:
        print(f"    Jerk analysis failed: {e}")
    
    # 4. Integrated Flow Physics
    print("\n Running Integrated Flow Physics Analysis...")
    try:
        integrated_results = integrator.analyze(analysis_data)
        results['integrated'] = integrated_results
        
        if integrated_results:
            print(f"   Overall Flow State: {integrated_results.get('flow_state', 'Unknown')}")
            print(f"   Institutional Activity Score: {integrated_results.get('institutional_score', 0):.2f}")
            print(f"   Detected Patterns: {integrated_results.get('patterns', [])}")
    except Exception as e:
        print(f"    Integrated analysis failed: {e}")
    
    return results


def display_flow_regimes(results: Dict[str, Any]):
    """Display detected flow regimes."""
    print("\n" + "="*60)
    print("DETECTED FLOW REGIMES")
    print("="*60)
    
    # Check each regime type
    for regime_name, regime_def in FLOW_REGIMES.items():
        print(f"\n{regime_name}:")
        print(f"  Description: {regime_def['description']}")
        
        # Check if this regime was detected
        detected = False
        
        if regime_name == 'ACCUMULATION':
            if results.get('velocity', {}).get('average_velocity', 0) > 0 and \
               results.get('acceleration', {}).get('average_acceleration', 0) > 0:
                detected = True
                
        elif regime_name == 'DISTRIBUTION':
            if results.get('velocity', {}).get('average_velocity', 0) < 0 and \
               results.get('acceleration', {}).get('average_acceleration', 0) < 0:
                detected = True
                
        elif regime_name == 'MOMENTUM_SHIFT':
            if results.get('acceleration', {}).get('momentum_shifts', 0) > 0:
                detected = True
                
        elif regime_name == 'REGIME_CHANGE':
            if results.get('jerk', {}).get('regime_changes', 0) > 0:
                detected = True
        
        print(f"  Detected: {' YES' if detected else ' NO'}")


def generate_report(results: Dict[str, Any], output_file: str = "flow_physics_report.txt"):
    """Generate a text report of the analysis."""
    print(f"\n Generating report to {output_file}...")
    
    with open(output_file, 'w') as f:
        f.write("ENHANCED FLOW PHYSICS ANALYSIS REPORT\n")
        f.write("=" * 60 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Physics Constants
        f.write("PHYSICS CONSTANTS USED:\n")
        f.write("-" * 30 + "\n")
        for key, value in FLOW_PHYSICS_CONSTANTS.items():
            f.write(f"{key}: {value}\n")
        f.write("\n")
        
        # Analysis Results
        f.write("ANALYSIS RESULTS:\n")
        f.write("-" * 30 + "\n")
        
        # Velocity
        if 'velocity' in results:
            f.write("\nVelocity Analysis:\n")
            for key, value in results['velocity'].items():
                f.write(f"  {key}: {value}\n")
        
        # Acceleration
        if 'acceleration' in results:
            f.write("\nAcceleration Analysis:\n")
            for key, value in results['acceleration'].items():
                f.write(f"  {key}: {value}\n")
        
        # Jerk
        if 'jerk' in results:
            f.write("\nJerk Analysis:\n")
            for key, value in results['jerk'].items():
                f.write(f"  {key}: {value}\n")
        
        # Integrated
        if 'integrated' in results:
            f.write("\nIntegrated Analysis:\n")
            for key, value in results['integrated'].items():
                f.write(f"  {key}: {value}\n")
    
    print(f" Report saved to {output_file}")


def main():
    """Main demo function."""
    print(" ENHANCED FLOW PHYSICS SUITE DEMO")
    print("=" * 80)
    
    # Generate sample data
    print("\n Generating sample flow data...")
    flow_data = generate_sample_flow_data(hours=24)
    print(f" Generated {len(flow_data)} data points over 24 hours")
    
    # Display sample statistics
    print(f"\nSample Data Statistics:")
    print(f"  Average Flow: ${flow_data['flow_value'].mean():,.0f}")
    print(f"  Max Flow: ${flow_data['flow_value'].max():,.0f}")
    print(f"  Min Flow: ${flow_data['flow_value'].min():,.0f}")
    print(f"  Std Dev: ${flow_data['flow_value'].std():,.0f}")
    
    # Run analysis
    results = run_flow_physics_analysis(flow_data)
    
    # Display regimes
    if results:
        display_flow_regimes(results)
        
        # Generate report
        generate_report(results)
        
        print("\n DEMO COMPLETE!")
        print("\nThe enhanced flow physics suite can detect:")
        print("   Institutional accumulation/distribution")
        print("   Momentum shifts in flow patterns")
        print("   Regime changes (sudden flow dynamics changes)")
        print("   Hidden flow patterns through derivative analysis")
        
        print("\nTo use with real data:")
        print("  1. Replace generate_sample_flow_data() with your data source")
        print("  2. Ensure data has 'timestamp' and 'flow_value' columns")
        print("  3. Optionally include 'volume' and 'price' for enhanced analysis")
    else:
        print("\n Analysis failed - check error messages above")


if __name__ == "__main__":
    main()