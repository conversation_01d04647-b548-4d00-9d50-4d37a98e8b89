"""
Model Quantization Utility

This module provides utilities for model quantization to improve inference speed
and reduce memory footprint.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import logging
import time
from typing import Dict, List, Optional, Union, Any, Tuple

import torch
import torch.nn as nn
import numpy as np

from ml_logging import get_logger

logger = get_logger(__name__)

class ModelQuantizer:
    """
    Model Quantizer Utility
    
    This class provides methods for quantizing PyTorch models to improve
    inference speed and reduce memory footprint.
    """
    
    def __init__(self):
        """Initialize the model quantizer."""
        # Check if quantization is supported
        self.quantization_supported = hasattr(torch, 'quantization')
        if not self.quantization_supported:
            logger.warning("PyTorch quantization not supported in this version")
    
    def is_supported(self) -> bool:
        """
        Check if quantization is supported.
        
        Returns:
            bool: True if quantization is supported
        """
        return self.quantization_supported
    
    def quantize_dynamic(
        self, 
        model: torch.nn.Module, 
        dtype: Optional[torch.dtype] = torch.qint8
    ) -> torch.nn.Module:
        """
        Apply dynamic quantization to a model.
        
        Dynamic quantization converts weights to int8 while keeping activations
        in floating point. This is the easiest form of quantization to apply.
        
        Args:
            model: The PyTorch model to quantize
            dtype: Quantization data type (default: torch.qint8)
            
        Returns:
            Quantized model
        """
        if not self.quantization_supported:
            logger.warning("Quantization not supported, returning original model")
            return model
        
        try:
            # Define quantizable operation types
            qconfig_mapping = torch.quantization.get_default_qconfig_mapping()
            
            # Define quantization API version - use latest by default
            if hasattr(torch.quantization, 'quantize_fx'):
                return self._quantize_dynamic_fx(model, dtype)
            else:
                return self._quantize_dynamic_eager(model, dtype)
            
        except Exception as e:
            logger.error(f"Error in dynamic quantization: {str(e)}")
            return model
    
    def _quantize_dynamic_eager(
        self, 
        model: torch.nn.Module, 
        dtype: torch.dtype = torch.qint8
    ) -> torch.nn.Module:
        """
        Apply dynamic quantization using eager mode.
        
        Args:
            model: The PyTorch model to quantize
            dtype: Quantization data type
            
        Returns:
            Quantized model
        """
        try:
            # Set model to evaluation mode
            model.eval()
            
            # Specify quantizable operations
            quantizable_ops = {
                torch.nn.Linear,
                torch.nn.LSTM,
                torch.nn.GRU,
                torch.nn.LSTMCell,
                torch.nn.RNNCell,
                torch.nn.GRUCell
            }
            
            # Quantize model
            quantized_model = torch.quantization.quantize_dynamic(
                model, 
                quantizable_ops,
                dtype=dtype
            )
            
            logger.info("Model quantized using dynamic quantization (eager mode)")
            return quantized_model
            
        except Exception as e:
            logger.error(f"Error in eager mode quantization: {str(e)}")
            return model
    
    def _quantize_dynamic_fx(
        self, 
        model: torch.nn.Module, 
        dtype: torch.dtype = torch.qint8
    ) -> torch.nn.Module:
        """
        Apply dynamic quantization using FX graph mode.
        
        Args:
            model: The PyTorch model to quantize
            dtype: Quantization data type
            
        Returns:
            Quantized model
        """
        try:
            # Set model to evaluation mode
            model.eval()
            
            # Prepare example inputs (needed for FX graph capture)
            example_inputs = self._get_example_inputs(model)
            if example_inputs is None:
                logger.warning("Could not generate example inputs for FX quantization, falling back to eager mode")
                return self._quantize_dynamic_eager(model, dtype)
            
            # Create quantization configuration
            qconfig_mapping = torch.quantization.get_default_qconfig_mapping()
            
            # Prepare model for quantization
            prepared_model = torch.quantization.quantize_fx.prepare_fx(
                model, 
                qconfig_mapping, 
                example_inputs
            )
            
            # Quantize model
            quantized_model = torch.quantization.quantize_fx.convert_fx(prepared_model)
            
            logger.info("Model quantized using dynamic quantization (FX graph mode)")
            return quantized_model
            
        except Exception as e:
            logger.error(f"Error in FX graph mode quantization: {str(e)}")
            # Fall back to eager mode
            return self._quantize_dynamic_eager(model, dtype)
    
    def quantize_static(
        self, 
        model: torch.nn.Module, 
        calibration_data: Any,
        dtype: torch.dtype = torch.qint8
    ) -> torch.nn.Module:
        """
        Apply static quantization to a model.
        
        Static quantization requires calibration with representative data and
        adds observer/quant/dequant ops to the model.
        
        Args:
            model: The PyTorch model to quantize
            calibration_data: Data for calibration
            dtype: Quantization data type
            
        Returns:
            Quantized model
        """
        if not self.quantization_supported:
            logger.warning("Quantization not supported, returning original model")
            return model
        
        try:
            # Set model to evaluation mode
            model.eval()
            
            # Create a copy of the model for quantization
            model_to_quantize = type(model)()
            model_to_quantize.load_state_dict(model.state_dict())
            
            # Define quantization configuration
            qconfig_mapping = torch.quantization.get_default_qconfig_mapping()
            
            # Fuse operations like conv+bn+relu for better quantization
            model_to_quantize = self._fuse_modules(model_to_quantize)
            
            # Prepare model for static quantization
            model_prepared = torch.quantization.prepare(model_to_quantize, inplace=True)
            
            # Calibrate the model with the provided data
            self._calibrate_model(model_prepared, calibration_data)
            
            # Convert the model to quantized version
            quantized_model = torch.quantization.convert(model_prepared, inplace=True)
            
            logger.info("Model quantized using static quantization")
            return quantized_model
            
        except Exception as e:
            logger.error(f"Error in static quantization: {str(e)}")
            return model
    
    def _fuse_modules(self, model: torch.nn.Module) -> torch.nn.Module:
        """
        Fuse modules for better quantization performance.
        
        Args:
            model: The PyTorch model to fuse
            
        Returns:
            Fused model
        """
        try:
            # List of common patterns to fuse
            patterns = []
            
            # Find Conv+BN+ReLU patterns
            for name, module in model.named_modules():
                # Check for Conv2d followed by BatchNorm2d followed by ReLU
                if isinstance(module, torch.nn.Conv2d) and \
                   name + '.bn' in dict(model.named_modules()) and \
                   name + '.relu' in dict(model.named_modules()):
                    patterns.append([name, name + '.bn', name + '.relu'])
                
                # Check for Linear followed by ReLU
                if isinstance(module, torch.nn.Linear) and \
                   name + '.relu' in dict(model.named_modules()):
                    patterns.append([name, name + '.relu'])
            
            # Fuse the identified patterns
            if patterns:
                torch.quantization.fuse_modules(model, patterns, inplace=True)
                logger.info(f"Fused {len(patterns)} module patterns")
            
            return model
            
        except Exception as e:
            logger.warning(f"Error fusing modules: {str(e)}")
            return model
    
    def _calibrate_model(self, model: torch.nn.Module, calibration_data: Any) -> None:
        """
        Calibrate the model for static quantization.
        
        Args:
            model: The PyTorch model to calibrate
            calibration_data: Data for calibration
        """
        try:
            # Set model to evaluation mode
            model.eval()
            
            # Run calibration data through the model
            with torch.no_grad():
                for data_batch in calibration_data:
                    if isinstance(data_batch, tuple) or isinstance(data_batch, list):
                        # Assume first element is input
                        inputs = data_batch[0]
                    else:
                        inputs = data_batch
                    
                    # Forward pass
                    _ = model(inputs)
            
            logger.info("Model calibration completed")
            
        except Exception as e:
            logger.error(f"Error calibrating model: {str(e)}")
    
    def _get_example_inputs(self, model: torch.nn.Module) -> Optional[torch.Tensor]:
        """
        Generate example inputs for the model.
        
        Args:
            model: The PyTorch model
            
        Returns:
            Example inputs tensor or None if not possible
        """
        try:
            # Try to infer input shape from model
            input_shape = None
            
            # Check if model has input_shape attribute
            if hasattr(model, 'input_shape'):
                input_shape = model.input_shape
            # Check if first layer has in_features (Linear) or in_channels (Conv)
            elif isinstance(model, torch.nn.Sequential) and len(list(model.children())) > 0:
                first_layer = list(model.children())[0]
                if hasattr(first_layer, 'in_features'):
                    input_shape = (1, first_layer.in_features)
                elif hasattr(first_layer, 'in_channels'):
                    # For Conv layers, need more information to determine shape
                    # Using a default shape as fallback
                    input_shape = (1, first_layer.in_channels, 32, 32)
            
            if input_shape:
                return torch.randn(*input_shape)
            
            # Fallback to default shape if can't determine
            logger.warning("Could not determine input shape, using default (1, 256)")
            return torch.randn(1, 256)
            
        except Exception as e:
            logger.warning(f"Error generating example inputs: {str(e)}")
            return None
    
    def measure_performance(
        self, 
        model: torch.nn.Module, 
        sample_input: torch.Tensor, 
        num_runs: int = 100
    ) -> Dict[str, float]:
        """
        Measure performance metrics of a model.
        
        Args:
            model: The PyTorch model to measure
            sample_input: Sample input tensor
            num_runs: Number of inference runs for measurement
            
        Returns:
            Dict with performance metrics
        """
        try:
            # Set model to evaluation mode
            model.eval()
            
            # Warm-up runs
            with torch.no_grad():
                for _ in range(10):
                    _ = model(sample_input)
            
            # Measure inference time
            start_time = time.time()
            with torch.no_grad():
                for _ in range(num_runs):
                    _ = model(sample_input)
            end_time = time.time()
            
            # Calculate metrics
            total_time = end_time - start_time
            avg_time = total_time / num_runs
            
            # Measure memory
            if hasattr(torch, 'cuda') and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
                with torch.no_grad():
                    _ = model(sample_input)
                memory_bytes = torch.cuda.max_memory_allocated()
                memory_mb = memory_bytes / (1024 * 1024)
            else:
                # Estimate model size for CPU
                memory_mb = sum(p.numel() * p.element_size() for p in model.parameters()) / (1024 * 1024)
            
            # Calculate throughput
            throughput = num_runs / total_time
            
            return {
                "inference_time_ms": avg_time * 1000,
                "memory_mb": memory_mb,
                "throughput": throughput
            }
            
        except Exception as e:
            logger.error(f"Error measuring performance: {str(e)}")
            return {
                "inference_time_ms": 0.0,
                "memory_mb": 0.0,
                "throughput": 0.0,
                "error": str(e)
            }
    
    def compare_models(
        self, 
        original_model: torch.nn.Module, 
        quantized_model: torch.nn.Module, 
        sample_input: torch.Tensor,
        num_runs: int = 100
    ) -> Dict[str, Any]:
        """
        Compare performance between original and quantized models.
        
        Args:
            original_model: The original PyTorch model
            quantized_model: The quantized model
            sample_input: Sample input tensor
            num_runs: Number of inference runs for measurement
            
        Returns:
            Dict with comparison metrics
        """
        try:
            # Set models to evaluation mode
            original_model.eval()
            quantized_model.eval()
            
            # Measure performance of original model
            original_metrics = self.measure_performance(
                original_model,
                sample_input,
                num_runs
            )
            
            # Measure performance of quantized model
            quantized_metrics = self.measure_performance(
                quantized_model,
                sample_input,
                num_runs
            )
            
            # Calculate improvement percentages
            time_improvement = (
                (original_metrics["inference_time_ms"] - quantized_metrics["inference_time_ms"]) / 
                original_metrics["inference_time_ms"]
            ) * 100
            
            memory_improvement = (
                (original_metrics["memory_mb"] - quantized_metrics["memory_mb"]) / 
                original_metrics["memory_mb"]
            ) * 100
            
            throughput_improvement = (
                (quantized_metrics["throughput"] - original_metrics["throughput"]) / 
                original_metrics["throughput"]
            ) * 100
            
            # Verify output correctness
            with torch.no_grad():
                original_output = original_model(sample_input)
                quantized_output = quantized_model(sample_input)
            
            # Calculate error metrics if outputs are comparable
            error_metrics = {}
            try:
                # Convert to numpy for easier comparison
                original_np = original_output.cpu().numpy()
                quantized_np = quantized_output.cpu().numpy()
                
                # Calculate Mean Absolute Error
                mae = np.mean(np.abs(original_np - quantized_np))
                # Calculate Mean Squared Error
                mse = np.mean(np.square(original_np - quantized_np))
                # Calculate Relative Error
                rel_error = np.mean(np.abs((original_np - quantized_np) / (original_np + 1e-10)))
                
                error_metrics = {
                    "mae": float(mae),
                    "mse": float(mse),
                    "relative_error": float(rel_error)
                }
            except Exception as e:
                logger.warning(f"Could not calculate error metrics: {str(e)}")
                error_metrics = {"error": str(e)}
            
            # Prepare comparison results
            comparison = {
                "original": original_metrics,
                "quantized": quantized_metrics,
                "improvements": {
                    "time_percent": float(time_improvement),
                    "memory_percent": float(memory_improvement),
                    "throughput_percent": float(throughput_improvement)
                },
                "error_metrics": error_metrics
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing models: {str(e)}")
            return {
                "error": str(e)
            }
