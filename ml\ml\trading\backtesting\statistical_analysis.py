"""
Statistical analysis framework for backtest results.

This module provides functions to analyze backtest results, calculate performance
metrics, and generate statistical reports.
"""

import logging
import os
import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from scipy import stats
import matplotlib.pyplot as plt

# Configure logging
logger = logging.getLogger(__name__)


class BacktestAnalyzer:
    """
    Backtest analyzer for evaluating trading strategies.
    
    This class analyzes backtest results and calculates various performance metrics
    and statistical measures.
    """
    
    def __init__(
        self,
        results: Dict[str, Any],
        benchmark_data: Optional[pd.DataFrame] = None,
        risk_free_rate: float = 0.02
    ):
        """
        Initialize the backtest analyzer.
        
        Args:
            results: Backtest results
            benchmark_data: Benchmark data for comparison
            risk_free_rate: Annual risk-free rate
        """
        self.results = results
        self.benchmark_data = benchmark_data
        self.risk_free_rate = risk_free_rate
        
        # Extract data
        self.equity_curve = pd.DataFrame(results.get('equity_curve', []))
        self.trade_history = pd.DataFrame(results.get('trade_history', []))
        self.performance_metrics = results.get('performance_metrics', {})
        
        # Prepare data
        self._prepare_data()
        
        # Advanced metrics
        self.advanced_metrics = {}
        
        # Calculated analytics
        self.return_metrics = {}
        self.risk_metrics = {}
        self.trade_metrics = {}
        self.time_metrics = {}
    
    def _prepare_data(self) -> None:
        """Prepare data for analysis."""
        # Prepare equity curve
        if not self.equity_curve.empty:
            # Convert timestamp to datetime
            if 'timestamp' in self.equity_curve.columns:
                self.equity_curve['timestamp'] = pd.to_datetime(self.equity_curve['timestamp'])
                self.equity_curve.set_index('timestamp', inplace=True)
            
            # Calculate returns
            self.equity_curve['return'] = self.equity_curve['equity'].pct_change().fillna(0)
            self.equity_curve['log_return'] = np.log(self.equity_curve['equity'] / self.equity_curve['equity'].shift(1)).fillna(0)
            self.equity_curve['cumulative_return'] = (1 + self.equity_curve['return']).cumprod() - 1
        
        # Prepare trade history
        if not self.trade_history.empty:
            # Convert timestamps to datetime
            if 'open_time' in self.trade_history.columns:
                self.trade_history['open_time'] = pd.to_datetime(self.trade_history['open_time'])
            
            if 'close_time' in self.trade_history.columns:
                self.trade_history['close_time'] = pd.to_datetime(self.trade_history['close_time'])
        
        # Prepare benchmark data
        if self.benchmark_data is not None:
            # Ensure benchmark data has the same frequency as equity curve
            if not self.equity_curve.empty and isinstance(self.benchmark_data.index, pd.DatetimeIndex):
                # Align data
                common_index = self.equity_curve.index.intersection(self.benchmark_data.index)
                if not common_index.empty:
                    self.benchmark_data = self.benchmark_data.loc[common_index]
                    self.equity_curve = self.equity_curve.loc[common_index]
                
                # Calculate benchmark returns
                if 'close' in self.benchmark_data.columns:
                    self.benchmark_data['return'] = self.benchmark_data['close'].pct_change().fillna(0)
                    self.benchmark_data['log_return'] = np.log(self.benchmark_data['close'] / self.benchmark_data['close'].shift(1)).fillna(0)
                    self.benchmark_data['cumulative_return'] = (1 + self.benchmark_data['return']).cumprod() - 1
    
    def calculate_return_metrics(self) -> Dict[str, float]:
        """
        Calculate return metrics.
        
        Returns:
            Dictionary of return metrics
        """
        metrics = {}
        
        if self.equity_curve.empty:
            return metrics
        
        # Extract returns
        returns = self.equity_curve['return'].values
        
        # Calculate return metrics
        metrics['total_return'] = self.performance_metrics.get('total_return', 0.0)
        metrics['annualized_return'] = self.performance_metrics.get('annualized_return', 0.0)
        metrics['cagr'] = metrics['annualized_return']  # Compound Annual Growth Rate
        
        # Calculate average returns
        metrics['mean_return_daily'] = np.mean(returns)
        metrics['median_return_daily'] = np.median(returns)
        
        # Calculate geometric mean return
        if len(returns) > 0:
            metrics['geometric_return_daily'] = np.prod(1 + returns) ** (1 / len(returns)) - 1
        else:
            metrics['geometric_return_daily'] = 0.0
        
        # Annualize metrics (assuming 252 trading days)
        metrics['mean_return_annual'] = metrics['mean_return_daily'] * 252
        metrics['median_return_annual'] = metrics['median_return_daily'] * 252
        metrics['geometric_return_annual'] = (1 + metrics['geometric_return_daily']) ** 252 - 1
        
        # Store for later use
        self.return_metrics = metrics
        
        return metrics
    
    def calculate_risk_metrics(self) -> Dict[str, float]:
        """
        Calculate risk metrics.
        
        Returns:
            Dictionary of risk metrics
        """
        metrics = {}
        
        if self.equity_curve.empty:
            return metrics
        
        # Extract returns
        returns = self.equity_curve['return'].values
        
        # Calculate risk metrics
        metrics['volatility_daily'] = np.std(returns)
        metrics['volatility_annual'] = metrics['volatility_daily'] * np.sqrt(252)
        
        # Calculate downside risk
        negative_returns = returns[returns < 0]
        metrics['downside_risk_daily'] = np.std(negative_returns) if len(negative_returns) > 0 else 0.0
        metrics['downside_risk_annual'] = metrics['downside_risk_daily'] * np.sqrt(252)
        
        # Calculate maximum drawdown
        metrics['max_drawdown'] = self.performance_metrics.get('max_drawdown', 0.0)
        
        # Calculate drawdown statistics
        drawdowns = self._calculate_drawdowns()
        metrics['avg_drawdown'] = np.mean(drawdowns) if drawdowns else 0.0
        metrics['median_drawdown'] = np.median(drawdowns) if drawdowns else 0.0
        
        # Calculate Sharpe ratio
        if metrics['volatility_annual'] > 0:
            metrics['sharpe_ratio'] = (metrics['mean_return_annual'] - self.risk_free_rate) / metrics['volatility_annual']
        else:
            metrics['sharpe_ratio'] = 0.0
        
        # Calculate Sortino ratio
        if metrics['downside_risk_annual'] > 0:
            metrics['sortino_ratio'] = (metrics['mean_return_annual'] - self.risk_free_rate) / metrics['downside_risk_annual']
        else:
            metrics['sortino_ratio'] = 0.0
        
        # Calculate Calmar ratio (Return / Max Drawdown)
        if metrics['max_drawdown'] > 0:
            metrics['calmar_ratio'] = metrics['annualized_return'] / metrics['max_drawdown']
        else:
            metrics['calmar_ratio'] = float('inf')
        
        # Calculate Value at Risk (VaR)
        metrics['var_95'] = np.percentile(returns, 5)  # 95% VaR
        metrics['var_99'] = np.percentile(returns, 1)  # 99% VaR
        
        # Calculate Expected Shortfall (CVaR)
        metrics['cvar_95'] = np.mean(returns[returns <= metrics['var_95']])
        metrics['cvar_99'] = np.mean(returns[returns <= metrics['var_99']])
        
        # Calculate skewness and kurtosis
        metrics['skewness'] = stats.skew(returns)
        metrics['kurtosis'] = stats.kurtosis(returns)
        
        # Store for later use
        self.risk_metrics = metrics
        
        return metrics
    
    def calculate_trade_metrics(self) -> Dict[str, float]:
        """
        Calculate trade metrics.
        
        Returns:
            Dictionary of trade metrics
        """
        metrics = {}
        
        if self.trade_history.empty:
            return metrics
        
        # Extract profit/loss
        pnl = self.trade_history['profit_loss'].values
        pnl_pct = self.trade_history['profit_loss_pct'].values
        
        # Calculate basic trade metrics
        metrics['num_trades'] = len(self.trade_history)
        metrics['num_winning'] = len(self.trade_history[self.trade_history['profit_loss'] > 0])
        metrics['num_losing'] = len(self.trade_history[self.trade_history['profit_loss'] <= 0])
        
        metrics['win_rate'] = metrics['num_winning'] / metrics['num_trades'] if metrics['num_trades'] > 0 else 0.0
        
        # Calculate profit metrics
        metrics['total_profit'] = np.sum(pnl[pnl > 0])
        metrics['total_loss'] = np.sum(pnl[pnl <= 0])
        metrics['net_profit'] = metrics['total_profit'] + metrics['total_loss']
        
        # Calculate average metrics
        metrics['avg_profit'] = np.mean(pnl[pnl > 0]) if len(pnl[pnl > 0]) > 0 else 0.0
        metrics['avg_loss'] = np.mean(pnl[pnl <= 0]) if len(pnl[pnl <= 0]) > 0 else 0.0
        metrics['avg_trade'] = np.mean(pnl)
        
        metrics['avg_profit_pct'] = np.mean(pnl_pct[pnl_pct > 0]) if len(pnl_pct[pnl_pct > 0]) > 0 else 0.0
        metrics['avg_loss_pct'] = np.mean(pnl_pct[pnl_pct <= 0]) if len(pnl_pct[pnl_pct <= 0]) > 0 else 0.0
        metrics['avg_trade_pct'] = np.mean(pnl_pct)
        
        # Calculate profit factor
        metrics['profit_factor'] = abs(metrics['total_profit'] / metrics['total_loss']) if metrics['total_loss'] < 0 else float('inf')
        
        # Calculate payoff ratio
        metrics['payoff_ratio'] = abs(metrics['avg_profit'] / metrics['avg_loss']) if metrics['avg_loss'] < 0 else float('inf')
        
        # Calculate expectancy
        metrics['expectancy'] = metrics['win_rate'] * metrics['avg_profit'] + (1 - metrics['win_rate']) * metrics['avg_loss']
        metrics['expectancy_pct'] = metrics['win_rate'] * metrics['avg_profit_pct'] + (1 - metrics['win_rate']) * metrics['avg_loss_pct']
        
        # Calculate risk-adjusted metrics
        if metrics['avg_loss'] < 0:
            metrics['profit_to_risk'] = abs(metrics['net_profit'] / metrics['total_loss'])
        else:
            metrics['profit_to_risk'] = float('inf')
        
        # Store for later use
        self.trade_metrics = metrics
        
        return metrics
    
    def calculate_time_metrics(self) -> Dict[str, float]:
        """
        Calculate time-based metrics.
        
        Returns:
            Dictionary of time metrics
        """
        metrics = {}
        
        if self.equity_curve.empty:
            return metrics
        
        # Calculate time metrics
        if isinstance(self.equity_curve.index, pd.DatetimeIndex):
            # Get start and end timestamps
            start_time = self.equity_curve.index[0]
            end_time = self.equity_curve.index[-1]
            
            # Calculate duration
            duration_seconds = (end_time - start_time).total_seconds()
            metrics['duration_days'] = duration_seconds / (60 * 60 * 24)
            
            # Calculate trading frequency
            if self.trade_history.empty:
                metrics['trades_per_day'] = 0.0
                metrics['trades_per_month'] = 0.0
                metrics['trades_per_year'] = 0.0
            else:
                metrics['trades_per_day'] = len(self.trade_history) / metrics['duration_days'] if metrics['duration_days'] > 0 else 0.0
                metrics['trades_per_month'] = metrics['trades_per_day'] * 30
                metrics['trades_per_year'] = metrics['trades_per_day'] * 365
            
            # Calculate average trade duration
            if 'duration' in self.trade_history.columns:
                metrics['avg_trade_duration_seconds'] = np.mean(self.trade_history['duration'].values)
                metrics['avg_trade_duration_minutes'] = metrics['avg_trade_duration_seconds'] / 60
                metrics['avg_trade_duration_hours'] = metrics['avg_trade_duration_minutes'] / 60
                metrics['avg_trade_duration_days'] = metrics['avg_trade_duration_hours'] / 24
            
            # Calculate time in market
            if not self.trade_history.empty and 'open_time' in self.trade_history.columns and 'close_time' in self.trade_history.columns:
                # Calculate trade durations
                trade_durations = []
                
                for _, trade in self.trade_history.iterrows():
                    if pd.notna(trade['open_time']) and pd.notna(trade['close_time']):
                        duration = (trade['close_time'] - trade['open_time']).total_seconds()
                        trade_durations.append(duration)
                
                # Calculate total time in market
                total_time_in_market = sum(trade_durations)
                
                # Calculate time in market percentage
                metrics['time_in_market_seconds'] = total_time_in_market
                metrics['time_in_market_pct'] = total_time_in_market / duration_seconds if duration_seconds > 0 else 0.0
        
        # Store for later use
        self.time_metrics = metrics
        
        return metrics
    
    def calculate_benchmark_metrics(self) -> Dict[str, float]:
        """
        Calculate benchmark comparison metrics.
        
        Returns:
            Dictionary of benchmark metrics
        """
        metrics = {}
        
        if self.equity_curve.empty or self.benchmark_data is None:
            return metrics
        
        # Check if benchmark returns are available
        if 'return' not in self.benchmark_data.columns:
            return metrics
        
        # Extract returns
        strategy_returns = self.equity_curve['return'].values
        benchmark_returns = self.benchmark_data['return'].values
        
        # Calculate beta
        if len(strategy_returns) == len(benchmark_returns):
            # Calculate covariance and variance
            covariance = np.cov(strategy_returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            
            # Calculate beta
            if benchmark_variance > 0:
                metrics['beta'] = covariance / benchmark_variance
            else:
                metrics['beta'] = 0.0
            
            # Calculate alpha (Jensen's Alpha)
            benchmark_mean_return = np.mean(benchmark_returns)
            strategy_mean_return = np.mean(strategy_returns)
            
            if metrics['beta'] != 0:
                metrics['alpha'] = strategy_mean_return - (self.risk_free_rate / 252 + metrics['beta'] * (benchmark_mean_return - self.risk_free_rate / 252))
            else:
                metrics['alpha'] = strategy_mean_return - benchmark_mean_return
            
            # Calculate information ratio
            tracking_error = np.std(strategy_returns - benchmark_returns)
            if tracking_error > 0:
                metrics['information_ratio'] = (strategy_mean_return - benchmark_mean_return) / tracking_error
            else:
                metrics['information_ratio'] = 0.0
            
            # Calculate R-squared
            correlation = np.corrcoef(strategy_returns, benchmark_returns)[0, 1]
            metrics['r_squared'] = correlation ** 2
            
            # Calculate tracking error
            metrics['tracking_error'] = tracking_error
            metrics['tracking_error_annual'] = tracking_error * np.sqrt(252)
            
            # Calculate up/down capture
            up_market = benchmark_returns > 0
            down_market = benchmark_returns < 0
            
            if np.sum(up_market) > 0:
                up_capture = np.mean(strategy_returns[up_market]) / np.mean(benchmark_returns[up_market])
                metrics['up_capture'] = up_capture
            else:
                metrics['up_capture'] = 1.0
            
            if np.sum(down_market) > 0:
                down_capture = np.mean(strategy_returns[down_market]) / np.mean(benchmark_returns[down_market])
                metrics['down_capture'] = down_capture
            else:
                metrics['down_capture'] = 1.0
            
            # Calculate outperformance
            metrics['outperformance'] = np.mean(strategy_returns - benchmark_returns) * 252
        
        # Calculate benchmark metrics
        benchmark_returns_annual = np.mean(benchmark_returns) * 252
        benchmark_volatility_annual = np.std(benchmark_returns) * np.sqrt(252)
        
        # Calculate benchmark Sharpe ratio
        if benchmark_volatility_annual > 0:
            metrics['benchmark_sharpe_ratio'] = (benchmark_returns_annual - self.risk_free_rate) / benchmark_volatility_annual
        else:
            metrics['benchmark_sharpe_ratio'] = 0.0
        
        # Calculate relative metrics
        metrics['relative_sharpe'] = self.risk_metrics.get('sharpe_ratio', 0.0) / metrics['benchmark_sharpe_ratio'] if metrics['benchmark_sharpe_ratio'] != 0 else float('inf')
        
        return metrics
    
    def calculate_advanced_metrics(self) -> Dict[str, float]:
        """
        Calculate advanced metrics.
        
        Returns:
            Dictionary of advanced metrics
        """
        metrics = {}
        
        if self.equity_curve.empty:
            return metrics
        
        # Extract returns
        returns = self.equity_curve['return'].values
        
        # Calculate advanced metrics
        
        # Calculate maximum consecutive wins and losses
        if not self.trade_history.empty:
            # Get trade results
            trade_results = (self.trade_history['profit_loss'] > 0).astype(int).values
            
            # Calculate consecutive wins
            win_streaks = []
            current_streak = 0
            
            for result in trade_results:
                if result == 1:
                    current_streak += 1
                else:
                    if current_streak > 0:
                        win_streaks.append(current_streak)
                    current_streak = 0
            
            if current_streak > 0:
                win_streaks.append(current_streak)
            
            metrics['max_consecutive_wins'] = max(win_streaks) if win_streaks else 0
            metrics['avg_consecutive_wins'] = np.mean(win_streaks) if win_streaks else 0
            
            # Calculate consecutive losses
            loss_streaks = []
            current_streak = 0
            
            for result in trade_results:
                if result == 0:
                    current_streak += 1
                else:
                    if current_streak > 0:
                        loss_streaks.append(current_streak)
                    current_streak = 0
            
            if current_streak > 0:
                loss_streaks.append(current_streak)
            
            metrics['max_consecutive_losses'] = max(loss_streaks) if loss_streaks else 0
            metrics['avg_consecutive_losses'] = np.mean(loss_streaks) if loss_streaks else 0
        
        # Calculate drawdown metrics
        drawdowns = self._calculate_drawdowns()
        
        if drawdowns:
            # Calculate drawdown statistics
            metrics['max_drawdown_duration'] = self._calculate_max_drawdown_duration()
            metrics['avg_drawdown_duration'] = self._calculate_avg_drawdown_duration()
            
            # Calculate Ulcer Index
            metrics['ulcer_index'] = np.sqrt(np.mean(np.array(drawdowns) ** 2))
            
            # Calculate Pain Index
            metrics['pain_index'] = np.mean(drawdowns)
            
            # Calculate Gain to Pain Ratio
            metrics['gain_to_pain_ratio'] = np.mean(returns) / metrics['pain_index'] if metrics['pain_index'] > 0 else float('inf')
            
            # Calculate Recovery Factor
            metrics['recovery_factor'] = self.return_metrics.get('total_return', 0.0) / max(drawdowns) if max(drawdowns) > 0 else float('inf')
        
        # Calculate Omega Ratio
        threshold = 0.0  # Can be changed to a target return
        returns_above = returns[returns > threshold]
        returns_below = returns[returns <= threshold]
        
        if len(returns_below) > 0 and abs(np.sum(returns_below)) > 0:
            metrics['omega_ratio'] = np.sum(returns_above) / abs(np.sum(returns_below))
        else:
            metrics['omega_ratio'] = float('inf')
        
        # Calculate Kappa Ratio (similar to Sortino but with higher moment risk)
        if len(returns_below) > 0:
            lower_partial_moment = np.mean((threshold - returns_below) ** 3) ** (1/3)
            metrics['kappa_ratio'] = (np.mean(returns) - threshold) / lower_partial_moment if lower_partial_moment > 0 else float('inf')
        else:
            metrics['kappa_ratio'] = float('inf')
        
        # Store for later use
        self.advanced_metrics = metrics
        
        return metrics
    
    def _calculate_drawdowns(self) -> List[float]:
        """
        Calculate drawdowns.
        
        Returns:
            List of drawdown values
        """
        if self.equity_curve.empty or 'equity' not in self.equity_curve.columns:
            return []
        
        # Calculate drawdowns
        equity = self.equity_curve['equity'].values
        peak = equity[0]
        drawdowns = []
        
        for value in equity:
            if value > peak:
                peak = value
            
            drawdown = (peak - value) / peak
            drawdowns.append(drawdown)
        
        return drawdowns
    
    def _calculate_max_drawdown_duration(self) -> float:
        """
        Calculate maximum drawdown duration.
        
        Returns:
            Maximum drawdown duration in days
        """
        if self.equity_curve.empty or 'equity' not in self.equity_curve.columns:
            return 0.0
        
        # Calculate drawdowns
        equity = self.equity_curve['equity'].values
        peak = equity[0]
        peak_idx = 0
        max_duration = 0
        current_duration = 0
        
        for i, value in enumerate(equity):
            if value > peak:
                peak = value
                peak_idx = i
                current_duration = 0
            else:
                current_duration = i - peak_idx
                max_duration = max(max_duration, current_duration)
        
        # Convert to days if we have a datetime index
        if isinstance(self.equity_curve.index, pd.DatetimeIndex) and max_duration > 0:
            # Get start and end dates for max drawdown
            if max_duration < len(self.equity_curve):
                start_date = self.equity_curve.index[peak_idx]
                end_date = self.equity_curve.index[peak_idx + max_duration]
                return (end_date - start_date).days
            else:
                return 0.0
        
        return max_duration
    
    def _calculate_avg_drawdown_duration(self) -> float:
        """
        Calculate average drawdown duration.
        
        Returns:
            Average drawdown duration in days
        """
        if self.equity_curve.empty or 'equity' not in self.equity_curve.columns:
            return 0.0
        
        # Calculate drawdowns
        equity = self.equity_curve['equity'].values
        peak = equity[0]
        peak_idx = 0
        in_drawdown = False
        drawdown_durations = []
        
        for i, value in enumerate(equity):
            if value > peak:
                # New peak
                peak = value
                peak_idx = i
                
                # End drawdown if we were in one
                if in_drawdown:
                    in_drawdown = False
            elif value < peak:
                # In drawdown
                if not in_drawdown:
                    # Start new drawdown
                    in_drawdown = True
                    drawdown_start = peak_idx
                
                # Check if we're ending the drawdown (reached a new peak)
                if i < len(equity) - 1 and equity[i + 1] > peak:
                    in_drawdown = False
                    drawdown_duration = i - drawdown_start
                    drawdown_durations.append(drawdown_duration)
        
        # Add final drawdown if still in one
        if in_drawdown:
            drawdown_duration = len(equity) - 1 - drawdown_start
            drawdown_durations.append(drawdown_duration)
        
        # Calculate average duration
        if drawdown_durations:
            avg_duration = np.mean(drawdown_durations)
            
            # Convert to days if we have a datetime index
            if isinstance(self.equity_curve.index, pd.DatetimeIndex):
                # Estimate days per period
                if len(self.equity_curve) > 1:
                    first_date = self.equity_curve.index[0]
                    last_date = self.equity_curve.index[-1]
                    total_days = (last_date - first_date).days
                    days_per_period = total_days / (len(self.equity_curve) - 1)
                    return avg_duration * days_per_period
            
            return avg_duration
        
        return 0.0
    
    def calculate_all_metrics(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate all metrics.
        
        Returns:
            Dictionary of all metrics
        """
        # Calculate individual metric groups
        return_metrics = self.calculate_return_metrics()
        risk_metrics = self.calculate_risk_metrics()
        trade_metrics = self.calculate_trade_metrics()
        time_metrics = self.calculate_time_metrics()
        benchmark_metrics = self.calculate_benchmark_metrics()
        advanced_metrics = self.calculate_advanced_metrics()
        
        # Combine all metrics
        all_metrics = {
            'return': return_metrics,
            'risk': risk_metrics,
            'trade': trade_metrics,
            'time': time_metrics,
            'benchmark': benchmark_metrics,
            'advanced': advanced_metrics
        }
        
        return all_metrics
    
    def generate_report(self, output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a comprehensive backtest report.
        
        Args:
            output_dir: Directory to save report files
            
        Returns:
            Dictionary with report data
        """
        # Calculate all metrics
        all_metrics = self.calculate_all_metrics()
        
        # Create report data
        report_data = {
            'metrics': all_metrics,
            'equity_curve': self.equity_curve.to_dict() if not self.equity_curve.empty else {},
            'trade_history': self.trade_history.to_dict() if not self.trade_history.empty else {},
            'performance_metrics': self.performance_metrics
        }
        
        # Generate plots if output directory is provided
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            self._generate_plots(output_dir)
        
        return report_data
    
    def _generate_plots(self, output_dir: str) -> None:
        """
        Generate plots for the backtest report.
        
        Args:
            output_dir: Directory to save plots
        """
        if self.equity_curve.empty:
            return
        
        # Plot equity curve
        plt.figure(figsize=(12, 6))
        plt.plot(self.equity_curve.index, self.equity_curve['equity'])
        plt.title('Equity Curve')
        plt.xlabel('Date')
        plt.ylabel('Equity')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'equity_curve.png'))
        plt.close()
        
        # Plot drawdowns
        drawdowns = self._calculate_drawdowns()
        
        if drawdowns:
            plt.figure(figsize=(12, 6))
            plt.plot(self.equity_curve.index, drawdowns)
            plt.title('Drawdowns')
            plt.xlabel('Date')
            plt.ylabel('Drawdown')
            plt.grid(True)
            plt.savefig(os.path.join(output_dir, 'drawdowns.png'))
            plt.close()
        
        # Plot returns
        plt.figure(figsize=(12, 6))
        plt.plot(self.equity_curve.index, self.equity_curve['return'])
        plt.title('Daily Returns')
        plt.xlabel('Date')
        plt.ylabel('Return')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'returns.png'))
        plt.close()
        
        # Plot cumulative returns
        plt.figure(figsize=(12, 6))
        plt.plot(self.equity_curve.index, self.equity_curve['cumulative_return'])
        
        # Add benchmark if available
        if self.benchmark_data is not None and 'cumulative_return' in self.benchmark_data.columns:
            plt.plot(self.benchmark_data.index, self.benchmark_data['cumulative_return'])
            plt.legend(['Strategy', 'Benchmark'])
        
        plt.title('Cumulative Returns')
        plt.xlabel('Date')
        plt.ylabel('Cumulative Return')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'cumulative_returns.png'))
        plt.close()
        
        # Plot return distribution
        plt.figure(figsize=(12, 6))
        plt.hist(self.equity_curve['return'], bins=50)
        plt.title('Return Distribution')
        plt.xlabel('Return')
        plt.ylabel('Frequency')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'return_distribution.png'))
        plt.close()
        
        # Plot underwater equity curve
        underwater = np.maximum.accumulate(self.equity_curve['equity']) - self.equity_curve['equity']
        underwater_pct = underwater / np.maximum.accumulate(self.equity_curve['equity'])
        
        plt.figure(figsize=(12, 6))
        plt.plot(self.equity_curve.index, underwater_pct)
        plt.title('Underwater Equity Curve')
        plt.xlabel('Date')
        plt.ylabel('Drawdown')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'underwater.png'))
        plt.close()
        
        # Plot trade results if available
        if not self.trade_history.empty and 'profit_loss' in self.trade_history.columns:
            # Plot trade P&L
            plt.figure(figsize=(12, 6))
            plt.bar(range(len(self.trade_history)), self.trade_history['profit_loss'])
            plt.title('Trade P&L')
            plt.xlabel('Trade #')
            plt.ylabel('P&L')
            plt.grid(True)
            plt.savefig(os.path.join(output_dir, 'trade_pnl.png'))
            plt.close()
            
            # Plot cumulative P&L
            plt.figure(figsize=(12, 6))
            plt.plot(np.cumsum(self.trade_history['profit_loss']))
            plt.title('Cumulative P&L')
            plt.xlabel('Trade #')
            plt.ylabel('Cumulative P&L')
            plt.grid(True)
            plt.savefig(os.path.join(output_dir, 'cumulative_pnl.png'))
            plt.close()
            
            # Plot trade duration if available
            if 'duration' in self.trade_history.columns:
                plt.figure(figsize=(12, 6))
                plt.scatter(self.trade_history['duration'], self.trade_history['profit_loss'])
                plt.title('Trade Duration vs P&L')
                plt.xlabel('Duration (seconds)')
                plt.ylabel('P&L')
                plt.grid(True)
                plt.savefig(os.path.join(output_dir, 'duration_vs_pnl.png'))
                plt.close()
    
    def print_summary(self) -> None:
        """Print summary of backtest results."""
        # Calculate all metrics
        all_metrics = self.calculate_all_metrics()
        
        # Print return metrics
        print("\n--- Return Metrics ---")
        print(f"Total Return: {all_metrics['return'].get('total_return', 0.0):.2%}")
        print(f"Annualized Return: {all_metrics['return'].get('annualized_return', 0.0):.2%}")
        print(f"Mean Daily Return: {all_metrics['return'].get('mean_return_daily', 0.0):.4%}")
        
        # Print risk metrics
        print("\n--- Risk Metrics ---")
        print(f"Volatility (Annual): {all_metrics['risk'].get('volatility_annual', 0.0):.2%}")
        print(f"Maximum Drawdown: {all_metrics['risk'].get('max_drawdown', 0.0):.2%}")
        print(f"Sharpe Ratio: {all_metrics['risk'].get('sharpe_ratio', 0.0):.2f}")
        print(f"Sortino Ratio: {all_metrics['risk'].get('sortino_ratio', 0.0):.2f}")
        
        # Print trade metrics
        print("\n--- Trade Metrics ---")
        print(f"Number of Trades: {all_metrics['trade'].get('num_trades', 0)}")
        print(f"Win Rate: {all_metrics['trade'].get('win_rate', 0.0):.2%}")
        print(f"Profit Factor: {all_metrics['trade'].get('profit_factor', 0.0):.2f}")
        print(f"Average Trade: {all_metrics['trade'].get('avg_trade', 0.0):.2f}")
        
        # Print benchmark metrics if available
        if all_metrics['benchmark']:
            print("\n--- Benchmark Metrics ---")
            print(f"Alpha: {all_metrics['benchmark'].get('alpha', 0.0):.4f}")
            print(f"Beta: {all_metrics['benchmark'].get('beta', 0.0):.2f}")
            print(f"R-Squared: {all_metrics['benchmark'].get('r_squared', 0.0):.2f}")
            print(f"Information Ratio: {all_metrics['benchmark'].get('information_ratio', 0.0):.2f}")
        
        # Print advanced metrics
        print("\n--- Advanced Metrics ---")
        print(f"Max Consecutive Wins: {all_metrics['advanced'].get('max_consecutive_wins', 0)}")
        print(f"Max Consecutive Losses: {all_metrics['advanced'].get('max_consecutive_losses', 0)}")
        print(f"Gain to Pain Ratio: {all_metrics['advanced'].get('gain_to_pain_ratio', 0.0):.2f}")
        print(f"Maximum Drawdown Duration (days): {all_metrics['advanced'].get('max_drawdown_duration', 0.0):.2f}")


def analyze_multiple_backtests(
    results_list: List[Dict[str, Any]],
    labels: Optional[List[str]] = None,
    benchmark_data: Optional[pd.DataFrame] = None,
    risk_free_rate: float = 0.02,
    output_dir: Optional[str] = None
) -> Dict[str, Any]:
    """
    Analyze multiple backtest results for comparison.
    
    Args:
        results_list: List of backtest results
        labels: List of labels for the backtests
        benchmark_data: Benchmark data for comparison
        risk_free_rate: Annual risk-free rate
        output_dir: Directory to save comparison files
        
    Returns:
        Dictionary with comparison data
    """
    # Validate inputs
    if not results_list:
        return {}
    
    if labels is None:
        labels = [f"Strategy {i+1}" for i in range(len(results_list))]
    
    if len(labels) != len(results_list):
        raise ValueError("Number of labels must match number of backtest results")
    
    # Create analyzers for each backtest
    analyzers = [BacktestAnalyzer(results, benchmark_data, risk_free_rate) for results in results_list]
    
    # Calculate metrics for each backtest
    all_metrics = [analyzer.calculate_all_metrics() for analyzer in analyzers]
    
    # Create comparison data
    comparison = {
        'labels': labels,
        'metrics': {}
    }
    
    # Combine metrics
    metric_groups = ['return', 'risk', 'trade', 'time', 'benchmark', 'advanced']
    
    for group in metric_groups:
        comparison['metrics'][group] = {}
        
        # Get all metric keys for this group
        all_keys = set()
        for metrics in all_metrics:
            if group in metrics:
                all_keys.update(metrics[group].keys())
        
        # Combine metrics for each key
        for key in all_keys:
            comparison['metrics'][group][key] = [
                metrics[group].get(key, float('nan')) if group in metrics else float('nan')
                for metrics in all_metrics
            ]
    
    # Generate comparison plots if output directory is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        _generate_comparison_plots(analyzers, labels, output_dir)
    
    return comparison


def _generate_comparison_plots(
    analyzers: List[BacktestAnalyzer],
    labels: List[str],
    output_dir: str
) -> None:
    """
    Generate comparison plots for multiple backtests.
    
    Args:
        analyzers: List of backtest analyzers
        labels: List of labels for the backtests
        output_dir: Directory to save plots
    """
    # Plot cumulative returns
    plt.figure(figsize=(12, 6))
    
    for i, analyzer in enumerate(analyzers):
        if not analyzer.equity_curve.empty and 'cumulative_return' in analyzer.equity_curve.columns:
            plt.plot(analyzer.equity_curve.index, analyzer.equity_curve['cumulative_return'])
    
    # Add benchmark if available
    if analyzers[0].benchmark_data is not None and 'cumulative_return' in analyzers[0].benchmark_data.columns:
        plt.plot(analyzers[0].benchmark_data.index, analyzers[0].benchmark_data['cumulative_return'])
        labels.append('Benchmark')
    
    plt.title('Cumulative Returns Comparison')
    plt.xlabel('Date')
    plt.ylabel('Cumulative Return')
    plt.legend(labels)
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'cumulative_returns_comparison.png'))
    plt.close()
    
    # Plot drawdowns
    plt.figure(figsize=(12, 6))
    
    for i, analyzer in enumerate(analyzers):
        drawdowns = analyzer._calculate_drawdowns()
        
        if drawdowns and not analyzer.equity_curve.empty:
            plt.plot(analyzer.equity_curve.index, drawdowns)
    
    plt.title('Drawdowns Comparison')
    plt.xlabel('Date')
    plt.ylabel('Drawdown')
    plt.legend(labels[:len(analyzers)])
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'drawdowns_comparison.png'))
    plt.close()
    
    # Plot key metrics comparison
    key_metrics = [
        ('return', 'annualized_return', 'Annualized Return'),
        ('risk', 'sharpe_ratio', 'Sharpe Ratio'),
        ('risk', 'max_drawdown', 'Maximum Drawdown'),
        ('trade', 'win_rate', 'Win Rate'),
        ('trade', 'profit_factor', 'Profit Factor')
    ]
    
    for group, key, title in key_metrics:
        values = []
        
        for analyzer in analyzers:
            all_metrics = analyzer.calculate_all_metrics()
            if group in all_metrics and key in all_metrics[group]:
                values.append(all_metrics[group][key])
            else:
                values.append(0.0)
        
        plt.figure(figsize=(10, 6))
        plt.bar(labels[:len(analyzers)], values)
        plt.title(title)
        plt.ylabel(title)
        plt.xticks(rotation=45)
        plt.grid(True, axis='y')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f"{key}_comparison.png"))
        plt.close()
