# mcp_http_gateway.py - Async queue-based FastAPI bridge
import asyncio, json, subprocess, uuid
from fastapi import FastAP<PERSON>, HTTPException
import uvicorn

app = FastAPI()

proc = subprocess.Popen(
    ["python", "mcp_server_production.py"],
    stdin=subprocess.PIPE,
    stdout=subprocess.PIPE,
    stderr=subprocess.PIPE,
    text=True
)

# Async task that continuously reads daemon stdout
pending = {}  # uid -> Future

async def _stdout_reader():
    loop = asyncio.get_running_loop()
    while True:
        try:
            line = proc.stdout.readline()
            if not line:
                await asyncio.sleep(0.01)
                continue
            line = line.strip()
            if not line:
                continue
                
            resp = json.loads(line)
            uid = resp.pop("_id", None)
            if uid and uid in pending:
                pending[uid].set_result(resp)
        except json.JSONDecodeError as e:
            print(f"Malformed MCP line: {line[:120] if line else 'empty'}")
        except Exception as e:
            print(f"Error in stdout reader: {e}")
            await asyncio.sleep(0.01)

# Start the stdout reader task - will be created at startup
# asyncio.create_task(_stdout_reader())  # Remove this line

async def mcp_call(cmd: dict, timeout=8):
    uid = str(uuid.uuid4())
    fut = asyncio.get_running_loop().create_future()
    pending[uid] = fut
    
    # Use the JSON-RPC id field, but also add our _id for multiplexing
    cmd["_id"] = uid
    
    try:
        proc.stdin.write(json.dumps(cmd) + "\n")
        proc.stdin.flush()
        result = await asyncio.wait_for(fut, timeout)
        
        # Return the result field from JSON-RPC response
        if "result" in result:
            return result["result"]
        elif "error" in result:
            raise HTTPException(500, detail=f"MCP error: {result['error']}")
        else:
            return result
            
    except asyncio.TimeoutError:
        raise HTTPException(504, detail="MCP call timeout")
    finally:
        pending.pop(uid, None)

@app.get("/bars")
async def bars(tk: str, tf: str = "1"):
    return await mcp_call({
        "jsonrpc": "2.0",
        "method": "get_price_data", 
        "params": {"ticker": tk, "timespan": "minute", "multiplier": int(tf)},
        "id": 1
    })

@app.get("/options")
async def options(tk: str):
    return await mcp_call({
        "jsonrpc": "2.0",
        "method": "get_options_chain",
        "params": {"ticker": tk},
        "id": 1
    })

@app.get("/api/bars")
async def api_bars(tk: str, tf: str = "1"):
    return await mcp_call({
        "jsonrpc": "2.0",
        "method": "get_price_data",
        "params": {"ticker": tk, "timespan": "minute", "multiplier": int(tf)},
        "id": 1
    })

@app.get("/api/options")
async def api_options(tk: str):
    return await mcp_call({
        "jsonrpc": "2.0",
        "method": "get_options_chain", 
        "params": {"ticker": tk},
        "id": 1
    })

@app.get("/")
async def health():
    return await mcp_call({
        "jsonrpc": "2.0",
        "method": "health_check",
        "params": {},
        "id": 1
    })

@app.on_event("startup")
async def startup_event():
    # Start the stdout reader when FastAPI starts
    asyncio.create_task(_stdout_reader())

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8004)
