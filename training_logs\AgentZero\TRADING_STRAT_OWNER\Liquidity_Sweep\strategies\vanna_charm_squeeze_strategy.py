"""
Vanna/Charm Squeeze Strategy

Exploits higher-order Greeks positioning for explosive moves by detecting <PERSON><PERSON> and Charm
exposure clusters at key strike prices. When these second-order Greeks converge with
high open interest, they create powerful squeeze conditions that force market makers
into massive hedging operations, resulting in explosive price movements.

Strategy Logic:
1. Scan options chain for <PERSON>na and Charm exposure at each strike
2. Identify strike clustering with significant Greeks exposure
3. Analyze IV term structure and skew for setup conditions
4. Detect squeeze types: Pin Risk, Acceleration, or Combined Effects
5. Enter based on squeeze direction with Greeks-based risk management
6. Exit using Delta hedging and volatility expansion signals

Integration Points:
- Enhanced Greeks Core (Vanna/Charm calculations)
- Higher Order Greeks Physics (advanced calculations)
- IV Skew Analyzer (setup confirmation)
- Options Chain Analyzer (strike clustering)
- Enhanced Flow Physics (momentum confirmation)

NO PLACEHOLDERS. FULL IMPLEMENTATION. INSTITUTIONAL GRADE.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd
import math
import time
from datetime import datetime, timedelta
from scipy import stats
import sys
import os
from pathlib import Path

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

# Import handling for both direct execution and module execution
from strategies.base_strategy import BaseStrategy, StrategySignal, SignalDirection

# Import Enhanced Greeks components
try:
    from bsm_model import BlackScholesModel
    BSM_MODEL_AVAILABLE = True
    logging.info("Authenticated BSM model imported successfully")
except ImportError:
    BSM_MODEL_AVAILABLE = False
    logging.warning("BSM model not available - using fallback Greeks calculations")

try:
    from options.Enhanced_Greeks_Core import EnhancedGreeksCore
    from options.Higher_Order_Greeks_Physics import HigherOrderGreeksPhysics
    from options.Enhanced_Ordinal_Greeks_Analysis import EnhancedOrdinalGreeksAnalysis
    ENHANCED_GREEKS_AVAILABLE = True
    logging.info("Enhanced Greeks components imported successfully")
except ImportError as e:
    ENHANCED_GREEKS_AVAILABLE = False
    logging.debug(f"Enhanced Greeks components not available - using fallback calculations. Error: {e}")

# Import analyzers
try:
    from analyzers.iv_skew_analyzer import IVSkewAnalyzer
    from analyzers.options_oi_analyzer import OptionsOIAnalyzer
    from flow_physics_adapter import FlowPhysicsAdapter
    ANALYZERS_AVAILABLE = True
except ImportError:
    ANALYZERS_AVAILABLE = False
    logging.warning("Some analyzers not available - using fallback implementations")

# Import Unified API Gateway
try:
    from api_robustness.unified_api_gateway import UnifiedAPIGateway
except ImportError:
    UnifiedAPIGateway = None
    logging.warning("UnifiedAPIGateway not available")

# Import BSM model if available
try:
    from bsm_model import BlackScholesModel
    VECTORIZED_BSM_AVAILABLE = True
    logging.info("BSM model loaded successfully")
except ImportError:
    VECTORIZED_BSM_AVAILABLE = False
    logging.warning("BSM not available - using fallback")

logger = logging.getLogger(__name__)


class VannaCharmSqueezeStrategy(BaseStrategy):
    """
    Vanna/Charm Squeeze Strategy - Exploit higher-order Greeks positioning.

    This strategy identifies and trades squeeze conditions created by Vanna and Charm
    exposure clusters. When these second-order Greeks converge at key strikes with
    high open interest, they create powerful market dynamics that force explosive moves.

    Squeeze Types:
    1. Vanna Pin Risk - Delta clustering creates pin/breakout scenarios
    2. Charm Acceleration - Time decay acceleration near expiration
    3. Combined Effects - Vanna + Charm convergence for volatility explosions
    """

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for Vanna/Charm squeeze strategy."""
        return {
            'enabled': True,
            'min_confidence': 0.75,
            'max_signals_per_ticker': 2,

            # Greeks Exposure Thresholds
            'min_vanna_exposure': 50000,        # Minimum Vanna exposure for consideration
            'min_charm_exposure': 10000,        # Minimum Charm exposure for consideration
            'vanna_clustering_threshold': 0.02,  # Price distance for Vanna clustering
            'charm_clustering_threshold': 0.015, # Price distance for Charm clustering
            'combined_exposure_multiplier': 1.5, # Multiplier for combined effects

            # Strike Analysis
            'min_open_interest': 1000,          # Minimum OI for significant strikes
            'oi_concentration_ratio': 0.15,     # OI concentration threshold
            'strike_range_factor': 0.05,        # % range around current price to analyze
            'max_strikes_analyzed': 20,         # Maximum strikes to analyze per side

            # IV and Time Parameters
            'min_iv_skew': 0.03,               # Minimum IV skew for setup
            'max_dte': 45,                     # Maximum days to expiration
            'min_dte': 1,                      # Minimum days to expiration
            'iv_percentile_threshold': 0.7,    # IV percentile for elevated IV
            'term_structure_slope': 0.02,      # Required term structure slope

            # Squeeze Detection
            'vanna_pin_threshold': 100000,      # Vanna exposure for pin risk
            'charm_acceleration_threshold': 25000, # Charm for acceleration
            'squeeze_confluence_factor': 0.8,   # Factor for confluence detection
            'breakout_probability_threshold': 0.65, # Min probability for breakout

            # Entry Conditions
            'momentum_confirmation_required': True,
            'min_flow_velocity': 0.3,          # Flow velocity confirmation
            'volume_surge_threshold': 2.0,     # Volume surge requirement
            'delta_hedging_threshold': 0.1,    # Delta imbalance threshold

            # Position Sizing
            'base_position_size': 0.6,         # Conservative base sizing
            'squeeze_intensity_scaling': {
                'low': (0, 50000, 0.4),        # (min, max, multiplier)
                'medium': (50000, 150000, 0.6),
                'high': (150000, 300000, 0.8),
                'extreme': (300000, 1000000, 1.0)
            },

            # Risk Management
            'max_loss_per_trade': 0.03,        # 3% max loss per trade
            'vanna_pin_stop_multiplier': 0.8,  # Tighter stops for pin risk
            'acceleration_stop_multiplier': 1.2, # Wider stops for acceleration
            'iv_crush_protection': True,       # Protect against IV crush
            'delta_hedge_adjustment': True,    # Adjust for delta hedging

            # Exit Conditions
            'profit_targets': {
                'vanna_pin': 0.02,             # 2% target for pin trades
                'charm_acceleration': 0.04,    # 4% target for acceleration
                'combined_squeeze': 0.06,      # 6% target for combined
                'volatility_explosion': 0.08   # 8% target for vol explosion
            },
            'time_decay_exit': {
                'enabled': True,
                'theta_threshold': -50,        # Exit if theta exceeds threshold
                'days_to_exit': 2              # Exit X days before expiration
            },

            # Greeks Calculation Parameters
            'risk_free_rate': 0.05,           # Risk-free rate for calculations
            'dividend_yield': 0.0,            # Default dividend yield
            'volatility_estimation_days': 30,  # Days for vol estimation
            'greeks_recalc_frequency': 300    # Recalculate every 5 minutes
        }

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize Vanna/Charm Squeeze Strategy."""
        super().__init__(config)

        # Initialize Enhanced Greeks components
        if ENHANCED_GREEKS_AVAILABLE:
            try:
                self.enhanced_greeks_core = EnhancedGreeksCore()
                self.higher_order_greeks = HigherOrderGreeksPhysics()
                self.ordinal_greeks_analysis = EnhancedOrdinalGreeksAnalysis()
                logger.info("Enhanced Greeks components initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize Enhanced Greeks: {e}")
                self.enhanced_greeks_core = None
                self.higher_order_greeks = None
                self.ordinal_greeks_analysis = None
        else:
            self.enhanced_greeks_core = None
            self.higher_order_greeks = None
            self.ordinal_greeks_analysis = None

        # Initialize analyzers
        if ANALYZERS_AVAILABLE:
            try:
                self.iv_skew_analyzer = IVSkewAnalyzer()
                self.options_oi_analyzer = OptionsOIAnalyzer()  # Use correct class name
                self.flow_physics_adapter = FlowPhysicsAdapter()
                logger.info("Analyzers initialized successfully")
            except Exception as e:
                logger.warning(f"Failed to initialize analyzers: {e}")
                self.iv_skew_analyzer = None
                self.options_oi_analyzer = None
                self.flow_physics_adapter = None
        else:
            self.iv_skew_analyzer = None
            self.options_oi_analyzer = None
            self.flow_physics_adapter = None

        # Initialize API gateway and your VECTORIZED BSM MODEL
        self.api_gateway = UnifiedAPIGateway()

        # Initialize BSM Model
        if VECTORIZED_BSM_AVAILABLE:
            self.bsm_model = BlackScholesModel()
            logger.info("Vectorized BSM model initialized successfully")
        else:
            self.bsm_model = None
            logger.warning("Using fallback BSM calculations")

        # Cache for Greeks calculations
        self.greeks_cache = {}
        self.last_calculation_time = {}

        logger.info("Vanna/Charm Squeeze Strategy initialized")

    def analyze(self,
               ticker: str,
               data: Dict[str, Any],
               analysis_results: Dict[str, Any]) -> List[StrategySignal]:
        """
        Analyze for Vanna/Charm squeeze opportunities.

        Args:
            ticker: Stock ticker
            data: Market data including options_data, current_price, etc.
            analysis_results: Results from other analyzers

        Returns:
            List of Vanna/Charm squeeze signals
        """
        # Validate data
        is_valid, error_msg = self.validate_data(data)
        if not is_valid:
            logger.warning(f"Invalid data for {ticker}: {error_msg}")
            return []

        signals = []

        try:
            # Get options data
            options_data = self._get_options_data(ticker, data)
            if options_data is None or options_data.empty:
                logger.debug(f"No options data available for {ticker}")
                return []

            current_price = data.get('current_price', options_data['underlying_price'].iloc[0])

            # Step 1: Calculate Vanna and Charm exposure for all strikes
            greeks_exposure = self._calculate_greeks_exposure(ticker, options_data, current_price)
            if not greeks_exposure:
                logger.debug(f"No significant Greeks exposure for {ticker}")
                return []

            # Step 2: Identify strike clustering and concentration
            clustering_analysis = self._analyze_strike_clustering(greeks_exposure, current_price)
            if not clustering_analysis['significant_clusters']:
                logger.debug(f"No significant clustering found for {ticker}")
                return []

            # Step 3: Analyze IV conditions and term structure
            iv_analysis = self._analyze_iv_conditions(ticker, options_data, current_price)
            if not iv_analysis['favorable_conditions']:
                logger.debug(f"IV conditions not favorable for {ticker}")
                return []

            # Step 4: Detect squeeze types and conditions
            squeeze_conditions = self._detect_squeeze_conditions(
                clustering_analysis, iv_analysis, current_price
            )

            if not squeeze_conditions:
                logger.debug(f"No squeeze conditions detected for {ticker}")
                return []

            # Step 5: Confirm momentum and flow conditions
            momentum_confirmed = self._confirm_momentum_conditions(ticker, data, analysis_results)

            # Step 6: Generate signals for each detected squeeze
            for squeeze in squeeze_conditions:
                squeeze_signals = self._generate_squeeze_signals(
                    ticker=ticker,
                    current_price=current_price,
                    squeeze_info=squeeze,
                    momentum_confirmed=momentum_confirmed,
                    data=data
                )
                signals.extend(squeeze_signals)

            # Step 7: Filter and rank signals
            return self.filter_signals(signals)

        except Exception as e:
            logger.error(f"Error analyzing Vanna/Charm squeeze for {ticker}: {e}")
            return []

    def _get_options_data(self, ticker: str, data: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Get options data for analysis."""
        # Try to get from data first
        if 'options_data' in data and data['options_data'] is not None:
            options_data = data['options_data']
            if not options_data.empty:
                return options_data

        # Fallback: fetch from API
        try:
            current_price = data.get('current_price', 100.0)  # Fallback price

            # Get options chain from API
            options_data = self.api_gateway.get_options_chain(
                ticker=ticker
            )

            if options_data is not None and not options_data.empty:
                # Map API gateway columns to expected strategy columns
                column_mapping = {
                    'type': 'option_type',
                    'expiry': 'expiration'
                }

                # Rename columns to match strategy expectations
                for api_col, strategy_col in column_mapping.items():
                    if api_col in options_data.columns and strategy_col not in options_data.columns:
                        options_data[strategy_col] = options_data[api_col]

                # Add required columns if missing
                if 'underlying_price' not in options_data.columns:
                    options_data['underlying_price'] = current_price
                if 'timestamp' not in options_data.columns:
                    options_data['timestamp'] = pd.Timestamp.now()

                # Calculate days to expiration if missing
                if 'days_to_expiration' not in options_data.columns and 'expiration' in options_data.columns:
                    current_time = pd.Timestamp.now()
                    exp_dates = pd.to_datetime(options_data['expiration'])
                    options_data['days_to_expiration'] = (exp_dates - current_time).dt.days

                return options_data

        except Exception as e:
            logger.error(f"Failed to fetch options data for {ticker}: {e}")

        return None

    def _calculate_greeks_exposure(self,
                                 ticker: str,
                                 options_data: pd.DataFrame,
                                 current_price: float) -> Dict[float, Dict[str, float]]:
        """Calculate Vanna and Charm exposure using vectorized BSM model."""
        try:
            logger.debug(f"Starting Greeks calculation for {ticker}")

            # Use vectorized BSM model for speed
            if VECTORIZED_BSM_AVAILABLE and self.bsm_model:
                return self._calculate_vectorized_greeks_exposure(ticker, options_data, current_price)
            else:
                # Fallback to slower method
                return self._calculate_fallback_greeks_exposure(ticker, options_data, current_price)

        except Exception as e:
            logger.error(f"Error calculating Greeks exposure: {e}")
            return {}

    def _calculate_vectorized_greeks_exposure(self,
                                            ticker: str,
                                            options_data: pd.DataFrame,
                                            current_price: float) -> Dict[float, Dict[str, float]]:
        """Vectorized Greeks calculation using authenticated BSM model."""
        try:
            logger.debug(f"Processing {len(options_data)} options with authenticated BSM calculation")

            greeks_exposure = {}

            if BSM_MODEL_AVAILABLE:
                # Use authenticated BSM model for accurate calculations
                return self._calculate_bsm_greeks_exposure(options_data, current_price)
            else:
                # Fallback to legacy method
                return self._calculate_legacy_vectorized_greeks(options_data, current_price)

        except Exception as e:
            logger.error(f"Error in vectorized Greeks calculation: {e}")
            return {}
    
    def _calculate_bsm_greeks_exposure(self, options_data: pd.DataFrame, current_price: float) -> Dict[float, Dict[str, float]]:
        """Calculate Greeks exposure using authenticated Black-Scholes-Merton model."""
        from datetime import datetime
        
        # Prepare BSM inputs
        current_date = datetime.now()
        risk_free_rate = 0.05  # Should be fetched from market data
        
        # Calculate time to expiration
        if 'expiration_date' in options_data.columns:
            exp_dates = pd.to_datetime(options_data['expiration_date'])
            time_to_expiry = (exp_dates - current_date).dt.days / 365.0
        else:
            # Estimate from option chain structure
            time_to_expiry = np.full(len(options_data), 0.25)
        
        # Prepare vectorized inputs for authenticated BSM
        S = np.full(len(options_data), current_price)
        K = options_data['strike'].values
        T = np.maximum(time_to_expiry.values, 1/365)  # Minimum 1 day
        r = np.full(len(options_data), risk_free_rate)
        sigma = options_data.get('implied_volatility', np.full(len(options_data), 0.25)).values
        option_types = options_data.get('option_type', ['call'] * len(options_data)).values
        
        # Calculate all Greeks using authenticated BSM model
        greeks_results = BlackScholesModel.calculate_batch(S, K, T, r, sigma, option_types)
        
        # Extract relevant Greeks
        calculated_vanna = greeks_results['vanna']
        calculated_charm = greeks_results['charm']
        calculated_gamma = greeks_results['gamma']
        calculated_delta = greeks_results['delta']
        calculated_theta = greeks_results['theta']
        calculated_vega = greeks_results['vega']
        
        # Group by strike for exposure analysis
        greeks_exposure = {}
        for idx, (_, row) in enumerate(options_data.iterrows()):
            strike = row['strike']
            
            if strike not in greeks_exposure:
                greeks_exposure[strike] = {
                    'total_vanna': 0.0,
                    'total_charm': 0.0,
                    'total_gamma': 0.0,
                    'total_delta': 0.0,
                    'total_theta': 0.0,
                    'total_vega': 0.0,
                    'open_interest': 0.0,
                    'option_count': 0,
                    'calculation_method': 'authenticated_bsm'
                }
            
            # Weight Greeks by open interest
            oi_weight = row.get('open_interest', 1.0)
            
            greeks_exposure[strike]['total_vanna'] += calculated_vanna[idx] * oi_weight
            greeks_exposure[strike]['total_charm'] += calculated_charm[idx] * oi_weight
            greeks_exposure[strike]['total_gamma'] += calculated_gamma[idx] * oi_weight
            greeks_exposure[strike]['total_delta'] += calculated_delta[idx] * oi_weight
            greeks_exposure[strike]['total_theta'] += calculated_theta[idx] * oi_weight
            greeks_exposure[strike]['total_vega'] += calculated_vega[idx] * oi_weight
            greeks_exposure[strike]['open_interest'] += oi_weight
            greeks_exposure[strike]['option_count'] += 1
            
            # Add derived metrics
            dte = T[idx] * 365  # Days to expiration
            greeks_exposure[strike]['distance_from_spot'] = abs(strike - current_price) / current_price
            greeks_exposure[strike]['in_the_money'] = self._calculate_proper_itm(strike, current_price, option_types[idx], dte)
            greeks_exposure[strike]['time_weighted_charm'] = calculated_charm[idx] * self._time_decay_weight(dte)
            
        logger.info(f"Calculated authenticated BSM Greeks for {len(greeks_exposure)} strikes")
        return greeks_exposure
    
    def _calculate_legacy_vectorized_greeks(self, options_data: pd.DataFrame, current_price: float) -> Dict[float, Dict[str, float]]:
        """Legacy vectorized Greeks calculation as fallback."""
        try:
            greeks_exposure = {}
            
            # Prepare vectorized inputs for legacy BSM model
            strikes = options_data['strike'].values
            option_types = options_data['option_type'].values

            # Calculate time to expiry for all options
            current_time = pd.Timestamp.now()
            if 'days_to_expiration' in options_data.columns:
                time_to_expiry = options_data['days_to_expiration'].values / 365.0
            else:
                # Calculate from expiration dates
                exp_dates = pd.to_datetime(options_data['expiration'])
                time_to_expiry = (exp_dates - current_time).dt.total_seconds() / (365.25 * 24 * 3600)
                time_to_expiry = time_to_expiry.values

            # Get implied volatilities
            ivs = options_data['implied_volatility'].values

            # Create arrays for vectorized calculation
            spot_prices = np.full_like(strikes, current_price)
            risk_free_rates = np.full_like(strikes, self.config['risk_free_rate'])

            # Filter out invalid data
            valid_mask = (
                (time_to_expiry >= self.config['min_dte'] / 365.0) &
                (time_to_expiry <= self.config['max_dte'] / 365.0) &
                (ivs > 0) &
                (ivs < 5.0) &
                (strikes > 0)
            )

            if not valid_mask.any():
                logger.warning("No valid options data for vectorized calculation")
                return {}

            # Apply filter
            valid_indices = np.where(valid_mask)[0]
            filtered_strikes = strikes[valid_mask]
            filtered_types = option_types[valid_mask]
            filtered_tte = time_to_expiry[valid_mask]
            filtered_ivs = ivs[valid_mask]
            filtered_spots = spot_prices[valid_mask]
            filtered_rates = risk_free_rates[valid_mask]

            logger.debug(f"Vectorized calculation: {len(filtered_strikes)} valid options")

            # Run vectorized BSM calculation
            start_time = time.time()

            vectorized_greeks = self.bsm_model.calculate_batch(
                S=filtered_spots,
                K=filtered_strikes,
                T=filtered_tte,
                r=filtered_rates,
                sigma=filtered_ivs,
                option_types=filtered_types
            )

            calculation_time = time.time() - start_time
            logger.debug(f"Vectorized BSM completed in {calculation_time:.4f}s - {len(filtered_strikes)/calculation_time:.0f} options/sec")

            # Process results by strike (group options at same strike)
            for i, original_idx in enumerate(valid_indices):
                strike = filtered_strikes[i]
                original_row = options_data.iloc[original_idx]
                oi = original_row.get('open_interest', 0)

                if oi < self.config['min_open_interest']:
                    continue

                # Get vectorized Greeks for this option
                option_vanna = vectorized_greeks['vanna'][i]
                option_charm = vectorized_greeks['charm'][i]
                option_delta = vectorized_greeks['delta'][i]
                option_gamma = vectorized_greeks['gamma'][i]

                # Handle NaN values from your BSM model's stability checks
                if np.isnan(option_vanna) or np.isnan(option_charm):
                    continue

                # Weight by open interest
                weighted_vanna = option_vanna * oi
                weighted_charm = option_charm * oi
                weighted_delta = option_delta * oi
                weighted_gamma = option_gamma * oi

                # Adjust for put/call direction
                if filtered_types[i].lower() == 'put':
                    weighted_delta *= -1  # Puts have negative delta

                # Aggregate by strike
                if strike not in greeks_exposure:
                    greeks_exposure[strike] = {
                        'vanna': 0.0,
                        'charm': 0.0,
                        'delta': 0.0,
                        'gamma': 0.0,
                        'open_interest': 0.0,
                        'distance_from_spot': abs(strike - current_price) / current_price,
                        'in_the_money': (strike < current_price),
                        'time_weighted_charm': 0.0,
                        'option_count': 0
                    }

                # Accumulate exposure
                greeks_exposure[strike]['vanna'] += weighted_vanna
                greeks_exposure[strike]['charm'] += weighted_charm
                greeks_exposure[strike]['delta'] += weighted_delta
                greeks_exposure[strike]['gamma'] += weighted_gamma
                greeks_exposure[strike]['open_interest'] += oi
                greeks_exposure[strike]['option_count'] += 1

                # Time-weighted charm
                dte = filtered_tte[i] * 365
                time_weight = math.sqrt(min(dte, 30) / 30)
                greeks_exposure[strike]['time_weighted_charm'] += weighted_charm * time_weight

            # Filter for significant exposure
            significant_exposure = {}
            for strike, data in greeks_exposure.items():
                if (abs(data['vanna']) >= self.config['min_vanna_exposure'] or
                    abs(data['charm']) >= self.config['min_charm_exposure']):
                    significant_exposure[strike] = data

            logger.debug(f"Found {len(significant_exposure)} strikes with significant Greeks exposure")
            logger.debug(f"Performance: {len(options_data)} options processed in {calculation_time:.4f}s")

            return significant_exposure

        except Exception as e:
            logger.error(f"Error in vectorized Greeks calculation: {e}")
            return self._calculate_fallback_greeks_exposure(ticker, options_data, current_price)

    def _calculate_fallback_greeks_exposure(self,
                                          ticker: str,
                                          options_data: pd.DataFrame,
                                          current_price: float) -> Dict[float, Dict[str, float]]:
        """Fallback Greeks calculation (original slower method)."""
        try:
            logger.debug(f"Using fallback Greeks calculation for {ticker}")
            greeks_exposure = {}

            # Group by strike price (original method)
            for strike in options_data['strike'].unique():
                strike_data = options_data[options_data['strike'] == strike]

                # Calculate total open interest at this strike
                total_oi = strike_data['open_interest'].sum()
                if total_oi < self.config['min_open_interest']:
                    continue

                # Initialize exposure tracking
                total_vanna = 0.0
                total_charm = 0.0
                total_delta = 0.0
                total_gamma = 0.0

                for _, option in strike_data.iterrows():
                    try:
                        # Get basic option parameters
                        option_type = option.get('option_type', 'call').lower()
                        dte = option.get('days_to_expiration', 30)
                        iv = option.get('implied_volatility', 0.2)
                        oi = option.get('open_interest', 0)

                        # Skip if not enough time or too much time
                        if dte < self.config['min_dte'] or dte > self.config['max_dte']:
                            continue

                        # Calculate Greeks using Enhanced Greeks or fallback
                        greeks = self._calculate_option_greeks(
                            spot=current_price,
                            strike=strike,
                            time_to_expiry=dte / 365.0,
                            volatility=iv,
                            option_type=option_type,
                            risk_free_rate=self.config['risk_free_rate']
                        )

                        # Weight by open interest
                        weighted_vanna = greeks['vanna'] * oi
                        weighted_charm = greeks['charm'] * oi
                        weighted_delta = greeks['delta'] * oi
                        weighted_gamma = greeks['gamma'] * oi

                        # Adjust for call/put direction
                        if option_type == 'put':
                            weighted_delta *= -1  # Puts have negative delta

                        total_vanna += weighted_vanna
                        total_charm += weighted_charm
                        total_delta += weighted_delta
                        total_gamma += weighted_gamma

                    except Exception as e:
                        logger.warning(f"Error calculating Greeks for {strike} strike: {e}")
                        continue

                # Store exposure data
                if abs(total_vanna) >= self.config['min_vanna_exposure'] or \
                   abs(total_charm) >= self.config['min_charm_exposure']:

                    greeks_exposure[strike] = {
                        'vanna': total_vanna,
                        'charm': total_charm,
                        'delta': total_delta,
                        'gamma': total_gamma,
                        'open_interest': total_oi,
                        'distance_from_spot': abs(strike - current_price) / current_price,
                        'in_the_money': self._calculate_proper_itm(strike, current_price, option_type, dte),
                        'time_weighted_charm': total_charm * self._time_decay_weight(dte)
                    }

            logger.debug(f"Calculated Greeks exposure for {len(greeks_exposure)} strikes")
            return greeks_exposure

        except Exception as e:
            logger.error(f"Error in fallback Greeks calculation: {e}")
            return {}

    def _calculate_option_greeks(self,
                               spot: float,
                               strike: float,
                               time_to_expiry: float,
                               volatility: float,
                               option_type: str,
                               risk_free_rate: float = 0.05) -> Dict[str, float]:
        """Calculate option Greeks including Vanna and Charm."""
        try:
            # Use Enhanced Greeks if available
            if self.enhanced_greeks_core and self.higher_order_greeks:
                try:
                    # Prepare option data for Enhanced Greeks
                    option_data = {
                        'underlying_price': spot,
                        'strike': strike,
                        'time_to_expiry': time_to_expiry,
                        'implied_volatility': volatility,
                        'option_type': option_type,
                        'risk_free_rate': risk_free_rate,
                        'dividend_yield': self.config['dividend_yield']
                    }

                    # Calculate using Enhanced Greeks
                    greeks_result = self.higher_order_greeks.calculate_higher_order_greeks(option_data)

                    return {
                        'delta': greeks_result.get('delta', 0.0),
                        'gamma': greeks_result.get('gamma', 0.0),
                        'vanna': greeks_result.get('vanna', 0.0),
                        'charm': greeks_result.get('charm', 0.0),
                        'volga': greeks_result.get('volga', 0.0),
                        'vomma': greeks_result.get('vomma', 0.0)
                    }
                except Exception as e:
                    logger.warning(f"Enhanced Greeks calculation failed: {e}, using fallback")

            # Fallback: Black-Scholes calculations
            return self._black_scholes_greeks(spot, strike, time_to_expiry, volatility, risk_free_rate, option_type)

        except Exception as e:
            logger.error(f"Error calculating option Greeks: {e}")
            return {'delta': 0.0, 'gamma': 0.0, 'vanna': 0.0, 'charm': 0.0, 'volga': 0.0, 'vomma': 0.0}

    def _black_scholes_greeks(self,
                            spot: float,
                            strike: float,
                            time_to_expiry: float,
                            volatility: float,
                            risk_free_rate: float,
                            option_type: str) -> Dict[str, float]:
        """Fallback Black-Scholes Greeks calculations."""
        try:
            if time_to_expiry <= 0:
                return {'delta': 0.0, 'gamma': 0.0, 'vanna': 0.0, 'charm': 0.0, 'volga': 0.0, 'vomma': 0.0}

            # Black-Scholes parameters
            d1 = (math.log(spot / strike) + (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / \
                 (volatility * math.sqrt(time_to_expiry))
            d2 = d1 - volatility * math.sqrt(time_to_expiry)

            # Standard normal distribution
            N_d1 = stats.norm.cdf(d1)
            N_d2 = stats.norm.cdf(d2)
            n_d1 = stats.norm.pdf(d1)  # Normal density

            sqrt_t = math.sqrt(time_to_expiry)

            # Basic Greeks
            if option_type.lower() == 'call':
                delta = N_d1
            else:
                delta = N_d1 - 1

            gamma = n_d1 / (spot * volatility * sqrt_t)

            # Higher-order Greeks
            vanna = -n_d1 * d2 / volatility

            if option_type.lower() == 'call':
                charm = -n_d1 * (2 * risk_free_rate * time_to_expiry - d2 * volatility * sqrt_t) / \
                        (2 * time_to_expiry * volatility * sqrt_t)
            else:
                charm = -n_d1 * (2 * risk_free_rate * time_to_expiry - d2 * volatility * sqrt_t) / \
                        (2 * time_to_expiry * volatility * sqrt_t) + risk_free_rate * math.exp(-risk_free_rate * time_to_expiry)

            volga = spot * n_d1 * sqrt_t * d1 * d2 / volatility
            vomma = volga  # Simplified

            return {
                'delta': delta,
                'gamma': gamma,
                'vanna': vanna,
                'charm': charm,
                'volga': volga,
                'vomma': vomma
            }

        except Exception as e:
            logger.error(f"Error in Black-Scholes Greeks calculation: {e}")
            return {'delta': 0.0, 'gamma': 0.0, 'vanna': 0.0, 'charm': 0.0, 'volga': 0.0, 'vomma': 0.0}

    def _analyze_strike_clustering(self,
                                 greeks_exposure: Dict[float, Dict[str, float]],
                                 current_price: float) -> Dict[str, Any]:
        """Analyze strike clustering for Vanna and Charm exposure."""
        try:
            clustering_analysis = {
                'significant_clusters': [],
                'vanna_clusters': [],
                'charm_clusters': [],
                'combined_clusters': [],
                'max_exposure_strike': None,
                'total_vanna_exposure': 0.0,
                'total_charm_exposure': 0.0
            }

            if not greeks_exposure:
                return clustering_analysis

            # Sort strikes by distance from current price
            strikes = sorted(greeks_exposure.keys())

            # Calculate total exposures
            total_vanna = sum(abs(data['vanna']) for data in greeks_exposure.values())
            total_charm = sum(abs(data['charm']) for data in greeks_exposure.values())

            clustering_analysis['total_vanna_exposure'] = total_vanna
            clustering_analysis['total_charm_exposure'] = total_charm

            # Identify Vanna clusters
            vanna_threshold = self.config['vanna_clustering_threshold']
            vanna_clusters = self._find_exposure_clusters(
                greeks_exposure, 'vanna', current_price, vanna_threshold
            )
            clustering_analysis['vanna_clusters'] = vanna_clusters

            # Identify Charm clusters
            charm_threshold = self.config['charm_clustering_threshold']
            charm_clusters = self._find_exposure_clusters(
                greeks_exposure, 'charm', current_price, charm_threshold
            )
            clustering_analysis['charm_clusters'] = charm_clusters

            # Find combined clusters (both Vanna and Charm significant)
            combined_clusters = []
            for strike, data in greeks_exposure.items():
                vanna_significant = abs(data['vanna']) >= self.config['min_vanna_exposure']
                charm_significant = abs(data['charm']) >= self.config['min_charm_exposure']

                if vanna_significant and charm_significant:
                    combined_exposure = abs(data['vanna']) + abs(data['charm']) * self.config['combined_exposure_multiplier']
                    combined_clusters.append({
                        'strike': strike,
                        'vanna': data['vanna'],
                        'charm': data['charm'],
                        'combined_exposure': combined_exposure,
                        'distance_from_spot': data['distance_from_spot'],
                        'open_interest': data['open_interest']
                    })

            # Sort combined clusters by exposure
            combined_clusters.sort(key=lambda x: x['combined_exposure'], reverse=True)
            clustering_analysis['combined_clusters'] = combined_clusters

            # Identify most significant clusters
            all_clusters = []

            for cluster in vanna_clusters[:3]:  # Top 3 Vanna clusters
                cluster['type'] = 'vanna'
                all_clusters.append(cluster)

            for cluster in charm_clusters[:3]:  # Top 3 Charm clusters
                cluster['type'] = 'charm'
                all_clusters.append(cluster)

            for cluster in combined_clusters[:2]:  # Top 2 combined clusters
                cluster['type'] = 'combined'
                all_clusters.append(cluster)

            # Filter for significant clusters
            significant_clusters = []
            for cluster in all_clusters:
                if cluster['type'] == 'vanna' and abs(cluster['vanna']) >= self.config['min_vanna_exposure']:
                    significant_clusters.append(cluster)
                elif cluster['type'] == 'charm' and abs(cluster['charm']) >= self.config['min_charm_exposure']:
                    significant_clusters.append(cluster)
                elif cluster['type'] == 'combined':
                    significant_clusters.append(cluster)

            clustering_analysis['significant_clusters'] = significant_clusters

            # Find maximum exposure strike
            max_exposure = 0
            max_strike = None
            for strike, data in greeks_exposure.items():
                total_exposure = abs(data['vanna']) + abs(data['charm'])
                if total_exposure > max_exposure:
                    max_exposure = total_exposure
                    max_strike = strike

            clustering_analysis['max_exposure_strike'] = max_strike

            logger.debug(f"Found {len(significant_clusters)} significant clusters")
            return clustering_analysis

        except Exception as e:
            logger.error(f"Error analyzing strike clustering: {e}")
            return {'significant_clusters': [], 'vanna_clusters': [], 'charm_clusters': [], 'combined_clusters': []}

    def _find_exposure_clusters(self,
                              greeks_exposure: Dict[float, Dict[str, float]],
                              greek_type: str,
                              current_price: float,
                              distance_threshold: float) -> List[Dict[str, Any]]:
        """Find clusters of Greeks exposure."""
        try:
            clusters = []
            strikes = sorted(greeks_exposure.keys())

            for strike in strikes:
                data = greeks_exposure[strike]
                exposure = abs(data[greek_type])

                if greek_type == 'vanna' and exposure >= self.config['min_vanna_exposure']:
                    clusters.append({
                        'strike': strike,
                        'vanna': data['vanna'],
                        'charm': data['charm'],
                        'exposure': exposure,
                        'distance_from_spot': data['distance_from_spot'],
                        'open_interest': data['open_interest']
                    })
                elif greek_type == 'charm' and exposure >= self.config['min_charm_exposure']:
                    clusters.append({
                        'strike': strike,
                        'vanna': data['vanna'],
                        'charm': data['charm'],
                        'exposure': exposure,
                        'distance_from_spot': data['distance_from_spot'],
                        'open_interest': data['open_interest']
                    })

            # Sort by exposure
            clusters.sort(key=lambda x: x['exposure'], reverse=True)
            return clusters

        except Exception as e:
            logger.error(f"Error finding exposure clusters: {e}")
            return []

    def _analyze_iv_conditions(self,
                             ticker: str,
                             options_data: pd.DataFrame,
                             current_price: float) -> Dict[str, Any]:
        """Analyze IV conditions for squeeze setup."""
        try:
            iv_analysis = {
                'favorable_conditions': False,
                'iv_skew': 0.0,
                'iv_percentile': 0.0,
                'term_structure_slope': 0.0,
                'elevated_iv': False,
                'skew_direction': 'neutral'
            }

            # Use IV Skew Analyzer if available
            if self.iv_skew_analyzer:
                try:
                    skew_result = self.iv_skew_analyzer.analyze(ticker, options_data.to_dict('records')[0])
                    if skew_result and 'iv_skew' in skew_result:
                        iv_analysis['iv_skew'] = abs(skew_result['iv_skew'])
                        iv_analysis['skew_direction'] = 'put' if skew_result['iv_skew'] > 0 else 'call'
                except Exception as e:
                    logger.warning(f"IV Skew Analyzer failed: {e}")

            # Fallback IV analysis
            if iv_analysis['iv_skew'] == 0.0:
                iv_analysis.update(self._calculate_iv_metrics(options_data, current_price))

            # Check favorable conditions
            conditions_met = 0
            total_conditions = 4

            # Condition 1: Sufficient IV skew
            if iv_analysis['iv_skew'] >= self.config['min_iv_skew']:
                conditions_met += 1

            # Condition 2: Elevated IV
            if iv_analysis['iv_percentile'] >= self.config['iv_percentile_threshold']:
                conditions_met += 1
                iv_analysis['elevated_iv'] = True

            # Condition 3: Favorable term structure
            if abs(iv_analysis['term_structure_slope']) >= self.config['term_structure_slope']:
                conditions_met += 1

            # Condition 4: Time conditions (checked separately)
            time_favorable = self._check_time_conditions(options_data)
            if time_favorable:
                conditions_met += 1

            # Need at least 2 out of 4 conditions
            iv_analysis['favorable_conditions'] = conditions_met >= 2

            logger.debug(f"IV conditions: {conditions_met}/{total_conditions} favorable")
            return iv_analysis

        except Exception as e:
            logger.error(f"Error analyzing IV conditions: {e}")
            return {'favorable_conditions': False}

    def _calculate_iv_metrics(self, options_data: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """Calculate IV metrics manually."""
        try:
            # Get ATM options
            options_data['moneyness'] = abs(options_data['strike'] - current_price) / current_price
            atm_options = options_data[options_data['moneyness'] <= 0.05]  # Within 5% of ATM

            if atm_options.empty:
                return {'iv_skew': 0.0, 'iv_percentile': 0.5, 'term_structure_slope': 0.0}

            # Calculate skew (put IV - call IV)
            calls = atm_options[atm_options['option_type'].str.lower() == 'call']
            puts = atm_options[atm_options['option_type'].str.lower() == 'put']

            call_iv = calls['implied_volatility'].mean() if not calls.empty else 0.2
            put_iv = puts['implied_volatility'].mean() if not puts.empty else 0.2

            iv_skew = put_iv - call_iv

            # Calculate IV percentile (simplified)
            all_iv = options_data['implied_volatility'].dropna()
            current_iv = atm_options['implied_volatility'].mean()
            iv_percentile = stats.percentileofscore(all_iv, current_iv) / 100.0 if len(all_iv) > 5 else 0.5

            # Calculate term structure slope
            short_term = options_data[options_data['days_to_expiration'] <= 30]['implied_volatility'].mean()
            long_term = options_data[options_data['days_to_expiration'] > 30]['implied_volatility'].mean()

            term_structure_slope = (long_term - short_term) if pd.notna(long_term) and pd.notna(short_term) else 0.0

            return {
                'iv_skew': iv_skew,
                'iv_percentile': iv_percentile,
                'term_structure_slope': term_structure_slope
            }

        except Exception as e:
            logger.error(f"Error calculating IV metrics: {e}")
            return {'iv_skew': 0.0, 'iv_percentile': 0.5, 'term_structure_slope': 0.0}

    def _check_time_conditions(self, options_data: pd.DataFrame) -> bool:
        """Check if time conditions are favorable for squeeze."""
        try:
            # Get expiration dates
            expiration_dates = options_data['days_to_expiration'].unique()

            # Check if we have options in the optimal time range
            optimal_dte = [dte for dte in expiration_dates
                          if self.config['min_dte'] <= dte <= self.config['max_dte']]

            return len(optimal_dte) > 0

        except Exception as e:
            logger.error(f"Error checking time conditions: {e}")
            return False

    def _detect_squeeze_conditions(self,
                                 clustering_analysis: Dict[str, Any],
                                 iv_analysis: Dict[str, Any],
                                 current_price: float) -> List[Dict[str, Any]]:
        """Detect different types of squeeze conditions."""
        try:
            squeeze_conditions = []

            for cluster in clustering_analysis['significant_clusters']:
                squeeze_type = self._classify_squeeze_type(cluster, iv_analysis)
                if squeeze_type:
                    squeeze_probability = self._calculate_squeeze_probability(cluster, iv_analysis, current_price)

                    if squeeze_probability >= self.config['breakout_probability_threshold']:
                        squeeze_conditions.append({
                            'type': squeeze_type,
                            'cluster': cluster,
                            'probability': squeeze_probability,
                            'direction_bias': self._determine_direction_bias(cluster, current_price),
                            'intensity': self._calculate_squeeze_intensity(cluster),
                            'iv_conditions': iv_analysis
                        })

            # Sort by probability
            squeeze_conditions.sort(key=lambda x: x['probability'], reverse=True)

            logger.debug(f"Detected {len(squeeze_conditions)} squeeze conditions")
            return squeeze_conditions

        except Exception as e:
            logger.error(f"Error detecting squeeze conditions: {e}")
            return []

    def _classify_squeeze_type(self, cluster: Dict[str, Any], iv_analysis: Dict[str, Any]) -> Optional[str]:
        """Classify the type of squeeze based on cluster characteristics."""
        try:
            vanna_exposure = abs(cluster.get('vanna', 0))
            charm_exposure = abs(cluster.get('charm', 0))

            # Vanna Pin Risk
            if vanna_exposure >= self.config['vanna_pin_threshold']:
                return 'vanna_pin'

            # Charm Acceleration
            elif charm_exposure >= self.config['charm_acceleration_threshold']:
                return 'charm_acceleration'

            # Combined Effects
            elif cluster.get('type') == 'combined':
                combined_exposure = cluster.get('combined_exposure', 0)
                if combined_exposure >= (self.config['vanna_pin_threshold'] + self.config['charm_acceleration_threshold']) * 0.75:
                    return 'combined_squeeze'

            # Volatility Explosion (high IV + significant exposure)
            elif iv_analysis.get('elevated_iv', False) and (vanna_exposure + charm_exposure) >= 75000:
                return 'volatility_explosion'

            return None

        except Exception as e:
            logger.error(f"Error classifying squeeze type: {e}")
            return None

    def _calculate_squeeze_probability(self,
                                     cluster: Dict[str, Any],
                                     iv_analysis: Dict[str, Any],
                                     current_price: float) -> float:
        """Calculate probability of squeeze occurrence."""
        try:
            base_probability = 0.5

            # Factor 1: Exposure magnitude
            total_exposure = abs(cluster.get('vanna', 0)) + abs(cluster.get('charm', 0))
            exposure_factor = min(0.3, total_exposure / 200000)

            # Factor 2: Distance from spot
            distance_factor = max(0, 0.2 - cluster.get('distance_from_spot', 0.1) * 2)

            # Factor 3: Open interest concentration
            oi_factor = min(0.15, cluster.get('open_interest', 0) / 10000)

            # Factor 4: IV conditions
            iv_factor = 0.1 if iv_analysis.get('favorable_conditions', False) else 0

            # Factor 5: Time decay acceleration (for charm)
            time_factor = 0.1 if abs(cluster.get('charm', 0)) >= self.config['charm_acceleration_threshold'] else 0

            probability = base_probability + exposure_factor + distance_factor + oi_factor + iv_factor + time_factor

            return min(0.95, probability)

        except Exception as e:
            logger.error(f"Error calculating squeeze probability: {e}")
            return 0.5

    def _determine_direction_bias(self, cluster: Dict[str, Any], current_price: float) -> str:
        """Determine directional bias for the squeeze."""
        try:
            strike = cluster.get('strike', current_price)
            vanna = cluster.get('vanna', 0)
            charm = cluster.get('charm', 0)

            # Price position relative to strike
            above_strike = current_price > strike
            below_strike = current_price < strike

            # Vanna direction analysis
            if abs(vanna) > abs(charm):
                # Vanna dominant - consider pin risk vs breakout
                if above_strike and vanna > 0:
                    return 'bearish'  # Negative gamma pull
                elif below_strike and vanna < 0:
                    return 'bullish'  # Positive gamma push
                else:
                    return 'neutral'

            # Charm dominant - time decay acceleration
            elif abs(charm) > abs(vanna):
                if charm > 0:
                    return 'bullish'  # Positive time decay momentum
                else:
                    return 'bearish'  # Negative time decay momentum

            return 'neutral'

        except Exception as e:
            logger.error(f"Error determining direction bias: {e}")
            return 'neutral'

    def _calculate_squeeze_intensity(self, cluster: Dict[str, Any]) -> str:
        """Calculate the intensity of the squeeze."""
        try:
            total_exposure = abs(cluster.get('vanna', 0)) + abs(cluster.get('charm', 0))

            for intensity, (min_exp, max_exp, _) in self.config['squeeze_intensity_scaling'].items():
                if min_exp <= total_exposure <= max_exp:
                    return intensity

            return 'extreme' if total_exposure > 300000 else 'low'

        except Exception as e:
            logger.error(f"Error calculating squeeze intensity: {e}")
            return 'medium'

    def _confirm_momentum_conditions(self,
                                   ticker: str,
                                   data: Dict[str, Any],
                                   analysis_results: Dict[str, Any]) -> bool:
        """Confirm momentum conditions for squeeze entry."""
        try:
            if not self.config['momentum_confirmation_required']:
                return True

            # Check flow velocity if flow physics integrator available
            if self.flow_physics_adapter:
                try:
                    # Create flow data point
                    flow_point = {
                        'timestamp': pd.Timestamp.now(),
                        'flow_value': data.get('volume', 1000) * 0.001  # Simplified flow calculation
                    }

                    flow_result = self.flow_physics_adapter.analyze(ticker, {}, current_price)
                    if hasattr(flow_result, 'flow_velocity'):
                        flow_velocity = abs(flow_result.flow_velocity)
                        if flow_velocity >= self.config['min_flow_velocity']:
                            return True
                except Exception as e:
                    logger.warning(f"Flow physics analysis failed: {e}")

            # Check volume surge
            current_volume = data.get('volume', 0)
            avg_volume = data.get('avg_volume', current_volume)

            if avg_volume > 0:
                volume_ratio = current_volume / avg_volume
                if volume_ratio >= self.config['volume_surge_threshold']:
                    return True

            # Default to allowing if momentum check fails
            return True

        except Exception as e:
            logger.warning(f"Error confirming momentum conditions: {e}")
            return True

    def _generate_squeeze_signals(self,
                                ticker: str,
                                current_price: float,
                                squeeze_info: Dict[str, Any],
                                momentum_confirmed: bool,
                                data: Dict[str, Any]) -> List[StrategySignal]:
        """Generate trading signals for detected squeeze conditions."""
        signals = []

        try:
            squeeze_type = squeeze_info['type']
            cluster = squeeze_info['cluster']
            direction_bias = squeeze_info['direction_bias']
            probability = squeeze_info['probability']
            intensity = squeeze_info['intensity']

            # Skip if momentum not confirmed and required
            if self.config['momentum_confirmation_required'] and not momentum_confirmed:
                return signals

            # Determine entry direction
            if direction_bias == 'neutral':
                # For neutral bias, create both long and short signals with lower confidence
                directions = [SignalDirection.LONG, SignalDirection.SHORT]
                confidence_adjustment = 0.8
            elif direction_bias == 'bullish':
                directions = [SignalDirection.LONG]
                confidence_adjustment = 1.0
            else:  # bearish
                directions = [SignalDirection.SHORT]
                confidence_adjustment = 1.0

            for direction in directions:
                signal = self._create_squeeze_signal(
                    ticker=ticker,
                    direction=direction,
                    current_price=current_price,
                    squeeze_info=squeeze_info,
                    confidence_adjustment=confidence_adjustment,
                    data=data
                )

                if signal:
                    signals.append(signal)

        except Exception as e:
            logger.error(f"Error generating squeeze signals: {e}")

        return signals

    def _create_squeeze_signal(self,
                             ticker: str,
                             direction: SignalDirection,
                             current_price: float,
                             squeeze_info: Dict[str, Any],
                             confidence_adjustment: float,
                             data: Dict[str, Any]) -> Optional[StrategySignal]:
        """Create a trading signal for the squeeze opportunity."""
        try:
            squeeze_type = squeeze_info['type']
            cluster = squeeze_info['cluster']
            probability = squeeze_info['probability']
            intensity = squeeze_info['intensity']

            entry = current_price

            # Calculate position size based on intensity
            intensity_config = self.config['squeeze_intensity_scaling'].get(intensity, (0, 100000, 0.6))
            position_multiplier = intensity_config[2] * self.config['base_position_size']

            # Calculate stop loss based on squeeze type
            atr = data.get('atr', current_price * 0.02)

            if squeeze_type == 'vanna_pin':
                stop_multiplier = self.config['vanna_pin_stop_multiplier']
            elif squeeze_type == 'charm_acceleration':
                stop_multiplier = self.config['acceleration_stop_multiplier']
            else:
                stop_multiplier = 1.0

            adjusted_atr = atr * stop_multiplier
            stop_loss = self.calculate_stop_loss(entry, direction, atr=adjusted_atr)

            # Calculate take profit based on squeeze type
            profit_target_pct = self.config['profit_targets'].get(squeeze_type, 0.04)

            if direction == SignalDirection.LONG:
                take_profit = entry * (1 + profit_target_pct)
            else:
                take_profit = entry * (1 - profit_target_pct)

            # Calculate confidence
            base_confidence = min(0.95, probability * confidence_adjustment)

            # Adjust confidence based on momentum confirmation
            if squeeze_info.get('momentum_confirmed', True):
                base_confidence = min(0.95, base_confidence + 0.05)

            confidence = max(self.config['min_confidence'], base_confidence)

            # Create detailed reason
            vanna_exposure = abs(cluster.get('vanna', 0))
            charm_exposure = abs(cluster.get('charm', 0))
            strike = cluster.get('strike', current_price)

            reason = (
                f"{squeeze_type.replace('_', ' ').title()} squeeze: "
                f"Vanna {vanna_exposure:,.0f}, Charm {charm_exposure:,.0f} "
                f"at ${strike:.2f} strike, {intensity} intensity, "
                f"{probability:.1%} probability"
            )

            # Create analysis details
            analysis = {
                'component': 'vanna_charm_squeeze',
                'squeeze_info': {
                    'type': squeeze_type,
                    'intensity': intensity,
                    'probability': probability,
                    'strike': strike,
                    'vanna_exposure': vanna_exposure,
                    'charm_exposure': charm_exposure,
                    'direction_bias': squeeze_info['direction_bias']
                },
                'position_sizing': {
                    'multiplier': position_multiplier,
                    'intensity_based': True
                },
                'risk_management': {
                    'stop_multiplier': stop_multiplier,
                    'profit_target_pct': profit_target_pct,
                    'squeeze_type_specific': True
                },
                'exit_strategy': {
                    'time_decay_exit': self.config['time_decay_exit'],
                    'iv_crush_protection': self.config['iv_crush_protection']
                }
            }

            return self.create_signal(
                ticker=ticker,
                direction=direction,
                entry=entry,
                stop_loss=stop_loss,
                take_profit=take_profit,
                confidence=confidence,
                reason=reason,
                analysis=analysis
            )

        except Exception as e:
            logger.error(f"Error creating squeeze signal: {e}")
            return None

    def _calculate_proper_itm(self, strike: float, current_price: float, option_type: str, dte: int) -> bool:
        """Calculate proper ITM status considering option type and time decay."""
        if option_type.lower() in ['call', 'c']:
            # For calls: ITM when strike < current price
            return strike < current_price
        elif option_type.lower() in ['put', 'p']:
            # For puts: ITM when strike > current price
            return strike > current_price
        else:
            # Default to call behavior if type unknown
            return strike < current_price
    
    def _time_decay_weight(self, dte: int) -> float:
        """Calculate proper time decay weight using theta decay curve."""
        if dte <= 0:
            return 0.0
        
        # Use proper exponential decay model for options time value
        # Theta acceleration increases as expiration approaches
        normalized_time = min(dte / 365.0, 1.0)  # Normalize to yearly
        
        # Model theta acceleration: weight increases as expiration nears
        # Using exponential decay: e^(-lambda * t) where lambda controls decay rate
        decay_rate = 2.0  # Aggressive decay for charm calculations
        weight = np.exp(-decay_rate * normalized_time)
        
        # Ensure weight is between 0 and 1
        return max(0.0, min(1.0, weight))

    def validate_data(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate required data for Vanna/Charm squeeze analysis."""
        required_fields = ['current_price']

        for field in required_fields:
            if field not in data:
                return False, f"Missing required field: {field}"

        # Check if we have options data or can fetch it
        if 'options_data' not in data or data['options_data'] is None or data['options_data'].empty:
            logger.debug("No options_data in input, will attempt to fetch from API")

        return True, ""

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current configuration."""
        return {
            'name': 'Vanna/Charm Squeeze Strategy',
            'description': 'Exploit higher-order Greeks positioning for explosive moves',
            'version': '1.0.0',
            'config': self.config,
            'required_components': [
                'Enhanced Greeks Core',
                'Higher Order Greeks Physics',
                'IV Skew Analyzer',
                'Options Chain Analyzer'
            ],
            'squeeze_types': [
                'Vanna Pin Risk',
                'Charm Acceleration',
                'Combined Effects',
                'Volatility Explosion'
            ],
            'integration_points': [
                'Enhanced Greeks infrastructure',
                'IV Skew Analysis (setup confirmation)',
                'Options Chain Analysis (strike clustering)',
                'Flow Physics (momentum confirmation)',
                'Unified API Gateway (real-time data)'
            ],
            'risk_characteristics': {
                'typical_hold_time': '1-3 days',
                'win_rate_target': '70-80%',
                'risk_reward_ratio': '1:2 to 1:4',
                'max_drawdown_per_trade': '3-5%',
                'specialty': 'Options Greeks exploitation'
            },
            'performance_expectations': {
                'best_market_conditions': 'High IV, strong directional bias',
                'typical_profit_targets': '2-8% depending on squeeze type',
                'optimal_time_frame': '1-45 days to expiration',
                'minimum_requirements': 'Significant Greeks exposure + clustering'
            }
        }
