{"validation_timestamp": "2025-06-24T18:21:35.044121", "system_metrics": {"root_files_count": 69, "python_scripts_count": 17, "archived_files_count": 177, "optimization_ratio": "177/246"}, "core_file_validation": {"missing_files": ["orchestrator.py"], "present_files": ["main.py", "ultimate_orchestrator.py", "live_market_test.py", "enhanced_data_agent_broker_integration.py", "agent_zero_integration_hub.py", "agent_zero_advanced_capabilities.py", "agent_zero_performance_analytics.py", "test_greek_features.py", "test_core_system.py", "comprehensive_test.py", "README.md", "SYSTEM_ARCHITECTURE.md", "COMPLETE_SYSTEM_DOCUMENTATION.md", "settings.yml", "requirements.txt", ".env.example", "pyproject.toml"], "categories_status": {"entry_points": {"present": 4, "missing": 0, "files_present": ["main.py", "ultimate_orchestrator.py", "live_market_test.py", "enhanced_data_agent_broker_integration.py"], "files_missing": []}, "agent_zero_integration": {"present": 3, "missing": 0, "files_present": ["agent_zero_integration_hub.py", "agent_zero_advanced_capabilities.py", "agent_zero_performance_analytics.py"], "files_missing": []}, "orchestration": {"present": 0, "missing": 1, "files_present": [], "files_missing": ["orchestrator.py"]}, "essential_testing": {"present": 3, "missing": 0, "files_present": ["test_greek_features.py", "test_core_system.py", "comprehensive_test.py"], "files_missing": []}, "documentation": {"present": 3, "missing": 0, "files_present": ["README.md", "SYSTEM_ARCHITECTURE.md", "COMPLETE_SYSTEM_DOCUMENTATION.md"], "files_missing": []}, "configuration": {"present": 4, "missing": 0, "files_present": ["settings.yml", "requirements.txt", ".env.example", "pyproject.toml"], "files_missing": []}}}, "potential_duplicates": [], "directory_structure": {".github": {"total_files": 1, "subdirectories": ["workflows"], "file_types": {".yml": 1}}, ".pytest_cache": {"total_files": 6, "subdirectories": ["v"], "file_types": {"": 4, ".TAG": 1, ".md": 1}}, "agents": {"total_files": 59, "subdirectories": ["accumulation_distribution_detector", "agent_orchestrator", "breakout_validation_specialist", "options_flow_decoder", "__pycache__"], "file_types": {".py": 30, ".md": 1, ".pyc": 28}}, "agent_docs": {"total_files": 10, "subdirectories": ["instructions", "standards", "tasks", "workflows"], "file_types": {".md": 9, ".json": 1}}, "agent_templates": {"total_files": 1, "subdirectories": [], "file_types": {".py": 1}}, "agent_training": {"total_files": 0, "subdirectories": [], "file_types": {}}, "agent_workspace": {"total_files": 6, "subdirectories": ["auto_broker_adapter", "chart_gen_001", "flow_physics_agent", "fvg_specialist", "greek_anomaly_agent", "greek_enhancement_agent", "iv_dynamics_agent", "live_data_gateway_agent", "manual_broker_adapter_agent", "math_validator_07a3bf70", "math_validator_12c25de8", "math_validator_1392b5bd", "math_validator_2d26dbf3", "math_validator_2e52e07e", "math_validator_563af9a0", "math_validator_674cbcfe", "math_validator_717538ee", "math_validator_71bcbdf1", "math_validator_71e1b9a6", "math_validator_88cbeb68", "math_validator_a1ba0d9b", "math_validator_a35c1dc6", "math_validator_b37f5dcb", "math_validator_c007df87", "math_validator_da4d3e04", "math_validator_dca59161", "math_validator_e483b531", "math_validator_e769fe51", "math_validator_e8984ba4", "math_validator_ea7f3bd5", "math_validator_f0714e81", "math_validator_f8fbab8d", "mean_reversion_specialist", "output_coordinator_0627d7a9", "output_coordinator_06b89e09", "output_coordinator_06dc3400", "output_coordinator_072f1a2c", "output_coordinator_07b03d95", "output_coordinator_09192712", "output_coordinator_09451ab7", "output_coordinator_0989187a", "output_coordinator_09a0dcc1", "output_coordinator_09bae350", "output_coordinator_09d55b68", "output_coordinator_0a527d00", "output_coordinator_0cb456a8", "output_coordinator_0cdfb088", "output_coordinator_0ce1f729", "output_coordinator_0d0dc2a2", "output_coordinator_0d41a172", "output_coordinator_0daa9cb7", "output_coordinator_1028ce6b", "output_coordinator_11c791c8", "output_coordinator_1207a4f1", "output_coordinator_146f0285", "output_coordinator_165b7e0f", "output_coordinator_17aa4cff", "output_coordinator_18f46df9", "output_coordinator_18f4cd0e", "output_coordinator_1a2c84a3", "output_coordinator_1a3c7c9d", "output_coordinator_1a80b36c", "output_coordinator_1aba7312", "output_coordinator_1dca9a7f", "output_coordinator_1eae31a4", "output_coordinator_208ed1bf", "output_coordinator_21512139", "output_coordinator_21c300b0", "output_coordinator_229326ff", "output_coordinator_244ec2a8", "output_coordinator_247f7ede", "output_coordinator_24cd1f8f", "output_coordinator_2593279d", "output_coordinator_25c4c565", "output_coordinator_263cf608", "output_coordinator_270743d4", "output_coordinator_27738d18", "output_coordinator_27bcc75c", "output_coordinator_29e012c9", "output_coordinator_29e24576", "output_coordinator_2a1d9ae6", "output_coordinator_2a4e813a", "output_coordinator_2af8cbb9", "output_coordinator_2c1554fc", "output_coordinator_2dbc2ac5", "output_coordinator_30235ee8", "output_coordinator_337edbdf", "output_coordinator_34e84e87", "output_coordinator_353699e2", "output_coordinator_372f2c34", "output_coordinator_374c49a4", "output_coordinator_37c99204", "output_coordinator_38ce2600", "output_coordinator_390e5842", "output_coordinator_3a17d09e", "output_coordinator_3a359ab0", "output_coordinator_3a4c8ac2", "output_coordinator_3b4b030c", "output_coordinator_3c656837", "output_coordinator_3ccc7935", "output_coordinator_3d13cc07", "output_coordinator_3d471343", "output_coordinator_3de8bc82", "output_coordinator_43922212", "output_coordinator_451d1672", "output_coordinator_478abc43", "output_coordinator_484c6a51", "output_coordinator_48537f9a", "output_coordinator_49c83161", "output_coordinator_4a4a7933", "output_coordinator_4c0bd940", "output_coordinator_4e8a09b6", "output_coordinator_4ee91332", "output_coordinator_50b50241", "output_coordinator_5188f1ca", "output_coordinator_51f5de8e", "output_coordinator_52ab5b00", "output_coordinator_536eff6f", "output_coordinator_53d22ee0", "output_coordinator_53db8d2b", "output_coordinator_54c868b2", "output_coordinator_57e4fc17", "output_coordinator_58e5c341", "output_coordinator_593e2093", "output_coordinator_594e1e83", "output_coordinator_59f5b11f", "output_coordinator_5b6b528c", "output_coordinator_5eb5b6a8", "output_coordinator_60056ec3", "output_coordinator_612958d7", "output_coordinator_62d10b94", "output_coordinator_6506c658", "output_coordinator_66498826", "output_coordinator_679df920", "output_coordinator_6a6d002e", "output_coordinator_6b99fdc9", "output_coordinator_6f60e86c", "output_coordinator_6fc08ae0", "output_coordinator_71108665", "output_coordinator_73ddcde6", "output_coordinator_73f0717c", "output_coordinator_754a67b9", "output_coordinator_760f5367", "output_coordinator_76e7b5b1", "output_coordinator_7722187d", "output_coordinator_786e8aa7", "output_coordinator_78fa3386", "output_coordinator_7c9115e3", "output_coordinator_7d970dc0", "output_coordinator_7fa86eb1", "output_coordinator_84cb9d8d", "output_coordinator_85185c9f", "output_coordinator_86179cc6", "output_coordinator_87144ebe", "output_coordinator_880ab727", "output_coordinator_88b7c004", "output_coordinator_8919e2bb", "output_coordinator_89e642ec", "output_coordinator_8aab9077", "output_coordinator_8b1b21ec", "output_coordinator_8e6e2b4f", "output_coordinator_8f652256", "output_coordinator_8f98e23b", "output_coordinator_90988be3", "output_coordinator_90b26a97", "output_coordinator_93ae9c60", "output_coordinator_93cba729", "output_coordinator_949dc37d", "output_coordinator_973a9e2b", "output_coordinator_973e0ddd", "output_coordinator_97589433", "output_coordinator_980d6254", "output_coordinator_98bdc293", "output_coordinator_9a8937d8", "output_coordinator_9bef209f", "output_coordinator_9daa9a2f", "output_coordinator_a009b113", "output_coordinator_a105fd97", "output_coordinator_a148c4af", "output_coordinator_a5ddef8f", "output_coordinator_a7bc2449", "output_coordinator_a7f4faf8", "output_coordinator_a8494af1", "output_coordinator_aa02fcfe", "output_coordinator_aa3000a7", "output_coordinator_a<PERSON><PERSON><PERSON>e", "output_coordinator_ac3c518b", "output_coordinator_ae69a62d", "output_coordinator_af60c57c", "output_coordinator_af8864b3", "output_coordinator_agent", "output_coordinator_b02c63ab", "output_coordinator_b03aca6b", "output_coordinator_b1b09533", "output_coordinator_b288d554", "output_coordinator_b2ca12ed", "output_coordinator_b3972554", "output_coordinator_b42e346c", "output_coordinator_b455a1de", "output_coordinator_b81d91cf", "output_coordinator_b83b9df2", "output_coordinator_b8504533", "output_coordinator_b9977ef1", "output_coordinator_ba1b275e", "output_coordinator_ba5d6be1", "output_coordinator_bb042ba6", "output_coordinator_b<PERSON><PERSON>da", "output_coordinator_bc0dc4e4", "output_coordinator_bdac1ff0", "output_coordinator_bf066a78", "output_coordinator_c0398ee6", "output_coordinator_c12ff42e", "output_coordinator_c141a7a7", "output_coordinator_c1919c02", "output_coordinator_c2ea6d3b", "output_coordinator_c395e314", "output_coordinator_c42e2585", "output_coordinator_c4320e98", "output_coordinator_c5a96cbb", "output_coordinator_c5ff046c", "output_coordinator_c722044f", "output_coordinator_c7756358", "output_coordinator_c7bfacfd", "output_coordinator_c8075b57", "output_coordinator_c87c6feb", "output_coordinator_c88fed3f", "output_coordinator_c895e93d", "output_coordinator_c96ff7b3", "output_coordinator_ca8820ab", "output_coordinator_cb47b182", "output_coordinator_cd38df83", "output_coordinator_ce58570a", "output_coordinator_cea6d919", "output_coordinator_cf4640bd", "output_coordinator_cf672dca", "output_coordinator_cf7dbc78", "output_coordinator_d04e2b74", "output_coordinator_d0d3833d", "output_coordinator_d3d1034c", "output_coordinator_d6196451", "output_coordinator_d638e0ba", "output_coordinator_d8f26c49", "output_coordinator_db5904e2", "output_coordinator_db5e7bb0", "output_coordinator_de591c1d", "output_coordinator_e2363c2a", "output_coordinator_e2e7de30", "output_coordinator_e3088ad6", "output_coordinator_e8d85064", "output_coordinator_e9683868", "output_coordinator_e97e1b8c", "output_coordinator_ebc1344d", "output_coordinator_ebecaf65", "output_coordinator_ef64fd0e", "output_coordinator_f0202585", "output_coordinator_f0dbe479", "output_coordinator_f24ea28b", "output_coordinator_f2c02675", "output_coordinator_f2d301fe", "output_coordinator_f65b1f2d", "output_coordinator_f68fc39e", "output_coordinator_f7277fb7", "output_coordinator_fc3d31f1", "output_coordinator_fc938e3a", "output_coordinator_fcd33a6c", "output_coordinator_fce97b8d", "output_coordinator_fd1a3da4", "output_coordinator_fd836065", "output_coordinator_fe5b6186", "output_coordinator_fee37993", "pivot_point_specialist", "risk_guard_agent", "signal_convergence_orchestrator", "signal_generator_agent", "signal_quality_0d0eb799", "signal_quality_5328b6b7", "signal_quality_5338f653", "signal_quality_8e870541", "signal_quality_c02ef81e", "signal_quality_dba37afb", "test_agent", "test_agent_001", "test_chart_agent", "trading_system_pipeline"], "file_types": {".png": 6}}, "analyzers": {"total_files": 10, "subdirectories": ["flow_physics", "__pycache__"], "file_types": {".py": 5, ".pyc": 5}}, "api": {"total_files": 85, "subdirectories": ["logs", "mcp_installation", "migration_backups", "modules", "tests", "_archive", "__pycache__"], "file_types": {".py": 44, ".txt": 3, ".md": 3, ".json": 6, ".log": 4, ".bat": 3, ".ps1": 1, ".pyc": 21}}, "archive": {"total_files": 177, "subdirectories": ["completed_tasks", "debug_files", "final_cleanup", "misc_scripts", "old_documentation", "old_tests", "old_versions"], "file_types": {".md": 29, ".py": 114, ".ini": 11, ".sh": 5, ".bat": 4, ".ps1": 1, ".json": 13}}, "ci": {"total_files": 9, "subdirectories": [], "file_types": {".py": 9}}, "config": {"total_files": 8, "subdirectories": ["__pycache__"], "file_types": {".py": 3, ".json": 2, ".pyc": 3}}, "contracts": {"total_files": 15, "subdirectories": [], "file_types": {".yml": 15}}, "custom_data": {"total_files": 10, "subdirectories": ["2025-06-24"], "file_types": {".json": 5, ".parquet": 5}}, "dashboards": {"total_files": 3, "subdirectories": ["__pycache__"], "file_types": {".py": 2, ".pyc": 1}}, "data": {"total_files": 31, "subdirectories": ["features", "greeks_cache", "history", "live", "__pycache__"], "file_types": {".py": 4, ".parquet": 16, ".pyc": 4, ".json": 7}}, "docs": {"total_files": 3, "subdirectories": [], "file_types": {".md": 3}}, "engine": {"total_files": 6, "subdirectories": ["__pycache__"], "file_types": {".py": 3, ".pyc": 3}}, "fills": {"total_files": 1, "subdirectories": ["test_fills"], "file_types": {".json": 1}}, "flowphysics": {"total_files": 4, "subdirectories": ["__pycache__"], "file_types": {".py": 2, ".pyc": 2}}, "flow_phys": {"total_files": 2, "subdirectories": ["2025-06-15"], "file_types": {".json": 2}}, "Flow_Physics_Engine": {"total_files": 34, "subdirectories": ["advanced", "api", "api_robustness", "docs", "engine_core", "tests", "__pycache__"], "file_types": {".py": 20, ".md": 1, ".pyc": 13}}, "greeks": {"total_files": 12, "subdirectories": ["__pycache__"], "file_types": {".py": 6, ".pyc": 6}}, "guarded": {"total_files": 6, "subdirectories": ["2025-06-14"], "file_types": {".md": 3, ".json": 3}}, "live_test_results": {"total_files": 14, "subdirectories": [], "file_types": {".json": 14}}, "logs": {"total_files": 1, "subdirectories": [], "file_types": {".log": 1}}, "migration_backups": {"total_files": 0, "subdirectories": ["backup_1750638053"], "file_types": {}}, "ml": {"total_files": 94, "subdirectories": ["ml"], "file_types": {".py": 76, ".css": 2, ".append": 1, ".json": 2, ".txt": 1, ".png": 2, ".pkl": 2, ".pt": 2, ".tensorflow_bak": 1, ".pyc": 5}}, "models": {"total_files": 0, "subdirectories": [], "file_types": {}}, "output": {"total_files": 3, "subdirectories": ["coordination"], "file_types": {".md": 1, ".csv": 1, ".json": 1}}, "outputs": {"total_files": 18, "subdirectories": ["2025-06-14", "AAPL", "MSFT", "NVDA", "TSLA"], "file_types": {".json": 13, ".md": 5}}, "reports": {"total_files": 0, "subdirectories": [], "file_types": {}}, "risk_state": {"total_files": 0, "subdirectories": [], "file_types": {}}, "schemas": {"total_files": 3, "subdirectories": [], "file_types": {".json": 3}}, "tasks": {"total_files": 15, "subdirectories": ["__pycache__"], "file_types": {".py": 8, ".pyc": 7}}, "temp": {"total_files": 0, "subdirectories": ["20250624"], "file_types": {}}, "testing": {"total_files": 0, "subdirectories": ["temp"], "file_types": {}}, "tests": {"total_files": 8, "subdirectories": ["fixtures", "__pycache__"], "file_types": {".png": 1, ".pyc": 7}}, "test_data": {"total_files": 10, "subdirectories": ["2025-06-24"], "file_types": {".json": 5, ".parquet": 5}}, "tickets": {"total_files": 4, "subdirectories": ["2025-06-14", "2025-12-25"], "file_types": {".txt": 4}}, "training_logs": {"total_files": 0, "subdirectories": [], "file_types": {}}, "utils": {"total_files": 16, "subdirectories": ["__pycache__"], "file_types": {".py": 8, ".pyc": 8}}, "__pycache__": {"total_files": 23, "subdirectories": [], "file_types": {".pyc": 23}}}, "file_categories": {"python_scripts": ["agent_zero_advanced_capabilities.py", "agent_zero_integration_hub.py", "agent_zero_performance_analytics.py", "analyze_system_usage.py", "cleanup_test_files.py", "comprehensive_test.py", "enhanced_accumulation_distribution_agent_zero.py", "enhanced_data_agent_broker_integration.py", "final_cleanup.py", "final_system_validation.py", "live_market_test.py", "live_test_checklist.py", "main.py", "system_cleanup.py", "test_core_system.py", "test_greek_features.py", "ultimate_orchestrator.py"], "documentation": ["AGENT_DEVELOPMENT_PROGRESS.md", "AGENT_DEVELOPMENT_SUMMARY.md", "AGENT_HANDOFF_GUIDE.md", "AGENT_IMPLEMENTATION_STATUS.md", "AGENT_STANDARDS_IMPLEMENTATION_SUMMARY.md", "AGENT_STANDARDS_TICKER_AGNOSTICISM.md", "AGENT_SYSTEM_ARCHITECTURE.md", "AGENT_ZERO_ML_INTEGRATION_CORRECTED.md", "AI_AGENT_TRAINING_PIPELINE.md", "AUTO_BROKER_SETUP.md", "B01_INTEGRATION_SUMMARY.md", "B01_TICKER_AGNOSTIC_VALIDATION.md", "COMPLETE_SYSTEM_DOCUMENTATION.md", "CSID_OPTIMIZATION_FRAMEWORK.md", "CSID_SWING_TRADING_OPTIMIZATION.md", "CURRENT_ENHANCED_STATUS.md", "CURRENT_STATUS.md", "DEPLOYMENT_READY.md", "DEPLOYMENT_STATUS.md", "Directory Structure.md", "ENHANCEMENT_COMPLETION_REPORT.md", "EVALUATION_FINAL_SUMMARY.md", "FILES_UPDATED_STATUS_REPORT.md", "FINAL_DEPLOYMENT_STATUS.md", "FINAL_HANDOFF_STATUS.md", "GIT_SAFETY_SETUP.md", "HANDOFF_IMPLEMENTATION_PACKAGE.md", "MATHEMATICAL_VALIDATOR_COMPLETION_REPORT.md", "MCP_INTEGRATION.md", "PRODUCTION_ASSESSMENT.md", "PROFILE_SYSTEM_SETUP.md", "README.md", "REAL_TIME_ENHANCEMENT_STATUS.md", "requirements.txt", "requirements_lock.txt", "SCHWAB_MCP_AGENT_INTEGRATION_CHECKLIST.md", "SCHWAB_NEXT_AGENT_INSTRUCTIONS.md", "SYSTEM_ARCHITECTURE.md", "SYSTEM_CLEANUP_REPORT.md", "SYSTEM_STATUS_VALIDATION.md", "TRADING_SYSTEM_ARCHITECTURE.md", "TRADING_SYSTEM_INTEGRATION_UPDATE.md"], "configuration": [".pre-commit-config.yaml", "cleanup_report.json", "final_validation_report.json", "settings.yml", "system_analysis_report.json"], "data_files": [".env", ".env.example", ".giti<PERSON>re", "pyproject.toml", "run_core_win.ps1"], "archived_files": 177}, "optimization_status": "INCOMPLETE", "recommendations": [{"priority": "HIGH", "action": "Restore missing core files", "details": ["orchestrator.py"]}, {"priority": "LOW", "action": "Consider further cleanup", "details": "Still have 17 Python files in root"}]}