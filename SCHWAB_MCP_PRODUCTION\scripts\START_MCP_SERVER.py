#!/usr/bin/env python3
"""
Schwab MCP Production Startup Script
Single command to launch production MCP server
"""

import os
import sys
import subprocess
import logging
import time
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Launch Schwab MCP Server in production mode"""
    
    print("="*60)
    print("SCHWAB MCP PRODUCTION SERVER")
    print("="*60)
    print("Status: READY TO REPLACE POLYGON.IO")
    print("Target: AI Agent Integration")
    print("Mode: Production Deployment")
    print("="*60)
    
    # Get script directory
    script_dir = Path(__file__).parent.parent
    core_dir = script_dir / "core"
    mcp_server_path = core_dir / "schwab_mcp_server.py"
    
    # Validate files exist
    if not mcp_server_path.exists():
        logger.error(f"MCP server not found: {mcp_server_path}")
        return False
    
    # Launch MCP server
    logger.info("Starting Schwab MCP Server...")
    
    try:
        # Change to core directory for relative imports
        os.chdir(core_dir)
        
        # Execute MCP server
        result = subprocess.run([
            sys.executable, 
            str(mcp_server_path)
        ], check=True)
        
        return result.returncode == 0
        
    except subprocess.CalledProcessError as e:
        logger.error(f"MCP server failed to start: {e}")
        return False
    except KeyboardInterrupt:
        logger.info("Shutdown requested by user")
        return True
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
