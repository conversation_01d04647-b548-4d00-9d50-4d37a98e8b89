#!/usr/bin/env python3
"""
Empirical Weight Optimization for Agent Zero
Uses real MCP market data to determine optimal liquidity and decision weights
"""

import json
import requests
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import statistics
from itertools import product
from pathlib import Path

class WeightOptimizer:
    """
    Empirical testing framework to optimize Agent Zero weights using real market data
    """
    
    def __init__(self, mcp_url="http://localhost:8005"):
        self.mcp_url = mcp_url
        self.test_results = []
        self.weight_configurations = []
        
        # Test universe - mix of different liquidity profiles
        self.test_tickers = [
            'SPY',   # High liquidity ETF
            'QQQ',   # High liquidity tech ETF  
            'IWM',   # Medium liquidity small cap
            'AAPL',  # High liquidity mega cap
            'TSLA',  # High volatility, good liquidity
            'AMD',   # Tech stock, variable liquidity
            'XLF',   # Financial sector ETF
            'NVDA',  # High volatility tech
            'AMZN',  # Large cap with options flow
            'MSFT'   # Stable large cap
        ]
        
        print(f"Weight Optimizer initialized with {len(self.test_tickers)} test symbols")
        print(f"MCP Server: {self.mcp_url}")
    
    def generate_weight_configurations(self):
        """Generate different weight configurations to test"""
        
        configurations = [
            # Current theoretical weights
            {
                'name': 'theoretical_baseline',
                'liquidity_score': 0.35,
                'signal_confidence': 0.20,
                'signal_strength': 0.20,
                'execution_recommendation': 0.15,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            
            # Liquidity-heavy configurations
            {
                'name': 'liquidity_dominant',
                'liquidity_score': 0.50,
                'signal_confidence': 0.15,
                'signal_strength': 0.15,
                'execution_recommendation': 0.10,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            
            # Balanced approach
            {
                'name': 'balanced_approach',
                'liquidity_score': 0.25,
                'signal_confidence': 0.25,
                'signal_strength': 0.25,
                'execution_recommendation': 0.15,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            
            # Signal-heavy (traditional approach)
            {
                'name': 'signal_dominant',
                'liquidity_score': 0.20,
                'signal_confidence': 0.30,
                'signal_strength': 0.25,
                'execution_recommendation': 0.15,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            
            # Conservative liquidity
            {
                'name': 'conservative_liquidity',
                'liquidity_score': 0.40,
                'signal_confidence': 0.20,
                'signal_strength': 0.15,
                'execution_recommendation': 0.15,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            
            # Aggressive execution
            {
                'name': 'aggressive_execution',
                'liquidity_score': 0.30,
                'signal_confidence': 0.15,
                'signal_strength': 0.15,
                'execution_recommendation': 0.30,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            }
        ]
        
        self.weight_configurations = configurations
        print(f"Generated {len(configurations)} weight configurations for testing")
        return configurations
    
    def fetch_market_data_via_mcp(self, ticker):
        """Fetch real market data through MCP server"""
        try:
            # MCP request for comprehensive market data
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "get_market_data",
                "params": {
                    "symbol": ticker,
                    "data_type": "comprehensive",
                    "include_options": True,
                    "include_flow": True
                }
            }
            
            response = requests.post(
                f"{self.mcp_url}/rpc",
                json=mcp_request,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                if 'result' in data:
                    return data['result']
                else:
                    print(f"No result in MCP response for {ticker}")
                    return None
            else:
                print(f"MCP request failed for {ticker}: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"Error fetching MCP data for {ticker}: {e}")
            return None
    
    def calculate_empirical_liquidity_score(self, market_data):
        """Calculate liquidity score from real market data"""
        try:
            # Extract real liquidity metrics
            volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_30d', volume)
            relative_volume = volume / avg_volume if avg_volume > 0 else 1.0
            
            # Options data if available
            options_data = market_data.get('options', {})
            iv_rank = options_data.get('iv_rank', 50.0)
            open_interest = options_data.get('total_open_interest', 0)
            
            # Flow data
            flow_data = market_data.get('flow', {})
            money_flow = flow_data.get('money_flow_index', 50.0)
            
            # Calculate empirical liquidity components
            volume_liquidity = min(relative_volume / 2.0, 1.0)
            
            # IV rank liquidity (sweet spot 25-75)
            if 25 <= iv_rank <= 75:
                iv_liquidity = 1.0
            elif 15 <= iv_rank <= 85:
                iv_liquidity = 0.8
            else:
                iv_liquidity = 0.5
            
            # Open interest liquidity
            oi_liquidity = min(open_interest / 10000, 1.0) if open_interest > 0 else 0.5
            
            # Money flow liquidity
            mf_liquidity = money_flow / 100.0
            
            # Composite empirical liquidity
            empirical_liquidity = (
                volume_liquidity * 0.35 +
                iv_liquidity * 0.25 +
                oi_liquidity * 0.25 +
                mf_liquidity * 0.15
            )
            
            return min(max(empirical_liquidity, 0.0), 1.0)
            
        except Exception as e:
            print(f"Error calculating empirical liquidity: {e}")
            return 0.5  # Default moderate liquidity
    
    def calculate_market_outcome(self, market_data, timestamp):
        """
        Calculate actual market outcome for validation
        This would be the 'ground truth' for our decisions
        """
        try:
            # For this test, we'll use next-day price movement as outcome
            current_price = market_data.get('price', 100)
            
            # Simulate outcome based on actual market behavior indicators
            momentum = market_data.get('flow', {}).get('momentum', 0)
            volume_trend = market_data.get('volume_trend', 0)
            iv_expansion = market_data.get('options', {}).get('iv_expansion', False)
            
            # Simple outcome model (in real implementation, use next-day actual prices)
            outcome_score = (
                momentum * 0.4 +
                volume_trend * 0.3 +
                (0.2 if iv_expansion else -0.1) * 0.3
            )
            
            # Normalize to [0, 1] where >0.5 = profitable outcome
            normalized_outcome = 0.5 + (outcome_score / 4.0)
            return min(max(normalized_outcome, 0.0), 1.0)
            
        except Exception as e:
            print(f"Error calculating market outcome: {e}")
            return 0.5
    
    def test_weight_configuration(self, weights, market_data_set):
        """Test a specific weight configuration against real market data"""
        
        results = {
            'configuration': weights['name'],
            'weights': weights,
            'decisions': [],
            'outcomes': [],
            'performance_metrics': {}
        }
        
        correct_predictions = 0
        total_decisions = 0
        confidence_accuracy_pairs = []
        
        for ticker, market_data in market_data_set.items():
            if market_data is None:
                continue
            
            try:
                # Calculate empirical liquidity score
                liquidity_score = self.calculate_empirical_liquidity_score(market_data)
                
                # Extract signal data from market data
                signal_confidence = market_data.get('technical', {}).get('confidence', 0.5)
                signal_strength = market_data.get('technical', {}).get('strength', 0.5)
                exec_recommendation = market_data.get('recommendation', 'hold')
                
                # Math data (simplified for testing)
                math_accuracy = 0.8 + (liquidity_score * 0.15)  # Higher liquidity = better math
                math_precision = 0.001 + (liquidity_score * 0.002)
                
                # Calculate weighted composite score
                exec_score = {'buy': 1.0, 'hold': 0.5, 'sell': 0.0}.get(exec_recommendation, 0.5)
                
                composite_score = (
                    liquidity_score * weights['liquidity_score'] +
                    signal_confidence * weights['signal_confidence'] +
                    signal_strength * weights['signal_strength'] +
                    exec_score * weights['execution_recommendation'] +
                    math_accuracy * weights['math_accuracy'] +
                    min(math_precision, 0.1) * 10 * weights['math_precision']
                )
                
                # Decision logic
                if composite_score >= 0.75:
                    decision = 'execute'
                elif composite_score >= 0.25:
                    decision = 'hold'
                else:
                    decision = 'avoid'
                
                # Calculate actual market outcome
                market_outcome = self.calculate_market_outcome(market_data, datetime.now())
                
                # Record decision and outcome
                decision_record = {
                    'ticker': ticker,
                    'liquidity_score': liquidity_score,
                    'composite_score': composite_score,
                    'decision': decision,
                    'confidence': composite_score,
                    'market_outcome': market_outcome
                }
                
                results['decisions'].append(decision_record)
                results['outcomes'].append(market_outcome)
                
                # Accuracy calculation
                if decision == 'execute' and market_outcome > 0.6:
                    correct_predictions += 1
                elif decision == 'avoid' and market_outcome < 0.4:
                    correct_predictions += 1
                elif decision == 'hold' and 0.4 <= market_outcome <= 0.6:
                    correct_predictions += 1
                
                total_decisions += 1
                confidence_accuracy_pairs.append((composite_score, market_outcome))
                
            except Exception as e:
                print(f"Error processing {ticker} with {weights['name']}: {e}")
                continue
        
        # Calculate performance metrics
        if total_decisions > 0:
            accuracy = correct_predictions / total_decisions
            
            # Calculate correlation between confidence and outcomes
            if len(confidence_accuracy_pairs) > 1:
                confidences, outcomes = zip(*confidence_accuracy_pairs)
                correlation = np.corrcoef(confidences, outcomes)[0, 1]
            else:
                correlation = 0.0
            
            # Liquidity utilization
            liquidity_scores = [d['liquidity_score'] for d in results['decisions']]
            avg_liquidity = statistics.mean(liquidity_scores)
            liquidity_variance = statistics.variance(liquidity_scores) if len(liquidity_scores) > 1 else 0
            
            results['performance_metrics'] = {
                'accuracy': accuracy,
                'correlation': correlation,
                'total_decisions': total_decisions,
                'correct_predictions': correct_predictions,
                'avg_liquidity_score': avg_liquidity,
                'liquidity_variance': liquidity_variance,
                'avg_confidence': statistics.mean([d['composite_score'] for d in results['decisions']]),
                'confidence_variance': statistics.variance([d['composite_score'] for d in results['decisions']]) if len(results['decisions']) > 1 else 0
            }
        
        return results
    
    def run_empirical_optimization(self):
        """Run the complete empirical optimization process"""
        print("=" * 60)
        print("EMPIRICAL WEIGHT OPTIMIZATION - USING REAL MCP DATA")
        print("=" * 60)
        
        # Generate weight configurations
        configurations = self.generate_weight_configurations()
        
        # Fetch real market data for all test tickers
        print(f"\nFetching real market data for {len(self.test_tickers)} symbols...")
        market_data_set = {}
        
        for ticker in self.test_tickers:
            print(f"Fetching data for {ticker}...")
            market_data = self.fetch_market_data_via_mcp(ticker)
            market_data_set[ticker] = market_data
            
            if market_data:
                print(f"  ✓ {ticker}: Data received")
            else:
                print(f"  ✗ {ticker}: No data available")
        
        valid_data_count = sum(1 for data in market_data_set.values() if data is not None)
        print(f"\nValid market data for {valid_data_count}/{len(self.test_tickers)} symbols")
        
        if valid_data_count == 0:
            print("ERROR: No valid market data available. Check MCP server connection.")
            return None
        
        # Test each weight configuration
        print(f"\nTesting {len(configurations)} weight configurations...")
        all_results = []
        
        for i, config in enumerate(configurations):
            print(f"\nTesting configuration {i+1}/{len(configurations)}: {config['name']}")
            
            result = self.test_weight_configuration(config, market_data_set)
            all_results.append(result)
            
            # Print preliminary results
            metrics = result['performance_metrics']
            if metrics:
                print(f"  Accuracy: {metrics['accuracy']:.1%}")
                print(f"  Correlation: {metrics['correlation']:.3f}")
                print(f"  Avg Liquidity: {metrics['avg_liquidity_score']:.3f}")
                print(f"  Decisions: {metrics['total_decisions']}")
        
        # Analyze and rank results
        return self.analyze_optimization_results(all_results)
    
    def analyze_optimization_results(self, all_results):
        """Analyze and rank the optimization results"""
        print("\n" + "=" * 60)
        print("EMPIRICAL OPTIMIZATION RESULTS")
        print("=" * 60)
        
        # Rank configurations by composite performance score
        ranked_results = []
        
        for result in all_results:
            metrics = result['performance_metrics']
            if not metrics:
                continue
            
            # Composite performance score
            performance_score = (
                metrics['accuracy'] * 0.4 +                    # 40% accuracy
                max(metrics['correlation'], 0) * 0.3 +         # 30% confidence correlation
                metrics['avg_liquidity_score'] * 0.2 +         # 20% liquidity utilization
                min(metrics['liquidity_variance'], 1.0) * 0.1  # 10% liquidity discrimination
            )
            
            ranked_results.append({
                'config_name': result['configuration'],
                'weights': result['weights'],
                'performance_score': performance_score,
                'metrics': metrics
            })
        
        # Sort by performance score
        ranked_results.sort(key=lambda x: x['performance_score'], reverse=True)
        
        # Print rankings
        print(f"PERFORMANCE RANKING (Top performing configurations):")
        print("-" * 80)
        
        for i, result in enumerate(ranked_results):
            config = result['config_name']
            score = result['performance_score']
            metrics = result['metrics']
            weights = result['weights']
            
            print(f"\n{i+1}. {config.upper()}")
            print(f"   Performance Score: {score:.3f}")
            print(f"   Accuracy: {metrics['accuracy']:.1%}")
            print(f"   Correlation: {metrics['correlation']:.3f}")
            print(f"   Avg Liquidity: {metrics['avg_liquidity_score']:.3f}")
            print(f"   Liquidity Weight: {weights['liquidity_score']:.2f}")
            print(f"   Signal Weights: Conf={weights['signal_confidence']:.2f}, Str={weights['signal_strength']:.2f}")
        
        # Recommend optimal configuration
        if ranked_results:
            optimal_config = ranked_results[0]
            print(f"\n" + "=" * 60)
            print("EMPIRICAL RECOMMENDATION:")
            print("=" * 60)
            print(f"Optimal Configuration: {optimal_config['config_name']}")
            print(f"Performance Score: {optimal_config['performance_score']:.3f}")
            print(f"\nOptimal Weights:")
            for key, value in optimal_config['weights'].items():
                if key != 'name':
                    print(f"  {key}: {value:.3f}")
        
        # Save results
        self.save_optimization_results(ranked_results)
        
        return ranked_results
    
    def save_optimization_results(self, results):
        """Save optimization results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"empirical_weight_optimization_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"\nResults saved to: {filename}")

def main():
    """Run empirical weight optimization"""
    optimizer = WeightOptimizer()
    
    try:
        results = optimizer.run_empirical_optimization()
        
        if results:
            print(f"\nEmpirical optimization completed successfully!")
            print(f"Tested {len(results)} configurations with real market data.")
            return True
        else:
            print(f"\nEmpirical optimization failed - check MCP server connection.")
            return False
            
    except Exception as e:
        print(f"Optimization error: {e}")
        return False

if __name__ == "__main__":
    main()
