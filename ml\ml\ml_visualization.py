"""
ML Visualization Module for Machine Learning Components

This module provides visualization utilities for machine learning models,
predictions, and performance metrics for the Liquidity Strategy project.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Tuple, Any, Optional, Union, Callable
import seaborn as sns
from sklearn.metrics import confusion_matrix, roc_curve, auc, precision_recall_curve
from datetime import datetime
import os
import json
import logging

# Import from existing modules
from ml.ml_config import get_ml_config
from ml.ml_utils import format_time_elapsed

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('ml_visualization')


class MLVisualizer:
    """Base visualizer class with shared functionality for all visualization types."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the visualizer with optional configuration.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or get_ml_config().get_dashboard_config()
        self.color_map = self.config.get("confidence_color_map", {
            "high": "#28a745",  # Green
            "medium": "#ffc107",  # Yellow
            "low": "#dc3545"     # Red
        })
        self.default_colors = px.colors.qualitative.Plotly
        
    def create_figure(self, width: int = 800, height: int = 600) -> go.Figure:
        """
        Create a base plotly figure with project styling.
        
        Args:
            width: Figure width in pixels
            height: Figure height in pixels
            
        Returns:
            Plotly figure with base styling
        """
        fig = go.Figure()
        fig.update_layout(
            width=width,
            height=height,
            template="plotly_white",
            margin=dict(l=20, r=20, t=40, b=20),
            font=dict(family="Arial, sans-serif", size=12),
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="right", x=1)
        )
        return fig
    
    def save_figure(self, fig: go.Figure, filename: str, formats: List[str] = None) -> List[str]:
        """
        Save figure to disk in specified formats.
        
        Args:
            fig: Plotly figure to save
            filename: Base filename (without extension)
            formats: List of formats to save ('html', 'png', 'svg', 'pdf', 'json')
            
        Returns:
            List of saved file paths
        """
        if formats is None:
            formats = ['html', 'png']
        
        output_dir = get_ml_config().config.get("dashboard_integration", {}).get(
            "visualization_output_dir", os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(
                os.path.abspath(__file__)))), "visualizations"))
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        saved_files = []
        for fmt in formats:
            output_path = os.path.join(output_dir, f"{filename}.{fmt}")
            try:
                if fmt == 'html':
                    fig.write_html(output_path)
                elif fmt == 'png':
                    fig.write_image(output_path)
                elif fmt == 'svg':
                    fig.write_image(output_path)
                elif fmt == 'pdf':
                    fig.write_image(output_path)
                elif fmt == 'json':
                    with open(output_path, 'w') as f:
                        f.write(fig.to_json())
                else:
                    logger.warning(f"Unsupported format: {fmt}")
                    continue
                
                saved_files.append(output_path)
                logger.info(f"Saved figure to {output_path}")
            except Exception as e:
                logger.error(f"Error saving figure as {fmt}: {e}")
        
        return saved_files


class PatternVisualizer(MLVisualizer):
    """Visualizer for pattern detection results and confidence."""
    
    def plot_pattern_detection(self, price_data: pd.DataFrame, 
                              patterns: List[Dict[str, Any]], 
                              timeframe: str) -> go.Figure:
        """
        Plot price data with detected patterns highlighted.
        
        Args:
            price_data: DataFrame with OHLCV data
            patterns: List of detected patterns with metadata
            timeframe: Timeframe string (e.g., "1h", "4h", "1d")
            
        Returns:
            Plotly figure with patterns visualized
        """
        # Implementation for pattern visualization
        fig = self.create_figure(height=700)
        
        # Add candlestick chart
        fig.add_trace(go.Candlestick(
            x=price_data.index,
            open=price_data['open'],
            high=price_data['high'],
            low=price_data['low'],
            close=price_data['close'],
            name="Price"
        ))
        
        # Add volume as a bar chart at the bottom
        fig.add_trace(go.Bar(
            x=price_data.index,
            y=price_data['volume'],
            name="Volume",
            marker=dict(color='rgba(100, 100, 200, 0.4)'),
            opacity=0.5,
            yaxis="y2"
        ))
        
        # Highlight pattern regions
        for pattern in patterns:
            # Get pattern details
            pattern_type = pattern.get('type', 'unknown')
            start_idx = pattern.get('start_idx')
            end_idx = pattern.get('end_idx', start_idx)
            confidence = pattern.get('confidence', 0.5)
            confidence_level = self._get_confidence_level(confidence)
            color = self.color_map.get(confidence_level, "#808080")
            
            # Calculate pattern region
            if start_idx is not None and end_idx is not None:
                # Get corresponding timestamps
                try:
                    start_time = price_data.index[start_idx]
                    end_time = price_data.index[end_idx]
                    
                    # Add a highlight rectangle for the pattern
                    fig.add_shape(
                        type="rect",
                        x0=start_time,
                        x1=end_time,
                        y0=price_data['low'].iloc[start_idx:end_idx+1].min() * 0.99,
                        y1=price_data['high'].iloc[start_idx:end_idx+1].max() * 1.01,
                        line=dict(color=color, width=2),
                        fillcolor=f"rgba({int(color[1:3], 16)}, {int(color[3:5], 16)}, {int(color[5:7], 16)}, 0.2)",
                        layer="below"
                    )
                    
                    # Add an annotation for the pattern type
                    fig.add_annotation(
                        x=start_time,
                        y=price_data['high'].iloc[start_idx:end_idx+1].max() * 1.02,
                        text=f"{pattern_type.replace('_', ' ').title()} ({confidence:.2f})",
                        showarrow=True,
                        arrowhead=1,
                        arrowcolor=color,
                        font=dict(color=color)
                    )
                except (IndexError, KeyError) as e:
                    # Skip invalid pattern index
                    continue
        
        # Set up dual Y axes for price and volume
        fig.update_layout(
            title=f"Pattern Detection - {timeframe}",
            xaxis_title="Date",
            yaxis_title="Price",
            yaxis2=dict(
                title="Volume",
                overlaying="y",
                side="right",
                showgrid=False,
                rangemode="tozero"
            ),
            xaxis_rangeslider_visible=False
        )
        
        return fig
    
    def plot_pattern_confidence_distribution(self, patterns: List[Dict[str, Any]]) -> go.Figure:
        """
        Plot confidence distribution for detected patterns.
        
        Args:
            patterns: List of detected patterns with metadata
            
        Returns:
            Plotly figure with confidence distribution
        """
        if not patterns:
            fig = self.create_figure(height=400)
            fig.add_annotation(text="No patterns to display", showarrow=False)
            fig.update_layout(title="Pattern Confidence Distribution")
            return fig
            
        # Extract pattern types and confidences
        pattern_types = []
        confidences = []
        
        for pattern in patterns:
            pattern_types.append(pattern.get('type', 'unknown').replace('_', ' ').title())
            confidences.append(pattern.get('confidence', 0.0))
        
        # Create dataframe for plotting
        df = pd.DataFrame({
            'Pattern Type': pattern_types,
            'Confidence': confidences
        })
        
        # Create box plot grouped by pattern type
        fig = px.box(df, x='Pattern Type', y='Confidence', 
                    color='Pattern Type', points='all',
                    title="Pattern Confidence Distribution")
        
        # Add horizontal lines for confidence thresholds
        thresholds = get_ml_config().get("pattern_recognition", "confidence_thresholds")
        for level, threshold in thresholds.items():
            fig.add_shape(
                type="line",
                x0=-0.5,
                x1=len(set(pattern_types)) - 0.5,
                y0=threshold,
                y1=threshold,
                line=dict(
                    color=self.color_map.get(level, "#808080"),
                    width=2,
                    dash="dash"
                )
            )
            
            # Add annotation for threshold
            fig.add_annotation(
                x=len(set(pattern_types)) - 0.5,
                y=threshold,
                text=f"{level.title()} Threshold ({threshold})",
                showarrow=False,
                xshift=50,
                font=dict(color=self.color_map.get(level, "#808080"))
            )
        
        fig.update_layout(
            xaxis_title="Pattern Type",
            yaxis_title="Confidence Score",
            yaxis_range=[0, 1.05],
            showlegend=False
        )
        
        return fig
    
    def plot_pattern_summary(self, patterns: List[Dict[str, Any]]) -> go.Figure:
        """
        Create a summary visualization of detected patterns.
        
        Args:
            patterns: List of detected patterns with metadata
            
        Returns:
            Plotly figure with pattern summary
        """
        if not patterns:
            fig = self.create_figure(height=400)
            fig.add_annotation(text="No patterns to display", showarrow=False)
            fig.update_layout(title="Pattern Summary")
            return fig
            
        # Count patterns by type and confidence level
        pattern_counts = {}
        
        for pattern in patterns:
            pattern_type = pattern.get('type', 'unknown')
            confidence = pattern.get('confidence', 0.0)
            confidence_level = self._get_confidence_level(confidence)
            
            if pattern_type not in pattern_counts:
                pattern_counts[pattern_type] = {'high': 0, 'medium': 0, 'low': 0, 'total': 0}
            
            pattern_counts[pattern_type][confidence_level] += 1
            pattern_counts[pattern_type]['total'] += 1
        
        # Prepare data for stacked bar chart
        pattern_types = list(pattern_counts.keys())
        high_counts = [pattern_counts[pt]['high'] for pt in pattern_types]
        medium_counts = [pattern_counts[pt]['medium'] for pt in pattern_types]
        low_counts = [pattern_counts[pt]['low'] for pt in pattern_types]
        
        # Create stacked bar chart
        fig = go.Figure(data=[
            go.Bar(name='High Confidence', x=pattern_types, y=high_counts, 
                  marker_color=self.color_map.get('high')),
            go.Bar(name='Medium Confidence', x=pattern_types, y=medium_counts, 
                  marker_color=self.color_map.get('medium')),
            go.Bar(name='Low Confidence', x=pattern_types, y=low_counts, 
                  marker_color=self.color_map.get('low'))
        ])
        
        # Format pattern types for display
        x_labels = [pt.replace('_', ' ').title() for pt in pattern_types]
        
        fig.update_layout(
            title='Pattern Detection Summary',
            xaxis=dict(
                title='Pattern Type',
                tickvals=list(range(len(pattern_types))),
                ticktext=x_labels
            ),
            yaxis=dict(title='Count'),
            barmode='stack',
            legend=dict(orientation='h', yanchor='bottom', y=1.02, xanchor='center', x=0.5)
        )
        
        return fig
    
    def _get_confidence_level(self, confidence: float) -> str:
        """
        Convert numerical confidence to level string (high, medium, low).
        
        Args:
            confidence: Confidence value (0.0 to 1.0)
            
        Returns:
            Confidence level string
        """
        thresholds = get_ml_config().get("pattern_recognition", "confidence_thresholds")
        if confidence >= thresholds.get("high", 0.8):
            return "high"
        elif confidence >= thresholds.get("medium", 0.6):
            return "medium"
        else:
            return "low"


class ModelPerformanceVisualizer(MLVisualizer):
    """Visualizer for model training and evaluation metrics."""
    
    def plot_confusion_matrix(self, y_true: np.ndarray, 
                             y_pred: np.ndarray, 
                             class_names: List[str]) -> go.Figure:
        """
        Plot confusion matrix for classification model evaluation.
        
        Args:
            y_true: Array of true labels
            y_pred: Array of predicted labels
            class_names: List of class names
            
        Returns:
            Plotly figure with confusion matrix
        """
        cm = confusion_matrix(y_true, y_pred)
        cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
        
        fig = make_subplots(rows=1, cols=2, 
                           subplot_titles=("Confusion Matrix", "Normalized Confusion Matrix"))
        
        # Plot raw counts
        fig.add_trace(
            go.Heatmap(
                z=cm,
                x=class_names,
                y=class_names,
                colorscale="Blues",
                showscale=False,
                text=cm,
                texttemplate="%{text}",
                textfont={"size": 12},
                hovertemplate="True: %{y}<br>Predicted: %{x}<br>Count: %{z}<extra></extra>"
            ),
            row=1, col=1
        )
        
        # Plot normalized values
        fig.add_trace(
            go.Heatmap(
                z=cm_normalized,
                x=class_names,
                y=class_names,
                colorscale="Blues",
                showscale=True,
                text=cm_normalized,
                texttemplate="%{text:.2f}",
                textfont={"size": 12},
                hovertemplate="True: %{y}<br>Predicted: %{x}<br>Percentage: %{text:.2%}<extra></extra>"
            ),
            row=1, col=2
        )
        
        fig.update_layout(
            title="Model Classification Performance",
            height=500,
            width=900,
            xaxis=dict(title="Predicted Label"),
            yaxis=dict(title="True Label"),
            xaxis2=dict(title="Predicted Label"),
            yaxis2=dict(title="True Label")
        )
        
        return fig
    
    def plot_training_history(self, history: Dict[str, List[float]]) -> go.Figure:
        """
        Plot model training history metrics.
        
        Args:
            history: Dictionary of training metrics (loss, accuracy, val_loss, etc.)
            
        Returns:
            Plotly figure with training metrics
        """
        fig = make_subplots(rows=1, cols=2, subplot_titles=("Loss", "Metrics"))
        
        # Plot loss curves
        if 'loss' in history:
            fig.add_trace(
                go.Scatter(
                    x=list(range(1, len(history['loss'])+1)),
                    y=history['loss'],
                    mode='lines',
                    name='Training Loss'
                ),
                row=1, col=1
            )
        
        if 'val_loss' in history:
            fig.add_trace(
                go.Scatter(
                    x=list(range(1, len(history['val_loss'])+1)),
                    y=history['val_loss'],
                    mode='lines',
                    name='Validation Loss'
                ),
                row=1, col=1
            )
        
        # Plot other metrics (accuracy, precision, recall, etc.)
        for metric in history:
            if metric not in ['loss', 'val_loss'] and not metric.startswith('val_'):
                val_metric = f'val_{metric}' if f'val_{metric}' in history else None
                
                # Training metric
                fig.add_trace(
                    go.Scatter(
                        x=list(range(1, len(history[metric])+1)),
                        y=history[metric],
                        mode='lines',
                        name=f'Training {metric.capitalize()}'
                    ),
                    row=1, col=2
                )
                
                # Validation metric
                if val_metric:
                    fig.add_trace(
                        go.Scatter(
                            x=list(range(1, len(history[val_metric])+1)),
                            y=history[val_metric],
                            mode='lines',
                            name=f'Validation {metric.capitalize()}'
                        ),
                        row=1, col=2
                    )
        
        fig.update_layout(
            title="Model Training History",
            height=500,
            width=900,
            xaxis=dict(title="Epoch"),
            yaxis=dict(title="Loss"),
            xaxis2=dict(title="Epoch"),
            yaxis2=dict(title="Metric Value")
        )
        
        return fig
    
    def plot_roc_curve(self, y_true: np.ndarray, 
                      y_score: np.ndarray, 
                      class_names: List[str] = None) -> go.Figure:
        """
        Plot ROC curve for binary or multi-class classification.
        
        Args:
            y_true: One-hot encoded true labels or binary labels
            y_score: Predicted probabilities
            class_names: List of class names
            
        Returns:
            Plotly figure with ROC curve
        """
        fig = self.create_figure()
        
        # Add diagonal line (random classifier)
        fig.add_trace(
            go.Scatter(
                x=[0, 1],
                y=[0, 1],
                mode='lines',
                name='Random Classifier',
                line=dict(color='gray', dash='dash'),
                hoverinfo='none'
            )
        )
        
        # Binary classification
        if len(y_true.shape) == 1 or y_true.shape[1] == 1:
            fpr, tpr, _ = roc_curve(y_true, y_score)
            roc_auc = auc(fpr, tpr)
            
            fig.add_trace(
                go.Scatter(
                    x=fpr,
                    y=tpr,
                    mode='lines',
                    name=f'ROC curve (AUC = {roc_auc:.3f})',
                    line=dict(color='blue')
                )
            )
        
        # Multi-class classification
        else:
            n_classes = y_true.shape[1]
            class_names = class_names or [f'Class {i}' for i in range(n_classes)]
            
            for i in range(n_classes):
                fpr, tpr, _ = roc_curve(y_true[:, i], y_score[:, i])
                roc_auc = auc(fpr, tpr)
                
                fig.add_trace(
                    go.Scatter(
                        x=fpr,
                        y=tpr,
                        mode='lines',
                        name=f'{class_names[i]} (AUC = {roc_auc:.3f})',
                        line=dict(color=self.default_colors[i % len(self.default_colors)])
                    )
                )
        
        fig.update_layout(
            title="Receiver Operating Characteristic (ROC) Curve",
            xaxis=dict(
                title="False Positive Rate",
                constrain="domain",
                range=[0, 1],
                showgrid=True
            ),
            yaxis=dict(
                title="True Positive Rate",
                constrain="domain",
                range=[0, 1],
                showgrid=True
            ),
            legend=dict(
                orientation="v",
                yanchor="bottom",
                y=0.02,
                xanchor="right",
                x=0.98
            )
        )
        
        return fig
    
    def plot_precision_recall_curve(self, y_true: np.ndarray, 
                                   y_score: np.ndarray, 
                                   class_names: List[str] = None) -> go.Figure:
        """
        Plot precision-recall curve for binary or multi-class classification.
        
        Args:
            y_true: One-hot encoded true labels or binary labels
            y_score: Predicted probabilities
            class_names: List of class names
            
        Returns:
            Plotly figure with precision-recall curve
        """
        fig = self.create_figure()
        
        # Binary classification
        if len(y_true.shape) == 1 or y_true.shape[1] == 1:
            precision, recall, _ = precision_recall_curve(y_true, y_score)
            pr_auc = auc(recall, precision)
            
            fig.add_trace(
                go.Scatter(
                    x=recall,
                    y=precision,
                    mode='lines',
                    name=f'PR curve (AUC = {pr_auc:.3f})',
                    line=dict(color='blue')
                )
            )
        
        # Multi-class classification
        else:
            n_classes = y_true.shape[1]
            class_names = class_names or [f'Class {i}' for i in range(n_classes)]
            
            for i in range(n_classes):
                precision, recall, _ = precision_recall_curve(y_true[:, i], y_score[:, i])
                pr_auc = auc(recall, precision)
                
                fig.add_trace(
                    go.Scatter(
                        x=recall,
                        y=precision,
                        mode='lines',
                        name=f'{class_names[i]} (AUC = {pr_auc:.3f})',
                        line=dict(color=self.default_colors[i % len(self.default_colors)])
                    )
                )
        
        fig.update_layout(
            title="Precision-Recall Curve",
            xaxis=dict(
                title="Recall",
                constrain="domain",
                range=[0, 1],
                showgrid=True
            ),
            yaxis=dict(
                title="Precision",
                constrain="domain",
                range=[0, 1],
                showgrid=True
            ),
            legend=dict(
                orientation="v",
                yanchor="bottom",
                y=0.02,
                xanchor="right",
                x=0.98
            )
        )
        
        return fig
    
    def plot_feature_importance(self, feature_names: List[str], 
                               importance_values: np.ndarray,
                               top_n: int = 20) -> go.Figure:
        """
        Plot feature importance for tree-based models or feature attribution.
        
        Args:
            feature_names: List of feature names
            importance_values: Array of feature importance values
            top_n: Number of top features to display
            
        Returns:
            Plotly figure with feature importance
        """
        # Sort features by importance
        indices = np.argsort(importance_values)[::-1]
        
        # Take top N features
        if top_n and top_n < len(feature_names):
            indices = indices[:top_n]
        
        # Create sorted lists
        sorted_names = [feature_names[i] for i in indices]
        sorted_values = [importance_values[i] for i in indices]
        
        # Format feature names for display
        display_names = [name.replace('_', ' ').title() for name in sorted_names]
        
        # Create bar chart
        fig = go.Figure(go.Bar(
            x=sorted_values,
            y=display_names,
            orientation='h'
        ))
        
        fig.update_layout(
            title=f"Top {len(sorted_names)} Feature Importance",
            xaxis=dict(title="Importance"),
            yaxis=dict(title="Feature", autorange="reversed")
        )
        
        return fig


class PredictionVisualizer(MLVisualizer):
    """Visualizer for model predictions and forecasts."""
    
    def plot_price_prediction(self, price_data: pd.DataFrame, 
                             predictions: pd.DataFrame, 
                             confidence_intervals: Optional[pd.DataFrame] = None) -> go.Figure:
        """
        Plot price predictions with optional confidence intervals.
        
        Args:
            price_data: Historical price data
            predictions: Predicted price data
            confidence_intervals: Optional confidence intervals for predictions
            
        Returns:
            Plotly figure with price predictions
        """
        fig = self.create_figure(height=600)
        
        # Add historical price data
        fig.add_trace(go.Scatter(
            x=price_data.index,
            y=price_data['close'],
            mode='lines',
            name='Historical Price',
            line=dict(color='blue')
        ))
        
        # Add prediction line
        fig.add_trace(go.Scatter(
            x=predictions.index,
            y=predictions['close'],
            mode='lines',
            name='Predicted Price',
            line=dict(color='red')
        ))
        
        # Add confidence intervals if available
        if confidence_intervals is not None and 'lower' in confidence_intervals and 'upper' in confidence_intervals:
            # Add upper bound
            fig.add_trace(go.Scatter(
                x=confidence_intervals.index,
                y=confidence_intervals['upper'],
                mode='lines',
                name='Upper Bound',
                line=dict(color='rgba(255, 0, 0, 0)'),
                showlegend=False
            ))
            
            # Add lower bound
            fig.add_trace(go.Scatter(
                x=confidence_intervals.index,
                y=confidence_intervals['lower'],
                mode='lines',
                name='Lower Bound',
                fill='tonexty',
                fillcolor='rgba(255, 0, 0, 0.2)',
                line=dict(color='rgba(255, 0, 0, 0)'),
                showlegend=True
            ))
        
        # Add vertical line at prediction start
        first_prediction_idx = predictions.index[0]
        fig.add_shape(
            type="line",
            x0=first_prediction_idx,
            x1=first_prediction_idx,
            y0=0,
            y1=1,
            yref="paper",
            line=dict(color="green", width=2, dash="dash"),
        )
        
        fig.add_annotation(
            x=first_prediction_idx,
            y=1,
            yref="paper",
            text="Prediction Start",
            showarrow=True,
            arrowhead=1,
            arrowcolor="green",
            font=dict(color="green")
        )
        
        fig.update_layout(
            title="Price Prediction",
            xaxis=dict(title="Date"),
            yaxis=dict(title="Price")
        )
        
        return fig
    
    def plot_probability_distribution(self, price_levels: np.ndarray, 
                                     probabilities: np.ndarray, 
                                     current_price: float) -> go.Figure:
        """
        Plot probability distribution for price targets.
        
        Args:
            price_levels: Array of price levels
            probabilities: Probability for each price level
            current_price: Current price for reference
            
        Returns:
            Plotly figure with probability distribution
        """
        fig = self.create_figure(height=500)
        
        # Add probability bars
        fig.add_trace(go.Bar(
            x=price_levels,
            y=probabilities,
            marker_color='lightblue',
            name='Probability'
        ))
        
        # Add current price line
        fig.add_shape(
            type="line",
            x0=current_price,
            x1=current_price,
            y0=0,
            y1=1,
            yref="paper",
            line=dict(color="black", width=2),
        )
        
        fig.add_annotation(
            x=current_price,
            y=1,
            yref="paper",
            text="Current Price",
            showarrow=True,
            arrowhead=1,
            arrowcolor="black",
            font=dict(color="black")
        )
        
        # Calculate expected value
        expected_price = np.sum(price_levels * probabilities)
        
        # Add expected value line
        fig.add_shape(
            type="line",
            x0=expected_price,
            x1=expected_price,
            y0=0,
            y1=1,
            yref="paper",
            line=dict(color="red", width=2, dash="dash"),
        )
        
        fig.add_annotation(
            x=expected_price,
            y=0.9,
            yref="paper",
            text=f"Expected Value: {expected_price:.2f}",
            showarrow=True,
            arrowhead=1,
            arrowcolor="red",
            font=dict(color="red")
        )
        
        fig.update_layout(
            title="Price Target Probability Distribution",
            xaxis=dict(title="Price Level"),
            yaxis=dict(title="Probability"),
            bargap=0,
            bargroupgap=0
        )
        
        return fig
    
    def plot_pattern_completion_forecasts(self, price_data: pd.DataFrame, 
                                         pattern_data: Dict[str, Any], 
                                         forecasts: List[Dict[str, Any]]) -> go.Figure:
        """
        Plot pattern completion forecasts with different scenarios.
        
        Args:
            price_data: Historical price data
            pattern_data: Detected pattern metadata
            forecasts: List of forecast scenarios
            
        Returns:
            Plotly figure with pattern completion forecasts
        """
        fig = self.create_figure(height=600)
        
        # Add candlestick chart for historical data
        fig.add_trace(go.Candlestick(
            x=price_data.index,
            open=price_data['open'],
            high=price_data['high'],
            low=price_data['low'],
            close=price_data['close'],
            name="Historical Price"
        ))
        
        # Extract pattern information
        pattern_type = pattern_data.get('type', 'unknown')
        start_idx = pattern_data.get('start_idx')
        end_idx = pattern_data.get('end_idx', start_idx)
        confidence = pattern_data.get('confidence', 0.5)
        confidence_level = self._get_confidence_level(confidence)
        color = self.color_map.get(confidence_level, "#808080")
        
        # Highlight pattern region
        if start_idx is not None and end_idx is not None:
            start_time = price_data.index[start_idx]
            end_time = price_data.index[end_idx]
            
            fig.add_shape(
                type="rect",
                x0=start_time,
                x1=end_time,
                y0=price_data['low'].iloc[start_idx:end_idx+1].min() * 0.99,
                y1=price_data['high'].iloc[start_idx:end_idx+1].max() * 1.01,
                line=dict(color=color, width=2),
                fillcolor=f"rgba({int(color[1:3], 16)}, {int(color[3:5], 16)}, {int(color[5:7], 16)}, 0.2)",
                layer="below"
            )
            
            fig.add_annotation(
                x=start_time,
                y=price_data['high'].iloc[start_idx:end_idx+1].max() * 1.02,
                text=f"{pattern_type.replace('_', ' ').title()} ({confidence:.2f})",
                showarrow=True,
                arrowhead=1,
                arrowcolor=color,
                font=dict(color=color)
            )
        
        # Add forecast scenarios
        for i, forecast in enumerate(forecasts):
            scenario = forecast.get('scenario', f'Scenario {i+1}')
            probability = forecast.get('probability', 0.0)
            forecast_prices = forecast.get('prices', [])
            forecast_timestamps = forecast.get('timestamps', [])
            
            # Skip if no forecast data
            if not forecast_prices or not forecast_timestamps:
                continue
            
            # Get color based on scenario type
            scenario_type = forecast.get('type', 'neutral')
            if scenario_type == 'bullish':
                line_color = 'green'
            elif scenario_type == 'bearish':
                line_color = 'red'
            else:
                line_color = 'gray'
            
            # Add forecast line
            fig.add_trace(go.Scatter(
                x=forecast_timestamps,
                y=forecast_prices,
                mode='lines',
                name=f"{scenario} ({probability:.1%})",
                line=dict(color=line_color, dash='dot')
            ))
        
        fig.update_layout(
            title=f"Pattern Completion Forecast - {pattern_type.replace('_', ' ').title()}",
            xaxis=dict(title="Date"),
            yaxis=dict(title="Price"),
            xaxis_rangeslider_visible=False
        )
        
        return fig
    
    def _get_confidence_level(self, confidence: float) -> str:
        """
        Convert numerical confidence to level string (high, medium, low).
        
        Args:
            confidence: Confidence value (0.0 to 1.0)
            
        Returns:
            Confidence level string
        """
        thresholds = get_ml_config().get("pattern_recognition", "confidence_thresholds")
        if confidence >= thresholds.get("high", 0.8):
            return "high"
        elif confidence >= thresholds.get("medium", 0.6):
            return "medium"
        else:
            return "low"


class RegimeVisualizer(MLVisualizer):
    """Visualizer for market regime detection and analysis."""
    
    def plot_regime_detection(self, price_data: pd.DataFrame, 
                             regimes: np.ndarray, 
                             regime_names: List[str]) -> go.Figure:
        """
        Plot price data with detected market regimes.
        
        Args:
            price_data: Historical price data
            regimes: Array of regime labels for each time point
            regime_names: List of regime names
            
        Returns:
            Plotly figure with regime visualization
        """
        fig = self.create_figure(height=700)
        
        # Add price line
        fig.add_trace(go.Scatter(
            x=price_data.index,
            y=price_data['close'],
            mode='lines',
            name='Price',
            line=dict(color='black')
        ))
        
        # Define colors for different regimes
        regime_colors = px.colors.qualitative.Plotly[:len(regime_names)]
        
        # Identify contiguous regime regions
        regime_regions = []
        current_regime = regimes[0]
        start_idx = 0
        
        for i, regime in enumerate(regimes):
            if regime != current_regime:
                regime_regions.append({
                    'start_idx': start_idx,
                    'end_idx': i - 1,
                    'regime': current_regime
                })
                current_regime = regime
                start_idx = i
        
        # Add last region
        regime_regions.append({
            'start_idx': start_idx,
            'end_idx': len(regimes) - 1,
            'regime': current_regime
        })
        
        # Add shaded regions for each regime
        for region in regime_regions:
            start_time = price_data.index[region['start_idx']]
            end_time = price_data.index[region['end_idx']]
            regime = int(region['regime'])
            
            if regime < len(regime_names) and regime >= 0:
                regime_color = regime_colors[regime]
                
                # Add shaded region
                fig.add_shape(
                    type="rect",
                    x0=start_time,
                    x1=end_time,
                    y0=0,
                    y1=1,
                    yref="paper",
                    fillcolor=f"rgba({int(regime_color[1:3], 16)}, {int(regime_color[3:5], 16)}, {int(regime_color[5:7], 16)}, 0.2)",
                    line=dict(width=0),
                    layer="below"
                )
                
                # Add regime label at top of chart
                if (region['end_idx'] - region['start_idx']) > 10:  # Only label larger regions
                    fig.add_annotation(
                        x=(start_time + end_time) / 2,
                        y=0.95,
                        yref="paper",
                        text=regime_names[regime],
                        showarrow=False,
                        font=dict(color=regime_color)
                    )
        
        # Add regime legend as separate traces with zero opacity
        for i, name in enumerate(regime_names):
            fig.add_trace(go.Scatter(
                x=[None],
                y=[None],
                mode='markers',
                marker=dict(color=regime_colors[i], size=10),
                name=name,
                hoverinfo='name'
            ))
        
        fig.update_layout(
            title="Market Regime Detection",
            xaxis=dict(title="Date"),
            yaxis=dict(title="Price"),
            showlegend=True
        )
        
        return fig
    
    def plot_regime_transition_matrix(self, transition_matrix: np.ndarray, 
                                     regime_names: List[str]) -> go.Figure:
        """
        Plot regime transition probability matrix.
        
        Args:
            transition_matrix: Matrix of transition probabilities
            regime_names: List of regime names
            
        Returns:
            Plotly figure with transition matrix
        """
        fig = go.Figure(data=go.Heatmap(
            z=transition_matrix,
            x=regime_names,
            y=regime_names,
            colorscale="Blues",
            text=np.round(transition_matrix, 2),
            texttemplate="%{text:.2f}",
            textfont={"size": 12},
            hovertemplate="From: %{y}<br>To: %{x}<br>Probability: %{z:.3f}<extra></extra>"
        ))
        
        fig.update_layout(
            title="Regime Transition Probability Matrix",
            xaxis=dict(title="To Regime"),
            yaxis=dict(title="From Regime")
        )
        
        return fig
    
    def plot_regime_characteristics(self, regime_stats: Dict[str, Dict[str, float]], 
                                   regime_names: List[str]) -> go.Figure:
        """
        Plot statistical characteristics of different market regimes.
        
        Args:
            regime_stats: Dictionary of regime statistics
            regime_names: List of regime names
            
        Returns:
            Plotly figure with regime characteristics
        """
        # Determine which metrics to plot
        metrics = list(next(iter(regime_stats.values())).keys())
        
        # Create radar chart data
        fig = go.Figure()
        
        for i, regime in enumerate(regime_names):
            # Skip if no stats for this regime
            if regime not in regime_stats:
                continue
                
            # Get stats for this regime
            stats = regime_stats[regime]
            
            # Create radar chart trace
            fig.add_trace(go.Scatterpolar(
                r=[stats.get(metric, 0) for metric in metrics],
                theta=metrics,
                fill='toself',
                name=regime
            ))
        
        fig.update_layout(
            title="Regime Characteristics",
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]  # Assuming normalized values
                )
            ),
            showlegend=True
        )
        
        return fig


class EnsembleVisualizer(MLVisualizer):
    """Visualizer for ensemble model performance and comparison."""
    
    def plot_model_comparison(self, 
                             model_names: List[str], 
                             metrics: Dict[str, List[float]]) -> go.Figure:
        """
        Plot performance comparison across multiple models.
        
        Args:
            model_names: List of model names
            metrics: Dictionary of metrics for each model
            
        Returns:
            Plotly figure with model comparison
        """
        # Determine which metrics to plot
        metric_names = list(metrics.keys())
        
        # Create subplots for each metric
        fig = make_subplots(rows=len(metric_names), cols=1, 
                           subplot_titles=metric_names,
                           shared_xaxes=True)
        
        # Add bar for each metric
        for i, metric in enumerate(metric_names):
            fig.add_trace(
                go.Bar(
                    x=model_names,
                    y=metrics[metric],
                    name=metric
                ),
                row=i+1, col=1
            )
            
            # Add dashed line for ensemble average if there are multiple models
            if len(model_names) > 1:
                avg_value = np.mean(metrics[metric])
                fig.add_shape(
                    type="line",
                    x0=-0.5,
                    x1=len(model_names) - 0.5,
                    y0=avg_value,
                    y1=avg_value,
                    line=dict(color="red", width=2, dash="dash"),
                    row=i+1, col=1
                )
                
                fig.add_annotation(
                    x=len(model_names) - 0.5,
                    y=avg_value,
                    text=f"Avg: {avg_value:.3f}",
                    showarrow=False,
                    xshift=50,
                    font=dict(color="red"),
                    row=i+1, col=1
                )
        
        fig.update_layout(
            title="Model Performance Comparison",
            height=200 * len(metric_names),
            showlegend=False
        )
        
        # Set y-axis title for each subplot
        for i, metric in enumerate(metric_names):
            fig.update_yaxes(title_text=metric, row=i+1, col=1)
        
        # Set x-axis title for the last subplot
        fig.update_xaxes(title_text="Model", row=len(metric_names), col=1)
        
        return fig
    
    def plot_ensemble_weights(self, model_names: List[str], 
                             weights: List[float],
                             performance: Optional[List[float]] = None) -> go.Figure:
        """
        Plot ensemble model weights with optional performance metrics.
        
        Args:
            model_names: List of model names
            weights: List of ensemble weights for each model
            performance: Optional list of performance metrics for each model
            
        Returns:
            Plotly figure with ensemble weights visualization
        """
        fig = self.create_figure(height=500)
        
        # Create dataset for plotting
        data = pd.DataFrame({
            'Model': model_names,
            'Weight': weights
        })
        
        if performance is not None:
            data['Performance'] = performance
        
        # Sort by weight
        data = data.sort_values('Weight', ascending=False)
        
        # Create bar chart for weights
        fig.add_trace(go.Bar(
            x=data['Model'],
            y=data['Weight'],
            name='Ensemble Weight',
            marker_color='blue'
        ))
        
        # Add performance line if available
        if performance is not None:
            fig.add_trace(go.Scatter(
                x=data['Model'],
                y=data['Performance'],
                mode='markers+lines',
                name='Performance',
                yaxis='y2',
                marker=dict(size=10, color='red')
            ))
            
            # Set up dual Y axes
            fig.update_layout(
                yaxis2=dict(
                    title="Performance",
                    overlaying="y",
                    side="right",
                    showgrid=False
                )
            )
        
        fig.update_layout(
            title="Ensemble Model Weights",
            xaxis=dict(title="Model"),
            yaxis=dict(title="Weight"),
            barmode='stack'
        )
        
        return fig


# Factory function to create visualizers
def create_visualizer(visualizer_type: str, config: Optional[Dict[str, Any]] = None) -> MLVisualizer:
    """
    Create a visualizer instance of the specified type.
    
    Args:
        visualizer_type: Type of visualizer ('pattern', 'performance', 'prediction', 'regime', 'ensemble')
        config: Optional configuration dictionary
        
    Returns:
        MLVisualizer instance
    """
    visualizer_map = {
        'pattern': PatternVisualizer,
        'performance': ModelPerformanceVisualizer,
        'prediction': PredictionVisualizer,
        'regime': RegimeVisualizer,
        'ensemble': EnsembleVisualizer
    }
    
    if visualizer_type not in visualizer_map:
        raise ValueError(f"Unsupported visualizer type: {visualizer_type}. " 
                       f"Supported types are: {list(visualizer_map.keys())}")
    
    visualizer_class = visualizer_map[visualizer_type]
    return visualizer_class(config)
