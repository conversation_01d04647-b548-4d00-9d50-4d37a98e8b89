# Agent Zero Options Workflow
## Advanced Options Intelligence for Trading Decisions

### **Workflow Overview**
Transform Agent Zero directional signals into specific, executable options trades with optimal strike selection, expiration analysis, and execution quality assessment.

---

## **Phase 1: Input Validation and Context Analysis**

### **Step 1.1: Validate Required Inputs**
- **Ticker**: Symbol validation and options availability check
- **Options Chain**: Complete call/put data with bid/ask/volume
- **Underlying Price**: Current stock price for moneyness calculations
- **Agent Zero Signal**: buy_calls/buy_puts/hold/avoid decision

### **Step 1.2: Extract Market Context**
- **Market Direction**: Bullish/bearish/neutral from signal analysis
- **IV Environment**: High/normal/low volatility regime
- **Liquidity Conditions**: Overall options market quality

---

## **Phase 2: Options Chain Analysis**

### **Step 2.1: Strike Analysis**
```python
for each_strike in options_chain:
    # Calculate key metrics
    moneyness = calculate_moneyness(strike, underlying_price)
    delta = estimate_delta(strike, underlying_price, option_type)
    liquidity_score = assess_strike_liquidity(volume, open_interest, spread)
    
    # Quality gates
    if liquidity_score < 0.3: continue  # Skip illiquid strikes
    if spread_pct > 0.15: continue      # Skip wide spreads
```

### **Step 2.2: Delta Targeting**
- **Preferred Range**: 0.25 - 0.75 delta for directional trades
- **Bullish Calls**: Target 0.40 - 0.60 delta (slight OTM to ATM)
- **Bearish Puts**: Target -0.40 to -0.60 delta (slight OTM to ATM)
- **Risk Management**: Avoid deep ITM (delta > 0.8) or far OTM (delta < 0.2)

### **Step 2.3: Liquidity Assessment**
```python
liquidity_components = {
    'volume_score': min(1.0, daily_volume / 100) * 0.30,
    'open_interest_score': min(1.0, open_interest / 500) * 0.30,
    'spread_score': max(0, (0.15 - spread_pct) / 0.15) * 0.25,
    'price_level_score': min(1.0, mid_price / 5.0) * 0.15
}
```

---

## **Phase 3: Optimal Selection Logic**

### **Step 3.1: Bullish Signal Processing (buy_calls)**
```python
if agent_zero_signal == 'buy_calls':
    # Filter viable calls
    viable_calls = filter_by_liquidity_and_spreads(calls)
    
    # Score each call
    for call in viable_calls:
        # Preference for ATM to slight OTM
        moneyness_score = score_moneyness(call.strike, underlying_price, 'bullish')
        delta_score = score_delta_preference(call.delta, target_range=[0.4, 0.6])
        liquidity_score = call.liquidity_score
        
        composite_score = (
            moneyness_score * 0.35 +
            delta_score * 0.35 +
            liquidity_score * 0.30
        )
    
    optimal_call = max(viable_calls, key=lambda x: x.composite_score)
```

### **Step 3.2: Bearish Signal Processing (buy_puts)**
```python
if agent_zero_signal == 'buy_puts':
    # Similar logic for puts with bearish preferences
    viable_puts = filter_by_liquidity_and_spreads(puts)
    
    # Score puts for bearish trades
    for put in viable_puts:
        moneyness_score = score_moneyness(put.strike, underlying_price, 'bearish')
        delta_score = score_delta_preference(put.delta, target_range=[-0.6, -0.4])
        liquidity_score = put.liquidity_score
        
        composite_score = (
            moneyness_score * 0.35 +
            delta_score * 0.35 +
            liquidity_score * 0.30
        )
    
    optimal_put = max(viable_puts, key=lambda x: x.composite_score)
```

### **Step 3.3: Risk Assessment**
```python
risk_metrics = {
    'estimated_delta': optimal_option.delta,
    'gamma_risk': estimate_gamma_exposure(optimal_option),
    'theta_decay': estimate_daily_theta(optimal_option),
    'vega_sensitivity': estimate_vega_exposure(optimal_option),
    'liquidity_risk': assess_execution_risk(optimal_option),
    'spread_cost': calculate_spread_impact(optimal_option)
}
```

---

## **Phase 4: Integration with Agent Zero Decision**

### **Step 4.1: Signal Validation**
```python
# Ensure Agent Zero signal matches option availability
if agent_zero_signal == 'buy_calls' and not viable_calls:
    return {'action': 'avoid', 'reason': 'No viable call options'}

if agent_zero_signal == 'buy_puts' and not viable_puts:
    return {'action': 'avoid', 'reason': 'No viable put options'}

# Liquidity gate enforcement
if overall_options_liquidity < 0.3:
    return {'action': 'avoid', 'reason': 'Poor options liquidity'}
```

### **Step 4.2: Final Recommendation Generation**
```python
final_recommendation = {
    'action': agent_zero_signal,  # buy_calls/buy_puts/hold/avoid
    'strike': optimal_option.strike,
    'option_type': optimal_option.type,  # 'call' or 'put'
    'premium': optimal_option.mid_price,
    'bid': optimal_option.bid,
    'ask': optimal_option.ask,
    'estimated_delta': optimal_option.delta,
    'liquidity_score': optimal_option.liquidity_score,
    'spread_pct': optimal_option.spread_pct,
    'risk_metrics': risk_metrics,
    'execution_quality': assess_execution_quality(optimal_option),
    'reasoning': generate_reasoning(agent_zero_signal, optimal_option)
}
```

---

## **Phase 5: Quality Validation and Output**

### **Step 5.1: Quality Gates**
- **Liquidity Score**: ≥ 0.3 minimum requirement
- **Spread Percentage**: ≤ 15% maximum
- **Delta Range**: Within target preferences
- **Execution Quality**: Overall score ≥ 0.7

### **Step 5.2: Training Data Capture**
```python
training_data = {
    'agent_zero_signal': agent_zero_signal,
    'option_selected': optimal_option,
    'alternatives_considered': len(viable_options),
    'selection_rationale': reasoning,
    'quality_metrics': quality_scores,
    'market_context': market_environment,
    'decision_timestamp': datetime.now()
}
```

### **Step 5.3: Error Handling**
- **No Viable Options**: Return avoid with clear reason
- **Poor Liquidity**: Enforce liquidity gates
- **Calculation Errors**: Use conservative defaults
- **Data Quality Issues**: Flag for manual review

---

## **Quality Standards**

### **Execution Time**: < 150ms
### **Accuracy Requirements**:
- Strike selection accuracy: 95%
- Liquidity assessment precision: 99%
- Delta calculation tolerance: ±0.01

### **Mathematical Precision**:
- All Greek estimates within ±5% of theoretical values
- Liquidity scores bounded [0.0, 1.0]
- Risk metrics mathematically consistent

### **Output Validation**:
- All required fields present
- Numerical values within expected ranges
- Reasoning chain complete and logical

---

## **Success Criteria**

1. **Functional Integration**: Seamlessly converts Agent Zero signals to specific options trades
2. **Execution Quality**: Selects optimal strikes with good liquidity
3. **Risk Awareness**: Provides complete risk assessment
4. **Performance**: Meets speed and accuracy requirements
5. **Training Data**: Captures decisions for Agent Zero learning

**Agent Zero Options Intelligence: Transform signals into executable, optimal options trades.**
