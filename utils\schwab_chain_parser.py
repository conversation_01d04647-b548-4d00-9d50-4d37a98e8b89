#!/usr/bin/env python3
"""
Streamlined Schwab Option Chain Parser
Flatten Schwab option-chain JSON into a tidy DataFrame.
Mathematical rigor: Vector-friendly numeric columns for Greek/ROC builder
"""

import pandas as pd
from itertools import chain

def parse_chain(chain_json: dict) -> pd.DataFrame:
    """
    Flatten Schwab optionchain JSON into a tidy DataFrame.

    Expected input: dict that looks like the Swagger snippet
    returned by GET /markets/options/chains/{symbol}

    Output columns
    --------------
     symbol         underlying + strike + C/P
     expiry         YYYY-MM-DD
     type           CALL / PUT
     strike
     bid, ask, mark
     iv             implied volatility (0-1)
     delta, gamma, theta, vega, rho   (NaN if not provided)
     underlying_px  (copied from top-level "underlying.mark")
    """
    u = chain_json["underlying"]
    u_px = u.get("mark") or u["last"]

    rows = []
    for sec in chain_json.get("options", []):      # list per expiry
        expiry = sec["expiryDate"]
        for typ in ("call", "put"):
            for opt in sec.get(typ+"s", []):
                greeks = opt.get("greeks", {})
                rows.append({
                    "symbol"        : opt["symbol"],
                    "expiry"        : expiry,
                    "type"          : typ.upper(),
                    "strike"        : opt["strikePrice"],
                    "bid"           : opt.get("bid", 0.0),
                    "ask"           : opt.get("ask", 0.0),
                    "mark"          : opt.get("mark", 0.0),
                    "iv"            : greeks.get("iv", 0.0),
                    "delta"         : greeks.get("delta"),
                    "gamma"         : greeks.get("gamma"),
                    "theta"         : greeks.get("theta"),
                    "vega"          : greeks.get("vega"),
                    "rho"           : greeks.get("rho"),
                    "underlying_px" : u_px
                })
    return pd.DataFrame(rows)

# Vector-friendly: returns numeric columns ready for Greek/ROC builder
