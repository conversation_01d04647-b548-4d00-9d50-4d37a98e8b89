#!/usr/bin/env python3
"""
Test Options Data and Greeks Functionality
Tests the complete options data pipeline including Greeks calculations
"""

import sys
import os
import logging
from datetime import datetime

# Add SCHWAB_MCP_PRODUCTION to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SCHWAB_MCP_PRODUCTION'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_schwab_options_data():
    """Test Schwab API options data retrieval"""
    print("\n🔍 Testing Schwab Options Data Retrieval")
    print("=" * 50)
    
    try:
        from core.schwab_production_api import SchwabAPI
        
        schwab_api = SchwabAPI()
        logger.info("✓ Schwab API initialized")
        
        # Test options chain retrieval
        logger.info("Testing options chain for AAPL...")
        options_chain = schwab_api.get_option_chains("AAPL", strike_count=5)
        
        logger.info(f"Options chain response type: {type(options_chain)}")
        logger.info(f"Options chain keys: {list(options_chain.keys()) if isinstance(options_chain, dict) else 'Not a dict'}")
        
        if options_chain:
            # Check for calls and puts
            calls = options_chain.get('callExpDateMap', {})
            puts = options_chain.get('putExpDateMap', {})
            
            logger.info(f"✓ OPTIONS DATA RETRIEVED")
            logger.info(f"  Call expirations: {len(calls)}")
            logger.info(f"  Put expirations: {len(puts)}")
            
            # Show sample option data
            if calls:
                first_exp = list(calls.keys())[0]
                first_strikes = list(calls[first_exp].keys())[:3]
                logger.info(f"  Sample call strikes for {first_exp}: {first_strikes}")
                
                # Show detailed option data
                if first_strikes:
                    sample_option = calls[first_exp][first_strikes[0]][0]
                    logger.info(f"  Sample option data keys: {list(sample_option.keys())}")
                    
                    # Check for Greeks
                    greeks_fields = ['delta', 'gamma', 'theta', 'vega', 'rho']
                    available_greeks = [field for field in greeks_fields if field in sample_option]
                    logger.info(f"  Available Greeks: {available_greeks}")
                    
                    # Show sample values
                    logger.info(f"  Strike: {sample_option.get('strikePrice', 'N/A')}")
                    logger.info(f"  Bid: ${sample_option.get('bid', 'N/A')}")
                    logger.info(f"  Ask: ${sample_option.get('ask', 'N/A')}")
                    logger.info(f"  IV: {sample_option.get('volatility', 'N/A')}")
                    logger.info(f"  Delta: {sample_option.get('delta', 'N/A')}")
                    logger.info(f"  Volume: {sample_option.get('totalVolume', 'N/A')}")
            
            return True
        else:
            logger.error("✗ No options data returned")
            return False
            
    except Exception as e:
        logger.error(f"✗ Options data test failed: {e}")
        return False

def test_greeks_calculation():
    """Test Greeks calculation engine"""
    print("\n🔍 Testing Greeks Calculation Engine")
    print("=" * 50)

    try:
        # Test 1: Enhanced Greeks Engine
        try:
            from greeks import GreekEnhancementEngine

            calculator = GreekEnhancementEngine()
            logger.info("✓ Enhanced Greeks Engine initialized")

            # Test sample calculation
            spot_price = 201.0  # AAPL current price
            strike_price = 200.0
            time_to_expiry = 0.0833  # ~30 days
            risk_free_rate = 0.05
            volatility = 0.25

            greeks = calculator.calculate_greeks(
                spot_price=spot_price,
                strike_price=strike_price,
                time_to_expiry=time_to_expiry,
                risk_free_rate=risk_free_rate,
                volatility=volatility,
                option_type='call'
            )

            logger.info(f"✓ ENHANCED GREEKS CALCULATED")
            logger.info(f"  Delta: {greeks.delta:.4f}")
            logger.info(f"  Gamma: {greeks.gamma:.4f}")
            logger.info(f"  Theta: {greeks.theta:.4f}")
            logger.info(f"  Vega: {greeks.vega:.4f}")
            logger.info(f"  Rho: {greeks.rho:.4f}")

            return True

        except ImportError:
            logger.info("Enhanced Greeks Engine not available, trying basic calculator...")

        # Test 2: Basic Greeks Calculator
        try:
            from tasks.greeks import GreeksCalculator

            calculator = GreeksCalculator()
            logger.info("✓ Basic Greeks calculator initialized")

            # Test sample calculation
            spot_price = 201.0  # AAPL current price
            strike_price = 200.0
            time_to_expiry = 0.0833  # ~30 days
            risk_free_rate = 0.05
            volatility = 0.25

            greeks = calculator.calculate_greeks(
                S=spot_price,
                K=strike_price,
                T=time_to_expiry,
                r=risk_free_rate,
                sigma=volatility,
                q=0.0,  # dividend yield
                cp_flag=1  # call option
            )

            logger.info(f"✓ BASIC GREEKS CALCULATED")
            logger.info(f"  Delta: {greeks.get('delta', 'N/A'):.4f}")
            logger.info(f"  Gamma: {greeks.get('gamma', 'N/A'):.4f}")
            logger.info(f"  Theta: {greeks.get('theta', 'N/A'):.4f}")
            logger.info(f"  Vega: {greeks.get('vega', 'N/A'):.4f}")
            logger.info(f"  Rho: {greeks.get('rho', 'N/A'):.4f}")

            return True

        except ImportError:
            logger.error("✗ No Greeks calculator available")
            return False

    except Exception as e:
        logger.error(f"✗ Greeks calculation test failed: {e}")
        return False

def test_options_intelligence():
    """Test options intelligence services"""
    print("\n🔍 Testing Options Intelligence Services")
    print("=" * 50)

    try:
        from options_intelligence_services import OptionsIntelligenceService

        service = OptionsIntelligenceService()
        logger.info("✓ Options Intelligence Service initialized")

        # Test basic functionality
        logger.info("✓ OPTIONS INTELLIGENCE: AVAILABLE")
        return True

    except Exception as e:
        logger.error(f"✗ Options intelligence test failed: {e}")
        return False

def test_options_agents():
    """Test options-related agents"""
    print("\n🔍 Testing Options Agents")
    print("=" * 50)

    agents_found = []

    # Test Agent Zero Options Agent
    try:
        from agents.agent_zero_options_agent import AgentZeroOptionsAgent
        agents_found.append("AgentZeroOptionsAgent")
        logger.info("✓ Agent Zero Options Agent available")
    except ImportError as e:
        logger.warning(f"Agent Zero Options Agent not available: {e}")

    # Test Options Flow Decoder
    try:
        from agents.options_flow_decoder import OptionsFlowDecoderAgent
        agents_found.append("OptionsFlowDecoderAgent")
        logger.info("✓ Options Flow Decoder Agent available")
    except ImportError as e:
        logger.warning(f"Options Flow Decoder Agent not available: {e}")

    # Test IV Dynamics Agent
    try:
        from agents.iv_dynamics_agent import IVDynamicsAgent
        agents_found.append("IVDynamicsAgent")
        logger.info("✓ IV Dynamics Agent available")
    except ImportError as e:
        logger.warning(f"IV Dynamics Agent not available: {e}")

    # Test Greek Enhancement Agent
    try:
        from agents.greek_enhancement_agent import GreekEnhancementAgent
        agents_found.append("GreekEnhancementAgent")
        logger.info("✓ Greek Enhancement Agent available")
    except ImportError as e:
        logger.warning(f"Greek Enhancement Agent not available: {e}")

    logger.info(f"✓ OPTIONS AGENTS FOUND: {len(agents_found)}")
    for agent in agents_found:
        logger.info(f"  - {agent}")

    return len(agents_found) > 0

def main():
    """Run all options and Greeks tests"""
    print("\n🎯 COMPREHENSIVE OPTIONS & GREEKS TEST")
    print("=" * 60)

    results = []

    # Test 1: Schwab Options Data (may fail due to network)
    results.append(test_schwab_options_data())

    # Test 2: Greeks Calculation
    results.append(test_greeks_calculation())

    # Test 3: Options Intelligence
    results.append(test_options_intelligence())

    # Test 4: Options Agents
    results.append(test_options_agents())

    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)

    if passed >= 3:  # Allow network failure for Schwab API
        print(f"✅ CORE FUNCTIONALITY WORKING ({passed}/{total})")
        print("✅ Options intelligence and Greeks are available!")
        if passed < total:
            print("⚠️  Note: Schwab API network connectivity issue detected")
    else:
        print(f"❌ CRITICAL ISSUES FOUND ({passed}/{total})")
        print("❌ Options/Greeks functionality needs attention")

    return passed >= 3

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
