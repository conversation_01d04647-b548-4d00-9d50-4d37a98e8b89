import unittest
import sys
from pathlib import Path
from datetime import datetime
from unittest.mock import patch

# Add the parent directory to the sys.path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from Flow_Physics_Engine.position_manager import Position, PositionManager, get_position_manager


class TestPosition(unittest.TestCase):
    """Test cases for the Position class."""

    def setUp(self):
        """Set up test fixtures."""
        self.symbol = "AAPL"
        self.size = 100.0
        self.entry_price = 150.0
        self.position = Position(self.symbol, self.size, self.entry_price)

    def test_position_initialization(self):
        """Test position initialization."""
        self.assertEqual(self.position.symbol, self.symbol)
        self.assertEqual(self.position.size, self.size)
        self.assertEqual(self.position.entry_price, self.entry_price)
        self.assertEqual(self.position.current_price, self.entry_price)
        self.assertIsInstance(self.position.entry_time, datetime)

    def test_update_price(self):
        """Test price update functionality."""
        new_price = 155.0
        self.position.update_price(new_price)
        self.assertEqual(self.position.current_price, new_price)

    def test_get_pnl_positive(self):
        """Test P&L calculation with positive gain."""
        self.position.update_price(160.0)
        expected_pnl = (160.0 - 150.0) * 100.0  # $10 * 100 shares = $1000
        self.assertEqual(self.position.get_pnl(), expected_pnl)

    def test_get_pnl_negative(self):
        """Test P&L calculation with negative loss."""
        self.position.update_price(140.0)
        expected_pnl = (140.0 - 150.0) * 100.0  # -$10 * 100 shares = -$1000
        self.assertEqual(self.position.get_pnl(), expected_pnl)

    def test_get_pnl_zero(self):
        """Test P&L calculation with no change."""
        pnl = self.position.get_pnl()
        self.assertEqual(pnl, 0.0)

    def test_get_pnl_percent_positive(self):
        """Test P&L percentage calculation with positive gain."""
        self.position.update_price(165.0)
        expected_percent = (165.0 - 150.0) / 150.0  # 10% gain
        self.assertAlmostEqual(self.position.get_pnl_percent(), expected_percent, places=6)

    def test_get_pnl_percent_negative(self):
        """Test P&L percentage calculation with negative loss."""
        self.position.update_price(135.0)
        expected_percent = (135.0 - 150.0) / 150.0  # -10% loss
        self.assertAlmostEqual(self.position.get_pnl_percent(), expected_percent, places=6)

    def test_get_pnl_percent_zero_entry_price(self):
        """Test P&L percentage calculation with zero entry price."""
        position = Position("TEST", 100.0, 0.0)
        self.assertEqual(position.get_pnl_percent(), 0.0)


class TestPositionManager(unittest.TestCase):
    """Test cases for the PositionManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.manager = PositionManager()

    def test_initialization(self):
        """Test position manager initialization."""
        self.assertEqual(len(self.manager.positions), 0)
        self.assertEqual(self.manager.total_value, 100000.0)

    def test_add_position_success(self):
        """Test successful position addition."""
        result = self.manager.add_position("AAPL", 100.0, 150.0)
        self.assertTrue(result)
        self.assertEqual(len(self.manager.positions), 1)
        self.assertIn("AAPL", self.manager.positions)
        
        position = self.manager.positions["AAPL"]
        self.assertEqual(position.symbol, "AAPL")
        self.assertEqual(position.size, 100.0)
        self.assertEqual(position.entry_price, 150.0)

    def test_add_position_overwrite(self):
        """Test position overwriting when adding same symbol."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.manager.add_position("AAPL", 200.0, 160.0)
        
        self.assertEqual(len(self.manager.positions), 1)
        position = self.manager.positions["AAPL"]
        self.assertEqual(position.size, 200.0)
        self.assertEqual(position.entry_price, 160.0)

    def test_close_position_existing(self):
        """Test closing an existing position."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.manager.update_price("AAPL", 155.0)
        
        pnl = self.manager.close_position("AAPL")
        
        self.assertEqual(pnl, 500.0)  # (155-150) * 100
        self.assertEqual(len(self.manager.positions), 0)
        self.assertNotIn("AAPL", self.manager.positions)

    def test_close_position_nonexistent(self):
        """Test closing a non-existent position."""
        pnl = self.manager.close_position("NONEXISTENT")
        self.assertIsNone(pnl)

    def test_update_price_existing(self):
        """Test updating price for existing position."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.manager.update_price("AAPL", 160.0)
        
        position = self.manager.positions["AAPL"]
        self.assertEqual(position.current_price, 160.0)

    def test_update_price_nonexistent(self):
        """Test updating price for non-existent position (should not crash)."""
        # This should not raise an exception
        self.manager.update_price("NONEXISTENT", 100.0)
        self.assertEqual(len(self.manager.positions), 0)

    def test_get_position_existing(self):
        """Test getting an existing position."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        position = self.manager.get_position("AAPL")
        
        self.assertIsNotNone(position)
        self.assertEqual(position.symbol, "AAPL")

    def test_get_position_nonexistent(self):
        """Test getting a non-existent position."""
        position = self.manager.get_position("NONEXISTENT")
        self.assertIsNone(position)

    def test_get_all_positions_empty(self):
        """Test getting all positions when empty."""
        positions = self.manager.get_all_positions()
        self.assertEqual(len(positions), 0)
        self.assertIsInstance(positions, dict)

    def test_get_all_positions_with_data(self):
        """Test getting all positions with data."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.manager.add_position("GOOGL", 50.0, 2000.0)
        
        positions = self.manager.get_all_positions()
        self.assertEqual(len(positions), 2)
        self.assertIn("AAPL", positions)
        self.assertIn("GOOGL", positions)
        
        # Note: Current implementation returns shallow copy of dict but same position objects
        # This is actually a potential issue in the real code, but test what it actually does
        original_size = self.manager.positions["AAPL"].size
        self.assertEqual(positions["AAPL"].size, original_size)

    def test_get_total_pnl_empty(self):
        """Test total P&L with no positions."""
        total_pnl = self.manager.get_total_pnl()
        self.assertEqual(total_pnl, 0.0)

    def test_get_total_pnl_multiple_positions(self):
        """Test total P&L with multiple positions."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.manager.add_position("GOOGL", 10.0, 2000.0)
        
        self.manager.update_price("AAPL", 155.0)  # +$500
        self.manager.update_price("GOOGL", 1950.0)  # -$500
        
        total_pnl = self.manager.get_total_pnl()
        self.assertEqual(total_pnl, 0.0)  # +500 - 500 = 0

    def test_get_position_count(self):
        """Test position count functionality."""
        self.assertEqual(self.manager.get_position_count(), 0)
        
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.assertEqual(self.manager.get_position_count(), 1)
        
        self.manager.add_position("GOOGL", 10.0, 2000.0)
        self.assertEqual(self.manager.get_position_count(), 2)
        
        self.manager.close_position("AAPL")
        self.assertEqual(self.manager.get_position_count(), 1)

    def test_get_portfolio_summary_empty(self):
        """Test portfolio summary with no positions."""
        summary = self.manager.get_portfolio_summary()
        
        expected_summary = {
            "total_value": 100000.0,
            "total_pnl": 0.0,
            "total_value_current": 100000.0,
            "position_count": 0,
            "pnl_percent": 0.0,
            "positions": {}
        }
        
        self.assertEqual(summary, expected_summary)

    def test_get_portfolio_summary_with_positions(self):
        """Test portfolio summary with positions."""
        self.manager.add_position("AAPL", 100.0, 150.0)
        self.manager.add_position("GOOGL", 10.0, 2000.0)
        
        self.manager.update_price("AAPL", 160.0)  # +$1000
        self.manager.update_price("GOOGL", 1800.0)  # -$2000
        
        summary = self.manager.get_portfolio_summary()
        
        self.assertEqual(summary["total_value"], 100000.0)
        self.assertEqual(summary["total_pnl"], -1000.0)
        self.assertEqual(summary["total_value_current"], 99000.0)
        self.assertEqual(summary["position_count"], 2)
        self.assertEqual(summary["pnl_percent"], -0.01)  # -1%
        
        # Check individual position data
        self.assertIn("AAPL", summary["positions"])
        self.assertIn("GOOGL", summary["positions"])
        
        aapl_pos = summary["positions"]["AAPL"]
        self.assertEqual(aapl_pos["size"], 100.0)
        self.assertEqual(aapl_pos["entry_price"], 150.0)
        self.assertEqual(aapl_pos["current_price"], 160.0)
        self.assertEqual(aapl_pos["pnl"], 1000.0)
        self.assertAlmostEqual(aapl_pos["pnl_percent"], 1/15, places=6)

    def test_get_portfolio_summary_zero_total_value(self):
        """Test portfolio summary with zero total value."""
        self.manager.total_value = 0.0
        self.manager.add_position("AAPL", 100.0, 150.0)
        
        summary = self.manager.get_portfolio_summary()
        self.assertEqual(summary["pnl_percent"], 0.0)


class TestPositionManagerDefaults(unittest.TestCase):
    """Test default instance functionality."""

    def test_get_position_manager(self):
        """Test getting default position manager."""
        manager1 = get_position_manager()
        manager2 = get_position_manager()
        
        # Should return the same instance
        self.assertIs(manager1, manager2)
        self.assertIsInstance(manager1, PositionManager)


if __name__ == '__main__':
    unittest.main()