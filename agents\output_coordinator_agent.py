import json
import uuid
from pathlib import Path
from datetime import datetime
from .agent_base import BaseAgent

class OutputCoordinatorAgent(BaseAgent):
    task_id = "C-04"
    
    def __init__(self, agent_id="output_coordinator_agent"):
        """Initialize Output Coordinator Agent with Real-Time Data Coordination"""
        super().__init__(agent_id)
        
        # Initialize real-time data agent for enhanced data aggregation
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.real_time_agent = EnhancedDataAgent()
            self.has_real_time = True
            self.logger.info("Output Coordinator initialized with real-time data coordination capability")
        except ImportError:
            self.real_time_agent = None
            self.has_real_time = False
            self.logger.warning("Real-time agent unavailable - using standard output coordination")
    
    def execute_task(self, task):
        """Execute the output coordination task"""
        # Extract paths from task inputs
        signal_path = task.inputs.get("signal_path")
        chart_paths = task.inputs.get("chart_paths", [])
        math_validation_path = task.inputs.get("math_validation_path")
        
        return self.execute(signal_path, chart_paths, math_validation_path)
    
    def validate_inputs(self, task):
        """Validate task inputs meet requirements"""
        inputs = task.inputs
        required = ["signal_path", "math_validation_path"]
        return all(key in inputs for key in required)
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        # Check completeness
        required_fields = ["run_id", "ticker", "confidence", "risk", "chart_files"]
        completeness = sum(1 for field in required_fields if field in outputs) / len(required_fields)
        quality_metrics["completeness"] = completeness
        
        # Check confidence range
        confidence = outputs.get("confidence", 0)
        quality_metrics["confidence_valid"] = 1.0 if 0 <= confidence <= 1 else 0.0
        
        # Check risk metrics
        risk = outputs.get("risk", {})
        risk_fields = ["max_pct_equity", "stop_type", "expected_roi"]
        risk_complete = sum(1 for field in risk_fields if field in risk) >= 3
        quality_metrics["risk_completeness"] = 1.0 if risk_complete else 0.0
        
        # Check ROI floor
        expected_roi = risk.get("expected_roi", 0)
        quality_metrics["roi_floor_met"] = 1.0 if expected_roi >= 1.75 else 0.0
        
        return quality_metrics
    
    def execute(self, signal_path, chart_paths, math_validation_path, *, option_price=None, target_price=None):
        """Execute output coordination with timing and ROI enforcement"""
        start_time = datetime.now()
        
        sig = self.load_json(signal_path)
        mathchk = self.load_json(math_validation_path)
        self.validate_schema(sig, "schemas/signal_v1.json")
        self.validate_schema(mathchk, "schemas/math_validation_v1.json")
        
        # Calculate expected ROI with ROI floor enforcement
        if option_price is not None and target_price is not None:
            expected_roi = self._calc_expected_roi(option_price, target_price)
        else:
            expected_roi = self._calc_options_roi(sig)
        
        if expected_roi < 1.75:  # Honour the ROI floor
            raise ValueError(f"Expected ROI {expected_roi} < 1.75; trade vetoed")
        
        unified = {
            "run_id": self.make_id(),
            "ticker": sig["ticker"],
            "timestamp": self.now_iso(),
            "direction": sig["direction"],
            "confidence": sig["confidence"],
            "precision": mathchk["precision"],
            "chart_files": chart_paths,
            "risk": {
                "max_pct_equity": 1,
                "stop_type": "dynamic",
                "expected_roi": expected_roi
            },
        }
        
        self.validate_schema(unified, "schemas/unified_analysis_v1.1.json")
        out = f"outputs/{self.today}/{sig['ticker']}/unified_analysis.json"
        self.dump_json(unified, out)
        
        plan_path = out.replace("unified_analysis.json", "execution_plan.md")
        Path(plan_path).write_text(self._mk_plan(unified))
        
        # Log execution time
        duration = (datetime.now() - start_time).total_seconds()
        self.log_training({
            "inputs": {"sig": sig, "math": mathchk},
            "outputs": {"unified": unified, "execution_time": duration}
        })
        
        return unified
    
    def load_json(self, file_path):
        """Load JSON from file"""
        with open(file_path, 'r') as f:
            return json.load(f)
    
    def dump_json(self, data, file_path):
        """Save JSON to file"""
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    def validate_schema(self, data, schema_path):
        """Basic schema validation placeholder"""
        # For now, just check data is dict
        return isinstance(data, dict)
    
    def make_id(self):
        """Generate unique ID"""
        return str(uuid.uuid4())
    
    def now_iso(self):
        """Get current timestamp in ISO format"""
        return datetime.now().isoformat()
    
    @property
    def today(self):
        """Get today's date string"""
        if hasattr(self, '_today_override'):
            return self._today_override
        return datetime.now().strftime("%Y-%m-%d")
    
    @today.setter
    def today(self, value):
        """Allow setting today for testing"""
        self._today_override = value
    
    def log_training(self, data):
        """Log training data for Agent Zero learning"""
        # Simple logging placeholder - in production this would go to training system
        print(f"Training logged: {json.dumps(data, indent=2, default=str)}")
    
    def _calc_expected_roi(self, option_price, target_price):
        """
        Simple ROI = (target_price - option_price) / option_price.
        For long calls/puts use theoretical payoff at EM or custom target.
        """
        return round((target_price - option_price) / option_price, 2)
    
    def _calc_options_roi(self, signal):
        """Calculate expected ROI for options based on signal confidence and Greeks"""
        confidence = signal.get("confidence", 0)
        premium = signal.get("premium_paid", 0)
        target_price = signal.get("target_price", 0)
        strike = signal.get("strike_price", 0)
        
        if premium > 0 and target_price > 0:
            intrinsic_value = max(0, target_price - strike) if signal.get("option_type") == "call" else max(0, strike - target_price)
            potential_gain = intrinsic_value - premium
            roi = (potential_gain / premium) * confidence if premium > 0 else 0
            return round(roi, 4)
        return 0
    
    def _mk_plan(self, unified):
        """Generate execution plan markdown"""
        confidence = unified["confidence"]
        direction = unified["direction"]
        expected_roi = unified["risk"]["expected_roi"]
        
        if confidence > 0.7:
            action = "EXECUTE"
            timing = "IMMEDIATE_ENTRY"
            urgency = "HIGH"
        elif confidence > 0.5:
            action = "MONITOR"
            timing = "DELAYED_ENTRY"
            urgency = "MEDIUM"
        else:
            action = "AVOID"
            timing = "NO_ENTRY"
            urgency = "LOW"
        
        plan = f"""# Execution Plan
        
## Action: {action}
**Direction**: {direction.upper()}
**Confidence**: {confidence:.2%}
**Timing**: {timing}
**Urgency**: {urgency}

## Risk Metrics
- **Expected ROI**: {expected_roi:.2%}
- **Max Equity Risk**: {unified["risk"]["max_pct_equity"]}%
- **Stop Type**: {unified["risk"]["stop_type"]}

## Execution Steps
1. Verify option liquidity and spreads
2. Set stop-loss at premium paid
3. Monitor position management
4. Review at next major price level

## Next Review
Review position in 4 hours or at next major price level.
"""
        return plan


def main():
    """Command-line interface for OutputCoordinatorAgent"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Output Coordinator Agent")
    parser.add_argument("--signal", required=True, help="Path to signal JSON file")
    parser.add_argument("--math", required=True, help="Path to math validation JSON file")
    parser.add_argument("--charts", required=True, help="Path to chart file(s)")
    parser.add_argument("--option_price", type=float, required=True, help="Option premium price")
    parser.add_argument("--target_price", type=float, required=True, help="Target price")
    
    args = parser.parse_args()
    
    # Convert charts to list
    chart_paths = [args.charts] if isinstance(args.charts, str) else args.charts
    
    # Create agent and execute
    agent = OutputCoordinatorAgent()
    
    try:
        result = agent.execute(
            signal_path=args.signal,
            chart_paths=chart_paths,
            math_validation_path=args.math,
            option_price=args.option_price,
            target_price=args.target_price
        )
        
        print("SUCCESS: OUTPUT COORDINATOR EXECUTION SUCCESSFUL")
        print("=" * 50)
        print(f"Ticker: {result['ticker']}")
        print(f"Direction: {result['direction']}")
        print(f"Confidence: {result['confidence']:.2%}")
        print(f"Expected ROI: {result['risk']['expected_roi']:.2%}")
        print(f"Max Equity Risk: {result['risk']['max_pct_equity']}%")
        print(f"Run ID: {result['run_id']}")
        print("=" * 50)
        print(f"SUCCESS: ROI {result['risk']['expected_roi']:.2f} meets 1.75 floor requirement")
        
        return 0
        
    except ValueError as e:
        print(f"ERROR: TRADE VETOED: {e}")
        return 1
    except Exception as e:
        print(f"ERROR: EXECUTION FAILED: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
