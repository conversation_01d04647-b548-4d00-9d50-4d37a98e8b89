"""
Comprehensive Polygon.io API Endpoint Registry - External Engine Version

Complete mapping of all Polygon.io REST API endpoints with:
- Version management and backward compatibility  
- Parameter validation and type checking
- Rate limit categorization for optimization
- AI agent training metadata
"""

import re
import logging
from typing import Dict, List, Optional, Any, Union, Set
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class EndpointCategory(Enum):

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Categorize endpoints by data type and usage patterns."""
    REFERENCE = "reference"
    REAL_TIME = "real_time"
    HISTORICAL = "historical"
    AGGREGATES = "aggregates"
    TRADES = "trades"
    QUOTES = "quotes"
    OPTIONS = "options"
    MARKET_STATUS = "market_status"
    TECHNICAL = "technical"


class RateLimitCategory(Enum):
    """Rate limit categories for optimization."""
    HIGH_FREQUENCY = "high_frequency"
    MODERATE = "moderate"
    BATCH_OPTIMIZED = "batch_optimized"
    CACHE_FRIENDLY = "cache_friendly"


@dataclass
class EndpointMetadata:
    """Metadata for API endpoints to enable intelligent usage."""
    category: EndpointCategory
    rate_limit_category: RateLimitCategory
    cache_ttl_seconds: int
    supports_pagination: bool = False
    supports_bulk: bool = False
    requires_subscription: bool = False
    max_results_per_call: int = 50000
    typical_response_size_kb: int = 10
    ai_training_priority: int = 1


@dataclass
class EndpointDefinition:
    """Complete endpoint definition with validation and metadata."""
    name: str
    path: str
    method: str = "GET"
    required_params: List[str] = field(default_factory=list)
    optional_params: List[str] = field(default_factory=list)
    path_params: List[str] = field(default_factory=list)
    metadata: Optional[EndpointMetadata] = None
    description: str = ""
    example_params: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self, ticker: str = None):
        """Extract path parameters from URL template."""
        if not self.path_params:
            self.path_params = re.findall(r'\{([^}]+)\}', self.path)


class PolygonEndpointRegistry:
    """Comprehensive registry of all Polygon.io API endpoints."""
    
    def __init__(self):
        """Initialize the endpoint registry."""
        self.endpoints: Dict[str, EndpointDefinition] = {}
        self.base_url = "https://api.polygon.io"
        self._initialize_endpoints()
        logger.info(f"Endpoint registry initialized with {len(self.endpoints)} endpoints")
    
    def _add_endpoint(self, name: str, path: str, **kwargs):
        """Add endpoint to registry."""
        endpoint = EndpointDefinition(name=name, path=path, **kwargs)
        self.endpoints[name] = endpoint
    
    def _initialize_endpoints(self, ticker: str = None):
        """Initialize key Polygon.io endpoints."""
        
        # Market Status
        self._add_endpoint(
            "market_status",
            "/v1/marketstatus/now",
            metadata=EndpointMetadata(
                category=EndpointCategory.MARKET_STATUS,
                rate_limit_category=RateLimitCategory.HIGH_FREQUENCY,
                cache_ttl_seconds=60,
                ai_training_priority=4
            ),
            description="Get current market status"
        )
        
        # Stocks - Real-time
        self._add_endpoint(
            "stock_last_trade",
            "/v2/last/trade/{ticker}",
            metadata=EndpointMetadata(
                category=EndpointCategory.REAL_TIME,
                rate_limit_category=RateLimitCategory.HIGH_FREQUENCY,
                cache_ttl_seconds=5,
                ai_training_priority=5
            ),
            description="Get the most recent trade for a stock"
        )
        
        self._add_endpoint(
            "stock_last_quote",
            "/v2/last/nbbo/{ticker}",
            metadata=EndpointMetadata(
                category=EndpointCategory.REAL_TIME,
                rate_limit_category=RateLimitCategory.HIGH_FREQUENCY,
                cache_ttl_seconds=5,
                ai_training_priority=5
            ),
            description="Get the most recent NBBO quote for a stock"
        )
        
        # Stocks - Historical
        self._add_endpoint(
            "stock_aggregates",
            "/v2/aggs/ticker/{ticker}/range/{multiplier}/{timespan}/{from_}/{to}",
            optional_params=["adjusted", "sort", "limit"],
            metadata=EndpointMetadata(
                category=EndpointCategory.AGGREGATES,
                rate_limit_category=RateLimitCategory.MODERATE,
                cache_ttl_seconds=3600,
                supports_pagination=True,
                max_results_per_call=50000,
                ai_training_priority=5
            ),
            description="Get aggregate bars for a stock over a date range"
        )
        
        # Options
        self._add_endpoint(
            "options_chain_snapshot",
            "/v3/snapshot/options/{underlying_ticker}",
            optional_params=["expiration_date", "contract_type", "strike_price"],
            metadata=EndpointMetadata(
                category=EndpointCategory.OPTIONS,
                rate_limit_category=RateLimitCategory.HIGH_FREQUENCY,
                cache_ttl_seconds=60,
                supports_bulk=True,
                ai_training_priority=5
            ),
            description="Get snapshot of options chain for underlying ticker"
        )
        
        self._add_endpoint(
            "options_contracts",
            "/v3/reference/options/contracts",
            optional_params=["underlying_ticker", "contract_type", "expiration_date",
                           "strike_price", "expired", "sort", "order", "limit"],
            metadata=EndpointMetadata(
                category=EndpointCategory.OPTIONS,
                rate_limit_category=RateLimitCategory.CACHE_FRIENDLY,
                cache_ttl_seconds=3600,
                supports_pagination=True,
                supports_bulk=True,
                ai_training_priority=5
            ),
            description="List options contracts"
        )
    
    def get_endpoint(self, name: str) -> Optional[EndpointDefinition]:
        """Get endpoint definition by name."""
        return self.endpoints.get(name)
    
    def build_url(self, endpoint_name: str, path_params: Dict[str, Any] = None, 
                  query_params: Dict[str, Any] = None) -> str:
        """Build complete URL for endpoint with validation."""
        endpoint = self.get_endpoint(endpoint_name)
        if not endpoint:
            raise ValueError(f"Unknown endpoint: {endpoint_name}")
        
        # Build path with parameters
        path = endpoint.path
        if path_params:
            try:
                path = path.format(**path_params)
            except KeyError as e:
                raise ValueError(f"Missing required path parameter: {e}")
        
        # Check for remaining unsubstituted parameters
        if '{' in path:
            missing_params = re.findall(r'\{([^}]+)\}', path)
            raise ValueError(f"Missing required path parameters: {missing_params}")
        
        # Build full URL
        url = f"{self.base_url}{path}"
        
        # Add query parameters
        if query_params:
            filtered_params = {k: v for k, v in query_params.items() 
                             if v is not None and v != ""}
            
            if filtered_params:
                param_str = "&".join(f"{k}={v}" for k, v in filtered_params.items())
                url += f"?{param_str}"
        
        return url
    
    def get_endpoints_by_category(self, category: EndpointCategory) -> List[EndpointDefinition]:
        """Get all endpoints in a specific category."""
        return [ep for ep in self.endpoints.values() 
                if ep.metadata and ep.metadata.category == category]
    
    def get_optimization_recommendations(self, endpoint_name: str) -> Dict[str, Any]:
        """Get optimization recommendations for an endpoint."""
        endpoint = self.get_endpoint(endpoint_name)
        if not endpoint or not endpoint.metadata:
            return {"error": "Endpoint not found or missing metadata"}
        
        meta = endpoint.metadata
        
        return {
            "caching": {
                "recommended_ttl": meta.cache_ttl_seconds,
                "cache_priority": "high" if meta.rate_limit_category == RateLimitCategory.CACHE_FRIENDLY else "medium"
            },
            "rate_limiting": {
                "category": meta.rate_limit_category.value,
                "burst_friendly": meta.rate_limit_category in [RateLimitCategory.BATCH_OPTIMIZED, RateLimitCategory.CACHE_FRIENDLY]
            },
            "ai_training": {
                "priority": meta.ai_training_priority,
                "critical_for_training": meta.ai_training_priority >= 4
            }
        }
    
    def list_all_endpoints(self) -> Dict[str, Dict[str, Any]]:
        """List all endpoints with their key information."""
        result = {}
        for name, endpoint in self.endpoints.items():
            result[name] = {
                "path": endpoint.path,
                "method": endpoint.method,
                "description": endpoint.description,
                "required_params": endpoint.required_params,
                "optional_params": endpoint.optional_params,
                "category": endpoint.metadata.category.value if endpoint.metadata else "unknown",
                "rate_limit_category": endpoint.metadata.rate_limit_category.value if endpoint.metadata else "unknown",
                "ai_priority": endpoint.metadata.ai_training_priority if endpoint.metadata else 0
            }
        return result


# Global registry instance
polygon_registry = PolygonEndpointRegistry()


def get_endpoint_registry() -> PolygonEndpointRegistry:
    """Get the global endpoint registry instance."""
    return polygon_registry


if __name__ == "__main__":
    # Test the registry
    registry = get_endpoint_registry()
    
    print(f"Total endpoints registered: {len(registry.endpoints)}")
    
    # Test URL building
    try:
        url = registry.build_url(
            "stock_aggregates",
            path_params={
                "ticker": "AAPL",
                "multiplier": 1,
                "timespan": "day",
                "from_": "2023-01-01",
                "to": "2023-12-31"
            },
            query_params={"adjusted": "true", "limit": 5000}
        )
        print(f"Built URL: {url}")
    except Exception as e:
        print(f"URL building error: {e}")
    
    # Show endpoints
    endpoints = registry.list_all_endpoints()
    print(f"\nRegistered endpoints:")
    for name, info in endpoints.items():
        print(f"  {name}: {info['description']}")
