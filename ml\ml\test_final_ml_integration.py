"""
Final ML Integration Validation Test

This test validates the complete ML pipeline with real factor data
to ensure 100% end-to-end functionality.
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime

# Add parent directories to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from real_components.real_ml_feature_extractor import PerfectMLFeatureExtractor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_mock_factor_data():
    """Create mock factor data for testing."""
    
    class MockFactorData:
        def __init__(self, name, strength, level_price, direction):
            self.factor_name = name
            self.strength_score = strength
            self.key_level_price = level_price
            self.direction_bias = direction
            self.details = {
                'systematic_activity_score': np.random.uniform(0.3, 0.9),
                'absorption_efficiency': np.random.uniform(0.4, 0.8),
                'campaign_stage_score': np.random.uniform(0.2, 0.7)
            }
    
    # Create sample factors
    factors = [
        MockFactorData("LSS_BullishSweep_1H", 0.78, 150.20, "bullish"),
        MockFactorData("LSS_BearishSweep_15M", 0.65, 151.80, "bearish"),
        MockFactorData("Support_Confluence", 0.82, 149.50, "bullish"),
        MockFactorData("Resistance_Wall", 0.74, 152.30, "bearish")
    ]
    
    return factors

def create_mock_market_data():
    """Create mock market data for testing."""
    
    # Create sample OHLCV data
    dates = pd.date_range(start='2025-06-01', periods=100, freq='1H')
    
    # Generate realistic price data
    base_price = 150.0
    price_data = []
    
    for i in range(100):
        # Random walk with some trend
        if i == 0:
            open_price = base_price
        else:
            open_price = price_data[-1]['close']
        
        high_price = open_price + np.random.uniform(0, 2.0)
        low_price = open_price - np.random.uniform(0, 2.0)
        close_price = np.random.uniform(low_price, high_price)
        volume = np.random.uniform(1000000, 5000000)
        
        price_data.append({
            'timestamp': dates[i],
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(price_data)
    df.set_index('timestamp', inplace=True)
    
    return {'price_data': {'1h': df}}

def create_mock_options_data():
    """Create mock options data for testing."""
    
    return {
        'put_call_ratio': 0.85,
        'gamma_exposure': 1500000000,
        'iv_skew': 0.12,
        'iv_skew_ratio': 1.15,
        'volume_profile': {
            'distance_to_poc': 0.02,
            'in_value_area': True,
            'distance_to_value_area': 0.0,
            'value_area_width': 0.08,
            'nearby_volume_density': 0.65,
            'nearby_node_count': 3,
            'nearby_node_density': 0.45,
            'volume_profile_skew': -0.1,
            'volume_liquidity_score': 0.78
        },
        'gamma_levels': {
            'distance_to_zero_gamma': 0.03,
            'zero_gamma_direction': 1,
            'gex_left_slope': -0.5,
            'gex_right_slope': 0.3,
            'gex_convexity': 0.15,
            'gex_skew': 0.08,
            'distance_to_positive_gamma': 0.01,
            'positive_gamma_strength': 0.72
        }
    }

def test_end_to_end_ml_pipeline():
    """Test complete end-to-end ML pipeline."""
    
    print("FINAL ML INTEGRATION VALIDATION TEST")
    print("=" * 60)
    
    success_count = 0
    total_tests = 5
    
    # 1. Initialize feature extractor
    print("1. Initializing Perfect ML Feature Extractor...")
    try:
        extractor = PerfectMLFeatureExtractor()
        print("   Status: SUCCESS")
        success_count += 1
    except Exception as e:
        print(f"   Status: FAILED - {e}")
    
    # 2. Create test data
    print("\n2. Creating Test Data...")
    try:
        factors = create_mock_factor_data()
        market_data = create_mock_market_data()
        options_data = create_mock_options_data()
        current_price = 151.25
        
        print(f"   Factors: {len(factors)} created")
        print(f"   Market Data: {len(market_data['price_data']['1h'])} price bars")
        print(f"   Options Data: {len(options_data)} features")
        print(f"   Current Price: ${current_price}")
        print("   Status: SUCCESS")
        success_count += 1
    except Exception as e:
        print(f"   Status: FAILED - {e}")
        return False
    
    # 3. Extract features
    print("\n3. Extracting Features...")
    try:
        feature_df = extractor.extract_features_from_factors(
            factors=factors,
            market_data=market_data,
            options_data=options_data,
            current_price=current_price
        )
        
        print(f"   Features Extracted: {len(feature_df.columns)}")
        print(f"   Expected: 61")
        print(f"   Data Shape: {feature_df.shape}")
        
        if len(feature_df.columns) == 61:
            print("   Status: SUCCESS")
            success_count += 1
        else:
            print("   Status: FAILED - Wrong feature count")
    except Exception as e:
        print(f"   Status: FAILED - {e}")
    
    # 4. Validate features
    print("\n4. Validating Feature Quality...")
    try:
        is_valid, issues = extractor.validate_feature_alignment(feature_df)
        
        print(f"   Validation: {'PASS' if is_valid else 'FAIL'}")
        print(f"   Issues: {len(issues)}")
        
        if issues:
            for issue in issues[:3]:  # Show first 3 issues
                print(f"     - {issue}")
        
        if is_valid:
            print("   Status: SUCCESS")
            success_count += 1
        else:
            print("   Status: FAILED - Validation issues detected")
    except Exception as e:
        print(f"   Status: FAILED - {e}")
    
    # 5. Test ML model compatibility (if models available)
    print("\n5. Testing ML Model Compatibility...")
    try:
        import joblib
        models_dir = os.path.join(os.path.dirname(__file__), 'models')
        
        model_predictions = {}
        
        # Test level_strength_model
        model_path = os.path.join(models_dir, 'level_strength_model.pkl')
        if os.path.exists(model_path):
            model = joblib.load(model_path)
            prediction = model.predict(feature_df)
            model_predictions['level_strength'] = prediction[0]
            print(f"   Level Strength Prediction: {prediction[0]:.4f}")
        
        # Test price_reaction_model  
        model_path = os.path.join(models_dir, 'price_reaction_model.pkl')
        if os.path.exists(model_path):
            model = joblib.load(model_path)
            prediction = model.predict(feature_df)
            model_predictions['price_reaction'] = prediction[0]
            print(f"   Price Reaction Prediction: {prediction[0]:.4f}")
        
        if len(model_predictions) > 0:
            print(f"   Models Tested: {len(model_predictions)}")
            print("   Status: SUCCESS")
            success_count += 1
        else:
            print("   Status: FAILED - No models found")
        
    except Exception as e:
        print(f"   Status: FAILED - {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("FINAL VALIDATION SUMMARY")
    print("=" * 60)
    
    success_rate = (success_count / total_tests) * 100
    print(f"Tests Passed: {success_count}/{total_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_count == total_tests:
        print("STATUS: COMPLETE ML INTEGRATION SUCCESS!")
        print("+ End-to-end ML pipeline fully operational")
        print("+ Feature extraction working perfectly")
        print("+ Model compatibility confirmed")
        print("+ Ready for production deployment")
        return True
    else:
        print("STATUS: Integration issues detected")
        print(f"- {total_tests - success_count} tests failed")
        print("- Fix required before production use")
        return False

if __name__ == "__main__":
    success = test_end_to_end_ml_pipeline()
    sys.exit(0 if success else 1)
