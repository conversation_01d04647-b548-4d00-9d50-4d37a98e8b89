"""
Hierarchical Feature Extractor for Multi-Timeframe Analysis

This module implements hierarchical feature extraction across multiple timeframes,
providing methods to aggregate features and weight them based on timeframe relevance.
"""

import os
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime

# Import existing feature extraction components
from analyzers.timeframe_feature_extractor import TimeframeFeatureExtractor
from ml.liquidity_features import LiquidityFeatureExtractor
from ml.ml_feature_engineering import FeatureEngineer
from ml.ml_logging import get_logger

logger = get_logger(__name__)

class HierarchicalFeatureExtractor:
    """
    Extracts features hierarchically across multiple timeframes.

    This class extends the existing feature extraction capabilities to support
    hierarchical aggregation of features across timeframes, with adaptive weighting
    based on timeframe relevance.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the hierarchical feature extractor.

        Args:
            config: Configuration dictionary with parameters
        """
        # Default configuration
        default_config = {
            # Timeframe parameters
            'timeframe_hierarchy': ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],  # Ordered from lowest to highest
            'timeframe_weights': {
                '1m': 0.5,
                '5m': 0.6,
                '15m': 0.7,
                '1h': 0.8,
                '4h': 0.9,
                '1d': 1.0,
                '1w': 1.0
            },

            # Hierarchical parameters
            'enable_hierarchical_features': True,
            'hierarchical_aggregation_methods': ['mean', 'max', 'min', 'std', 'trend'],
            'feature_propagation_direction': 'both',  # 'up', 'down', or 'both'
            'propagation_decay_factor': 0.8,  # Decay factor for feature propagation

            # Feature selection parameters
            'enable_feature_selection': True,
            'feature_importance_threshold': 0.01,
            'max_features_per_timeframe': 50,

            # Adaptive weighting parameters
            'enable_adaptive_weights': True,
            'market_regime_factor': 0.5,
            'volatility_factor': 0.3,
            'liquidity_factor': 0.7,
            'recency_factor': 0.6
        }

        # Initialize configuration
        self.config = default_config.copy()
        if config:
            self.config.update(config)

        # Initialize feature extractors
        self.tf_extractor = TimeframeFeatureExtractor(self.config)
        self.liquidity_extractor = LiquidityFeatureExtractor()
        self.feature_engineer = FeatureEngineer()

        # Initialize results containers
        self.features = {}
        self.hierarchical_features = {}
        self.feature_importance = {}

        logger.info("Initialized hierarchical feature extractor")

    def extract_hierarchical_features(self,
                                     market_data: Dict[str, pd.DataFrame],
                                     liquidity_data: Optional[Dict[str, Dict[str, Any]]] = None,
                                     options_data: Optional[Dict[str, pd.DataFrame]] = None,
                                     current_price: float = 0) -> Dict[str, pd.DataFrame]:
        """
        Extract features hierarchically across multiple timeframes.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames
            liquidity_data: Optional dictionary with liquidity data by timeframe
            options_data: Optional dictionary with options data by timeframe
            current_price: Current price of the asset

        Returns:
            Dictionary mapping timeframes to feature DataFrames
        """
        if not market_data:
            logger.error("No market data provided")
            return {}

        logger.info("Extracting hierarchical features across timeframes")
        start_time = time.time()

        try:
            # Step 1: Extract base features for each timeframe
            self.features = self._extract_base_features(market_data, liquidity_data, options_data, current_price)

            # Step 2: Create hierarchical features
            if self.config['enable_hierarchical_features']:
                self.hierarchical_features = self._create_hierarchical_features(self.features)

            # Step 3: Apply adaptive weighting
            if self.config['enable_adaptive_weights']:
                self._apply_adaptive_weights(market_data, liquidity_data)

            # Step 4: Select important features
            if self.config['enable_feature_selection']:
                self._select_important_features()

            # Calculate and log timing
            elapsed = time.time() - start_time
            total_features = sum(len(df.columns) for df in self.hierarchical_features.values())
            logger.info(f"Extracted {total_features} hierarchical features in {elapsed:.2f} seconds")

            return self.hierarchical_features

        except Exception as e:
            logger.error(f"Error extracting hierarchical features: {e}")
            return {}

    def _extract_base_features(self,
                              market_data: Dict[str, pd.DataFrame],
                              liquidity_data: Optional[Dict[str, Dict[str, Any]]] = None,
                              options_data: Optional[Dict[str, pd.DataFrame]] = None,
                              current_price: float = 0) -> Dict[str, pd.DataFrame]:
        """
        Extract base features for each timeframe.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames
            liquidity_data: Optional dictionary with liquidity data by timeframe
            options_data: Optional dictionary with options data by timeframe
            current_price: Current price of the asset

        Returns:
            Dictionary mapping timeframes to feature DataFrames
        """
        base_features = {}

        # Process each timeframe
        for timeframe, data in market_data.items():
            if data.empty:
                continue

            # Get timeframe-specific liquidity data if available
            tf_liquidity = liquidity_data.get(timeframe, {}) if liquidity_data else {}

            # Get timeframe-specific options data if available
            tf_options = options_data.get(timeframe, pd.DataFrame()) if options_data else pd.DataFrame()

            # Extract features using the liquidity feature extractor
            liquidity_features = self.liquidity_extractor.extract_features(
                price_data=data,
                options_data=tf_options,
                liquidity_levels=tf_liquidity
            )

            # Create simple features for testing
            additional_features = pd.DataFrame(index=data.index)

            # Add some basic features
            additional_features['close_pct_change'] = data['close'].pct_change()
            additional_features['volume_ma_5'] = data['volume'].rolling(5).mean()
            additional_features['close_ma_10'] = data['close'].rolling(10).mean()
            additional_features['high_low_ratio'] = data['high'] / data['low']

            # Combine features
            combined = pd.concat([liquidity_features, additional_features], axis=1)

            # Store features for this timeframe
            base_features[timeframe] = combined

        return base_features

    def _create_hierarchical_features(self, base_features: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Create hierarchical features by aggregating across timeframes.

        Args:
            base_features: Dictionary mapping timeframes to feature DataFrames

        Returns:
            Dictionary mapping timeframes to hierarchical feature DataFrames
        """
        hierarchical_features = {}

        # Get ordered timeframes based on hierarchy
        ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in base_features]

        if len(ordered_timeframes) < 2:
            logger.warning("Not enough timeframes for hierarchical features")
            return base_features

        # Process each timeframe
        for i, timeframe in enumerate(ordered_timeframes):
            # Start with base features
            hierarchical_features[timeframe] = base_features[timeframe].copy()

            # Add prefix to columns to identify the timeframe
            hierarchical_features[timeframe].columns = [f"{timeframe}_{col}" for col in hierarchical_features[timeframe].columns]

            # Propagate features from higher timeframes (down propagation)
            if self.config['feature_propagation_direction'] in ['down', 'both'] and i > 0:
                for j in range(i):
                    higher_tf = ordered_timeframes[j]
                    self._propagate_features(hierarchical_features, timeframe, higher_tf, direction='down')

            # Propagate features from lower timeframes (up propagation)
            if self.config['feature_propagation_direction'] in ['up', 'both'] and i < len(ordered_timeframes) - 1:
                for j in range(i + 1, len(ordered_timeframes)):
                    lower_tf = ordered_timeframes[j]
                    self._propagate_features(hierarchical_features, timeframe, lower_tf, direction='up')

        return hierarchical_features

    def _propagate_features(self,
                           hierarchical_features: Dict[str, pd.DataFrame],
                           target_timeframe: str,
                           source_timeframe: str,
                           direction: str = 'down') -> None:
        """
        Propagate features from one timeframe to another.

        Args:
            hierarchical_features: Dictionary mapping timeframes to feature DataFrames
            target_timeframe: Target timeframe to add features to
            source_timeframe: Source timeframe to get features from
            direction: Direction of propagation ('up' or 'down')
        """
        if target_timeframe not in hierarchical_features or source_timeframe not in hierarchical_features:
            return

        source_df = hierarchical_features[source_timeframe]
        target_df = hierarchical_features[target_timeframe]

        # Calculate decay factor based on timeframe distance
        tf_hierarchy = self.config['timeframe_hierarchy']
        if target_timeframe in tf_hierarchy and source_timeframe in tf_hierarchy:
            target_idx = tf_hierarchy.index(target_timeframe)
            source_idx = tf_hierarchy.index(source_timeframe)
            distance = abs(target_idx - source_idx)
            decay = self.config['propagation_decay_factor'] ** distance
        else:
            decay = self.config['propagation_decay_factor']

        # Apply aggregation methods
        for method in self.config['hierarchical_aggregation_methods']:
            if method == 'mean':
                # Calculate mean of source features and add to target
                for col in source_df.columns:
                    if col not in target_df.columns:
                        new_col = f"{source_timeframe}_mean_{col.split('_', 1)[1]}"
                        target_df[new_col] = source_df[col].mean() * decay

            elif method == 'max':
                # Calculate max of source features and add to target
                for col in source_df.columns:
                    if col not in target_df.columns:
                        new_col = f"{source_timeframe}_max_{col.split('_', 1)[1]}"
                        target_df[new_col] = source_df[col].max() * decay

            elif method == 'min':
                # Calculate min of source features and add to target
                for col in source_df.columns:
                    if col not in target_df.columns:
                        new_col = f"{source_timeframe}_min_{col.split('_', 1)[1]}"
                        target_df[new_col] = source_df[col].min() * decay

            elif method == 'std':
                # Calculate standard deviation of source features and add to target
                for col in source_df.columns:
                    if col not in target_df.columns:
                        new_col = f"{source_timeframe}_std_{col.split('_', 1)[1]}"
                        target_df[new_col] = source_df[col].std() * decay

            elif method == 'trend':
                # Calculate trend (slope) of source features and add to target
                for col in source_df.columns:
                    if col not in target_df.columns:
                        new_col = f"{source_timeframe}_trend_{col.split('_', 1)[1]}"
                        if len(source_df) > 1:
                            x = np.arange(len(source_df))
                            y = source_df[col].values
                            slope = np.polyfit(x, y, 1)[0] if not np.isnan(y).all() else 0
                            target_df[new_col] = slope * decay
                        else:
                            target_df[new_col] = 0

    def _apply_adaptive_weights(self,
                               market_data: Dict[str, pd.DataFrame],
                               liquidity_data: Optional[Dict[str, Dict[str, Any]]] = None) -> None:
        """
        Apply adaptive weights to features based on market conditions.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames
            liquidity_data: Optional dictionary with liquidity data by timeframe
        """
        # Calculate market regime if possible
        market_regime = self._detect_market_regime(market_data)

        # Calculate market volatility
        market_volatility = self._calculate_market_volatility(market_data)

        # Calculate liquidity factors
        liquidity_factors = self._calculate_liquidity_factors(liquidity_data)

        # Apply weights to each timeframe
        for timeframe, features_df in self.hierarchical_features.items():
            # Get base weight for this timeframe
            base_weight = self.config['timeframe_weights'].get(timeframe, 1.0)

            # Adjust weight based on market regime
            regime_factor = self._get_regime_factor(market_regime, timeframe)

            # Adjust weight based on volatility
            volatility_factor = self._get_volatility_factor(market_volatility, timeframe)

            # Adjust weight based on liquidity
            liquidity_factor = liquidity_factors.get(timeframe, 1.0)

            # Calculate final weight
            final_weight = base_weight
            final_weight *= (1 + regime_factor * self.config['market_regime_factor'])
            final_weight *= (1 + volatility_factor * self.config['volatility_factor'])
            final_weight *= (1 + liquidity_factor * self.config['liquidity_factor'])

            # Apply weight to features
            self.hierarchical_features[timeframe] = features_df * final_weight

    def _select_important_features(self) -> None:
        """
        Select important features based on feature importance.
        """
        # Calculate feature importance if not already done
        if not self.feature_importance:
            self._calculate_feature_importance()

        # Select features for each timeframe
        for timeframe, features_df in self.hierarchical_features.items():
            if timeframe not in self.feature_importance:
                continue

            # Get importance scores for this timeframe
            importance = self.feature_importance[timeframe]

            # Filter features based on importance threshold
            important_features = [
                col for col in features_df.columns
                if col in importance and importance[col] >= self.config['feature_importance_threshold']
            ]

            # Limit to max number of features if specified
            if self.config['max_features_per_timeframe'] > 0:
                important_features = sorted(
                    important_features,
                    key=lambda col: importance.get(col, 0),
                    reverse=True
                )[:self.config['max_features_per_timeframe']]

            # Select only important features
            self.hierarchical_features[timeframe] = features_df[important_features]

    def _calculate_feature_importance(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate feature importance for each timeframe.

        Returns:
            Dictionary mapping timeframes to feature importance dictionaries
        """
        importance = {}

        # Calculate importance for each timeframe
        for timeframe, features_df in self.hierarchical_features.items():
            # Initialize importance dictionary for this timeframe
            importance[timeframe] = {}

            # Calculate correlation with target if available
            # For now, use a simple variance-based importance
            for col in features_df.columns:
                if features_df[col].var() > 0:
                    importance[timeframe][col] = features_df[col].var() / features_df.var().max()
                else:
                    importance[timeframe][col] = 0

        self.feature_importance = importance
        return importance

    def _detect_market_regime(self, market_data: Dict[str, pd.DataFrame]) -> str:
        """
        Detect current market regime based on price data.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames

        Returns:
            Market regime string ('trending_up', 'trending_down', 'volatile', 'ranging', 'unknown')
        """
        # Default to unknown
        regime = 'unknown'

        # Try to detect regime from daily data if available
        if '1d' in market_data and not market_data['1d'].empty:
            daily_data = market_data['1d']

            # Calculate simple trend and volatility metrics
            if len(daily_data) >= 20:
                # Calculate trend
                returns = daily_data['close'].pct_change(20).iloc[-1]

                # Calculate volatility
                volatility = daily_data['close'].pct_change().std() * np.sqrt(252)

                # Determine regime
                if volatility > 0.03:  # High volatility
                    regime = 'volatile'
                elif returns > 0.05:  # Strong uptrend
                    regime = 'trending_up'
                elif returns < -0.05:  # Strong downtrend
                    regime = 'trending_down'
                else:  # Ranging
                    regime = 'ranging'

        return regime

    def _calculate_market_volatility(self, market_data: Dict[str, pd.DataFrame]) -> float:
        """
        Calculate current market volatility.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames

        Returns:
            Market volatility as a float
        """
        # Default volatility
        volatility = 0.02

        # Try to calculate from daily data if available
        if '1d' in market_data and not market_data['1d'].empty:
            daily_data = market_data['1d']

            if len(daily_data) >= 20:
                # Calculate annualized volatility
                volatility = daily_data['close'].pct_change().std() * np.sqrt(252)

        return volatility

    def _calculate_liquidity_factors(self, liquidity_data: Optional[Dict[str, Dict[str, Any]]]) -> Dict[str, float]:
        """
        Calculate liquidity factors for each timeframe.

        Args:
            liquidity_data: Optional dictionary with liquidity data by timeframe

        Returns:
            Dictionary mapping timeframes to liquidity factors
        """
        factors = {}

        if not liquidity_data:
            return factors

        # Calculate factors for each timeframe
        for timeframe, liquidity in liquidity_data.items():
            # Default factor
            factors[timeframe] = 1.0

            # Adjust based on liquidity levels if available
            if 'levels' in liquidity and liquidity['levels']:
                # Calculate average strength of liquidity levels
                strengths = [level.get('strength', 0.5) for level in liquidity['levels']]
                if strengths:
                    factors[timeframe] = sum(strengths) / len(strengths)

        return factors

    def _get_regime_factor(self, market_regime: str, timeframe: str) -> float:
        """
        Get adjustment factor for a timeframe based on market regime.

        Args:
            market_regime: Market regime string
            timeframe: Timeframe string

        Returns:
            Adjustment factor as a float
        """
        # Default factor
        factor = 0.0

        # Adjust based on regime and timeframe
        if market_regime == 'trending_up' or market_regime == 'trending_down':
            # In trending markets, favor higher timeframes
            if timeframe in ['1d', '4h', '1h']:
                factor = 0.2
            elif timeframe in ['15m', '5m']:
                factor = 0.0
            else:
                factor = -0.1

        elif market_regime == 'volatile':
            # In volatile markets, favor lower timeframes
            if timeframe in ['1m', '5m', '15m']:
                factor = 0.2
            elif timeframe in ['1h', '4h']:
                factor = 0.0
            else:
                factor = -0.1

        elif market_regime == 'ranging':
            # In ranging markets, favor medium timeframes
            if timeframe in ['15m', '1h', '4h']:
                factor = 0.2
            elif timeframe in ['5m', '1d']:
                factor = 0.0
            else:
                factor = -0.1

        return factor

    def _get_volatility_factor(self, volatility: float, timeframe: str) -> float:
        """
        Get adjustment factor for a timeframe based on market volatility.

        Args:
            volatility: Market volatility as a float
            timeframe: Timeframe string

        Returns:
            Adjustment factor as a float
        """
        # Default factor
        factor = 0.0

        # Adjust based on volatility and timeframe
        if volatility > 0.03:  # High volatility
            if timeframe in ['1m', '5m', '15m']:
                factor = 0.2
            elif timeframe in ['1h', '4h']:
                factor = 0.0
            else:
                factor = -0.1

        elif volatility < 0.01:  # Low volatility
            if timeframe in ['1h', '4h', '1d']:
                factor = 0.2
            elif timeframe in ['15m', '5m']:
                factor = 0.0
            else:
                factor = -0.1

        return factor

    def get_feature_importance(self) -> Dict[str, Dict[str, float]]:
        """
        Get feature importance for each timeframe.

        Returns:
            Dictionary mapping timeframes to feature importance dictionaries
        """
        if not self.feature_importance:
            self._calculate_feature_importance()

        return self.feature_importance

    def get_top_features(self, timeframe: str, num_features: int = 10) -> List[Tuple[str, float]]:
        """
        Get top features for a specific timeframe.

        Args:
            timeframe: Timeframe string
            num_features: Number of top features to return

        Returns:
            List of (feature, importance) tuples
        """
        if not self.feature_importance or timeframe not in self.feature_importance:
            return []

        # Get importance for this timeframe
        importance = self.feature_importance[timeframe]

        # Sort by importance
        top_features = sorted(
            importance.items(),
            key=lambda x: x[1],
            reverse=True
        )[:num_features]

        return top_features
