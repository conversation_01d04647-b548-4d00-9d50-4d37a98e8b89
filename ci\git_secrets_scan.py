#!/usr/bin/env python3
"""
Git Secrets Scanner - Financial API Security
Scans for hardcoded secrets in git history and current files
"""

import os
import subprocess
import re
import sys
from pathlib import Path

class GitSecretsScanner:
    """Comprehensive git secrets detection for financial APIs"""
    
    def __init__(self):
        self.patterns = {
            'schwab_api_key': r'schwab.*api.*[\'"][a-zA-Z0-9]{20,}[\'"]',
            'schwab_secret': r'schwab.*secret.*[\'"][a-zA-Z0-9]{40,}[\'"]',
            'polygon_key': r'polygon.*[\'"][a-zA-Z0-9]{20,}[\'"]',
            'tradier_token': r'tradier.*[\'"]Bearer\s+[a-zA-Z0-9]{30,}[\'"]',
            'api_key_hardcoded': r'api_key\s*=\s*[\'"][a-zA-Z0-9]{15,}[\'"]',
            'bearer_hardcoded': r'<PERSON><PERSON>\s+[a-zA-Z0-9]{20,}(?![\'"\s]*[}])',
            'connection_string': r'(mongodb|postgres|mysql)://[^\\s]+',
            'aws_keys': r'AKIA[0-9A-Z]{16}',
        }
        
        self.violations = []
    
    def scan_git_history(self):
        """Scan entire git history for secrets"""
        print("[SCAN] Checking git history for secrets...")
        
        if not Path('.git').exists():
            print("[INFO] No git repository found - skipping history scan")
            return []
        
        violations = []
        
        try:
            # Get all commits
            result = subprocess.run(
                ['git', 'log', '--pretty=format:%H', '--all'],
                capture_output=True, text=True
            )
            
            if result.returncode != 0:
                print("[INFO] No commits found - clean history")
                return []
            
            commits = result.stdout.strip().split('\n')
            
            for commit in commits[:50]:  # Check last 50 commits
                if not commit:
                    continue
                    
                # Get commit diff
                diff_result = subprocess.run(
                    ['git', 'show', commit, '--format='],
                    capture_output=True, text=True
                )
                
                if diff_result.returncode == 0:
                    diff_content = diff_result.stdout
                    commit_violations = self._scan_content(diff_content, f"commit {commit[:8]}")
                    violations.extend(commit_violations)
        
        except Exception as e:
            print(f"[ERROR] Git history scan failed: {e}")
        
        return violations
    
    def scan_current_files(self):
        """Scan current files for secrets"""
        print("[SCAN] Checking current files for secrets...")
        
        violations = []
        
        # Scan Python files
        for py_file in Path('.').rglob('*.py'):
            if 'venv' in str(py_file) or '__pycache__' in str(py_file):
                continue
                
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                file_violations = self._scan_content(content, str(py_file))
                violations.extend(file_violations)
            
            except Exception as e:
                print(f"[WARNING] Could not scan {py_file}: {e}")
        
        # Scan config files
        config_files = ['.env', 'config.yml', 'settings.yml', '*.ini']
        for pattern in config_files:
            for config_file in Path('.').rglob(pattern):
                if config_file.name == '.env.example':
                    continue  # Skip template
                
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    file_violations = self._scan_content(content, str(config_file))
                    violations.extend(file_violations)
                
                except Exception as e:
                    print(f"[WARNING] Could not scan {config_file}: {e}")
        
        return violations
    
    def _scan_content(self, content, source):
        """Scan content for secret patterns"""
        violations = []
        
        for pattern_name, pattern in self.patterns.items():
            matches = re.finditer(pattern, content, re.IGNORECASE)
            
            for match in matches:
                match_text = match.group()
                
                # Skip template/example patterns
                if any(keyword in match_text.lower() for keyword in [
                    'your_', 'example', 'placeholder', 'template', 'xxx',
                    'f\'bearer', 'f"bearer', '{access_token}', '{mcp_token}',
                    'bearer {', '= f\'bearer', '= f"bearer', 'agents.schwab'
                ]):
                    continue
                
                # Skip code patterns that are legitimate
                if any(code_pattern in match_text.lower() for code_pattern in [
                    'authorization\'] = f\'bearer', 'authorization"] = f"bearer',
                    'authorization\']: f\'bearer', 'bearer {token}', 'bearer {access',
                    'agents\\schwab_data_agent', 'agents.schwab_data_agent'
                ]):
                    continue
                
                violations.append({
                    'source': source,
                    'pattern': pattern_name,
                    'match': match_text[:50] + "..." if len(match_text) > 50 else match_text,
                    'line': content[:match.start()].count('\n') + 1
                })
        
        return violations
    
    def generate_rotation_script(self):
        """Generate key rotation script if secrets found"""
        rotation_script = '''#!/bin/bash
# API Key Rotation Script - Execute if secrets detected in git history

echo "=== API KEY ROTATION REQUIRED ==="
echo "Secrets detected in git history - rotating all keys"

# 1. Schwab API - Regenerate keys
echo "1. Log into https://developer.schwab.com/"
echo "2. Revoke current API key and secret"
echo "3. Generate new API credentials"
echo "4. Update .env file with new credentials"

# 2. Polygon API - Rotate key
echo "5. Log into https://polygon.io/dashboard"
echo "6. Regenerate API key"
echo "7. Update POLYGON_API_KEY in .env"

# 3. Tradier - New token
echo "8. Log into https://developer.tradier.com/"
echo "9. Generate new sandbox token"
echo "10. Update TRADIER_TOKEN in .env"

# 4. Clean git history (DESTRUCTIVE - use with caution)
echo "=== WARNING: Consider cleaning git history ==="
echo "git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch .env' --prune-empty --tag-name-filter cat -- --all"
echo "git push origin --force --all"

echo "=== MANUAL VERIFICATION ==="
echo "Test all API connections after rotation"
'''
        
        with open('rotate_api_keys.sh', 'w') as f:
            f.write(rotation_script)
        
        print("[CREATED] rotate_api_keys.sh - manual key rotation script")
    
    def run_scan(self):
        """Execute complete secrets scan"""
        print("=== GIT SECRETS SCAN ===")
        
        all_violations = []
        
        # Scan git history
        history_violations = self.scan_git_history()
        all_violations.extend(history_violations)
        
        # Scan current files
        current_violations = self.scan_current_files()
        all_violations.extend(current_violations)
        
        # Report results
        if all_violations:
            print(f"\n[CRITICAL] {len(all_violations)} potential secrets detected!")
            
            for violation in all_violations:
                print(f"  {violation['source']} (line {violation['line']})")
                print(f"    Pattern: {violation['pattern']}")
                print(f"    Match: {violation['match']}")
                print()
            
            self.generate_rotation_script()
            print("\n[ACTION] Execute ./rotate_api_keys.sh to rotate compromised keys")
            return False
        
        else:
            print("\n[OK] No secrets detected in git history or current files")
            return True

if __name__ == "__main__":
    scanner = GitSecretsScanner()
    clean = scanner.run_scan()
    sys.exit(0 if clean else 1)
