"""
ML Performance Dashboard Module

This module provides a web-based dashboard for monitoring ML model performance,
system resource usage, and real-time inference metrics.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import logging
import json
import time
import datetime
from typing import Dict, List, Optional, Union, Any, Tuple
import threading
from collections import deque

import dash
from dash import dcc, html
import dash_bootstrap_components as dbc
from dash.dependencies import Input, Output, State
import plotly.graph_objs as go
import pandas as pd
import numpy as np

from ml_logging import get_logger
from ml_integration import get_ml_integration
from ml_config_manager import ConfigManager

logger = get_logger(__name__)

class MLPerformanceDashboard:
    """
    Machine Learning Performance Dashboard
    
    This class provides a web-based dashboard for monitoring model performance,
    system resource usage, and real-time metrics.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the ML performance dashboard.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.ml_integration = get_ml_integration()
        
        # Dashboard configuration
        self.port = self.config_manager.get("dashboard_port", 8050)
        self.host = self.config_manager.get("dashboard_host", "127.0.0.1")
        self.debug = self.config_manager.get("dashboard_debug", False)
        
        # Performance data storage
        self.max_history = self.config_manager.get("max_metrics_history", 1000)
        self.metrics_history = {
            "inference_time": deque(maxlen=self.max_history),
            "memory_usage": deque(maxlen=self.max_history),
            "request_count": deque(maxlen=self.max_history),
            "timestamps": deque(maxlen=self.max_history)
        }
        self.model_metrics = {}
        
        # Dashboard app
        self.app = dash.Dash(
            __name__,
            external_stylesheets=[dbc.themes.BOOTSTRAP],
            title="ML Performance Dashboard"
        )
        self.setup_layout()
        self.setup_callbacks()
        
        # Update thread
        self.update_active = False
        self.update_thread = None
        self.update_interval = self.config_manager.get("metrics_update_interval", 5)  # seconds
        
        logger.info("ML Performance Dashboard initialized")
    
    def setup_layout(self) -> None:
        """Set up the dashboard layout."""
        self.app.layout = dbc.Container(
            [
                dbc.Row(
                    dbc.Col(
                        html.H1("ML Performance Dashboard", className="text-center my-4"),
                        width=12
                    )
                ),
                
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader("System Overview"),
                                    dbc.CardBody(
                                        [
                                            html.Div(id="system-stats"),
                                            html.Div(
                                                [
                                                    html.Button(
                                                        "Refresh",
                                                        id="refresh-button",
                                                        className="btn btn-primary mt-3"
                                                    )
                                                ],
                                                className="d-flex justify-content-end"
                                            )
                                        ]
                                    )
                                ],
                                className="mb-4"
                            ),
                            width=12
                        )
                    ]
                ),
                
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader("Inference Time (ms)"),
                                    dbc.CardBody(
                                        dcc.Graph(id="inference-time-graph")
                                    )
                                ],
                                className="mb-4"
                            ),
                            width=6
                        ),
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader("Memory Usage (MB)"),
                                    dbc.CardBody(
                                        dcc.Graph(id="memory-usage-graph")
                                    )
                                ],
                                className="mb-4"
                            ),
                            width=6
                        )
                    ]
                ),
                
                dbc.Row(
                    [
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader("Request Count"),
                                    dbc.CardBody(
                                        dcc.Graph(id="request-count-graph")
                                    )
                                ],
                                className="mb-4"
                            ),
                            width=6
                        ),
                        dbc.Col(
                            dbc.Card(
                                [
                                    dbc.CardHeader("Model Performance"),
                                    dbc.CardBody(
                                        [
                                            dbc.Row(
                                                [
                                                    dbc.Col(
                                                        dcc.Dropdown(
                                                            id="model-dropdown",
                                                            placeholder="Select a model",
                                                            className="mb-3"
                                                        ),
                                                        width=6
                                                    )
                                                ]
                                            ),
                                            dcc.Graph(id="model-performance-graph")
                                        ]
                                    )
                                ],
                                className="mb-4"
                            ),
                            width=6
                        )
                    ]
                ),
                
                dbc.Row(
                    dbc.Col(
                        dbc.Card(
                            [
                                dbc.CardHeader("Active Models"),
                                dbc.CardBody(
                                    html.Div(id="active-models-table")
                                )
                            ],
                            className="mb-4"
                        ),
                        width=12
                    )
                ),
                
                # Hidden components for state
                dcc.Interval(
                    id="interval-component",
                    interval=5000,  # ms
                    n_intervals=0
                ),
                dcc.Store(id="metrics-store"),
                
                # Footer
                dbc.Row(
                    dbc.Col(
                        html.Footer(
                            f"ML Performance Dashboard v1.0 - Last updated: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                            className="text-center text-muted py-3"
                        ),
                        width=12
                    )
                )
            ],
            fluid=True,
            className="mt-4"
        )
    
    def setup_callbacks(self) -> None:
        """Set up the dashboard callbacks."""
        
        # Update metrics on interval or refresh button
        @self.app.callback(
            Output("metrics-store", "data"),
            [
                Input("interval-component", "n_intervals"),
                Input("refresh-button", "n_clicks")
            ]
        )
        def update_metrics(n_intervals, n_clicks):
            # Fetch latest metrics data
            return self._get_latest_metrics()
        
        # Update system stats
        @self.app.callback(
            Output("system-stats", "children"),
            [Input("metrics-store", "data")]
        )
        def update_system_stats(data):
            if not data:
                return html.Div("No data available")
            
            # Extract system stats from data
            active_models = data.get("active_models", [])
            memory_usage = data.get("current_memory_usage", 0)
            total_requests = data.get("total_requests", 0)
            
            # Create stats cards
            stats_cards = dbc.Row(
                [
                    dbc.Col(
                        dbc.Card(
                            [
                                html.H3(f"{len(active_models)}"),
                                html.P("Active Models")
                            ],
                            className="text-center p-3"
                        ),
                        width=4
                    ),
                    dbc.Col(
                        dbc.Card(
                            [
                                html.H3(f"{memory_usage:.1f} MB"),
                                html.P("Memory Usage")
                            ],
                            className="text-center p-3"
                        ),
                        width=4
                    ),
                    dbc.Col(
                        dbc.Card(
                            [
                                html.H3(f"{total_requests}"),
                                html.P("Total Requests")
                            ],
                            className="text-center p-3"
                        ),
                        width=4
                    )
                ]
            )
            
            # Add status indicator
            status_row = dbc.Row(
                dbc.Col(
                    html.Div(
                        [
                            html.Span("System Status: ", className="mr-2"),
                            html.Span(
                                "Active",
                                className="bg-success text-white px-2 py-1 rounded"
                            ) if data.get("system_active", False) else
                            html.Span(
                                "Inactive",
                                className="bg-danger text-white px-2 py-1 rounded"
                            )
                        ],
                        className="mt-3"
                    )
                )
            )
            
            return html.Div([stats_cards, status_row])
        
        # Update inference time graph
        @self.app.callback(
            Output("inference-time-graph", "figure"),
            [Input("metrics-store", "data")]
        )
        def update_inference_graph(data):
            if not data or "inference_time_history" not in data:
                return go.Figure()
            
            times = data["inference_time_history"]
            timestamps = data["timestamps"]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=list(range(len(times))),
                y=times,
                mode="lines",
                name="Inference Time",
                line={"color": "#636EFA"}
            ))
            
            fig.update_layout(
                margin=dict(l=20, r=20, t=20, b=20),
                height=300,
                yaxis_title="Time (ms)"
            )
            
            return fig
        
        # Update memory usage graph
        @self.app.callback(
            Output("memory-usage-graph", "figure"),
            [Input("metrics-store", "data")]
        )
        def update_memory_graph(data):
            if not data or "memory_usage_history" not in data:
                return go.Figure()
            
            memory = data["memory_usage_history"]
            timestamps = data["timestamps"]
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=list(range(len(memory))),
                y=memory,
                mode="lines",
                name="Memory Usage",
                line={"color": "#EF553B"}
            ))
            
            fig.update_layout(
                margin=dict(l=20, r=20, t=20, b=20),
                height=300,
                yaxis_title="Memory (MB)"
            )
            
            return fig
        
        # Update request count graph
        @self.app.callback(
            Output("request-count-graph", "figure"),
            [Input("metrics-store", "data")]
        )
        def update_request_graph(data):
            if not data or "request_count_history" not in data:
                return go.Figure()
            
            counts = data["request_count_history"]
            timestamps = data["timestamps"]
            
            fig = go.Figure()
            fig.add_trace(go.Bar(
                x=list(range(len(counts))),
                y=counts,
                name="Request Count",
                marker_color="#00CC96"
            ))
            
            fig.update_layout(
                margin=dict(l=20, r=20, t=20, b=20),
                height=300,
                yaxis_title="Requests"
            )
            
            return fig
        
        # Update model dropdown
        @self.app.callback(
            Output("model-dropdown", "options"),
            [Input("metrics-store", "data")]
        )
        def update_model_dropdown(data):
            if not data:
                return []
            
            active_models = data.get("active_models", [])
            return [{"label": model, "value": model} for model in active_models]
        
        # Update model performance graph
        @self.app.callback(
            Output("model-performance-graph", "figure"),
            [
                Input("metrics-store", "data"),
                Input("model-dropdown", "value")
            ]
        )
        def update_model_performance(data, selected_model):
            if not data or not selected_model or "model_metrics" not in data:
                return go.Figure()
            
            model_data = data["model_metrics"].get(selected_model, {})
            if not model_data:
                return go.Figure()
            
            # Extract model metrics
            inference_times = model_data.get("inference_times", [])
            if not inference_times:
                return go.Figure()
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=list(range(len(inference_times))),
                y=inference_times,
                mode="lines",
                name=f"{selected_model} Inference Time",
                line={"color": "#AB63FA"}
            ))
            
            fig.update_layout(
                margin=dict(l=20, r=20, t=20, b=20),
                height=300,
                yaxis_title="Time (ms)"
            )
            
            return fig
        
        # Update active models table
        @self.app.callback(
            Output("active-models-table", "children"),
            [Input("metrics-store", "data")]
        )
        def update_active_models_table(data):
            if not data or "model_metrics" not in data:
                return html.Div("No active models")
            
            model_metrics = data["model_metrics"]
            if not model_metrics:
                return html.Div("No active models")
            
            # Create table header
            header = html.Thead(html.Tr([
                html.Th("Model ID"),
                html.Th("Avg. Inference Time (ms)"),
                html.Th("Avg. Memory Usage (MB)"),
                html.Th("Request Count"),
                html.Th("Last Update")
            ]))
            
            # Create table rows
            rows = []
            for model_id, metrics in model_metrics.items():
                avg_inference = metrics.get("avg_inference_time", 0)
                avg_memory = metrics.get("avg_memory_usage", 0)
                request_count = metrics.get("request_count", 0)
                last_update = metrics.get("last_update", "N/A")
                
                row = html.Tr([
                    html.Td(model_id),
                    html.Td(f"{avg_inference:.2f}"),
                    html.Td(f"{avg_memory:.1f}"),
                    html.Td(f"{request_count}"),
                    html.Td(last_update)
                ])
                rows.append(row)
            
            table_body = html.Tbody(rows)
            
            return dbc.Table(
                [header, table_body],
                bordered=True,
                hover=True,
                responsive=True,
                striped=True
            )
    
    def _get_latest_metrics(self) -> Dict[str, Any]:
        """
        Get the latest performance metrics.
        
        Returns:
            Dict containing metrics data
        """
        try:
            # Get active models
            active_models = self.ml_integration.get_active_models()
            
            # Get model metrics
            model_metrics_data = self.ml_integration.get_model_metrics()
            
            # Current memory usage
            current_memory = self._get_memory_usage()
            
            # Process metrics for dashboard
            metrics_data = {
                "system_active": True,
                "active_models": active_models,
                "current_memory_usage": current_memory,
                "total_requests": sum(self.metrics_history["request_count"]) if self.metrics_history["request_count"] else 0,
                "inference_time_history": list(self.metrics_history["inference_time"]),
                "memory_usage_history": list(self.metrics_history["memory_usage"]),
                "request_count_history": list(self.metrics_history["request_count"]),
                "timestamps": list(self.metrics_history["timestamps"]),
                "model_metrics": {}
            }
            
            # Process model-specific metrics
            if "summary" in model_metrics_data:
                for model_id, metrics in model_metrics_data["summary"].items():
                    metrics_data["model_metrics"][model_id] = {
                        "avg_inference_time": metrics.get("avg_inference_time_ms", 0),
                        "avg_memory_usage": metrics.get("avg_memory_usage_mb", 0),
                        "request_count": metrics.get("count", 0),
                        "last_update": datetime.datetime.fromtimestamp(
                            metrics.get("last_update", time.time())
                        ).strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    # Get inference times if available
                    if model_id in self.model_metrics:
                        metrics_data["model_metrics"][model_id]["inference_times"] = self.model_metrics[model_id]
            
            return metrics_data
            
        except Exception as e:
            logger.error(f"Error getting metrics: {str(e)}")
            return {
                "system_active": False,
                "error": str(e)
            }
    
    def _get_memory_usage(self) -> float:
        """
        Get current memory usage in MB.
        
        Returns:
            float: Memory usage in MB
        """
        # This is a simplified implementation
        # In a production system, use a proper memory profiler
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return memory_info.rss / (1024 * 1024)  # Convert to MB
    
    def _update_metrics_loop(self) -> None:
        """Background thread for updating metrics."""
        logger.info("Metrics update thread started")
        
        while self.update_active:
            try:
                # Get memory usage
                memory_usage = self._get_memory_usage()
                self.metrics_history["memory_usage"].append(memory_usage)
                
                # Get inference times from models
                model_metrics = self.ml_integration.get_model_metrics()
                
                # Calculate request count
                request_count = 0
                if "summary" in model_metrics:
                    for model_id, metrics in model_metrics["summary"].items():
                        request_count += metrics.get("count", 0)
                        
                        # Store model-specific metrics
                        if model_id not in self.model_metrics:
                            self.model_metrics[model_id] = []
                        
                        if "avg_inference_time_ms" in metrics:
                            self.model_metrics[model_id].append(metrics["avg_inference_time_ms"])
                            # Keep only the last 100 values
                            if len(self.model_metrics[model_id]) > 100:
                                self.model_metrics[model_id] = self.model_metrics[model_id][-100:]
                
                # Update request count and inference time
                self.metrics_history["request_count"].append(request_count)
                
                # Sample inference time (average across models)
                avg_inference = 0
                count = 0
                if "summary" in model_metrics:
                    for _, metrics in model_metrics["summary"].items():
                        if "avg_inference_time_ms" in metrics:
                            avg_inference += metrics["avg_inference_time_ms"]
                            count += 1
                
                if count > 0:
                    avg_inference /= count
                
                self.metrics_history["inference_time"].append(avg_inference)
                
                # Update timestamps
                self.metrics_history["timestamps"].append(time.time())
                
                # Sleep for the interval
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in metrics update loop: {str(e)}")
                time.sleep(10)  # Sleep briefly to prevent rapid logging on error
    
    def start_metrics_update(self) -> bool:
        """
        Start the metrics update thread.
        
        Returns:
            bool: True if update was started successfully
        """
        if self.update_active:
            logger.info("Metrics update already active")
            return True
        
        try:
            self.update_active = True
            self.update_thread = threading.Thread(
                target=self._update_metrics_loop,
                daemon=True
            )
            self.update_thread.start()
            logger.info("Metrics update thread started")
            return True
        except Exception as e:
            logger.error(f"Error starting metrics update: {str(e)}")
            self.update_active = False
            return False
    
    def stop_metrics_update(self) -> bool:
        """
        Stop the metrics update thread.
        
        Returns:
            bool: True if update was stopped successfully
        """
        if not self.update_active:
            return True
        
        try:
            self.update_active = False
            if self.update_thread and self.update_thread.is_alive():
                self.update_thread.join(timeout=5.0)
            logger.info("Metrics update thread stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping metrics update: {str(e)}")
            return False
    
    def run_dashboard(self, debug: Optional[bool] = None, port: Optional[int] = None) -> None:
        """
        Run the dashboard server.
        
        Args:
            debug: Debug mode flag
            port: Server port
        """
        if debug is None:
            debug = self.debug
        
        if port is None:
            port = self.port
        
        # Start metrics update thread
        self.start_metrics_update()
        
        try:
            logger.info(f"Starting ML Performance Dashboard on port {port}")
            self.app.run_server(debug=debug, port=port, host=self.host)
        except Exception as e:
            logger.error(f"Error running dashboard: {str(e)}")
        finally:
            # Stop metrics update thread
            self.stop_metrics_update()
    
    def shutdown(self) -> None:
        """Safely shut down the dashboard."""
        logger.info("Shutting down ML Performance Dashboard")
        self.stop_metrics_update()
        logger.info("ML Performance Dashboard shut down successfully")


# Global dashboard instance
_dashboard = None

def get_ml_dashboard() -> MLPerformanceDashboard:
    """Get the global ML dashboard instance."""
    global _dashboard
    if _dashboard is None:
        _dashboard = MLPerformanceDashboard()
    return _dashboard

def run_dashboard(debug: bool = False, port: Optional[int] = None) -> None:
    """Run the ML performance dashboard."""
    dashboard = get_ml_dashboard()
    dashboard.run_dashboard(debug=debug, port=port)

def shutdown_dashboard() -> None:
    """Safely shut down the dashboard."""
    global _dashboard
    if _dashboard is not None:
        _dashboard.shutdown()
        _dashboard = None
