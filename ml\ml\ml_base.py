"""
Base ML Components for Liquidity Strategy

This module provides base classes for ML components, including
common functionality for all ML modules.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging
import time
import uuid
import os
import json
from typing import Dict, List, Optional, Any, Union, Tuple
import datetime

# Setup logger
from ml_logging import get_logger
logger = get_logger('ml_base')

class MLComponent:
    """Base class for all ML components."""
    
    def __init__(self, name: str, component_type: str):
        """
        Initialize ML component.
        
        Args:
            name: Name of the component
            component_type: Type of component
        """
        self.id = str(uuid.uuid4())
        self.name = name
        self.component_type = component_type
        self.created_at = datetime.datetime.now()
        self.status = 'initialized'
        self.config = {}
        self.metadata = {}
        
        logger.info(f"Initialized {component_type} component: {name} ({self.id})")
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        Initialize the component with config.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            True if initialization successful, False otherwise
        """
        self.config = config
        self.status = 'ready'
        
        logger.info(f"Component {self.name} initialized with config")
        return True
    
    def execute(self, data: Any) -> Dict[str, Any]:
        """
        Execute component functionality.
        
        Args:
            data: Input data
            
        Returns:
            Result dictionary
        """
        start_time = time.time()
        
        # Default implementation does nothing
        result = {
            'component_id': self.id,
            'component_name': self.name,
            'component_type': self.component_type,
            'status': 'executed',
            'execution_time': time.time() - start_time
        }
        
        logger.info(f"Component {self.name} executed in {result['execution_time']:.2f}s")
        return result
    
    def update_metadata(self, metadata: Dict[str, Any]) -> None:
        """
        Update component metadata.
        
        Args:
            metadata: Metadata dictionary
        """
        self.metadata.update(metadata)
        logger.debug(f"Updated metadata for component {self.name}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert component to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            'id': self.id,
            'name': self.name,
            'component_type': self.component_type,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'config': self.config,
            'metadata': self.metadata
        }
    
    def __str__(self) -> str:
        """
        String representation.
        
        Returns:
            String representation
        """
        return f"{self.component_type.capitalize()}Component(name={self.name}, id={self.id})"


class MLPipeline(MLComponent):
    """Pipeline of ML components."""
    
    def __init__(self, name: str):
        """
        Initialize ML pipeline.
        
        Args:
            name: Name of the pipeline
        """
        super().__init__(name, 'pipeline')
        self.components = []
        self.input_transforms = []
        self.output_transforms = []
    
    def add_component(self, component: MLComponent) -> None:
        """
        Add component to pipeline.
        
        Args:
            component: Component to add
        """
        self.components.append(component)
        logger.info(f"Added component {component.name} to pipeline {self.name}")
    
    def add_input_transform(self, transform_func: callable) -> None:
        """
        Add input transformation function.
        
        Args:
            transform_func: Transformation function
        """
        self.input_transforms.append(transform_func)
        logger.info(f"Added input transform to pipeline {self.name}")
    
    def add_output_transform(self, transform_func: callable) -> None:
        """
        Add output transformation function.
        
        Args:
            transform_func: Transformation function
        """
        self.output_transforms.append(transform_func)
        logger.info(f"Added output transform to pipeline {self.name}")
    
    def execute(self, data: Any) -> Dict[str, Any]:
        """
        Execute pipeline.
        
        Args:
            data: Input data
            
        Returns:
            Result dictionary
        """
        start_time = time.time()
        pipeline_results = []
        
        # Apply input transforms
        transformed_data = data
        for transform in self.input_transforms:
            transformed_data = transform(transformed_data)
        
        # Execute components
        current_data = transformed_data
        for i, component in enumerate(self.components):
            logger.info(f"Executing component {i+1}/{len(self.components)}: {component.name}")
            
            try:
                component_result = component.execute(current_data)
                pipeline_results.append(component_result)
                
                # Update data for next component
                if 'result' in component_result:
                    current_data = component_result['result']
            except Exception as e:
                logger.error(f"Error executing component {component.name}: {e}")
                pipeline_results.append({
                    'component_id': component.id,
                    'component_name': component.name,
                    'status': 'error',
                    'error': str(e)
                })
                break
        
        # Apply output transforms
        final_result = current_data
        for transform in self.output_transforms:
            final_result = transform(final_result)
        
        # Create pipeline result
        result = {
            'component_id': self.id,
            'component_name': self.name,
            'component_type': 'pipeline',
            'status': 'executed',
            'execution_time': time.time() - start_time,
            'component_results': pipeline_results,
            'result': final_result
        }
        
        logger.info(f"Pipeline {self.name} executed in {result['execution_time']:.2f}s")
        return result


class MLRegistry:
    """Registry for ML components and models."""
    
    _instance = None
    
    def __new__(cls):
        """Singleton pattern."""
        if cls._instance is None:
            cls._instance = super(MLRegistry, cls).__new__(cls)
            cls._instance.initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialize the registry."""
        if self.initialized:
            return
        
        self.components = {}
        self.models = {}
        self.pipelines = {}
        self.initialized = True
        
        logger.info("Initialized ML Registry")
    
    def register_component(self, component: MLComponent) -> str:
        """
        Register a component.
        
        Args:
            component: Component to register
            
        Returns:
            Component ID
        """
        self.components[component.id] = component
        logger.info(f"Registered component: {component.name} ({component.id})")
        return component.id
    
    def register_model(self, model: Any, model_name: str, model_type: str,
                     metadata: Dict[str, Any] = None, version: Optional[str] = None) -> str:
        """
        Register a model.
        
        Args:
            model: Model instance
            model_name: Name of the model
            model_type: Type of model
            metadata: Optional metadata
            version: Optional version string
            
        Returns:
            Model ID
        """
        model_id = str(uuid.uuid4())
        
        self.models[model_id] = {
            'id': model_id,
            'name': model_name,
            'type': model_type,
            'version': version or datetime.datetime.now().strftime('%Y%m%d_%H%M%S'),
            'created_at': datetime.datetime.now(),
            'model': model,
            'metadata': metadata or {}
        }
        
        logger.info(f"Registered model: {model_name} ({model_id})")
        return model_id
    
    def get_component(self, component_id: str) -> Optional[MLComponent]:
        """
        Get a component by ID.
        
        Args:
            component_id: Component ID
            
        Returns:
            Component instance or None if not found
        """
        return self.components.get(component_id)
    
    def get_model(self, model_id: str) -> Optional[Tuple[Any, Dict[str, Any]]]:
        """
        Get a model by ID.
        
        Args:
            model_id: Model ID
            
        Returns:
            Tuple of (model, metadata) or None if not found
        """
        model_data = self.models.get(model_id)
        if model_data:
            return model_data['model'], model_data['metadata']
        return None
    
    def load_model(self, model_id: str, expected_type: Optional[str] = None) -> Tuple[Any, Dict[str, Any]]:
        """
        Load a model by ID.
        
        Args:
            model_id: Model ID
            expected_type: Optional expected model type
            
        Returns:
            Tuple of (model, metadata)
            
        Raises:
            ValueError: If model not found or type doesn't match
        """
        model_data = self.models.get(model_id)
        if not model_data:
            raise ValueError(f"Model not found: {model_id}")
        
        if expected_type and model_data['type'] != expected_type:
            raise ValueError(f"Model type mismatch: expected {expected_type}, got {model_data['type']}")
        
        return model_data['model'], model_data['metadata']
    
    def list_components(self, component_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List registered components.
        
        Args:
            component_type: Optional component type to filter by
            
        Returns:
            List of component dictionaries
        """
        components = []
        
        for component in self.components.values():
            if component_type and component.component_type != component_type:
                continue
            
            components.append(component.to_dict())
        
        return components
    
    def list_models(self, model_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List registered models.
        
        Args:
            model_type: Optional model type to filter by
            
        Returns:
            List of model dictionaries
        """
        models = []
        
        for model_data in self.models.values():
            if model_type and model_data['type'] != model_type:
                continue
            
            # Don't include the actual model object
            model_info = model_data.copy()
            model_info.pop('model')
            
            models.append(model_info)
        
        return models


# Singleton accessor
def get_registry() -> MLRegistry:
    """
    Get the ML registry instance.
    
    Returns:
        MLRegistry instance
    """
    return MLRegistry()
