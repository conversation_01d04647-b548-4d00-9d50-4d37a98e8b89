{"test_execution_time": "2025-06-26T08:04:52.369120", "overall_metrics": {"total_tests": 12, "passed_tests": 12, "failed_tests": 0, "success_rate_percentage": 100.0, "overall_status": "EXCELLENT"}, "performance_metrics": {"avg_response_time_seconds": 2.745, "max_response_time_seconds": 10.28, "min_response_time_seconds": 2.036, "total_response_measurements": 12}, "category_results": {"Infrastructure Tests": ["TestResult(test_name='Server Availability', passed=True, execution_time=2.0840513706207275, error_message=None, response_data={'status': 'healthy', 'service': 'schwab-http-mcp-server', 'timestamp': **********.504549, 'uptime_seconds': 822.4643926620483}, performance_metrics={'status_code': 200})", "TestResult(test_name='Health Endpoint', passed=True, execution_time=2.0700767040252686, error_message=None, response_data={'status': 'healthy', 'service': 'schwab-http-mcp-server', 'timestamp': **********.5745413, 'uptime_seconds': 824.5343852043152}, performance_metrics={'response_size': 121})"], "Basic Endpoint Tests": ["TestResult(test_name='Quotes Endpoint (SPY)', passed=True, execution_time=2.045262098312378, error_message=None, response_data={'symbol': 'SPY', 'last_price': 463.25, 'bid': 463.2, 'ask': 463.3, 'volume': 45250000, 'timestamp': **********.6190925}, performance_metrics={'data_size': 120})", "TestResult(test_name='Quotes Endpoint (AAPL)', passed=True, execution_time=2.0609495639801025, error_message=None, response_data={'symbol': 'AAPL', 'last_price': 195.89, 'bid': 195.85, 'ask': 195.95, 'volume': 28340000, 'timestamp': **********.6796439}, performance_metrics={'data_size': 123})", "TestResult(test_name='Quotes Endpoint (MSFT)', passed=True, execution_time=2.057650327682495, error_message=None, response_data={'symbol': 'MSFT', 'last_price': 442.78, 'bid': 442.7, 'ask': 442.85, 'volume': 18920000, 'timestamp': 1750939469.7383003}, performance_metrics={'data_size': 122})", "TestResult(test_name='Options Endpoint (SPY)', passed=True, execution_time=2.061682939529419, error_message=None, response_data={'symbol': 'SPY', 'expiration_date': '2025-01-17', 'calls': [{'strike': 460.0, 'bid': 8.5, 'ask': 8.7, 'last': 8.6, 'volume': 1250, 'open_interest': 15420}], 'puts': [{'strike': 460.0, 'bid': 4.8, 'ask': 5.0, 'last': 4.9, 'volume': 890, 'open_interest': 18920}], 'timestamp': 1750939471.8001094}, performance_metrics={'data_size': 295})", "TestResult(test_name='Market Hours Endpoint', passed=True, execution_time=2.0476417541503906, error_message=None, response_data={'market': 'US_EQUITY', 'date': '2025-06-26', 'is_open': True, 'session_hours': {'regular': {'start': '09:30:00', 'end': '16:00:00'}}, 'timestamp': **********.849269}, performance_metrics=None)", "TestResult(test_name='Metrics Endpoint', passed=True, execution_time=2.0355796813964844, error_message=None, response_data={'performance': {'total_requests': 7, 'error_count': 0, 'error_rate_percentage': 0.0, 'requests_per_second': 0.01}, 'system': {'uptime_seconds': 836.85, 'status': 'operational', 'server_type': 'schwab_http_mcp'}, 'endpoints': {'health': 'operational', 'quotes': 'operational', 'options': 'operational', 'market_hours': 'operational', 'metrics': 'operational'}, 'timestamp': **********.8857243}, performance_metrics=None)"], "Performance Tests": ["TestResult(test_name='Response Time', passed=True, execution_time=10.279598236083984, error_message=None, response_data=None, performance_metrics={'avg_response_time': 2.055919647216797, 'max_response_time': 2.0690343379974365, 'min_response_time': 2.0497546195983887, 'total_calls': 5})", "TestResult(test_name='Concurrent Requests', passed=True, execution_time=2.094545841217041, error_message=None, response_data=None, performance_metrics={'success_count': 3, 'total_requests': 3, 'success_rate': 100.0})"], "Error Handling Tests": ["TestResult(test_name='Invalid Symbol Handling', passed=True, execution_time=2.057443618774414, error_message=None, response_data=None, performance_metrics={'status_code': 500})", "TestResult(test_name='Invalid Endpoint Handling', passed=True, execution_time=2.049333095550537, error_message=None, response_data=None, performance_metrics={'status_code': 404})"]}, "detailed_test_results": [{"test_name": "Server Availability", "passed": true, "execution_time": 2.0840513706207275, "error_message": null, "performance_metrics": {"status_code": 200}}, {"test_name": "Health Endpoint", "passed": true, "execution_time": 2.0700767040252686, "error_message": null, "performance_metrics": {"response_size": 121}}, {"test_name": "Quotes Endpoint (SPY)", "passed": true, "execution_time": 2.045262098312378, "error_message": null, "performance_metrics": {"data_size": 120}}, {"test_name": "Quotes Endpoint (AAPL)", "passed": true, "execution_time": 2.0609495639801025, "error_message": null, "performance_metrics": {"data_size": 123}}, {"test_name": "Quotes Endpoint (MSFT)", "passed": true, "execution_time": 2.057650327682495, "error_message": null, "performance_metrics": {"data_size": 122}}, {"test_name": "Options Endpoint (SPY)", "passed": true, "execution_time": 2.061682939529419, "error_message": null, "performance_metrics": {"data_size": 295}}, {"test_name": "Market Hours Endpoint", "passed": true, "execution_time": 2.0476417541503906, "error_message": null, "performance_metrics": null}, {"test_name": "Metrics Endpoint", "passed": true, "execution_time": 2.0355796813964844, "error_message": null, "performance_metrics": null}, {"test_name": "Response Time", "passed": true, "execution_time": 10.279598236083984, "error_message": null, "performance_metrics": {"avg_response_time": 2.055919647216797, "max_response_time": 2.0690343379974365, "min_response_time": 2.0497546195983887, "total_calls": 5}}, {"test_name": "Concurrent Requests", "passed": true, "execution_time": 2.094545841217041, "error_message": null, "performance_metrics": {"success_count": 3, "total_requests": 3, "success_rate": 100.0}}, {"test_name": "Invalid Symbol Handling", "passed": true, "execution_time": 2.057443618774414, "error_message": null, "performance_metrics": {"status_code": 500}}, {"test_name": "Invalid Endpoint Handling", "passed": true, "execution_time": 2.049333095550537, "error_message": null, "performance_metrics": {"status_code": 404}}], "recommendations": ["System performing well - continue monitoring"]}