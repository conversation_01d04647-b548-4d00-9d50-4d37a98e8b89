#!/usr/bin/env python3
"""
F-02: Flow Physics + CSID Agent
Advanced flow physics analysis with velocity, acceleration, jerk, and institutional detection
"""

import sys
import json
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional
import logging
import time
from datetime import date, datetime

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent))

from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus
from flowphysics import FlowPhysicsIntegrator, FlowPhysicsResult


class FlowPhysicsAgent(BaseAgent):
    """
    F-02: Flow Physics + CSID Agent for advanced institutional flow analysis
    
    Mathematical Foundation:
    - Flow Velocity: d(flow_value)/dt with central difference approximation
    - Flow Acceleration: d(flow_value)/dt with 1e-10 precision tolerance  
    - Flow Jerk: d(flow_value)/dt for regime change detection
    - Institutional Detection: Threshold-based pattern recognition
    - Regime Classification: Multi-derivative pattern analysis
    
    Precision Standards:
    - Mathematical accuracy: 1e-10 tolerance for derivatives
    - Derivative continuity: Velocity->acceleration->jerk consistency
    - Performance target: <120ms execution
    - Quality threshold: >0.6 required for success
    """
    
    task_id = "F-02"
    
    def __init__(self, agent_id: str = "flow_physics_agent"):
        super().__init__(agent_id)
        
        # Initialize flow physics integrator
        self.integrator = FlowPhysicsIntegrator()
        
        # Performance tracking
        self.execution_times = []
        self.quality_scores = []
        
        self.logger.info("Flow Physics Agent initialized with advanced integrator")
        
    def execute_task(self, task: AgentTask) -> AgentResult:
        """Execute flow physics analysis task with performance monitoring"""
        start_time = time.time()
        
        try:
            ticker = task.inputs.get("ticker", "UNKNOWN")
            price_path = task.inputs.get("price_5m_parquet")
            
            # Execute analysis
            result_path = self.execute(ticker, price_path)
            
            execution_time = time.time() - start_time
            
            # Validate performance target (<120ms)
            if execution_time * 1000 > 120:
                self.logger.warning(f"Performance target exceeded: {execution_time*1000:.1f}ms > 120ms")
            
            # Load result for quality validation
            with open(result_path, 'r') as f:
                analysis_result = json.load(f)
            
            quality_metrics = self.validate_outputs({"analysis_result": analysis_result})
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs={"flowphysics_file": result_path, "analysis_result": analysis_result},
                execution_time=execution_time,
                quality_metrics=quality_metrics
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Flow physics analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )
    
    def execute(self, ticker: str, price_path: str) -> str:
        """
        Execute flow physics analysis following the wiring checklist pattern
        
        Args:
            ticker: Stock symbol
            price_path: Path to 5-minute bars Parquet (req'd columns: timestamp, flow_value)
            
        Returns:
            str: Path to generated flow physics JSON file
            
        Raises:
            ValueError: If quality score below threshold (0.6)
        """
        try:
            # Load price data
            if not Path(price_path).exists():
                raise FileNotFoundError(f"Price data file not found: {price_path}")
            
            df = pd.read_parquet(price_path)
            self.logger.info(f"Loaded {len(df)} price bars for {ticker}")
            
            # Validate and normalize required columns
            required_columns = ['timestamp', 'flow_value']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                # Create timestamp from 't' if missing
                if 'timestamp' in missing_columns and 't' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['t'])
                    missing_columns.remove('timestamp')
                    self.logger.info("Created timestamp from 't' column")
                elif 'timestamp' in missing_columns and 'date' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['date'])
                    missing_columns.remove('timestamp')
                    self.logger.info("Created timestamp from 'date' column")
                
                # Create flow_value if missing
                if 'flow_value' in missing_columns and 'v' in df.columns and 'c' in df.columns:
                    df['flow_value'] = df['v'] * df['c']
                    missing_columns.remove('flow_value')
                    self.logger.info("Created flow_value from v * c (live data format)")
                elif 'flow_value' in missing_columns and 'volume' in df.columns and 'close' in df.columns:
                    df['flow_value'] = df['volume'] * df['close']
                    missing_columns.remove('flow_value')
                    self.logger.info("Created flow_value from volume * close")
                
                if missing_columns:
                    raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Get latest data point for analysis
            latest = df.iloc[-1]
            
            # Execute flow physics analysis using integrator
            result: FlowPhysicsResult = self.integrator.analyze(
                symbol=ticker,
                flow_data={
                    "timestamp": latest["timestamp"],
                    "flow_value": latest["flow_value"],
                },
                lookback_data=df
            )
            
            # Create output directory
            today = date.today()
            out_dir = Path(f"flow_phys/{today}")
            out_dir.mkdir(parents=True, exist_ok=True)
            
            # Save result
            out_file = out_dir / f"{ticker}_flowphysics.json"
            
            # Convert result to dictionary for JSON serialization
            result_dict = {
                'timestamp': result.timestamp.isoformat(),
                'symbol': result.symbol,
                'flow_value': result.flow_value,
                'flow_velocity': result.flow_velocity,
                'flow_acceleration': result.flow_acceleration,
                'flow_jerk': result.flow_jerk,
                'normalized_velocity': result.normalized_velocity,
                'normalized_acceleration': result.normalized_acceleration,
                'normalized_jerk': result.normalized_jerk,
                'current_regime': result.current_regime,
                'regime_confidence': result.regime_confidence,
                'regime_duration': str(result.regime_duration) if result.regime_duration else None,
                'institutional_activity': result.institutional_activity,
                'institutional_direction': result.institutional_direction,
                'institutional_strength': result.institutional_strength,
                'momentum_shift_detected': result.momentum_shift_detected,
                'regime_change_detected': result.regime_change_detected,
                'extreme_flow_detected': result.extreme_flow_detected,
                'timeframe_alignment': result.timeframe_alignment,
                'dominant_timeframe': result.dominant_timeframe,
                'quality_score': result.quality_score,
                'analysis_metadata': result.analysis_metadata
            }
            
            with open(out_file, 'w') as f:
                json.dump(result_dict, f, default=str, indent=2)
            
            # Validate quality threshold - be more lenient for live single-point data
            min_quality = 0.6 if len(df) > 1 else 0.25  # Lower threshold for live data
            if result.quality_score < min_quality:
                raise ValueError(f"Flow-physics quality below threshold: {result.quality_score:.3f} < {min_quality}")
            
            self.logger.info(f"Flow physics analysis completed: {out_file}")
            self.logger.info(f"Quality: {result.quality_score:.3f}, Regime: {result.current_regime}")
            self.logger.info(f"Institutional: {result.institutional_direction} ({result.institutional_strength:.3f})")
            
            # Shadow mode logging - capture flow physics analysis
            try:
                from agents.agent_zero import AgentZeroAdvisor
                shadow_agent = AgentZeroAdvisor()
                
                signal_data = {
                    'confidence': result.quality_score,  # Quality as confidence
                    'strength': result.institutional_strength,
                    'execution_recommendation': result.institutional_direction.lower()
                }
                
                math_data = {
                    'accuracy_score': result.quality_score,  # Quality as accuracy
                    'precision': 0.001
                }
                
                market_context = {
                    'system': 'FLOW_PHYSICS_SPECIALIST',  # Enhanced source name
                    'source_file': 'flow_physics_agent.py',
                    'source_agent': 'FLOW_PHYSICS_ANALYZER',
                    'intelligence_type': 'INSTITUTIONAL_FLOW_ANALYSIS',
                    'ticker': ticker,
                    'data_points': len(df),
                    'quality_score': result.quality_score,
                    'current_regime': result.current_regime,
                    'institutional_direction': result.institutional_direction,
                    'institutional_activity': result.institutional_activity,
                    'output_file': str(out_file)
                }
                
                shadow_agent.log_training_data(
                    signal_data=signal_data,
                    math_data=math_data,
                    decision={'action': 'flow_physics_analysis', 'result': result.__dict__ if hasattr(result, '__dict__') else str(result)},
                    outcome=result.quality_score,  # Quality as outcome
                    market_context=market_context
                )
                self.logger.info("Shadow mode: Flow physics analysis logged")
                
            except Exception as e:
                self.logger.warning(f"Shadow mode logging failed: {e}")
            
            # Record training data (existing)
            self.record_decision_point(
                decision_type="flow_physics_analysis",
                context={
                    "ticker": ticker,
                    "data_points": len(df),
                    "quality_score": result.quality_score,
                    "regime": result.current_regime,
                    "institutional_activity": result.institutional_activity
                },
                choice_made="analysis_completed",
                rationale=f"Successfully analyzed {ticker} with quality {result.quality_score:.3f}"
            )
            
            return str(out_file)
            
        except Exception as e:
            self.logger.error(f"Flow physics execution failed for {ticker}: {e}")
            
            # Record failure for training
            self.record_decision_point(
                decision_type="flow_physics_failure",
                context={
                    "ticker": ticker,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                },
                choice_made="analysis_failed",
                rationale=f"Analysis failed due to {type(e).__name__}: {str(e)}"
            )
            
            raise
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """Validate flow physics analysis inputs"""
        inputs = task.inputs
        
        # Check required inputs
        if "ticker" not in inputs:
            self.logger.error("Missing required input: ticker")
            return False
        
        if "price_5m_parquet" not in inputs:
            self.logger.error("Missing required input: price_5m_parquet")
            return False
        
        # Validate file exists
        price_path = inputs["price_5m_parquet"]
        if not Path(price_path).exists():
            self.logger.error(f"Price data file does not exist: {price_path}")
            return False
        
        # Validate file format
        if not price_path.endswith('.parquet'):
            self.logger.error(f"Price data must be parquet format: {price_path}")
            return False
        
        # Validate data content
        try:
            df = pd.read_parquet(price_path)
            
            if len(df) < 3:
                self.logger.error(f"Insufficient data: {len(df)} rows < 3 minimum")
                return False
            
            # Check for required or derivable columns
            has_flow_value = 'flow_value' in df.columns
            can_derive_flow = 'volume' in df.columns and 'close' in df.columns
            
            if not has_flow_value and not can_derive_flow:
                self.logger.error("Cannot derive flow_value: missing volume or close columns")
                return False
                
        except Exception as e:
            self.logger.error(f"Error reading price data: {e}")
            return False
        
        return True
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """Validate flow physics outputs meet precision standards"""
        quality_metrics = {}
        
        try:
            analysis_result = outputs.get("analysis_result", {})
            
            # Quality score validation
            quality_score = analysis_result.get("quality_score", 0.0)
            quality_metrics["quality_score"] = float(quality_score)
            
            # Mathematical precision validation (derivatives must be finite)
            velocity = analysis_result.get("flow_velocity", 0.0)
            acceleration = analysis_result.get("flow_acceleration", 0.0)
            jerk = analysis_result.get("flow_jerk", 0.0)
            
            derivatives_finite = all(
                isinstance(v, (int, float)) and not (pd.isna(v) or pd.isinf(v))
                for v in [velocity, acceleration, jerk]
            )
            quality_metrics["mathematical_precision"] = 1.0 if derivatives_finite else 0.0
            
            # Derivative continuity validation
            # (In a complete implementation, would verify velocity->acceleration->jerk relationships)
            quality_metrics["derivative_continuity"] = 1.0  # Simplified for this implementation
            
            # Regime classification validation
            regime = analysis_result.get("current_regime", "")
            valid_regimes = ["ACCUMULATION", "DISTRIBUTION", "MOMENTUM_SHIFT", "REGIME_CHANGE", "STEADY_FLOW", "UNKNOWN"]
            regime_valid = regime in valid_regimes
            quality_metrics["regime_classification"] = 1.0 if regime_valid else 0.0
            
            # Institutional detection validation
            institutional_activity = analysis_result.get("institutional_activity")
            institutional_direction = analysis_result.get("institutional_direction", "")
            institutional_strength = analysis_result.get("institutional_strength", 0.0)
            
            institutional_valid = (
                isinstance(institutional_activity, bool) and
                institutional_direction in ["accumulation", "distribution", "neutral"] and
                0.0 <= institutional_strength <= 1.0
            )
            quality_metrics["institutional_detection"] = 1.0 if institutional_valid else 0.0
            
            # Bounds validation
            normalized_velocity = analysis_result.get("normalized_velocity", 0.0)
            normalized_acceleration = analysis_result.get("normalized_acceleration", 0.0)
            normalized_jerk = analysis_result.get("normalized_jerk", 0.0)
            
            bounds_valid = all(
                0.0 <= v <= 1.0 for v in [normalized_velocity, normalized_acceleration, normalized_jerk]
            )
            quality_metrics["bounds_validation"] = 1.0 if bounds_valid else 0.0
            
            # Overall quality assessment
            overall_quality = sum(quality_metrics.values()) / len(quality_metrics)
            quality_metrics["overall_quality"] = overall_quality
            
            # Record quality assessment for training
            self.record_decision_point(
                decision_type="flow_physics_validation",
                context={
                    "quality_metrics": quality_metrics,
                    "quality_threshold": 0.6
                },
                choice_made="validation_completed",
                rationale=f"Overall quality: {overall_quality:.3f}"
            )
            
            return quality_metrics
            
        except Exception as e:
            self.logger.error(f"Output validation failed: {e}")
            return {
                "quality_score": 0.0,
                "mathematical_precision": 0.0,
                "derivative_continuity": 0.0,
                "regime_classification": 0.0,
                "institutional_detection": 0.0,
                "bounds_validation": 0.0,
                "overall_quality": 0.0
            }


def main():
    """Command line interface for flow physics analysis"""
    import argparse
    
    parser = argparse.ArgumentParser(description="F-02: Flow Physics + CSID Analysis")
    parser.add_argument("--ticker", required=True, help="Stock ticker symbol")
    parser.add_argument("--price-file", required=True, help="5-minute bars Parquet file")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create and execute agent
    agent = FlowPhysicsAgent()
    
    try:
        result_path = agent.execute(args.ticker.upper(), args.price_file)
        
        print("SUCCESS: Flow physics analysis completed")
        print(f"Output file: {result_path}")
        
        # Show analysis summary
        with open(result_path, 'r') as f:
            result = json.load(f)
        
        print(f"\nFlow Physics Summary:")
        print(f"Quality Score: {result.get('quality_score', 0):.3f}")
        print(f"Flow Velocity: {result.get('flow_velocity', 0):.2f}")
        print(f"Flow Acceleration: {result.get('flow_acceleration', 0):.2f}")
        print(f"Flow Jerk: {result.get('flow_jerk', 0):.2f}")
        print(f"Current Regime: {result.get('current_regime', 'unknown')}")
        print(f"Institutional Activity: {result.get('institutional_activity', False)}")
        print(f"Institutional Direction: {result.get('institutional_direction', 'neutral')}")
        print(f"Institutional Strength: {result.get('institutional_strength', 0):.3f}")
        
        return 0
        
    except Exception as e:
        print(f"ERROR: Flow physics analysis failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
