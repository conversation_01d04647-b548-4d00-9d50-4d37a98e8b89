"""
ML Inference Service for Liquidity Strategy

This module provides inference services for ML models, allowing for
asynchronous inference and result caching.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import time
import logging
import uuid
from typing import Dict, List, Optional, Any, Union, Tuple
import threading
import queue
import datetime
import json
import os

# Setup logger
from ml_logging import get_logger
logger = get_logger('ml_inference')

class InferenceResult:
    """
    Class to store and manage inference results.
    """
    
    def __init__(self, model_id: str, data_id: str, result: Optional[Dict[str, Any]] = None,
                status: str = 'pending', error: Optional[str] = None):
        """
        Initialize inference result.
        
        Args:
            model_id: ID of the model used for inference
            data_id: ID of the data used for inference
            result: Optional inference result
            status: Status of inference ('pending', 'running', 'complete', 'error')
            error: Optional error message
        """
        self.id = str(uuid.uuid4())
        self.model_id = model_id
        self.data_id = data_id
        self.result = result or {}
        self.status = status
        self.error = error
        
        self.created_at = datetime.datetime.now()
        self.updated_at = self.created_at
        self.completed_at = None
        
        self.prediction = None
        self.confidence = 0.0
        self.execution_time = 0.0
        
        # If result is provided, extract key metrics
        if result:
            self.process_result(result)
    
    def process_result(self, result: Dict[str, Any]) -> None:
        """
        Process and extract key metrics from result.
        
        Args:
            result: Inference result
        """
        self.result = result
        
        # Extract prediction if available
        if 'prediction' in result:
            self.prediction = result['prediction']
        elif 'patterns' in result:
            self.prediction = result['patterns']
        
        # Extract confidence
        self.confidence = result.get('confidence', 0.0)
        
        # Extract execution time
        self.execution_time = result.get('execution_time', 0.0)
        
        # Update timestamps
        self.updated_at = datetime.datetime.now()
        if self.status == 'complete':
            self.completed_at = self.updated_at
    
    def update_status(self, status: str, error: Optional[str] = None) -> None:
        """
        Update status of inference.
        
        Args:
            status: New status
            error: Optional error message
        """
        self.status = status
        if error:
            self.error = error
        
        self.updated_at = datetime.datetime.now()
        if status == 'complete':
            self.completed_at = self.updated_at
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert to dictionary.
        
        Returns:
            Dictionary representation
        """
        return {
            'id': self.id,
            'model_id': self.model_id,
            'data_id': self.data_id,
            'status': self.status,
            'error': self.error,
            'prediction': self.prediction,
            'confidence': self.confidence,
            'execution_time': self.execution_time,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
    
    def __str__(self) -> str:
        """
        String representation.
        
        Returns:
            String representation
        """
        return f"InferenceResult(id={self.id}, status={self.status}, confidence={self.confidence:.2f})"

class InferenceService:
    """
    Service to manage model inference requests and results.
    """
    
    def __init__(self, max_workers: int = 4, result_ttl: int = 3600):
        """
        Initialize inference service.
        
        Args:
            max_workers: Maximum number of worker threads
            result_ttl: Time-to-live for results in seconds
        """
        self.max_workers = max_workers
        self.result_ttl = result_ttl
        
        self.inference_queue = queue.Queue()
        self.results = {}
        self.models = {}
        
        self.workers = []
        self.running = False
        
        self.result_lock = threading.Lock()
        
        logger.info(f"InferenceService initialized with {max_workers} workers")
    
    def start(self) -> None:
        """Start the inference service."""
        if self.running:
            logger.warning("InferenceService already running")
            return
        
        self.running = True
        
        # Start worker threads
        for i in range(self.max_workers):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"inference-worker-{i}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
        
        # Start cleanup thread
        cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="inference-cleanup",
            daemon=True
        )
        cleanup_thread.start()
        self.workers.append(cleanup_thread)
        
        logger.info(f"InferenceService started with {self.max_workers} workers")
    
    def stop(self) -> None:
        """Stop the inference service."""
        if not self.running:
            logger.warning("InferenceService not running")
            return
        
        self.running = False
        
        # Wait for workers to finish
        for worker in self.workers:
            if worker.is_alive():
                worker.join(timeout=1.0)
        
        logger.info("InferenceService stopped")
    
    def register_model(self, model_id: str, model: Any) -> None:
        """
        Register a model for inference.
        
        Args:
            model_id: ID of the model
            model: Model instance
        """
        self.models[model_id] = model
        logger.info(f"Model registered: {model_id}")
    
    def unregister_model(self, model_id: str) -> None:
        """
        Unregister a model.
        
        Args:
            model_id: ID of the model to unregister
        """
        if model_id in self.models:
            del self.models[model_id]
            logger.info(f"Model unregistered: {model_id}")
    
    def submit_inference(self, model_id: str, data: Any, data_id: Optional[str] = None,
                       priority: int = 0) -> str:
        """
        Submit an inference request.
        
        Args:
            model_id: ID of the model to use
            data: Data for inference
            data_id: Optional ID for the data
            priority: Priority of the request (higher is more important)
            
        Returns:
            ID of the inference request
        """
        if model_id not in self.models:
            logger.warning(f"Model not found: {model_id}")
            result = InferenceResult(
                model_id=model_id,
                data_id=data_id or str(uuid.uuid4()),
                status='error',
                error=f"Model not found: {model_id}"
            )
            self.results[result.id] = result
            return result.id
        
        # Create result object
        result = InferenceResult(
            model_id=model_id,
            data_id=data_id or str(uuid.uuid4()),
            status='pending'
        )
        
        # Store result
        with self.result_lock:
            self.results[result.id] = result
        
        # Submit to queue
        self.inference_queue.put((priority, result.id, model_id, data))
        
        logger.info(f"Inference request submitted: {result.id} (model: {model_id})")
        return result.id
    
    def get_result(self, result_id: str) -> Optional[InferenceResult]:
        """
        Get an inference result.
        
        Args:
            result_id: ID of the result to retrieve
            
        Returns:
            Inference result or None if not found
        """
        with self.result_lock:
            return self.results.get(result_id)
    
    def get_results(self, model_id: Optional[str] = None, 
                  status: Optional[str] = None,
                  limit: int = 100) -> List[InferenceResult]:
        """
        Get inference results.
        
        Args:
            model_id: Optional filter by model ID
            status: Optional filter by status
            limit: Maximum number of results to return
            
        Returns:
            List of inference results
        """
        with self.result_lock:
            results = list(self.results.values())
        
        # Apply filters
        if model_id:
            results = [r for r in results if r.model_id == model_id]
        
        if status:
            results = [r for r in results if r.status == status]
        
        # Sort by creation time (newest first)
        results.sort(key=lambda r: r.created_at, reverse=True)
        
        # Apply limit
        return results[:limit]
    
    def _worker_loop(self) -> None:
        """Worker thread loop."""
        while self.running:
            try:
                # Get item from queue with timeout
                try:
                    priority, result_id, model_id, data = self.inference_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # Get result and model
                with self.result_lock:
                    result = self.results.get(result_id)
                
                if not result:
                    logger.warning(f"Result not found: {result_id}")
                    self.inference_queue.task_done()
                    continue
                
                model = self.models.get(model_id)
                if not model:
                    logger.warning(f"Model not found: {model_id}")
                    result.update_status('error', f"Model not found: {model_id}")
                    self.inference_queue.task_done()
                    continue
                
                # Update status
                result.update_status('running')
                
                # Run inference
                start_time = time.time()
                try:
                    inference_result = model.predict(data)
                    
                    # Update result
                    result.process_result(inference_result)
                    result.update_status('complete')
                    
                    logger.info(f"Inference complete: {result_id} (model: {model_id}, time: {result.execution_time:.2f}s)")
                    
                except Exception as e:
                    logger.error(f"Inference error: {e}")
                    result.update_status('error', str(e))
                
                # Mark task as done
                self.inference_queue.task_done()
                
            except Exception as e:
                logger.error(f"Worker error: {e}")
    
    def _cleanup_loop(self) -> None:
        """Cleanup thread loop."""
        while self.running:
            try:
                # Sleep for a while
                time.sleep(60.0)
                
                # Clean up old results
                now = datetime.datetime.now()
                with self.result_lock:
                    to_remove = []
                    for result_id, result in self.results.items():
                        # Check if result is older than TTL
                        age = (now - result.updated_at).total_seconds()
                        if age > self.result_ttl:
                            to_remove.append(result_id)
                    
                    # Remove old results
                    for result_id in to_remove:
                        del self.results[result_id]
                
                if to_remove:
                    logger.info(f"Cleaned up {len(to_remove)} old results")
                
            except Exception as e:
                logger.error(f"Cleanup error: {e}")

# Singleton instance
_inference_service = None

def get_inference_service() -> InferenceService:
    """
    Get the inference service instance.
    
    Returns:
        InferenceService instance
    """
    global _inference_service
    if _inference_service is None:
        _inference_service = InferenceService()
        _inference_service.start()
    return _inference_service
