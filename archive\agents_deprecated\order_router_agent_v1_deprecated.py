from pathlib import Path
import textwrap, json, datetime
from agents.agent_base import BaseAgent

class ManualBrokerAdapterAgent(BaseAgent):
    task_id = "R-01"
    
    def __init__(self, agent_id="manual_broker_adapter_agent"):
        """Initialize Manual Broker Adapter Agent with Real-Time Enhancement"""
        super().__init__(agent_id)
        
        # Initialize real-time data agent for live bid/ask execution optimization
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.real_time_agent = EnhancedDataAgent()
            self.has_real_time = True
            self.logger.info("Order Router initialized with real-time bid/ask access for optimal execution")
        except ImportError:
            self.real_time_agent = None
            self.has_real_time = False
            self.logger.warning("Real-time agent unavailable - using static execution prices")
    
    def execute_task(self, task):
        """Execute the broker adapter task"""
        # Extract paths from task inputs
        execution_plan_md = task.inputs.get("execution_plan_md")
        unified_analysis_json = task.inputs.get("unified_analysis_json")
        
        return self.execute(execution_plan_md, unified_analysis_json)
    
    def validate_inputs(self, task):
        """Validate task inputs meet requirements"""
        inputs = task.inputs
        required = ["execution_plan_md", "unified_analysis_json"]
        return all(key in inputs for key in required)
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        # Check if ticket was created
        if isinstance(outputs, str) and Path(outputs).exists():
            ticket_size = Path(outputs).stat().st_size
            quality_metrics["file_created"] = 1.0
            quality_metrics["size_valid"] = 1.0 if ticket_size <= 2048 else 0.0
        else:
            quality_metrics["file_created"] = 0.0
            quality_metrics["size_valid"] = 0.0
        
        return quality_metrics
    
    @property
    def today(self):
        """Get today's date string"""
        if hasattr(self, '_today_override'):
            return self._today_override
        return datetime.date.today().strftime("%Y-%m-%d")
    
    @today.setter
    def today(self, value):
        """Allow setting today for testing"""
        self._today_override = value
    
    def log_training(self, data):
        """Log training data for Agent Zero learning"""
        # Simple logging placeholder - in production this would go to training system
        print(f"Training logged: {json.dumps(data, indent=2, default=str)}")
    
    def execute(self, execution_plan_md, unified_analysis_json):
        """Execute order routing with real-time bid/ask optimization"""
        plan = Path(execution_plan_md).read_text()
        ua   = json.loads(Path(unified_analysis_json).read_text())
        qty  = ua.get("risk", {}).get("max_pct_equity", 1)  # placeholder sizing
        side = ua["direction"]               # CALL / PUT
        roi  = ua.get("risk", {}).get("expected_roi", 0.0)  # Handle missing ROI
        ticker = ua.get('ticker', 'UNKNOWN')
        
        # Real-time bid/ask optimization for better execution
        execution_price_guidance = "USE premium in execution_plan.md"
        real_time_info = ""
        
        if self.has_real_time:
            try:
                result = self.real_time_agent.get_market_data([ticker])
                
                if result and result.get("source") == "schwab_broker":
                    ticker_data = result["data"][ticker]
                    
                    if ticker_data.get("is_current_candle"):
                        current_price = ticker_data["last_price"]
                        bid = ticker_data.get("bid")
                        ask = ticker_data.get("ask")
                        
                        if bid and ask:
                            mid_price = (bid + ask) / 2
                            spread = ask - bid
                            spread_percentage = spread / ask * 100
                            
                            # Enhanced execution guidance with live data
                            execution_price_guidance = f"LIVE: Bid=${bid:.2f}, Ask=${ask:.2f}, Mid=${mid_price:.2f}"
                            real_time_info = f"""
        **REAL-TIME EXECUTION DATA**
        Current Price : ${current_price:.2f}
        Live Bid      : ${bid:.2f}
        Live Ask      : ${ask:.2f}
        Mid Price     : ${mid_price:.2f}
        Spread        : ${spread:.2f} ({spread_percentage:.2f}%)
        Optimal Entry : Target between ${bid:.2f} and ${mid_price:.2f} for better fill
        """
                            
                            self.logger.info(f"[{ticker}] Order routing enhanced with live bid/ask: {bid:.2f}/{ask:.2f}")
                        else:
                            real_time_info = f"\n        **REAL-TIME DATA**: Current Price ${current_price:.2f}"
                            self.logger.info(f"[{ticker}] Current price available: ${current_price:.2f}")
                    else:
                        self.logger.info(f"[{ticker}] Using most recent available data for order routing")
                        
            except Exception as e:
                self.logger.warning(f"[{ticker}] Real-time execution optimization failed: {e}")
        
        ticket = textwrap.dedent(f"""
        === MANUAL ORDER TICKET ===
        Date          : {datetime.date.today()}
        Ticker        : {ticker}
        Side          : {side} (long option)
        Quantity      : {qty} contracts   # adjust to stay <=1% equity
        Limit Price   : {execution_price_guidance}
        Min ROI       : {roi:.2f} (floor = 1.75){real_time_info}
        Steps:
          1. Open broker option chain for {ticker}.
          2. Select strike & expiry from execution_plan.md.
          3. Submit LIMIT order at price shown.
          4. Set stop or OCO per plan.
          5. Log fill in tracking spreadsheet.
        Plan reference:
        ----------------
        {plan}
        """).strip()
        out_path = Path(f"tickets/{self.today}/{ua['ticker']}_order_ticket.txt")
        out_path.parent.mkdir(parents=True, exist_ok=True)
        out_path.write_text(ticket, encoding='utf-8')
        
        # Shadow mode logging - capture order routing decisions
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            # Extract decision metrics
            order_confidence = 0.88  # High confidence for order routing
            
            signal_data = {
                'confidence': order_confidence,
                'strength': min(abs(roi), 1.0) if roi else 0.75,  # ROI as strength
                'execution_recommendation': side.lower()
            }
            
            math_data = {
                'accuracy_score': 0.92,  # Order routing accuracy
                'precision': 0.0008
            }
            
            market_context = {
                'system': 'order_router_agent',
                'ticker': ticker,
                'direction': side,
                'quantity': qty,
                'expected_roi': roi,
                'has_real_time_data': self.has_real_time,
                'execution_guidance': execution_price_guidance,
                'ticket_generated': True
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'order_routing', 'direction': side, 'ticket_path': str(out_path)},
                outcome=min(abs(roi), 1.0) if roi else 0.75,  # Expected ROI as outcome
                market_context=market_context
            )
            self.logger.info("Shadow mode: Order routing decision logged")
            
        except Exception as e:
            self.logger.warning(f"Shadow mode logging failed: {e}")
        
        self.log_training({"inputs": {"plan": plan, "ua": ua},
                          "outputs": {"ticket": ticket}})
        return str(out_path)


def main():
    """Command-line interface for ManualBrokerAdapterAgent"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="Manual Broker Adapter Agent")
    parser.add_argument("execution_plan_md", help="Path to execution plan markdown file")
    parser.add_argument("unified_analysis_json", help="Path to unified analysis JSON file")
    
    args = parser.parse_args()
    
    # Validate input files exist
    if not Path(args.execution_plan_md).exists():
        print(f"ERROR: Execution plan file not found: {args.execution_plan_md}")
        return 1
    
    if not Path(args.unified_analysis_json).exists():
        print(f"ERROR: Unified analysis file not found: {args.unified_analysis_json}")
        return 1
    
    # Create agent and execute
    agent = ManualBrokerAdapterAgent()
    
    try:
        ticket_path = agent.execute(args.execution_plan_md, args.unified_analysis_json)
        
        print("SUCCESS: MANUAL ORDER TICKET GENERATED")
        print("=" * 50)
        print(f"Ticket saved to: {ticket_path}")
        
        # Show ticket size info
        ticket_size = Path(ticket_path).stat().st_size
        print(f"Ticket size: {ticket_size} bytes (limit: 2048)")
        
        # Show ticket preview
        print("\nTICKET PREVIEW:")
        print("-" * 30)
        ticket_content = Path(ticket_path).read_text(encoding='utf-8')
        preview_lines = ticket_content.split('\n')[:10]
        for line in preview_lines:
            print(line)
        if len(ticket_content.split('\n')) > 10:
            print("... (truncated)")
        
        print("\nREADY FOR BROKER SUBMISSION")
        return 0
        
    except Exception as e:
        print(f"ERROR: Failed to generate order ticket: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
