#!/bin/bash
# VM Production Runner for CORE
# Applies vm_iso profile and runs multi-ticker analysis with Agent Zero active

source ~/.bash_profile

python - <<'PY'
import utils.profile_loader as P
import subprocess
import sys
import os
import requests

# Apply VM isolation profile
p = P.apply_profile("vm_iso")

# Run multi-ticker analysis
result = subprocess.run([
    sys.executable, "multi_orchestrator.py",
    "--tickers", "AAPL,TSLA,NVDA",
    "--option_prices", "1.55,2.10,3.20",
    "--target_prices", "5.0,6.0,10.0",
    "--source", p["data_source"]
])

# Health check ping
healthcheck_url = os.getenv("HEALTHCHECK_PING_URL")
if healthcheck_url:
    try:
        if result.returncode == 0:
            # Success ping
            requests.get(healthcheck_url, timeout=10)
            print("Health check ping sent (success)")
        else:
            # Failure ping
            requests.get(f"{healthcheck_url}/fail", timeout=10)
            print("Health check ping sent (failure)")
    except Exception as e:
        print(f"Health check ping failed: {e}")
else:
    print("No HEALTHCHECK_PING_URL configured")

PY

echo "VM production run completed. Check fills and logs directories."
