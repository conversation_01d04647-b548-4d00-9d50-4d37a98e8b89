[{"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:36:26.890877", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:36:26.890724", "success": false, "latency_ms": 2429.010299994843, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:36:26.923198", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:36:26.923112", "success": false, "latency_ms": 0.024599998141638935, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:36:26.924231", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:36:26.924117", "success": false, "latency_ms": 0.011199997970834374, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:41:26.926078", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:41:26.925948", "success": false, "latency_ms": 0.03139997716061771, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:41:26.926795", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:41:26.926745", "success": false, "latency_ms": 0.008499977411702275, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:41:26.927472", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:41:26.927351", "success": false, "latency_ms": 0.008000002708286047, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:46:26.929480", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:46:26.929346", "success": false, "latency_ms": 0.022899999748915434, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:46:26.930514", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:46:26.930382", "success": false, "latency_ms": 0.012899981811642647, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:46:26.931702", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:46:26.931553", "success": false, "latency_ms": 0.025500019546598196, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093624", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:46:26.989978", "result": {"timestamp": "2025-06-24T09:46:26.989649", "total_execution_time_ms": 57.29239998618141, "budget_compliance": false, "error": "cannot import name 'get_config' from 'config.settings' (D:\\script-work\\CORE\\config\\settings.py)", "overall_success": false}}]