#!/usr/bin/env python3
"""
Shadow Mode Log Validation
==========================
Validates that training logs match expected format for shadow mode operation
"""

import json
import os
from pathlib import Path
from datetime import datetime

def validate_shadow_mode_logs():
    """Validate shadow mode training logs are properly formatted"""
    
    # Define expected log locations
    core_root = Path(__file__).parent
    agent_zero_logs = core_root / "training_logs" / "AgentZero"
    
    print("SHADOW MODE LOG VALIDATION")
    print("=" * 50)
    
    # Check directory structure
    if not agent_zero_logs.exists():
        print("CRITICAL: AgentZero training directory missing")
        return False
    
    print(f"Training directory exists: {agent_zero_logs}")
    
    # Find training logs
    training_files = list(agent_zero_logs.glob("training_*.json"))
    
    if not training_files:
        print("CRITICAL: No training logs found")
        return False
    
    print(f"Found {len(training_files)} training log files")
    
    # Validate log format
    valid_logs = 0
    required_fields = [
        'timestamp', 'signal_data', 'math_data', 
        'decision', 'outcome', 'agent_version'
    ]
    
    for log_file in training_files[-5:]:  # Check last 5 logs
        try:
            with open(log_file, 'r') as f:
                log_data = json.load(f)
            
            # Check required fields
            missing_fields = [field for field in required_fields if field not in log_data]
            
            if missing_fields:
                print(f"WARNING {log_file.name}: Missing fields: {missing_fields}")
            else:
                valid_logs += 1
                print(f"VALID {log_file.name}: Correct format")
                
        except Exception as e:
            print(f"ERROR {log_file.name}: Error reading - {e}")
    
    # Summary
    print(f"\nVALIDATION SUMMARY:")
    print(f"Valid logs: {valid_logs}/{min(len(training_files), 5)}")
    
    # Test log reading capability
    print(f"\nTESTING LOG READING CAPABILITY:")
    
    try:
        from agents.training_mixin import TrainingMixin
        mixin = TrainingMixin()
        stats = mixin.get_training_stats("AgentZero")
        
        print(f"PASS Training stats readable: {stats['total_records']} records")
        
    except Exception as e:
        print(f"FAIL Training mixin error: {e}")
        return False
    
    # Test Agent Zero initialization
    try:
        from agents.agent_zero import AgentZeroAdvisor
        agent_zero = AgentZeroAdvisor()
        
        print(f"PASS Agent Zero initializes correctly")
        print(f"   Training dir: {agent_zero.training_dir}")
        print(f"   ML available: {agent_zero.ml_available}")
        
    except Exception as e:
        print(f"FAIL Agent Zero error: {e}")
        return False
    
    print(f"\n" + "=" * 50)
    print("SHADOW MODE VALIDATION: PASSED")
    print("Scripts know exactly what logs to look for:")
    print("  Pattern: training_YYYYMMDD_HHMMSS_mmm.json")
    print("  Location: CORE/training_logs/AgentZero/")
    print("  Format: JSON with required fields validated")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    validate_shadow_mode_logs()
