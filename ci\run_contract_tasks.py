#!/usr/bin/env python3
"""
Contract Task Runner for CI Pipeline
Validates contract compliance for specific agent tasks
"""

import json
import yaml
import argparse
import sys
from pathlib import Path

def load_contract(contract_id):
    """Load contract YAML file"""
    contract_path = Path("contracts") / f"{contract_id}.yml"
    if not contract_path.exists():
        raise FileNotFoundError(f"Contract {contract_id} not found at {contract_path}")
    
    with open(contract_path, 'r') as f:
        return yaml.safe_load(f)

def validate_c04_roi_floor(unified_analysis_path, contract):
    """Validate C-04 ROI floor requirement"""
    if not Path(unified_analysis_path).exists():
        print(f"ERROR: Output file not found: {unified_analysis_path}")
        return False
    
    with open(unified_analysis_path, 'r') as f:
        unified = json.load(f)
    
    roi = unified["risk"]["expected_roi"]
    floor = contract["success_criteria"]["roi_floor"]["min_expected_roi"]
    
    if roi < floor:
        print(f"ERROR: ROI {roi} below floor {floor}")
        return False
    
    print(f"SUCCESS: ROI {roi} meets floor requirement {floor}")
    return True

def run_contract_validation(contract_id):
    """Run validation for specific contract"""
    print(f"Validating contract {contract_id}")
    
    try:
        contract = load_contract(contract_id)
        print(f"SUCCESS: Contract {contract_id} loaded successfully")
        
        if contract_id == "C-04":
            # For C-04, validate ROI floor if output exists
            test_output = "outputs/2025-06-14/SPY/unified_analysis.json"
            if Path(test_output).exists():
                success = validate_c04_roi_floor(test_output, contract)
                if not success:
                    return False
            else:
                print(f"INFO: No test output found at {test_output}, skipping ROI validation")
        
        print(f"SUCCESS: Contract {contract_id} validation passed")
        return True
        
    except Exception as e:
        print(f"ERROR: Contract {contract_id} validation failed: {e}")
        return False

def main():
    """Main contract validation runner"""
    parser = argparse.ArgumentParser(description="Run contract task validation")
    parser.add_argument("--only", help="Run only specific contract (e.g., C-04)")
    parser.add_argument("--all", action="store_true", help="Run all contract validations")
    
    args = parser.parse_args()
    
    if args.only:
        contracts = [args.only]
    elif args.all:
        # Discover all contract files
        contract_files = list(Path("contracts").glob("*.yml"))
        contracts = [f.stem for f in contract_files]
    else:
        print("ERROR: Must specify --only <contract_id> or --all")
        sys.exit(1)
    
    print(f"Running contract validation for: {contracts}")
    
    all_passed = True
    for contract_id in contracts:
        success = run_contract_validation(contract_id)
        if not success:
            all_passed = False
    
    if all_passed:
        print("SUCCESS: All contract validations passed")
        sys.exit(0)
    else:
        print("FAILED: Some contract validations failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
