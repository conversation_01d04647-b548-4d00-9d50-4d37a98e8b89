@echo off
REM Liquidity Sweep MCP Server - Windows Service Wrapper
REM Auto-start with Windows and run in background

title Liquidity Sweep MCP Server
cd /d "D:\script-work\CORE\api"

REM Check if already running
tasklist /FI "WINDOWTITLE eq Liquidity Sweep MCP Server*" 2>NUL | find /I /N "cmd.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo MCP Server already running
    timeout /t 3
    exit /b 0
)

echo Starting Liquidity Sweep MCP Server...
echo Server will run in background
echo Close this window to stop the server
echo.

REM Start server with restart on failure
:restart
echo [%date% %time%] Starting MCP Server...
py mcp_server_production.py --config mcp_installation\config\config.json --log-level INFO

REM If server crashes, wait 5 seconds and restart
if %ERRORLEVEL% NEQ 0 (
    echo [%date% %time%] Server stopped. Restarting in 5 seconds...
    timeout /t 5
    goto restart
)

echo [%date% %time%] Server shutdown normally
pause
