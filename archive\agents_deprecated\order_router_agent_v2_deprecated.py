import os
from pathlib import Path
import textwrap, json, datetime
from agents.agent_base import BaseAgent

class ManualBrokerAdapterAgent(BaseAgent):
    task_id = "R-01"
    
    def __init__(self, agent_id="manual_broker_adapter_agent"):
        """Initialize Manual Broker Adapter Agent with MCP Integration"""
        super().__init__(agent_id)
        
        # Initialize real-time data agent for enhanced order routing
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.real_time_agent = EnhancedDataAgent()
            self.has_real_time = True
            self.logger.info("Order Router V2 initialized with real-time execution enhancement")
        except ImportError:
            self.real_time_agent = None
            self.has_real_time = False
            self.logger.warning("Real-time agent unavailable - using static order routing")
    
    def execute_task(self, task):
        """Execute the broker adapter task"""
        # Extract paths from task inputs
        execution_plan_md = task.inputs.get("execution_plan_md")
        unified_analysis_json = task.inputs.get("unified_analysis_json")
        
        return self.execute(execution_plan_md, unified_analysis_json)
    
    def validate_inputs(self, task):
        """Validate task inputs meet requirements"""
        inputs = task.inputs
        required = ["execution_plan_md", "unified_analysis_json"]
        return all(key in inputs for key in required)
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        # Check if ticket was created
        if isinstance(outputs, str) and Path(outputs).exists():
            ticket_size = Path(outputs).stat().st_size
            quality_metrics["file_created"] = 1.0
            quality_metrics["size_valid"] = 1.0 if ticket_size <= 2048 else 0.0
        else:
            quality_metrics["file_created"] = 0.0
            quality_metrics["size_valid"] = 0.0
        
        return quality_metrics
    
    @property
    def today(self):
        """Get today's date string"""
        if hasattr(self, '_today_override'):
            return self._today_override
        return datetime.date.today().strftime("%Y-%m-%d")
    
    @today.setter
    def today(self, value):
        """Allow setting today for testing"""
        self._today_override = value
    
    def log_training(self, data):
        """Log training data for Agent Zero learning"""
        # Simple logging placeholder - in production this would go to training system
        print(f"Training logged: {json.dumps(data, indent=2, default=str)}")
    
    def execute(self, execution_plan_md, unified_analysis_json):
        plan = Path(execution_plan_md).read_text()
        ua   = json.loads(Path(unified_analysis_json).read_text())
        
        # Enhanced pricing and position sizing
        limit_price = ua.get("pricing", {}).get("limit_price", 2.45)  # Default fallback
        acct_equity = float(os.getenv("ACCOUNT_EQUITY", "25000"))
        qty = max(1, round((acct_equity * 0.01) / (limit_price * 100)))
        
        side = ua["direction"]
        roi = ua.get("risk", {}).get("expected_roi", 0.0)
        
        # Extract enhanced options data
        strike = ua.get("strike", "210")
        expiry = ua.get("expiry", "2025-07-19")
        dte = ua.get("dte", "35")
        delta = ua.get("greeks", {}).get("delta", 0.34)
        gamma = ua.get("greeks", {}).get("gamma", 0.018)
        
        # Build enhanced ticket header
        ticket_lines = [
            "=== MANUAL ORDER TICKET ===",
            f"Date          : {datetime.date.today()}",
            f"Ticker        : {ua['ticker']}",
            f"Side          : {side} (long)",
            f"Strike        : ${strike} {side[0]}",
            f"Expiry        : {expiry}  ({dte} DTE)",
            f"Quantity      : {qty} contracts   # 1% equity",
            f"Limit Price   : ${limit_price:.2f}",
            f"Min ROI       : {roi:.2f}  (floor ok)",
            f"Greeks        : D {delta:.2f}, G {gamma:.3f}",
            "",
            "Steps:",
            f"  1. Open broker option chain for {ua['ticker']}.",
            f"  2. Select {strike} {side[0]} exp {expiry}.",
            f"  3. Submit LIMIT order at ${limit_price:.2f}.",
            "  4. Set stop or OCO per plan.",
            "  5. Log fill in tracking spreadsheet.",
            "",
            "Plan reference:",
            "----------------",
            plan
        ]
        
        ticket = "\n".join(ticket_lines)
        
        out_path = Path(f"tickets/{self.today}/{ua['ticker']}_order_ticket.txt")
        out_path.parent.mkdir(parents=True, exist_ok=True)
        out_path.write_text(ticket, encoding='utf-8')
        self.log_training({"inputs": {"plan": plan, "ua": ua},
                          "outputs": {"ticket": ticket, "qty": qty, "limit_price": limit_price}})
        return str(out_path)


def main():
    """Command-line interface for ManualBrokerAdapterAgent"""
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="Manual Broker Adapter Agent v0.2.0")
    parser.add_argument("execution_plan_md", help="Path to execution plan markdown file")
    parser.add_argument("unified_analysis_json", help="Path to unified analysis JSON file")
    
    args = parser.parse_args()
    
    # Validate input files exist
    if not Path(args.execution_plan_md).exists():
        print(f"ERROR: Execution plan file not found: {args.execution_plan_md}")
        return 1
    
    if not Path(args.unified_analysis_json).exists():
        print(f"ERROR: Unified analysis file not found: {args.unified_analysis_json}")
        return 1
    
    # Create agent and execute
    agent = ManualBrokerAdapterAgent()
    
    try:
        ticket_path = agent.execute(args.execution_plan_md, args.unified_analysis_json)
        
        print("SUCCESS: ENHANCED ORDER TICKET GENERATED")
        print("=" * 50)
        print(f"Ticket saved to: {ticket_path}")
        
        # Show ticket size info
        ticket_size = Path(ticket_path).stat().st_size
        print(f"Ticket size: {ticket_size} bytes (limit: 2048)")
        
        # Show ticket preview
        print("\nTICKET PREVIEW:")
        print("-" * 30)
        ticket_content = Path(ticket_path).read_text(encoding='utf-8')
        preview_lines = ticket_content.split('\n')[:15]  # Show more lines for enhanced format
        for line in preview_lines:
            print(line)
        if len(ticket_content.split('\n')) > 15:
            print("... (truncated)")
        
        print("\nREADY FOR BROKER SUBMISSION")
        return 0
        
    except Exception as e:
        print(f"ERROR: Failed to generate order ticket: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
