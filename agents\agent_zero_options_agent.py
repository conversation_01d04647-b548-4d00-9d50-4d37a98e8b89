#!/usr/bin/env python3
"""
CONSOLIDATED Agent Zero Options Agent
Combines proper data integration with advanced intelligence capabilities:
- Data Ingestion Agent integration (Agent 1 strength)
- Greek Engine and advanced intelligence (Agent 3 strength)
- Continuous learning framework (Agent 3 strength)
- Mathematical precision and performance standards (Agent 1 strength)
"""

import sys
import json
import time
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
import logging

# Add parent directory for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import agent framework
from agents.agent_base import BaseAgent, AgentTask, AgentResult, TaskStatus, TaskPriority

# Import options intelligence services (Agent 3 capability)
try:
    from options_intelligence_services import (
        OptionsIntelligenceService, 
        OptionsDataContract, 
        MarketContextContract,
        TradeAnalysisResult,
        PositionSizingResult,
        ExitStrategyResult
    )
    OPTIONS_INTELLIGENCE_AVAILABLE = True
except ImportError:
    OPTIONS_INTELLIGENCE_AVAILABLE = False

# Import continuous learning framework (Agent 3 capability)
try:
    from options_intelligence_learning_framework import (
        OptionsIntelligenceLearningFramework,
        OptionsLearningMetrics,
        OptionsTrainingData
    )
    LEARNING_FRAMEWORK_AVAILABLE = True
except ImportError:
    LEARNING_FRAMEWORK_AVAILABLE = False

# Import pure intelligence module (Agent 2 integration)
try:
    from agents.agent_zero_options_intelligence import AgentZeroOptionsIntelligence
    PURE_INTELLIGENCE_AVAILABLE = True
except ImportError:
    PURE_INTELLIGENCE_AVAILABLE = False

logger = logging.getLogger(__name__)

class AgentZeroOptionsAgent(BaseAgent):
    """
    CONSOLIDATED Agent Zero Options Agent
    
    Combines the best of all options agent versions:
    - Proper data source integration via Data Ingestion Agent
    - Advanced Greek Engine integration and sophisticated intelligence  
    - Continuous learning framework with options expertise
    - Mathematical precision with performance standards
    - Complete Agent Zero decision pipeline integration
    
    Mathematical Foundation:
    - Delta targeting with 0.25-0.75 range preferences
    - Liquidity scoring with multi-component weighting
    - Risk assessment with Greeks exposure analysis
    - Execution quality optimization
    
    Performance Standards:
    - Execution time: <150ms target
    - Strike selection accuracy: 95%
    - Liquidity assessment precision: 99%
    """
    
    task_id = "AZ-OPT"
    
    def __init__(self, agent_id: str = "agent_zero_options_consolidated", config: dict = None):
        super().__init__(agent_id, config)
        
        # CONSOLIDATED DATA INTEGRATION (Agent 1 strength)
        try:
            from agents.data_ingestion_agent import LiveDataGatewayAgent
            self.data_agent = LiveDataGatewayAgent()
            self.has_data_agent = True
            self.logger.info("CONSOLIDATED Options Agent: Using Data Ingestion Agent for options/market data")
        except ImportError:
            # Fallback to enhanced data agent
            try:
                from enhanced_data_agent_broker_integration import EnhancedDataAgent
                self.data_agent = EnhancedDataAgent()
                self.has_data_agent = True
                self.logger.info("CONSOLIDATED Options Agent: Using Enhanced Data Agent fallback")
            except ImportError:
                self.logger.error("No data agent available - Options Agent requires data source")
                self.has_data_agent = False
        
        # ADVANCED INTELLIGENCE INTEGRATION (Agent 3 capabilities)
        if OPTIONS_INTELLIGENCE_AVAILABLE:
            self.options_intelligence = OptionsIntelligenceService()
            self.logger.info("Advanced OptionsIntelligenceService integrated")
        else:
            self.options_intelligence = None
            self.logger.warning("OptionsIntelligenceService not available")
        
        # CONTINUOUS LEARNING FRAMEWORK (Agent 3 enhancement)
        if LEARNING_FRAMEWORK_AVAILABLE:
            self.learning_framework = OptionsIntelligenceLearningFramework()
            self.logger.info("Continuous learning framework integrated")
        else:
            self.learning_framework = None
            self.logger.warning("Learning framework not available")
        
        # PURE INTELLIGENCE MODULE (Agent 2 integration)
        if PURE_INTELLIGENCE_AVAILABLE:
            self.pure_intelligence = AgentZeroOptionsIntelligence()
            self.logger.info("Pure options intelligence module integrated")
        else:
            self.pure_intelligence = None
            self.logger.warning("Pure intelligence module not available")
        
        # MATHEMATICAL CONFIGURATION (Agent 1 precision)
        self.config = config or {}
        self.performance_targets = {
            'execution_time_ms': self.config.get('execution_time_target', 150),
            'strike_selection_accuracy': self.config.get('strike_accuracy_target', 0.95),
            'liquidity_assessment_precision': self.config.get('liquidity_precision_target', 0.99)
        }
        
        # OPTIONS INTELLIGENCE CONFIGURATION (Combined from all agents)
        self.options_config = {
            'preferred_delta_range': self.config.get('delta_range', (0.25, 0.75)),
            'max_bid_ask_spread_pct': self.config.get('max_spread', 0.15),
            'min_open_interest': self.config.get('min_oi', 50),
            'min_volume': self.config.get('min_vol', 10),
            'preferred_dte_range': self.config.get('dte_range', (7, 45)),
            'iv_percentile_thresholds': {
                'cheap': 25,
                'normal': 75,
                'expensive': 75
            }
        }
        
        # Performance tracking
        self.execution_times = []
        self.accuracy_scores = []
        
        logger.info(f"CONSOLIDATED Agent Zero Options Agent initialized")
        logger.info(f"Data Agent: {self.has_data_agent}")
        logger.info(f"Advanced Intelligence: {OPTIONS_INTELLIGENCE_AVAILABLE}")
        logger.info(f"Learning Framework: {LEARNING_FRAMEWORK_AVAILABLE}")
        logger.info(f"Pure Intelligence: {PURE_INTELLIGENCE_AVAILABLE}")
    
    def execute_task(self, task: AgentTask) -> AgentResult:
        """Execute consolidated options analysis task"""
        start_time = time.time()
        
        try:
            # Extract task parameters
            ticker = task.inputs.get("ticker", "UNKNOWN")
            agent_zero_signal = task.inputs.get("agent_zero_signal", {})
            market_context = task.inputs.get("market_context", {})
            
            # Execute consolidated analysis
            result = self.execute(ticker, agent_zero_signal, market_context)
            
            execution_time = time.time() - start_time
            
            # Validate performance targets
            if execution_time * 1000 > self.performance_targets['execution_time_ms']:
                self.logger.warning(f"Performance target exceeded: {execution_time*1000:.1f}ms > {self.performance_targets['execution_time_ms']}ms")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.COMPLETED,
                outputs=result,
                execution_time=execution_time,
                quality_metrics=self._calculate_quality_metrics(result)
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Consolidated options analysis failed: {e}")
            
            return AgentResult(
                task_id=task.task_id,
                agent_id=self.agent_id,
                status=TaskStatus.FAILED,
                outputs={},
                execution_time=execution_time,
                quality_metrics={},
                error_details=str(e)
            )
    
    def execute(self, ticker: str, agent_zero_signal: Dict[str, Any], 
                market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        CONSOLIDATED options analysis execution
        Combines data integration, advanced intelligence, and continuous learning
        """
        if not self.has_data_agent:
            raise ValueError("No data agent available for options analysis")
        
        try:
            # STEP 1: GET REAL OPTIONS DATA (Agent 1 strength)
            options_data = self._get_real_options_data(ticker)
            underlying_price = self._get_current_price(ticker)
            
            # STEP 2: ADVANCED INTELLIGENCE ANALYSIS (Agent 3 capability)
            if self.options_intelligence:
                advanced_analysis = self._advanced_intelligence_analysis(
                    options_data, underlying_price, agent_zero_signal, market_context
                )
            else:
                advanced_analysis = {}
            
            # STEP 3: PURE INTELLIGENCE ANALYSIS (Agent 2 integration)
            if self.pure_intelligence:
                market_direction = agent_zero_signal.get('direction', 'neutral')
                pure_analysis = self.pure_intelligence.analyze_options_chain(
                    options_data, underlying_price, market_direction
                )
            else:
                pure_analysis = {}
            
            # STEP 4: CONSOLIDATED STRIKE SELECTION (Combined logic)
            strike_selection = self._consolidated_strike_selection(
                options_data, underlying_price, agent_zero_signal, 
                advanced_analysis, pure_analysis
            )
            
            # STEP 5: RISK ASSESSMENT AND POSITION SIZING (Agent 3 enhancement)
            risk_assessment = self._consolidated_risk_assessment(
                strike_selection, market_context, agent_zero_signal
            )
            
            # STEP 6: EXECUTION STRATEGY (Combined best practices)
            execution_strategy = self._consolidated_execution_strategy(
                strike_selection, risk_assessment, options_data
            )
            
            # STEP 7: CONTINUOUS LEARNING (Agent 3 capability)
            if self.learning_framework:
                learning_metrics = self._capture_learning_metrics(
                    ticker, agent_zero_signal, strike_selection, market_context
                )
            else:
                learning_metrics = {}
            
            # Compile consolidated result
            result = {
                'ticker': ticker,
                'timestamp': datetime.now().isoformat(),
                'agent_zero_signal': agent_zero_signal,
                'underlying_price': underlying_price,
                'options_analysis': {
                    'advanced_intelligence': advanced_analysis,
                    'pure_intelligence': pure_analysis,
                    'consolidated_features': True
                },
                'strike_selection': strike_selection,
                'risk_assessment': risk_assessment,
                'execution_strategy': execution_strategy,
                'learning_metrics': learning_metrics,
                'performance_metadata': {
                    'data_source': 'real_data_agent',
                    'intelligence_level': 'consolidated_advanced',
                    'learning_enabled': LEARNING_FRAMEWORK_AVAILABLE,
                    'version': 'consolidated'
                }
            }
            
            # SHADOW MODE INTEGRATION (Agent Zero training)
            self._integrate_with_agent_zero(result, ticker, agent_zero_signal)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Consolidated options execution failed for {ticker}: {e}")
            raise
    
    def _get_real_options_data(self, ticker: str) -> Dict[str, Any]:
        """Get real options data through Data Ingestion Agent"""
        try:
            # Use data agent to get options chain
            options_result = self.data_agent.get_options_chain(ticker)
            
            if options_result.get('status') == 'SUCCESS':
                return options_result.get('data', {})
            else:
                raise ValueError(f"Failed to get options data: {options_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"Real options data retrieval failed: {e}")
            raise
    
    def _get_current_price(self, ticker: str) -> float:
        """Get current underlying price through Data Ingestion Agent"""
        try:
            # Use data agent to get current quote
            quote_result = self.data_agent.get_quote(ticker)
            
            if quote_result.get('status') == 'SUCCESS':
                quote_data = quote_result.get('data', {})
                return quote_data.get('last_price', quote_data.get('price', 0.0))
            else:
                raise ValueError(f"Failed to get current price: {quote_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            self.logger.error(f"Current price retrieval failed: {e}")
            raise
    
    def _advanced_intelligence_analysis(self, options_data: Dict, underlying_price: float,
                                      agent_zero_signal: Dict, market_context: Dict) -> Dict[str, Any]:
        """Advanced intelligence analysis using OptionsIntelligenceService"""
        try:
            # Create data contracts for advanced analysis
            options_contract = OptionsDataContract(
                symbol=agent_zero_signal.get('ticker', 'UNKNOWN'),
                underlying_price=underlying_price,
                options_chain=options_data,
                timestamp=datetime.now()
            )
            
            market_contract = MarketContextContract(
                market_direction=agent_zero_signal.get('direction', 'neutral'),
                confidence=agent_zero_signal.get('confidence', 0.5),
                volatility_regime=market_context.get('volatility_regime', 'normal'),
                trend_strength=market_context.get('trend_strength', 0.5)
            )
            
            # Execute advanced analysis
            trade_analysis = self.options_intelligence.analyze_trade_opportunity(
                options_contract, market_contract
            )
            
            position_sizing = self.options_intelligence.calculate_position_sizing(
                trade_analysis, market_contract
            )
            
            exit_strategy = self.options_intelligence.develop_exit_strategy(
                trade_analysis, market_contract
            )
            
            return {
                'trade_analysis': trade_analysis.__dict__ if hasattr(trade_analysis, '__dict__') else str(trade_analysis),
                'position_sizing': position_sizing.__dict__ if hasattr(position_sizing, '__dict__') else str(position_sizing),
                'exit_strategy': exit_strategy.__dict__ if hasattr(exit_strategy, '__dict__') else str(exit_strategy),
                'intelligence_level': 'advanced'
            }
            
        except Exception as e:
            self.logger.warning(f"Advanced intelligence analysis failed: {e}")
            return {'error': str(e), 'intelligence_level': 'fallback'}
    
    def _consolidated_strike_selection(self, options_data: Dict, underlying_price: float,
                                     agent_zero_signal: Dict, advanced_analysis: Dict,
                                     pure_analysis: Dict) -> Dict[str, Any]:
        """Consolidated strike selection using all available intelligence"""
        try:
            direction = agent_zero_signal.get('direction', 'neutral').lower()
            confidence = agent_zero_signal.get('confidence', 0.5)
            
            # Determine option type
            option_type = 'call' if direction in ['bullish', 'call'] else 'put'
            
            # Get relevant options chain side
            chain_side = options_data.get('calls' if option_type == 'call' else 'puts', [])
            
            if not chain_side:
                raise ValueError(f"No {option_type} options available")
            
            # Score each strike using consolidated logic
            strike_scores = []
            
            for option in chain_side:
                try:
                    strike_price = float(option.get('strike', 0))
                    bid = float(option.get('bid', 0))
                    ask = float(option.get('ask', 0))
                    delta = float(option.get('delta', 0))
                    volume = int(option.get('volume', 0))
                    open_interest = int(option.get('open_interest', 0))
                    
                    # Skip invalid options
                    if not all([strike_price, ask, delta]):
                        continue
                    
                    # Calculate consolidated score
                    score = self._calculate_consolidated_strike_score(
                        strike_price, underlying_price, bid, ask, delta, 
                        volume, open_interest, confidence, option_type
                    )
                    
                    strike_scores.append({
                        'strike': strike_price,
                        'option_data': option,
                        'score': score,
                        'delta': delta,
                        'bid_ask_spread': (ask - bid) / ask if ask > 0 else 1.0,
                        'liquidity_score': (volume + open_interest) / 2
                    })
                    
                except (ValueError, TypeError) as e:
                    continue
            
            # Sort by score and select best
            strike_scores.sort(key=lambda x: x['score'], reverse=True)
            
            if not strike_scores:
                raise ValueError("No valid strikes found for selection")
            
            best_strike = strike_scores[0]
            
            return {
                'selected_strike': best_strike['strike'],
                'option_details': best_strike['option_data'],
                'selection_score': best_strike['score'],
                'delta': best_strike['delta'],
                'liquidity_metrics': {
                    'bid_ask_spread': best_strike['bid_ask_spread'],
                    'liquidity_score': best_strike['liquidity_score']
                },
                'alternative_strikes': strike_scores[1:min(4, len(strike_scores))],  # Top 3 alternatives
                'selection_method': 'consolidated_intelligence',
                'option_type': option_type
            }
            
        except Exception as e:
            self.logger.error(f"Consolidated strike selection failed: {e}")
            raise
    
    def _calculate_consolidated_strike_score(self, strike: float, underlying: float,
                                           bid: float, ask: float, delta: float,
                                           volume: int, oi: int, confidence: float,
                                           option_type: str) -> float:
        """Calculate consolidated strike score using mathematical weighting"""
        try:
            # Delta score (prefer optimal delta range)
            delta_target = 0.5  # Neutral starting point
            if confidence > 0.7:
                delta_target = 0.6  # Higher delta for high confidence
            elif confidence < 0.3:
                delta_target = 0.3  # Lower delta for low confidence
            
            delta_score = 1.0 - abs(delta - delta_target)
            
            # Liquidity score
            bid_ask_spread = (ask - bid) / ask if ask > 0 else 1.0
            spread_score = max(0, 1.0 - (bid_ask_spread / self.options_config['max_bid_ask_spread_pct']))
            
            volume_score = min(1.0, volume / 100.0)  # Normalize to 100 daily volume
            oi_score = min(1.0, oi / 500.0)  # Normalize to 500 OI
            
            liquidity_score = (spread_score * 0.4 + volume_score * 0.3 + oi_score * 0.3)
            
            # Moneyness score (proximity to current price)
            moneyness = strike / underlying if option_type == 'call' else underlying / strike
            if option_type == 'call':
                moneyness_score = 1.0 - abs(moneyness - 1.05)  # Slightly OTM calls preferred
            else:
                moneyness_score = 1.0 - abs(moneyness - 1.05)  # Slightly OTM puts preferred
            
            moneyness_score = max(0, min(1.0, moneyness_score))
            
            # Weighted composite score
            composite_score = (
                delta_score * 0.35 +           # Delta targeting
                liquidity_score * 0.40 +       # Liquidity (most important)
                moneyness_score * 0.25         # Moneyness
            )
            
            return composite_score
            
        except Exception as e:
            self.logger.warning(f"Strike score calculation failed: {e}")
            return 0.0
    
    def _consolidated_risk_assessment(self, strike_selection: Dict, market_context: Dict,
                                    agent_zero_signal: Dict) -> Dict[str, Any]:
        """Consolidated risk assessment combining all methodologies"""
        try:
            option_details = strike_selection.get('option_details', {})
            
            # Extract Greeks
            delta = float(option_details.get('delta', 0))
            gamma = float(option_details.get('gamma', 0))
            theta = float(option_details.get('theta', 0))
            vega = float(option_details.get('vega', 0))
            
            # Extract pricing
            ask_price = float(option_details.get('ask', 0))
            bid_price = float(option_details.get('bid', 0))
            
            # Risk metrics calculation
            max_loss = ask_price  # Premium paid
            breakeven = strike_selection['selected_strike'] + ask_price  # For calls
            
            # Position sizing based on risk
            confidence = agent_zero_signal.get('confidence', 0.5)
            base_position_size = min(0.02, confidence * 0.03)  # 2-3% max based on confidence
            
            # Risk-adjusted position size
            liquidity_factor = min(1.0, strike_selection['liquidity_metrics']['liquidity_score'])
            spread_factor = max(0.3, 1.0 - strike_selection['liquidity_metrics']['bid_ask_spread'])
            
            adjusted_position_size = base_position_size * liquidity_factor * spread_factor
            
            return {
                'max_loss_per_contract': max_loss,
                'breakeven_price': breakeven,
                'position_size_pct': adjusted_position_size,
                'greeks_exposure': {
                    'delta': delta,
                    'gamma': gamma,
                    'theta': theta,
                    'vega': vega
                },
                'risk_factors': {
                    'liquidity_risk': 1.0 - liquidity_factor,
                    'spread_risk': strike_selection['liquidity_metrics']['bid_ask_spread'],
                    'time_decay_risk': abs(theta) * ask_price  # Theta impact
                },
                'risk_level': self._calculate_risk_level(adjusted_position_size, max_loss, confidence),
                'assessment_method': 'consolidated_risk_analysis'
            }
            
        except Exception as e:
            self.logger.error(f"Risk assessment failed: {e}")
            return {'error': str(e), 'risk_level': 'high'}
    
    def _consolidated_execution_strategy(self, strike_selection: Dict, risk_assessment: Dict,
                                       options_data: Dict) -> Dict[str, Any]:
        """Consolidated execution strategy with optimal entry and exit rules"""
        try:
            option_details = strike_selection.get('option_details', {})
            bid = float(option_details.get('bid', 0))
            ask = float(option_details.get('ask', 0))
            
            # Optimal entry price calculation
            mid_price = (bid + ask) / 2
            spread_pct = (ask - bid) / ask if ask > 0 else 0
            
            # Entry strategy based on spread width
            if spread_pct <= 0.05:  # Tight spread
                entry_price = mid_price
                order_type = "LIMIT"
            elif spread_pct <= 0.15:  # Medium spread
                entry_price = mid_price + (ask - mid_price) * 0.3  # Closer to ask
                order_type = "LIMIT"
            else:  # Wide spread
                entry_price = ask  # Market order territory
                order_type = "MARKET"
            
            # Exit strategy
            profit_target_pct = 0.50  # 50% profit target
            stop_loss_pct = 0.30      # 30% stop loss
            
            profit_target_price = entry_price * (1 + profit_target_pct)
            stop_loss_price = entry_price * (1 - stop_loss_pct)
            
            return {
                'entry_strategy': {
                    'recommended_price': round(entry_price, 2),
                    'order_type': order_type,
                    'bid_ask_spread_pct': round(spread_pct * 100, 2),
                    'market_conditions': 'tight' if spread_pct <= 0.05 else 'medium' if spread_pct <= 0.15 else 'wide'
                },
                'exit_strategy': {
                    'profit_target': round(profit_target_price, 2),
                    'stop_loss': round(stop_loss_price, 2),
                    'profit_target_pct': profit_target_pct * 100,
                    'stop_loss_pct': stop_loss_pct * 100
                },
                'execution_timing': {
                    'immediate': spread_pct <= 0.05,
                    'patient': 0.05 < spread_pct <= 0.15,
                    'aggressive': spread_pct > 0.15
                },
                'strategy_type': 'consolidated_execution'
            }
            
        except Exception as e:
            self.logger.error(f"Execution strategy failed: {e}")
            return {'error': str(e)}
    
    def _capture_learning_metrics(self, ticker: str, agent_zero_signal: Dict,
                                strike_selection: Dict, market_context: Dict) -> Dict[str, Any]:
        """Capture learning metrics for continuous improvement"""
        if not self.learning_framework:
            return {}
        
        try:
            # Prepare learning data
            learning_data = OptionsTrainingData(
                ticker=ticker,
                signal_direction=agent_zero_signal.get('direction', 'neutral'),
                signal_confidence=agent_zero_signal.get('confidence', 0.5),
                selected_strike=strike_selection.get('selected_strike', 0),
                selection_score=strike_selection.get('selection_score', 0),
                market_context=market_context,
                timestamp=datetime.now()
            )
            
            # Capture metrics
            metrics = self.learning_framework.capture_metrics(learning_data)
            
            return {
                'learning_metrics': metrics.__dict__ if hasattr(metrics, '__dict__') else str(metrics),
                'data_captured': True,
                'framework_version': 'consolidated'
            }
            
        except Exception as e:
            self.logger.warning(f"Learning metrics capture failed: {e}")
            return {'error': str(e), 'data_captured': False}
    
    def _integrate_with_agent_zero(self, result: Dict[str, Any], ticker: str, 
                                 agent_zero_signal: Dict[str, Any]):
        """Integrate with Agent Zero shadow mode for training"""
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            strike_selection = result.get('strike_selection', {})
            risk_assessment = result.get('risk_assessment', {})
            
            signal_data = {
                'confidence': agent_zero_signal.get('confidence', 0.5),
                'strength': strike_selection.get('selection_score', 0.5),
                'execution_recommendation': 'execute' if strike_selection.get('selection_score', 0) > 0.6 else 'hold'
            }
            
            math_data = {
                'accuracy_score': min(strike_selection.get('selection_score', 0.5), 1.0),
                'precision': 0.001
            }
            
            market_context = {
                'system': 'CONSOLIDATED_AGENT_ZERO_OPTIONS',
                'version': 'consolidated',
                'ticker': ticker,
                'selected_strike': strike_selection.get('selected_strike', 0),
                'option_type': strike_selection.get('option_type', 'unknown'),
                'risk_level': risk_assessment.get('risk_level', 'medium'),
                'data_source': 'real_data_agent',
                'intelligence_level': 'consolidated_advanced',
                'features_integrated': {
                    'data_integration': self.has_data_agent,
                    'advanced_intelligence': OPTIONS_INTELLIGENCE_AVAILABLE,
                    'learning_framework': LEARNING_FRAMEWORK_AVAILABLE,
                    'pure_intelligence': PURE_INTELLIGENCE_AVAILABLE
                }
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={
                    'action': 'consolidated_options_analysis',
                    'result': strike_selection,
                    'risk_assessment': risk_assessment
                },
                outcome=strike_selection.get('selection_score', 0.5),
                market_context=market_context
            )
            
            self.logger.info("Agent Zero shadow mode integration complete")
            
        except Exception as e:
            self.logger.warning(f"Agent Zero integration failed: {e}")
    
    def _calculate_risk_level(self, position_size: float, max_loss: float, confidence: float) -> str:
        """Calculate quality metrics for the analysis"""
        try:
            metrics = {}
            
            # Strike selection quality
            strike_selection = result.get('strike_selection', {})
            selection_score = strike_selection.get('selection_score', 0)
            metrics['strike_selection_quality'] = float(selection_score)
            
            # Data quality
            has_real_data = result.get('performance_metadata', {}).get('data_source') == 'real_data_agent'
            metrics['data_quality'] = 1.0 if has_real_data else 0.5
            
            # Intelligence level
            intelligence_level = result.get('performance_metadata', {}).get('intelligence_level', 'basic')
            intelligence_scores = {'consolidated_advanced': 1.0, 'advanced': 0.8, 'basic': 0.5}
            metrics['intelligence_quality'] = intelligence_scores.get(intelligence_level, 0.5)
            
            # Risk assessment quality
            risk_assessment = result.get('risk_assessment', {})
            has_risk_data = 'risk_level' in risk_assessment and 'position_size_pct' in risk_assessment
            metrics['risk_assessment_quality'] = 1.0 if has_risk_data else 0.0
            
            # Overall quality
            metrics['overall_quality'] = sum(metrics.values()) / len(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.warning(f"Quality metrics calculation failed: {e}")
            return {'overall_quality': 0.0}
    
    def validate_inputs(self, task: AgentTask) -> bool:
        """Validate task inputs meet requirements"""
        inputs = task.inputs
        
        # Check required inputs
        if "ticker" not in inputs:
            self.logger.error("Missing required input: ticker")
            return False
        
        if "agent_zero_signal" not in inputs:
            self.logger.error("Missing required input: agent_zero_signal")
            return False
        
        # Validate agent zero signal structure
        signal = inputs["agent_zero_signal"]
        if not isinstance(signal, dict):
            self.logger.error("agent_zero_signal must be a dictionary")
            return False
        
        # Check for required signal fields
        if "direction" not in signal:
            self.logger.error("agent_zero_signal missing 'direction' field")
            return False
        
        if "confidence" not in signal:
            self.logger.error("agent_zero_signal missing 'confidence' field")
            return False
        
        # Validate direction values
        valid_directions = ['bullish', 'bearish', 'neutral', 'call', 'put']
        if signal["direction"].lower() not in valid_directions:
            self.logger.error(f"Invalid direction: {signal['direction']}. Must be one of {valid_directions}")
            return False
        
        # Validate confidence range
        try:
            confidence = float(signal["confidence"])
            if not 0.0 <= confidence <= 1.0:
                self.logger.error(f"Confidence must be between 0.0 and 1.0, got {confidence}")
                return False
        except (ValueError, TypeError):
            self.logger.error(f"Confidence must be a number, got {type(signal['confidence'])}")
            return False
        
        return True
    
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """Validate outputs meet quality standards"""
        return self._calculate_quality_metrics(outputs)
        """Calculate overall risk level"""
        try:
            # Risk factors
            size_risk = min(1.0, position_size / 0.05)  # Normalize to 5% max
            loss_risk = min(1.0, max_loss / 10.0)  # Normalize to $10 max loss
            confidence_risk = 1.0 - confidence
            
            # Composite risk score
            risk_score = (size_risk * 0.4 + loss_risk * 0.3 + confidence_risk * 0.3)
            
            if risk_score <= 0.3:
                return 'low'
            elif risk_score <= 0.7:
                return 'medium'
            else:
                return 'high'
                
        except Exception:
            return 'medium'


# Compatibility functions for existing imports
def create_enhanced_options_task(ticker: str, agent_zero_signal: Dict[str, Any], 
                               market_context: Dict[str, Any] = None) -> AgentTask:
    """Create options task compatible with existing code"""
    return AgentTask(
        task_id=f"options_analysis_{ticker}_{int(time.time())}",
        agent_id="agent_zero_options_consolidated",
        priority=TaskPriority.HIGH,
        inputs={
            'ticker': ticker,
            'agent_zero_signal': agent_zero_signal,
            'market_context': market_context or {}
        }
    )


# Backward compatibility alias
EnhancedAgentZeroOptionsAgent = AgentZeroOptionsAgent

if __name__ == "__main__":
    # Test consolidated agent
    agent = AgentZeroOptionsAgent()
    
    test_signal = {
        'direction': 'bullish',
        'confidence': 0.75,
        'ticker': 'SPY'
    }
    
    test_context = {
        'volatility_regime': 'normal',
        'trend_strength': 0.6
    }
    
    try:
        result = agent.execute('SPY', test_signal, test_context)
        print("CONSOLIDATED AGENT ZERO OPTIONS TEST RESULT:")
        print(json.dumps(result, indent=2, default=str))
    except Exception as e:
        print(f"Test failed: {e}")
