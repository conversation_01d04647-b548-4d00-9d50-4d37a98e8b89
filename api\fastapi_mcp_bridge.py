from fastapi import FastAPI, HTTPException
import uvicorn, json, subprocess, os, asyncio

MCP_BIN = ["python", "mcp_server_production.py"]      # adjust
app = FastAPI()

proc = subprocess.Popen(
    MCP_BIN, stdin=subprocess.PIPE, stdout=subprocess.PIPE, text=True
)

async def mcp_call(cmd: dict, timeout=8):
    proc.stdin.write(json.dumps(cmd) + "\n")
    proc.stdin.flush()
    try:
        return await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(
                None, proc.stdout.readline), timeout
        )
    except asyncio.TimeoutError:
        raise HTTPException(504, detail="MCP read timeout")

@app.get("/bars")
async def bars(tk: str, tf: str = "1"):
    resp = await mcp_call({"op": "bars", "ticker": tk, "tf": tf})
    return json.loads(resp)

@app.get("/options")
async def options(tk: str):
    resp = await mcp_call({"op": "options", "ticker": tk})
    return json.loads(resp)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8002)
