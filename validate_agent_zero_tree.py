#!/usr/bin/env python3
"""
Agent Zero Tree Validation
Comprehensive path logic and efficiency analysis for Agent Zero's decision tree
"""

import sys
import json
import time
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, Any, List

def test_agent_zero_core():
    """Test Agent Zero core functionality"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO CORE]")
    
    try:
        from agents.agent_zero import AgentZeroAdvisor
        
        # Test initialization
        start_time = time.time()
        agent = AgentZeroAdvisor()
        init_time = (time.time() - start_time) * 1000
        
        if hasattr(agent, 'predict') and hasattr(agent, 'log_training_data'):
            results['passed'] += 1
            results['performance']['initialization_ms'] = init_time
            print(f"+ AgentZeroCore: Import SUCCESS ({init_time:.2f}ms)")
        else:
            results['failed'] += 1
            results['errors'].append("AgentZeroCore: Missing core methods")
            print("- AgentZeroCore: Missing core methods")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"AgentZeroCore: {str(e)}")
        print(f"- AgentZeroCore: Import FAILED - {str(e)}")
    
    return results

def test_agent_zero_decision_logic():
    """Test Agent Zero decision logic and mathematical precision"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO DECISION LOGIC]")
    
    try:
        from agents.agent_zero import AgentZeroAdvisor
        agent = AgentZeroAdvisor()
        
        # Test decision with high confidence inputs
        signal_data = {
            'confidence': 0.9,
            'strength': 0.8,
            'execution_recommendation': 'execute'
        }
        
        math_data = {
            'accuracy_score': 0.95,
            'precision': 0.001
        }
        
        market_context = {
            'volatility_regime': 'normal',
            'trend_strength': 0.7
        }
        
        start_time = time.time()
        decision = agent.predict(signal_data, math_data, market_context)
        decision_time = (time.time() - start_time) * 1000
        
        # Validate decision structure
        required_fields = ['action', 'confidence', 'reasoning', 'execution_time_ms']
        if all(field in decision for field in required_fields):
            results['passed'] += 1
            results['performance']['decision_time_ms'] = decision_time
            print(f"+ DecisionLogic: Structure SUCCESS ({decision_time:.2f}ms)")
            
            # Test mathematical consistency
            if (decision['confidence'] >= 0.0 and decision['confidence'] <= 1.0 and
                decision['action'] in ['execute', 'hold', 'avoid']):
                results['passed'] += 1
                print("+ DecisionLogic: Mathematical Validation SUCCESS")
            else:
                results['failed'] += 1
                results['errors'].append("DecisionLogic: Invalid confidence or action values")
                print("- DecisionLogic: Invalid confidence or action values")
        else:
            results['failed'] += 1
            results['errors'].append("DecisionLogic: Missing required fields")
            print("- DecisionLogic: Missing required fields")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"DecisionLogic: {str(e)}")
        print(f"- DecisionLogic: FAILED - {str(e)}")
    
    return results

def test_agent_zero_hub_integration():
    """Test Agent Zero hub functionality"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO HUB INTEGRATION]")
    
    try:
        from agents.agent_zero import get_agent_zero_hub
        
        start_time = time.time()
        hub = get_agent_zero_hub()
        hub_time = (time.time() - start_time) * 1000
        
        if hasattr(hub, 'predict'):
            results['passed'] += 1
            results['performance']['hub_access_ms'] = hub_time
            print(f"+ HubIntegration: Access SUCCESS ({hub_time:.2f}ms)")
            
            # Test singleton behavior
            hub2 = get_agent_zero_hub()
            if hub is hub2:
                results['passed'] += 1
                print("+ HubIntegration: Singleton Pattern SUCCESS")
            else:
                results['failed'] += 1
                results['errors'].append("HubIntegration: Singleton pattern broken")
                print("- HubIntegration: Singleton pattern broken")
        else:
            results['failed'] += 1
            results['errors'].append("HubIntegration: Missing predict method")
            print("- HubIntegration: Missing predict method")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"HubIntegration: {str(e)}")
        print(f"- HubIntegration: FAILED - {str(e)}")
    
    return results

def test_agent_zero_advanced_capabilities():
    """Test Agent Zero advanced capabilities"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO ADVANCED CAPABILITIES]")
    
    try:
        from agent_zero_advanced_capabilities import AgentZeroAdvanced, AdvancedConfig
        
        # Test initialization
        config = AdvancedConfig()
        advanced = AgentZeroAdvanced(config)
        
        if hasattr(advanced, 'enhance_decision'):
            results['passed'] += 1
            print("+ AdvancedCapabilities: Import SUCCESS")
            
            # Test enhancement functionality
            base_decision = {
                'action': 'execute',
                'confidence': 0.8,
                'reasoning': ['Test decision']
            }
            
            market_data = {
                'prices': [100, 101, 102, 101, 103, 104, 103, 105],
                'volumes': [1000, 1100, 900, 1200, 800, 1300, 1000, 1500]
            }
            
            start_time = time.time()
            enhanced = advanced.enhance_decision(base_decision, market_data)
            enhance_time = (time.time() - start_time) * 1000
            
            if ('risk_adjustment' in enhanced or 
                'regime_prediction' in enhanced or
                'timeframe_analysis' in enhanced):
                results['passed'] += 1
                results['performance']['enhancement_ms'] = enhance_time
                print(f"+ AdvancedCapabilities: Enhancement SUCCESS ({enhance_time:.2f}ms)")
            else:
                results['failed'] += 1
                results['errors'].append("AdvancedCapabilities: No enhancements applied")
                print("- AdvancedCapabilities: No enhancements applied")
        else:
            results['failed'] += 1
            results['errors'].append("AdvancedCapabilities: Missing enhance_decision method")
            print("- AdvancedCapabilities: Missing enhance_decision method")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"AdvancedCapabilities: {str(e)}")
        print(f"- AdvancedCapabilities: FAILED - {str(e)}")
    
    return results

def test_agent_zero_training_pipeline():
    """Test Agent Zero training data pipeline"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO TRAINING PIPELINE]")
    
    try:
        from agents.agent_zero import AgentZeroAdvisor
        agent = AgentZeroAdvisor()
        
        # Test training data logging
        signal_data = {'confidence': 0.8, 'strength': 0.7}
        math_data = {'accuracy_score': 0.9, 'precision': 0.001}
        decision = {'action': 'execute', 'confidence': 0.8}
        outcome = 0.15  # 15% return
        
        start_time = time.time()
        agent.log_training_data(signal_data, math_data, decision, outcome)
        log_time = (time.time() - start_time) * 1000
        
        # Check if training directory exists
        if agent.training_dir.exists():
            results['passed'] += 1
            results['performance']['logging_ms'] = log_time
            print(f"+ TrainingPipeline: Data Logging SUCCESS ({log_time:.2f}ms)")
            
            # Check for training files
            training_files = list(agent.training_dir.glob("training_*.json"))
            if training_files:
                results['passed'] += 1
                print("+ TrainingPipeline: File Creation SUCCESS")
            else:
                results['failed'] += 1
                results['errors'].append("TrainingPipeline: No training files created")
                print("- TrainingPipeline: No training files created")
        else:
            results['failed'] += 1
            results['errors'].append("TrainingPipeline: Training directory not created")
            print("- TrainingPipeline: Training directory not created")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"TrainingPipeline: {str(e)}")
        print(f"- TrainingPipeline: FAILED - {str(e)}")
    
    return results

def test_agent_zero_performance_characteristics():
    """Test Agent Zero performance and efficiency"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO PERFORMANCE CHARACTERISTICS]")
    
    try:
        from agents.agent_zero import AgentZeroAdvisor
        agent = AgentZeroAdvisor()
        
        # Performance test: Multiple decisions
        decision_times = []
        test_iterations = 100
        
        for i in range(test_iterations):
            signal_data = {
                'confidence': np.random.uniform(0.5, 1.0),
                'strength': np.random.uniform(0.5, 1.0),
                'execution_recommendation': np.random.choice(['execute', 'hold', 'avoid'])
            }
            
            math_data = {
                'accuracy_score': np.random.uniform(0.7, 1.0),
                'precision': np.random.uniform(0.001, 0.01)
            }
            
            start_time = time.time()
            decision = agent.predict(signal_data, math_data)
            decision_time = (time.time() - start_time) * 1000
            decision_times.append(decision_time)
        
        avg_time = np.mean(decision_times)
        max_time = np.max(decision_times)
        min_time = np.min(decision_times)
        std_time = np.std(decision_times)
        
        results['performance']['avg_decision_time_ms'] = avg_time
        results['performance']['max_decision_time_ms'] = max_time
        results['performance']['min_decision_time_ms'] = min_time
        results['performance']['std_decision_time_ms'] = std_time
        
        # Performance criteria
        if avg_time < 10.0:  # Sub-10ms average
            results['passed'] += 1
            print(f"+ Performance: Average Speed SUCCESS ({avg_time:.2f}ms)")
        else:
            results['failed'] += 1
            results['errors'].append(f"Performance: Slow average time {avg_time:.2f}ms")
            print(f"- Performance: Slow average time {avg_time:.2f}ms")
        
        if max_time < 50.0:  # No outliers over 50ms
            results['passed'] += 1
            print(f"+ Performance: Maximum Speed SUCCESS ({max_time:.2f}ms)")
        else:
            results['failed'] += 1
            results['errors'].append(f"Performance: Outlier detected {max_time:.2f}ms")
            print(f"- Performance: Outlier detected {max_time:.2f}ms")
        
        if std_time < 5.0:  # Consistent performance
            results['passed'] += 1
            print(f"+ Performance: Consistency SUCCESS (std: {std_time:.2f}ms)")
        else:
            results['failed'] += 1
            results['errors'].append(f"Performance: Inconsistent timing {std_time:.2f}ms")
            print(f"- Performance: Inconsistent timing {std_time:.2f}ms")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"Performance: {str(e)}")
        print(f"- Performance: FAILED - {str(e)}")
    
    return results

def test_agent_zero_decision_consistency():
    """Test Agent Zero decision consistency and logic paths"""
    results = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    print("\n[AGENT ZERO DECISION CONSISTENCY]")
    
    try:
        from agents.agent_zero import AgentZeroAdvisor
        agent = AgentZeroAdvisor()
        
        # Test consistency with identical inputs
        signal_data = {'confidence': 0.8, 'strength': 0.7, 'execution_recommendation': 'execute'}
        math_data = {'accuracy_score': 0.9, 'precision': 0.001}
        
        decisions = []
        for i in range(10):
            decision = agent.predict(signal_data, math_data)
            decisions.append(decision)
        
        # Check consistency
        actions = [d['action'] for d in decisions]
        confidences = [d['confidence'] for d in decisions]
        
        if len(set(actions)) == 1:  # All actions should be identical
            results['passed'] += 1
            print("+ Consistency: Action Consistency SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("Consistency: Inconsistent actions with identical inputs")
            print("- Consistency: Inconsistent actions with identical inputs")
        
        confidence_std = np.std(confidences)
        if confidence_std < 0.01:  # Very low variance in confidence
            results['passed'] += 1
            print(f"+ Consistency: Confidence Consistency SUCCESS (std: {confidence_std:.4f})")
        else:
            results['failed'] += 1
            results['errors'].append(f"Consistency: Confidence variance too high {confidence_std:.4f}")
            print(f"- Consistency: Confidence variance too high {confidence_std:.4f}")
        
        # Test decision path logic
        # High confidence inputs should lead to execute
        high_signal = {'confidence': 0.95, 'strength': 0.9, 'execution_recommendation': 'execute'}
        high_math = {'accuracy_score': 0.98, 'precision': 0.0001}
        high_decision = agent.predict(high_signal, high_math)
        
        if high_decision['action'] == 'execute':
            results['passed'] += 1
            print("+ Logic: High Confidence Path SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append(f"Logic: High confidence should execute, got {high_decision['action']}")
            print(f"- Logic: High confidence should execute, got {high_decision['action']}")
        
        # Low confidence inputs should lead to avoid/hold
        low_signal = {'confidence': 0.2, 'strength': 0.1, 'execution_recommendation': 'avoid'}
        low_math = {'accuracy_score': 0.3, 'precision': 0.1}
        low_decision = agent.predict(low_signal, low_math)
        
        if low_decision['action'] in ['avoid', 'hold']:
            results['passed'] += 1
            print("+ Logic: Low Confidence Path SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append(f"Logic: Low confidence should avoid/hold, got {low_decision['action']}")
            print(f"- Logic: Low confidence should avoid/hold, got {low_decision['action']}")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"Consistency: {str(e)}")
        print(f"- Consistency: FAILED - {str(e)}")
    
    return results

def combine_results(*result_sets):
    """Combine multiple test result sets"""
    combined = {'passed': 0, 'failed': 0, 'errors': [], 'performance': {}}
    
    for result_set in result_sets:
        combined['passed'] += result_set['passed']
        combined['failed'] += result_set['failed']
        combined['errors'].extend(result_set['errors'])
        combined['performance'].update(result_set.get('performance', {}))
    
    return combined

def main():
    """Run complete Agent Zero tree validation"""
    print("AGENT ZERO TREE VALIDATION")
    print("=" * 50)
    
    # Run all test suites
    core_results = test_agent_zero_core()
    logic_results = test_agent_zero_decision_logic()
    hub_results = test_agent_zero_hub_integration()
    advanced_results = test_agent_zero_advanced_capabilities()
    training_results = test_agent_zero_training_pipeline()
    performance_results = test_agent_zero_performance_characteristics()
    consistency_results = test_agent_zero_decision_consistency()
    
    # Combine all results
    total_results = combine_results(
        core_results, logic_results, hub_results, advanced_results,
        training_results, performance_results, consistency_results
    )
    
    # Summary
    print("\n" + "=" * 50)
    print("AGENT ZERO TREE VALIDATION SUMMARY")
    print("=" * 50)
    print(f"PASSED: {total_results['passed']}")
    print(f"FAILED: {total_results['failed']}")
    
    if total_results['errors']:
        print("\nERRORS:")
        for error in total_results['errors']:
            print(f"  {error}")
    
    # Performance metrics
    if total_results['performance']:
        print("\nPERFORMANCE METRICS:")
        for metric, value in total_results['performance'].items():
            print(f"  {metric}: {value:.2f}")
    
    success_rate = (total_results['passed'] / (total_results['passed'] + total_results['failed'])) * 100
    print(f"\nSUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("\n+ AGENT ZERO TREE: EXCELLENT - Optimal path logic and efficiency")
    elif success_rate >= 90:
        print("\n+ AGENT ZERO TREE: OPERATIONAL - Good path logic and efficiency")
    elif success_rate >= 80:
        print("\n~ AGENT ZERO TREE: FUNCTIONAL - Minor path inefficiencies")
    else:
        print("\n- AGENT ZERO TREE: NEEDS ATTENTION - Path logic issues detected")
    
    print(f"\nAGENT ZERO STATUS: {'READY' if success_rate >= 90 else 'REQUIRES OPTIMIZATION'}")
    
    return success_rate >= 90

if __name__ == "__main__":
    main()
