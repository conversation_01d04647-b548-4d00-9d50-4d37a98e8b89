#!/usr/bin/env python3
"""
GREEK ENGINE INTEGRATION VALIDATION
Validate integration approach with existing Greek Engine
"""

import sys
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List

# Add CORE to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import existing Greek Engine
from greeks import GreekEnhancementEngine, GreeksResult

@dataclass
class OptionsDataContract:
    """Standardized data format for options analysis"""
    underlying_price: float
    strike: float
    expiration_date: str
    option_type: str  # 'call' or 'put'
    implied_volatility: float
    risk_free_rate: float = 0.05
    dividend_yield: float = 0.0
    symbol: str = "TEST"

@dataclass 
class GreeksContract:
    """Standardized Greeks output contract"""
    # First order Greeks
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    
    # ROC derivatives (from your engine)
    delta_roc: float
    gamma_roc: float
    theta_roc: float
    vega_roc: float
    rho_roc: float
    
    # Quality metrics
    calculation_quality: float
    mathematical_validity: bool
    anomaly_count: int
    
    # Optional higher-order Greeks (if available)
    vanna: Optional[float] = None
    charm: Optional[float] = None
    volga: Optional[float] = None

class GreekEngineAdapter:
    """
    Adapter to integrate existing Greek Engine with Options Agent
    """
    
    def __init__(self):
        self.greek_engine = GreekEnhancementEngine()
        print("[OK] Greek Engine Adapter initialized")
    
    def calculate_greeks(self, option_data: OptionsDataContract) -> GreeksContract:
        """
        Convert option data to Greek Engine format, calculate, convert back
        """
        try:
            # Convert expiration date to time_to_expiry (years)
            time_to_expiry = self._calculate_time_to_expiry(option_data.expiration_date)
            
            # Call your existing Greek Engine
            greeks_result = self.greek_engine.calculate_greeks(
                spot_price=option_data.underlying_price,
                strike_price=option_data.strike,
                time_to_expiry=time_to_expiry,
                risk_free_rate=option_data.risk_free_rate,
                volatility=option_data.implied_volatility,
                option_type=option_data.option_type,
                dividend_yield=option_data.dividend_yield,
                symbol=option_data.symbol,
                historical_data=None  # For now, we'll add this later
            )
            
            # Convert to standardized contract
            return self._convert_to_standard_contract(greeks_result)
            
        except Exception as e:
            print(f"[ERROR] Greek calculation failed: {e}")
            raise
    
    def _calculate_time_to_expiry(self, expiration_date: str) -> float:
        """Convert expiration date string to years"""
        try:
            if isinstance(expiration_date, str):
                exp_date = datetime.strptime(expiration_date, "%Y-%m-%d")
            else:
                exp_date = expiration_date
            
            today = datetime.now()
            days_to_exp = (exp_date - today).days
            
            # Convert to years (trading days = 252)
            return max(0.001, days_to_exp / 365.0)  # Minimum 0.1% of year
            
        except Exception as e:
            print(f"Date conversion error: {e}")
            return 0.04  # Default to ~2 weeks
    
    def _convert_to_standard_contract(self, greeks_result: GreeksResult) -> GreeksContract:
        """Convert GreeksResult to our standardized contract"""
        
        return GreeksContract(
            # Core Greeks
            delta=greeks_result.delta,
            gamma=greeks_result.gamma,
            theta=greeks_result.theta,
            vega=greeks_result.vega,
            rho=greeks_result.rho,
            
            # ROC derivatives (your engine provides these)
            delta_roc=getattr(greeks_result, 'delta_roc', 0.0),
            gamma_roc=getattr(greeks_result, 'gamma_roc', 0.0),
            theta_roc=getattr(greeks_result, 'theta_roc', 0.0),
            vega_roc=getattr(greeks_result, 'vega_roc', 0.0),
            rho_roc=getattr(greeks_result, 'rho_roc', 0.0),
            
            # Quality metrics
            calculation_quality=getattr(greeks_result, 'calculation_quality', 1.0),
            mathematical_validity=getattr(greeks_result, 'mathematical_validity', True),
            anomaly_count=getattr(greeks_result, 'anomaly_count', 0),
            
            # Higher-order Greeks (if your engine provides them)
            vanna=getattr(greeks_result, 'vanna', None),
            charm=getattr(greeks_result, 'charm', None),
            volga=getattr(greeks_result, 'volga', None)
        )

def validate_integration():
    """
    Validate the Greek Engine integration approach
    """
    print("=" * 60)
    print("GREEK ENGINE INTEGRATION VALIDATION")
    print("=" * 60)
    
    # Test 1: Initialize adapter
    try:
        adapter = GreekEngineAdapter()
        print("[OK] Test 1: Adapter initialization successful")
    except Exception as e:
        print(f"[ERROR] Test 1: Adapter initialization failed: {e}")
        return False
    
    # Test 2: Create test option data
    try:
        test_option = OptionsDataContract(
            underlying_price=455.0,
            strike=460.0,
            expiration_date="2025-01-17",  # ~3 weeks out
            option_type="call",
            implied_volatility=0.25,
            risk_free_rate=0.05,
            dividend_yield=0.0,
            symbol="SPY"
        )
        print("[OK] Test 2: Option data contract created")
    except Exception as e:
        print(f"[ERROR] Test 2: Option data creation failed: {e}")
        return False
    
    # Test 3: Calculate Greeks through adapter
    try:
        greeks = adapter.calculate_greeks(test_option)
        print("[OK] Test 3: Greek calculation successful")
        
        # Validate Greek values
        print(f"   Delta: {greeks.delta:.4f}")
        print(f"   Gamma: {greeks.gamma:.4f}")
        print(f"   Theta: {greeks.theta:.4f}")
        print(f"   Vega:  {greeks.vega:.4f}")
        print(f"   Rho:   {greeks.rho:.4f}")
        
        # Validate ROC values
        print(f"   Delta ROC: {greeks.delta_roc:.6f}")
        print(f"   Gamma ROC: {greeks.gamma_roc:.6f}")
        
        # Validate quality metrics
        print(f"   Quality: {greeks.calculation_quality:.3f}")
        print(f"   Valid: {greeks.mathematical_validity}")
        print(f"   Anomalies: {greeks.anomaly_count}")
        
    except Exception as e:
        print(f"[ERROR] Test 3: Greek calculation failed: {e}")
        return False
    
    # Test 4: Validate Greek bounds
    try:
        # Delta bounds check
        if not (-1.0 <= greeks.delta <= 1.0):
            print(f"[ERROR] Test 4: Delta out of bounds: {greeks.delta}")
            return False
        
        # Gamma non-negative check
        if greeks.gamma < 0:
            print(f"[ERROR] Test 4: Gamma negative: {greeks.gamma}")
            return False
        
        # Vega non-negative check  
        if greeks.vega < 0:
            print(f"[ERROR] Test 4: Vega negative: {greeks.vega}")
            return False
        
        print("[OK] Test 4: Greek bounds validation passed")
        
    except Exception as e:
        print(f"[ERROR] Test 4: Bounds validation failed: {e}")
        return False
    
    # Test 5: Test multiple option types
    try:
        # Test put option
        put_option = OptionsDataContract(
            underlying_price=455.0,
            strike=450.0,
            expiration_date="2025-01-17",
            option_type="put",
            implied_volatility=0.28,
            symbol="SPY"
        )
        
        put_greeks = adapter.calculate_greeks(put_option)
        print("[OK] Test 5: Put option calculation successful")
        print(f"   Put Delta: {put_greeks.delta:.4f} (should be negative)")
        
        # Validate put delta is negative
        if put_greeks.delta >= 0:
            print(f"[ERROR] Test 5: Put delta should be negative: {put_greeks.delta}")
            return False
            
    except Exception as e:
        print(f"[ERROR] Test 5: Put option test failed: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("[SUCCESS] ALL INTEGRATION VALIDATION TESTS PASSED")
    print("[SUCCESS] Greek Engine Adapter is working correctly")
    print("[SUCCESS] Ready to proceed with Options Intelligence Services")
    print("=" * 60)
    
    return True

def demonstrate_integration_capabilities():
    """
    Demonstrate what the integration provides
    """
    print("\n" + "=" * 60)
    print("INTEGRATION CAPABILITIES DEMONSTRATION")
    print("=" * 60)
    
    adapter = GreekEngineAdapter()
    
    # Test different scenarios
    scenarios = [
        {
            'name': 'ATM Call',
            'option': OptionsDataContract(455.0, 455.0, "2025-01-17", "call", 0.25, symbol="SPY")
        },
        {
            'name': 'OTM Call', 
            'option': OptionsDataContract(455.0, 465.0, "2025-01-17", "call", 0.28, symbol="SPY")
        },
        {
            'name': 'ITM Put',
            'option': OptionsDataContract(455.0, 465.0, "2025-01-17", "put", 0.23, symbol="SPY")
        },
        {
            'name': 'Near Expiry',
            'option': OptionsDataContract(455.0, 455.0, "2025-01-03", "call", 0.35, symbol="SPY")
        }
    ]
    
    for scenario in scenarios:
        print(f"\n{scenario['name']}:")
        try:
            greeks = adapter.calculate_greeks(scenario['option'])
            print(f"  Delta: {greeks.delta:7.4f} | Gamma: {greeks.gamma:7.4f} | Theta: {greeks.theta:7.4f}")
            print(f"  Vega:  {greeks.vega:7.4f} | Rho:   {greeks.rho:7.4f}")
            print(f"  Quality: {greeks.calculation_quality:.3f} | Anomalies: {greeks.anomaly_count}")
        except Exception as e:
            print(f"  [ERROR]: {e}")

if __name__ == "__main__":
    # Run validation
    success = validate_integration()
    
    if success:
        # Demonstrate capabilities
        demonstrate_integration_capabilities()
        
        print("\n" + "[SUCCESS] VALIDATION COMPLETE")
        print("[SUCCESS] Greek Engine integration approach is VALIDATED")
        print("[SUCCESS] Ready to implement Options Intelligence Services")
        print("[SUCCESS] Ready to build Enhanced Options Agent")
    else:
        print("\n" + "[FAILED] VALIDATION FAILED")
        print("[ERROR] Need to fix integration issues before proceeding")
