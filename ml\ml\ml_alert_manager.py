"""
Alert Manager for Machine Learning Components

This module implements an alert management system for ML-generated insights,
including alert creation, storage, retrieval, and notification delivery.
"""

import os
import json
import time
import uuid
import threading
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime, timedelta
import pandas as pd

# Import custom modules
from src.ml.ml_inference_service import InferenceResult
from src.ml.ml_config_manager import get_component_config
from src.ml.ml_logging import get_logger, handle_errors
from src.ml.ml_base import MLComponent

# Set up logger
logger = get_logger("ml_alert_manager")


class Alert:
    """
    Representation of an ML-generated alert.
    """
    
    def __init__(self, alert_type: str, message: str, source_model: str, 
                confidence: float, data: Dict[str, Any] = None,
                timestamp: Optional[datetime] = None,
                priority: str = "medium",
                expiration: Optional[datetime] = None,
                id: Optional[str] = None):
        """
        Initialize an alert.
        
        Args:
            alert_type: Type of alert (e.g., 'pattern_detected', 'price_target')
            message: Human-readable alert message
            source_model: ID of the model that generated the alert
            confidence: Confidence level (0.0 to 1.0)
            data: Optional additional data related to the alert
            timestamp: Alert generation time (defaults to now)
            priority: Alert priority ('low', 'medium', 'high', 'critical')
            expiration: Optional expiration time
            id: Optional alert ID (auto-generated if not provided)
        """
        self.id = id or str(uuid.uuid4())
        self.alert_type = alert_type
        self.message = message
        self.source_model = source_model
        self.confidence = max(0.0, min(1.0, confidence))  # Clamp to [0, 1]
        self.data = data or {}
        self.timestamp = timestamp or datetime.now()
        self.priority = priority.lower()
        self.expiration = expiration
        self.is_active = True
        self.acknowledged = False
        self.acknowledged_time = None
        self.acknowledged_by = None
        self.notification_sent = False
        self.notification_time = None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert alert to a dictionary.
        
        Returns:
            Dictionary representation of the alert
        """
        result = {
            'id': self.id,
            'alert_type': self.alert_type,
            'message': self.message,
            'source_model': self.source_model,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'priority': self.priority,
            'is_active': self.is_active,
            'acknowledged': self.acknowledged,
            'notification_sent': self.notification_sent
        }
        
        if self.data:
            result['data'] = self.data
        
        if self.expiration:
            result['expiration'] = self.expiration.isoformat()
        
        if self.acknowledged_time:
            result['acknowledged_time'] = self.acknowledged_time.isoformat()
        
        if self.acknowledged_by:
            result['acknowledged_by'] = self.acknowledged_by
        
        if self.notification_time:
            result['notification_time'] = self.notification_time.isoformat()
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Alert':
        """
        Create an alert from a dictionary.
        
        Args:
            data: Dictionary with alert data
            
        Returns:
            Alert instance
        """
        # Convert string timestamps to datetime
        timestamp = datetime.fromisoformat(data['timestamp']) if 'timestamp' in data else None
        expiration = datetime.fromisoformat(data['expiration']) if 'expiration' in data else None
        acknowledged_time = datetime.fromisoformat(data['acknowledged_time']) if 'acknowledged_time' in data else None
        notification_time = datetime.fromisoformat(data['notification_time']) if 'notification_time' in data else None
        
        # Create alert
        alert = cls(
            alert_type=data.get('alert_type', 'unknown'),
            message=data.get('message', ''),
            source_model=data.get('source_model', ''),
            confidence=data.get('confidence', 0.0),
            data=data.get('data', {}),
            timestamp=timestamp,
            priority=data.get('priority', 'medium'),
            expiration=expiration,
            id=data.get('id')
        )
        
        # Set additional fields
        alert.is_active = data.get('is_active', True)
        alert.acknowledged = data.get('acknowledged', False)
        alert.acknowledged_time = acknowledged_time
        alert.acknowledged_by = data.get('acknowledged_by')
        alert.notification_sent = data.get('notification_sent', False)
        alert.notification_time = notification_time
        
        return alert
    
    def is_expired(self) -> bool:
        """
        Check if the alert has expired.
        
        Returns:
            True if expired, False otherwise
        """
        if not self.expiration:
            return False
        
        return datetime.now() > self.expiration
    
    def acknowledge(self, by: Optional[str] = None) -> None:
        """
        Acknowledge the alert.
        
        Args:
            by: Optional identifier of who acknowledged the alert
        """
        self.acknowledged = True
        self.acknowledged_time = datetime.now()
        self.acknowledged_by = by
    
    def mark_notified(self) -> None:
        """Mark the alert as having been sent as a notification."""
        self.notification_sent = True
        self.notification_time = datetime.now()


class AlertStorage:
    """
    Storage for alerts with persistence capabilities.
    """
    
    def __init__(self, storage_path: Optional[str] = None):
        """
        Initialize the alert storage.
        
        Args:
            storage_path: Optional path for persisting alerts to disk
        """
        self.storage_path = storage_path
        self.alerts = {}
        self.storage_lock = threading.Lock()
        
        # Create storage directory if specified
        if storage_path:
            os.makedirs(storage_path, exist_ok=True)
            
            # Load persisted alerts
            self._load_alerts()
    
    def add_alert(self, alert: Alert) -> str:
        """
        Add an alert to storage.
        
        Args:
            alert: Alert to add
            
        Returns:
            ID of the added alert
        """
        with self.storage_lock:
            self.alerts[alert.id] = alert
            self._persist_alert(alert)
            
            logger.debug(f"Added alert {alert.id}")
            return alert.id
    
    def get_alert(self, alert_id: str) -> Optional[Alert]:
        """
        Get an alert by ID.
        
        Args:
            alert_id: ID of the alert
            
        Returns:
            Alert if found, None otherwise
        """
        with self.storage_lock:
            return self.alerts.get(alert_id)
    
    def get_alerts(self, include_inactive: bool = False, 
                 include_acknowledged: bool = False,
                 alert_type: Optional[str] = None,
                 priority: Optional[Union[str, List[str]]] = None,
                 source_model: Optional[str] = None,
                 min_confidence: float = 0.0,
                 limit: Optional[int] = None,
                 sort_by: str = 'timestamp',
                 sort_desc: bool = True) -> List[Alert]:
        """
        Get alerts with optional filtering.
        
        Args:
            include_inactive: Whether to include inactive alerts
            include_acknowledged: Whether to include acknowledged alerts
            alert_type: Optional filter by alert type
            priority: Optional filter by priority (single or list)
            source_model: Optional filter by source model
            min_confidence: Minimum confidence level
            limit: Optional maximum number of alerts to return
            sort_by: Field to sort by
            sort_desc: Whether to sort in descending order
            
        Returns:
            List of matching alerts
        """
        with self.storage_lock:
            # Start with all alerts
            result = list(self.alerts.values())
            
            # Apply filters
            if not include_inactive:
                result = [a for a in result if a.is_active and not a.is_expired()]
            
            if not include_acknowledged:
                result = [a for a in result if not a.acknowledged]
            
            if alert_type:
                result = [a for a in result if a.alert_type == alert_type]
            
            if priority:
                if isinstance(priority, str):
                    result = [a for a in result if a.priority == priority]
                else:
                    result = [a for a in result if a.priority in priority]
            
            if source_model:
                result = [a for a in result if a.source_model == source_model]
            
            if min_confidence > 0:
                result = [a for a in result if a.confidence >= min_confidence]
            
            # Sort results
            if sort_by == 'timestamp':
                result.sort(key=lambda a: a.timestamp, reverse=sort_desc)
            elif sort_by == 'priority':
                priority_order = {'critical': 3, 'high': 2, 'medium': 1, 'low': 0}
                result.sort(key=lambda a: (priority_order.get(a.priority, 0), a.timestamp), 
                          reverse=sort_desc)
            elif sort_by == 'confidence':
                result.sort(key=lambda a: (a.confidence, a.timestamp), reverse=sort_desc)
            
            # Apply limit
            if limit is not None and limit > 0:
                result = result[:limit]
            
            return result
    
    def update_alert(self, alert: Alert) -> bool:
        """
        Update an existing alert.
        
        Args:
            alert: Alert with updated fields
            
        Returns:
            True if successful, False otherwise
        """
        with self.storage_lock:
            if alert.id not in self.alerts:
                logger.warning(f"Alert {alert.id} not found")
                return False
            
            self.alerts[alert.id] = alert
            self._persist_alert(alert)
            
            logger.debug(f"Updated alert {alert.id}")
            return True
    
    def remove_alert(self, alert_id: str) -> bool:
        """
        Remove an alert from storage.
        
        Args:
            alert_id: ID of the alert
            
        Returns:
            True if successful, False otherwise
        """
        with self.storage_lock:
            if alert_id not in self.alerts:
                logger.warning(f"Alert {alert_id} not found")
                return False
            
            del self.alerts[alert_id]
            
            # Remove from persistent storage
            if self.storage_path:
                alert_path = os.path.join(self.storage_path, f"{alert_id}.json")
                if os.path.exists(alert_path):
                    try:
                        os.remove(alert_path)
                    except Exception as e:
                        logger.error(f"Error removing alert file {alert_path}: {e}")
            
            logger.debug(f"Removed alert {alert_id}")
            return True
    
    def cleanup_expired(self) -> int:
        """
        Remove expired alerts from storage.
        
        Returns:
            Number of alerts removed
        """
        with self.storage_lock:
            expired_ids = []
            
            # Find expired alerts
            for alert_id, alert in self.alerts.items():
                if alert.is_expired():
                    expired_ids.append(alert_id)
            
            # Remove expired alerts
            for alert_id in expired_ids:
                self.remove_alert(alert_id)
            
            logger.debug(f"Cleaned up {len(expired_ids)} expired alerts")
            return len(expired_ids)
    
    def _persist_alert(self, alert: Alert) -> None:
        """
        Persist an alert to disk.
        
        Args:
            alert: Alert to persist
        """
        if not self.storage_path:
            return
        
        try:
            alert_path = os.path.join(self.storage_path, f"{alert.id}.json")
            with open(alert_path, 'w') as f:
                json.dump(alert.to_dict(), f, indent=2)
        except Exception as e:
            logger.error(f"Error persisting alert {alert.id}: {e}")
    
    def _load_alerts(self) -> None:
        """Load alerts from persistent storage."""
        if not self.storage_path or not os.path.exists(self.storage_path):
            return
        
        try:
            # Find all alert files
            alert_files = [f for f in os.listdir(self.storage_path) if f.endswith('.json')]
            
            for file_name in alert_files:
                try:
                    file_path = os.path.join(self.storage_path, file_name)
                    with open(file_path, 'r') as f:
                        alert_data = json.load(f)
                    
                    alert = Alert.from_dict(alert_data)
                    self.alerts[alert.id] = alert
                
                except Exception as e:
                    logger.error(f"Error loading alert from {file_name}: {e}")
            
            logger.info(f"Loaded {len(self.alerts)} alerts from storage")
        
        except Exception as e:
            logger.error(f"Error loading alerts: {e}")


class AlertNotifier:
    """
    Handles notification delivery for alerts.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the alert notifier.
        
        Args:
            config: Optional notifier configuration
        """
        self.config = config or {}
        self.notification_methods = self.config.get('notification_methods', ['dashboard'])
        self.handlers = {
            'dashboard': self._notify_dashboard,
            'log': self._notify_log,
            'email': self._notify_email,
            'webhook': self._notify_webhook
        }
        
        # Add custom notification handlers
        custom_handlers = self.config.get('custom_handlers', {})
        self.handlers.update(custom_handlers)
    
    def notify(self, alert: Alert) -> bool:
        """
        Send notifications for an alert.
        
        Args:
            alert: Alert to notify about
            
        Returns:
            True if any notification was sent, False otherwise
        """
        if alert.notification_sent:
            logger.debug(f"Alert {alert.id} already notified")
            return False
        
        success = False
        
        # Only notify for active, non-expired alerts
        if not alert.is_active or alert.is_expired():
            return False
        
        # Filter by priority if configured
        min_priority = self.config.get('min_notification_priority')
        if min_priority:
            priority_order = {'critical': 3, 'high': 2, 'medium': 1, 'low': 0}
            alert_priority = priority_order.get(alert.priority, 0)
            min_priority_value = priority_order.get(min_priority, 0)
            
            if alert_priority < min_priority_value:
                logger.debug(f"Alert {alert.id} priority too low for notification")
                return False
        
        # Send notifications through configured methods
        for method in self.notification_methods:
            if method in self.handlers:
                try:
                    handler_success = self.handlers[method](alert)
                    success = success or handler_success
                except Exception as e:
                    logger.error(f"Error in {method} notification: {e}")
        
        if success:
            # Mark as notified
            alert.mark_notified()
        
        return success
    
    def _notify_dashboard(self, alert: Alert) -> bool:
        """
        Add alert to dashboard notification area.
        
        Args:
            alert: Alert to notify about
            
        Returns:
            True (dashboard notifications always succeed)
        """
        # Dashboard notifications are handled by the dashboard UI
        # This method just marks the alert as ready for dashboard display
        logger.debug(f"Alert {alert.id} ready for dashboard display")
        return True
    
    def _notify_log(self, alert: Alert) -> bool:
        """
        Log alert as a notification.
        
        Args:
            alert: Alert to notify about
            
        Returns:
            True (log notifications always succeed)
        """
        log_level = self.config.get('log_level', 'INFO')
        
        # Map priority to log level
        if log_level == 'AUTO':
            if alert.priority == 'critical':
                log_level = 'CRITICAL'
            elif alert.priority == 'high':
                log_level = 'ERROR'
            elif alert.priority == 'medium':
                log_level = 'WARNING'
            else:
                log_level = 'INFO'
        
        # Log the alert
        log_method = getattr(logger, log_level.lower())
        log_method(f"ALERT [{alert.priority.upper()}] {alert.message}")
        
        return True
    
    def _notify_email(self, alert: Alert) -> bool:
        """
        Send email notification for alert.
        
        Args:
            alert: Alert to notify about
            
        Returns:
            True if successful, False otherwise
        """
        # Email configuration
        email_config = self.config.get('email', {})
        recipients = email_config.get('recipients', [])
        
        if not recipients:
            logger.warning("No email recipients configured")
            return False
        
        # In a real implementation, this would send an email
        # For now, just log the action
        recipients_str = ', '.join(recipients)
        logger.info(f"Would send email alert to {recipients_str}: {alert.message}")
        
        return True
    
    def _notify_webhook(self, alert: Alert) -> bool:
        """
        Send webhook notification for alert.
        
        Args:
            alert: Alert to notify about
            
        Returns:
            True if successful, False otherwise
        """
        # Webhook configuration
        webhook_config = self.config.get('webhook', {})
        url = webhook_config.get('url')
        
        if not url:
            logger.warning("No webhook URL configured")
            return False
        
        # In a real implementation, this would post to a webhook
        # For now, just log the action
        logger.info(f"Would post alert to webhook {url}: {alert.message}")
        
        return True


class AlertManager(MLComponent):
    """
    Manager for ML-generated alerts, handling creation, storage, and notification.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the alert manager.
        
        Args:
            config: Optional configuration dictionary
        """
        # Get default configuration if not provided
        if config is None:
            config = get_component_config("alert_system")
        
        super().__init__("alert_manager", config)
        
        # Initialize alert storage
        storage_path = self.config.get('storage_path')
        if storage_path:
            # Handle relative paths
            if not os.path.isabs(storage_path):
                storage_path = os.path.abspath(storage_path)
            
            # Ensure directory exists
            os.makedirs(storage_path, exist_ok=True)
        
        self.storage = AlertStorage(storage_path)
        
        # Initialize notifier
        self.notifier = AlertNotifier(self.config.get('notifications', {}))
        
        # Start cleanup thread if enabled
        if self.config.get('auto_cleanup', True):
            self.cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
            self.cleanup_thread.start()
        
        logger.info("Alert manager initialized")
    
    def create_alert(self, alert_type: str, message: str, source_model: str, 
                    confidence: float, data: Dict[str, Any] = None,
                    priority: str = "medium",
                    expiration_hours: Optional[float] = None) -> Alert:
        """
        Create a new alert.
        
        Args:
            alert_type: Type of alert (e.g., 'pattern_detected', 'price_target')
            message: Human-readable alert message
            source_model: ID of the model that generated the alert
            confidence: Confidence level (0.0 to 1.0)
            data: Optional additional data related to the alert
            priority: Alert priority ('low', 'medium', 'high', 'critical')
            expiration_hours: Optional expiration time in hours
            
        Returns:
            Created alert
        """
        # Validate parameters
        if not alert_type or not message or not source_model:
            logger.error("Missing required alert parameters")
            raise ValueError("Missing required alert parameters")
        
        # Validate priority
        valid_priorities = ['low', 'medium', 'high', 'critical']
        if priority.lower() not in valid_priorities:
            logger.warning(f"Invalid priority: {priority}, using 'medium'")
            priority = 'medium'
        
        # Calculate expiration time if specified
        expiration = None
        if expiration_hours is not None:
            expiration = datetime.now() + timedelta(hours=expiration_hours)
        
        # Create alert
        alert = Alert(
            alert_type=alert_type,
            message=message,
            source_model=source_model,
            confidence=confidence,
            data=data,
            priority=priority,
            expiration=expiration
        )
        
        # Add to storage
        self.storage.add_alert(alert)
        
        # Send notifications if configured
        if self.config.get('notify_on_create', True):
            self.notifier.notify(alert)
        
        logger.info(f"Created alert: {alert.id} - {message}")
        return alert
    
    def create_alert_from_inference(self, inference_result: InferenceResult, 
                                  alert_config: Dict[str, Any]) -> Optional[Alert]:
        """
        Create an alert from an inference result based on configuration.
        
        Args:
            inference_result: Inference result
            alert_config: Alert generation configuration
            
        Returns:
            Created alert or None if no alert generated
        """
        # Extract info from inference result
        model_id = inference_result.model_id
        model_type = inference_result.model_type
        predictions = inference_result.predictions
        
        # Get alert type based on model type
        if model_type == 'pattern_recognition':
            return self._create_pattern_alert(model_id, predictions, alert_config)
        elif model_type == 'predictive_analytics':
            return self._create_prediction_alert(model_id, predictions, alert_config)
        elif model_type == 'strategy_optimizer':
            return self._create_strategy_alert(model_id, predictions, alert_config)
        else:
            logger.warning(f"Unsupported model type for alert generation: {model_type}")
            return None
    
    def _create_pattern_alert(self, model_id: str, 
                            predictions: Dict[str, Any], 
                            alert_config: Dict[str, Any]) -> Optional[Alert]:
        """
        Create an alert for pattern recognition results.
        
        Args:
            model_id: ID of the source model
            predictions: Pattern predictions
            alert_config: Alert configuration
            
        Returns:
            Created alert or None if no alert generated
        """
        # Check if pattern detected
        if 'pattern_indices' not in predictions or 'pattern_names' not in predictions:
            return None
        
        pattern_indices = predictions.get('pattern_indices', [])
        pattern_names = predictions.get('pattern_names', [])
        pattern_types = predictions.get('pattern_types', [])
        confidence_levels = predictions.get('confidence_levels', [])
        
        # Get confidence threshold from config
        confidence_threshold = alert_config.get('confidence_threshold', 0.7)
        
        # Find pattern(s) with confidence above threshold
        alert_patterns = []
        
        if 'pattern_probabilities' in predictions:
            probabilities = predictions['pattern_probabilities']
            
            for i, probs in enumerate(probabilities):
                if i < len(pattern_indices):
                    idx = pattern_indices[i]
                    if idx > 0 and idx < len(probs):  # Skip "No Pattern" (idx 0)
                        confidence = probs[idx]
                        if confidence >= confidence_threshold:
                            pattern_name = "Unknown"
                            if i < len(pattern_names):
                                pattern_name = pattern_names[i]
                            
                            confidence_level = "medium"
                            if i < len(confidence_levels):
                                confidence_level = confidence_levels[i]
                            
                            alert_patterns.append({
                                'name': pattern_name,
                                'confidence': confidence,
                                'confidence_level': confidence_level
                            })
        
        # Create alert if patterns found
        if alert_patterns:
            # Sort patterns by confidence
            alert_patterns.sort(key=lambda p: p['confidence'], reverse=True)
            
            # Take top pattern for alert
            top_pattern = alert_patterns[0]
            
            # Create alert message
            pattern_name = top_pattern['name']
            confidence = top_pattern['confidence']
            confidence_level = top_pattern['confidence_level']
            
            # Map confidence level to priority
            priority_map = {
                'high': 'high',
                'medium': 'medium',
                'low': 'low',
                'very_low': 'low'
            }
            
            priority = priority_map.get(confidence_level, 'medium')
            
            # Create alert message
            message = f"Detected {pattern_name} pattern with {confidence:.1%} confidence"
            
            # Create alert
            return self.create_alert(
                alert_type='pattern_detected',
                message=message,
                source_model=model_id,
                confidence=confidence,
                data={'patterns': alert_patterns},
                priority=priority,
                expiration_hours=alert_config.get('expiration_hours', 24)
            )
        
        return None
    
    def _create_prediction_alert(self, model_id: str, 
                               predictions: Dict[str, Any], 
                               alert_config: Dict[str, Any]) -> Optional[Alert]:
        """
        Create an alert for price prediction results.
        
        Args:
            model_id: ID of the source model
            predictions: Price predictions
            alert_config: Alert configuration
            
        Returns:
            Created alert or None if no alert generated
        """
        # Check if predictions exist
        if 'price_predictions' not in predictions:
            return None
        
        price_predictions = predictions['price_predictions']
        horizons = predictions.get('horizons', [1, 5, 10, 20])
        
        # Ensure predictions and horizons have the same length
        if len(horizons) > len(price_predictions):
            horizons = horizons[:len(price_predictions)]
        
        # Get alert thresholds from config
        threshold_pct = alert_config.get('threshold_percent', 5.0) / 100.0
        time_horizon = alert_config.get('time_horizon', 5)
        
        # Find prediction for specified horizon
        horizon_idx = None
        for i, h in enumerate(horizons):
            if h == time_horizon:
                horizon_idx = i
                break
        
        if horizon_idx is None or horizon_idx >= len(price_predictions):
            return None
        
        # Get prediction for target horizon
        prediction = price_predictions[horizon_idx]
        
        # Calculate percent change from current price
        percent_change = prediction - 1.0  # Assuming normalized prediction
        
        # Check if change exceeds threshold
        if abs(percent_change) >= threshold_pct:
            # Determine direction and format message
            direction = "up" if percent_change > 0 else "down"
            message = f"Price predicted to move {direction} by {abs(percent_change):.1%} in {time_horizon} periods"
            
            # Determine priority based on magnitude of change
            priority = "low"
            if abs(percent_change) >= threshold_pct * 3:
                priority = "high"
            elif abs(percent_change) >= threshold_pct * 2:
                priority = "medium"
            
            # Create alert
            return self.create_alert(
                alert_type='price_prediction',
                message=message,
                source_model=model_id,
                confidence=alert_config.get('confidence', 0.7),
                data={
                    'direction': direction,
                    'percent_change': percent_change,
                    'time_horizon': time_horizon,
                    'prediction': prediction
                },
                priority=priority,
                expiration_hours=alert_config.get('expiration_hours', 24)
            )
        
        return None
    
    def _create_strategy_alert(self, model_id: str, 
                             predictions: Dict[str, Any], 
                             alert_config: Dict[str, Any]) -> Optional[Alert]:
        """
        Create an alert for trading strategy results.
        
        Args:
            model_id: ID of the source model
            predictions: Strategy predictions
            alert_config: Alert configuration
            
        Returns:
            Created alert or None if no alert generated
        """
        # Check if signals exist
        if 'signals' not in predictions:
            return None
        
        signals = predictions['signals']
        
        if not signals:
            return None
        
        # Get most recent signal
        latest_signal = signals[-1]
        
        # Only create alerts for non-zero signals
        if latest_signal == 0:
            return None
        
        # Create alert message
        signal_type = "buy" if latest_signal > 0 else "sell"
        
        # Get confidence from predictions if available
        confidence = alert_config.get('confidence', 0.7)
        if 'signal_probabilities' in predictions:
            probs = predictions['signal_probabilities']
            if probs and len(probs) > 0:
                latest_probs = probs[-1]
                if len(latest_probs) >= 3:  # [sell, hold, buy]
                    idx = 2 if latest_signal > 0 else 0
                    confidence = latest_probs[idx]
        
        # Get predicted return if available
        predicted_return = None
        if 'predicted_returns' in predictions:
            returns = predictions['predicted_returns']
            if returns and len(returns) > 0:
                predicted_return = returns[-1]
        
        # Create message
        message = f"Strategy generated {signal_type} signal"
        if predicted_return is not None:
            message += f" with expected return of {predicted_return:.2%}"
        
        # Determine priority based on confidence
        priority = "low"
        if confidence >= 0.9:
            priority = "high"
        elif confidence >= 0.7:
            priority = "medium"
        
        # Create alert
        return self.create_alert(
            alert_type='strategy_signal',
            message=message,
            source_model=model_id,
            confidence=confidence,
            data={
                'signal_type': signal_type,
                'signal_value': latest_signal,
                'predicted_return': predicted_return
            },
            priority=priority,
            expiration_hours=alert_config.get('expiration_hours', 24)
        )
    
    def get_alert(self, alert_id: str) -> Optional[Alert]:
        """
        Get an alert by ID.
        
        Args:
            alert_id: ID of the alert
            
        Returns:
            Alert if found, None otherwise
        """
        return self.storage.get_alert(alert_id)
    
    def get_alerts(self, include_inactive: bool = False, 
                 include_acknowledged: bool = False,
                 alert_type: Optional[str] = None,
                 priority: Optional[Union[str, List[str]]] = None,
                 source_model: Optional[str] = None,
                 min_confidence: float = 0.0,
                 limit: Optional[int] = None,
                 sort_by: str = 'timestamp',
                 sort_desc: bool = True) -> List[Alert]:
        """
        Get alerts with optional filtering.
        
        Args:
            include_inactive: Whether to include inactive alerts
            include_acknowledged: Whether to include acknowledged alerts
            alert_type: Optional filter by alert type
            priority: Optional filter by priority (single or list)
            source_model: Optional filter by source model
            min_confidence: Minimum confidence level
            limit: Optional maximum number of alerts to return
            sort_by: Field to sort by
            sort_desc: Whether to sort in descending order
            
        Returns:
            List of matching alerts
        """
        return self.storage.get_alerts(
            include_inactive=include_inactive,
            include_acknowledged=include_acknowledged,
            alert_type=alert_type,
            priority=priority,
            source_model=source_model,
            min_confidence=min_confidence,
            limit=limit,
            sort_by=sort_by,
            sort_desc=sort_desc
        )
    
    def acknowledge_alert(self, alert_id: str, by: Optional[str] = None) -> bool:
        """
        Acknowledge an alert.
        
        Args:
            alert_id: ID of the alert
            by: Optional identifier of who acknowledged the alert
            
        Returns:
            True if successful, False otherwise
        """
        alert = self.storage.get_alert(alert_id)
        if not alert:
            logger.warning(f"Alert {alert_id} not found")
            return False
        
        alert.acknowledge(by)
        self.storage.update_alert(alert)
        
        logger.info(f"Alert {alert_id} acknowledged by {by or 'unknown'}")
        return True
    
    def remove_alert(self, alert_id: str) -> bool:
        """
        Remove an alert.
        
        Args:
            alert_id: ID of the alert
            
        Returns:
            True if successful, False otherwise
        """
        return self.storage.remove_alert(alert_id)
    
    def process_inference_result(self, inference_result: InferenceResult) -> List[Alert]:
        """
        Process an inference result and generate alerts if needed.
        
        Args:
            inference_result: Inference result to process
            
        Returns:
            List of created alerts
        """
        # Get alert configuration based on model type
        model_type = inference_result.model_type
        alert_configs = self.config.get('alert_configs', {}).get(model_type, [])
        
        if not alert_configs:
            logger.debug(f"No alert configurations found for model type: {model_type}")
            return []
        
        # Process each alert configuration
        created_alerts = []
        
        for alert_config in alert_configs:
            try:
                alert = self.create_alert_from_inference(inference_result, alert_config)
                if alert:
                    created_alerts.append(alert)
            except Exception as e:
                logger.error(f"Error creating alert from inference: {e}")
        
        return created_alerts
    
    def _cleanup_worker(self) -> None:
        """Worker thread for cleaning up expired alerts."""
        cleanup_interval = self.config.get('cleanup_interval_seconds', 3600)
        
        logger.info(f"Alert cleanup worker started with interval: {cleanup_interval} seconds")
        
        while True:
            try:
                # Sleep first to avoid immediate cleanup on startup
                time.sleep(cleanup_interval)
                
                # Cleanup expired alerts
                removed_count = self.storage.cleanup_expired()
                if removed_count > 0:
                    logger.info(f"Cleaned up {removed_count} expired alerts")
            
            except Exception as e:
                logger.error(f"Error in alert cleanup worker: {e}")
    
    def save(self, path: str) -> str:
        """
        Save the alert manager state.
        
        Args:
            path: Directory path to save the state
            
        Returns:
            Path to the saved state
        """
        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)
        
        # Save metadata and config
        metadata_path = os.path.join(path, 'alert_manager_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(self.metadata, f, indent=2)
        
        config_path = os.path.join(path, 'alert_manager_config.json')
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # Save alerts to a subdirectory
        alerts_path = os.path.join(path, 'alerts')
        os.makedirs(alerts_path, exist_ok=True)
        
        for alert in self.storage.get_alerts(include_inactive=True, include_acknowledged=True):
            alert_path = os.path.join(alerts_path, f"{alert.id}.json")
            with open(alert_path, 'w') as f:
                json.dump(alert.to_dict(), f, indent=2)
        
        logger.info(f"Alert manager state saved to {path}")
        return path
    
    @classmethod
    def load(cls, path: str) -> 'AlertManager':
        """
        Load an alert manager from saved state.
        
        Args:
            path: Path to the saved state
            
        Returns:
            Loaded alert manager
        """
        # Check if path exists
        if not os.path.exists(path):
            logger.error(f"Path not found: {path}")
            return None
        
        try:
            # Load config
            config_path = os.path.join(path, 'alert_manager_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
            else:
                logger.warning(f"Config file not found at {config_path}, using defaults")
                config = None
            
            # Create manager
            manager = cls(config)
            
            # Load metadata
            metadata_path = os.path.join(path, 'alert_manager_metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                manager.metadata = metadata
            
            # Load alerts
            alerts_path = os.path.join(path, 'alerts')
            if os.path.exists(alerts_path):
                alert_files = [f for f in os.listdir(alerts_path) if f.endswith('.json')]
                
                for file_name in alert_files:
                    try:
                        file_path = os.path.join(alerts_path, file_name)
                        with open(file_path, 'r') as f:
                            alert_data = json.load(f)
                        
                        alert = Alert.from_dict(alert_data)
                        manager.storage.add_alert(alert)
                    
                    except Exception as e:
                        logger.error(f"Error loading alert from {file_name}: {e}")
            
            logger.info(f"Alert manager loaded from {path}")
            return manager
        
        except Exception as e:
            logger.error(f"Error loading alert manager: {e}")
            return None


# Global instance
_alert_manager = None

def get_alert_manager(config: Optional[Dict[str, Any]] = None) -> AlertManager:
    """
    Get the global alert manager instance.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        AlertManager instance
    """
    global _alert_manager
    if _alert_manager is None:
        _alert_manager = AlertManager(config)
    return _alert_manager

def create_alert(alert_type: str, message: str, source_model: str, 
               confidence: float, data: Dict[str, Any] = None,
               priority: str = "medium",
               expiration_hours: Optional[float] = None) -> Alert:
    """
    Create a new alert using the global alert manager.
    
    Args:
        alert_type: Type of alert (e.g., 'pattern_detected', 'price_target')
        message: Human-readable alert message
        source_model: ID of the model that generated the alert
        confidence: Confidence level (0.0 to 1.0)
        data: Optional additional data related to the alert
        priority: Alert priority ('low', 'medium', 'high', 'critical')
        expiration_hours: Optional expiration time in hours
        
    Returns:
        Created alert
    """
    manager = get_alert_manager()
    return manager.create_alert(
        alert_type=alert_type,
        message=message,
        source_model=source_model,
        confidence=confidence,
        data=data,
        priority=priority,
        expiration_hours=expiration_hours
    )

def process_inference_result(inference_result: InferenceResult) -> List[Alert]:
    """
    Process an inference result and generate alerts if needed using the global alert manager.
    
    Args:
        inference_result: Inference result to process
        
    Returns:
        List of created alerts
    """
    manager = get_alert_manager()
    return manager.process_inference_result(inference_result)
