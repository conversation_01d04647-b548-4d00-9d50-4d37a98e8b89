{"agent_zero_options_analysis": {"agent_type": "AgentZeroOptionsAgent", "priority": "CRITICAL", "phase": "2", "description": "Agent Zero options intelligence with strike selection, expiration analysis, and execution quality assessment", "inputs": {"required": ["ticker", "options_chain", "underlying_price", "agent_zero_signal"], "optional": ["market_direction", "iv_environment", "risk_parameters"]}, "outputs": {"optimal_option_selection": "Strike, expiration, premium with liquidity assessment", "execution_recommendation": "Specific options trade recommendation", "risk_assessment": "Greeks exposure and risk metrics", "quality_score": "Overall execution quality rating"}, "workflow_file": "agent_zero_options_workflow.md", "quality_standards": "options_execution_standards.md", "performance_targets": {"max_execution_time": "150ms", "strike_selection_accuracy": "0.95", "liquidity_assessment_precision": "0.99"}, "dependencies": ["greek_enhancement_analysis", "options_flow_decoder"], "training_data_tags": ["options_selection", "strike_optimization", "execution_quality", "agent_zero_integration"]}}