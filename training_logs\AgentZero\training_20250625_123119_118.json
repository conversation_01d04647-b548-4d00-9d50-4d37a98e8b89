{"timestamp": "2025-06-25T12:31:19.118052", "signal_data": {"confidence": 0.8, "strength": 0.7, "execution_recommendation": "execute"}, "math_data": {"accuracy_score": 0.95, "precision": 0.001}, "decision": {"timestamp": "2025-06-25T12:31:19.118027", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.02, "action": "execute", "confidence": 0.7585, "reasoning": ["Rule-based decision: composite score 0.758", "Signal confidence: 0.800", "Signal strength: 0.700", "Execution recommendation: execute", "Math accuracy: 0.950", "ML system not available - using rule-based logic"], "composite_score": 0.7585, "decision_method": "rule_based", "ml_enhanced": false}, "outcome": 0.9, "market_context": {}, "agent_version": "1.5_Integrated"}