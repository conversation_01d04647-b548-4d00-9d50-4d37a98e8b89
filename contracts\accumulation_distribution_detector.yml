task_id: ACCUMULATION_DISTRIBUTION_DETECTOR
name: Accumulation Distribution Master Agent
version: 1.0.0
description: ML-powered institutional pattern detection agent specialized in
            accumulation vs distribution analysis with 78%+ accuracy through
            ensemble methods and adaptive thresholds.

inputs:
  - price_data: np.ndarray  # OHLC or close price data
  - volume_data: np.ndarray # Volume data array
  - symbol: str             # Ticker symbol
  - timeframe: str          # Optional timeframe

outputs:
  data:
    type: Dict[str, Any]
    schema: schemas/accumulation_distribution_detector_output_v1.json
    
success_criteria:
  perf_budget:
    max_runtime_ms: 5000
  precision_threshold: 0.001
  code_coverage_min: 0.90
  output_completeness: 1.00
  accuracy_target: 0.78
  dependency_constraints:
    max_additional_deps: 2
    forbidden_packages:
      - tensorflow
      - torch
      - opencv-python
    allowed_packages:
      - scikit-learn  # ML operations
      - scipy         # Statistical calculations

mathematical_requirements:
  - numerical_stability: true
  - precision_validation: required
  - statistical_rigor: enforced
  - ensemble_accuracy: ">= 78%"
  - dynamic_thresholds: required

specialization:
  focus: "THE ONE THING - Accumulation vs Distribution Detection"
  methodology: "Wyckoff + ML Enhancement + Dynamic Ranges"
  foundation: "Traditional formulas as base, ML for discovery"
  
features:
  traditional_formulas:
    - RSI
    - OBV
    - Williams_R
    - AD_Line
  ml_enhancements:
    - RSI_volume_divergence
    - time_weighted_returns
    - price_acceleration
    - volume_clustering
    - cross_timeframe_interactions
  advanced_patterns:
    - wyckoff_springs
    - no_supply_response
    - backup_patterns
    - volume_price_relationship
    - support_resistance_ml_enhanced

data_sources:
  primary: schwab_mcp
  required_fields:
    - OHLC_data
    - volume_data
    - timestamp
  
schwab_integration:
  mcp_server: "api/schwab_mcp_server.py"
  data_agent: "agents/schwab_data_agent.py"
  real_time: true
  
validation_framework:
  input_validation: comprehensive
  output_validation: probability_bounds_check
  precision_monitoring: continuous
  performance_tracking: enabled
