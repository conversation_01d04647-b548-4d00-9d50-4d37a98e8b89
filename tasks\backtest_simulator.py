#!/usr/bin/env python3
"""
B-04: Backtest Simulator
Simulates trading strategy based on trained ML model
"""

import os
import sys
import logging
import argparse
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))


class BacktestSimulator:
    """Simulates trading strategy using trained ML model"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def run(self, ticker, model_file=None, features_file=None, threshold=0.6, 
            commission=0.001, initial_capital=100000):
        """
        Run backtest simulation
        
        Args:
            ticker (str): Stock symbol
            model_file (str): Path to trained model file
            features_file (str): Path to features file
            threshold (float): Prediction probability threshold for trades
            commission (float): Commission rate per trade
            initial_capital (float): Starting capital
        
        Returns:
            dict: Backtest results and performance metrics
        """
        try:
            # Set default file paths
            if model_file is None:
                model_file = "models/backtest_model.pkl"
            if features_file is None:
                features_file = f"data/features/{ticker}_features.parquet"
            
            self.logger.info(f"Running backtest simulation for {ticker}")
            
            # Load model and features
            model_dict = self._load_model(model_file)
            df = self._load_features(features_file)
            
            # Generate predictions
            df_with_predictions = self._generate_predictions(df, model_dict, threshold)
            
            # Simulate trading
            trades, portfolio_history = self._simulate_trading(
                df_with_predictions, initial_capital, commission
            )
            
            # Calculate performance metrics
            performance = self._calculate_performance(trades, portfolio_history, initial_capital)
            
            # Shadow mode logging - capture backtest results
            self.log_backtest_results(ticker, performance, trades)
            
            # Generate report
            report_file = self._generate_report(ticker, performance, trades, portfolio_history)
            
            return {
                "status": "SUCCESS",
                "ticker": ticker,
                "report_file": report_file,
                "performance": performance,
                "total_trades": len(trades),
                "model_file": model_file
            }
            
        except Exception as e:
            self.logger.error(f"Backtest simulation failed for {ticker}: {e}")
            return {
                "status": "ERROR",
                "ticker": ticker,
                "error": str(e)
            }
    
    def _load_model(self, model_file):
        """Load trained model from file"""
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"Model file not found: {model_file}")
        
        model_dict = joblib.load(model_file)
        required_keys = ['model', 'scaler', 'features', 'model_type']
        
        if not all(key in model_dict for key in required_keys):
            raise ValueError(f"Invalid model file format. Expected keys: {required_keys}")
        
        self.logger.info(f"Loaded {model_dict['model_type']} model with {len(model_dict['features'])} features")
        return model_dict
    
    def _load_features(self, features_file):
        """Load features data"""
        if not os.path.exists(features_file):
            raise FileNotFoundError(f"Features file not found: {features_file}")
        
        df = pd.read_parquet(features_file)
        self.logger.info(f"Loaded {len(df)} rows of features data")
        return df
    
    def _generate_predictions(self, df, model_dict, threshold):
        """Generate trading predictions using the model"""
        df = df.copy()
        
        # Extract model components
        model = model_dict['model']
        scaler = model_dict['scaler']
        feature_cols = model_dict['features']
        
        # Prepare features
        X = df[feature_cols].fillna(0)  # Handle any remaining NaN
        X_scaled = scaler.transform(X)
        
        # Generate predictions
        predictions = model.predict(X_scaled)
        probabilities = model.predict_proba(X_scaled)[:, 1]
        
        # Add to dataframe
        df['prediction'] = predictions
        df['probability'] = probabilities
        df['signal'] = (probabilities > threshold).astype(int)
        
        self.logger.info(f"Generated {df['signal'].sum()} buy signals out of {len(df)} periods")
        return df
    
    def _simulate_trading(self, df, initial_capital, commission):
        """Simulate trading based on signals"""
        portfolio_value = initial_capital
        cash = initial_capital
        position = 0  # Number of shares held
        trades = []
        portfolio_history = []
        
        for i, row in df.iterrows():
            current_price = row['c']  # Close price
            signal = row['signal']
            probability = row['probability']
            
            # Record portfolio state
            portfolio_value = cash + (position * current_price)
            portfolio_history.append({
                'timestamp': row.get('t', i),
                'price': current_price,
                'position': position,
                'cash': cash,
                'portfolio_value': portfolio_value,
                'signal': signal,
                'probability': probability
            })
            
            # Trading logic
            if signal == 1 and position == 0:  # Buy signal and no position
                # Calculate shares to buy (use 95% of available cash to leave buffer)
                shares_to_buy = int((cash * 0.95) / current_price)
                if shares_to_buy > 0:
                    trade_value = shares_to_buy * current_price
                    commission_cost = trade_value * commission
                    
                    if cash >= trade_value + commission_cost:
                        # Execute buy
                        position = shares_to_buy
                        cash -= (trade_value + commission_cost)
                        
                        trades.append({
                            'timestamp': row.get('t', i),
                            'action': 'BUY',
                            'shares': shares_to_buy,
                            'price': current_price,
                            'value': trade_value,
                            'commission': commission_cost,
                            'probability': probability
                        })
            
            elif signal == 0 and position > 0:  # Sell signal and holding position
                # Sell all shares
                trade_value = position * current_price
                commission_cost = trade_value * commission
                
                cash += (trade_value - commission_cost)
                
                trades.append({
                    'timestamp': row.get('t', i),
                    'action': 'SELL',
                    'shares': position,
                    'price': current_price,
                    'value': trade_value,
                    'commission': commission_cost,
                    'probability': probability
                })
                
                position = 0
        
        # Close any remaining position at the end
        if position > 0:
            final_price = df.iloc[-1]['c']
            trade_value = position * final_price
            commission_cost = trade_value * commission
            cash += (trade_value - commission_cost)
            
            trades.append({
                'timestamp': df.iloc[-1].get('t', len(df)-1),
                'action': 'SELL',
                'shares': position,
                'price': final_price,
                'value': trade_value,
                'commission': commission_cost,
                'probability': 0.0
            })
            
            position = 0
        
        # Update final portfolio value
        final_portfolio_value = cash + (position * df.iloc[-1]['c'])
        portfolio_history.append({
            'timestamp': df.iloc[-1].get('t', len(df)),
            'price': df.iloc[-1]['c'],
            'position': position,
            'cash': cash,
            'portfolio_value': final_portfolio_value,
            'signal': 0,
            'probability': 0.0
        })
        
        return trades, portfolio_history
    
    def _calculate_performance(self, trades, portfolio_history, initial_capital):
        """Calculate performance metrics"""
        if not portfolio_history:
            return {}
        
        portfolio_df = pd.DataFrame(portfolio_history)
        
        # Basic metrics
        final_value = portfolio_df['portfolio_value'].iloc[-1]
        total_return = (final_value / initial_capital) - 1
        
        # Calculate daily returns
        portfolio_df['daily_return'] = portfolio_df['portfolio_value'].pct_change().fillna(0)
        
        # Performance metrics
        avg_daily_return = portfolio_df['daily_return'].mean()
        volatility = portfolio_df['daily_return'].std()
        sharpe_ratio = (avg_daily_return / volatility * np.sqrt(252)) if volatility > 0 else 0
        
        # Drawdown calculation
        portfolio_df['cummax'] = portfolio_df['portfolio_value'].cummax()
        portfolio_df['drawdown'] = (portfolio_df['portfolio_value'] / portfolio_df['cummax']) - 1
        max_drawdown = portfolio_df['drawdown'].min()
        
        # Trade analysis
        if trades:
            buy_trades = [t for t in trades if t['action'] == 'BUY']
            sell_trades = [t for t in trades if t['action'] == 'SELL']
            
            # Pair up buy/sell trades to calculate individual trade returns
            trade_returns = []
            for i in range(min(len(buy_trades), len(sell_trades))):
                buy_price = buy_trades[i]['price']
                sell_price = sell_trades[i]['price']
                trade_return = (sell_price / buy_price) - 1
                trade_returns.append(trade_return)
            
            win_rate = len([r for r in trade_returns if r > 0]) / len(trade_returns) if trade_returns else 0
            avg_trade_return = np.mean(trade_returns) if trade_returns else 0
        else:
            win_rate = 0
            avg_trade_return = 0
        
        return {
            'initial_capital': initial_capital,
            'final_value': final_value,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown * 100,
            'volatility': volatility,
            'win_rate': win_rate,
            'avg_trade_return': avg_trade_return,
            'total_trades': len(trades),
            'total_commission': sum(t['commission'] for t in trades)
        }
    
    def log_backtest_results(self, ticker, performance, trades):
        """Log backtest results for shadow mode learning"""
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            # Convert backtest performance to signal format
            signal_data = {
                'confidence': min(performance['win_rate'] / 100, 1.0),  # Win rate as confidence
                'strength': min(abs(performance['total_return']) / 100, 1.0),  # Return magnitude as strength
                'execution_recommendation': 'execute' if performance['total_return'] > 0 else 'avoid'
            }
            
            math_data = {
                'accuracy_score': performance['win_rate'] / 100,  # Win rate as accuracy
                'precision': 0.001  # High precision for backtest
            }
            
            market_context = {
                'system': 'BACKTEST_PERFORMANCE_ANALYZER',  # Enhanced source name
                'source_file': 'backtest_simulator.py',
                'source_agent': 'BACKTEST_ENGINE',
                'intelligence_type': 'PERFORMANCE_VALIDATION',
                'ticker': ticker,
                'total_trades': performance['total_trades'],
                'sharpe_ratio': performance['sharpe_ratio'],
                'max_drawdown': performance['max_drawdown_pct'],
                'backtest_timestamp': datetime.now().isoformat()
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'backtest_complete', 'performance': performance},
                outcome=performance['total_return'],  # Actual return as outcome
                market_context=market_context
            )
            self.logger.info(f"Shadow mode: Backtest results logged for {ticker}")
            
        except Exception as e:
            self.logger.warning(f"Shadow mode logging failed: {e}")
    
    def _generate_report(self, ticker, performance, trades, portfolio_history):
        """Generate backtest report"""
        report_dir = Path("reports")
        report_dir.mkdir(parents=True, exist_ok=True)
        
        today = datetime.now().strftime("%Y-%m-%d")
        report_file = report_dir / f"backtest_{ticker}_{today}.md"
        
        # Generate markdown report
        report_content = f"""# Backtest Report - {ticker}

**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Summary

| Metric | Value |
|--------|--------|
| Initial Capital | ${performance['initial_capital']:,.2f} |
| Final Value | ${performance['final_value']:,.2f} |
| Total Return | {performance['total_return_pct']:.2f}% |
| Sharpe Ratio | {performance['sharpe_ratio']:.3f} |
| Max Drawdown | {performance['max_drawdown_pct']:.2f}% |
| Win Rate | {performance['win_rate']:.2f}% |
| Total Trades | {performance['total_trades']} |
| Total Commission | ${performance['total_commission']:.2f} |

## Performance Analysis

The strategy generated a total return of **{performance['total_return_pct']:.2f}%** over the backtest period.

- **Risk-Adjusted Performance:** Sharpe ratio of {performance['sharpe_ratio']:.3f}
- **Maximum Drawdown:** {performance['max_drawdown_pct']:.2f}%
- **Trading Activity:** {performance['total_trades']} total trades
- **Trade Success Rate:** {performance['win_rate']:.1%} winning trades

## Model Performance

- **Average Trade Return:** {performance['avg_trade_return']:.2%}
- **Volatility:** {performance['volatility']:.3f}

## Trade Summary

"""
        
        if trades:
            report_content += "### Recent Trades\n\n"
            report_content += "| Timestamp | Action | Shares | Price | Value | Commission |\n"
            report_content += "|-----------|--------|--------|-------|-------|------------|\n"
            
            # Show last 10 trades
            recent_trades = trades[-10:] if len(trades) > 10 else trades
            for trade in recent_trades:
                timestamp = trade['timestamp'] if isinstance(trade['timestamp'], str) else str(trade['timestamp'])
                report_content += f"| {timestamp} | {trade['action']} | {trade['shares']} | ${trade['price']:.2f} | ${trade['value']:.2f} | ${trade['commission']:.2f} |\n"
        
        report_content += f"""

## Methodology

This backtest was performed using a machine learning model trained on historical data.
The model generates buy/sell signals based on technical indicators and price patterns.

**Risk Disclaimer:** Past performance does not guarantee future results. This is for educational purposes only.
"""
        
        # Write report to file
        with open(report_file, 'w') as f:
            f.write(report_content)
        
        self.logger.info(f"Generated report: {report_file}")
        return str(report_file)


def main():
    """Command line interface for backtest simulation"""
    parser = argparse.ArgumentParser(description="Run backtest simulation")
    parser.add_argument("--ticker", required=True, help="Stock ticker symbol")
    parser.add_argument("--model", help="Path to model file")
    parser.add_argument("--features", help="Path to features file")
    parser.add_argument("--threshold", type=float, default=0.6, help="Prediction threshold")
    parser.add_argument("--commission", type=float, default=0.001, help="Commission rate")
    parser.add_argument("--capital", type=float, default=100000, help="Initial capital")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Execute backtest
    simulator = BacktestSimulator()
    result = simulator.run(
        ticker=args.ticker.upper(),
        model_file=args.model,
        features_file=args.features,
        threshold=args.threshold,
        commission=args.commission,
        initial_capital=args.capital
    )
    
    # Output results
    if result["status"] == "SUCCESS":
        print("SUCCESS: Backtest simulation completed")
        print(f"Ticker: {result['ticker']}")
        print(f"Report: {result['report_file']}")
        print(f"Total Trades: {result['total_trades']}")
        
        perf = result['performance']
        print(f"\nKey Metrics:")
        print(f"  Total Return: {perf['total_return_pct']:.2f}%")
        print(f"  Sharpe Ratio: {perf['sharpe_ratio']:.3f}")
        print(f"  Max Drawdown: {perf['max_drawdown_pct']:.2f}%")
        print(f"  Win Rate: {perf['win_rate']:.1%}")
        
        return 0
    else:
        print(f"ERROR: {result['error']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
