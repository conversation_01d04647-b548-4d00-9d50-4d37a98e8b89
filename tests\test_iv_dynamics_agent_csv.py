import unittest
import pandas as pd
from pathlib import Path
import json
from unittest import mock
import sys
import os
from datetime import datetime

# Add the parent directory to the sys.path to allow imports from the 'agents' directory
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.iv_dynamics_agent import IVDynamicsAgent

class TestIVDynamicsAgent(unittest.TestCase):

    TEST_DATA_DIR = Path(__file__).parent / "test_data"
    SAMPLE_CSV_PATH = TEST_DATA_DIR / "sample_iv_dynamics_data.csv"
    OUTPUT_DIR = Path(__file__).parent.parent / "iv_analysis"

    @classmethod
    def setUpClass(cls):
        """Set up test data and directories once for all tests."""
        cls.TEST_DATA_DIR.mkdir(parents=True, exist_ok=True)
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        # Create a dummy CSV file for testing
        data = {
            'date': pd.to_datetime([f'2024-01-{i:02d}' for i in range(1, 32)]),
            'iv_rank': [i / 31 for i in range(31)],
            'iv_roc': [i / 1000 for i in range(31)],
            'iv_level': [0.2 + i / 100 for i in range(31)],
            'c': [100 + i for i in range(31)]
        }
        df = pd.DataFrame(data)
        df.to_csv(cls.SAMPLE_CSV_PATH, index=False)
        print(f"Created dummy CSV file at: {cls.SAMPLE_CSV_PATH}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test data and directories after all tests."""
        if cls.SAMPLE_CSV_PATH.exists():
            cls.SAMPLE_CSV_PATH.unlink()
        if cls.TEST_DATA_DIR.exists() and not any(cls.TEST_DATA_DIR.iterdir()):
            cls.TEST_DATA_DIR.rmdir()
        
        today_str = datetime.now().strftime("%Y-%m-%d")
        dynamics_file = cls.OUTPUT_DIR / today_str / "TEST_TICKER_iv_dynamics.json"
        regime_file = cls.OUTPUT_DIR / today_str / "TEST_TICKER_vol_regime.json"
        if dynamics_file.exists():
            dynamics_file.unlink()
        if regime_file.exists():
            regime_file.unlink()
        
        daily_output_dir = cls.OUTPUT_DIR / today_str
        if daily_output_dir.exists() and not any(daily_output_dir.iterdir()):
            daily_output_dir.rmdir()

    @mock.patch('enhanced_data_agent_broker_integration.EnhancedDataAgent')
    @mock.patch('enhanced_greeks_engine.EnhancedGreeksEngine')
    def test_execute_iv_dynamics_analysis(self, MockEnhancedGreeksEngine, MockEnhancedDataAgent):
        """Test the execute method of IVDynamicsAgent."""
        
        # Get the mock instance that EnhancedGreeksEngine() will return
        mock_greeks_engine_instance = MockEnhancedGreeksEngine.return_value

        # Configure the mock EnhancedGreeksEngine instance
        mock_greeks_engine_instance.get_enhanced_greeks.return_value = {
            'ticker': 'TEST_TICKER',
            'timestamp': datetime.now().isoformat(),
            'data_source': 'mock',
            'quality_score': 1.0,
            'greeks': {'delta_overall_mean': 0.5, 'gamma_overall_mean': 0.1},
            'validation': {},
            'fallback_used': False,
            'performance': {'execution_time_ms': 10, 'mcp_attempted': True, 'mcp_successful': True}
        }
        mock_greeks_engine_instance.get_greeks_with_roc.return_value = {
            'greeks': {'delta_overall_mean': 0.5, 'gamma_overall_mean': 0.1, 'gamma_roc': 0.01},
            'roc_derivatives': {'gamma_roc': 0.01},
            '_data_source': 'mock',
            '_quality_score': 1.0,
            '_fallback_used': False
        }
        mock_greeks_engine_instance.get_greeks_quality_info.return_value = {'data_source': 'mock', 'quality_score': 1.0}

        mock_data_agent = MockEnhancedDataAgent.return_value
        mock_data_agent.get_market_data.return_value = None

        agent = IVDynamicsAgent()
        ticker = "TEST_TICKER"
        
        # Load CSV data
        iv_data = pd.read_csv(self.SAMPLE_CSV_PATH)
        iv_data['date'] = pd.to_datetime(iv_data['date'])
        price_data = iv_data.copy()  # Use same data for both
        
        # Execute the agent
        result = agent.execute(ticker, iv_data=iv_data, price_data=price_data)

        # Assert that the correct methods were called on the mock objects
        mock_greeks_engine_instance.get_enhanced_greeks.assert_called_once_with(ticker)
        mock_greeks_engine_instance.get_greeks_with_roc.assert_called_once_with(ticker)
        mock_greeks_engine_instance.get_greeks_quality_info.assert_called_once_with(ticker)

        # Assert that the output files were created
        self.assertEqual(result['status'], 'SUCCESS')
        self.assertTrue(Path(result['iv_dynamics_file']).exists())
        self.assertTrue(Path(result['regime_file']).exists())

        # Load the output files and verify their content
        with open(result['iv_dynamics_file'], 'r') as f:
            dynamics_data = json.load(f)
        
        self.assertEqual(dynamics_data['ticker'], ticker)
        self.assertIn('iv_roc_analysis', dynamics_data)
        self.assertIn('slope_analysis', dynamics_data)
        self.assertIn('regime_analysis', dynamics_data)
        self.assertIn('correlation_analysis', dynamics_data)

if __name__ == '__main__':
    unittest.main()