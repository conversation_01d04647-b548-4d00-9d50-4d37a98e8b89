"""
Liquidity Void Analyzer - Factor Output Version

Transforms the LiquidityVoidStrategy into a factor-generating analyzer
that outputs standardized FactorData objects for ML integration.

Strategy Logic converted to Factor Generation:
1. Calculate Volume Profile from historical data to find LVNs (voids) and HVNs (targets).
2. Monitor if current price is approaching a significant LVN (void).
3. Calculate Flow Velocity (price * volume trend) to confirm momentum into/through the void.
4. Generate factors for detected void scenarios instead of trading signals.

PRODUCTION READY - Outputs FactorData objects for system integration.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging
import math
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

# Import factor data structures
try:
    from factor_specification import FactorData, DirectionBias, TimeFrame, normalize_strength_score
except ImportError:
    try:
        from .factor_specification import FactorData, DirectionBias, TimeFrame, normalize_strength_score
    except ImportError:
        from factor_specification import FactorData, DirectionBias, TimeFrame, normalize_strength_score

# Import base analyzer
try:
    from analyzers.base_analyzer import BaseAnalyzer
except ImportError:
    try:
        from base_analyzer import BaseAnalyzer
    except ImportError:
        from base_analyzer import BaseAnalyzer

# --- API Gateway Import ---
try:
    from ..api_robustness.unified_api_gateway import get_api_gateway
    UNIFIED_GATEWAY_AVAILABLE = True
except ImportError:
    try:
        import sys
        import os
        src_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)
        from api_robustness.unified_api_gateway import get_api_gateway
        UNIFIED_GATEWAY_AVAILABLE = True
    except ImportError as e:
        UNIFIED_GATEWAY_AVAILABLE = False
        logging.critical(f"CRITICAL: Unified API Gateway module not found. Error: {e}")
        get_api_gateway = None

logger = logging.getLogger(__name__)


class LiquidityVoidAnalyzer(BaseAnalyzer):
    """
    Liquidity Void Analyzer - Detects and generates factors for liquidity void scenarios.
    
    Analyzes price movements through areas with minimal liquidity (LVN zones) and
    outputs standardized FactorData objects for system integration.
    """

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for liquidity void analysis."""
        return {
            'enabled': True,
            'min_confidence': 0.65,
            'max_factors_per_ticker': 3,  # Limit number of factors generated
            'require_api_data': True,  # Void analysis requires fresh API data
            
            # Data fetching configuration
            'volume_profile_bins': 50,
            'analysis_lookback_bars': 100,
            'primary_timeframe_polygon': 'minute',
            'primary_timeframe_multiplier': 15,

            'void_detection': {
                'min_void_size_pct_of_range': 0.02,
                'max_void_distance_pct_from_price': 0.005,
                'lvn_volume_threshold_vs_mean': 0.3,
            },

            'momentum_confirmation': {
                'min_flow_velocity_normalized': 0.3,
                'velocity_lookback_bars': 10,
                'confirmation_bars': 3,
                'min_volume_increase_factor': 1.1,
                'min_price_move_pct_confirm': 0.002,
            },

            'factor_strength': {
                'base_strength': 0.6,
                'velocity_multiplier': 0.3,
                'void_size_multiplier': 0.2,
                'proximity_bonus': 0.1
            }
        }

    def __init__(self, config: Optional[Dict[str, Any]] = None,
                 system_config: Optional[Dict[str, Any]] = None,
                 api_gateway_instance=None):
        """Initialize the Liquidity Void Analyzer."""
        super().__init__(config, system_config, api_gateway_instance)
        self.system_config = system_config or {}
        self.api_gateway = api_gateway_instance or None
        
        # Update config with defaults
        default_config = self._get_default_config()
        if config:
            default_config.update(config)
        self.config = default_config
        
        # Use provided API gateway or initialize if needed
        if not self.api_gateway:
            self._initialize_api_gateway()
        
        logger.info("LiquidityVoidAnalyzer initialized")

    def _initialize_api_gateway(self):
        """Initialize API gateway for data fetching - REQUIRED for void analysis."""
        if UNIFIED_GATEWAY_AVAILABLE and get_api_gateway:
            try:
                self.api_gateway = get_api_gateway()
                logger.info("API Gateway initialized for void analysis")
                
                # Verify API connectivity
                if hasattr(self.api_gateway, 'test_connection'):
                    try:
                        connected = self.api_gateway.test_connection()
                        if connected:
                            logger.info("API Gateway connection verified")
                        else:
                            logger.warning("API Gateway connection test failed")
                    except:
                        logger.warning("API Gateway connection test unavailable")
                        
            except Exception as e:
                logger.error(f"Failed to initialize API Gateway: {e}")
                self.api_gateway = None
        else:
            logger.error("Unified API Gateway not available - void analyzer cannot function without API data")
            self.api_gateway = None

    def analyze_factors(self, data_package=None, **kwargs) -> List[FactorData]:
        """
        Analyze liquidity voids and generate FactorData objects.
        
        Args:
            ticker: Stock symbol to analyze
            current_price: Current market price
            mtf_data: Multi-timeframe price data
            **kwargs: Additional analysis data
            
        Returns:
            List[FactorData]: Factors detected by void analysis
        """
        factors = []
        
        # Extract parameters from data_package or kwargs
        if data_package:
            ticker = data_package.get('ticker', 'UNKNOWN')
            current_price = data_package.get('current_price')
            mtf_data = data_package.get('mtf_market_data', {})
        else:
            ticker = kwargs.get('ticker', 'UNKNOWN')
            current_price = kwargs.get('current_price')
            mtf_data = kwargs.get('mtf_data', {})
        
        if not self.config.get('enabled', True):
            return factors

        # API validation - prefer fresh data but can work with provided data
        if not self.api_gateway:
            logger.debug(f"[{ticker}] API Gateway not available - using provided market data for void analysis")

        try:
            # Validate inputs
            if current_price <= 0:
                logger.warning(f"[{ticker}] Invalid current_price: {current_price}")
                return factors

            # Get the primary timeframe data
            primary_tf = self._get_primary_timeframe()
            price_data_df = self._get_price_data(ticker, mtf_data, primary_tf, kwargs)
            
            if price_data_df is None or price_data_df.empty:
                logger.warning(f"[{ticker}] Insufficient price data for void analysis")
                return factors

            if len(price_data_df) < self.config['analysis_lookback_bars']:
                logger.warning(f"[{ticker}] Insufficient data: {len(price_data_df)} bars")
                return factors

            logger.debug(f"[{ticker}] Starting void analysis. Price: ${current_price:.2f}, Data: {len(price_data_df)} bars")

            # 1. Identify liquidity voids using volume profile
            voids, hvn_levels = self._identify_liquidity_voids_and_hvns(ticker, price_data_df)
            if not voids:
                logger.debug(f"[{ticker}] No liquidity voids found")
                return factors

            # 2. Check for price approaching or inside void zones
            active_void_scenarios = self._filter_active_void_scenarios(current_price, voids, hvn_levels)
            if not active_void_scenarios:
                logger.debug(f"[{ticker}] Price not actively interacting with any voids")
                return factors

            # 3. Analyze momentum and generate factors for each scenario
            for void_scenario in active_void_scenarios:
                # Calculate flow velocity
                flow_velocity_norm, flow_velocity_raw = self._calculate_flow_velocity(price_data_df)

                if flow_velocity_norm < self.config['momentum_confirmation']['min_flow_velocity_normalized']:
                    logger.debug(f"[{ticker}] Flow velocity {flow_velocity_norm:.2f} too low for void")
                    continue

                # Confirm momentum aligns with direction
                required_velocity_sign = 1 if void_scenario['direction_bias'] == DirectionBias.BULLISH else -1
                if np.sign(flow_velocity_raw) != required_velocity_sign and flow_velocity_raw != 0:
                    logger.debug(f"[{ticker}] Flow velocity direction misaligned")
                    continue

                if not self._confirm_recent_momentum(price_data_df, required_velocity_sign):
                    logger.debug(f"[{ticker}] Recent momentum not confirmed")
                    continue

                # Create factor for this void scenario
                factor = self._create_void_factor(
                    ticker, current_price, void_scenario, 
                    flow_velocity_norm, primary_tf
                )
                
                if factor:
                    factors.append(factor)

            # Limit number of factors
            max_factors = self.config.get('max_factors_per_ticker', 3)
            if len(factors) > max_factors:
                # Sort by strength and take top factors
                factors.sort(key=lambda f: f.strength_score, reverse=True)
                factors = factors[:max_factors]

            logger.info(f"[{ticker}] Generated {len(factors)} void factors")
            return factors

        except Exception as e:
            logger.error(f"[{ticker}] Error in void factor analysis: {e}", exc_info=True)
            return factors

    def analyze(self, ticker: str, mtf_data: Dict[str, pd.DataFrame], 
                current_price: float, **kwargs) -> List[FactorData]:
        """
        Required analyze method from BaseAnalyzer interface.
        
        Args:
            ticker: Symbol being analyzed
            mtf_data: Multi-timeframe price data (string keys)
            current_price: Current market price
            **kwargs: Additional data
            
        Returns:
            List of FactorData objects
        """
        # Convert string timeframe keys to TimeFrame enum for analyze_factors
        mtf_data_enum = {}
        for tf_str, data in mtf_data.items():
            if tf_str == '1m':
                mtf_data_enum[TimeFrame.MIN_1] = data
            elif tf_str == '5m':
                mtf_data_enum[TimeFrame.MIN_5] = data
            elif tf_str == '15m':
                mtf_data_enum[TimeFrame.MIN_15] = data
            elif tf_str == '1h':
                mtf_data_enum[TimeFrame.HOUR_1] = data
            elif tf_str == '4h':
                mtf_data_enum[TimeFrame.HOUR_4] = data
            elif tf_str == '1d':
                mtf_data_enum[TimeFrame.DAY_1] = data
        
        return self.analyze_factors(ticker, current_price, mtf_data_enum, **kwargs)

    def _get_primary_timeframe(self) -> TimeFrame:
        """Get the primary timeframe for analysis."""
        # Convert config timeframe to TimeFrame enum
        poly_timespan = self.config['primary_timeframe_polygon']
        poly_multiplier = self.config['primary_timeframe_multiplier']
        
        # Map to TimeFrame enum
        if poly_timespan == 'minute':
            if poly_multiplier == 1:
                return TimeFrame.MIN_1
            elif poly_multiplier == 5:
                return TimeFrame.MIN_5
            elif poly_multiplier == 15:
                return TimeFrame.MIN_15
            elif poly_multiplier == 30:
                return TimeFrame.MIN_30
        elif poly_timespan == 'hour':
            if poly_multiplier == 1:
                return TimeFrame.HOUR_1
            elif poly_multiplier == 4:
                return TimeFrame.HOUR_4
        elif poly_timespan == 'day':
            return TimeFrame.DAY_1
        elif poly_timespan == 'week':
            return TimeFrame.WEEK_1
        
        # Default fallback
        return TimeFrame.MIN_15

    def _get_price_data(self, ticker: str, mtf_data: Dict[TimeFrame, pd.DataFrame],
                       primary_tf: TimeFrame, kwargs: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Get price data for analysis - prioritizes API data for freshness."""

        # PRIORITY 1: Try provided MTF data first (more reliable in test environment)
        if primary_tf in mtf_data and not mtf_data[primary_tf].empty:
            df = mtf_data[primary_tf]
            if len(df) >= self.config['analysis_lookback_bars']:
                logger.debug(f"[{ticker}] Using provided MTF data for {primary_tf.value}: {len(df)} bars")
                return df.copy()
            else:
                logger.debug(f"[{ticker}] MTF data has {len(df)} bars, need {self.config['analysis_lookback_bars']}")

        # PRIORITY 2: Try any available timeframe data
        for tf, df in mtf_data.items():
            if df is not None and not df.empty and len(df) >= self.config['analysis_lookback_bars']:
                logger.debug(f"[{ticker}] Using alternative timeframe {tf.value}: {len(df)} bars")
                return df.copy()

        # PRIORITY 3: Fetch fresh data from API if available
        if self.api_gateway:
            logger.debug(f"[{ticker}] Fetching fresh data from API for void analysis")
            api_data = self._fetch_historical_data(ticker)
            if api_data is not None and len(api_data) >= self.config['analysis_lookback_bars']:
                logger.info(f"[{ticker}] Using fresh API data: {len(api_data)} bars")
                return api_data

        # FALLBACK: Try kwargs data
        if 'price_data' in kwargs and isinstance(kwargs['price_data'], pd.DataFrame):
            df = kwargs['price_data']
            if not df.empty and len(df) >= self.config['analysis_lookback_bars']:
                logger.debug(f"[{ticker}] Using fallback price_data from kwargs: {len(df)} bars")
                return df.copy()

        logger.debug(f"[{ticker}] No suitable price data available for void analysis")
        return None

    def _fetch_historical_data(self, ticker: str) -> Optional[pd.DataFrame]:
        """Fetch historical price data via API gateway - enhanced for void analysis."""
        try:
            poly_timespan = self.config['primary_timeframe_polygon']
            poly_multiplier = self.config['primary_timeframe_multiplier']
            lookback_bars = self.config['analysis_lookback_bars']

            # Calculate date range with buffer for weekends/holidays
            now = datetime.now()
            if poly_timespan == 'minute':
                # For intraday data, get extra days to account for market hours
                buffer_multiplier = 3.0  # Extra buffer for intraday
                delta = timedelta(minutes=poly_multiplier * lookback_bars * buffer_multiplier)
            elif poly_timespan == 'hour':
                buffer_multiplier = 2.0
                delta = timedelta(hours=poly_multiplier * lookback_bars * buffer_multiplier)
            elif poly_timespan == 'day':
                buffer_multiplier = 1.5
                delta = timedelta(days=poly_multiplier * lookback_bars * buffer_multiplier)
            else:
                buffer_multiplier = 2.0
                delta = timedelta(days=poly_multiplier * lookback_bars * 7 * buffer_multiplier)

            from_date = (now - delta).strftime('%Y-%m-%d')
            to_date = now.strftime('%Y-%m-%d')

            logger.info(f"[{ticker}] Fetching void analysis data: {poly_multiplier}{poly_timespan} from {from_date} to {to_date}")

            # Attempt to fetch data
            price_df = self.api_gateway.get_price_data(
                ticker=ticker,
                timespan=poly_timespan,
                multiplier=poly_multiplier,
                from_date=from_date,
                to_date=to_date
            )

            if price_df is not None and not price_df.empty:
                logger.debug(f"[{ticker}] API returned {len(price_df)} bars")
                
                # Ensure datetime index
                if not isinstance(price_df.index, pd.DatetimeIndex):
                    if 'timestamp' in price_df.columns:
                        price_df = price_df.set_index(pd.to_datetime(price_df['timestamp'])).sort_index()
                    elif 't' in price_df.columns:  # Polygon format
                        price_df = price_df.set_index(pd.to_datetime(price_df['t'])).sort_index()
                    else:
                        logger.error(f"[{ticker}] No timestamp column found in API data")
                        return None

                # Validate required columns
                required_cols = ['open', 'high', 'low', 'close', 'volume']
                missing_cols = [col for col in required_cols if col not in price_df.columns]
                
                if missing_cols:
                    # Try common alternative column names
                    col_mapping = {
                        'o': 'open', 'h': 'high', 'l': 'low', 'c': 'close', 'v': 'volume'
                    }
                    for alt_col, std_col in col_mapping.items():
                        if alt_col in price_df.columns and std_col in missing_cols:
                            price_df[std_col] = price_df[alt_col]
                            missing_cols.remove(std_col)
                
                if missing_cols:
                    logger.error(f"[{ticker}] Missing required columns after mapping: {missing_cols}")
                    return None

                # Take the most recent data up to lookback_bars
                final_data = price_df.tail(lookback_bars).copy()
                
                if len(final_data) < self.config['analysis_lookback_bars']:
                    logger.warning(f"[{ticker}] Only got {len(final_data)} bars, need {self.config['analysis_lookback_bars']}")
                    if len(final_data) < self.config['analysis_lookback_bars'] * 0.7:  # Less than 70% of required data
                        logger.error(f"[{ticker}] Insufficient data for reliable void analysis")
                        return None

                logger.info(f"[{ticker}] Successfully fetched {len(final_data)} bars for void analysis")
                return final_data
            else:
                logger.error(f"[{ticker}] API returned empty data")
                return None

        except Exception as e:
            logger.error(f"[{ticker}] Failed to fetch price data from API: {e}")
            return None

    def _identify_liquidity_voids_and_hvns(self, ticker: str, price_data_df: pd.DataFrame) -> Tuple[List[Dict[str, Any]], List[float]]:
        """Identify liquidity voids (LVNs) and High Volume Nodes (HVNs)."""
        voids = []
        hvn_price_levels = []
        cfg_vp_bins = self.config['volume_profile_bins']
        cfg_void = self.config['void_detection']

        try:
            # Calculate Volume Profile
            price_min_range = price_data_df['low'].min()
            price_max_range = price_data_df['high'].max()
            
            if pd.isna(price_min_range) or pd.isna(price_max_range) or price_min_range == price_max_range:
                return voids, hvn_price_levels

            price_bins = np.linspace(price_min_range, price_max_range, cfg_vp_bins + 1)
            volume_at_price = np.zeros(cfg_vp_bins)
            bin_centers = (price_bins[:-1] + price_bins[1:]) / 2

            # Distribute volume across price bins
            for _, bar in price_data_df.iterrows():
                bar_low, bar_high, bar_vol = bar['low'], bar['high'], bar['volume']
                if bar_vol == 0:
                    continue

                low_idx = np.searchsorted(price_bins, bar_low, side='right') - 1
                high_idx = np.searchsorted(price_bins, bar_high, side='left')
                low_idx = np.clip(low_idx, 0, cfg_vp_bins - 1)
                high_idx = np.clip(high_idx, 0, cfg_vp_bins - 1)
                
                if low_idx <= high_idx:
                    relevant_indices = range(low_idx, high_idx + 1)
                    num_relevant = len(relevant_indices)
                    if num_relevant > 0:
                        vol_per_segment = bar_vol / num_relevant
                        for i_bin in relevant_indices:
                            volume_at_price[i_bin] += vol_per_segment

            if np.sum(volume_at_price) == 0:
                return voids, hvn_price_levels

            mean_vol_per_bin = np.mean(volume_at_price[volume_at_price > 0])

            # Identify HVNs (High Volume Nodes)
            hvn_cutoff = mean_vol_per_bin * 1.5
            for i, vol in enumerate(volume_at_price):
                if vol >= hvn_cutoff:
                    hvn_price_levels.append(bin_centers[i])
            hvn_price_levels = sorted(list(set(hvn_price_levels)))

            # Identify LVNs for Voids
            lvn_volume_max = mean_vol_per_bin * cfg_void['lvn_volume_threshold_vs_mean']
            current_void_start_idx = -1
            
            for i, vol_in_bin in enumerate(volume_at_price):
                if vol_in_bin <= lvn_volume_max:  # Low volume node
                    if current_void_start_idx == -1:
                        current_void_start_idx = i
                else:  # High volume node - void ends
                    if current_void_start_idx != -1:
                        void_end_idx = i - 1
                        void_start_price = bin_centers[current_void_start_idx]
                        void_end_price = bin_centers[void_end_idx]
                        
                        # Ensure proper ordering
                        if void_start_price > void_end_price:
                            void_start_price, void_end_price = void_end_price, void_start_price

                        void_size = void_end_price - void_start_price
                        min_void_size = (price_max_range - price_min_range) * cfg_void['min_void_size_pct_of_range']

                        if void_size >= min_void_size:
                            void_volumes = volume_at_price[current_void_start_idx:void_end_idx + 1]
                            voids.append({
                                'start_price': void_start_price,
                                'end_price': void_end_price,
                                'size': void_size,
                                'avg_volume_in_void': np.mean(void_volumes) if len(void_volumes) > 0 else 0,
                                'num_bins_in_void': (void_end_idx - current_void_start_idx + 1)
                            })
                        current_void_start_idx = -1

            # Handle void at end of range
            if current_void_start_idx != -1:
                void_end_idx = cfg_vp_bins - 1
                void_start_price = bin_centers[current_void_start_idx]
                void_end_price = bin_centers[void_end_idx]
                
                if void_start_price > void_end_price:
                    void_start_price, void_end_price = void_end_price, void_start_price
                
                void_size = void_end_price - void_start_price
                min_void_size = (price_max_range - price_min_range) * cfg_void['min_void_size_pct_of_range']
                
                if void_size >= min_void_size:
                    void_volumes = volume_at_price[current_void_start_idx:void_end_idx + 1]
                    voids.append({
                        'start_price': void_start_price,
                        'end_price': void_end_price,
                        'size': void_size,
                        'avg_volume_in_void': np.mean(void_volumes) if len(void_volumes) > 0 else 0,
                        'num_bins_in_void': (void_end_idx - current_void_start_idx + 1)
                    })

            logger.debug(f"[{ticker}] Identified {len(voids)} voids and {len(hvn_price_levels)} HVN levels")

        except Exception as e:
            logger.error(f"[{ticker}] Error identifying voids/HVNs: {e}", exc_info=True)

        return voids, hvn_price_levels

    def _filter_active_void_scenarios(self, current_price: float, 
                                    voids: List[Dict[str, Any]], 
                                    hvn_levels: List[float]) -> List[Dict[str, Any]]:
        """Filter voids that price is actively interacting with."""
        active_scenarios = []
        cfg_void = self.config['void_detection']
        max_dist_abs = current_price * cfg_void['max_void_distance_pct_from_price']

        for void in voids:
            void_start, void_end = void['start_price'], void['end_price']
            scenario = None

            # Price approaching void from below
            if current_price < void_start and (void_start - current_price) <= max_dist_abs:
                target_hvns_above = sorted([hvn for hvn in hvn_levels if hvn > void_end])
                if target_hvns_above:
                    scenario = {
                        'void_data': void,
                        'direction_bias': DirectionBias.BULLISH,
                        'entry_price': void_start,
                        'target_hvn': target_hvns_above[0],
                        'scenario_type': 'approaching_from_below'
                    }

            # Price approaching void from above
            elif current_price > void_end and (current_price - void_end) <= max_dist_abs:
                target_hvns_below = sorted([hvn for hvn in hvn_levels if hvn < void_start], reverse=True)
                if target_hvns_below:
                    scenario = {
                        'void_data': void,
                        'direction_bias': DirectionBias.BEARISH,
                        'entry_price': void_end,
                        'target_hvn': target_hvns_below[0],
                        'scenario_type': 'approaching_from_above'
                    }

            # Price inside the void
            elif void_start <= current_price <= void_end:
                dist_to_top = void_end - current_price
                dist_to_bottom = current_price - void_start
                
                target_hvns_above = sorted([hvn for hvn in hvn_levels if hvn > void_end])
                target_hvns_below = sorted([hvn for hvn in hvn_levels if hvn < void_start], reverse=True)

                # Choose direction based on proximity and available targets
                if dist_to_bottom < dist_to_top and target_hvns_above:
                    scenario = {
                        'void_data': void,
                        'direction_bias': DirectionBias.BULLISH,
                        'entry_price': current_price,
                        'target_hvn': target_hvns_above[0],
                        'scenario_type': 'inside_void_bullish'
                    }
                elif target_hvns_below:
                    scenario = {
                        'void_data': void,
                        'direction_bias': DirectionBias.BEARISH,
                        'entry_price': current_price,
                        'target_hvn': target_hvns_below[0],
                        'scenario_type': 'inside_void_bearish'
                    }

            if scenario:
                active_scenarios.append(scenario)

        logger.debug(f"Filtered to {len(active_scenarios)} active void scenarios")
        return active_scenarios

    def _calculate_flow_velocity(self, price_data_df: pd.DataFrame) -> Tuple[float, float]:
        """Calculate flow velocity and normalized value."""
        cfg_mom = self.config['momentum_confirmation']
        lookback = cfg_mom['velocity_lookback_bars']
        
        if len(price_data_df) < lookback:
            return 0.0, 0.0

        recent_data = price_data_df.tail(lookback).copy()
        
        # Ensure timestamp availability
        if 'timestamp' not in recent_data.columns:
            if isinstance(recent_data.index, pd.DatetimeIndex):
                recent_data['timestamp'] = recent_data.index.astype(np.int64) // 10**9
            else:
                logger.warning("Missing timestamp for velocity calculation")
                return 0.0, 0.0

        recent_data['flow_value'] = recent_data['close'] * recent_data['volume']
        
        # Calculate velocity using gradient
        flow_values = recent_data['flow_value'].values
        timestamps_sec = recent_data['timestamp'].values

        if len(flow_values) < 2:
            return 0.0, 0.0

        try:
            # Ensure numeric timestamps
            if not pd.api.types.is_numeric_dtype(timestamps_sec):
                if isinstance(timestamps_sec[0], (datetime, pd.Timestamp)):
                    timestamps_sec = np.array([ts.timestamp() for ts in timestamps_sec])
                else:
                    timestamps_sec = pd.to_datetime(timestamps_sec).astype(np.int64) // 10**9

            velocities = np.gradient(flow_values, timestamps_sec)
            raw_velocity = velocities[-1]
            
        except Exception as e:
            logger.error(f"Error in velocity calculation: {e}")
            # Fallback to simple difference
            if len(flow_values) >= 2 and (timestamps_sec[-1] - timestamps_sec[-2]) > 0:
                raw_velocity = (flow_values[-1] - flow_values[-2]) / (timestamps_sec[-1] - timestamps_sec[-2])
            else:
                raw_velocity = 0.0

        # Normalize velocity
        avg_abs_flow = np.mean(np.abs(flow_values))
        if avg_abs_flow > 0 and len(velocities) > 1:
            max_abs_velocity = np.percentile(np.abs(velocities), 95)
            if max_abs_velocity > 0:
                normalized_velocity = np.clip(raw_velocity / max_abs_velocity, -1.0, 1.0)
            else:
                normalized_velocity = 0.0
        else:
            normalized_velocity = 0.0

        return normalized_velocity, raw_velocity

    def _confirm_recent_momentum(self, price_data_df: pd.DataFrame, required_direction_sign: int) -> bool:
        """Confirm momentum based on recent price action and volume."""
        cfg_mom = self.config['momentum_confirmation']
        num_bars = cfg_mom['confirmation_bars']
        
        if len(price_data_df) < num_bars + 1:
            return False

        recent_bars = price_data_df.tail(num_bars)
        
        # Price confirmation
        price_change = recent_bars['close'].iloc[-1] - recent_bars['close'].iloc[0]
        price_start = recent_bars['close'].iloc[0]
        price_move_pct = (price_change / price_start) if price_start > 0 else 0

        price_agrees = np.sign(price_change) == required_direction_sign
        price_significant = abs(price_move_pct) >= cfg_mom['min_price_move_pct_confirm']

        # Volume confirmation
        lookback_for_avg = max(20, num_bars + 5)
        if len(price_data_df) < lookback_for_avg:
            return False

        avg_volume_baseline = price_data_df['volume'].iloc[-lookback_for_avg:-num_bars].mean()
        recent_avg_volume = recent_bars['volume'].mean()

        volume_supports = (recent_avg_volume >= avg_volume_baseline * cfg_mom['min_volume_increase_factor']) \
                         if avg_volume_baseline > 0 else True

        confirmed = price_agrees and price_significant and volume_supports
        
        if confirmed:
            logger.debug(f"Momentum confirmed: Price move {price_move_pct*100:.2f}%, Volume ratio {recent_avg_volume/(avg_volume_baseline if avg_volume_baseline > 0 else 1):.2f}")
        
        return confirmed

    def _create_void_factor(self, 
                           ticker: str,
                           current_price: float,
                           void_scenario: Dict[str, Any],
                           flow_velocity_norm: float,
                           timeframe: TimeFrame) -> Optional[FactorData]:
        """Create a FactorData object for a void scenario."""
        try:
            direction_bias = void_scenario['direction_bias']
            void_data = void_scenario['void_data']
            target_hvn = void_scenario['target_hvn']
            entry_price = void_scenario['entry_price']
            scenario_type = void_scenario['scenario_type']

            # Calculate strength score
            cfg_strength = self.config['factor_strength']
            base_strength = cfg_strength['base_strength']
            
            # Velocity contribution
            velocity_contribution = min(cfg_strength['velocity_multiplier'], 
                                      abs(flow_velocity_norm) * cfg_strength['velocity_multiplier'])
            
            # Void size contribution
            void_size_pct = (void_data['size'] / current_price) * 100
            void_size_contribution = min(cfg_strength['void_size_multiplier'],
                                       void_size_pct * 0.02 * cfg_strength['void_size_multiplier'])
            
            # Proximity bonus
            dist_to_entry_pct = abs(current_price - entry_price) / current_price if entry_price != current_price else 0
            proximity_bonus = cfg_strength['proximity_bonus'] if dist_to_entry_pct < 0.001 else 0
            
            strength_score = min(1.0, max(0.0, base_strength + velocity_contribution + void_size_contribution + proximity_bonus))
            strength_score = normalize_strength_score(strength_score, 0.0, 1.0)

            # Check minimum confidence
            if strength_score < self.config['min_confidence']:
                logger.debug(f"[{ticker}] Void factor strength {strength_score:.2f} below minimum")
                return None

            # Create factor name
            direction_str = "Bullish" if direction_bias == DirectionBias.BULLISH else "Bearish"
            factor_name = f"LVS_{direction_str}Void"

            # Calculate target and stop prices for context
            if direction_bias == DirectionBias.BULLISH:
                target_price = target_hvn
                stop_loss_price = void_data['start_price'] * 0.997  # Small buffer
            else:
                target_price = target_hvn
                stop_loss_price = void_data['end_price'] * 1.003  # Small buffer

            # Create reason
            reason_short = (f"{direction_str} void at {void_data['start_price']:.2f}-{void_data['end_price']:.2f}, "
                          f"target HVN {target_hvn:.2f}, velocity {flow_velocity_norm:.2f}")

            # Factor details
            details = {
                'void_start_price': void_data['start_price'],
                'void_end_price': void_data['end_price'],
                'void_size': void_data['size'],
                'void_size_pct': void_size_pct,
                'target_hvn_price': target_hvn,
                'flow_velocity_normalized': flow_velocity_norm,
                'scenario_type': scenario_type,
                'avg_volume_in_void': void_data['avg_volume_in_void'],
                'entry_trigger_price': entry_price,
                'proximity_to_entry_pct': dist_to_entry_pct,
                'velocity_contribution': velocity_contribution,
                'void_size_contribution': void_size_contribution,
                'proximity_bonus': proximity_bonus
            }

            factor = FactorData(
                factor_name=factor_name,
                ticker=ticker,
                timestamp=datetime.now(),
                timeframe=timeframe,
                direction_bias=direction_bias,
                strength_score=strength_score,
                key_level_price=entry_price,
                entry_price=entry_price,
                stop_loss_price=stop_loss_price,
                target_price=target_price,
                details=details,
                reason_short=reason_short,
                analyzer_name="liquidity_void_analyzer",
                analyzer_version="1.0",
                data_quality_score=0.9  # High quality from volume profile analysis
            )

            return factor

        except Exception as e:
            logger.error(f"[{ticker}] Error creating void factor: {e}", exc_info=True)
            return None


if __name__ == "__main__":
    # Test the analyzer
    logging.basicConfig(level=logging.INFO)
    
    analyzer = LiquidityVoidAnalyzer()
    logger.info(f"LiquidityVoidAnalyzer test: {analyzer.__class__.__name__} initialized")
    
    # Test with dummy data
    test_ticker = "AAPL"
    test_price = 175.50
    
    # Create sample data
    dates = pd.date_range(start='2024-01-01', periods=100, freq='15T')
    test_data = pd.DataFrame({
        'open': np.random.randn(100).cumsum() + 175,
        'high': np.random.randn(100).cumsum() + 176,
        'low': np.random.randn(100).cumsum() + 174,
        'close': np.random.randn(100).cumsum() + 175,
        'volume': np.random.randint(1000, 10000, 100)
    }, index=dates)
    
    mtf_data = {TimeFrame.MIN_15: test_data}
    
    factors = analyzer.analyze_factors(test_ticker, test_price, mtf_data)
    logger.info(f"Generated {len(factors)} factors for {test_ticker}")
    
    for factor in factors:
        logger.info(f"Factor: {factor.factor_name}, Strength: {factor.strength_score:.2f}, Direction: {factor.direction_bias.value}")
