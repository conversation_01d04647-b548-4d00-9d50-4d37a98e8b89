#!/usr/bin/env python3
"""
Greeks Result Data Structures
Complete data structures for Greeks analysis results with validation
"""

import numpy as np
import pandas as pd
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Any, Optional


@dataclass
class GreeksResult:
    """
    Complete Greeks analysis result with mathematical validation
    
    Mathematical Foundation:
    - All Greeks calculated using analytical Black-Scholes formulas
    - Bounds validation enforced for mathematical consistency
    - ROC derivatives calculated with statistical significance
    - Quality metrics for precision tracking
    """
    
    # Identification
    timestamp: datetime
    symbol: str
    option_type: str  # 'call' or 'put'
    
    # Option parameters
    spot_price: float
    strike_price: float
    time_to_expiry: float
    risk_free_rate: float
    volatility: float
    dividend_yield: float
    
    # Core Greeks (analytical Black-Scholes)
    delta: float      # Price sensitivity [-1, 1]
    gamma: float      # Delta sensitivity [0, ]
    theta: float      # Time decay (negative for long positions)
    vega: float       # Volatility sensitivity [0, ]
    rho: float        # Interest rate sensitivity
    
    # ROC Derivatives
    delta_roc: float  # Delta rate of change
    gamma_roc: float  # Gamma rate of change
    theta_roc: float  # Theta rate of change
    vega_roc: float   # Vega rate of change
    rho_roc: float    # Rho rate of change
    
    # Validation metrics
    calculation_quality: float           # Overall calculation quality [0, 1]
    bounds_validation: Dict[str, bool]   # Individual bounds validation results
    statistical_significance: Dict[str, float]  # Statistical significance of ROC values
    
    # Anomaly detection
    greek_anomalies: List[Dict[str, Any]]  # Detected statistical anomalies
    anomaly_count: int                     # Total number of anomalies
    anomaly_severity: str                  # 'low', 'moderate', 'high', 'critical'
    
    # Cross-validation
    put_call_parity_check: bool           # Put-call parity validation
    greeks_consistency_score: float      # Internal Greeks relationship consistency
    
    # Metadata
    quality_score: float                  # Overall quality score [0, 1]
    calculation_metadata: Dict[str, Any] # Additional calculation details
    error_flags: List[str]               # Any calculation warnings or errors
    
    def __post_init__(self):
        """Post-initialization validation and quality scoring"""
        self._validate_mathematical_bounds()
        self._calculate_quality_score()
        self._validate_greeks_consistency()
    
    def _validate_mathematical_bounds(self):
        """Validate all Greeks meet mathematical bounds"""
        from .constants import DELTA_BOUNDS, GAMMA_MIN_BOUND, VEGA_MIN_BOUND
        
        self.bounds_validation = {
            'delta_bounds': DELTA_BOUNDS[0] <= self.delta <= DELTA_BOUNDS[1],
            'gamma_positive': self.gamma >= GAMMA_MIN_BOUND,
            'vega_positive': self.vega >= VEGA_MIN_BOUND,
            'finite_values': all(np.isfinite([self.delta, self.gamma, self.theta, self.vega, self.rho]))
        }
        
        # Add error flags for bound violations
        if not self.bounds_validation['delta_bounds']:
            self.error_flags.append(f"Delta out of bounds: {self.delta}")
        if not self.bounds_validation['gamma_positive']:
            self.error_flags.append(f"Gamma negative: {self.gamma}")
        if not self.bounds_validation['vega_positive']:
            self.error_flags.append(f"Vega negative: {self.vega}")
        if not self.bounds_validation['finite_values']:
            self.error_flags.append("Non-finite Greek values detected")
    
    def _calculate_quality_score(self):
        """Calculate overall quality score based on validation results"""
        # Base quality from bounds validation
        bounds_score = sum(self.bounds_validation.values()) / len(self.bounds_validation)
        
        # Quality from calculation precision
        precision_score = 1.0 if len(self.error_flags) == 0 else 0.8
        
        # Quality from statistical significance
        if self.statistical_significance:
            significance_score = sum(self.statistical_significance.values()) / len(self.statistical_significance)
        else:
            significance_score = 0.8  # Default if no ROC data
        
        # Combined quality score
        self.quality_score = (bounds_score * 0.4 + precision_score * 0.4 + significance_score * 0.2)
        
        # Quality classification
        self.calculation_quality = self.quality_score
    
    def _validate_greeks_consistency(self):
        """Validate internal Greeks relationships"""
        try:
            # For European options, some relationships should hold
            # This is a simplified consistency check
            
            consistency_checks = []
            
            # Delta should be related to option moneyness
            moneyness = self.spot_price / self.strike_price
            if self.option_type == 'call':
                delta_expected_sign = 1 if moneyness >= 1 else 0.5
                consistency_checks.append(abs(self.delta) > 0 and self.delta <= 1)
            else:  # put
                consistency_checks.append(abs(self.delta) > 0 and self.delta >= -1)
            
            # Gamma should be highest around ATM
            atm_factor = abs(1 - moneyness)
            consistency_checks.append(self.gamma >= 0)
            
            # Vega should be positive
            consistency_checks.append(self.vega >= 0)
            
            # Calculate consistency score
            self.greeks_consistency_score = sum(consistency_checks) / len(consistency_checks)
            
        except Exception as e:
            self.greeks_consistency_score = 0.5  # Neutral score on error
            self.error_flags.append(f"Consistency validation error: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'symbol': self.symbol,
            'option_type': self.option_type,
            'spot_price': self.spot_price,
            'strike_price': self.strike_price,
            'time_to_expiry': self.time_to_expiry,
            'risk_free_rate': self.risk_free_rate,
            'volatility': self.volatility,
            'dividend_yield': self.dividend_yield,
            'delta': self.delta,
            'gamma': self.gamma,
            'theta': self.theta,
            'vega': self.vega,
            'rho': self.rho,
            'delta_roc': self.delta_roc,
            'gamma_roc': self.gamma_roc,
            'theta_roc': self.theta_roc,
            'vega_roc': self.vega_roc,
            'rho_roc': self.rho_roc,
            'calculation_quality': self.calculation_quality,
            'bounds_validation': self.bounds_validation,
            'statistical_significance': self.statistical_significance,
            'greek_anomalies': self.greek_anomalies,
            'anomaly_count': self.anomaly_count,
            'anomaly_severity': self.anomaly_severity,
            'put_call_parity_check': self.put_call_parity_check,
            'greeks_consistency_score': self.greeks_consistency_score,
            'quality_score': self.quality_score,
            'calculation_metadata': self.calculation_metadata,
            'error_flags': self.error_flags
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'GreeksResult':
        """Create GreeksResult from dictionary"""
        # Convert timestamp back from string
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        # Initialize error_flags if not present
        if 'error_flags' not in data:
            data['error_flags'] = []
        
        return cls(**data)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get concise summary of Greeks analysis"""
        return {
            'symbol': self.symbol,
            'option_type': self.option_type,
            'greeks': {
                'delta': round(self.delta, 4),
                'gamma': round(self.gamma, 4),
                'theta': round(self.theta, 4),
                'vega': round(self.vega, 4)
            },
            'quality_score': round(self.quality_score, 3),
            'anomaly_count': self.anomaly_count,
            'anomaly_severity': self.anomaly_severity,
            'has_errors': len(self.error_flags) > 0
        }


@dataclass
class GreeksPortfolioResult:
    """
    Portfolio-level Greeks analysis result
    Aggregates individual option Greeks for portfolio risk management
    """
    
    timestamp: datetime
    portfolio_id: str
    individual_results: List[GreeksResult]
    
    # Aggregated Greeks
    portfolio_delta: float
    portfolio_gamma: float
    portfolio_theta: float
    portfolio_vega: float
    portfolio_rho: float
    
    # Risk metrics
    delta_exposure: float      # Total delta exposure in dollars
    gamma_risk: float         # Gamma risk assessment
    theta_decay: float        # Daily theta decay
    vega_exposure: float      # Volatility exposure
    
    # Portfolio quality
    portfolio_quality_score: float
    position_count: int
    error_count: int
    
    def calculate_portfolio_greeks(self, position_sizes: Dict[str, float]):
        """Calculate position-weighted portfolio Greeks"""
        total_delta = 0.0
        total_gamma = 0.0
        total_theta = 0.0
        total_vega = 0.0
        total_rho = 0.0
        
        for result in self.individual_results:
            position_key = f"{result.symbol}_{result.option_type}_{result.strike_price}"
            position_size = position_sizes.get(position_key, 1.0)
            
            total_delta += result.delta * position_size
            total_gamma += result.gamma * position_size
            total_theta += result.theta * position_size
            total_vega += result.vega * position_size
            total_rho += result.rho * position_size
        
        self.portfolio_delta = total_delta
        self.portfolio_gamma = total_gamma
        self.portfolio_theta = total_theta
        self.portfolio_vega = total_vega
        self.portfolio_rho = total_rho
        
        # Calculate risk metrics
        self.delta_exposure = abs(total_delta) * 100  # Assuming $100 per delta
        self.gamma_risk = total_gamma * 0.01  # 1% move impact
        self.theta_decay = total_theta  # Daily decay
        self.vega_exposure = total_vega * 0.01  # 1% vol change impact
