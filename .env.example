# CORE Environment Variables - Template
# Copy this file to .env and fill in your values
# Never commit .env to git - only commit this .env.example

# =============================================================================
# ACCOUNT CONFIGURATION
# =============================================================================

# Account equity for position sizing and risk calculations
# vm_iso: 25000, win_quick: 10000
ACCOUNT_EQUITY=25000

# =============================================================================
# SCHWAB API CONFIGURATION
# =============================================================================

# Schwab API credentials for live trading
# Get from: https://developer.schwab.com/
SCHWAB_API_KEY=your_schwab_api_key_here
SCHWAB_API_SECRET=your_schwab_api_secret_here
SCHWAB_REFRESH_TOKEN=your_schwab_refresh_token_here

# =============================================================================
# DATA SOURCE CONFIGURATION  
# =============================================================================

# Primary data source for market data
# Options: mcp-http, polygon, mcp
DATA_SOURCE=mcp-http

# Polygon API key (for polygon data source)
# Get from: https://polygon.io/dashboard
POLYGON_API_KEY=your_polygon_api_key_here

# =============================================================================
# TRADING CONFIGURATION
# =============================================================================

# Tradier API configuration for paper trading
# Get sandbox token from: https://developer.tradier.com/
TRADIER_TOKEN=Bearer your_tradier_sandbox_token_here
TRADIER_URL=https://sandbox.tradier.com/v1

# =============================================================================
# AGENT ZERO CONFIGURATION
# =============================================================================

# Agent Zero operation mode
# Options: off, shadow, active
AGENT_ZERO_MODE=off

# =============================================================================
# DIRECTORY CONFIGURATION
# =============================================================================

# Output directories (profile-specific)
# VM paths
FILLS_DIR_VM=/home/<USER>/fills
LOGS_DIR_VM=/home/<USER>/logs
TRAINING_LOGS_DIR_VM=/home/<USER>/training_logs

# Windows paths  
FILLS_DIR_WIN=D:\script-work\CORE\fills
LOGS_DIR_WIN=D:\script-work\CORE\logs

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Health check ping URL for monitoring
# Get from: https://healthchecks.io/
HEALTHCHECK_PING_URL=https://hc-ping.com/your-uuid-here

# =============================================================================
# MCP CONFIGURATION
# =============================================================================

# MCP server configuration
MCP_HOST=https://your.mcp.server
MCP_TOKEN=your_mcp_token_here

# =============================================================================
# PROFILE-SPECIFIC EXAMPLES
# =============================================================================

# VM Production Profile (vm_iso):
# ACCOUNT_EQUITY=25000
# DATA_SOURCE=mcp-http
# AGENT_ZERO_MODE=active
# TRADIER_TOKEN=Bearer your_sandbox_token
# FILLS_DIR=/home/<USER>/fills
# LOGS_DIR=/home/<USER>/logs
# TRAINING_LOGS_DIR=/home/<USER>/training_logs

# Windows Development Profile (win_quick):
# ACCOUNT_EQUITY=10000  
# DATA_SOURCE=polygon
# AGENT_ZERO_MODE=off
# TRADIER_TOKEN=         # Leave empty to disable auto broker
# FILLS_DIR=D:\script-work\CORE\fills
# LOGS_DIR=D:\script-work\CORE\logs
# TRAINING_LOGS_DIR=     # Leave empty when Agent Zero is off
