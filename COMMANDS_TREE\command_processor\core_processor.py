type, parameters)
            )
            
            logger.info(f"Command parsed: {command_id} - {command_type}")
            return command
            
        except Exception as e:
            logger.error(f"Command parsing failed: {e}")
            return None
    
    def execute_command(self, command: Command) -> CommandResult:
        """
        Execute command with full validation and security checks
        """
        start_time = datetime.now()
        output_log = []
        warnings = []
        
        try:
            # Check concurrent command limit
            if len(self.active_commands) >= self.max_concurrent_commands:
                return CommandResult(
                    command_id=command.command_id,
                    status=CommandStatus.FAILED,
                    result_data=None,
                    execution_time=0.0,
                    error_message="Maximum concurrent commands exceeded",
                    warnings=[],
                    output_log=[]
                )
            
            # Add to active commands
            self.active_commands[command.command_id] = command
            
            # Step 1: Security validation
            if self.security_enabled:
                security_result = self._validate_security(command)
                if not security_result['approved']:
                    return self._create_failed_result(command, security_result['reason'], start_time)
                warnings.extend(security_result.get('warnings', []))
            
            # Step 2: Command validation
            if command.validation_required:
                validation_result = self._validate_command(command)
                if not validation_result['valid']:
                    return self._create_failed_result(command, validation_result['reason'], start_time)
                warnings.extend(validation_result.get('warnings', []))
            
            # Step 3: Execute command
            output_log.append(f"Executing {command.command_type} command: {command.command_id}")
            
            execution_result = self._execute_command_by_type(command)
            
            # Step 4: Create result
            execution_time = (datetime.now() - start_time).total_seconds()
            
            result = CommandResult(
                command_id=command.command_id,
                status=CommandStatus.COMPLETED if execution_result['success'] else CommandStatus.FAILED,
                result_data=execution_result['data'],
                execution_time=execution_time,
                error_message=execution_result.get('error'),
                warnings=warnings,
                output_log=output_log + execution_result.get('log', [])
            )
            
            # Update tracking
            self.commands_processed += 1
            self.total_execution_time += execution_time
            if not execution_result['success']:
                self.error_count += 1
            
            # Remove from active commands
            del self.active_commands[command.command_id]
            
            # Add to history
            self.command_history.append(result)
            
            return result
            
        except Exception as e:
            logger.error(f"Command execution failed: {e}")
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Cleanup
            if command.command_id in self.active_commands:
                del self.active_commands[command.command_id]
            
            return CommandResult(
                command_id=command.command_id,
                status=CommandStatus.FAILED,
                result_data=None,
                execution_time=execution_time,
                error_message=str(e),
                warnings=warnings,
                output_log=output_log
            )
    
    def _classify_command(self, command_text: str) -> tuple:
        """Classify command and extract parameters"""
        
        for cmd_type, config in self.command_patterns.items():
            pattern = config['pattern']
            match = re.match(pattern, command_text)
            
            if match:
                groups = match.groups()
                
                # Extract parameters based on command type
                if cmd_type == 'trading':
                    parameters = {
                        'action': groups[0],
                        'symbol': groups[1].upper(),
                        'details': groups[2] if len(groups) > 2 else ''
                    }
                elif cmd_type == 'system':
                    parameters = {
                        'action': groups[0],
                        'component': groups[1],
                        'options': groups[2] if len(groups) > 2 else ''
                    }
                elif cmd_type in ['data', 'analysis']:
                    parameters = {
                        'action': groups[0],
                        'target': groups[1],
                        'parameters': groups[2] if len(groups) > 2 else ''
                    }
                else:
                    parameters = {'raw_groups': groups}
                
                return cmd_type, parameters
        
        return None, {}
    
    def _determine_priority(self, command_type: str, parameters: Dict) -> CommandPriority:
        """Determine command priority"""
        
        # Emergency keywords
        emergency_keywords = ['emergency', 'urgent', 'stop', 'halt', 'cancel']
        
        if command_type == 'trading':
            action = parameters.get('action', '')
            if action in ['sell', 'close'] or any(kw in parameters.get('details', '') for kw in emergency_keywords):
                return CommandPriority.HIGH
            return CommandPriority.NORMAL
        
        elif command_type == 'system':
            action = parameters.get('action', '')
            if action in ['stop', 'restart'] or any(kw in parameters.get('options', '') for kw in emergency_keywords):
                return CommandPriority.HIGH
            return CommandPriority.NORMAL
        
        else:
            return CommandPriority.LOW
    
    def _estimate_duration(self, command_type: str, parameters: Dict) -> float:
        """Estimate command execution duration in seconds"""
        
        duration_estimates = {
            'trading': 2.0,
            'system': 5.0,
            'data': 1.0,
            'analysis': 3.0
        }
        
        return duration_estimates.get(command_type, 1.0)
    
    def _validate_security(self, command: Command) -> Dict[str, Any]:
        """Validate command security"""
        
        # Basic security checks
        security_result = {
            'approved': True,
            'reason': '',
            'warnings': []
        }
        
        # Check security level requirements
        if command.security_level > 2:
            # High security commands need additional validation
            if command.command_type == 'trading':
                # Trading commands need market hours check
                current_hour = datetime.now().hour
                if not (9 <= current_hour <= 16):  # Basic market hours
                    security_result['warnings'].append("Trading outside market hours")
        
        # Check for dangerous patterns
        dangerous_patterns = ['delete', 'remove', 'clear', 'wipe']
        if any(pattern in command.command_text for pattern in dangerous_patterns):
            security_result['warnings'].append("Potentially destructive command detected")
        
        return security_result
    
    def _validate_command(self, command: Command) -> Dict[str, Any]:
        """Validate command parameters and feasibility"""
        
        validation_result = {
            'valid': True,
            'reason': '',
            'warnings': []
        }
        
        # Command-specific validation
        if command.command_type == 'trading':
            symbol = command.parameters.get('symbol', '')
            if not symbol or len(symbol) < 1:
                validation_result['valid'] = False
                validation_result['reason'] = "Invalid or missing symbol"
            
            action = command.parameters.get('action', '')
            if action not in ['buy', 'sell', 'close']:
                validation_result['valid'] = False
                validation_result['reason'] = f"Invalid trading action: {action}"
        
        elif command.command_type == 'system':
            component = command.parameters.get('component', '')
            if not component:
                validation_result['valid'] = False
                validation_result['reason'] = "Missing system component"
        
        return validation_result
    
    def _execute_command_by_type(self, command: Command) -> Dict[str, Any]:
        """Execute command based on type"""
        
        try:
            if command.command_type == 'trading':
                return self._execute_trading_command(command)
            elif command.command_type == 'system':
                return self._execute_system_command(command)
            elif command.command_type == 'data':
                return self._execute_data_command(command)
            elif command.command_type == 'analysis':
                return self._execute_analysis_command(command)
            else:
                return {
                    'success': False,
                    'error': f"Unknown command type: {command.command_type}",
                    'data': None,
                    'log': []
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'data': None,
                'log': [f"Execution error: {str(e)}"]
            }
    
    def _execute_trading_command(self, command: Command) -> Dict[str, Any]:
        """Execute trading command"""
        
        action = command.parameters['action']
        symbol = command.parameters['symbol']
        details = command.parameters.get('details', '')
        
        # This would integrate with the execution tree
        execution_log = [
            f"Trading command: {action} {symbol}",
            f"Details: {details}",
            "Routing to execution engine...",
            "Command completed successfully"
        ]
        
        return {
            'success': True,
            'data': {
                'action': action,
                'symbol': symbol,
                'status': 'executed',
                'timestamp': datetime.now().isoformat()
            },
            'log': execution_log
        }
    
    def _execute_system_command(self, command: Command) -> Dict[str, Any]:
        """Execute system command"""
        
        action = command.parameters['action']
        component = command.parameters['component']
        
        execution_log = [
            f"System command: {action} {component}",
            f"Component status check...",
            f"Action '{action}' completed for {component}"
        ]
        
        return {
            'success': True,
            'data': {
                'action': action,
                'component': component,
                'status': 'completed',
                'timestamp': datetime.now().isoformat()
            },
            'log': execution_log
        }
    
    def _execute_data_command(self, command: Command) -> Dict[str, Any]:
        """Execute data command"""
        
        action = command.parameters['action']
        target = command.parameters['target']
        
        execution_log = [
            f"Data command: {action} {target}",
            "Accessing data sources...",
            "Data operation completed"
        ]
        
        return {
            'success': True,
            'data': {
                'action': action,
                'target': target,
                'result': f"Data {action} completed for {target}",
                'timestamp': datetime.now().isoformat()
            },
            'log': execution_log
        }
    
    def _execute_analysis_command(self, command: Command) -> Dict[str, Any]:
        """Execute analysis command"""
        
        action = command.parameters['action']
        target = command.parameters['target']
        
        execution_log = [
            f"Analysis command: {action} {target}",
            "Performing analysis...",
            "Analysis completed"
        ]
        
        return {
            'success': True,
            'data': {
                'action': action,
                'target': target,
                'result': f"Analysis {action} completed for {target}",
                'timestamp': datetime.now().isoformat()
            },
            'log': execution_log
        }
    
    def _generate_command_id(self) -> str:
        """Generate unique command ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        return f"CMD_{timestamp}_{self.commands_processed:06d}"
    
    def _create_failed_result(self, command: Command, error_message: str, start_time: datetime) -> CommandResult:
        """Create failed command result"""
        execution_time = (datetime.now() - start_time).total_seconds()
        
        if command.command_id in self.active_commands:
            del self.active_commands[command.command_id]
        
        return CommandResult(
            command_id=command.command_id,
            status=CommandStatus.FAILED,
            result_data=None,
            execution_time=execution_time,
            error_message=error_message,
            warnings=[],
            output_log=[f"Command failed: {error_message}"]
        )
    
    def get_command_status(self, command_id: str) -> Optional[CommandStatus]:
        """Get status of specific command"""
        
        # Check active commands
        if command_id in self.active_commands:
            return CommandStatus.EXECUTING
        
        # Check history
        for result in reversed(self.command_history):
            if result.command_id == command_id:
                return result.status
        
        return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get command processor performance statistics"""
        
        avg_execution_time = (
            self.total_execution_time / self.commands_processed 
            if self.commands_processed > 0 else 0.0
        )
        
        error_rate = (
            self.error_count / self.commands_processed 
            if self.commands_processed > 0 else 0.0
        )
        
        return {
            'total_commands_processed': self.commands_processed,
            'total_execution_time': self.total_execution_time,
            'average_execution_time': avg_execution_time,
            'error_count': self.error_count,
            'error_rate': error_rate,
            'active_commands': len(self.active_commands),
            'commands_per_second': (1.0 / avg_execution_time) if avg_execution_time > 0 else 0.0
        }

if __name__ == "__main__":
    # Test command processor
    processor = CommandProcessor()
    
    # Test commands
    test_commands = [
        "buy AAPL 100 shares",
        "get market data TSLA",
        "analyze portfolio performance",
        "status monitoring system"
    ]
    
    for cmd_text in test_commands:
        print(f"\nTesting: {cmd_text}")
        command = processor.parse_command(cmd_text)
        if command:
            result = processor.execute_command(command)
            print(f"Result: {result.status.value}")
            print(f"Execution time: {result.execution_time:.3f}s")
        else:
            print("Failed to parse command")
    
    stats = processor.get_performance_stats()
    print(f"\nPerformance: {stats['total_commands_processed']} commands, {stats['average_execution_time']:.3f}s avg")
