"""
Enhanced Gamma Exposure (GEX) Analysis Module - ORCHESTRATOR COMPATIBLE

CRITICAL: This version is fully API-dependent and orchestrator compatible.
- Receives api_gateway_instance in __init__
- Implements analyze_factors() method for orchestrator
- Fetches real-time data automatically
- Returns standardized FactorData objects
- NO manual DataFrame preprocessing required

INTEGRATION PATTERN:
1. Orchestrator initializes with api_gateway_instance
2. <PERSON><PERSON> calls analyze_factors() with data package
3. <PERSON><PERSON><PERSON> fetches live options data if needed
4. Returns FactorData objects for formatter
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.ticker as mticker
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union, Any
import os
import json
from scipy.stats import norm
import time
import logging
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed

# Import base analyzer and factor specifications
try:
    from factor_specification import FactorData, TimeFrame, DirectionBias
    from analyzers.base_analyzer import BaseAnalyzer
except ImportError:
    try:
        from .factor_specification import FactorData, TimeFrame, DirectionBias
        from base_analyzer import BaseAnalyzer
    except ImportError:
        # Create basic BaseAnalyzer if not available
        class BaseAnalyzer:
            def __init__(self, config=None, system_config=None, api_gateway_instance=None):
                self.config = config or {}
                self.system_config = system_config or {}
                self.api_gateway = api_gateway_instance
                self.name = self.__class__.__name__

        # Import factor specifications directly
        from factor_specification import FactorData, TimeFrame, DirectionBias

logger = logging.getLogger("enhanced_gex_analyzer")

# Import BSM model for accurate Greeks calculations
try:
    from bsm_model import BlackScholesModel
    BSM_MODEL_AVAILABLE = True
    logger.info("BSM model imported successfully for Enhanced GEX Analyzer")
except ImportError:
    BSM_MODEL_AVAILABLE = False
    logger.warning("BSM model not available - using fallback Greeks calculations")


class EnhancedGammaExposureAnalyzer(BaseAnalyzer):
    """
    Enhanced Gamma Exposure Analyzer - ORCHESTRATOR COMPATIBLE
    
    This analyzer integrates with the orchestrator system and is fully API-dependent:
    - Receives api_gateway_instance from orchestrator
    - Implements analyze_factors() method
    - Fetches real-time options data automatically
    - Returns standardized FactorData objects
    - No manual DataFrame preprocessing required
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None,
                 system_config: Optional[Dict[str, Any]] = None,
                 api_gateway_instance=None):
        """
        Initialize Enhanced GEX Analyzer for orchestrator compatibility.
        
        Args:
            config: Analyzer-specific configuration
            system_config: System-wide configuration  
            api_gateway_instance: UnifiedAPIGateway instance (REQUIRED)
        """
        # Initialize base analyzer with only the config parameter
        super().__init__(config, system_config, api_gateway_instance)
        
        # Store additional parameters
        self.system_config = system_config or {}
        self.api_gateway = api_gateway_instance
        
        # Validate API gateway - CRITICAL for orchestrator compatibility
        if not self.api_gateway:
            logger.warning("API gateway instance not provided - Enhanced GEX Analyzer will have limited functionality")
            # Don't raise error, just warn
        
        # Update configuration with defaults
        self.config.update(self._get_default_config())
        
        # Analyzer configuration with sensible defaults
        self.risk_free_rate = self.config.get('risk_free_rate', 0.05)
        self.price_range = self.config.get('price_range', 0.15)  # 15% range
        self.num_steps = self.config.get('num_steps', 100)
        self.filter_threshold = self.config.get('filter_threshold', 0.1)
        self.use_parallel = self.config.get('use_parallel', False)
        self.num_processes = self.config.get('num_processes') or max(1, mp.cpu_count() - 1)
        self.min_zero_cross_slope = self.config.get('min_zero_cross_slope', 0.01)
        self.dealer_net_position = self.config.get('dealer_net_position', 0.0)
        self.max_zero_crosses = self.config.get('max_zero_crosses', 5)
        self.max_accumulation_zones = self.config.get('max_accumulation_zones', 3)
        
        logger.info("Enhanced GEX Analyzer initialized with API gateway integration")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for Enhanced GEX Analyzer."""
        return {
            'enabled': True,
            'risk_free_rate': 0.05,
            'price_range': 0.15,  # 15% price range
            'num_steps': 100,
            'filter_threshold': 0.1,
            'use_parallel': False,
            'min_zero_cross_slope': 0.01,
            'dealer_net_position': 0.0,
            'max_zero_crosses': 5,
            'max_accumulation_zones': 3
        }
    
    def analyze(self, ticker: str, mtf_data: Dict[str, pd.DataFrame], 
                current_price: float, **kwargs) -> List[FactorData]:
        """
        Required abstract method from BaseAnalyzer.
        Delegates to analyze_factors for orchestrator compatibility.
        
        Args:
            ticker: Symbol being analyzed
            mtf_data: Multi-timeframe price data
            current_price: Current market price
            **kwargs: Additional data (options, etc.)
            
        Returns:
            List of FactorData objects
        """
        # Create data package for analyze_factors
        data_package = {
            'ticker': ticker,
            'current_price': current_price,
            'mtf_market_data': mtf_data
        }
        
        # Add any additional data from kwargs
        data_package.update(kwargs)
        
        return self.analyze_factors(data_package=data_package)
    
    def analyze_factors(self, data_package=None, **kwargs) -> List[FactorData]:
        """
        ORCHESTRATOR COMPATIBLE: Main factor analysis method.
        
        This method is called by the orchestrator with standardized data packages.
        It fetches real-time options data and performs GEX analysis.
        
        Args:
            data_package: Standardized data package from orchestrator
            **kwargs: Additional parameters for backward compatibility
            
        Returns:
            List of FactorData objects representing GEX-based trading factors
        """
        logger.info("Starting Enhanced GEX factor analysis with real-time API data")
        
        try:
            # Extract parameters from orchestrator data package
            if data_package:
                ticker = data_package.get('ticker', 'UNKNOWN')
                current_price = data_package.get('current_price')
                # Options data might be provided by orchestrator
                provided_options_data = data_package.get('options_data')
            else:
                # Fallback to kwargs for backward compatibility
                ticker = kwargs.get('ticker', 'UNKNOWN')
                current_price = kwargs.get('current_price')
                provided_options_data = kwargs.get('options_data')
            
            if not ticker or ticker == 'UNKNOWN':
                logger.error("No ticker provided for GEX analysis")
                return []
            
            # Fetch current price from API if not provided
            if not current_price:
                try:
                    current_price = self.api_gateway.get_current_price(ticker)
                    logger.info(f"Fetched current price for {ticker}: ${current_price:.2f}")
                except Exception as e:
                    logger.error(f"Failed to fetch current price for {ticker}: {e}")
                    return []
            
            # Fetch or use options chain data
            options_data = None
            if provided_options_data is not None and not provided_options_data.empty:
                logger.info(f"Using provided options data for {ticker}")
                options_data = provided_options_data.copy()
            else:
                try:
                    logger.info(f"Fetching real-time options chain for {ticker}")
                    options_data = self.api_gateway.get_options_chain(ticker)
                    if options_data is None or options_data.empty:
                        logger.warning(f"No options data available for {ticker}")
                        return []
                    logger.info(f"Fetched {len(options_data)} options contracts for {ticker}")
                except Exception as e:
                    logger.error(f"Failed to fetch options data for {ticker}: {e}")
                    return []
            
            # Perform GEX analysis
            gex_results = self._analyze_gex(ticker, current_price, options_data)
            
            # Convert GEX results to FactorData objects
            factors = self._convert_to_factors(ticker, current_price, gex_results)
            
            logger.info(f"Generated {len(factors)} GEX factors for {ticker}")
            return factors
            
        except Exception as e:
            logger.error(f"Error in Enhanced GEX factor analysis for {ticker}: {e}", exc_info=True)
            return []
    
    def _analyze_gex(self, ticker: str, current_price: float, 
                     options_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform comprehensive GEX analysis on real options data.
        
        Args:
            ticker: Stock ticker symbol
            current_price: Current stock price
            options_data: Real options chain data from API
            
        Returns:
            Dictionary containing GEX analysis results
        """
        logger.info(f"Performing GEX analysis for {ticker} at ${current_price:.2f}")
        
        try:
            # Preprocess options data
            processed_options = self._preprocess_options_data(options_data, current_price)
            
            if processed_options.empty:
                logger.warning(f"No valid options data after preprocessing for {ticker}")
                return {'error': 'No valid options data', 'ticker': ticker}
            
            # Determine price range for analysis
            if isinstance(self.price_range, tuple):
                min_price, max_price = self.price_range
            else:
                pct = float(self.price_range)
                min_price = current_price * (1 - pct)
                max_price = current_price * (1 + pct)
            
            # Generate price array
            prices = np.linspace(min_price, max_price, self.num_steps)
            
            # Filter significant options
            significant_options = self._filter_significant_options(processed_options)
            
            if significant_options.empty:
                logger.warning(f"No significant options found for {ticker}")
                return {'error': 'No significant options', 'ticker': ticker}
            
            # Calculate GEX at each price point
            net_gex, call_gex, put_gex = self._calculate_gex_at_prices(significant_options, prices)
            
            # Find GEX at current price
            current_idx = np.abs(prices - current_price).argmin()
            gex_at_current = net_gex[current_idx]
            
            # Detect zero crosses (GEX flip points)
            zero_crosses = self._detect_zero_crosses(prices, net_gex)
            
            # Find accumulation zones (gamma concentration areas)
            accumulation_zones = self._find_accumulation_zones(prices, net_gex)
            
            # Calculate dealer positioning
            dealer_positioning = self._calculate_dealer_positioning(significant_options, prices, current_price)
            
            # Compile results
            results = {
                'ticker': ticker,
                'current_price': current_price,
                'gex_at_current': gex_at_current,
                'prices': prices.tolist(),
                'net_gex': net_gex.tolist(),
                'call_gex': call_gex.tolist(),
                'put_gex': put_gex.tolist(),
                'zero_crosses': zero_crosses,
                'accumulation_zones': accumulation_zones,
                'dealer_positioning': dealer_positioning,
                'options_processed': len(significant_options),
                'analysis_timestamp': datetime.now()
            }
            
            logger.info(f"GEX analysis complete for {ticker}: "
                       f"GEX={gex_at_current:.2f}, "
                       f"Zero crosses={len(zero_crosses)}, "
                       f"Accumulation zones={len(accumulation_zones)}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in GEX analysis for {ticker}: {e}", exc_info=True)
            return {'error': str(e), 'ticker': ticker}
    
    def _convert_to_factors(self, ticker: str, current_price: float, 
                           gex_results: Dict[str, Any]) -> List[FactorData]:
        """
        Convert GEX analysis results to FactorData objects for orchestrator.
        
        Args:
            ticker: Stock ticker symbol
            current_price: Current stock price
            gex_results: GEX analysis results
            
        Returns:
            List of FactorData objects
        """
        factors = []
        
        # Check for errors in GEX results
        if 'error' in gex_results:
            logger.warning(f"GEX analysis had errors for {ticker}: {gex_results['error']}")
            return factors
        
        try:
            # Default timeframe for GEX analysis
            timeframe = TimeFrame.HOUR_1
            
            # Create factors from zero-crosses (GEX flip points)
            zero_crosses = gex_results.get('zero_crosses', [])
            for i, cross in enumerate(zero_crosses[:self.max_zero_crosses]):
                direction_bias = (DirectionBias.BULLISH if cross['crossing_type'] == 'negative_to_positive' 
                                else DirectionBias.BEARISH)
                
                factor = FactorData(
                    factor_name=f"GEX_ZeroFlip_{direction_bias.value.title()}_{timeframe.value}",
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=timeframe,
                    direction_bias=direction_bias,
                    strength_score=min(0.95, cross['strength']),  # Cap at 0.95
                    key_level_price=float(cross['price']),
                    reason_short=f"GEX flip point at ${cross['price']:.2f} ({cross['crossing_type']})",
                    analyzer_name="enhanced_gex_analyzer",
                    details={
                        'gex_flip_type': cross['crossing_type'],
                        'slope': cross['slope'],
                        'strength': cross['strength'],
                        'distance_from_current': cross['distance_from_current'],
                        'market_impact': cross.get('market_impact', 'unknown'),
                        'flip_strength': cross['strength']
                    }
                )
                factors.append(factor)
            
            # Create factors from accumulation zones (high gamma concentration)
            accumulation_zones = gex_results.get('accumulation_zones', [])
            for i, zone in enumerate(accumulation_zones[:self.max_accumulation_zones]):
                direction_bias = DirectionBias.BULLISH if zone['gex'] > 0 else DirectionBias.BEARISH
                zone_type = "support" if zone['gex'] > 0 else "resistance"
                
                factor = FactorData(
                    factor_name=f"GEX_AccumulationZone_{zone_type.title()}_{timeframe.value}",
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=timeframe,
                    direction_bias=direction_bias,
                    strength_score=min(0.90, zone['strength']),  # Cap at 0.90
                    key_level_price=float(zone['price']),
                    reason_short=f"GEX {zone_type} zone at ${zone['price']:.2f}",
                    analyzer_name="enhanced_gex_analyzer",
                    details={
                        'zone_type': zone_type,
                        'gex_value': zone['gex'],
                        'zone_strength': zone['strength'],
                        'distance_from_current': zone['distance_from_current'],
                        'market_impact': zone.get('market_impact', 'unknown')
                    }
                )
                factors.append(factor)
            
            # Create factor for overall GEX condition
            gex_at_current = gex_results.get('gex_at_current', 0)
            if abs(gex_at_current) > 0.1:  # Only create factor if GEX is significant
                if gex_at_current > 0:
                    overall_bias = DirectionBias.BULLISH
                    gex_condition = "positive"
                    explanation = "Positive GEX suggests dealer support"
                else:
                    overall_bias = DirectionBias.BEARISH
                    gex_condition = "negative"
                    explanation = "Negative GEX suggests dealer resistance"
                
                # Normalize GEX value to strength score (0-1)
                max_reasonable_gex = 1000000  # Adjust based on typical GEX values
                strength = min(0.8, abs(gex_at_current) / max_reasonable_gex)
                
                overall_factor = FactorData(
                    factor_name=f"GEX_Overall_{gex_condition.title()}_{timeframe.value}",
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=timeframe,
                    direction_bias=overall_bias,
                    strength_score=min(1.0, max(0.0, strength)),
                    key_level_price=current_price,
                    reason_short=f"Overall {gex_condition} GEX: {gex_at_current:.2f}",
                    analyzer_name="enhanced_gex_analyzer",
                    details={
                        'gex_at_current': gex_at_current,
                        'gex_condition': gex_condition,
                        'explanation': explanation,
                        'options_processed': gex_results.get('options_processed', 0)
                    }
                )
                factors.append(overall_factor)
            
            logger.info(f"Converted GEX analysis to {len(factors)} FactorData objects for {ticker}")
            return factors
            
        except Exception as e:
            logger.error(f"Error converting GEX results to factors for {ticker}: {e}", exc_info=True)
            return []
    
    def _preprocess_options_data(self, options_data: pd.DataFrame, 
                                current_price: float) -> pd.DataFrame:
        """
        Preprocess and standardize options data from API.
        
        Args:
            options_data: Raw options data from API
            current_price: Current stock price
            
        Returns:
            Processed options DataFrame
        """
        df = options_data.copy()
        
        # Standardize column names - handle various API formats
        column_mappings = {
            'strike': 'strike_price',
            'expiry': 'expiration_date',
            'expiration': 'expiration_date',
            'exp_date': 'expiration_date',
            'type': 'option_type',
            'call_put': 'option_type',
            'iv': 'implied_volatility',
            'impl_vol': 'implied_volatility',
            'oi': 'open_interest',
            'vol': 'volume',
            'dte': 'days_to_expiry'
        }
        
        for old_name, new_name in column_mappings.items():
            if old_name in df.columns and new_name not in df.columns:
                df[new_name] = df[old_name]
        
        # Ensure required columns exist with fallbacks
        if 'strike_price' not in df.columns:
            logger.error("No strike_price column found in options data")
            return pd.DataFrame()
        
        if 'option_type' not in df.columns:
            logger.warning("No option_type column - creating balanced mix")
            df['option_type'] = ['call' if i % 2 == 0 else 'put' for i in range(len(df))]
        
        if 'implied_volatility' not in df.columns:
            logger.warning("No implied_volatility column - using default 0.25")
            df['implied_volatility'] = 0.25
        
        if 'open_interest' not in df.columns:
            logger.warning("No open_interest column - creating synthetic values")
            # More OI near the money
            atm_factor = np.exp(-0.5 * ((df['strike_price'] - current_price) / (current_price * 0.1)) ** 2)
            df['open_interest'] = (1000 * atm_factor).astype(int)
        
        # Calculate days to expiry
        if 'days_to_expiry' not in df.columns:
            if 'expiration_date' in df.columns:
                try:
                    df['expiration_date'] = pd.to_datetime(df['expiration_date'])
                    df['days_to_expiry'] = (df['expiration_date'] - datetime.now()).dt.days
                except:
                    df['days_to_expiry'] = 30  # Default
            else:
                df['days_to_expiry'] = 30  # Default
        
        # Separate call and put OI
        df['call_oi'] = df.apply(
            lambda row: row['open_interest'] if str(row['option_type']).lower() == 'call' else 0,
            axis=1
        )
        df['put_oi'] = df.apply(
            lambda row: row['open_interest'] if str(row['option_type']).lower() == 'put' else 0,
            axis=1
        )
        
        # Calculate Greeks if missing
        if 'gamma' not in df.columns or 'delta' not in df.columns:
            df = self._calculate_greeks(df, current_price)
        
        # Filter out invalid data
        df = df[
            (df['strike_price'] > 0) &
            (df['implied_volatility'] > 0) &
            (df['days_to_expiry'] > 0)
        ].copy()
        
        logger.info(f"Preprocessed {len(df)} valid options contracts")
        return df
    
    def _calculate_greeks(self, df: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """Calculate option Greeks using Black-Scholes."""
        logger.info("Calculating option Greeks")
        
        greeks_data = []
        for _, row in df.iterrows():
            time_to_expiry = max(row['days_to_expiry'] / 365.0, 1/365)
            
            delta, gamma = self._black_scholes_greeks(
                current_price,
                row['strike_price'],
                time_to_expiry,
                row['implied_volatility'],
                self.risk_free_rate,
                str(row['option_type']).lower()
            )
            
            greeks_data.append({'delta': delta, 'gamma': gamma})
        
        greeks_df = pd.DataFrame(greeks_data)
        df['delta'] = greeks_df['delta']
        df['gamma'] = greeks_df['gamma']
        
        return df
    
    def _black_scholes_greeks(self, spot: float, strike: float, time_to_expiry: float,
                             volatility: float, risk_free_rate: float, 
                             option_type: str) -> Tuple[float, float]:
        """Calculate Black-Scholes delta and gamma."""
        if time_to_expiry <= 0 or volatility <= 0:
            if option_type == 'call':
                delta = 1.0 if spot > strike else 0.0
            else:
                delta = -1.0 if spot < strike else 0.0
            gamma = 0.0
            return delta, gamma
        
        try:
            d1 = (np.log(spot / strike) + (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / (volatility * np.sqrt(time_to_expiry))
            d2 = d1 - volatility * np.sqrt(time_to_expiry)
            
            # Delta calculation
            if option_type == 'call':
                delta = norm.cdf(d1)
            else:
                delta = norm.cdf(d1) - 1.0
            
            # Gamma calculation (same for calls and puts)
            gamma = norm.pdf(d1) / (spot * volatility * np.sqrt(time_to_expiry))
            
            # Bounds checking
            delta = max(-1.0, min(1.0, delta))
            gamma = max(0.0, min(10.0, gamma))
            
            return delta, gamma
            
        except Exception as e:
            logger.warning(f"Error calculating Greeks: {e}")
            if option_type == 'call':
                delta = 0.5 if spot >= strike else 0.1
            else:
                delta = -0.5 if spot <= strike else -0.1
            gamma = 0.1
            return delta, gamma
    
    def _filter_significant_options(self, options: pd.DataFrame) -> pd.DataFrame:
        """Filter options based on significance threshold."""
        # Calculate significance score
        options['significance'] = (
            np.abs(options['gamma']) * 
            (options['call_oi'] + options['put_oi'])
        )
        
        # Normalize significance
        max_sig = options['significance'].max()
        if max_sig > 0:
            options['norm_significance'] = options['significance'] / max_sig
        else:
            options['norm_significance'] = 0
        
        # Filter
        filtered = options[
            options['norm_significance'] >= self.filter_threshold
        ].copy()
        
        logger.info(f"Filtered to {len(filtered)} significant options "
                   f"({len(filtered)/len(options):.1%} of total)")
        
        return filtered
    
    def _calculate_gex_at_prices(self, options: pd.DataFrame, 
                                prices: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Calculate GEX at each price point."""
        net_gex = np.zeros(len(prices))
        call_gex = np.zeros(len(prices))
        put_gex = np.zeros(len(prices))
        
        for i, price in enumerate(prices):
            for _, option in options.iterrows():
                # Recalculate gamma at this price
                time_to_expiry = option['days_to_expiry'] / 365.0
                
                _, gamma = self._black_scholes_greeks(
                    price,
                    option['strike_price'],
                    time_to_expiry,
                    option['implied_volatility'],
                    self.risk_free_rate,
                    str(option['option_type']).lower()
                )
                
                # Calculate GEX contribution (dealers are short options)
                if str(option['option_type']).lower() == 'call':
                    contribution = -gamma * option['call_oi'] * 100  # 100 shares per contract
                    call_gex[i] += contribution
                else:
                    contribution = -gamma * option['put_oi'] * 100
                    put_gex[i] += contribution
                
                net_gex[i] += contribution
        
        return net_gex, call_gex, put_gex
    
    def _detect_zero_crosses(self, prices: np.ndarray, gex: np.ndarray) -> List[Dict]:
        """Detect zero-gamma crossing points."""
        zero_crosses = []
        
        # Find sign changes
        sign_changes = np.where(np.diff(np.signbit(gex)))[0]
        
        for idx in sign_changes:
            p1, p2 = prices[idx], prices[idx + 1]
            g1, g2 = gex[idx], gex[idx + 1]
            
            # Interpolate exact crossing point
            if g2 != g1:
                cross_price = p1 - g1 * (p2 - p1) / (g2 - g1)
            else:
                cross_price = (p1 + p2) / 2
            
            # Calculate slope
            slope = (g2 - g1) / (p2 - p1)
            
            # Skip weak crossings
            if abs(slope) < self.min_zero_cross_slope:
                continue
            
            # Determine crossing type
            crossing_type = 'negative_to_positive' if g1 < 0 and g2 > 0 else 'positive_to_negative'
            
            # Calculate strength
            distance_factor = 1 / (1 + abs(cross_price - prices[len(prices)//2]) / prices[len(prices)//2] * 10)
            slope_factor = min(1.0, abs(slope) / 0.1)
            strength = 0.7 * slope_factor + 0.3 * distance_factor
            
            zero_crosses.append({
                'price': float(cross_price),
                'crossing_type': crossing_type,
                'slope': float(slope),
                'strength': float(strength),
                'distance_from_current': float(abs(cross_price - prices[len(prices)//2])),
                'market_impact': 'support' if crossing_type == 'negative_to_positive' else 'resistance'
            })
        
        # Sort by strength
        zero_crosses.sort(key=lambda x: x['strength'], reverse=True)
        
        return zero_crosses
    
    def _find_accumulation_zones(self, prices: np.ndarray, gex: np.ndarray) -> List[Dict]:
        """Find gamma accumulation zones (local extrema)."""
        accumulation_zones = []
        
        # Find local extrema
        for i in range(1, len(gex) - 1):
            is_max = gex[i] > gex[i-1] and gex[i] > gex[i+1]
            is_min = gex[i] < gex[i-1] and gex[i] < gex[i+1]
            
            if is_max or is_min:
                # Calculate strength
                max_abs_gex = max(abs(np.max(gex)), abs(np.min(gex)))
                gex_strength = abs(gex[i]) / max_abs_gex if max_abs_gex > 0 else 0
                
                current_price = prices[len(prices)//2]  # Approximate current price
                distance_factor = 1 / (1 + abs(prices[i] - current_price) / current_price * 10)
                strength = 0.7 * gex_strength + 0.3 * distance_factor
                
                accumulation_zones.append({
                    'price': float(prices[i]),
                    'gex': float(gex[i]),
                    'type': 'positive' if gex[i] > 0 else 'negative',
                    'strength': float(strength),
                    'distance_from_current': float(abs(prices[i] - current_price)),
                    'market_impact': 'support' if gex[i] > 0 else 'resistance'
                })
        
        # Sort by strength
        accumulation_zones.sort(key=lambda x: x['strength'], reverse=True)
        
        return accumulation_zones
    
    def _calculate_dealer_positioning(self, options: pd.DataFrame, prices: np.ndarray, 
                                     current_price: float) -> Dict:
        """Calculate dealer positioning analysis."""
        try:
            delta_exposure = np.zeros(len(prices))
            
            # Calculate delta exposure at each price
            for i, price in enumerate(prices):
                for _, option in options.iterrows():
                    time_to_expiry = option['days_to_expiry'] / 365.0
                    
                    delta, _ = self._black_scholes_greeks(
                        price,
                        option['strike_price'],
                        time_to_expiry,
                        option['implied_volatility'],
                        self.risk_free_rate,
                        str(option['option_type']).lower()
                    )
                    
                    # Dealers are short options
                    if str(option['option_type']).lower() == 'call':
                        delta_exposure[i] += -delta * option['call_oi'] * 100
                    else:
                        delta_exposure[i] += -delta * option['put_oi'] * 100
            
            current_idx = np.abs(prices - current_price).argmin()
            current_delta = delta_exposure[current_idx]
            
            return {
                'prices': prices.tolist(),
                'delta_exposure': delta_exposure.tolist(),
                'current_delta': float(current_delta),
                'position_adjustment': float(-current_delta - self.dealer_net_position)
            }
            
        except Exception as e:
            logger.error(f"Error calculating dealer positioning: {e}")
            return {
                'prices': [],
                'delta_exposure': [],
                'current_delta': 0.0,
                'position_adjustment': 0.0,
                'error': str(e)
            }


# Legacy interface support - for backward compatibility
def analyze_gex_factors(ticker: str, api_gateway, **kwargs) -> List[FactorData]:
    """
    Legacy function interface for backward compatibility.
    
    Args:
        ticker: Stock ticker symbol
        api_gateway: API gateway instance
        **kwargs: Additional parameters
        
    Returns:
        List of FactorData objects
    """
    try:
        analyzer = EnhancedGammaExposureAnalyzer(api_gateway_instance=api_gateway)
        return analyzer.analyze_factors(ticker=ticker, **kwargs)
    except Exception as e:
        logger.error(f"Error in legacy GEX analysis interface: {e}")
        return []

# Backward compatibility alias for orchestrator
EnhancedGEXAnalyzer = EnhancedGammaExposureAnalyzer
