#!/usr/bin/env python3
"""
Guaranteed Logging Wrapper
===========================
Ensures shadow mode data is captured even if main logging fails
"""

import json
import os
from datetime import datetime
from pathlib import Path

class GuaranteedLogger:
    """Failsafe logging that cannot be silently ignored"""
    
    def __init__(self):
        self.core_root = Path(__file__).parent.parent
        self.backup_dir = self.core_root / "training_logs" / "AgentZero" / "backup"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def force_log(self, signal_data, math_data, decision, outcome):
        """Force log data with multiple fallback mechanisms"""
        
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        
        # Primary attempt
        try:
            from agents.agent_zero import AgentZeroAdvisor
            agent = AgentZeroAdvisor()
            agent.log_training_data(signal_data, math_data, decision, outcome)
            print(f"PRIMARY LOGGING: SUCCESS - {timestamp_str}")
            return True
        except Exception as e:
            print(f"PRIMARY LOGGING: FAILED - {e}")
        
        # Backup attempt - direct file write
        try:
            backup_file = self.backup_dir / f"backup_training_{timestamp_str}.json"
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'signal_data': signal_data,
                'math_data': math_data,
                'decision': decision,
                'outcome': outcome,
                'backup_reason': 'primary_logging_failed'
            }
            
            with open(backup_file, 'w') as f:
                json.dump(backup_data, f, indent=2, default=str)
            
            print(f"BACKUP LOGGING: SUCCESS - {backup_file.name}")
            return True
            
        except Exception as e:
            print(f"BACKUP LOGGING: FAILED - {e}")
            
        # Emergency attempt - print to console
        print(f"EMERGENCY LOG: {json.dumps({
            'signal': signal_data, 'math': math_data, 
            'decision': decision, 'outcome': outcome
        }, default=str)}")
        
        return False

# Global instance
_guaranteed_logger = GuaranteedLogger()

def guaranteed_log(signal_data, math_data, decision, outcome):
    """Global function for guaranteed logging"""
    return _guaranteed_logger.force_log(signal_data, math_data, decision, outcome)
