"""
Fixed Options Open Interest Analyzer

This module implements the OptionsOIAnalyzer class which analyzes options
open interest data to identify potential support and resistance levels,
institutional positioning, and other liquidity patterns.

Complete implementation with proper imports, error handling, and factor generation.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
import scipy.stats as stats
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union

# Import BSM model for accurate gamma calculations
try:
    from bsm_model import BlackScholesModel
    BSM_MODEL_AVAILABLE = True
except ImportError:
    BSM_MODEL_AVAILABLE = False

try:
    from sklearn.cluster import DBSCAN
except ImportError:
    DBSCAN = None

# Setup logging
logger = logging.getLogger(__name__)

# Import data structures with fallbacks
try:
    from data.data_structures import LiquidityData, LiquidityLevel, LiquidityType
except ImportError:
    try:
        from data.data_structures import LiquidityData, LiquidityLevel, LiquidityType
    except ImportError:
        # Fallback implementations
        class LiquidityType:
            BUY = "BUY"
            SELL = "SELL"
            NEUTRAL = "NEUTRAL"
            INSTITUTIONAL = "INSTITUTIONAL"
            DYNAMIC = "DYNAMIC"

        class LiquidityLevel:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        class LiquidityData:
            def __init__(self, **kwargs):
                self.levels = []
                self.traps = []
                self.metrics = {}
                for key, value in kwargs.items():
                    setattr(self, key, value)

            def add_level(self, level):
                self.levels.append(level)

            def add_trap(self, trap):
                self.traps.append(trap)

            def add_metric(self, name, value):
                self.metrics[name] = value

# Import base analyzer with fallback
try:
    from base_analyzer import BaseAnalyzer
except ImportError:
    try:
        from base_analyzer import BaseAnalyzer
    except ImportError:
        class BaseAnalyzer:
            def __init__(self, config=None):
                self.config = config or {}
            
            def _get_default_config(self):
                return {}


class OptionsOIAnalyzer(BaseAnalyzer):
    """
    Analyzer that uses options open interest to detect liquidity levels.

    This analyzer focuses on identifying strike prices with significant
    open interest that may act as support/resistance or liquidity zones.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Options OI analyzer with configuration.

        Args:
            config: Dictionary containing configuration parameters
        """
        super().__init__(config)
        logger.info("OptionsOIAnalyzer initialized")

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for options OI analyzer with dynamic thresholds."""
        return {
            'oi_percentile': 90,
            'use_dynamic_thresholds': True,
            'min_oi_base': 50,  # Base minimum, will be adjusted dynamically
            'cluster_distance_base': 0.003,  # Base cluster distance, adjusted by volatility
            'significance_level': 0.05,  # Statistical significance level
            'use_advanced_clustering': False,
            'max_days_to_expiry': 90,
            'min_days_to_expiry': 0,
            # Dynamic threshold multipliers
            'volatility_adjustment_factor': 2.0,
            'price_level_adjustment': True,
            'market_cap_adjustment': True
        }

    def _calculate_dynamic_thresholds(self, data: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """Calculate dynamic thresholds based on actual market data and conditions."""
        try:
            thresholds = {}

            # Calculate market volatility from options data
            if 'implied_volatility' in data.columns:
                avg_iv = data['implied_volatility'].mean()
                volatility_factor = max(0.5, min(2.0, avg_iv / 0.25))  # Normalize around 25% IV
            else:
                volatility_factor = 1.0

            # Calculate price level adjustment (higher prices need higher absolute thresholds)
            if current_price > 500:
                price_factor = 2.0
            elif current_price > 200:
                price_factor = 1.5
            elif current_price > 100:
                price_factor = 1.2
            else:
                price_factor = 1.0

            # Dynamic minimum OI threshold
            oi_values = data['open_interest'].values
            oi_median = np.median(oi_values[oi_values > 0])
            oi_std = np.std(oi_values[oi_values > 0])
            thresholds['min_oi'] = max(
                self.config.get('min_oi_base', 50),
                oi_median + (oi_std * 0.5)  # 0.5 standard deviations above median
            )

            # Dynamic cluster distance based on volatility
            base_distance = self.config.get('cluster_distance_base', 0.003)
            thresholds['cluster_distance'] = base_distance * volatility_factor

            # Dynamic call/put ratio threshold based on market conditions
            call_oi = data[data['option_type'] == 'call']['open_interest'].sum()
            put_oi = data[data['option_type'] == 'put']['open_interest'].sum()
            market_cp_ratio = call_oi / put_oi if put_oi > 0 else 2.0

            # Adjust threshold based on current market bias
            if market_cp_ratio > 3.0:  # Very bullish market
                thresholds['call_put_ratio_threshold'] = market_cp_ratio * 0.8
            elif market_cp_ratio < 0.5:  # Very bearish market
                thresholds['call_put_ratio_threshold'] = 1.0 / (market_cp_ratio * 0.8)
            else:
                thresholds['call_put_ratio_threshold'] = 2.0 + (volatility_factor - 1.0)

            # Dynamic volume/OI ratio threshold
            if 'volume' in data.columns:
                vol_oi_ratios = data['volume'] / data['open_interest']
                vol_oi_ratios = vol_oi_ratios[vol_oi_ratios.notna() & (vol_oi_ratios > 0)]
                if len(vol_oi_ratios) > 0:
                    vol_oi_25th = np.percentile(vol_oi_ratios, 25)  # Use 25th percentile for institutional (low ratios)
                    thresholds['volume_oi_ratio_threshold'] = max(0.2, vol_oi_25th * 1.5)  # General threshold
                    thresholds['institutional_volume_threshold'] = min(0.25, vol_oi_25th * 1.2)  # Lower threshold for institutional
                else:
                    thresholds['volume_oi_ratio_threshold'] = 0.3
                    thresholds['institutional_volume_threshold'] = 0.25  # Lower = institutional
            else:
                thresholds['volume_oi_ratio_threshold'] = 0.3
                thresholds['institutional_volume_threshold'] = 0.25  # Lower = institutional

            # Dynamic confidence threshold based on data quality
            data_quality = self._assess_data_quality(data)
            thresholds['min_confidence'] = 0.4 + (data_quality * 0.3)  # 40-70% based on data quality

            # Dynamic gamma exposure threshold
            thresholds['min_gamma_exposure'] = current_price * 10 * price_factor  # Scale with price

            # Add other required config keys with dynamic values
            thresholds['oi_percentile'] = 90  # Keep this static for now
            thresholds['min_days_to_expiry'] = 0
            thresholds['max_days_to_expiry'] = 90
            thresholds['significance_level'] = 0.05

            logger.debug(f"Dynamic thresholds calculated: {thresholds}")
            return thresholds

        except Exception as e:
            logger.error(f"Error calculating dynamic thresholds: {e}")
            # Return conservative defaults
            return {
                'min_oi': 100,
                'cluster_distance': 0.005,
                'call_put_ratio_threshold': 2.0,
                'volume_oi_ratio_threshold': 0.3,
                'institutional_volume_threshold': 0.25,  # Lower = institutional
                'min_confidence': 0.6,
                'min_gamma_exposure': 1000
            }

    def _assess_data_quality(self, data: pd.DataFrame) -> float:
        """Assess the quality of options data for threshold adjustment."""
        try:
            quality_score = 0.0

            # Check data completeness
            required_cols = ['strike', 'open_interest', 'option_type']
            completeness = sum(1 for col in required_cols if col in data.columns) / len(required_cols)
            quality_score += completeness * 0.3

            # Check data volume
            if len(data) > 100:
                volume_score = 1.0
            elif len(data) > 50:
                volume_score = 0.7
            elif len(data) > 20:
                volume_score = 0.5
            else:
                volume_score = 0.2
            quality_score += volume_score * 0.3

            # Check for additional data fields
            optional_cols = ['volume', 'implied_volatility', 'delta', 'gamma']
            optional_score = sum(1 for col in optional_cols if col in data.columns) / len(optional_cols)
            quality_score += optional_score * 0.4

            return min(1.0, quality_score)

        except Exception as e:
            logger.error(f"Error assessing data quality: {e}")
            return 0.5

    def analyze(self, data: pd.DataFrame) -> LiquidityData:
        """
        Analyze options open interest data to detect liquidity levels.

        Args:
            data: DataFrame containing options data with columns:
                - timestamp: Datetime of the observation
                - symbol: Ticker symbol
                - strike: Strike price
                - expiration: Option expiration date
                - option_type: 'call' or 'put'
                - open_interest: Open interest
                - volume: Trading volume (optional)
                - underlying_price: Price of the underlying (optional)

        Returns:
            LiquidityData object containing detected liquidity levels
        """
        if data.empty:
            return LiquidityData(
                symbol='Unknown',
                timestamp=datetime.now()
            )

        # CRITICAL FIX: Standardize API column names to analyzer expectations
        logger.debug("Standardizing API column names for OI analysis")
        data = data.copy()

        # Map API gateway column names to analyzer expected names
        column_mapping = {
            'type': 'option_type',           # API returns 'type', analyzer expects 'option_type'
            'expiry': 'expiration',          # API returns 'expiry', analyzer expects 'expiration'
        }

        for api_col, analyzer_col in column_mapping.items():
            if api_col in data.columns and analyzer_col not in data.columns:
                data[analyzer_col] = data[api_col]
                logger.debug(f"Mapped {api_col} -> {analyzer_col}")

        # Verify required columns are now present
        required_columns = ['strike', 'expiration', 'option_type', 'open_interest']
        missing_columns = [col for col in required_columns if col not in data.columns]

        if missing_columns:
            logger.error(f"Missing required columns after mapping: {missing_columns}")
            logger.error(f"Available columns: {list(data.columns)}")
            return LiquidityData(symbol='Unknown', timestamp=datetime.now())

        try:
            # Extract basic information
            symbol = data['symbol'].iloc[0] if 'symbol' in data.columns else 'Unknown'
            timestamp = data['timestamp'].iloc[-1] if 'timestamp' in data.columns else datetime.now()

            # Get underlying price if available
            underlying_price = None
            if 'underlying_price' in data.columns:
                underlying_price = data['underlying_price'].iloc[-1]

            # Create result container
            result = LiquidityData(symbol=symbol, timestamp=timestamp)

            # Filter by expiration date
            filtered_data = self.filter_by_expiration(data)

            if filtered_data.empty:
                logger.warning(f"No options data after expiration filtering for {symbol}")
                return result

            # Find significant OI levels
            oi_levels = self._find_significant_oi_levels(filtered_data, underlying_price)

            # Detect smart money patterns
            if 'volume' in filtered_data.columns:
                smart_money_positions = self.detect_smart_money_patterns(filtered_data)

                # Add smart money positions as liquidity levels
                for position in smart_money_positions:
                    # Skip if already covered by a regular OI level
                    if any(abs(level['price'] - position['strike']) < 0.01 * underlying_price 
                           for level in oi_levels if underlying_price):
                        continue

                    # Calculate strength based on volume/OI ratio
                    strength = min(1.0, position['volume_oi_ratio'] / self.config['institutional_volume_threshold'])

                    # Set liquidity type for institutional activity
                    position_type = LiquidityType.INSTITUTIONAL

                    # Add to levels
                    oi_levels.append({
                        'price': position['strike'],
                        'strike': position['strike'],
                        'expiration': position['expiration'],
                        'strength': strength,
                        'confidence': 0.8,  # High confidence for institutional activity
                        'type': position_type,
                        'call_oi': position['open_interest'] if position['option_type'] == 'call' else 0,
                        'put_oi': position['open_interest'] if position['option_type'] == 'put' else 0,
                        'call_volume': position['volume'] if position['option_type'] == 'call' else 0,
                        'put_volume': position['volume'] if position['option_type'] == 'put' else 0,
                        'volume_oi_ratio': position['volume_oi_ratio'],
                        'institutional': True
                    })

            # Test statistical significance for each level
            for level in oi_levels:
                significance_test = self.test_statistical_significance(filtered_data, level['strike'])
                level['statistically_significant'] = significance_test['significant']
                level['p_value'] = significance_test['p_value']
                level['z_score'] = significance_test['z_score']

                # Adjust confidence based on statistical significance
                if significance_test['significant']:
                    level['confidence'] = min(1.0, level['confidence'] * 1.2)
                else:
                    level['confidence'] = max(0.0, level['confidence'] * 0.8)

            # Calculate overall put/call ratio
            overall_pc_ratio = self.calculate_put_call_ratio(filtered_data)
            result.add_metric('put_call_ratio', overall_pc_ratio)

            # Calculate put/call ratio by expiration
            pc_ratio_by_expiry = {}
            for expiration, expiry_group in filtered_data.groupby('expiration'):
                pc_ratio_by_expiry[str(expiration)] = self.calculate_put_call_ratio(expiry_group)
            result.add_metric('put_call_ratio_by_expiry', pc_ratio_by_expiry)

            # Convert to liquidity levels
            for level in oi_levels:
                liquidity_level = LiquidityLevel(
                    price=level['price'],
                    strength=level['strength'],
                    liquidity_type=level['type'],
                    source="options_oi",
                    timestamp=timestamp,
                    confidence=level['confidence'],
                    metadata={
                        'strike': level['strike'],
                        'expiration': str(level['expiration']),
                        'call_oi': level.get('call_oi', 0),
                        'put_oi': level.get('put_oi', 0),
                        'call_volume': level.get('call_volume', 0),
                        'put_volume': level.get('put_volume', 0),
                        'call_put_ratio': level.get('call_put_ratio', 1.0),
                        'volume_oi_ratio': level.get('volume_oi_ratio', 0.0),
                        'statistically_significant': level.get('statistically_significant', False),
                        'p_value': level.get('p_value', 1.0),
                        'z_score': level.get('z_score', 0.0),
                        'institutional': level.get('institutional', False)
                    }
                )
                result.add_level(liquidity_level)

            logger.info(f"OptionsOI analysis complete for {symbol}: {len(result.levels)} levels found")
            return result

        except Exception as e:
            logger.error(f"Error in OptionsOI analysis for {symbol}: {e}", exc_info=True)
            return LiquidityData(symbol=symbol, timestamp=datetime.now())

    def _find_significant_oi_levels(self, data: pd.DataFrame, underlying_price: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        Find strike prices with significant open interest.

        Args:
            data: DataFrame containing options data
            underlying_price: Current price of the underlying asset

        Returns:
            List of dictionaries containing significant OI levels
        """
        significant_levels = []

        try:
            # If underlying price is not provided, try to estimate it
            if underlying_price is None:
                # Use the median of all strikes as a rough estimate
                underlying_price = data['strike'].median()

            # Calculate the cluster distance in absolute terms
            cluster_distance_abs = underlying_price * self.config['cluster_distance']

            # Group by expiration date to analyze each expiry separately
            for expiration, expiry_group in data.groupby('expiration'):
                # Calculate OI percentile threshold for this expiry
                oi_threshold = max(
                    self.config['min_oi'],
                    np.percentile(expiry_group['open_interest'], self.config['oi_percentile'])
                )

                # Find strikes with OI above threshold
                high_oi = expiry_group[expiry_group['open_interest'] >= oi_threshold]

                if high_oi.empty:
                    continue

                # Cluster nearby strikes
                if self.config.get('use_advanced_clustering', False) and DBSCAN is not None:
                    clusters = self.advanced_cluster_strikes(high_oi, underlying_price)
                else:
                    clusters = self._cluster_strikes(high_oi, cluster_distance_abs)

                for cluster in clusters:
                    cluster_data = high_oi[high_oi['strike'].isin(cluster)]

                    # Calculate aggregated metrics for the cluster
                    call_data = cluster_data[cluster_data['option_type'] == 'call']
                    put_data = cluster_data[cluster_data['option_type'] == 'put']

                    call_oi = call_data['open_interest'].sum()
                    put_oi = put_data['open_interest'].sum()
                    total_oi = call_oi + put_oi

                    # Skip if total OI is too low
                    if total_oi < self.config['min_oi']:
                        continue

                    # Calculate call/put ratio
                    call_put_ratio = call_oi / put_oi if put_oi > 0 else float('inf')

                    # Calculate volume metrics if available
                    volume_oi_ratio = 0.0
                    call_volume = put_volume = 0

                    if 'volume' in cluster_data.columns:
                        call_volume = call_data['volume'].sum()
                        put_volume = put_data['volume'].sum()
                        total_volume = call_volume + put_volume
                        volume_oi_ratio = total_volume / total_oi if total_oi > 0 else 0.0

                    # Calculate weighted average strike price
                    weighted_strike = (cluster_data['strike'] * cluster_data['open_interest']).sum() / total_oi

                    # Determine liquidity type based on call/put ratio and strike relative to underlying
                    liquidity_type = self._determine_liquidity_type(
                        weighted_strike, underlying_price, call_put_ratio, volume_oi_ratio
                    )

                    # Calculate strength based on OI magnitude and concentration
                    strength = self._calculate_strength(total_oi, high_oi['open_interest'].sum(), volume_oi_ratio)

                    # Calculate confidence
                    confidence = self._calculate_confidence(
                        call_put_ratio, volume_oi_ratio, total_oi, high_oi['open_interest'].sum()
                    )

                    if confidence >= self.config['min_confidence']:
                        significant_levels.append({
                            'price': weighted_strike,
                            'strike': weighted_strike,
                            'expiration': expiration,
                            'strength': strength,
                            'confidence': confidence,
                            'type': liquidity_type,
                            'call_oi': call_oi,
                            'put_oi': put_oi,
                            'call_volume': call_volume,
                            'put_volume': put_volume,
                            'call_put_ratio': call_put_ratio,
                            'volume_oi_ratio': volume_oi_ratio
                        })

        except Exception as e:
            logger.error(f"Error finding significant OI levels: {e}")

        return significant_levels

    def _cluster_strikes(self, high_oi_data: pd.DataFrame, cluster_distance: float) -> List[List[float]]:
        """
        Cluster nearby strike prices.

        Args:
            high_oi_data: DataFrame containing high OI options
            cluster_distance: Maximum distance between strikes in a cluster

        Returns:
            List of lists, where each inner list contains strikes in a cluster
        """
        try:
            # Sort strikes
            sorted_strikes = sorted(high_oi_data['strike'].unique())

            if not sorted_strikes:
                return []

            # Initialize clusters
            clusters = [[sorted_strikes[0]]]

            # Cluster nearby strikes
            for strike in sorted_strikes[1:]:
                if strike - clusters[-1][-1] <= cluster_distance:
                    # Add to current cluster
                    clusters[-1].append(strike)
                else:
                    # Start new cluster
                    clusters.append([strike])

            return clusters

        except Exception as e:
            logger.error(f"Error clustering strikes: {e}")
            return []

    def _determine_liquidity_type(
        self,
        strike: float,
        underlying_price: float,
        call_put_ratio: float,
        volume_oi_ratio: float
    ) -> str:
        """
        Determine the type of liquidity based on strike and call/put ratio.

        Args:
            strike: Strike price
            underlying_price: Current price of underlying
            call_put_ratio: Ratio of call OI to put OI
            volume_oi_ratio: Ratio of volume to OI

        Returns:
            LiquidityType enum value
        """
        try:
            # Check for institutional activity (LOW volume/OI ratio indicates institutional)
            is_institutional = volume_oi_ratio <= self.config['institutional_volume_threshold']

            # Determine if it's a buy or sell level
            if strike > underlying_price:
                # Above current price
                if call_put_ratio > self.config['call_put_ratio_threshold']:
                    # Heavy call activity above price = resistance/sell
                    return LiquidityType.INSTITUTIONAL if is_institutional else LiquidityType.SELL
                elif 1.0 / call_put_ratio > self.config['call_put_ratio_threshold']:
                    # Heavy put activity above price = potential buy
                    return LiquidityType.INSTITUTIONAL if is_institutional else LiquidityType.BUY
            else:
                # Below current price
                if call_put_ratio > self.config['call_put_ratio_threshold']:
                    # Heavy call activity below price = potential buy
                    return LiquidityType.INSTITUTIONAL if is_institutional else LiquidityType.BUY
                elif 1.0 / call_put_ratio > self.config['call_put_ratio_threshold']:
                    # Heavy put activity below price = support/sell
                    return LiquidityType.INSTITUTIONAL if is_institutional else LiquidityType.SELL

            # If no clear bias, mark as neutral
            return LiquidityType.NEUTRAL

        except Exception as e:
            logger.error(f"Error determining liquidity type: {e}")
            return LiquidityType.NEUTRAL

    def _calculate_strength(self, cluster_oi: float, total_high_oi: float, volume_oi_ratio: float) -> float:
        """
        Calculate the strength of a liquidity level based on its OI relative to all high OI.

        Args:
            cluster_oi: Total OI in the cluster
            total_high_oi: Sum of all high OI across all strikes
            volume_oi_ratio: Ratio of volume to OI

        Returns:
            Strength value between 0.0 and 1.0
        """
        try:
            # Base strength on the proportion of total high OI
            base_strength = min(1.0, cluster_oi / (total_high_oi * 0.3)) if total_high_oi > 0 else 0.0

            # Adjust based on volume/OI ratio if available
            # For institutional activity, lower vol/OI ratio should increase strength
            volume_factor = 1.0
            if volume_oi_ratio > 0:
                # Invert the ratio - lower vol/OI gets higher factor
                institutional_threshold = self.config['institutional_volume_threshold']
                if volume_oi_ratio <= institutional_threshold:
                    # Institutional range - boost strength
                    volume_factor = min(1.5, max(1.0, institutional_threshold / volume_oi_ratio))
                else:
                    # Retail range - reduce strength
                    volume_factor = max(0.7, institutional_threshold / volume_oi_ratio)

            return min(1.0, base_strength * volume_factor)

        except Exception as e:
            logger.error(f"Error calculating strength: {e}")
            return 0.0

    def _calculate_confidence(
        self,
        call_put_ratio: float,
        volume_oi_ratio: float,
        cluster_oi: float,
        total_high_oi: float
    ) -> float:
        """
        Calculate confidence in the liquidity level.

        Args:
            call_put_ratio: Ratio of call OI to put OI
            volume_oi_ratio: Ratio of volume to OI
            cluster_oi: Total OI in the cluster
            total_high_oi: Sum of all high OI

        Returns:
            Confidence value between 0.0 and 1.0
        """
        try:
            # Base confidence on call/put imbalance
            cp_ratio = max(call_put_ratio, 1.0 / call_put_ratio)
            cp_confidence = min(1.0, (cp_ratio - 1.0) / (self.config['call_put_ratio_threshold'] - 1.0))

            # Adjust based on OI concentration
            oi_concentration = cluster_oi / total_high_oi if total_high_oi > 0 else 0.0
            oi_confidence = min(1.0, oi_concentration * 3.0)

            # Adjust based on volume/OI ratio if available
            # For institutional activity, lower vol/OI ratio should increase confidence
            vol_confidence = 0.5
            if volume_oi_ratio > 0:
                institutional_threshold = self.config['institutional_volume_threshold']
                if volume_oi_ratio <= institutional_threshold:
                    # Institutional range - higher confidence for lower ratios
                    vol_confidence = min(1.0, institutional_threshold / volume_oi_ratio * 0.5)
                else:
                    # Retail range - lower confidence
                    vol_confidence = max(0.2, institutional_threshold / volume_oi_ratio * 0.5)

            # Combine the factors
            return min(1.0, (cp_confidence * 0.4 + oi_confidence * 0.4 + vol_confidence * 0.2))

        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.0

    def filter_by_expiration(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Filter options data based on expiration dates.

        Args:
            data: DataFrame containing options data

        Returns:
            Filtered DataFrame
        """
        try:
            today = datetime.now()

            # Calculate days to expiry if not already in the data
            if 'days_to_expiry' not in data.columns:
                data = data.copy()
                data['days_to_expiry'] = (pd.to_datetime(data['expiration']) - today).dt.days

            # Apply filters
            min_days = self.config['min_days_to_expiry']
            max_days = self.config['max_days_to_expiry']

            filtered_data = data[
                (data['days_to_expiry'] >= min_days) &
                (data['days_to_expiry'] <= max_days)
            ]

            return filtered_data

        except Exception as e:
            logger.error(f"Error filtering by expiration: {e}")
            return data

    def calculate_put_call_ratio(self, data: pd.DataFrame) -> float:
        """
        Calculate the put/call ratio for the given options data.

        Args:
            data: DataFrame containing options data

        Returns:
            Put/call ratio (put OI / call OI)
        """
        try:
            call_data = data[data['option_type'] == 'call']
            put_data = data[data['option_type'] == 'put']

            call_oi = call_data['open_interest'].sum()
            put_oi = put_data['open_interest'].sum()

            # Avoid division by zero
            if call_oi == 0:
                return float('inf')

            return put_oi / call_oi

        except Exception as e:
            logger.error(f"Error calculating put/call ratio: {e}")
            return 1.0

    def calculate_call_put_ratio(self, data: pd.DataFrame) -> float:
        """
        Calculate the call/put ratio for the given options data.

        Args:
            data: DataFrame containing options data

        Returns:
            Call/put ratio (call OI / put OI)
        """
        try:
            call_data = data[data['option_type'] == 'call']
            put_data = data[data['option_type'] == 'put']

            call_oi = call_data['open_interest'].sum()
            put_oi = put_data['open_interest'].sum()

            # Avoid division by zero
            if put_oi == 0:
                return float('inf')

            return call_oi / put_oi

        except Exception as e:
            logger.error(f"Error calculating call/put ratio: {e}")
            return 1.0

    def advanced_cluster_strikes(self, data: pd.DataFrame, underlying_price: float) -> List[List[float]]:
        """
        Use advanced clustering algorithms to group nearby strikes.

        Args:
            data: DataFrame containing options data
            underlying_price: Current price of the underlying

        Returns:
            List of lists, where each inner list contains strikes in a cluster
        """
        if data.empty or DBSCAN is None:
            return self._cluster_strikes(data, underlying_price * self.config['cluster_distance'])

        try:
            # Extract strikes and normalize by underlying price
            strikes = data['strike'].unique()
            normalized_strikes = strikes / underlying_price

            # Reshape for DBSCAN
            X = normalized_strikes.reshape(-1, 1)

            # Calculate epsilon based on config
            eps = self.config['cluster_distance']

            # Apply DBSCAN clustering
            clustering = DBSCAN(eps=eps, min_samples=1).fit(X)

            # Extract clusters
            labels = clustering.labels_
            unique_labels = set(labels)

            clusters = []
            for label in unique_labels:
                cluster_indices = np.where(labels == label)[0]
                cluster_strikes = strikes[cluster_indices]
                clusters.append(sorted(cluster_strikes))

            return clusters

        except Exception as e:
            logger.error(f"Error in advanced clustering: {e}")
            return self._cluster_strikes(data, underlying_price * self.config['cluster_distance'])

    def detect_smart_money_patterns(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Detect smart money positioning using pattern range analysis.

        Uses sophisticated pattern range detection:
        - Magnitude bands: Small (200-500), Mid (500-1200), Large (1200+)
        - V/OI ratios: 0.3-0.8 for smart money vs 1.5+ for retail
        - Temporal clustering and cross-dimensional analysis
        - Anomaly detection for new positioning signals

        Args:
            data: DataFrame containing options data

        Returns:
            List of dictionaries containing smart money pattern information
        """
        if data.empty or 'volume' not in data.columns:
            return []

        smart_money_patterns = []

        try:
            # Define magnitude bands for pattern range detection
            MAGNITUDE_BANDS = {
                'small_institutional': (200, 500),
                'mid_institutional': (500, 1200),
                'large_institutional': (1200, float('inf'))
            }

            # Smart money V/OI ratio range (0.3-0.8)
            SMART_MONEY_VOI_MIN = 0.3
            SMART_MONEY_VOI_MAX = 0.8
            RETAIL_VOI_MIN = 1.5  # Retail shows 1.5-4.0+ ratios

            # Group by strike and option type
            grouped = data.groupby(['strike', 'option_type'])

            for (strike, option_type), group in grouped:
                # Calculate volume/OI ratio
                total_volume = group['volume'].sum()
                total_oi = group['open_interest'].sum()

                if total_oi == 0:
                    continue

                volume_oi_ratio = total_volume / total_oi

                # Pattern Range Analysis
                # 1. Check if volume falls within smart money V/OI range
                is_smart_money_voi = SMART_MONEY_VOI_MIN <= volume_oi_ratio <= SMART_MONEY_VOI_MAX

                # 2. Exclude retail patterns (high churn)
                is_retail_pattern = volume_oi_ratio >= RETAIL_VOI_MIN

                # 3. Check magnitude bands for institutional sizing
                magnitude_band = None
                for band_name, (min_vol, max_vol) in MAGNITUDE_BANDS.items():
                    if min_vol <= total_volume < max_vol:
                        magnitude_band = band_name
                        break

                # 4. Pattern consistency check (irregular but persistent sizing)
                is_irregular_sizing = self._check_irregular_sizing(total_volume)

                # Smart money criteria: right V/OI range + magnitude band + not retail
                if is_smart_money_voi and magnitude_band and not is_retail_pattern and is_irregular_sizing:
                    # Calculate temporal clustering metrics
                    temporal_score = self._calculate_temporal_clustering(group)

                    # Calculate cross-dimensional patterns
                    cross_dim_score = self._calculate_cross_dimensional_patterns(
                        strike, option_type, total_volume, data
                    )

                    # Calculate average trade size if available
                    avg_trade_size = None
                    if 'trade_count' in group.columns:
                        trade_count = group['trade_count'].sum()
                        if trade_count > 0:
                            avg_trade_size = total_volume / trade_count

                    smart_money_patterns.append({
                        'strike': strike,
                        'option_type': option_type,
                        'volume': total_volume,
                        'open_interest': total_oi,
                        'volume_oi_ratio': volume_oi_ratio,
                        'magnitude_band': magnitude_band,
                        'temporal_score': temporal_score,
                        'cross_dim_score': cross_dim_score,
                        'avg_trade_size': avg_trade_size,
                        'expiration': group['expiration'].iloc[0],
                        'pattern_type': 'smart_money'
                    })

            # Sort by pattern strength (combination of V/OI, temporal, and cross-dimensional scores)
            smart_money_patterns.sort(
                key=lambda x: (x['temporal_score'] + x['cross_dim_score']) / 2,
                reverse=True
            )

        except Exception as e:
            logger.error(f"Error detecting smart money patterns: {e}")

        return smart_money_patterns

    def _check_irregular_sizing(self, volume: float) -> bool:
        """
        Check if volume represents irregular but institutional sizing.
        Institutions use irregular sizes (347, 523, 789) vs retail round numbers.
        """
        # Convert to string to check for patterns
        vol_str = str(int(volume))

        # Reject obvious round numbers (retail patterns)
        if volume % 100 == 0 or volume % 50 == 0:
            return False

        # Accept irregular numbers that suggest algorithmic slicing
        # Look for non-round patterns typical of institutional flow
        return len(vol_str) >= 3 and not all(d == vol_str[0] for d in vol_str)

    def _calculate_temporal_clustering(self, group: pd.DataFrame) -> float:
        """
        Calculate temporal clustering score for smart money detection.
        Institutions show consistent timing patterns.
        """
        try:
            if 'timestamp' not in group.columns or len(group) < 2:
                return 0.5  # Default score

            # Analyze time distribution
            timestamps = pd.to_datetime(group['timestamp'])

            # Check for concentration in first 2 hours or last 30 minutes
            market_open = timestamps.min().replace(hour=9, minute=30, second=0)
            market_close = timestamps.min().replace(hour=16, minute=0, second=0)

            first_2_hours = (timestamps >= market_open) & (timestamps <= market_open + pd.Timedelta(hours=2))
            last_30_min = timestamps >= market_close - pd.Timedelta(minutes=30)

            institutional_timing = (first_2_hours.sum() + last_30_min.sum()) / len(timestamps)

            return min(1.0, institutional_timing * 2)  # Scale to 0-1

        except Exception as e:
            logger.debug(f"Error calculating temporal clustering: {e}")
            return 0.5

    def _calculate_cross_dimensional_patterns(self, strike: float, option_type: str,
                                            volume: float, full_data: pd.DataFrame) -> float:
        """
        Calculate cross-dimensional pattern score.
        Institutions show coordinated activity across strikes/expirations.
        """
        try:
            score = 0.0

            # Check for cross-expiration positioning (same strike, multiple months)
            same_strike_data = full_data[
                (full_data['strike'] == strike) &
                (full_data['option_type'] == option_type)
            ]

            if len(same_strike_data['expiration'].unique()) > 1:
                score += 0.3  # Cross-expiration bonus

            # Check for strike clustering within similar volume ranges
            volume_range = (volume * 0.8, volume * 1.2)
            similar_volume_data = full_data[
                (full_data['volume'] >= volume_range[0]) &
                (full_data['volume'] <= volume_range[1]) &
                (full_data['option_type'] == option_type)
            ]

            if len(similar_volume_data) > 1:
                strike_spread = similar_volume_data['strike'].std()
                if strike_spread < strike * 0.1:  # Clustered strikes
                    score += 0.4

            # Check for systematic OTM positioning (15-30 delta range)
            # This would require delta calculation, simplified for now
            score += 0.3  # Base score for reaching this analysis

            return min(1.0, score)

        except Exception as e:
            logger.debug(f"Error calculating cross-dimensional patterns: {e}")
            return 0.3

    def analyze_factors(self,
                       data_package: Dict[str, Any]) -> List:
        """
        Analyze options open interest and generate FactorData objects.
        
        Args:
            data_package: Dictionary containing:
                - ticker: Stock symbol
                - current_price: Current market price
                - mtf_market_data: Multi-timeframe data
                - options_data: Options data (if available)
            
        Returns:
            List[FactorData]: Options OI factors detected
        """
        factors = []
        
        try:
            # Import FactorData to avoid circular imports
            try:
                from factor_specification import FactorData, DirectionBias, TimeFrame
            except ImportError:
                try:
                    from .factor_specification import FactorData, DirectionBias, TimeFrame
                except ImportError:
                    logger.error("Could not import FactorData - Options OI factors unavailable")
                    return factors

            # Extract data package components
            ticker = data_package.get('ticker', 'Unknown')
            current_price = data_package.get('current_price')

            # Ensure we have a real current price - no hardcoded fallbacks
            if current_price is None or current_price <= 0:
                logger.error(f"[{ticker}] No valid current_price provided in data package")
                return factors

            logger.info(f"[{ticker}] Starting OptionsOI analysis with price ${current_price:.2f}")
            logger.debug(f"[{ticker}] Data package keys: {list(data_package.keys())}")

            # Get options data from the data package
            options_data = data_package.get('options_data')

            # Ensure we have options data from orchestrator
            if options_data is None or (hasattr(options_data, 'empty') and options_data.empty):
                logger.warning(f"[{ticker}] No options data provided by orchestrator - cannot perform OI analysis")
                return factors

            # CRITICAL FIX: Standardize API column names to analyzer expectations
            logger.info(f"[{ticker}] Standardizing API column names for OI analysis")
            options_data = options_data.copy()

            # Map API gateway column names to analyzer expected names
            column_mapping = {
                'type': 'option_type',           # API returns 'type', analyzer expects 'option_type'
                'expiry': 'expiration',          # API returns 'expiry', analyzer expects 'expiration'
            }

            for api_col, analyzer_col in column_mapping.items():
                if api_col in options_data.columns and analyzer_col not in options_data.columns:
                    options_data[analyzer_col] = options_data[api_col]
                    logger.debug(f"[{ticker}] Mapped {api_col} -> {analyzer_col}")

            # Verify required columns are now present
            required_columns = ['strike', 'expiration', 'option_type', 'open_interest', 'volume']
            missing_columns = [col for col in required_columns if col not in options_data.columns]

            if missing_columns:
                logger.error(f"[{ticker}] Missing required columns after mapping: {missing_columns}")
                logger.error(f"[{ticker}] Available columns: {list(options_data.columns)}")
                return factors

            logger.info(f"[{ticker}] Column mapping successful - proceeding with OI analysis")
            
            # Add current price to options data if not present
            if hasattr(options_data, 'columns') and 'underlying_price' not in options_data.columns:
                options_data = options_data.copy()
                options_data['underlying_price'] = current_price

            # Calculate dynamic thresholds based on actual market data
            if self.config.get('use_dynamic_thresholds', True):
                dynamic_thresholds = self._calculate_dynamic_thresholds(options_data, current_price)
                # Update config with dynamic thresholds for this analysis
                original_config = self.config.copy()
                self.config.update(dynamic_thresholds)
                logger.info(f"[{ticker}] Using dynamic thresholds: min_oi={dynamic_thresholds['min_oi']:.0f}, "
                           f"cluster_distance={dynamic_thresholds['cluster_distance']:.4f}")
            else:
                original_config = None

            # Perform standard OI analysis
            logger.debug(f"[{ticker}] Performing OI analysis on {len(options_data)} options")
            liquidity_data = self.analyze(options_data)
            logger.debug(f"[{ticker}] OI analysis found {len(liquidity_data.levels)} liquidity levels")

            # Convert liquidity levels to factors
            oi_factors = self._create_oi_level_factors(ticker, liquidity_data, current_price, DirectionBias, FactorData, TimeFrame)
            factors.extend(oi_factors)
            logger.debug(f"[{ticker}] Created {len(oi_factors)} OI level factors")

            # Create put/call ratio factors
            pc_factors = self._create_pc_ratio_factors(ticker, liquidity_data, DirectionBias, FactorData, TimeFrame)
            factors.extend(pc_factors)
            logger.debug(f"[{ticker}] Created {len(pc_factors)} P/C ratio factors")

            # Create smart money pattern factors
            smart_money_factors = self._create_smart_money_factors(ticker, options_data, current_price, DirectionBias, FactorData, TimeFrame)
            factors.extend(smart_money_factors)
            logger.debug(f"[{ticker}] Created {len(smart_money_factors)} smart money factors")

            # Create gamma exposure factors
            gamma_factors = self._create_gamma_exposure_factors(ticker, options_data, current_price, DirectionBias, FactorData, TimeFrame)
            factors.extend(gamma_factors)
            logger.debug(f"[{ticker}] Created {len(gamma_factors)} gamma factors")
            
            # Limit factors to prevent over-generation
            max_factors = 8
            if len(factors) > max_factors:
                factors.sort(key=lambda f: f.strength_score, reverse=True)
                factors = factors[:max_factors]
            
            logger.info(f"[{ticker}] Generated {len(factors)} options OI factors")
            return factors
            
        except Exception as e:
            logger.error(f"[{ticker}] Error generating options OI factors: {e}", exc_info=True)
            return factors



    def _calculate_greeks_with_bsm(self, options_data: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """Calculate Greeks using the BSM model for options data."""
        try:
            from bsm_model import BlackScholesModel
            from datetime import datetime

            # Make a copy to avoid modifying original
            df = options_data.copy()

            # Calculate time to expiry in years
            if 'expiration' in df.columns:
                now = datetime.now()
                df['time_to_expiry'] = df['expiration'].apply(
                    lambda x: max(0.001, (x - now).days / 365.0) if pd.notnull(x) else 0.001
                )
            else:
                df['time_to_expiry'] = 0.0274  # Default to 10 days

            # Use default values for missing parameters
            risk_free_rate = 0.05  # 5% risk-free rate
            implied_vol = 0.25     # 25% implied volatility default

            # Add IV column if not present
            if 'implied_volatility' not in df.columns:
                df['implied_volatility'] = implied_vol

            # Calculate Greeks using vectorized BSM
            try:
                greeks_result = BlackScholesModel.calculate_batch(
                    S=np.full(len(df), current_price),
                    K=df['strike'].values,
                    T=df['time_to_expiry'].values,
                    r=np.full(len(df), risk_free_rate),
                    sigma=df.get('implied_volatility', implied_vol).values,
                    option_types=df['option_type'].values
                )

                # Add Greeks to dataframe
                for greek_name, greek_values in greeks_result.items():
                    if greek_name not in df.columns:
                        df[greek_name] = greek_values

                logger.debug(f"Successfully calculated Greeks for {len(df)} options")

            except Exception as e:
                logger.warning(f"BSM Greeks calculation failed: {e}")
                # Add default Greek values
                df['delta'] = 0.5
                df['gamma'] = 0.01
                df['theta'] = -0.05
                df['vega'] = 0.2

            return df

        except Exception as e:
            logger.error(f"Error calculating Greeks with BSM: {e}")
            return options_data

    def _create_oi_level_factors(self, ticker: str, liquidity_data, current_price: float,
                                DirectionBias, FactorData, TimeFrame) -> List:
        """Create factors from significant OI levels."""
        factors = []
        
        try:
            for level in liquidity_data.levels:
                # Skip low confidence levels
                if level.confidence < self.config['min_confidence']:
                    continue
                
                # Determine direction bias based on level type and position
                if level.price > current_price:
                    # Above current price
                    if level.metadata.get('call_oi', 0) > level.metadata.get('put_oi', 0):
                        direction = DirectionBias.BEARISH  # Heavy call OI above = resistance
                    else:
                        direction = DirectionBias.BULLISH  # Heavy put OI above = potential target
                else:
                    # Below current price
                    if level.metadata.get('call_oi', 0) > level.metadata.get('put_oi', 0):
                        direction = DirectionBias.BULLISH  # Heavy call OI below = support
                    else:
                        direction = DirectionBias.BEARISH  # Heavy put OI below = potential target
                
                # Use level strength and confidence for factor strength
                strength_score = min(1.0, max(0.0, (level.strength + level.confidence) / 2.0))
                
                # Determine factor name based on characteristics
                if level.metadata.get('institutional', False):
                    factor_name = f"OptionsOI_Institutional_{direction.value.title()}"
                else:
                    factor_name = f"OptionsOI_Level_{direction.value.title()}"
                
                # Create reason
                call_oi = level.metadata.get('call_oi', 0)
                put_oi = level.metadata.get('put_oi', 0)
                strike = level.metadata.get('strike', level.price)
                reason = f"Options OI at ${strike:.0f}: {call_oi:,.0f} calls, {put_oi:,.0f} puts"
                
                factor = FactorData(
                    factor_name=factor_name,
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=TimeFrame.DAY_1,  # Options typically daily analysis
                    direction_bias=direction,
                    strength_score=strength_score,
                    key_level_price=level.price,
                    details={
                        'strike': strike,
                        'call_oi': call_oi,
                        'put_oi': put_oi,
                        'total_oi': call_oi + put_oi,
                        'call_put_ratio': level.metadata.get('call_put_ratio', 1.0),
                        'volume_oi_ratio': level.metadata.get('volume_oi_ratio', 0.0),
                        'expiration': str(level.metadata.get('expiration', '')),
                        'distance_from_price_pct': abs(level.price - current_price) / current_price,
                        'statistically_significant': level.metadata.get('statistically_significant', False),
                        'p_value': level.metadata.get('p_value', 1.0),
                        'institutional': level.metadata.get('institutional', False)
                    },
                    reason_short=reason,
                    analyzer_name="OptionsOIAnalyzer",
                    analyzer_version="1.0",
                    data_quality_score=0.85
                )
                
                factors.append(factor)
        
        except Exception as e:
            logger.error(f"Error creating OI level factors: {e}")
        
        return factors
    
    def _create_pc_ratio_factors(self, ticker: str, liquidity_data, DirectionBias, FactorData, TimeFrame) -> List:
        """Create factors from put/call ratio analysis."""
        factors = []
        
        try:
            pc_ratio = liquidity_data.metrics.get('put_call_ratio', 1.0)
            
            # Check if put/call ratio is extreme
            if pc_ratio > 2.0:  # Heavy put activity
                direction = DirectionBias.BULLISH  # Contrarian indicator
                strength_score = min(1.0, (pc_ratio - 2.0) / 3.0)  # Scale from 0-1
                factor_name = "OptionsOI_PCR_Sentiment_Bullish"
                reason = f"PCR sentiment: High put/call ratio {pc_ratio:.2f} indicates bullish contrarian positioning"

            elif pc_ratio < 0.5:  # Heavy call activity
                direction = DirectionBias.BEARISH  # Contrarian indicator
                strength_score = min(1.0, (0.5 - pc_ratio) / 0.4)  # Scale from 0-1
                factor_name = "OptionsOI_PCR_Sentiment_Bearish"
                reason = f"PCR sentiment: Low put/call ratio {pc_ratio:.2f} indicates bearish contrarian positioning"
                
            else:
                return factors  # Normal range, no factor
            
            # Only create factor if strength is significant
            if strength_score >= 0.3:
                factor = FactorData(
                    factor_name=factor_name,
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=TimeFrame.DAY_1,
                    direction_bias=direction,
                    strength_score=strength_score,
                    details={
                        'put_call_ratio': pc_ratio,
                        'interpretation': 'contrarian_signal',
                        'ratio_type': 'extreme_high' if pc_ratio > 2.0 else 'extreme_low'
                    },
                    reason_short=reason,
                    analyzer_name="OptionsOIAnalyzer",
                    analyzer_version="1.0",
                    data_quality_score=0.8
                )
                
                factors.append(factor)
        
        except Exception as e:
            logger.error(f"Error creating P/C ratio factors: {e}")
        
        return factors
    
    def _create_smart_money_factors(self, ticker: str, options_data: pd.DataFrame,
                                   current_price: float, DirectionBias, FactorData, TimeFrame) -> List:
        """Create factors from smart money pattern analysis."""
        factors = []
        
        try:
            if not hasattr(options_data, 'columns') or 'volume' not in options_data.columns:
                return factors
            
            smart_money_positions = self.detect_smart_money_patterns(options_data)
            
            for position in smart_money_positions[:3]:  # Top 3 smart money positions
                volume_oi_ratio = position['volume_oi_ratio']
                
                # Skip if not truly smart money (we already filtered in detect_smart_money_patterns)
                # The positions returned are already smart money, so no need to filter again
                
                strike = position['strike']
                option_type = position['option_type']
                
                # Determine direction based on smart money positioning logic
                # Smart money bullish: calls at/below current price (value buying) or protective puts above
                if option_type == 'call':
                    if strike <= current_price:
                        direction = DirectionBias.BULLISH  # Smart money calls at/below current price (value)
                    else:
                        direction = DirectionBias.NEUTRAL  # Calls above current price (likely retail momentum)
                else:  # put
                    if strike >= current_price:
                        direction = DirectionBias.BULLISH  # Protective puts above current price (smart money hedging)
                    else:
                        direction = DirectionBias.BEARISH  # Puts below current price (bearish positioning)
                
                # Calculate strength using pattern range analysis
                magnitude_band = position.get('magnitude_band', 'unknown')
                temporal_score = position.get('temporal_score', 0.5)
                cross_dim_score = position.get('cross_dim_score', 0.5)

                # Combine pattern scores for overall strength
                pattern_strength = (temporal_score + cross_dim_score) / 2
                voi_strength = min(1.0, (0.8 - volume_oi_ratio) / 0.5)  # Higher strength for lower V/OI
                strength_score = (pattern_strength + voi_strength) / 2

                factor_name = f"OptionsOI_SmartMoney_{option_type.title()}_{direction.value.title()}"
                reason = f"Smart money {option_type} pattern at ${strike:.0f} ({magnitude_band}, V/OI: {volume_oi_ratio:.2f})"
                
                factor = FactorData(
                    factor_name=factor_name,
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=TimeFrame.DAY_1,
                    direction_bias=direction,
                    strength_score=strength_score,
                    key_level_price=strike,
                    details={
                        'strike': strike,
                        'option_type': option_type,
                        'volume': position['volume'],
                        'open_interest': position['open_interest'],
                        'volume_oi_ratio': volume_oi_ratio,
                        'magnitude_band': magnitude_band,
                        'temporal_score': temporal_score,
                        'cross_dim_score': cross_dim_score,
                        'pattern_strength': pattern_strength,
                        'avg_trade_size': position.get('avg_trade_size'),
                        'expiration': str(position['expiration']),
                        'distance_from_price_pct': abs(strike - current_price) / current_price
                    },
                    reason_short=reason,
                    analyzer_name="OptionsOIAnalyzer",
                    analyzer_version="1.0",
                    data_quality_score=0.9
                )
                
                factors.append(factor)
        
        except Exception as e:
            logger.error(f"Error creating smart money factors: {e}")
        
        return factors
    
    def _create_gamma_exposure_factors(self, ticker: str, options_data: pd.DataFrame,
                                      current_price: float, DirectionBias, FactorData, TimeFrame) -> List:
        """Create factors from gamma exposure analysis."""
        factors = []
        
        try:
            if not hasattr(options_data, 'columns'):
                return factors
                
            # Group by strike to analyze gamma concentration
            strike_groups = options_data.groupby('strike')
            
            gamma_concentrations = []
            for strike, group in strike_groups:
                call_oi = group[group['option_type'] == 'call']['open_interest'].sum()
                put_oi = group[group['option_type'] == 'put']['open_interest'].sum()
                total_oi = call_oi + put_oi
                
                if total_oi < self.config['min_oi']:
                    continue
                
                # Calculate distance from ATM (needed for all calculations)
                distance_from_atm = abs(strike - current_price) / current_price

                # Calculate gamma exposure using real Greeks if available
                gamma_exposure = 0.0

                # Check if we have real gamma data from BSM calculations
                strike_options = group[group['strike'] == strike]
                if 'gamma' in strike_options.columns and not strike_options['gamma'].isna().all():
                    # Use real gamma values
                    avg_gamma = strike_options['gamma'].mean()
                    gamma_exposure = total_oi * avg_gamma * 100  # 100 shares per contract
                    logger.debug(f"Using real gamma for strike {strike}: {avg_gamma:.4f}")

                elif BSM_MODEL_AVAILABLE:
                    try:
                        # Calculate gamma using BSM model with real expiry data
                        if not strike_options.empty and 'time_to_expiry' in strike_options.columns:
                            avg_tte = strike_options['time_to_expiry'].mean()
                        else:
                            avg_tte = 30 / 365.0  # Default 30 days

                        # Use real IV if available, otherwise default
                        if 'implied_volatility' in strike_options.columns:
                            avg_iv = strike_options['implied_volatility'].mean()
                        else:
                            avg_iv = 0.25  # Default 25% IV

                        risk_free_rate = 0.05

                        # Calculate gamma using BSM
                        greeks = BlackScholesModel.calculate(
                            S=current_price, K=strike, T=avg_tte,
                            r=risk_free_rate, sigma=avg_iv, option_type='call'
                        )

                        gamma_per_contract = greeks['gamma']
                        gamma_exposure = total_oi * gamma_per_contract * 100
                        logger.debug(f"Calculated BSM gamma for strike {strike}: {gamma_per_contract:.4f}")

                    except Exception as e:
                        logger.debug(f"BSM gamma calculation failed for strike {strike}: {e}")
                        # Fallback to simplified calculation
                        gamma_weight = max(0, 1 - distance_from_atm * 10)
                        gamma_exposure = total_oi * gamma_weight
                else:
                    # Simplified gamma estimation when BSM not available
                    gamma_weight = max(0, 1 - distance_from_atm * 10)  # Decay with distance
                    gamma_exposure = total_oi * gamma_weight
                
                gamma_concentrations.append({
                    'strike': strike,
                    'gamma_exposure': gamma_exposure,
                    'call_oi': call_oi,
                    'put_oi': put_oi,
                    'total_oi': total_oi,
                    'distance_from_atm': distance_from_atm
                })
            
            # Sort by gamma exposure and take top concentrations
            gamma_concentrations.sort(key=lambda x: x['gamma_exposure'], reverse=True)
            
            for concentration in gamma_concentrations[:2]:  # Top 2 gamma concentrations
                if concentration['gamma_exposure'] < 1000:  # Minimum threshold
                    continue
                
                strike = concentration['strike']
                
                # Gamma walls typically act as magnets (price tends to pin to high gamma strikes)
                if strike > current_price:
                    direction = DirectionBias.BULLISH  # Gamma wall above pulls price up
                else:
                    direction = DirectionBias.BEARISH  # Gamma wall below pulls price down
                
                # Calculate strength from gamma exposure relative to max
                max_gamma = gamma_concentrations[0]['gamma_exposure'] if gamma_concentrations else 1
                strength_score = min(1.0, concentration['gamma_exposure'] / max_gamma * 0.8)
                
                factor_name = f"OptionsOI_GammaWall_{direction.value.title()}"
                reason = f"Gamma concentration at ${strike:.0f} ({concentration['total_oi']:,.0f} OI)"
                
                factor = FactorData(
                    factor_name=factor_name,
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=TimeFrame.DAY_1,
                    direction_bias=direction,
                    strength_score=strength_score,
                    key_level_price=strike,
                    details={
                        'strike': strike,
                        'gamma_exposure': concentration['gamma_exposure'],
                        'call_oi': concentration['call_oi'],
                        'put_oi': concentration['put_oi'],
                        'total_oi': concentration['total_oi'],
                        'distance_from_atm': concentration['distance_from_atm'],
                        'distance_from_price_pct': abs(strike - current_price) / current_price
                    },
                    reason_short=reason,
                    analyzer_name="OptionsOIAnalyzer",
                    analyzer_version="1.0",
                    data_quality_score=0.85
                )
                
                factors.append(factor)
        
        except Exception as e:
            logger.error(f"Error creating gamma exposure factors: {e}")
        
        return factors

    def test_statistical_significance(self, data: pd.DataFrame, strike: float) -> Dict[str, Any]:
        """
        Test the statistical significance of open interest at a given strike.

        Args:
            data: DataFrame containing options data
            strike: Strike price to test

        Returns:
            Dictionary containing test results
        """
        try:
            # Get data for this strike
            strike_data = data[data['strike'] == strike]

            if strike_data.empty:
                return {
                    'strike': strike,
                    'significant': False,
                    'p_value': 1.0,
                    'z_score': 0.0
                }

            # Get all OI values
            all_oi = data['open_interest'].values
            strike_oi = strike_data['open_interest'].sum()

            # Calculate mean and standard deviation
            mean_oi = np.mean(all_oi)
            std_oi = np.std(all_oi)

            if std_oi == 0:
                return {
                    'strike': strike,
                    'significant': False,
                    'p_value': 1.0,
                    'z_score': 0.0
                }

            # Calculate z-score
            z_score = (strike_oi - mean_oi) / std_oi

            # Calculate p-value (two-tailed test)
            p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))

            # Determine significance
            significant = p_value < self.config['significance_level']

            return {
                'strike': strike,
                'oi': strike_oi,
                'mean_oi': mean_oi,
                'std_oi': std_oi,
                'z_score': z_score,
                'p_value': p_value,
                'significant': significant
            }

        except Exception as e:
            logger.error(f"Error testing statistical significance: {e}")
            return {
                'strike': strike,
                'significant': False,
                'p_value': 1.0,
                'z_score': 0.0
            }

# Backward compatibility alias
OILiquidityAnalyzer = OptionsOIAnalyzer
