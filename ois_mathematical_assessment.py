#!/usr/bin/env python3
"""
MATHEMATICAL ASSESSMENT: Options Intelligence Services
Comprehensive analysis with edge case testing and numerical validation
"""

import sys
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple

# Test Data Scenarios for Mathematical Validation
TEST_SCENARIOS = [
    # Standard ATM Call
    {
        'name': 'ATM_Call_Standard',
        'underlying': 100.0,
        'strike': 100.0,
        'expiration': '2025-02-21',  # 30 days
        'option_type': 'call',
        'iv': 0.20,
        'iv_rank': 50.0,
        'expected_delta': 0.51,  # Theoretical ATM delta ~0.5
        'expected_gamma': 0.039,  # Peak gamma at ATM
        'risk_level': 'moderate'
    },
    
    # Deep ITM Call - Low Risk/High Probability
    {
        'name': 'Deep_ITM_Call',
        'underlying': 100.0,
        'strike': 80.0,
        'expiration': '2025-02-21',
        'option_type': 'call',
        'iv': 0.20,
        'iv_rank': 30.0,
        'expected_delta': 0.95,  # Very high delta
        'expected_gamma': 0.005,  # Low gamma
        'risk_level': 'low'
    },
    
    # Far OTM Call - High Risk/Low Probability
    {
        'name': 'Far_OTM_Call',
        'underlying': 100.0,
        'strike': 130.0,
        'expiration': '2025-02-21',
        'option_type': 'call',
        'iv': 0.20,
        'iv_rank': 80.0,
        'expected_delta': 0.05,  # Very low delta
        'expected_gamma': 0.003,  # Very low gamma
        'risk_level': 'very_high'
    },
    
    # Near Expiration ATM - Gamma Risk
    {
        'name': 'Near_Exp_ATM',
        'underlying': 100.0,
        'strike': 100.0,
        'expiration': '2025-06-30',  # 4 days
        'option_type': 'call',
        'iv': 0.30,
        'iv_rank': 70.0,
        'expected_delta': 0.52,
        'expected_gamma': 0.15,  # Very high gamma near expiration
        'risk_level': 'extreme_time_decay'
    },
    
    # High IV Environment - Vega Risk
    {
        'name': 'High_IV_Environment',
        'underlying': 100.0,
        'strike': 105.0,
        'expiration': '2025-08-15',  # 50 days
        'option_type': 'call',
        'iv': 0.60,  # Very high IV
        'iv_rank': 95.0,
        'expected_delta': 0.38,
        'expected_vega': 0.25,  # High vega exposure
        'risk_level': 'high_iv_crush_risk'
    }
]

class MathematicalAssessment:
    """
    Mathematical validation framework for Options Intelligence Service
    """
    
    def __init__(self):
        self.test_results = []
        self.accuracy_thresholds = {
            'delta': 0.05,    # ±5% tolerance
            'gamma': 0.10,    # ±10% tolerance  
            'theta': 0.15,    # ±15% tolerance
            'vega': 0.10,     # ±10% tolerance
            'position_size': 0.20  # ±20% tolerance for complex calculations
        }
    
    def test_position_sizing_logic(self, scenario: Dict) -> Dict:
        """
        Test position sizing mathematical logic
        """
        # Simulate position sizing factors
        base_size = 2.0
        confidence = 0.75
        
        # IV adjustment factor
        iv_rank = scenario['iv_rank']
        if iv_rank > 80:
            iv_factor = 0.5  # Reduce size in expensive IV
        elif iv_rank < 20:
            iv_factor = 1.3  # Increase size in cheap IV
        else:
            iv_factor = 1.0
        
        # Delta adjustment factor
        delta = scenario.get('expected_delta', 0.5)
        if delta > 0.7:
            delta_factor = 0.8  # Reduce leverage for ITM
        elif delta < 0.3:
            delta_factor = 0.6  # Reduce size for far OTM
        else:
            delta_factor = 1.0
        
        # Calculate final position size
        position_size = base_size * confidence * iv_factor * delta_factor
        
        # Apply bounds
        position_size = max(0.1, min(position_size, 10.0))
        
        return {
            'base_size': base_size,
            'confidence': confidence,
            'iv_factor': iv_factor,
            'delta_factor': delta_factor,
            'final_size': position_size,
            'reasoning': f"Base({base_size}) × Confidence({confidence}) × IV({iv_factor}) × Delta({delta_factor}) = {position_size:.2f}"
        }
    
    def test_risk_profile_classification(self, scenario: Dict) -> Dict:
        """
        Test risk profile classification logic
        """
        delta = scenario.get('expected_delta', 0.5)
        gamma = scenario.get('expected_gamma', 0.02)
        iv_rank = scenario['iv_rank']
        
        # Classification logic
        if delta > 0.7:
            risk_class = 'low_risk_high_probability'
            leverage = 'low'
        elif delta > 0.45:
            risk_class = 'moderate_risk_balanced'
            leverage = 'moderate'
        elif delta > 0.2:
            risk_class = 'high_risk_moderate_probability'
            leverage = 'high'
        else:
            risk_class = 'very_high_risk_low_probability'
            leverage = 'very_high'
        
        # Adjust for gamma risk
        if gamma > 0.1:
            risk_modifier = 'gamma_acceleration_risk'
        else:
            risk_modifier = 'normal_gamma'
        
        # Adjust for IV environment
        if iv_rank > 80:
            iv_risk = 'iv_crush_risk'
        elif iv_rank < 20:
            iv_risk = 'iv_expansion_opportunity'
        else:
            iv_risk = 'normal_iv'
        
        return {
            'risk_classification': risk_class,
            'leverage_level': leverage,
            'gamma_risk': risk_modifier,
            'iv_risk': iv_risk,
            'delta': delta,
            'gamma': gamma,
            'iv_rank': iv_rank
        }
    
    def calculate_expected_move_accuracy(self, scenario: Dict) -> Dict:
        """
        Test expected move calculations
        """
        underlying = scenario['underlying']
        iv = scenario['iv']
        
        # Standard expected move formula: S × IV × sqrt(T)
        # Using 30 days = 30/365 years
        time_fraction = 30/365
        expected_move_pct = iv * np.sqrt(time_fraction) * 100
        expected_move_dollar = underlying * (expected_move_pct / 100)
        
        # One standard deviation move
        one_sigma_move = {
            'percentage': expected_move_pct,
            'dollar_amount': expected_move_dollar,
            'upper_target': underlying + expected_move_dollar,
            'lower_target': underlying - expected_move_dollar,
            'probability': 68.2  # One sigma probability
        }
        
        return {
            'expected_move': one_sigma_move,
            'calculation': f"{underlying} × {iv} × sqrt({time_fraction:.4f}) = ${expected_move_dollar:.2f}",
            'iv_used': iv,
            'time_fraction': time_fraction
        }
    
    def run_comprehensive_assessment(self) -> Dict:
        """
        Run comprehensive mathematical assessment
        """
        print("=" * 80)
        print("MATHEMATICAL ASSESSMENT: OPTIONS INTELLIGENCE SERVICE")
        print("=" * 80)
        
        assessment_results = {
            'scenario_tests': [],
            'accuracy_summary': {},
            'edge_case_handling': {},
            'mathematical_consistency': {}
        }
        
        for scenario in TEST_SCENARIOS:
            print(f"\n[TESTING] {scenario['name']}")
            print(f"Risk Level: {scenario['risk_level']}")
            
            # Test position sizing logic
            position_test = self.test_position_sizing_logic(scenario)
            print(f"Position Size: {position_test['final_size']:.2f} contracts")
            print(f"Logic: {position_test['reasoning']}")
            
            # Test risk classification
            risk_test = self.test_risk_profile_classification(scenario)
            print(f"Risk Class: {risk_test['risk_classification']}")
            print(f"Leverage: {risk_test['leverage_level']}")
            
            # Test expected move calculations
            move_test = self.calculate_expected_move_accuracy(scenario)
            print(f"Expected Move: ±${move_test['expected_move']['dollar_amount']:.2f}")
            print(f"Calculation: {move_test['calculation']}")
            
            scenario_result = {
                'scenario': scenario['name'],
                'position_sizing': position_test,
                'risk_classification': risk_test,
                'expected_move': move_test
            }
            
            assessment_results['scenario_tests'].append(scenario_result)
        
        # Mathematical Consistency Tests
        print(f"\n[MATHEMATICAL CONSISTENCY CHECKS]")
        
        # Test 1: Position size should decrease with higher risk
        high_risk_scenario = next(s for s in TEST_SCENARIOS if s['name'] == 'Far_OTM_Call')
        low_risk_scenario = next(s for s in TEST_SCENARIOS if s['name'] == 'Deep_ITM_Call')
        
        high_risk_size = self.test_position_sizing_logic(high_risk_scenario)['final_size']
        low_risk_size = self.test_position_sizing_logic(low_risk_scenario)['final_size']
        
        size_consistency = high_risk_size < low_risk_size
        print(f"Position Sizing Consistency: {size_consistency} (High Risk: {high_risk_size:.2f} < Low Risk: {low_risk_size:.2f})")
        
        # Test 2: IV rank should affect position sizing
        high_iv_scenario = next(s for s in TEST_SCENARIOS if s['name'] == 'High_IV_Environment')
        standard_scenario = next(s for s in TEST_SCENARIOS if s['name'] == 'ATM_Call_Standard')
        
        high_iv_size = self.test_position_sizing_logic(high_iv_scenario)['final_size']
        standard_size = self.test_position_sizing_logic(standard_scenario)['final_size']
        
        iv_consistency = high_iv_size < standard_size
        print(f"IV Rank Consistency: {iv_consistency} (High IV: {high_iv_size:.2f} < Standard: {standard_size:.2f})")
        
        # Test 3: Expected move calculations should scale with volatility
        moves = []
        for scenario in TEST_SCENARIOS:
            move_test = self.calculate_expected_move_accuracy(scenario)
            moves.append((scenario['iv'], move_test['expected_move']['percentage']))
        
        # Check if moves increase with volatility
        moves.sort(key=lambda x: x[0])  # Sort by IV
        volatility_consistency = all(moves[i][1] <= moves[i+1][1] for i in range(len(moves)-1))
        print(f"Volatility Scaling Consistency: {volatility_consistency}")
        
        assessment_results['mathematical_consistency'] = {
            'position_size_risk_relationship': size_consistency,
            'iv_rank_impact': iv_consistency,
            'volatility_scaling': volatility_consistency,
            'overall_consistency': size_consistency and iv_consistency and volatility_consistency
        }
        
        # Edge Case Testing
        print(f"\n[EDGE CASE TESTING]")
        
        edge_cases = [
            {'name': 'Zero_IV', 'iv': 0.001, 'expected_behavior': 'minimal_move'},
            {'name': 'Extreme_IV', 'iv': 2.0, 'expected_behavior': 'massive_move'},
            {'name': 'Zero_Time', 'expiration': '2025-06-26', 'expected_behavior': 'intrinsic_only'},
            {'name': 'Deep_ITM', 'strike': 50.0, 'underlying': 100.0, 'expected_behavior': 'stock_equivalent'}
        ]
        
        edge_results = {}
        for edge in edge_cases:
            try:
                # Create test scenario
                test_scenario = {
                    'underlying': edge.get('underlying', 100.0),
                    'strike': edge.get('strike', 100.0),
                    'iv': edge.get('iv', 0.20),
                    'iv_rank': 50.0,
                    'expected_delta': 0.5
                }
                
                result = self.test_position_sizing_logic(test_scenario)
                edge_results[edge['name']] = {
                    'handled': True,
                    'result': result['final_size'],
                    'expected': edge['expected_behavior']
                }
                print(f"{edge['name']}: Handled OK (Size: {result['final_size']:.2f})")
                
            except Exception as e:
                edge_results[edge['name']] = {
                    'handled': False,
                    'error': str(e),
                    'expected': edge['expected_behavior']
                }
                print(f"{edge['name']}: Error FAIL ({str(e)})")
        
        assessment_results['edge_case_handling'] = edge_results
        
        # Final Assessment Summary
        print(f"\n{'='*80}")
        print("ASSESSMENT SUMMARY")
        print(f"{'='*80}")
        
        consistency_score = sum(assessment_results['mathematical_consistency'].values()) / len(assessment_results['mathematical_consistency'])
        edge_case_score = sum(1 for e in edge_results.values() if e['handled']) / len(edge_results)
        
        overall_score = (consistency_score + edge_case_score) / 2
        
        print(f"Mathematical Consistency: {consistency_score:.1%}")
        print(f"Edge Case Handling: {edge_case_score:.1%}")
        print(f"Overall Mathematical Score: {overall_score:.1%}")
        
        if overall_score >= 0.9:
            grade = "EXCELLENT"
        elif overall_score >= 0.8:
            grade = "GOOD"
        elif overall_score >= 0.7:
            grade = "ACCEPTABLE"
        else:
            grade = "NEEDS_IMPROVEMENT"
        
        print(f"Final Grade: {grade}")
        
        assessment_results['final_score'] = {
            'consistency_score': consistency_score,
            'edge_case_score': edge_case_score,
            'overall_score': overall_score,
            'grade': grade
        }
        
        return assessment_results

if __name__ == "__main__":
    assessor = MathematicalAssessment()
    results = assessor.run_comprehensive_assessment()
