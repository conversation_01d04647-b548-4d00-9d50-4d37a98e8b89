#!/usr/bin/env python3
"""
EXTERNAL API CONTAMINATION VERIFICATION SCRIPT
Surgical validation that all external API dependencies are removed
Mathematical rigor: 100% verification of cleanup
"""

import os
import re
import json
import time
from pathlib import Path
from typing import Dict, List, Set

class ExternalAPIVerifier:
    """Verify complete removal of external API dependencies"""
    
    def __init__(self):
        self.core_path = Path("D:/script-work/CORE")
        self.external_apis = {
            "polygon": [
                "polygon.io", "POLYGON_API_KEY", "polygon-api-client",
                "api.polygon", "RESTClient", "_pull_polygon"
            ],
            "alpha_vantage": [
                "alpha-vantage", "ALPHA_VANTAGE_API_KEY", "alphavantage",
                "alpha_vantage", "AlphaVantage"
            ],
            "yahoo_finance": [
                "yfinance", "yahoo", "yf.", "yahoofinancials"
            ],
            "finnhub": [
                "finnhub", "FINNHUB_API_KEY"
            ],
            "iex": [
                "iexfinance", "IEX_TOKEN"
            ]
        }
        self.findings = {
            "contaminated_files": [],
            "timeout_issues": [],
            "verification_results": {},
            "summary": {}
        }
    
    def scan_for_external_apis(self) -> Dict[str, List[str]]:
        """Scan all Python files for external API references"""
        print("=" * 60)
        print("SCANNING FOR EXTERNAL API CONTAMINATION")
        print("=" * 60)
        
        contaminated_files = {}
        
        for root, dirs, files in os.walk(self.core_path):
            # Skip cache and archive directories
            dirs[:] = [d for d in dirs if not d.startswith('.') and 'archive' not in d.lower()]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, self.core_path)
                    
                    contamination = self._check_file_contamination(file_path)
                    if contamination:
                        contaminated_files[relative_path] = contamination
                        print(f"CONTAMINATED: {relative_path}")
                        for issue in contamination:
                            print(f"  - {issue}")
        
        if not contaminated_files:
            print("✓ NO EXTERNAL API CONTAMINATION FOUND")
        else:
            print(f"\n⚠ CONTAMINATION DETECTED: {len(contaminated_files)} files")
        
        self.findings["contaminated_files"] = contaminated_files
        return contaminated_files
    
    def _check_file_contamination(self, file_path: str) -> List[str]:
        """Check individual file for external API references"""
        contamination = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for each external API pattern
            for api_name, patterns in self.external_apis.items():
                for pattern in patterns:
                    if pattern.lower() in content.lower():
                        contamination.append(f"{api_name}: {pattern}")
            
        except Exception as e:
            contamination.append(f"READ_ERROR: {str(e)}")
        
        return contamination
    
    def scan_timeout_configurations(self) -> Dict[str, List[str]]:
        """Scan for aggressive timeout configurations"""
        print("\n" + "=" * 60)
        print("SCANNING FOR TIMEOUT ISSUES")
        print("=" * 60)
        
        timeout_issues = {}
        timeout_pattern = re.compile(r'timeout\s*=\s*([1-5])[^0-9]')
        
        for root, dirs, files in os.walk(self.core_path):
            dirs[:] = [d for d in dirs if not d.startswith('.') and 'archive' not in d.lower()]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, self.core_path)
                    
                    issues = self._check_timeout_issues(file_path, timeout_pattern)
                    if issues:
                        timeout_issues[relative_path] = issues
                        print(f"TIMEOUT ISSUE: {relative_path}")
                        for issue in issues:
                            print(f"  - {issue}")
        
        if not timeout_issues:
            print("✓ NO AGGRESSIVE TIMEOUT CONFIGURATIONS FOUND")
        else:
            print(f"\n⚠ TIMEOUT ISSUES: {len(timeout_issues)} files")
        
        self.findings["timeout_issues"] = timeout_issues
        return timeout_issues
    
    def _check_timeout_issues(self, file_path: str, pattern: re.Pattern) -> List[str]:
        """Check file for aggressive timeout configurations"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                matches = pattern.findall(line)
                for match in matches:
                    if "schwab" in line.lower() or "mcp" in line.lower():
                        issues.append(f"Line {line_num}: timeout={match} (too aggressive for Schwab MCP)")
        
        except Exception as e:
            issues.append(f"READ_ERROR: {str(e)}")
        
        return issues
    
    def verify_schwab_mcp_only(self) -> Dict[str, bool]:
        """Verify agents use only Schwab MCP endpoints"""
        print("\n" + "=" * 60)
        print("VERIFYING SCHWAB MCP ONLY CONFIGURATION")
        print("=" * 60)
        
        verification_results = {}
        
        # Key files to verify
        key_files = [
            "agents/data_ingestion_agent.py",
            "enhanced_data_agent_broker_integration.py",
            "requirements.txt"
        ]
        
        for file_path in key_files:
            full_path = self.core_path / file_path
            if full_path.exists():
                result = self._verify_file_schwab_only(full_path)
                verification_results[file_path] = result
                status = "✓ CLEAN" if result else "⚠ CONTAMINATED"
                print(f"{status}: {file_path}")
            else:
                verification_results[file_path] = False
                print(f"✗ MISSING: {file_path}")
        
        self.findings["verification_results"] = verification_results
        return verification_results
    
    def _verify_file_schwab_only(self, file_path: Path) -> bool:
        """Verify individual file uses only Schwab MCP"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for external API patterns
            external_patterns = [
                "polygon.io", "alpha-vantage", "yfinance", "finnhub",
                "POLYGON_API_KEY", "_pull_polygon", "polygon_mcp_url"
            ]
            
            for pattern in external_patterns:
                if pattern in content:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def generate_summary_report(self) -> Dict[str, any]:
        """Generate mathematical summary of verification results"""
        print("\n" + "=" * 60)
        print("VERIFICATION SUMMARY REPORT")
        print("=" * 60)
        
        # Calculate contamination metrics
        total_contaminated = len(self.findings["contaminated_files"])
        total_timeout_issues = len(self.findings["timeout_issues"])
        total_verified = len(self.findings["verification_results"])
        clean_files = sum(1 for result in self.findings["verification_results"].values() if result)
        
        # Mathematical assessment
        contamination_rate = total_contaminated / max(total_verified, 1)
        cleanup_success_rate = clean_files / max(total_verified, 1)
        
        summary = {
            "contaminated_files": total_contaminated,
            "timeout_issues": total_timeout_issues,
            "verified_files": total_verified,
            "clean_files": clean_files,
            "contamination_rate": round(contamination_rate * 100, 2),
            "cleanup_success_rate": round(cleanup_success_rate * 100, 2),
            "system_status": "CLEAN" if contamination_rate == 0 else "CONTAMINATED"
        }
        
        print(f"CONTAMINATED FILES: {total_contaminated}")
        print(f"TIMEOUT ISSUES: {total_timeout_issues}")
        print(f"CLEANUP SUCCESS RATE: {summary['cleanup_success_rate']}%")
        print(f"SYSTEM STATUS: {summary['system_status']}")
        
        if summary['system_status'] == "CLEAN":
            print("\n✓ SURGICAL CLEANUP SUCCESSFUL")
            print("✓ ALL EXTERNAL API DEPENDENCIES REMOVED")
            print("✓ SCHWAB MCP ONLY CONFIGURATION VERIFIED")
        else:
            print(f"\n⚠ CLEANUP INCOMPLETE - {summary['contamination_rate']}% contamination remains")
        
        self.findings["summary"] = summary
        return summary
    
    def run_complete_verification(self) -> Dict[str, any]:
        """Execute complete verification process"""
        print("EXTERNAL API CONTAMINATION VERIFICATION ENGINE")
        print("Surgical precision validation tool")
        print(f"Analysis time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            self.scan_for_external_apis()
            self.scan_timeout_configurations()
            self.verify_schwab_mcp_only()
            self.generate_summary_report()
            
            # Save verification results
            output_file = self.core_path / f"external_api_verification_{int(time.time())}.json"
            with open(output_file, 'w') as f:
                json.dump(self.findings, f, indent=2, default=str)
            
            print(f"\nVerification results saved to: {output_file}")
            return self.findings
            
        except Exception as e:
            print(f"VERIFICATION ERROR: {e}")
            return None

if __name__ == "__main__":
    verifier = ExternalAPIVerifier()
    results = verifier.run_complete_verification()
