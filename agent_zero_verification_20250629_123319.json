{"pipeline_execution": {"status": "SUCCESS", "timestamp": "2025-06-29T12:33:19.523219", "components": ["DATA_INIT", "B-Series", "A-01", "C-02", "F-02", "SPECIALIZED_ARMY", "EXTENDED_ANALYSIS"]}, "agent_zero_data": {"agent_zero_available": true, "ensemble_available": true, "market_context_available": false, "extended_analysis_available": true, "data_sources_info": {"total_sources": 14, "source_list": ["b_series", "anomalies", "csid", "iv_dynamics", "flow_physics", "accumulation", "breakout", "options_flow", "fvg", "mean_reversion", "pivot_points", "signal_convergence", "math_validation", "signal_quality"], "expected_sources": 26, "coverage_percentage": 53.84615384615385}, "extended_analysis_components": ["fvg_analysis", "mean_reversion_analysis", "pivot_point_analysis", "signal_convergence", "math_validation", "signal_quality"]}, "completeness_analysis": {"core_pipeline": {"expected": 5, "present": 5, "percentage": 100.0, "missing": []}, "specialized_army": {"expected": 3, "present": 0, "percentage": 0.0, "missing": ["accumulation_distribution", "breakout_validation", "options_flow"]}, "extended_analysis": {"expected": 6, "present": 6, "percentage": 100.0, "missing": []}, "overall_score": 78.57142857142857}, "quality_analysis": {"agent_zero_decision_quality": {"has_action": true, "has_confidence": true, "has_reasoning": true, "confidence_value": 0.5883, "decision_method": "unknown", "ml_available": false}, "ensemble_intelligence_quality": {"has_final_decision": true, "has_confidence": true, "has_ensemble_score": true, "confidence_value": 100.0, "ensemble_score": 50.0, "intelligence_sources_count": 14}, "data_consistency": {}, "error_analysis": {"total_errors": 3, "error_details": [{"component": "f02_flow_physics", "error": "flow_phys\\2025-06-29\\SPY_flowphysics.json"}, {"component": "options_intelligence", "error": "AgentTask.__init__() got an unexpected keyword argument 'agent_id'"}, {"component": "agent_zero_charts", "error": "ChartGeneratorAgent.__init__() missing 1 required positional argument: 'agent_id'"}]}}, "recommendations": ["Data completeness is 78.6% - investigate missing components", "Agent Zero is not receiving extended analysis data - update market context", "Agent Zero coverage is 0.0% - integrate more agents", "Found 3 errors - investigate component failures"], "verification_timestamp": "2025-06-29T12:33:19.524080"}