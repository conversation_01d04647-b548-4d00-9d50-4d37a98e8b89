#!/usr/bin/env python3
"""
PIPELINE VALIDATION TEST
Test complete system after surgical fixes
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

def test_complete_pipeline():
    """Test the complete pipeline with surgical fixes"""
    print("=" * 80)
    print("PIPELINE VALIDATION TEST - POST SURGICAL FIXES")
    print("=" * 80)
    
    try:
        from ultimate_orchestrator import ultimate_trading_pipeline
        
        print("\nTesting complete pipeline with SPY...")
        result = ultimate_trading_pipeline("SPY")
        
        print("\n" + "=" * 80)
        print("SUCCESS: COMPLETE PIPELINE EXECUTED")
        print("=" * 80)
        
        # Validate key components
        components = [
            "data_initialization",
            "b_series", 
            "a01_anomalies",
            "f01_csid",
            "c02_iv_dynamics", 
            "f02_flow_physics",
            "specialized_army",
            "options_intelligence",
            "agent_zero_intelligence"
        ]
        
        print("\nCOMPONENT VALIDATION:")
        for component in components:
            status = "✅ SUCCESS" if component in result else "❌ MISSING"
            print(f"   {component}: {status}")
        
        # Mathematical precision assessment
        successful_components = sum(1 for comp in components if comp in result)
        precision_score = (successful_components / len(components)) * 100
        
        print(f"\nMATHEMATICAL PRECISION SCORE: {precision_score:.1f}%")
        
        if precision_score >= 90:
            print("✅ MATHEMATICAL PRECISION: EXCELLENT (≥90%)")
        elif precision_score >= 75:
            print("⚠️  MATHEMATICAL PRECISION: GOOD (≥75%)")
        else:
            print("❌ MATHEMATICAL PRECISION: NEEDS IMPROVEMENT (<75%)")
        
        # Options Intelligence specific validation
        options_intel = result.get('options_intelligence', {})
        if options_intel:
            print(f"\nOPTIONS INTELLIGENCE STATUS:")
            print(f"   Status: {options_intel.get('status', 'UNKNOWN')}")
            print(f"   ROI Validation: {options_intel.get('roi_validation', False)}")
            print(f"   Learning Active: {'learning_metrics' in options_intel}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ PIPELINE TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_pipeline()
    if success:
        print("\n🎯 SURGICAL FIXES: SUCCESSFUL")
        print("System ready for 100% mathematical precision operation")
    else:
        print("\n⚠️  SURGICAL FIXES: ADDITIONAL WORK NEEDED")
