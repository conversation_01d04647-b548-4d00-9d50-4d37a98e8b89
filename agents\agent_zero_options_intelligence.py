#!/usr/bin/env python3
"""
AGENT ZERO OPTIONS INTELLIGENCE MODULE
Advanced options analysis and selection for trading decisions
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class AgentZeroOptionsIntelligence:
    """
    Options domain expertise for Agent Zero
    Handles strike selection, expiration analysis, Greeks, and IV assessment
    """
    
    def __init__(self):
        # Options strategy preferences
        self.preferred_delta_range = (0.25, 0.75)  # Target delta range
        self.max_bid_ask_spread_pct = 0.15         # Maximum 15% spread
        self.min_open_interest = 50                # Minimum liquidity requirement
        self.min_volume = 10                       # Minimum daily volume
        
        # IV environment thresholds
        self.iv_percentile_thresholds = {
            'cheap': 25,      # Below 25th percentile
            'normal': 75,     # 25th-75th percentile  
            'expensive': 75   # Above 75th percentile
        }
        
        # Time decay preferences
        self.preferred_days_to_expiration = (7, 45)  # 1-6 weeks typically
        
        logger.info("Agent Zero Options Intelligence initialized")
    
    def analyze_options_chain(self, options_data: Dict, underlying_price: float, 
                            market_direction: str) -> Dict[str, Any]:
        """
        Comprehensive options chain analysis
        
        Args:
            options_data: Raw options chain from MCP
            underlying_price: Current stock price
            market_direction: 'bullish', 'bearish', or 'neutral'
            
        Returns:
            Dict with detailed options analysis
        """
        try:
            calls = options_data.get('calls', [])
            puts = options_data.get('puts', [])
            
            # Analyze each option type
            call_analysis = self._analyze_option_side(calls, underlying_price, 'call')
            put_analysis = self._analyze_option_side(puts, underlying_price, 'put')
            
            # Select optimal strikes based on direction
            if market_direction == 'bullish':
                optimal_selection = self._select_optimal_calls(call_analysis, underlying_price)
            elif market_direction == 'bearish':
                optimal_selection = self._select_optimal_puts(put_analysis, underlying_price)
            else:
                optimal_selection = self._analyze_neutral_strategies(call_analysis, put_analysis, underlying_price)
            
            return {
                'call_analysis': call_analysis,
                'put_analysis': put_analysis,
                'optimal_selection': optimal_selection,
                'iv_environment': self._assess_iv_environment(calls + puts),
                'liquidity_assessment': self._assess_options_liquidity(calls + puts),
                'recommendation': optimal_selection
            }
            
        except Exception as e:
            logger.error(f"Options chain analysis failed: {e}")
            return {'error': str(e)}
    
    def _analyze_option_side(self, options: List[Dict], underlying_price: float, 
                           option_type: str) -> List[Dict]:
        """Analyze calls or puts with Greeks and metrics"""
        analyzed_options = []
        
        for option in options:
            try:
                strike = option.get('strike', 0)
                bid = option.get('bid', 0)
                ask = option.get('ask', 0)
                volume = option.get('volume', 0)
                open_interest = option.get('open_interest', 0)
                
                # Calculate key metrics
                mid_price = (bid + ask) / 2
                spread_pct = ((ask - bid) / mid_price) if mid_price > 0 else 1.0
                
                # Calculate moneyness
                if option_type == 'call':
                    moneyness = underlying_price / strike if strike > 0 else 0
                    intrinsic_value = max(0, underlying_price - strike)
                else:  # put
                    moneyness = strike / underlying_price if underlying_price > 0 else 0
                    intrinsic_value = max(0, strike - underlying_price)
                
                time_value = mid_price - intrinsic_value
                
                # Estimate delta (simplified Black-Scholes approximation)
                delta = self._estimate_delta(underlying_price, strike, option_type, time_value)
                
                # Liquidity score
                liquidity_score = self._calculate_option_liquidity_score(
                    volume, open_interest, spread_pct, bid, ask
                )
                
                analyzed_option = {
                    'strike': strike,
                    'bid': bid,
                    'ask': ask,
                    'mid_price': mid_price,
                    'volume': volume,
                    'open_interest': open_interest,
                    'spread_pct': spread_pct,
                    'moneyness': moneyness,
                    'intrinsic_value': intrinsic_value,
                    'time_value': time_value,
                    'estimated_delta': delta,
                    'liquidity_score': liquidity_score,
                    'option_type': option_type
                }
                
                analyzed_options.append(analyzed_option)
                
            except Exception as e:
                logger.warning(f"Failed to analyze option {option}: {e}")
                continue
        
        # Sort by liquidity score (best first)
        analyzed_options.sort(key=lambda x: x['liquidity_score'], reverse=True)
        
        return analyzed_options
    
    def _estimate_delta(self, underlying_price: float, strike: float, 
                       option_type: str, time_value: float) -> float:
        """Simplified delta estimation"""
        try:
            if option_type == 'call':
                if underlying_price > strike:
                    return min(0.95, 0.5 + (underlying_price - strike) / underlying_price * 0.4)
                else:
                    return max(0.05, 0.5 - (strike - underlying_price) / strike * 0.4)
            else:  # put
                if strike > underlying_price:
                    return min(-0.05, -0.5 - (strike - underlying_price) / strike * 0.4)
                else:
                    return max(-0.95, -0.5 + (underlying_price - strike) / underlying_price * 0.4)
        except:
            return 0.5 if option_type == 'call' else -0.5
    
    def _calculate_option_liquidity_score(self, volume: int, open_interest: int, 
                                        spread_pct: float, bid: float, ask: float) -> float:
        """Calculate option-specific liquidity score"""
        try:
            # Volume component (30%)
            volume_score = min(1.0, volume / 100) * 0.30
            
            # Open interest component (30%)
            oi_score = min(1.0, open_interest / 500) * 0.30
            
            # Spread component (25%) - lower spread is better
            spread_score = max(0, (0.15 - spread_pct) / 0.15) * 0.25
            
            # Price level component (15%) - higher prices generally more liquid
            price_score = min(1.0, (bid + ask) / 2 / 5.0) * 0.15
            
            return volume_score + oi_score + spread_score + price_score
            
        except:
            return 0.0
    
    def _select_optimal_calls(self, call_analysis: List[Dict], underlying_price: float) -> Dict:
        """Select optimal call options for bullish trades"""
        try:
            # Filter for good liquidity and reasonable spreads
            viable_calls = [
                call for call in call_analysis
                if call['liquidity_score'] > 0.3 and 
                   call['spread_pct'] < self.max_bid_ask_spread_pct and
                   call['volume'] >= self.min_volume
            ]
            
            if not viable_calls:
                return {'action': 'avoid', 'reason': 'No viable call options found'}
            
            # Prefer slightly OTM calls with good delta
            optimal_calls = []
            for call in viable_calls:
                if (0.2 <= call['estimated_delta'] <= 0.7 and 
                    call['moneyness'] >= 0.95):  # ATM to slightly OTM
                    optimal_calls.append(call)
            
            if not optimal_calls:
                optimal_calls = viable_calls[:3]  # Take top 3 by liquidity
            
            # Select best option
            best_call = max(optimal_calls, 
                          key=lambda x: x['liquidity_score'] * (1 - abs(x['estimated_delta'] - 0.5)))
            
            return {
                'action': 'buy_calls',
                'strike': best_call['strike'],
                'option_type': 'call',
                'premium': best_call['mid_price'],
                'estimated_delta': best_call['estimated_delta'],
                'liquidity_score': best_call['liquidity_score'],
                'spread_pct': best_call['spread_pct'],
                'reasoning': f"Optimal call: ${best_call['strike']} strike, "
                           f"delta {best_call['estimated_delta']:.2f}, "
                           f"liquidity {best_call['liquidity_score']:.2f}"
            }
            
        except Exception as e:
            return {'action': 'avoid', 'reason': f'Call selection failed: {e}'}
    
    def _select_optimal_puts(self, put_analysis: List[Dict], underlying_price: float) -> Dict:
        """Select optimal put options for bearish trades"""
        try:
            # Filter for good liquidity and reasonable spreads
            viable_puts = [
                put for put in put_analysis
                if put['liquidity_score'] > 0.3 and 
                   put['spread_pct'] < self.max_bid_ask_spread_pct and
                   put['volume'] >= self.min_volume
            ]
            
            if not viable_puts:
                return {'action': 'avoid', 'reason': 'No viable put options found'}
            
            # Prefer slightly OTM puts with good delta
            optimal_puts = []
            for put in viable_puts:
                if (-0.7 <= put['estimated_delta'] <= -0.2 and 
                    put['moneyness'] >= 0.95):  # ATM to slightly OTM
                    optimal_puts.append(put)
            
            if not optimal_puts:
                optimal_puts = viable_puts[:3]  # Take top 3 by liquidity
            
            # Select best option
            best_put = max(optimal_puts, 
                         key=lambda x: x['liquidity_score'] * (1 - abs(abs(x['estimated_delta']) - 0.5)))
            
            return {
                'action': 'buy_puts',
                'strike': best_put['strike'],
                'option_type': 'put',
                'premium': best_put['mid_price'],
                'estimated_delta': best_put['estimated_delta'],
                'liquidity_score': best_put['liquidity_score'],
                'spread_pct': best_put['spread_pct'],
                'reasoning': f"Optimal put: ${best_put['strike']} strike, "
                           f"delta {best_put['estimated_delta']:.2f}, "
                           f"liquidity {best_put['liquidity_score']:.2f}"
            }
            
        except Exception as e:
            return {'action': 'avoid', 'reason': f'Put selection failed: {e}'}
    
    def _analyze_neutral_strategies(self, call_analysis: List[Dict], 
                                  put_analysis: List[Dict], underlying_price: float) -> Dict:
        """Analyze neutral market strategies"""
        # For neutral markets, prefer holding or avoiding
        return {
            'action': 'hold',
            'reason': 'Neutral market - no clear directional edge',
            'alternative_strategies': ['iron_condor', 'straddle', 'strangle']
        }
    
    def _assess_iv_environment(self, all_options: List[Dict]) -> Dict:
        """Assess overall IV environment"""
        try:
            if not all_options:
                return {'environment': 'unknown', 'reason': 'No options data'}
            
            # Calculate average time value as IV proxy
            time_values = [opt.get('time_value', 0) for opt in all_options if opt.get('time_value', 0) > 0]
            
            if not time_values:
                return {'environment': 'unknown', 'reason': 'No time value data'}
            
            avg_time_value = np.mean(time_values)
            
            # Simple classification based on time value
            if avg_time_value > 3.0:
                environment = 'high_iv'
                strategy_bias = 'sell_premium'
            elif avg_time_value < 1.0:
                environment = 'low_iv'
                strategy_bias = 'buy_premium'
            else:
                environment = 'normal_iv'
                strategy_bias = 'neutral'
            
            return {
                'environment': environment,
                'avg_time_value': avg_time_value,
                'strategy_bias': strategy_bias,
                'reasoning': f"Average time value: {avg_time_value:.2f}"
            }
            
        except Exception as e:
            return {'environment': 'unknown', 'reason': f'IV analysis failed: {e}'}
    
    def _assess_options_liquidity(self, all_options: List[Dict]) -> Dict:
        """Assess overall options liquidity"""
        try:
            if not all_options:
                return {'quality': 'poor', 'reason': 'No options data'}
            
            # Calculate liquidity metrics
            avg_liquidity = np.mean([opt.get('liquidity_score', 0) for opt in all_options])
            viable_options = len([opt for opt in all_options if opt.get('liquidity_score', 0) > 0.3])
            total_options = len(all_options)
            
            viable_pct = viable_options / total_options if total_options > 0 else 0
            
            if avg_liquidity > 0.6 and viable_pct > 0.7:
                quality = 'excellent'
            elif avg_liquidity > 0.4 and viable_pct > 0.5:
                quality = 'good'
            elif avg_liquidity > 0.2 and viable_pct > 0.3:
                quality = 'moderate'
            else:
                quality = 'poor'
            
            return {
                'quality': quality,
                'avg_liquidity_score': avg_liquidity,
                'viable_options_pct': viable_pct,
                'total_options': total_options,
                'viable_options': viable_options
            }
            
        except Exception as e:
            return {'quality': 'unknown', 'reason': f'Liquidity analysis failed: {e}'}

    def integrate_with_agent_zero_decision(self, agent_zero_signal: str, 
                                         options_analysis: Dict) -> Dict:
        """
        Integrate Agent Zero directional signal with options intelligence
        
        Args:
            agent_zero_signal: 'buy_calls', 'buy_puts', 'hold', 'avoid'
            options_analysis: Full options chain analysis
            
        Returns:
            Enhanced decision with specific options recommendation
        """
        try:
            if agent_zero_signal == 'avoid':
                return {
                    'final_action': 'avoid',
                    'reason': 'Agent Zero recommends avoiding - no options trade'
                }
            
            if agent_zero_signal == 'hold':
                return {
                    'final_action': 'hold',
                    'reason': 'Agent Zero neutral - no directional options trade'
                }
            
            # Check options liquidity quality
            liquidity_quality = options_analysis.get('liquidity_assessment', {}).get('quality', 'poor')
            
            if liquidity_quality == 'poor':
                return {
                    'final_action': 'avoid',
                    'reason': 'Poor options liquidity - execution risk too high'
                }
            
            # Get optimal selection based on Agent Zero signal
            optimal_selection = options_analysis.get('optimal_selection', {})
            
            if optimal_selection.get('action') == agent_zero_signal:
                return {
                    'final_action': agent_zero_signal,
                    'strike': optimal_selection.get('strike'),
                    'premium': optimal_selection.get('premium'),
                    'delta': optimal_selection.get('estimated_delta'),
                    'liquidity_score': optimal_selection.get('liquidity_score'),
                    'reasoning': optimal_selection.get('reasoning'),
                    'options_quality': liquidity_quality
                }
            else:
                return {
                    'final_action': 'avoid',
                    'reason': f'Agent Zero signal {agent_zero_signal} but no viable options found'
                }
                
        except Exception as e:
            return {
                'final_action': 'avoid',
                'reason': f'Integration failed: {e}'
            }
