# Output Coordination Workflow

## 1. Input validation
Confirm three upstream artifacts exist: signal_result.json, mathematical_validation.json, and chart_data_paths list.

## 2. Timestamp & metadata merge
Produce `run_id` using UUID4, generate `session_hash` from input combination for correlation tracking.

## 3. Risk metrics calc
Simple Kelly-style 1% account cap with options ROI calculations, IV analysis, and Greeks-based position sizing for options strategies.

## 4. Execution-plan template
Generate market vs limit order recommendations with stop-loss and target levels based on signal strength.

## 5. Training-log emit
Call `TrainingMixin.log_training` with coordination decisions and output quality metrics for Agent Zero learning.

## 6. Quality gates
JSONSchema validation pass for all outputs, PNG file existence verification, and runtime performance under 3000ms threshold.
