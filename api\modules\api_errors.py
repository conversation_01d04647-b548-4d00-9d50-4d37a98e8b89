#!/usr/bin/env python
"""
Standardized API Error Response Handling

This module provides unified error response classes and handling mechanisms
for all API interactions in the Liquidity Reports system. It defines:

1. StandardApiResponse base class
2. Specialized error response classes
3. Error handling utilities

These classes ensure consistent error handling across different data sources
and APIs, making error handling more robust and easier to maintain.
"""

import logging
import time
import traceback
import json
from datetime import datetime
from typing import Dict, Any, Optional, List, Union, Tuple
import pandas as pd
import numpy as np

# Set up logger
logger = logging.getLogger("Liquidity_Reports.api_errors")

class StandardApiResponse:
    """
    Base class for standardized API responses.
    
    This class provides a foundation for all API responses, whether success
    or error, ensuring a consistent structure for error handling.
    """
    
    def __init__(self, 
                 status: str = "success", 
                 message: Optional[str] = None,
                 data: Any = None,
                 source: str = "unknown",
                 error_code: Optional[str] = None,
                 request_info: Optional[Dict[str, Any]] = None):
        """
        Initialize a standard API response.
        
        Args:
            status (str): Response status, either "success", "error", or "partial"
            message (str, optional): Human-readable message
            data (Any, optional): Response data
            source (str, optional): Source of the response (API name)
            error_code (str, optional): Specific error code if applicable
            request_info (dict, optional): Information about the request
        """
        self.status = status
        self.message = message
        self.data = data
        self.source = source
        self.error_code = error_code
        self.request_info = request_info or {}
        self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the response to a dictionary.
        
        Returns:
            dict: Dictionary representation of the response
        """
        result = {
            "status": self.status,
            "source": self.source,
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat()
        }
        
        if self.message:
            result["message"] = self.message
        
        if self.data is not None:
            result["data"] = self.data
        
        if self.error_code:
            result["error_code"] = self.error_code
        
        if self.request_info:
            result["request_info"] = self.request_info
        
        return result
    
    def to_json(self) -> str:
        """
        Convert the response to a JSON string.
        
        Returns:
            str: JSON representation of the response
        """
        # Handle non-serializable data
        def json_serializer(obj):
            if isinstance(obj, (datetime, pd.Timestamp)):
                return obj.isoformat()
            if isinstance(obj, pd.DataFrame):
                return "DataFrame(rows={}, columns={})".format(len(obj), list(obj.columns))
            if isinstance(obj, np.ndarray):
                return "Array(shape={}, dtype={})".format(obj.shape, obj.dtype)
            return str(obj)
        
        return json.dumps(self.to_dict(), default=json_serializer, indent=2)
    
    def is_success(self) -> bool:
        """
        Check if the response represents a success.
        
        Returns:
            bool: True if status is "success", False otherwise
        """
        return self.status == "success"
    
    def is_error(self) -> bool:
        """
        Check if the response represents an error.
        
        Returns:
            bool: True if status is "error", False otherwise
        """
        return self.status == "error"
    
    def is_partial(self) -> bool:
        """
        Check if the response represents a partial success.
        
        Returns:
            bool: True if status is "partial", False otherwise
        """
        return self.status == "partial"
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StandardApiResponse':
        """
        Create a response object from a dictionary.
        
        Args:
            data (dict): Dictionary representation of a response
            
        Returns:
            StandardApiResponse: New response object
        """
        return cls(
            status=data.get("status", "success"),
            message=data.get("message"),
            data=data.get("data"),
            source=data.get("source", "unknown"),
            error_code=data.get("error_code"),
            request_info=data.get("request_info", {})
        )
    
    @classmethod
    def success(cls, 
               data: Any = None, 
               message: Optional[str] = None,
               source: str = "api") -> 'StandardApiResponse':
        """
        Create a success response.
        
        Args:
            data (Any, optional): Response data
            message (str, optional): Success message
            source (str, optional): Source of the response
            
        Returns:
            StandardApiResponse: New success response
        """
        return cls(
            status="success",
            message=message,
            data=data,
            source=source
        )
    
    @classmethod
    def error(cls,
             message: str,
             error_code: Optional[str] = None,
             source: str = "api",
             request_info: Optional[Dict[str, Any]] = None) -> 'StandardApiResponse':
        """
        Create an error response.
        
        Args:
            message (str): Error message
            error_code (str, optional): Specific error code
            source (str, optional): Source of the error
            request_info (dict, optional): Information about the request
            
        Returns:
            StandardApiResponse: New error response
        """
        return cls(
            status="error",
            message=message,
            source=source,
            error_code=error_code,
            request_info=request_info
        )
    
    @classmethod
    def partial(cls,
               data: Any = None,
               message: str = "Partial success",
               source: str = "api",
               error_info: Optional[Dict[str, Any]] = None) -> 'StandardApiResponse':
        """
        Create a partial success response.
        
        Args:
            data (Any, optional): Partial response data
            message (str, optional): Partial success message
            source (str, optional): Source of the response
            error_info (dict, optional): Additional error information
            
        Returns:
            StandardApiResponse: New partial success response
        """
        return cls(
            status="partial",
            message=message,
            data=data,
            source=source,
            request_info={"error_info": error_info} if error_info else {}
        )


class ApiConnectionError(StandardApiResponse):
    """
    Response for API connection errors (network issues, timeouts, etc.).
    """
    
    def __init__(self, message: str, source: str, exception: Optional[Exception] = None):
        """
        Initialize a connection error response.
        
        Args:
            message (str): Error message
            source (str): API source name
            exception (Exception, optional): The exception that caused the error
        """
        request_info = {}
        if exception:
            request_info["exception"] = str(exception)
            request_info["exception_type"] = type(exception).__name__
        
        super().__init__(
            status="error",
            message=message,
            source=source,
            error_code="connection_error",
            request_info=request_info
        )


class ApiAuthenticationError(StandardApiResponse):
    """
    Response for API authentication errors (invalid API keys, etc.).
    """
    
    def __init__(self, message: str, source: str, auth_details: Optional[Dict[str, Any]] = None):
        """
        Initialize an authentication error response.
        
        Args:
            message (str): Error message
            source (str): API source name
            auth_details (dict, optional): Details about the authentication issue
                (with sensitive information removed)
        """
        super().__init__(
            status="error",
            message=message,
            source=source,
            error_code="authentication_error",
            request_info=auth_details or {}
        )


class ApiRateLimitError(StandardApiResponse):
    """
    Response for API rate limit errors.
    """
    
    def __init__(self, 
                message: str, 
                source: str, 
                retry_after: Optional[int] = None,
                limit_info: Optional[Dict[str, Any]] = None):
        """
        Initialize a rate limit error response.
        
        Args:
            message (str): Error message
            source (str): API source name
            retry_after (int, optional): Seconds to wait before retrying
            limit_info (dict, optional): Information about rate limits
        """
        request_info = limit_info or {}
        if retry_after:
            request_info["retry_after"] = retry_after
            request_info["retry_at"] = time.time() + retry_after
        
        super().__init__(
            status="error",
            message=message,
            source=source,
            error_code="rate_limit_error",
            request_info=request_info
        )


class ApiResponseError(StandardApiResponse):
    """
    Response for errors in API response parsing.
    """
    
    def __init__(self, 
                message: str, 
                source: str, 
                response_data: Optional[Dict[str, Any]] = None):
        """
        Initialize a response parsing error.
        
        Args:
            message (str): Error message
            source (str): API source name
            response_data (dict, optional): The response data that caused the error
                (sensitive information should be removed)
        """
        super().__init__(
            status="error",
            message=message,
            source=source,
            error_code="response_error",
            request_info={"response_data": response_data} if response_data else {}
        )


class ApiDataNotFoundError(StandardApiResponse):
    """
    Response for when requested data is not found.
    """
    
    def __init__(self, 
                message: str, 
                source: str, 
                query_info: Optional[Dict[str, Any]] = None):
        """
        Initialize a data not found error.
        
        Args:
            message (str): Error message
            source (str): API source name
            query_info (dict, optional): Information about the query
        """
        super().__init__(
            status="error",
            message=message,
            source=source,
            error_code="data_not_found",
            request_info=query_info or {}
        )


class ApiBackoffStrategy:
    """
    Exponential backoff strategy for handling API rate limits and retries.
    """
    
    def __init__(self, 
                initial_delay: float = 1.0, 
                max_delay: float = 60.0,
                factor: float = 2.0,
                jitter: bool = True):
        """
        Initialize backoff strategy.
        
        Args:
            initial_delay (float): Initial delay in seconds
            max_delay (float): Maximum delay in seconds
            factor (float): Multiplication factor for exponential backoff
            jitter (bool): Whether to add randomness to delay
        """
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.factor = factor
        self.jitter = jitter
    
    def get_delay(self, retry_count: int) -> float:
        """
        Calculate delay for a specific retry attempt.
        
        Args:
            retry_count (int): Current retry attempt (0-based)
            
        Returns:
            float: Delay in seconds
        """
        import random
        
        delay = min(self.initial_delay * (self.factor ** retry_count), self.max_delay)
        
        if self.jitter:
            # Add jitter (15%)
            jitter_factor = random.uniform(0.85, 1.15)
            delay = delay * jitter_factor
        
        return delay


def handle_api_response(response: Dict[str, Any], 
                       source: str, 
                       error_mapping: Optional[Dict[str, str]] = None) -> StandardApiResponse:
    """
    Convert a raw API response to a standardized response.
    
    Args:
        response (dict): Raw API response
        source (str): API source name
        error_mapping (dict, optional): Mapping of API-specific error codes
            to standardized error codes
            
    Returns:
        StandardApiResponse: Standardized response object
    """
    if not isinstance(response, dict):
        return ApiResponseError(
            message="Invalid response format (not a dictionary)",
            source=source,
            response_data={"raw_type": str(type(response))}
        )
    
    # Check for successful response
    if response.get("status") == "success":
        return StandardApiResponse.success(
            data=response.get("data") or response,
            message=response.get("message"),
            source=source
        )
    
    # Handle error responses
    if response.get("status") == "error":
        error_code = response.get("error_code") or response.get("code")
        
        # Map to standard error code if mapping is provided
        if error_mapping and error_code in error_mapping:
            error_code = error_mapping[error_code]
        
        # Handle specific error types
        if error_code == "authentication_error" or "invalid_api_key" in response.get("message", "").lower():
            return ApiAuthenticationError(
                message=response.get("message", "Authentication error"),
                source=source
            )
        
        if error_code == "rate_limit_error" or response.get("message", "").lower().startswith("rate limit"):
            return ApiRateLimitError(
                message=response.get("message", "Rate limit exceeded"),
                source=source,
                retry_after=response.get("retry_after")
            )
        
        if error_code == "data_not_found" or response.get("message", "").lower().startswith("not found"):
            return ApiDataNotFoundError(
                message=response.get("message", "Data not found"),
                source=source,
                query_info=response.get("request_info")
            )
        
        # Generic error response
        return StandardApiResponse.error(
            message=response.get("message", "Unknown API error"),
            error_code=error_code,
            source=source,
            request_info=response.get("request_info")
        )
    
    # Handle partial responses
    if response.get("status") == "partial":
        return StandardApiResponse.partial(
            data=response.get("data") or response,
            message=response.get("message", "Partial success"),
            source=source,
            error_info=response.get("error_info") or response.get("error_messages")
        )
    
    # If status is not explicitly set but has error field
    if "error" in response:
        return StandardApiResponse.error(
            message=response.get("error"),
            source=source
        )
    
    # Default to treating as success
    return StandardApiResponse.success(
        data=response,
        source=source
    )


def retry_api_call(max_retries: int = 3, 
                  backoff_strategy: Optional[ApiBackoffStrategy] = None):
    """
    Decorator for retrying API calls with exponential backoff.
    
    Args:
        max_retries (int): Maximum number of retry attempts
        backoff_strategy (ApiBackoffStrategy, optional): 
            Backoff strategy to use. If None, uses default strategy.
            
    Returns:
        Callable: Decorated function
    """
    backoff = backoff_strategy or ApiBackoffStrategy()
    
    def decorator(func):
        def wrapper(*args, **kwargs):
            attempts = 0
            last_error = None
            
            while attempts <= max_retries:
                try:
                    result = func(*args, **kwargs)
                    
                    # If result is a StandardApiResponse, check for rate limit error
                    if isinstance(result, StandardApiResponse) and result.is_error():
                        if result.error_code == "rate_limit_error" and attempts < max_retries:
                            # Get retry delay
                            retry_info = result.request_info or {}
                            retry_delay = retry_info.get("retry_after")
                            
                            if retry_delay is None:
                                retry_delay = backoff.get_delay(attempts)
                            
                            logger.warning(
                                f"Rate limit hit for {getattr(func, "__name__", "mock_function")}. "
                                f"Retrying in {retry_delay:.2f}s (attempt {attempts+1}/{max_retries})"
                            )
                            
                            time.sleep(retry_delay)
                            attempts += 1
                            continue
                    
                    # If result is a dict, check for rate limit in response
                    if isinstance(result, dict) and result.get("status") == "error":
                        error_msg = result.get("message", "").lower()
                        if "rate limit" in error_msg and attempts < max_retries:
                            retry_delay = backoff.get_delay(attempts)
                            
                            logger.warning(
                                f"Rate limit hit for {getattr(func, "__name__", "mock_function")}. "
                                f"Retrying in {retry_delay:.2f}s (attempt {attempts+1}/{max_retries})"
                            )
                            
                            time.sleep(retry_delay)
                            attempts += 1
                            continue
                    
                    # Return result if we reach here
                    return result
                    
                except (
                    ConnectionError, 
                    TimeoutError, 
                    ConnectionResetError,
                    ConnectionAbortedError,
                    ConnectionRefusedError
                ) as e:
                    # Network errors are retryable
                    last_error = e
                    
                    if attempts < max_retries:
                        retry_delay = backoff.get_delay(attempts)
                        logger.warning(
                            f"Network error in {getattr(func, "__name__", "mock_function")}: {str(e)}. "
                            f"Retrying in {retry_delay:.2f}s (attempt {attempts+1}/{max_retries})"
                        )
                        
                        time.sleep(retry_delay)
                        attempts += 1
                    else:
                        # Max retries reached, return error response
                        return ApiConnectionError(
                            message=f"Network error after {max_retries} retries: {str(e)}",
                            source=getattr(args[0], 'source', 'api_client') if args else 'api_client',
                            exception=e
                        )
                
                except Exception as e:
                    # Unexpected errors are not retried
                    logger.error(f"Unexpected error in {getattr(func, "__name__", "mock_function")}: {str(e)}")
                    logger.debug(traceback.format_exc())
                    
                    return ApiResponseError(
                        message=f"Unexpected error: {str(e)}",
                        source=getattr(args[0], 'source', 'api_client') if args else 'api_client',
                        response_data={"exception": str(e), "type": str(type(e))}
                    )
            
            # This should only be reached for network errors if max retries is 0
            if last_error:
                return ApiConnectionError(
                    message=f"Network error: {str(last_error)}",
                    source=getattr(args[0], 'source', 'api_client') if args else 'api_client',
                    exception=last_error
                )
            
            # Fallback error response (should not normally be reached)
            return ApiResponseError(
                message="Maximum retries exceeded with unknown error",
                source=getattr(args[0], 'source', 'api_client') if args else 'api_client'
            )
        
        return wrapper
    
    return decorator


# Example usage
if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Test standardized responses
    success_response = StandardApiResponse.success(
        data={"result": "test_data"},
        message="Operation completed successfully",
        source="test_api"
    )
    
    error_response = ApiConnectionError(
        message="Failed to connect to API",
        source="test_api",
        exception=ConnectionError("Connection timed out")
    )
    
    # Print responses
    print("Success response:")
    print(success_response.to_json())
    
    print("\nError response:")
    print(error_response.to_json())
    
    # Test error handling
    raw_response = {
        "status": "error",
        "message": "Rate limit exceeded",
        "code": "rate_limit",
        "retry_after": 30
    }
    
    handled_response = handle_api_response(raw_response, "test_api")
    print("\nHandled API response:")
    print(handled_response.to_json())
