#!/usr/bin/env python3
"""
Liquidity Profile Chart with Greek Anomalies
Shows WHERE THE MONEY IS - institutional liquidity concentration + anomalies
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

class LiquidityProfileChart:
    """
    Professional liquidity chart showing:
    1. LIQUIDITY PROFILE (where institutional money sits)
    2. Greek Anomalies overlay  
    3. Gamma walls and option flow
    4. Smart money concentration zones
    """
    
    def __init__(self):
        self.colors = {
            'bullish': '#00C851',        # Green
            'bearish': '#FF4444',        # Red  
            'neutral': '#FFBB33',        # Orange
            'background': '#1a1a1a',     # Dark
            'text': '#ffffff',           # White
            'grid': '#333333',           # Dark gray
            'liquidity_high': '#00BCD4', # Cyan - high liquidity
            'liquidity_med': '#4FC3F7',  # Light blue - medium
            'liquidity_low': '#B3E5FC',  # Very light blue - low
            'greek_anomaly': '#E91E63',  # Pink - anomalies
            'institutional': '#9C27B0',  # Purple - smart money
            'gamma_wall': '#FF9800',     # Orange - gamma
            'support': '#4CAF50',        # Green - support
            'resistance': '#F44336'      # Red - resistance
        }
    
    def generate_liquidity_profile_data(self, ticker: str) -> Dict[str, Any]:
        """Generate realistic liquidity profile data"""
        # Current price simulation
        current_price = 450.0 if ticker == 'SPY' else 180.0
        
        # Price levels (every $0.50 for granularity)
        price_levels = np.arange(current_price - 20, current_price + 20, 0.5)
        
        # LIQUIDITY CONCENTRATION (not volume)
        # Higher values = more institutional money sitting at that level
        liquidity_profile = []
        
        for price in price_levels:
            distance_from_current = abs(price - current_price)
            
            # Base liquidity (decreases with distance)
            base_liquidity = max(0, 100 - distance_from_current * 3)
            
            # Add institutional preference zones
            # Major round numbers (every $5)
            if price % 5.0 == 0:
                base_liquidity += 150
            # Half levels (every $2.50)
            elif price % 2.5 == 0:
                base_liquidity += 80
            # Dollar levels
            elif price % 1.0 == 0:
                base_liquidity += 40
            
            # Add some noise but keep institutional patterns
            noise = np.random.normal(0, 20)
            total_liquidity = max(0, base_liquidity + noise)
            
            liquidity_profile.append(total_liquidity)
        
        # GREEK ANOMALIES (overlay on liquidity)
        anomaly_levels = []
        for i, price in enumerate(price_levels):
            # Detect anomalies where liquidity doesn't match expected patterns
            expected_liquidity = max(0, 80 - abs(price - current_price) * 2)
            actual_liquidity = liquidity_profile[i]
            
            # Anomaly if actual liquidity is significantly different
            anomaly_strength = abs(actual_liquidity - expected_liquidity) / max(expected_liquidity, 1)
            
            if anomaly_strength > 1.5:  # Significant deviation
                anomaly_levels.append({
                    'price': price,
                    'strength': min(anomaly_strength, 5.0),  # Cap at 5x
                    'type': 'unusual_liquidity'
                })
        
        # GAMMA WALLS (major option strikes)
        gamma_walls = []
        for strike in [440, 445, 450, 455, 460]:  # Major strikes
            if current_price - 15 <= strike <= current_price + 15:
                gamma_strength = 200 - abs(strike - current_price) * 10
                gamma_walls.append({
                    'price': strike,
                    'strength': max(gamma_strength, 50),
                    'type': 'gamma_wall'
                })
        
        # SUPPORT/RESISTANCE from liquidity
        support_levels = []
        resistance_levels = []
        
        # Find local maxima in liquidity (support/resistance)
        for i in range(2, len(liquidity_profile) - 2):
            if (liquidity_profile[i] > liquidity_profile[i-1] and 
                liquidity_profile[i] > liquidity_profile[i+1] and
                liquidity_profile[i] > 150):  # Significant liquidity
                
                price = price_levels[i]
                if price < current_price:
                    support_levels.append({
                        'price': price,
                        'strength': liquidity_profile[i]
                    })
                else:
                    resistance_levels.append({
                        'price': price,
                        'strength': liquidity_profile[i]
                    })
        
        return {
            'current_price': current_price,
            'price_levels': price_levels,
            'liquidity_profile': liquidity_profile,
            'greek_anomalies': anomaly_levels,
            'gamma_walls': gamma_walls,
            'support_levels': support_levels,
            'resistance_levels': resistance_levels,
            'max_liquidity': max(liquidity_profile)
        }
    
    def create_liquidity_profile_chart(self, ticker: str, analysis_data: Dict[str, Any]) -> str:
        """Create the main liquidity profile chart"""
        
        # Generate liquidity data
        liquidity_data = self.generate_liquidity_profile_data(ticker)
        
        # Create figure
        fig, (ax_main, ax_profile) = plt.subplots(1, 2, figsize=(16, 10), 
                                                 gridspec_kw={'width_ratios': [3, 1]})
        fig.patch.set_facecolor(self.colors['background'])
        
        # MAIN CHART: Price levels with liquidity overlay
        self._plot_main_liquidity_chart(ax_main, liquidity_data, ticker)
        
        # PROFILE CHART: Horizontal liquidity profile
        self._plot_liquidity_profile(ax_profile, liquidity_data)
        
        # Overall styling
        for ax in [ax_main, ax_profile]:
            ax.set_facecolor(self.colors['background'])
            ax.tick_params(colors=self.colors['text'])
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
        
        plt.tight_layout()
        
        # Save chart
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"liquidity_profile_{ticker}_{timestamp}.png"
        output_dir = Path("visual_output")
        output_dir.mkdir(exist_ok=True)
        filepath = output_dir / filename
        
        plt.savefig(filepath, dpi=150, facecolor=self.colors['background'],
                   bbox_inches='tight')
        plt.close()
        
        return str(filepath)
    
    def _plot_main_liquidity_chart(self, ax, liquidity_data: Dict, ticker: str):
        """Plot main price chart with liquidity zones"""
        
        price_levels = liquidity_data['price_levels']
        liquidity_profile = liquidity_data['liquidity_profile']
        current_price = liquidity_data['current_price']
        max_liquidity = liquidity_data['max_liquidity']
        
        # LIQUIDITY HEATMAP (horizontal bands)
        for i, (price, liquidity) in enumerate(zip(price_levels, liquidity_profile)):
            # Normalize liquidity to alpha (transparency)
            alpha = min(liquidity / max_liquidity, 1.0)
            
            # Color based on liquidity intensity
            if liquidity > max_liquidity * 0.7:
                color = self.colors['liquidity_high']
            elif liquidity > max_liquidity * 0.4:
                color = self.colors['liquidity_med'] 
            else:
                color = self.colors['liquidity_low']
            
            # Draw liquidity band
            rect = patches.Rectangle((0, price - 0.25), 10, 0.5, 
                                   facecolor=color, alpha=alpha * 0.7,
                                   edgecolor='none')
            ax.add_patch(rect)
        
        # CURRENT PRICE LINE
        ax.axhline(y=current_price, color=self.colors['text'], 
                  linestyle='-', linewidth=3, label='Current Price', zorder=10)
        
        # SUPPORT LEVELS
        for support in liquidity_data['support_levels']:
            ax.axhline(y=support['price'], color=self.colors['support'], 
                      linestyle='--', linewidth=2, alpha=0.8, zorder=8)
            ax.text(9.5, support['price'], f"Support ${support['price']:.0f}", 
                   color=self.colors['support'], fontweight='bold', 
                   verticalalignment='center', horizontalalignment='right')
        
        # RESISTANCE LEVELS  
        for resistance in liquidity_data['resistance_levels']:
            ax.axhline(y=resistance['price'], color=self.colors['resistance'], 
                      linestyle='--', linewidth=2, alpha=0.8, zorder=8)
            ax.text(9.5, resistance['price'], f"Resistance ${resistance['price']:.0f}", 
                   color=self.colors['resistance'], fontweight='bold',
                   verticalalignment='center', horizontalalignment='right')
        
        # GAMMA WALLS
        for gamma in liquidity_data['gamma_walls']:
            ax.axhline(y=gamma['price'], color=self.colors['gamma_wall'], 
                      linestyle=':', linewidth=3, alpha=0.9, zorder=9)
            ax.text(0.5, gamma['price'], f"Gamma Wall ${gamma['price']:.0f}", 
                   color=self.colors['gamma_wall'], fontweight='bold',
                   verticalalignment='center')
        
        # GREEK ANOMALIES (scatter points)
        for anomaly in liquidity_data['greek_anomalies']:
            size = anomaly['strength'] * 100  # Scale size by strength
            ax.scatter([5], [anomaly['price']], s=size, 
                      color=self.colors['greek_anomaly'], alpha=0.8,
                      marker='X', linewidth=2, zorder=12,
                      label='Greek Anomaly' if anomaly == liquidity_data['greek_anomalies'][0] else "")
        
        # Styling
        ax.set_xlim(0, 10)
        ax.set_ylim(current_price - 15, current_price + 15)
        ax.set_ylabel('Price Level', color=self.colors['text'], fontsize=12)
        ax.set_title(f'{ticker} - LIQUIDITY PROFILE\n"Where The Money Is"', 
                    color=self.colors['text'], fontsize=16, fontweight='bold')
        
        # Remove x-axis (time not relevant for liquidity profile)
        ax.set_xticks([])
        
        # Legend
        ax.legend(loc='upper left', facecolor=self.colors['background'], 
                 edgecolor=self.colors['text'], labelcolor=self.colors['text'])
    
    def _plot_liquidity_profile(self, ax, liquidity_data: Dict):
        """Plot horizontal liquidity profile"""
        
        price_levels = liquidity_data['price_levels']
        liquidity_profile = liquidity_data['liquidity_profile']
        current_price = liquidity_data['current_price']
        
        # Horizontal bar chart of liquidity
        bars = ax.barh(price_levels, liquidity_profile, height=0.4,
                      color=self.colors['liquidity_high'], alpha=0.7)
        
        # Color bars by liquidity intensity
        max_liquidity = max(liquidity_profile)
        for bar, liquidity in zip(bars, liquidity_profile):
            if liquidity > max_liquidity * 0.7:
                bar.set_color(self.colors['liquidity_high'])
            elif liquidity > max_liquidity * 0.4:
                bar.set_color(self.colors['liquidity_med'])
            else:
                bar.set_color(self.colors['liquidity_low'])
        
        # Current price line
        ax.axhline(y=current_price, color=self.colors['text'], 
                  linestyle='-', linewidth=2)
        
        # Styling
        ax.set_ylim(current_price - 15, current_price + 15)
        ax.set_xlabel('Liquidity\nConcentration', color=self.colors['text'], fontsize=10)
        ax.set_title('Liquidity\nProfile', color=self.colors['text'], 
                    fontsize=12, fontweight='bold')
        
        # Add value labels for high liquidity levels
        for price, liquidity in zip(price_levels, liquidity_profile):
            if liquidity > max_liquidity * 0.6:
                ax.text(liquidity + max_liquidity * 0.02, price, f'{liquidity:.0f}',
                       color=self.colors['text'], fontsize=8, 
                       verticalalignment='center')

def update_trading_visualizer():
    """Add liquidity profile to existing trading visualizer"""
    
    # Read the existing trading_visualizer.py
    visualizer_path = Path("trading_visualizer.py")
    if not visualizer_path.exists():
        print("trading_visualizer.py not found")
        return False
    
    # Add liquidity chart method to TradingVisualizer class
    additional_code = '''
    def generate_liquidity_profile_chart(self, ticker: str, analysis_data: Dict[str, Any]) -> Optional[str]:
        """Generate liquidity profile chart showing where the money is"""
        try:
            from liquidity_profile_chart import LiquidityProfileChart
            
            liquidity_chart = LiquidityProfileChart()
            chart_path = liquidity_chart.create_liquidity_profile_chart(ticker, analysis_data)
            
            return chart_path
            
        except Exception as e:
            print(f"Liquidity profile chart error: {e}")
            return None
'''
    
    print("Liquidity profile chart module created successfully!")
    print("To integrate with trading_visualizer.py:")
    print("1. Add liquidity chart to generate_trade_validation_charts method")
    print("2. Call self.generate_liquidity_profile_chart(ticker, analysis_data)")
    
    return True

def main():
    """Test liquidity profile chart"""
    chart_generator = LiquidityProfileChart()
    
    # Test data
    test_data = {
        'agent_zero_decision': {
            'final_decision': 'BULLISH',
            'confidence': 87.3
        }
    }
    
    # Generate chart
    chart_path = chart_generator.create_liquidity_profile_chart("SPY", test_data)
    
    if chart_path:
        print(f"Liquidity Profile Chart generated: {chart_path}")
        
        # Auto-open
        import webbrowser
        try:
            webbrowser.open(f"file://{Path(chart_path).absolute()}")
            print("Opening chart in default image viewer...")
        except:
            print(f"Manual open: {chart_path}")
    else:
        print("Chart generation failed")

if __name__ == "__main__":
    main()
