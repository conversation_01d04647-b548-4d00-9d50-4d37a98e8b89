"""
Complete ML Feature Alignment Test

Test the complete ML pipeline with the corrected 61-feature extractor
to ensure 100% alignment with trained models.
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import logging

# Add parent directories to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from real_components.real_ml_feature_extractor import PerfectMLFeatureExtractor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_complete_ml_alignment():
    """Test complete ML pipeline alignment."""
    
    print("COMPLETE ML FEATURE ALIGNMENT TEST")
    print("=" * 50)
    
    # 1. Test feature extractor
    print("1. Testing Feature Extractor...")
    extractor = PerfectMLFeatureExtractor()
    
    feature_count = extractor.get_feature_count()
    print(f"   Feature count: {feature_count}")
    print(f"   Expected: 61")
    print(f"   Status: {'PASS' if feature_count == 61 else 'FAIL'}")
    
    # 2. Test feature extraction
    print("\n2. Testing Feature Extraction...")
    test_df = extractor.extract_features_from_factors([])
    
    extraction_success = len(test_df.columns) == 61
    print(f"   Extracted features: {len(test_df.columns)}")
    print(f"   Status: {'PASS' if extraction_success else 'FAIL'}")
    
    # 3. Test feature validation
    print("\n3. Testing Feature Validation...")
    is_valid, issues = extractor.validate_feature_alignment(test_df)
    print(f"   Validation: {'PASS' if is_valid else 'FAIL'}")
    if issues:
        for issue in issues:
            print(f"   Issue: {issue}")
    
    # 4. Test model compatibility
    print("\n4. Testing Model Compatibility...")
    models_dir = os.path.join(os.path.dirname(__file__), 'models')
    
    model_tests = {}
    
    # Test level_strength_model.pkl
    try:
        model_path = os.path.join(models_dir, 'level_strength_model.pkl')
        if os.path.exists(model_path):
            model = joblib.load(model_path)
            
            # Try prediction
            prediction = model.predict(test_df)
            model_tests['level_strength_model'] = {
                'loaded': True,
                'prediction_success': True,
                'expected_features': getattr(model, 'n_features_in_', 'Unknown'),
                'prediction_shape': prediction.shape
            }
            print(f"   level_strength_model: PASS (prediction shape: {prediction.shape})")
        else:
            model_tests['level_strength_model'] = {'loaded': False, 'error': 'File not found'}
            print(f"   level_strength_model: FAIL (not found)")
            
    except Exception as e:
        model_tests['level_strength_model'] = {'loaded': False, 'error': str(e)}
        print(f"   level_strength_model: FAIL ({e})")
    
    # Test price_reaction_model.pkl
    try:
        model_path = os.path.join(models_dir, 'price_reaction_model.pkl')
        if os.path.exists(model_path):
            model = joblib.load(model_path)
            
            # Try prediction
            prediction = model.predict(test_df)
            model_tests['price_reaction_model'] = {
                'loaded': True,
                'prediction_success': True,
                'expected_features': getattr(model, 'n_features_in_', 'Unknown'),
                'prediction_shape': prediction.shape
            }
            print(f"   price_reaction_model: PASS (prediction shape: {prediction.shape})")
        else:
            model_tests['price_reaction_model'] = {'loaded': False, 'error': 'File not found'}
            print(f"   price_reaction_model: FAIL (not found)")
            
    except Exception as e:
        model_tests['price_reaction_model'] = {'loaded': False, 'error': str(e)}
        print(f"   price_reaction_model: FAIL ({e})")
    
    # 5. Test current_features.txt alignment
    print("\n5. Testing current_features.txt Alignment...")
    features_file = os.path.join(models_dir, 'current_features.txt')
    
    if os.path.exists(features_file):
        with open(features_file, 'r') as f:
            file_features = [line.strip() for line in f if line.strip()]
        
        extractor_features = extractor.get_feature_names()
        
        file_count = len(file_features)
        features_match = file_features == extractor_features
        
        print(f"   File feature count: {file_count}")
        print(f"   Features match: {'PASS' if features_match else 'FAIL'}")
        
        if not features_match:
            print("   Mismatched features:")
            for i, (file_feat, ext_feat) in enumerate(zip(file_features, extractor_features)):
                if file_feat != ext_feat:
                    print(f"     Position {i+1}: file='{file_feat}' vs extractor='{ext_feat}'")
    else:
        print("   current_features.txt: FAIL (not found)")
        features_match = False
    
    # 6. Summary
    print("\n" + "=" * 50)
    print("SUMMARY")
    print("=" * 50)
    
    all_tests = [
        feature_count == 61,
        extraction_success,
        is_valid,
        model_tests.get('level_strength_model', {}).get('prediction_success', False),
        model_tests.get('price_reaction_model', {}).get('prediction_success', False),
        features_match
    ]
    
    passed_tests = sum(all_tests)
    total_tests = len(all_tests)
    
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("STATUS: 100% ML FEATURE ALIGNMENT ACHIEVED!")
        print("+ All systems perfectly aligned")
        print("+ Ready for production ML inference")
    else:
        print("STATUS: Alignment issues detected")
        print("X Fix required before production use")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = test_complete_ml_alignment()
    sys.exit(0 if success else 1)
