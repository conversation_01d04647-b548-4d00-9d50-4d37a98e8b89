{"cleanup_timestamp": "2025-06-24T18:18:06.879044", "files_moved": 80, "errors_encountered": 0, "moved_files_by_category": {"completed_tasks": 26, "debug_files": 4, "old_tests": 13, "old_versions": 8, "misc_scripts": 29}, "detailed_moves": [{"source": "A01_ANOMALY_DETECTION_COMPLETE.md", "destination": "archive\\completed_tasks\\A01_ANOMALY_DETECTION_COMPLETE.md", "category": "completed_tasks"}, {"source": "AGENT_ZERO_BACKTESTING_IMPLEMENTATION_COMPLETE.md", "destination": "archive\\completed_tasks\\AGENT_ZERO_BACKTESTING_IMPLEMENTATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "AGENT_ZERO_BACKTESTING_MISSION_ACCOMPLISHED.md", "destination": "archive\\completed_tasks\\AGENT_ZERO_BACKTESTING_MISSION_ACCOMPLISHED.md", "category": "completed_tasks"}, {"source": "AGENT_ZERO_FULL_POWER_COMPLETE.md", "destination": "archive\\completed_tasks\\AGENT_ZERO_FULL_POWER_COMPLETE.md", "category": "completed_tasks"}, {"source": "B_SERIES_GREEKS_IMPLEMENTATION_COMPLETE.md", "destination": "archive\\completed_tasks\\B_SERIES_GREEKS_IMPLEMENTATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "B_SERIES_GREEKS_IMPLEMENTATION_STATUS.md", "destination": "archive\\completed_tasks\\B_SERIES_GREEKS_IMPLEMENTATION_STATUS.md", "category": "completed_tasks"}, {"source": "B_SERIES_GREEK_ENHANCEMENT_REPORT.md", "destination": "archive\\completed_tasks\\B_SERIES_GREEK_ENHANCEMENT_REPORT.md", "category": "completed_tasks"}, {"source": "B_SERIES_IMPLEMENTATION_REPORT.md", "destination": "archive\\completed_tasks\\B_SERIES_IMPLEMENTATION_REPORT.md", "category": "completed_tasks"}, {"source": "COMPLETE_AGENT_EVALUATION_REPORT.md", "destination": "archive\\completed_tasks\\COMPLETE_AGENT_EVALUATION_REPORT.md", "category": "completed_tasks"}, {"source": "CONFIGURATION_ENHANCEMENT_COMPLETE.md", "destination": "archive\\completed_tasks\\CONFIGURATION_ENHANCEMENT_COMPLETE.md", "category": "completed_tasks"}, {"source": "CSID_FACTOR_SYSTEM_COMPLETE.md", "destination": "archive\\completed_tasks\\CSID_FACTOR_SYSTEM_COMPLETE.md", "category": "completed_tasks"}, {"source": "CSID_INTEGRATION_COMPLETE.md", "destination": "archive\\completed_tasks\\CSID_INTEGRATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "ENHANCED_AGENT_ZERO_INTEGRATION_COMPLETE.md", "destination": "archive\\completed_tasks\\ENHANCED_AGENT_ZERO_INTEGRATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "MISSION_ACCOMPLISHED.md", "destination": "archive\\completed_tasks\\MISSION_ACCOMPLISHED.md", "category": "completed_tasks"}, {"source": "MISSION_ACCOMPLISHED_AGENT_ZERO_ENHANCED.md", "destination": "archive\\completed_tasks\\MISSION_ACCOMPLISHED_AGENT_ZERO_ENHANCED.md", "category": "completed_tasks"}, {"source": "phase2_complete.md", "destination": "archive\\completed_tasks\\phase2_complete.md", "category": "completed_tasks"}, {"source": "PRE_ONBOARDING_COMPLETE.md", "destination": "archive\\completed_tasks\\PRE_ONBOARDING_COMPLETE.md", "category": "completed_tasks"}, {"source": "SCHWAB_MCP_IMPLEMENTATION_COMPLETE.md", "destination": "archive\\completed_tasks\\SCHWAB_MCP_IMPLEMENTATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "SCHWAB_MCP_INTEGRATION_FINAL_HANDOFF.md", "destination": "archive\\completed_tasks\\SCHWAB_MCP_INTEGRATION_FINAL_HANDOFF.md", "category": "completed_tasks"}, {"source": "SCHWAB_MCP_INTEGRATION_MISSION_ACCOMPLISHED.md", "destination": "archive\\completed_tasks\\SCHWAB_MCP_INTEGRATION_MISSION_ACCOMPLISHED.md", "category": "completed_tasks"}, {"source": "SCHWAB_MCP_TRADING_SYSTEM_INTEGRATION_COMPLETE.md", "destination": "archive\\completed_tasks\\SCHWAB_MCP_TRADING_SYSTEM_INTEGRATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "SPECIALIZED_AGENT_ARMY_COMPLETE.md", "destination": "archive\\completed_tasks\\SPECIALIZED_AGENT_ARMY_COMPLETE.md", "category": "completed_tasks"}, {"source": "SPECIAL_AGENT_EVALUATION_COMPLETE.md", "destination": "archive\\completed_tasks\\SPECIAL_AGENT_EVALUATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "STEP3_COMPLETION_REPORT.md", "destination": "archive\\completed_tasks\\STEP3_COMPLETION_REPORT.md", "category": "completed_tasks"}, {"source": "STEP4_COMPLETION_SUMMARY.md", "destination": "archive\\completed_tasks\\STEP4_COMPLETION_SUMMARY.md", "category": "completed_tasks"}, {"source": "TRADING_SYSTEM_IMPLEMENTATION_COMPLETE.md", "destination": "archive\\completed_tasks\\TRADING_SYSTEM_IMPLEMENTATION_COMPLETE.md", "category": "completed_tasks"}, {"source": "debug_b01_options.py", "destination": "archive\\debug_files\\debug_b01_options.py", "category": "debug_files"}, {"source": "debug_flow_physics_resolved.py", "destination": "archive\\debug_files\\debug_flow_physics_resolved.py", "category": "debug_files"}, {"source": "debug_mcp.py", "destination": "archive\\debug_files\\debug_mcp.py", "category": "debug_files"}, {"source": "debug_options.py", "destination": "archive\\debug_files\\debug_options.py", "category": "debug_files"}, {"source": "comprehensive_mcp_test_results_20250622_225017.json", "destination": "archive\\old_tests\\comprehensive_mcp_test_results_20250622_225017.json", "category": "old_tests"}, {"source": "comprehensive_mcp_test_results_20250622_225132.json", "destination": "archive\\old_tests\\comprehensive_mcp_test_results_20250622_225132.json", "category": "old_tests"}, {"source": "comprehensive_mcp_test_results_20250622_225240.json", "destination": "archive\\old_tests\\comprehensive_mcp_test_results_20250622_225240.json", "category": "old_tests"}, {"source": "comprehensive_mcp_test_results_20250622_230306.json", "destination": "archive\\old_tests\\comprehensive_mcp_test_results_20250622_230306.json", "category": "old_tests"}, {"source": "mcp_integration_test_results_20250622_223934.json", "destination": "archive\\old_tests\\mcp_integration_test_results_20250622_223934.json", "category": "old_tests"}, {"source": "mcp_integration_test_results_20250622_224059.json", "destination": "archive\\old_tests\\mcp_integration_test_results_20250622_224059.json", "category": "old_tests"}, {"source": "mcp_integration_test_results_20250622_224349.json", "destination": "archive\\old_tests\\mcp_integration_test_results_20250622_224349.json", "category": "old_tests"}, {"source": "test_results_20250614_110352.json", "destination": "archive\\old_tests\\test_results_20250614_110352.json", "category": "old_tests"}, {"source": "test_results_20250614_170359.json", "destination": "archive\\old_tests\\test_results_20250614_170359.json", "category": "old_tests"}, {"source": "test_results_20250614_170845.json", "destination": "archive\\old_tests\\test_results_20250614_170845.json", "category": "old_tests"}, {"source": "mcp_integration_report_20250622_223934.md", "destination": "archive\\old_tests\\mcp_integration_report_20250622_223934.md", "category": "old_tests"}, {"source": "mcp_integration_report_20250622_224059.md", "destination": "archive\\old_tests\\mcp_integration_report_20250622_224059.md", "category": "old_tests"}, {"source": "mcp_integration_report_20250622_224349.md", "destination": "archive\\old_tests\\mcp_integration_report_20250622_224349.md", "category": "old_tests"}, {"source": "agent_zero_backtester.py", "destination": "archive\\old_versions\\agent_zero_backtester.py", "category": "old_versions"}, {"source": "agent_zero_enhanced_backtester.py", "destination": "archive\\old_versions\\agent_zero_enhanced_backtester.py", "category": "old_versions"}, {"source": "multi_orchestrator.py", "destination": "archive\\old_versions\\multi_orchestrator.py", "category": "old_versions"}, {"source": "orchestrator_example.py", "destination": "archive\\old_versions\\orchestrator_example.py", "category": "old_versions"}, {"source": "enhanced_orchestrator.py", "destination": "archive\\old_versions\\enhanced_orchestrator.py", "category": "old_versions"}, {"source": "enhanced_orchestrator_iv.py", "destination": "archive\\old_versions\\enhanced_orchestrator_iv.py", "category": "old_versions"}, {"source": "fixed_accumulation_distribution_agent.py", "destination": "archive\\old_versions\\fixed_accumulation_distribution_agent.py", "category": "old_versions"}, {"source": "production_accumulation_distribution.py", "destination": "archive\\old_versions\\production_accumulation_distribution.py", "category": "old_versions"}, {"source": "create_ci_snapshot.py", "destination": "archive\\misc_scripts\\create_ci_snapshot.py", "category": "misc_scripts"}, {"source": "demo_live_testing.py", "destination": "archive\\misc_scripts\\demo_live_testing.py", "category": "misc_scripts"}, {"source": "deployment_checklist.py", "destination": "archive\\misc_scripts\\deployment_checklist.py", "category": "misc_scripts"}, {"source": "deploy_enhanced_agent.py", "destination": "archive\\misc_scripts\\deploy_enhanced_agent.py", "category": "misc_scripts"}, {"source": "discover_local_mcp.py", "destination": "archive\\misc_scripts\\discover_local_mcp.py", "category": "misc_scripts"}, {"source": "evaluate_special_agent.py", "destination": "archive\\misc_scripts\\evaluate_special_agent.py", "category": "misc_scripts"}, {"source": "final_b01_validation.py", "destination": "archive\\misc_scripts\\final_b01_validation.py", "category": "misc_scripts"}, {"source": "final_validation_test.py", "destination": "archive\\misc_scripts\\final_validation_test.py", "category": "misc_scripts"}, {"source": "generate_evaluation_report.py", "destination": "archive\\misc_scripts\\generate_evaluation_report.py", "category": "misc_scripts"}, {"source": "iv_roc_demo.py", "destination": "archive\\misc_scripts\\iv_roc_demo.py", "category": "misc_scripts"}, {"source": "launch_live_testing.py", "destination": "archive\\misc_scripts\\launch_live_testing.py", "category": "misc_scripts"}, {"source": "live_checklist_dashboard.py", "destination": "archive\\misc_scripts\\live_checklist_dashboard.py", "category": "misc_scripts"}, {"source": "live_testing_dashboard.py", "destination": "archive\\misc_scripts\\live_testing_dashboard.py", "category": "misc_scripts"}, {"source": "mcp_system_diagnostics.py", "destination": "archive\\misc_scripts\\mcp_system_diagnostics.py", "category": "misc_scripts"}, {"source": "quick_deploy.py", "destination": "archive\\misc_scripts\\quick_deploy.py", "category": "misc_scripts"}, {"source": "quick_mcp_test.py", "destination": "archive\\misc_scripts\\quick_mcp_test.py", "category": "misc_scripts"}, {"source": "run_live_testing_day.py", "destination": "archive\\misc_scripts\\run_live_testing_day.py", "category": "misc_scripts"}, {"source": "security_check.py", "destination": "archive\\misc_scripts\\security_check.py", "category": "misc_scripts"}, {"source": "security_sweep.py", "destination": "archive\\misc_scripts\\security_sweep.py", "category": "misc_scripts"}, {"source": "setup_pre_onboarding.py", "destination": "archive\\misc_scripts\\setup_pre_onboarding.py", "category": "misc_scripts"}, {"source": "updated_evaluation.py", "destination": "archive\\misc_scripts\\updated_evaluation.py", "category": "misc_scripts"}, {"source": "update_agent_zero_integration.py", "destination": "archive\\misc_scripts\\update_agent_zero_integration.py", "category": "misc_scripts"}, {"source": "validate_100_percent_integration.py", "destination": "archive\\misc_scripts\\validate_100_percent_integration.py", "category": "misc_scripts"}, {"source": "validate_agent_zero_backtesting.py", "destination": "archive\\misc_scripts\\validate_agent_zero_backtesting.py", "category": "misc_scripts"}, {"source": "validate_agent_zero_integration.py", "destination": "archive\\misc_scripts\\validate_agent_zero_integration.py", "category": "misc_scripts"}, {"source": "validate_b01_implementation.py", "destination": "archive\\misc_scripts\\validate_b01_implementation.py", "category": "misc_scripts"}, {"source": "validate_b_series.py", "destination": "archive\\misc_scripts\\validate_b_series.py", "category": "misc_scripts"}, {"source": "validate_for_next_agent.py", "destination": "archive\\misc_scripts\\validate_for_next_agent.py", "category": "misc_scripts"}, {"source": "verify_output_coordinator.py", "destination": "archive\\misc_scripts\\verify_output_coordinator.py", "category": "misc_scripts"}], "errors": []}