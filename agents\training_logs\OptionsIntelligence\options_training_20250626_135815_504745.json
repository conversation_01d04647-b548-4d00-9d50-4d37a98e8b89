{"input_data": {"input_data": {"ticker": "SPY", "underlying_price": 455.0, "strike": 460.0, "expiration_date": "2025-01-17", "option_type": "call", "implied_volatility": 0.25, "agent_zero_signal": "buy_calls", "agent_zero_confidence": 0.75, "iv_rank": 45.0, "price_trend": "bullish", "volatility_regime": "normal_vol"}, "option_data": {"underlying_price": 455.0, "strike": 460.0, "expiration_date": "2025-01-17", "option_type": "call", "implied_volatility": 0.25, "risk_free_rate": 0.05, "dividend_yield": 0.0, "symbol": "SPY"}, "market_context": {"iv_rank": 45.0, "iv_trend": "stable", "price_trend": "bullish", "volatility_regime": "normal_vol", "days_to_earnings": null, "support_level": null, "resistance_level": null, "historical_vol_20d": 0.2}, "greeks": {"delta": 0.08500793442433846, "gamma": 0.04326264865397493, "theta": -0.771570158267436, "vega": 0.0223911245939729, "rho": 0.00038538857650395326, "delta_roc": 0.0, "gamma_roc": 0.0, "theta_roc": 0.0, "vega_roc": 0.0, "rho_roc": 0.0, "calculation_quality": 0.9600000000000001, "mathematical_validity": true, "anomaly_count": 0, "vanna": null, "charm": null, "volga": null}, "learning_adjustments": {}}, "predicted_outcome": {"trade_analysis": {"trade_classification": "rejected_insufficient_roi", "risk_profile": "below_minimum_roi_threshold", "leverage_factor": 11.860561771987719, "daily_theta_cost": 0.771570158267436, "gamma_acceleration": 0.04326264865397493, "iv_sensitivity": 0.0223911245939729, "probability_estimate": 0.0, "breakeven_move_required": 8.43130383892777, "max_profit_potential": "high_but_limited", "risk_reward_ratio": 0.8500793442433845}, "position_sizing": {"recommended_size": 0.0, "size_reasoning": ["Trade rejected: Insufficient ROI potential (min 1.5x required)"], "risk_adjustments": {"roi_rejection": 0.0}, "maximum_size_limit": 0.0, "minimum_size_threshold": 0.0, "confidence_scaling": 0.0}, "exit_strategy": {"max_days_in_trade": 1, "profit_targets": [{"target_name": "quick_profit", "profit_percentage": 25, "target_value": 4.076388929403956, "timeframe": "within_3_days", "reasoning": "Quick profit capture to reduce risk"}, {"target_name": "primary_target", "profit_percentage": 50, "target_value": 4.891666715284747, "timeframe": "primary_target", "reasoning": "Primary profit objective"}, {"target_name": "extended_target", "profit_percentage": 100, "target_value": 6.522222287046329, "timeframe": "if_big_move", "reasoning": "Home run profit if thesis plays out strongly"}], "stop_losses": [{"stop_name": "premium_stop", "stop_percentage": 50.0, "stop_value": 1.6305555717615823, "reasoning": "Risk management - limit loss to 50%"}, {"stop_name": "time_decay_stop", "condition": "daily_theta_exceeds_expected", "reasoning": "Exit if time decay accelerates beyond normal"}], "time_exits": [{"exit_name": "theta_acceleration_zone", "trigger_days": 14, "condition": "days_to_expiration <= 14", "reasoning": "Theta acceleration begins - time decay risk increases"}, {"exit_name": "weekend_risk_zone", "trigger_days": 3, "condition": "days_to_expiration <= 3 AND approaching_friday", "reasoning": "Weekend time decay risk near expiration"}], "iv_exits": [{"exit_name": "iv_crush_protection", "trigger_condition": "iv_rank_drops_below_15.0", "reasoning": "Protect against IV crush"}, {"exit_name": "iv_expansion_profit", "trigger_condition": "iv_rank_rises_above_65.0", "reasoning": "Take profits on IV expansion"}], "emergency_exits": [{"exit_name": "liquidity_crisis", "condition": "bid_ask_spread > 20% OR volume < 10", "reasoning": "Execution risk too high - exit immediately"}, {"exit_name": "extreme_favorable_move", "condition": "underlying_move > 0.3% in single day", "reasoning": "Outsized move may reverse - take profits"}, {"exit_name": "market_regime_change", "condition": "market_down > 3% AND vix_spike > 20%", "reasoning": "Market stress - reassess all positions"}], "priority_exit_sequence": ["weekend_risk_zone", "quick_profit", "premium_stop"], "exit_monitoring_alerts": ["Monitor daily theta decay vs expected", "Track IV rank changes", "Watch underlying price vs support/resistance", "Monitor delta - OTM option needs significant move"]}, "final_recommendation": {"final_action": "buy_calls", "ticker": "SPY", "execution_quality": "moderate", "strike": 460.0, "option_type": "call", "position_size": 0.0, "expiration_date": "2025-01-17", "estimated_delta": 0.08500793442433846, "daily_theta_cost": -0.771570158267436, "iv_sensitivity": 0.0223911245939729, "gamma_risk": 0.04326264865397493, "trade_classification": "rejected_insufficient_roi", "leverage_factor": 11.860561771987719, "probability_estimate": 0.0, "breakeven_move_pct": 8.43130383892777, "max_hold_days": 1, "profit_targets": [{"target_name": "quick_profit", "profit_percentage": 25, "target_value": 4.076388929403956, "timeframe": "within_3_days", "reasoning": "Quick profit capture to reduce risk"}, {"target_name": "primary_target", "profit_percentage": 50, "target_value": 4.891666715284747, "timeframe": "primary_target", "reasoning": "Primary profit objective"}, {"target_name": "extended_target", "profit_percentage": 100, "target_value": 6.522222287046329, "timeframe": "if_big_move", "reasoning": "Home run profit if thesis plays out strongly"}], "stop_losses": [{"stop_name": "premium_stop", "stop_percentage": 50.0, "stop_value": 1.6305555717615823, "reasoning": "Risk management - limit loss to 50%"}, {"stop_name": "time_decay_stop", "condition": "daily_theta_exceeds_expected", "reasoning": "Exit if time decay accelerates beyond normal"}], "primary_exit_rule": "weekend_risk_zone", "overall_risk_level": "high", "primary_risks": ["high_leverage", "low_probability_trade"], "execution_risks": ["Low delta - may have wide spreads"], "comprehensive_analysis": {"option_specification": {"ticker": "SPY", "underlying_price": 455.0, "strike": 460.0, "expiration_date": "2025-01-17", "option_type": "call", "implied_volatility": 0.25}, "greeks_analysis": {"delta": 0.08500793442433846, "gamma": 0.04326264865397493, "theta": -0.771570158267436, "vega": 0.0223911245939729, "rho": 0.00038538857650395326, "delta_roc": 0.0, "gamma_roc": 0.0, "theta_roc": 0.0, "vega_roc": 0.0, "calculation_quality": 0.9600000000000001, "mathematical_validity": true, "anomaly_count": 0}, "trade_analysis": {"classification": "rejected_insufficient_roi", "risk_profile": "below_minimum_roi_threshold", "leverage_factor": 11.860561771987719, "daily_theta_cost": 0.771570158267436, "gamma_acceleration": 0.04326264865397493, "iv_sensitivity": 0.0223911245939729, "probability_estimate": 0.0, "breakeven_move_required": 8.43130383892777, "max_profit_potential": "high_but_limited", "risk_reward_ratio": 0.8500793442433845}, "position_sizing": {"recommended_size": 0.0, "size_reasoning": ["Trade rejected: Insufficient ROI potential (min 1.5x required)"], "risk_adjustments": {"roi_rejection": 0.0}, "confidence_scaling": 0.0}, "exit_strategy": {"max_days_in_trade": 1, "profit_targets": [{"target_name": "quick_profit", "profit_percentage": 25, "target_value": 4.076388929403956, "timeframe": "within_3_days", "reasoning": "Quick profit capture to reduce risk"}, {"target_name": "primary_target", "profit_percentage": 50, "target_value": 4.891666715284747, "timeframe": "primary_target", "reasoning": "Primary profit objective"}, {"target_name": "extended_target", "profit_percentage": 100, "target_value": 6.522222287046329, "timeframe": "if_big_move", "reasoning": "Home run profit if thesis plays out strongly"}], "stop_losses": [{"stop_name": "premium_stop", "stop_percentage": 50.0, "stop_value": 1.6305555717615823, "reasoning": "Risk management - limit loss to 50%"}, {"stop_name": "time_decay_stop", "condition": "daily_theta_exceeds_expected", "reasoning": "Exit if time decay accelerates beyond normal"}], "time_exits": [{"exit_name": "theta_acceleration_zone", "trigger_days": 14, "condition": "days_to_expiration <= 14", "reasoning": "Theta acceleration begins - time decay risk increases"}, {"exit_name": "weekend_risk_zone", "trigger_days": 3, "condition": "days_to_expiration <= 3 AND approaching_friday", "reasoning": "Weekend time decay risk near expiration"}], "iv_exits": [{"exit_name": "iv_crush_protection", "trigger_condition": "iv_rank_drops_below_15.0", "reasoning": "Protect against IV crush"}, {"exit_name": "iv_expansion_profit", "trigger_condition": "iv_rank_rises_above_65.0", "reasoning": "Take profits on IV expansion"}], "emergency_exits": [{"exit_name": "liquidity_crisis", "condition": "bid_ask_spread > 20% OR volume < 10", "reasoning": "Execution risk too high - exit immediately"}, {"exit_name": "extreme_favorable_move", "condition": "underlying_move > 0.3% in single day", "reasoning": "Outsized move may reverse - take profits"}, {"exit_name": "market_regime_change", "condition": "market_down > 3% AND vix_spike > 20%", "reasoning": "Market stress - reassess all positions"}], "priority_exit_sequence": ["weekend_risk_zone", "quick_profit", "premium_stop"], "monitoring_alerts": ["Monitor daily theta decay vs expected", "Track IV rank changes", "Watch underlying price vs support/resistance", "Monitor delta - OTM option needs significant move"]}, "market_environment": {"iv_rank": 45.0, "iv_trend": "stable", "price_trend": "bullish", "volatility_regime": "normal_vol", "days_to_earnings": null}, "risk_assessment": {"overall_risk_level": "high", "primary_risks": ["high_leverage", "low_probability_trade"], "risk_mitigation": ["Reduce position size due to high leverage", "Quick exit required due to time decay", "Small position size for speculation", "Monitor all exit conditions closely"], "execution_risks": ["Low delta - may have wide spreads"]}}, "reasoning": "Agent Zero buy_calls signal processed through comprehensive options analysis. Trade classified as rejected_insufficient_roi with 11.9x leverage and 0% estimated probability. Position sized to 0.0 contracts based on 1 risk factors. Greeks: Delta 0.085, Theta -0.772, Vega 0.022. Exit strategy: maximum 1 days with 3 profit targets and 2 stop losses.", "analysis_timestamp": "2025-06-26T13:58:15.504726", "greek_engine_quality": 0.9600000000000001}, "roi_validation": false}, "actual_outcome": null, "learning_metrics": {"accuracy_score": 0.8, "roi_prediction_accuracy": 0.7, "position_sizing_effectiveness": 0.75, "exit_timing_accuracy": 0.7, "confidence_improvement": 0.0, "learning_iteration": 1, "timestamp": "2025-06-26T13:58:15.504736"}, "feedback_score": 0.5, "timestamp": "2025-06-26T13:58:15.504741"}