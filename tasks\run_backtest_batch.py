    )
    
    # Output results
    print("=" * 60)
    print("BACKTEST BATCH RESULTS")
    print("=" * 60)
    
    summary = batch_results['summary']
    print(f"Total Tickers: {summary['total_tickers']}")
    print(f"Successful: {summary['successful']}")
    print(f"Failed: {summary['failed']}")
    print(f"Success Rate: {summary['success_rate']:.1%}")
    print(f"Total Duration: {batch_results['duration']:.2f}s")
    
    if summary['successful_tickers']:
        print(f"\nSUCCESSFUL TICKERS:")
        for ticker in summary['successful_tickers']:
            result = batch_results['results'][ticker]
            perf = result.get('performance', {})
            print(f"  {ticker}: Sharpe={perf.get('sharpe_ratio', 0):.3f}, "
                  f"Return={perf.get('total_return', 0):.2f}%, "
                  f"Trades={perf.get('total_trades', 0)}")
    
    if summary['failed_tickers']:
        print(f"\nFAILED TICKERS:")
        for ticker in summary['failed_tickers']:
            result = batch_results['results'][ticker]
            print(f"  {ticker}: {result.get('error', 'Unknown error')}")
    
    # Shadow mode logging - capture batch backtest results
    try:
        from agents.agent_zero import AgentZeroAdvisor
        shadow_agent = AgentZeroAdvisor()
        
        signal_data = {
            'confidence': summary['success_rate'],  # Success rate as confidence
            'strength': min(summary['successful'] / 10.0, 1.0),  # Normalize successful count
            'execution_recommendation': 'execute' if summary['success_rate'] > 0.5 else 'avoid'
        }
        
        math_data = {
            'accuracy_score': summary['success_rate'],  # Success rate as accuracy
            'precision': 0.001
        }
        
        market_context = {
            'system': 'backtest_batch_processor',
            'total_tickers': summary['total_tickers'],
            'successful': summary['successful'],
            'failed': summary['failed'],
            'success_rate': summary['success_rate'],
            'duration': batch_results['duration'],
            'successful_tickers': summary['successful_tickers'],
            'failed_tickers': summary['failed_tickers']
        }
        
        shadow_agent.log_training_data(
            signal_data=signal_data,
            math_data=math_data,
            decision={'action': 'batch_backtest', 'summary': summary},
            outcome=summary['success_rate'],  # Success rate as outcome
            market_context=market_context
        )
        print("Shadow mode: Batch backtest results logged")
        
    except Exception as e:
        print(f"Shadow mode logging failed: {e}")
    
    return 0 if summary['failed'] == 0 else 1


if __name__ == "__main__":
    sys.exit(main())
