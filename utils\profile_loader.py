#!/usr/bin/env python3
"""
Profile Loader - CORE Configuration Management
Loads environment profiles from settings.yml for different deployment scenarios
"""

import yaml
import os
import argparse
from pathlib import Path


def load_config():
    """Load configuration from settings.yml"""
    config_path = Path("settings.yml")
    if not config_path.exists():
        # Try from script directory
        script_dir = Path(__file__).parent.parent
        config_path = script_dir / "settings.yml"
    
    if not config_path.exists():
        raise FileNotFoundError("settings.yml not found in current directory or script root")
    
    with open(config_path) as f:
        return yaml.safe_load(f)


# Load configuration once at module level
CFG = load_config()


def apply_profile(name):
    """Apply a configuration profile by setting environment variables"""
    if name not in CFG["profiles"]:
        available = list(CFG["profiles"].keys())
        raise ValueError(f"Profile '{name}' not found. Available: {available}")
    
    prof = CFG["profiles"][name]
    
    # Set core environment variables
    os.environ["ACCOUNT_EQUITY"] = str(prof["account_equity"])
    os.environ["DATA_SOURCE"] = prof["data_source"]
    os.environ["AGENT_ZERO_MODE"] = prof["agent_zero_mode"]
    os.environ["CORE_PROFILE"] = name  # Track current profile
    
    # Set directory paths
    if prof.get("fills_dir"):
        os.environ["FILLS_DIR"] = prof["fills_dir"]
    if prof.get("logs_dir"):
        os.environ["LOGS_DIR"] = prof["logs_dir"]
    if prof.get("training_logs_dir"):
        os.environ["TRAINING_LOGS_DIR"] = prof["training_logs_dir"]
    
    # Set Tradier token if configured
    if prof.get("tradier_token_env"):
        token_env = prof["tradier_token_env"]
        token_value = os.getenv(token_env, "")
        if token_value:
            os.environ["TRADIER_TOKEN"] = token_value
        else:
            print(f"WARNING: {token_env} environment variable not set")
    
    # Create directories if they don't exist
    for dir_key in ["fills_dir", "logs_dir", "training_logs_dir"]:
        if prof.get(dir_key):
            Path(prof[dir_key]).mkdir(parents=True, exist_ok=True)
    
    print(f"Applied profile: {name}")
    print(f"  Account Equity: ${prof['account_equity']:,}")
    print(f"  Data Source: {prof['data_source']}")
    print(f"  Agent Zero Mode: {prof['agent_zero_mode']}")
    print(f"  Fills Directory: {prof.get('fills_dir', 'default')}")
    
    return prof


def get_profile(name):
    """Get profile configuration without applying it"""
    if name not in CFG["profiles"]:
        available = list(CFG["profiles"].keys())
        raise ValueError(f"Profile '{name}' not found. Available: {available}")
    
    return CFG["profiles"][name]


def list_profiles():
    """List all available profiles"""
    print("Available profiles:")
    for name, profile in CFG["profiles"].items():
        print(f"  {name}:")
        print(f"    Account Equity: ${profile['account_equity']:,}")
        print(f"    Data Source: {profile['data_source']}")
        print(f"    Agent Zero: {profile['agent_zero_mode']}")
        print(f"    Auto Broker: {'Yes' if profile.get('tradier_token_env') else 'No'}")
        print()


def main():
    """CLI interface for profile management"""
    parser = argparse.ArgumentParser(description="CORE Profile Management")
    parser.add_argument("--list", action="store_true", help="List available profiles")
    parser.add_argument("--apply", help="Apply a profile")
    parser.add_argument("--show", help="Show profile details")
    
    args = parser.parse_args()
    
    if args.list:
        list_profiles()
    elif args.apply:
        apply_profile(args.apply)
    elif args.show:
        profile = get_profile(args.show)
        print(f"Profile: {args.show}")
        for key, value in profile.items():
            print(f"  {key}: {value}")
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
