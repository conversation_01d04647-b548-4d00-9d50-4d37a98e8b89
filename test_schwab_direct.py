#!/usr/bin/env python3
"""
Direct Schwab API Test
Test real Schwab API connectivity without MCP layer
"""

import sys
import logging
from pathlib import Path

# Add Schwab production API to path
sys.path.append(str(Path("SCHWAB_MCP_PRODUCTION/core")))

def test_schwab_direct():
    """Test direct Schwab API access"""
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    logger.info("="*60)
    logger.info("DIRECT SCHWAB API CONNECTIVITY TEST")
    logger.info("="*60)
    
    try:
        # Import and test Schwab API directly
        from schwab_production_api import SchwabAPI
        
        logger.info("✓ Schwab API module imported successfully")
        
        # Initialize API client
        schwab_api = SchwabAPI()
        logger.info("✓ Schwab API client initialized")
        
        # Test basic quote retrieval with detailed diagnostics
        logger.info("Testing quote retrieval for AAPL...")

        # First test the raw API response
        try:
            endpoint = "/marketdata/v1/AAPL/quotes"
            raw_response = schwab_api._make_request(endpoint)
            logger.info(f"Raw API response: {raw_response}")
        except Exception as e:
            logger.error(f"Raw API request failed: {e}")

        quote = schwab_api.get_quote("AAPL")
        
        if quote:
            logger.info("✓ QUOTE RETRIEVAL SUCCESSFUL")
            logger.info(f"  Quote type: {type(quote)}")
            logger.info(f"  Quote data: {quote}")

            # Handle both dict and object formats
            if hasattr(quote, 'symbol'):
                logger.info(f"  Symbol: {quote.symbol}")
                logger.info(f"  Price: ${quote.price}")
                logger.info(f"  Volume: {quote.volume:,}")
                logger.info(f"  Change: {quote.change}")
            elif isinstance(quote, dict):
                logger.info(f"  Symbol: {quote.get('symbol', 'N/A')}")
                logger.info(f"  Price: ${quote.get('price', 'N/A')}")
                logger.info(f"  Volume: {quote.get('volume', 'N/A'):,}")
                logger.info(f"  Change: {quote.get('change', 'N/A')}")
            else:
                logger.info(f"  Raw quote: {quote}")
        else:
            logger.error("✗ Quote retrieval returned empty result")
            return False
            
        # Test price history
        logger.info("Testing price history retrieval...")
        try:
            history = schwab_api.get_price_history(
                symbol="AAPL",
                period_type="day",
                period=2,
                frequency_type="minute",
                frequency=1
            )

            logger.info(f"Price history response type: {type(history)}")
            logger.info(f"Price history data: {history}")

            if history and isinstance(history, list):
                logger.info(f"✓ PRICE HISTORY SUCCESSFUL - {len(history)} data points")
                if history:
                    latest = history[-1]
                    logger.info(f"  Latest Close: ${latest.get('close', 'N/A')}")
                    logger.info(f"  Latest Volume: {latest.get('volume', 'N/A'):,}")
            elif history and isinstance(history, dict) and 'candles' in history:
                candles = history['candles']
                logger.info(f"✓ PRICE HISTORY SUCCESSFUL - {len(candles)} data points")
                if candles:
                    latest = candles[-1]
                    logger.info(f"  Latest Close: ${latest.get('close', 'N/A')}")
                    logger.info(f"  Latest Volume: {latest.get('volume', 'N/A'):,}")
            else:
                logger.error("✗ Price history retrieval failed - unexpected format")
                return False
        except Exception as e:
            logger.error(f"✗ Price history error: {e}")
            return False
            
        logger.info("="*60)
        logger.info("✓ SCHWAB API CONNECTIVITY: CONFIRMED")
        logger.info("✓ Real data access: WORKING")
        logger.info("="*60)
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import error: {e}")
        return False
    except Exception as e:
        logger.error(f"✗ API test failed: {e}")
        logger.error(f"Error type: {type(e).__name__}")
        return False

def test_token_status():
    """Check token status"""
    import json
    import time
    
    logger = logging.getLogger(__name__)
    
    try:
        token_path = Path("SCHWAB_MCP_PRODUCTION/config/schwab_token.json")
        
        with open(token_path, 'r') as f:
            token_data = json.load(f)
        
        current_time = time.time()
        expires_at = token_data.get('expires_at', 0)
        time_remaining = expires_at - current_time
        
        logger.info(f"Token expires in: {time_remaining / 3600:.2f} hours")
        
        if time_remaining > 0:
            logger.info("✓ Token status: VALID")
            return True
        else:
            logger.error("✗ Token status: EXPIRED")
            return False
            
    except Exception as e:
        logger.error(f"✗ Token check failed: {e}")
        return False

if __name__ == "__main__":
    print("\n🔍 Testing Schwab API Direct Connectivity")
    print("=" * 50)
    
    # Check token first
    if not test_token_status():
        print("❌ Token validation failed - cannot proceed")
        sys.exit(1)
    
    # Test API connectivity
    if test_schwab_direct():
        print("\n✅ SCHWAB API TEST: SUCCESS")
        print("Real data access confirmed - ready for CORE system")
    else:
        print("\n❌ SCHWAB API TEST: FAILED")
        print("Data connectivity issues detected")
        sys.exit(1)
