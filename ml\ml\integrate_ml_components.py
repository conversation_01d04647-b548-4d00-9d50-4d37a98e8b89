"""
ML Components Integration Script

This script integrates the machine learning components into the Liquidity
Strategy Dashboard, adding pattern recognition, predictive analytics, and
alert generation capabilities.
"""

import os
import sys
import argparse
import logging
from typing import Dict, List, Any, Optional

# Add src directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "src")))

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("ml_integration.log")
    ]
)
logger = logging.getLogger("ml_integration")


def check_dependencies() -> bool:
    """
    Check if all required dependencies are installed.

    Returns:
        True if all dependencies are met, False otherwise
    """
    required_packages = [
        "numpy",
        "pandas",
        "scikit-learn",
        "dash",
        "dash-bootstrap-components",
        "plotly"
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.error("Install them with: pip install " + " ".join(missing_packages))
        return False

    # Optional deep learning dependencies
    optional_packages = ["tensorflow", "torch"]
    missing_optional = []

    for package in optional_packages:
        try:
            __import__(package)
        except ImportError:
            missing_optional.append(package)

    if missing_optional:
        logger.warning(f"Missing optional packages: {', '.join(missing_optional)}")
        logger.warning("Some advanced ML features may not be available.")

    return True


def setup_ml_directories() -> bool:
    """
    Set up directory structure for ML components.

    Returns:
        True if successful, False otherwise
    """
    try:
        ml_dirs = [
            "src/ml/config",
            "src/ml/models",
            "src/ml/rules",
            "src/ml/alerts"
        ]

        for directory in ml_dirs:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Created directory: {directory}")

        return True

    except Exception as e:
        logger.error(f"Error setting up ML directories: {e}")
        return False


def modify_dashboard_file() -> bool:
    """
    Modify the main dashboard file to add ML tab.

    Returns:
        True if successful, False otherwise
    """
    # Determine dashboard file to modify
    dashboard_file = "liquidity_dashboard.py"
    if not os.path.exists(dashboard_file):
        dashboard_file = "Liquidity Dashboard.py"
        if not os.path.exists(dashboard_file):
            logger.error("Dashboard file not found")
            return False

    try:
        # Read existing dashboard file
        with open(dashboard_file, 'r') as f:
            content = f.read()

        # Check if ML integration is already added
        if "from ml.ml_integration import" in content:
            logger.info("ML integration already added to dashboard")
            return True

        # Add imports
        import_code = "\n# ML Integration\nfrom ml.ml_integration import initialize_ml_components, get_ml_integration\n"
        if "# Import custom modules" in content:
            content = content.replace("# Import custom modules", "# Import custom modules" + import_code)
        else:
            # Find imports section and add at the end
            import_section_end = content.rfind("import") + 40  # Approximate
            content = content[:import_section_end] + import_code + content[import_section_end:]

        # Add initialization code
        init_code = "\n    # Initialize ML components\n    ml_integration = initialize_ml_components()\n"
        if "def initialize_dashboard():" in content:
            content = content.replace("def initialize_dashboard():", "def initialize_dashboard():" + init_code)
        else:
            # Find app creation and add after it
            app_creation = content.find("app = dash.Dash(")
            if app_creation >= 0:
                app_end = content.find("\n", app_creation + 20)  # Find end of app creation line
                content = content[:app_end+1] + init_code + content[app_end+1:]

        # Add ML tab to layout
        tab_code = "\n    # ML Tab\n    ml_tab = ml_integration.add_dashboard_tab(app, lambda: data_loader.get_current_data())\n"
        tab_addition = "    tabs = dcc.Tabs([\n"
        if tab_addition in content:
            ml_tab_addition = "        dcc.Tab(label='Machine Learning', children=ml_tab),\n"
            tab_section_start = content.find(tab_addition) + len(tab_addition)
            content = content[:tab_section_start] + ml_tab_addition + content[tab_section_start:]

        # Write modified content
        with open(dashboard_file, 'w') as f:
            f.write(content)

        logger.info(f"Modified dashboard file: {dashboard_file}")
        return True

    except Exception as e:
        logger.error(f"Error modifying dashboard file: {e}")
        return False


def create_ml_config() -> bool:
    """
    Create default ML configuration files.

    Returns:
        True if successful, False otherwise
    """
    try:
        # Import ML configuration module
        from ml.ml_config_manager import get_config_manager, update_global_config

        # Set up basic configuration
        config_updates = {
            'components': {
                'model_registry': {
                    'storage_path': 'src/ml/models',
                    'max_versions_per_model': 5
                },
                'alert_system': {
                    'storage_path': 'src/ml/alerts',
                    'notify_on_create': True,
                    'auto_cleanup': True,
                    'cleanup_interval_seconds': 3600,
                    'notifications': {
                        'notification_methods': ['dashboard', 'log']
                    }
                },
                'alert_pipeline': {
                    'rules_path': 'src/ml/rules',
                    'async_processing': True
                },
                'inference_service': {
                    'cache_size': 10,
                    'async_inference': True,
                    'batch_size': 32
                },
                'dashboard_integration': {
                    'ml_tab_layout': 'standard',
                    'show_feature_importance': True,
                    'show_prediction_intervals': True,
                    'max_displayed_patterns': 10,
                    'auto_update_interval_ms': 5000
                }
            }
        }

        # Update global configuration
        update_global_config(config_updates)
        logger.info("Created default ML configuration")

        return True

    except Exception as e:
        logger.error(f"Error creating ML configuration: {e}")
        return False


def setup_default_rules() -> bool:
    """
    Set up default alert rules.

    Returns:
        True if successful, False otherwise
    """
    try:
        # Import required modules
        from ml.ml_alert_pipeline import get_alert_pipeline, RuleBuilder

        # Get alert pipeline
        pipeline = get_alert_pipeline()

        # Create default rules
        rules = []

        # Bull trap rule
        rules.append(
            RuleBuilder()
            .for_pattern_detection()
            .with_patterns(['bull_trap'])
            .with_min_confidence(0.7)
            .with_confidence_threshold(0.7)
            .with_expiration(24)
            .with_description("Detect bull trap patterns")
            .build()
        )

        # Bear trap rule
        rules.append(
            RuleBuilder()
            .for_pattern_detection()
            .with_patterns(['bear_trap'])
            .with_min_confidence(0.7)
            .with_confidence_threshold(0.7)
            .with_expiration(24)
            .with_description("Detect bear trap patterns")
            .build()
        )

        # Significant price movement rules
        rules.append(
            RuleBuilder()
            .for_price_prediction()
            .with_price_direction('up')
            .with_price_threshold(5.0)
            .with_horizon(5)
            .with_threshold_percent(5.0)
            .with_time_horizon(5)
            .with_expiration(24)
            .with_description("Detect 5% upside move in 5 periods")
            .build()
        )

        rules.append(
            RuleBuilder()
            .for_price_prediction()
            .with_price_direction('down')
            .with_price_threshold(5.0)
            .with_horizon(5)
            .with_threshold_percent(5.0)
            .with_time_horizon(5)
            .with_expiration(24)
            .with_description("Detect 5% downside move in 5 periods")
            .build()
        )

        # Strategy signal rules
        rules.append(
            RuleBuilder()
            .for_strategy_signals()
            .with_signal_type('buy')
            .with_min_confidence(0.8)
            .with_alert_confidence(0.8)
            .with_expiration(24)
            .with_description("High confidence buy signals")
            .build()
        )

        rules.append(
            RuleBuilder()
            .for_strategy_signals()
            .with_signal_type('sell')
            .with_min_confidence(0.8)
            .with_alert_confidence(0.8)
            .with_expiration(24)
            .with_description("High confidence sell signals")
            .build()
        )

        # Liquidity anomaly rules
        rules.append(
            RuleBuilder()
            .for_pattern_detection()
            .with_patterns(['liquidity_anomaly'])
            .with_min_confidence(0.7)
            .with_confidence_threshold(0.7)
            .with_expiration(24)
            .with_description("Detect liquidity anomalies")
            .build()
        )

        # Add rules to pipeline
        for rule in rules:
            pipeline.add_rule(rule)

        # Save rules to file
        pipeline.save_rules("src/ml/rules/default_rules.json")

        logger.info(f"Created {len(rules)} default alert rules")
        return True

    except Exception as e:
        logger.error(f"Error setting up default rules: {e}")
        return False


def setup_liquidity_ml_integration() -> bool:
    """
    Set up the liquidity ML integration components.

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create necessary directories
        # Use absolute paths to ensure correct directory structure
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../.."))
        liquidity_ml_dirs = [
            os.path.join(project_root, "ml/models/liquidity"),
            os.path.join(project_root, "ml/features/liquidity")
        ]

        for directory in liquidity_ml_dirs:
            os.makedirs(directory, exist_ok=True)
            logger.info(f"Created directory: {directory}")

        # Copy liquidity ML files to appropriate locations
        from shutil import copyfile

        # Get current directory
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # Source files with absolute paths for destinations
        source_files = {
            "liquidity_features.py": os.path.join(project_root, "ml/features/liquidity/"),
            "liquidity_prediction.py": os.path.join(project_root, "ml/models/liquidity/"),
            "ml_liquidity_integration.py": os.path.join(project_root, "ml/")
        }

        # Copy files
        for source_file, dest_dir in source_files.items():
            source_path = os.path.join(current_dir, source_file)
            dest_path = os.path.join(dest_dir, source_file)

            if os.path.exists(source_path):
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                copyfile(source_path, dest_path)
                logger.info(f"Copied {source_file} to {dest_dir}")
            else:
                logger.warning(f"Source file not found: {source_path}")

        # Create integration module
        integration_code = """
from ml.features.liquidity.liquidity_features import LiquidityFeatureExtractor
from ml.models.liquidity.liquidity_prediction import LiquidityPredictionEngine
from ml.ml_liquidity_integration import MLLiquidityIntegration

# Create singleton instances
_feature_extractor = None
_prediction_engine = None
_ml_integration = None

def get_liquidity_feature_extractor(config=None):
    global _feature_extractor
    if _feature_extractor is None:
        _feature_extractor = LiquidityFeatureExtractor(config)
    return _feature_extractor

def get_liquidity_prediction_engine(config=None):
    global _prediction_engine
    if _prediction_engine is None:
        _prediction_engine = LiquidityPredictionEngine(config)
        _prediction_engine.initialize()
    return _prediction_engine

def get_ml_liquidity_integration(config=None):
    global _ml_integration
    if _ml_integration is None:
        _ml_integration = MLLiquidityIntegration(config)
        _ml_integration.initialize()
    return _ml_integration

def enhance_liquidity_analysis(price_data, liquidity_levels, options_data=None, volume_profile=None, gex_data=None):
    integration = get_ml_liquidity_integration()
    return integration.enhance_liquidity_analysis(
        price_data=price_data,
        liquidity_levels=liquidity_levels,
        options_data=options_data,
        volume_profile=volume_profile,
        gex_data=gex_data
    )
"""

        # Write integration module
        integration_module_path = os.path.join(project_root, "ml/liquidity_integration.py")
        with open(integration_module_path, "w") as f:
            f.write(integration_code)

        logger.info("Created liquidity ML integration module")
        return True

    except Exception as e:
        logger.error(f"Error setting up liquidity ML integration: {e}")
        return False


def main():
    """Main integration function."""
    parser = argparse.ArgumentParser(description="Integrate ML components into the Liquidity Strategy Dashboard")
    parser.add_argument('--no-checks', action='store_true', help="Skip dependency checks")
    parser.add_argument('--no-modify', action='store_true', help="Skip modifying dashboard file")
    parser.add_argument('--liquidity-only', action='store_true', help="Only set up liquidity ML integration")
    args = parser.parse_args()

    logger.info("Starting ML integration process")

    # Check dependencies
    if not args.no_checks and not check_dependencies():
        logger.error("Dependency check failed. Please install required packages.")
        return False

    # If liquidity-only flag is set, only set up liquidity ML integration
    if args.liquidity_only:
        logger.info("Setting up liquidity ML integration only")
        if not setup_liquidity_ml_integration():
            logger.error("Failed to set up liquidity ML integration")
            return False

        logger.info("Liquidity ML integration completed successfully")
        return True

    # Set up directories
    if not setup_ml_directories():
        logger.error("Failed to set up ML directories")
        return False

    # Create ML configuration
    if not create_ml_config():
        logger.error("Failed to create ML configuration")
        return False

    # Set up default rules
    if not setup_default_rules():
        logger.error("Failed to set up default rules")
        return False

    # Set up liquidity ML integration
    if not setup_liquidity_ml_integration():
        logger.error("Failed to set up liquidity ML integration")
        return False

    # Modify dashboard file
    if not args.no_modify and not modify_dashboard_file():
        logger.error("Failed to modify dashboard file")
        return False

    logger.info("ML integration completed successfully")
    logger.info("To use the ML features, relaunch the Liquidity Strategy Dashboard")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
