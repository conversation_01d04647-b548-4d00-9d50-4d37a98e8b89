"""
Machine Learning Models for Pattern Recognition and Price Prediction

This module contains models for pattern recognition and price prediction,
using both ML-based and rule-based approaches for robustness.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import time
import logging
import numpy as np
import pandas as pd
import json
from typing import Dict, Any, List, Optional, Tuple, Union, Callable

# Conditional imports for optional dependencies
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import Dataset, DataLoader, TensorDataset
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

try:
    import shap
    HAS_SHAP = True
except ImportError:
    HAS_SHAP = False

# Internal imports
from ml_feature_engineering import FeatureEngineer
from ml_model_registry import get_model_registry
from ml_config import get_component_config
from ml_logging import get_logger

# Setup logger
logger = get_logger('ml_model')

# Utility functions
def format_time_elapsed(start_time):
    """Format time elapsed since start_time."""
    elapsed = time.time() - start_time
    if elapsed < 60:
        return f"{elapsed:.2f} seconds"
    elif elapsed < 3600:
        minutes = int(elapsed / 60)
        seconds = elapsed % 60
        return f"{minutes}m {seconds:.2f}s"
    else:
        hours = int(elapsed / 3600)
        minutes = int((elapsed % 3600) / 60)
        return f"{hours}h {minutes}m"

# ML Models
class PatternRecognitionModel:
    """Pattern recognition model for detecting market patterns."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the pattern recognition model.

        Args:
            config: Optional configuration dictionary
        """
        # Get configuration from config manager if not provided
        self.config = config or get_component_config("pattern_recognition")
        self.initialized = False
        self.feature_engineer = FeatureEngineer(self.config.get('feature_config'))
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu") if HAS_TORCH else None
        self.input_size = self.config.get('input_size', 64)
        self.sequence_length = self.config.get('sequence_length', 50)
        self.batch_size = self.config.get('batch_size', 32)
        self.model_type = self.config.get('model_type', 'transformer')

        # Thresholds for pattern detection
        self.thresholds = self.config.get('thresholds', {
            'double_top': 0.7,
            'double_bottom': 0.7,
            'head_shoulders': 0.8,
            'inv_head_shoulders': 0.8,
            'triangle': 0.6,
            'wedge': 0.6,
            'channel': 0.6,
            'flag': 0.7,
            'bull_flag': 0.7,
            'bear_flag': 0.7,
            'bull_trap': 0.75,
            'bear_trap': 0.75,
            'breakout': 0.65,
            'breakdown': 0.65
        })

        # Feature importance
        self.feature_importance = None

        logger.info("PatternRecognitionModel initialized with config: %s", self.config)

    def build_model(self) -> 'PatternRecognitionModel':
        """
        Build and prepare the model for predictions.

        Returns:
            Self for method chaining
        """
        start_time = time.time()
        logger.info("Building PatternRecognitionModel")

        if not HAS_TORCH or self.model_type == 'rules_only':
            logger.warning("PyTorch not available or rules_only selected, using rule-based patterns only")
            self.initialized = True
            elapsed = format_time_elapsed(start_time)
            logger.info(f"Rule-based PatternRecognitionModel built in {elapsed}")
            return self

        # Build PyTorch model
        try:
            # Create appropriate model based on model_type
            if self.model_type == 'transformer':
                self.model = TransformerPatternDetector(
                    input_size=self.input_size,
                    d_model=self.config.get('d_model', 64),
                    nhead=self.config.get('nhead', 4),
                    num_layers=self.config.get('num_layers', 2),
                    dropout=self.config.get('dropout', 0.2)
                )
            else:
                logger.warning(f"Unknown model type '{self.model_type}', using LSTM")
                self.model = LSTMPatternDetector(
                    input_size=self.input_size,
                    hidden_size=self.config.get('hidden_size', 64),
                    num_layers=self.config.get('num_layers', 2),
                    dropout=self.config.get('dropout', 0.2)
                )

            # Move model to appropriate device
            self.model = self.model.to(self.device)

            # Initialize optimizer for training (if needed)
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config.get('learning_rate', 0.001),
                weight_decay=self.config.get('weight_decay', 1e-5)
            )

            # Set to evaluation mode for inference
            self.model.eval()
            self.initialized = True

            elapsed = format_time_elapsed(start_time)
            logger.info(f"PyTorch-based PatternRecognitionModel ({self.model_type}) built in {elapsed}")

        except Exception as e:
            logger.error(f"Error building PyTorch model: {e}")
            logger.warning("Falling back to rule-based pattern recognition")
            self.initialized = True

        return self

    def prepare_data(self, data: pd.DataFrame) -> Optional[torch.Tensor]:
        """
        Prepare data for model input.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Tensor prepared for model input or None if preparation fails
        """
        if not HAS_TORCH:
            return None

        try:
            # Import feature compatibility module
            from feature_compatibility import align_features

            # Get required features from model metadata
            required_features = self._get_required_features()

            # Extract features using the feature engineer
            features = self.feature_engineer.extract_all_features(
                data,
                transform=True,
                transform_method='standard'
            )

            if features.empty:
                logger.warning("No features extracted from data")
                return None

            # Align features with model requirements
            if required_features:
                logger.info(f"Aligning {len(features.columns)} available features with {len(required_features)} required features")
                features = align_features(features, required_features)
                logger.info(f"After alignment: {len(features.columns)} features")

            # Use only the most recent sequence_length data points
            if len(features) > self.sequence_length:
                features = features.iloc[-self.sequence_length:]

            # Convert to tensor
            feature_tensor = torch.tensor(
                features.values,
                dtype=torch.float32,
                device=self.device
            )

            # Add batch dimension if needed
            if len(feature_tensor.shape) == 2:
                feature_tensor = feature_tensor.unsqueeze(0)  # (1, sequence_length, n_features)

            return feature_tensor

        except Exception as e:
            logger.error(f"Error preparing data for model: {e}")
            return None

    def _get_required_features(self) -> List[str]:
        """
        Get the list of features required by the model.

        Returns:
            List of feature names required by the model
        """
        # Try to get features from model metadata
        try:
            # Check if model has metadata with features
            if hasattr(self.model, 'metadata') and isinstance(self.model.metadata, dict):
                if 'features' in self.model.metadata:
                    return self.model.metadata['features']

            # Try to load from model registry
            model_registry = get_model_registry()
            model_info = model_registry.get_model_info(self.config.get('model_name', 'pattern_recognition'))

            if model_info and 'features' in model_info:
                return model_info['features']

            # Try to load from file
            model_dir = os.path.dirname(os.path.abspath(__file__))
            features_file = os.path.join(model_dir, 'models', 'current_features.txt')

            if os.path.exists(features_file):
                with open(features_file, 'r') as f:
                    return [line.strip() for line in f if line.strip()]

        except Exception as e:
            logger.warning(f"Error getting required features: {e}")

        # Return empty list if no features found
        return []

    def predict_with_ml(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict patterns using ML model.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Dictionary with prediction results
        """
        if not self.initialized or not HAS_TORCH or self.model is None:
            logger.warning("ML model not initialized, falling back to rule-based prediction")
            return self.predict_with_rules(data)

        start_time = time.time()

        try:
            # Prepare data for model
            feature_tensor = self.prepare_data(data)

            if feature_tensor is None:
                logger.warning("Failed to prepare data for ML model, falling back to rule-based prediction")
                return self.predict_with_rules(data)

            # Get model predictions
            with torch.no_grad():
                pattern_probs = self.model(feature_tensor)

            # Convert probabilities to patterns
            patterns = []

            for pattern_name, probs in pattern_probs.items():
                # Get probability from tensor
                prob = float(probs[0][0].cpu().numpy())

                # Check against threshold
                threshold = self.thresholds.get(pattern_name, 0.7)
                if prob >= threshold:
                    # Find potential location of pattern
                    # For simplicity, we'll say it's at the end of the sequence
                    index = len(data) - 1
                    price = float(data['close'].iloc[-1])

                    patterns.append({
                        'type': pattern_name,
                        'price': price,
                        'confidence': prob,
                        'index': index
                    })

            # Sort patterns by confidence
            patterns = sorted(patterns, key=lambda x: x['confidence'], reverse=True)

            elapsed = format_time_elapsed(start_time)
            logger.info(f"ML pattern prediction completed in {elapsed}, found {len(patterns)} patterns")

            # Calculate overall confidence as max pattern confidence
            overall_confidence = max([p['confidence'] for p in patterns]) if patterns else 0.0

            return {
                'patterns': patterns,
                'confidence': overall_confidence,
                'processing_time': elapsed,
                'method': 'ml'
            }

        except Exception as e:
            logger.error(f"Error in ML pattern prediction: {e}")
            logger.warning("Falling back to rule-based prediction")
            return self.predict_with_rules(data)

    def predict_with_rules(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict patterns using rule-based approach.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Dictionary with prediction results
        """
        start_time = time.time()
        patterns = []

        try:
            # Only process if we have enough data
            if isinstance(data, pd.DataFrame) and len(data) > 20:
                # Look for potential double tops/bottoms (simplified)
                highs = data['high'].rolling(5).max()
                lows = data['low'].rolling(5).min()

                # Simple pattern detection logic
                for i in range(10, len(data)-5):
                    # Double top detection (simplified)
                    if (highs.iloc[i] > highs.iloc[i-5:i].mean() * 1.02 and
                        highs.iloc[i] > highs.iloc[i+1:i+5].mean() * 1.02):
                        patterns.append({
                            'type': 'double_top',
                            'price': float(highs.iloc[i]),
                            'confidence': 0.6,
                            'index': i
                        })

                    # Double bottom detection (simplified)
                    if (lows.iloc[i] < lows.iloc[i-5:i].mean() * 0.98 and
                        lows.iloc[i] < lows.iloc[i+1:i+5].mean() * 0.98):
                        patterns.append({
                            'type': 'double_bottom',
                            'price': float(lows.iloc[i]),
                            'confidence': 0.6,
                            'index': i
                        })

                # Look for bull/bear traps (simplified)
                for i in range(15, len(data)-5):
                    # Bull trap detection (price breaks above resistance then falls)
                    if (data['close'].iloc[i] > highs.iloc[i-10:i-1].max() and
                        data['close'].iloc[i+5] < data['close'].iloc[i]):
                        patterns.append({
                            'type': 'bull_trap',
                            'price': float(data['close'].iloc[i]),
                            'confidence': 0.7,
                            'index': i
                        })

                    # Bear trap detection (price breaks below support then rises)
                    if (data['close'].iloc[i] < lows.iloc[i-10:i-1].min() and
                        data['close'].iloc[i+5] > data['close'].iloc[i]):
                        patterns.append({
                            'type': 'bear_trap',
                            'price': float(data['close'].iloc[i]),
                            'confidence': 0.7,
                            'index': i
                        })

                # Detect breakouts and breakdowns
                for i in range(20, len(data)-5):
                    # Calculate resistance and support
                    resistance = highs.iloc[i-20:i-1].max()
                    support = lows.iloc[i-20:i-1].min()

                    # Breakout detection
                    if (data['close'].iloc[i] > resistance and
                        data['close'].iloc[i+5] > data['close'].iloc[i]):
                        patterns.append({
                            'type': 'breakout',
                            'price': float(data['close'].iloc[i]),
                            'confidence': 0.65,
                            'index': i
                        })

                    # Breakdown detection
                    if (data['close'].iloc[i] < support and
                        data['close'].iloc[i+5] < data['close'].iloc[i]):
                        patterns.append({
                            'type': 'breakdown',
                            'price': float(data['close'].iloc[i]),
                            'confidence': 0.65,
                            'index': i
                        })

            elapsed = format_time_elapsed(start_time)
            logger.info(f"Rule-based pattern prediction completed in {elapsed}, found {len(patterns)} patterns")

            return {
                'patterns': patterns,
                'confidence': 0.7 if patterns else 0.0,
                'processing_time': elapsed,
                'method': 'rule_based'
            }
        except Exception as e:
            logger.error(f"Error in rule-based pattern prediction: {e}")
            return {'patterns': [], 'confidence': 0.0, 'method': 'rule_based'}

    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict patterns in the given price data.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Dictionary with prediction results
        """
        if not self.initialized:
            self.build_model()

        # Use ML prediction if PyTorch is available and model is built
        if HAS_TORCH and self.model is not None:
            return self.predict_with_ml(data)
        else:
            return self.predict_with_rules(data)

    def train(self, train_data: pd.DataFrame, labels: Dict[str, List[int]],
              validation_data: Optional[pd.DataFrame] = None,
              validation_labels: Optional[Dict[str, List[int]]] = None,
              epochs: int = 50, patience: int = 5) -> Dict[str, Any]:
        """
        Train the model on labeled data.

        Args:
            train_data: Training data
            labels: Dictionary mapping pattern types to lists of indices
            validation_data: Optional validation data
            validation_labels: Optional validation labels
            epochs: Number of training epochs
            patience: Early stopping patience

        Returns:
            Dictionary with training metrics
        """
        if not HAS_TORCH:
            logger.error("PyTorch not available, cannot train model")
            return {'success': False, 'error': 'PyTorch not available'}

        if not self.initialized:
            self.build_model()

        if self.model is None:
            logger.error("Model not initialized")
            return {'success': False, 'error': 'Model not initialized'}

        start_time = time.time()
        logger.info("Training PatternRecognitionModel")

        try:
            # Prepare training data
            train_features = self.feature_engineer.extract_all_features(
                train_data,
                transform=True,
                transform_method='standard'
            )

            # Create label tensors
            pattern_types = list(self.thresholds.keys())
            train_labels = {}

            for pattern in pattern_types:
                # Create binary label tensor (1 where pattern is present)
                label_tensor = torch.zeros(len(train_data))

                if pattern in labels:
                    label_tensor[labels[pattern]] = 1.0

                train_labels[pattern] = label_tensor

            # Prepare data loaders
            train_dataset = self._create_dataset(train_features, train_labels)
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.batch_size,
                shuffle=True
            )

            # Prepare validation data if provided
            val_loader = None
            if validation_data is not None and validation_labels is not None:
                val_features = self.feature_engineer.extract_all_features(
                    validation_data,
                    transform=True,
                    transform_method='standard'
                )

                val_label_tensors = {}
                for pattern in pattern_types:
                    val_label_tensor = torch.zeros(len(validation_data))

                    if pattern in validation_labels:
                        val_label_tensor[validation_labels[pattern]] = 1.0

                    val_label_tensors[pattern] = val_label_tensor

                val_dataset = self._create_dataset(val_features, val_label_tensors)
                val_loader = DataLoader(
                    val_dataset,
                    batch_size=self.batch_size,
                    shuffle=False
                )

            # Set model to training mode
            self.model.train()

            # Training loop
            best_val_loss = float('inf')
            best_epoch = 0
            epochs_no_improve = 0
            train_losses = []
            val_losses = []

            # Loss function
            criterion = nn.BCELoss()

            for epoch in range(epochs):
                # Training
                self.model.train()
                epoch_loss = 0

                for batch_features, batch_labels in train_loader:
                    # Move data to device
                    batch_features = batch_features.to(self.device)
                    batch_labels = {k: v.to(self.device) for k, v in batch_labels.items()}

                    # Forward pass
                    self.optimizer.zero_grad()
                    predictions = self.model(batch_features)

                    # Calculate loss for each pattern
                    batch_loss = 0
                    for pattern in pattern_types:
                        if pattern in predictions and pattern in batch_labels:
                            pattern_loss = criterion(
                                predictions[pattern],
                                batch_labels[pattern].unsqueeze(1)
                            )
                            batch_loss += pattern_loss

                    # Backward pass
                    batch_loss.backward()
                    self.optimizer.step()

                    epoch_loss += batch_loss.item()

                # Calculate average training loss
                avg_train_loss = epoch_loss / len(train_loader)
                train_losses.append(avg_train_loss)

                # Validation if available
                if val_loader is not None:
                    self.model.eval()
                    val_loss = 0

                    with torch.no_grad():
                        for val_features, val_labels in val_loader:
                            # Move data to device
                            val_features = val_features.to(self.device)
                            val_labels = {k: v.to(self.device) for k, v in val_labels.items()}

                            # Forward pass
                            predictions = self.model(val_features)

                            # Calculate loss for each pattern
                            batch_val_loss = 0
                            for pattern in pattern_types:
                                if pattern in predictions and pattern in val_labels:
                                    pattern_loss = criterion(
                                        predictions[pattern],
                                        val_labels[pattern].unsqueeze(1)
                                    )
                                    batch_val_loss += pattern_loss

                            val_loss += batch_val_loss.item()

                    # Calculate average validation loss
                    avg_val_loss = val_loss / len(val_loader)
                    val_losses.append(avg_val_loss)

                    # Early stopping
                    if avg_val_loss < best_val_loss:
                        best_val_loss = avg_val_loss
                        best_epoch = epoch
                        epochs_no_improve = 0

                        # Save best model
                        self._save_model_state('best_model.pt')
                    else:
                        epochs_no_improve += 1
                        if epochs_no_improve >= patience:
                            logger.info(f"Early stopping at epoch {epoch}")
                            break

                # Log progress
                if val_loader is not None:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")
                else:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {avg_train_loss:.4f}")

            # Load best model if validation was used
            if val_loader is not None:
                self._load_model_state('best_model.pt')

            # Set model to evaluation mode
            self.model.eval()

            # Calculate feature importance if SHAP is available
            if HAS_SHAP:
                self._calculate_feature_importance(train_features)

            elapsed = format_time_elapsed(start_time)
            logger.info(f"Model training completed in {elapsed}")

            return {
                'success': True,
                'train_losses': train_losses,
                'val_losses': val_losses if val_loader is not None else None,
                'best_epoch': best_epoch if val_loader is not None else epochs - 1,
                'training_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error in model training: {e}")
            return {'success': False, 'error': str(e)}

    def _create_dataset(self, features: pd.DataFrame, labels: Dict[str, torch.Tensor]) -> Dataset:
        """
        Create a PyTorch dataset from features and labels.

        Args:
            features: DataFrame with features
            labels: Dictionary mapping pattern types to label tensors

        Returns:
            PyTorch dataset
        """
        # Convert features to tensor
        feature_tensor = torch.tensor(features.values, dtype=torch.float32)

        # Create dataset
        class PatternDataset(Dataset):
            def __init__(self, features, labels):
                self.features = features
                self.labels = labels

            def __len__(self):
                return len(self.features)

            def __getitem__(self, idx):
                feature = self.features[idx]
                label = {k: v[idx] for k, v in self.labels.items()}
                return feature, label

        return PatternDataset(feature_tensor, labels)

    def _calculate_feature_importance(self, features: pd.DataFrame) -> None:
        """
        Calculate feature importance using SHAP.

        Args:
            features: DataFrame with features
        """
        try:
            # Convert model to CPU for SHAP
            model_cpu = self.model.to('cpu')

            # Create background dataset
            background = features.sample(min(100, len(features)))
            background_tensor = torch.tensor(background.values, dtype=torch.float32)

            # Create explainer
            explainer = shap.DeepExplainer(model_cpu, background_tensor)

            # Calculate SHAP values
            shap_values = explainer.shap_values(background_tensor)

            # Store feature importance
            if isinstance(shap_values, list):
                # Average across pattern types
                avg_shap = np.abs(np.mean([sv for sv in shap_values], axis=0))
                importance = np.mean(avg_shap, axis=0)
            else:
                importance = np.mean(np.abs(shap_values), axis=0)

            # Map importance to feature names
            self.feature_importance = dict(zip(features.columns, importance))

            # Move model back to original device
            self.model = self.model.to(self.device)

        except Exception as e:
            logger.error(f"Error calculating feature importance: {e}")

    def _save_model_state(self, path: str) -> None:
        """
        Save model state to disk.

        Args:
            path: Path to save model state
        """
        if self.model is not None:
            try:
                torch.save(self.model.state_dict(), path)
            except Exception as e:
                logger.error(f"Error saving model state: {e}")

    def _load_model_state(self, path: str) -> None:
        """
        Load model state from disk.

        Args:
            path: Path to load model state from
        """
        if self.model is not None and os.path.exists(path):
            try:
                self.model.load_state_dict(torch.load(path, map_location=self.device))
            except Exception as e:
                logger.error(f"Error loading model state: {e}")

    def save(self, version: Optional[str] = None) -> str:
        """
        Save the model to the registry.

        Args:
            version: Optional version string

        Returns:
            Model ID
        """
        # Save model state if PyTorch model exists
        model_path = None
        if HAS_TORCH and self.model is not None:
            model_dir = os.path.join(os.getcwd(), 'models')
            os.makedirs(model_dir, exist_ok=True)
            model_path = os.path.join(model_dir, f"pattern_recognition_{version or 'latest'}.pt")
            self._save_model_state(model_path)

        # Save serialized model and metadata
        registry = get_model_registry()
        model_id = registry.register_model(
            model=self,
            model_name="pattern_recognition",
            model_type="pattern_detection",
            metadata={
                "config": self.config,
                "model_path": model_path,
                "feature_importance": self.feature_importance
            },
            version=version
        )
        logger.info(f"Saved PatternRecognitionModel with ID: {model_id}")
        return model_id

    @classmethod
    def load(cls, model_id: str) -> Tuple['PatternRecognitionModel', Dict[str, Any]]:
        """
        Load a model from the registry.

        Args:
            model_id: ID of the model to load

        Returns:
            Tuple of (model, metadata)
        """
        registry = get_model_registry()
        model, metadata = registry.load_model(model_id, expected_type="pattern_detection")

        # Load model state if available
        if HAS_TORCH and model is not None and 'model_path' in metadata:
            model_path = metadata['model_path']
            if os.path.exists(model_path):
                model._load_model_state(model_path)

        logger.info(f"Loaded PatternRecognitionModel with ID: {model_id}")
        return model, metadata


class PricePredictionModel:
    """Price prediction model for forecasting future price movements."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the price prediction model.

        Args:
            config: Optional configuration dictionary
        """
        # Get configuration from config manager if not provided
        self.config = config or get_component_config("predictive_analytics")
        self.initialized = False
        self.feature_engineer = FeatureEngineer(self.config.get('feature_config'))
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu") if HAS_TORCH else None
        self.input_size = self.config.get('input_size', 64)
        self.output_size = self.config.get('output_size', 1)  # Default to predicting just price
        self.sequence_length = self.config.get('sequence_length', 50)  # Look-back window
        self.prediction_horizon = self.config.get('prediction_horizon', 10)  # Future steps to predict
        self.batch_size = self.config.get('batch_size', 32)
        self.model_type = self.config.get('model_type', 'seq2seq')  # 'seq2seq', 'transformer', or 'simple'

        # Feature importance
        self.feature_importance = None

        logger.info("PricePredictionModel initialized with config: %s", self.config)

    def build_model(self) -> 'PricePredictionModel':
        """
        Build and prepare the model for predictions.

        Returns:
            Self for method chaining
        """
        start_time = time.time()
        logger.info("Building PricePredictionModel")

        if not HAS_TORCH or self.model_type == 'simple':
            logger.warning("Using simple fallback for price prediction")
            self.initialized = True
            elapsed = format_time_elapsed(start_time)
            logger.info(f"Simple PricePredictionModel built in {elapsed}")
            return self

        # Build PyTorch model
        try:
            # Create appropriate model
            if self.model_type == 'seq2seq':
                self.model = SequenceToSequenceModel(
                    input_size=self.input_size,
                    output_size=self.output_size,
                    hidden_size=self.config.get('hidden_size', 64),
                    num_layers=self.config.get('num_layers', 2),
                    dropout=self.config.get('dropout', 0.2)
                )
            elif self.model_type == 'transformer':
                # Use a custom transformer model for sequence prediction
                # Implementation depends on specific requirements
                logger.warning("Transformer model not implemented yet, using sequence-to-sequence model")
                self.model = SequenceToSequenceModel(
                    input_size=self.input_size,
                    output_size=self.output_size,
                    hidden_size=self.config.get('hidden_size', 64),
                    num_layers=self.config.get('num_layers', 2),
                    dropout=self.config.get('dropout', 0.2)
                )
            else:
                logger.warning(f"Unknown model type '{self.model_type}', using sequence-to-sequence")
                self.model = SequenceToSequenceModel(
                    input_size=self.input_size,
                    output_size=self.output_size,
                    hidden_size=self.config.get('hidden_size', 64),
                    num_layers=self.config.get('num_layers', 2),
                    dropout=self.config.get('dropout', 0.2)
                )

            # Move model to appropriate device
            self.model = self.model.to(self.device)

            # Initialize optimizer for training (if needed)
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config.get('learning_rate', 0.001),
                weight_decay=self.config.get('weight_decay', 1e-5)
            )

            # Set to evaluation mode for inference
            self.model.eval()
            self.initialized = True

            elapsed = format_time_elapsed(start_time)
            logger.info(f"PyTorch-based PricePredictionModel ({self.model_type}) built in {elapsed}")

        except Exception as e:
            logger.error(f"Error building PyTorch model: {e}")
            logger.warning("Falling back to simple price prediction")
            self.initialized = True

        return self

    def prepare_data(self, data: pd.DataFrame) -> Optional[torch.Tensor]:
        """
        Prepare data for model input.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Tensor prepared for model input or None if preparation fails
        """
        if not HAS_TORCH:
            return None

        try:
            # Import feature compatibility module
            from feature_compatibility import align_features

            # Get required features from model metadata
            required_features = self._get_required_features()

            # Extract features using the feature engineer
            features = self.feature_engineer.extract_all_features(
                data,
                transform=True,
                transform_method='standard'
            )

            if features.empty:
                logger.warning("No features extracted from data")
                return None

            # Align features with model requirements
            if required_features:
                logger.info(f"Aligning {len(features.columns)} available features with {len(required_features)} required features")
                features = align_features(features, required_features)
                logger.info(f"After alignment: {len(features.columns)} features")

            # Use only the most recent sequence_length data points
            if len(features) > self.sequence_length:
                features = features.iloc[-self.sequence_length:]

            # Convert to tensor
            feature_tensor = torch.tensor(
                features.values,
                dtype=torch.float32,
                device=self.device
            )

            # Add batch dimension if needed
            if len(feature_tensor.shape) == 2:
                feature_tensor = feature_tensor.unsqueeze(0)  # (1, sequence_length, n_features)

            return feature_tensor

        except Exception as e:
            logger.error(f"Error preparing data for model: {e}")
            return None

    def _get_required_features(self) -> List[str]:
        """
        Get the list of features required by the model.

        Returns:
            List of feature names required by the model
        """
        # Try to get features from model metadata
        try:
            # Check if model has metadata with features
            if hasattr(self.model, 'metadata') and isinstance(self.model.metadata, dict):
                if 'features' in self.model.metadata:
                    return self.model.metadata['features']

            # Try to load from model registry
            model_registry = get_model_registry()
            model_info = model_registry.get_model_info(self.config.get('model_name', 'price_prediction'))

            if model_info and 'features' in model_info:
                return model_info['features']

            # Try to load from file
            model_dir = os.path.dirname(os.path.abspath(__file__))
            features_file = os.path.join(model_dir, 'models', 'current_features.txt')

            if os.path.exists(features_file):
                with open(features_file, 'r') as f:
                    return [line.strip() for line in f if line.strip()]

        except Exception as e:
            logger.warning(f"Error getting required features: {e}")

        # Return empty list if no features found
        return []

    def predict_with_ml(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict future prices using ML model.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Dictionary with prediction results
        """
        if not self.initialized or not HAS_TORCH or self.model is None:
            logger.warning("ML model not initialized, falling back to simple prediction")
            return self.predict_with_simple(data)

        start_time = time.time()

        try:
            # Prepare data for model
            feature_tensor = self.prepare_data(data)

            if feature_tensor is None:
                logger.warning("Failed to prepare data for ML model, falling back to simple prediction")
                return self.predict_with_simple(data)

            # Get model predictions
            with torch.no_grad():
                predictions, uncertainties = self.model(feature_tensor, self.prediction_horizon)

            # Convert predictions and uncertainties to numpy arrays
            predictions_np = predictions[0].cpu().numpy()  # (prediction_horizon, output_size)
            uncertainties_np = uncertainties[0].cpu().numpy()  # (prediction_horizon, output_size)

            # Get last price for reference
            last_price = float(data['close'].iloc[-1])

            # Calculate predicted prices and confidence intervals
            predicted_prices = []
            confidence_intervals = []

            for i in range(self.prediction_horizon):
                # First output is assumed to be the price
                pred_price = float(predictions_np[i, 0])
                uncertainty = float(uncertainties_np[i, 0])

                # Calculate 95% confidence interval (2 standard deviations)
                lower_bound = pred_price - 2 * np.sqrt(uncertainty)
                upper_bound = pred_price + 2 * np.sqrt(uncertainty)

                predicted_prices.append(pred_price)
                confidence_intervals.append((lower_bound, upper_bound))

            # Determine trend based on predictions
            if predicted_prices[-1] > last_price:
                trend = "bullish"
            else:
                trend = "bearish"

            # Calculate prediction confidence based on uncertainty
            avg_uncertainty = np.mean([ci[1] - ci[0] for ci in confidence_intervals])
            confidence = 1.0 - min(0.9, avg_uncertainty / (4 * last_price))  # Normalize

            elapsed = format_time_elapsed(start_time)
            logger.info(f"ML price prediction completed in {elapsed}, trend: {trend}")

            return {
                'prediction': predicted_prices,
                'confidence_intervals': confidence_intervals,
                'trend': trend,
                'last_price': last_price,
                'confidence': float(confidence),
                'processing_time': elapsed,
                'method': 'ml'
            }

        except Exception as e:
            logger.error(f"Error in ML price prediction: {e}")
            logger.warning("Falling back to simple prediction")
            return self.predict_with_simple(data)

    def predict_with_simple(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict future prices using simple approach.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Dictionary with prediction results
        """
        start_time = time.time()

        try:
            # Only process if we have enough data
            if isinstance(data, pd.DataFrame) and len(data) > 5:
                # Simple moving average prediction
                last_price = float(data['close'].iloc[-1])
                avg_5d = float(data['close'].iloc[-5:].mean())
                avg_20d = float(data['close'].iloc[-20:].mean()) if len(data) >= 20 else avg_5d

                # Calculate volatility
                volatility = float(data['close'].pct_change().std() * 100)

                # Simple trend prediction
                if avg_5d > avg_20d:
                    trend = "bullish"
                    daily_change = 0.01 + volatility/200  # Adjusted for volatility
                else:
                    trend = "bearish"
                    daily_change = -0.01 - volatility/200  # Adjusted for volatility

                # Predict multiple days ahead
                predicted_prices = []
                confidence_intervals = []

                current_price = last_price
                for i in range(self.prediction_horizon):
                    current_price = current_price * (1 + daily_change)
                    predicted_prices.append(float(current_price))

                    # Simple confidence interval based on volatility
                    ci_width = current_price * volatility / 100 * np.sqrt(i + 1)
                    confidence_intervals.append((current_price - ci_width, current_price + ci_width))

                # Calculate confidence based on trend strength
                trend_strength = abs(avg_5d / avg_20d - 1)
                confidence = min(0.9, 0.5 + trend_strength * 10)

                elapsed = format_time_elapsed(start_time)
                logger.info(f"Simple price prediction completed in {elapsed}, trend: {trend}")

                return {
                    'prediction': predicted_prices,
                    'confidence_intervals': confidence_intervals,
                    'trend': trend,
                    'last_price': last_price,
                    'volatility': volatility,
                    'confidence': float(confidence),
                    'processing_time': elapsed,
                    'method': 'simple'
                }
            return {'prediction': None, 'confidence': 0.0, 'method': 'simple'}
        except Exception as e:
            logger.error(f"Error in simple price prediction: {e}")
            return {'prediction': None, 'confidence': 0.0, 'method': 'simple'}

    def predict(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict future prices based on the given data.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            Dictionary with prediction results
        """
        if not self.initialized:
            self.build_model()

        # Use ML prediction if PyTorch is available and model is built
        if HAS_TORCH and self.model is not None:
            return self.predict_with_ml(data)
        else:
            return self.predict_with_simple(data)

    def train(self, train_data: pd.DataFrame, target_data: pd.DataFrame,
              validation_data: Optional[pd.DataFrame] = None,
              validation_target: Optional[pd.DataFrame] = None,
              epochs: int = 50, patience: int = 5) -> Dict[str, Any]:
        """
        Train the model on historical data.

        Args:
            train_data: Training input data
            target_data: Training target data (future prices)
            validation_data: Optional validation input data
            validation_target: Optional validation target data
            epochs: Number of training epochs
            patience: Early stopping patience

        Returns:
            Dictionary with training metrics
        """
        if not HAS_TORCH:
            logger.error("PyTorch not available, cannot train model")
            return {'success': False, 'error': 'PyTorch not available'}

        if not self.initialized:
            self.build_model()

        if self.model is None:
            logger.error("Model not initialized")
            return {'success': False, 'error': 'Model not initialized'}

        start_time = time.time()
        logger.info("Training PricePredictionModel")

        try:
            # Prepare training data
            train_features = self.feature_engineer.extract_all_features(
                train_data,
                transform=True,
                transform_method='standard'
            )

            # Prepare target data (future prices)
            target_tensor = torch.tensor(target_data.values, dtype=torch.float32)

            # Create sliding windows for sequence prediction
            train_windows, target_windows = self._create_sliding_windows(
                train_features.values, target_data.values, self.sequence_length, self.prediction_horizon
            )

            # Create PyTorch dataset
            train_dataset = TensorDataset(
                torch.tensor(train_windows, dtype=torch.float32),
                torch.tensor(target_windows, dtype=torch.float32)
            )

            train_loader = DataLoader(
                train_dataset,
                batch_size=self.batch_size,
                shuffle=True
            )

            # Prepare validation data if provided
            val_loader = None
            if validation_data is not None and validation_target is not None:
                val_features = self.feature_engineer.extract_all_features(
                    validation_data,
                    transform=True,
                    transform_method='standard'
                )

                val_windows, val_target_windows = self._create_sliding_windows(
                    val_features.values, validation_target.values, self.sequence_length, self.prediction_horizon
                )

                val_dataset = TensorDataset(
                    torch.tensor(val_windows, dtype=torch.float32),
                    torch.tensor(val_target_windows, dtype=torch.float32)
                )

                val_loader = DataLoader(
                    val_dataset,
                    batch_size=self.batch_size,
                    shuffle=False
                )

            # Set model to training mode
            self.model.train()

            # Training loop
            best_val_loss = float('inf')
            best_epoch = 0
            epochs_no_improve = 0
            train_losses = []
            val_losses = []

            # Loss function (MSE with uncertainty regularization)
            def gaussian_nll_loss(pred, target, variance):
                """Gaussian Negative Log Likelihood Loss with uncertainty"""
                return 0.5 * (torch.log(variance) + ((pred - target) ** 2) / variance).mean()

            for epoch in range(epochs):
                # Training
                self.model.train()
                epoch_loss = 0

                for batch_features, batch_targets in train_loader:
                    # Move data to device
                    batch_features = batch_features.to(self.device)
                    batch_targets = batch_targets.to(self.device)

                    # Forward pass
                    self.optimizer.zero_grad()
                    predictions, uncertainties = self.model(batch_features)

                    # Calculate loss
                    loss = gaussian_nll_loss(predictions, batch_targets, uncertainties)

                    # Backward pass
                    loss.backward()
                    self.optimizer.step()

                    epoch_loss += loss.item()

                # Calculate average training loss
                avg_train_loss = epoch_loss / len(train_loader)
                train_losses.append(avg_train_loss)

                # Validation if available
                if val_loader is not None:
                    self.model.eval()
                    val_loss = 0

                    with torch.no_grad():
                        for val_features, val_targets in val_loader:
                            # Move data to device
                            val_features = val_features.to(self.device)
                            val_targets = val_targets.to(self.device)

                            # Forward pass
                            predictions, uncertainties = self.model(val_features)

                            # Calculate loss
                            loss = gaussian_nll_loss(predictions, val_targets, uncertainties)
                            val_loss += loss.item()

                    # Calculate average validation loss
                    avg_val_loss = val_loss / len(val_loader)
                    val_losses.append(avg_val_loss)

                    # Early stopping
                    if avg_val_loss < best_val_loss:
                        best_val_loss = avg_val_loss
                        best_epoch = epoch
                        epochs_no_improve = 0

                        # Save best model
                        self._save_model_state('best_price_model.pt')
                    else:
                        epochs_no_improve += 1
                        if epochs_no_improve >= patience:
                            logger.info(f"Early stopping at epoch {epoch}")
                            break

                # Log progress
                if val_loader is not None:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")
                else:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {avg_train_loss:.4f}")

            # Load best model if validation was used
            if val_loader is not None:
                self._load_model_state('best_price_model.pt')

            # Set model to evaluation mode
            self.model.eval()

            # Calculate feature importance if SHAP is available
            if HAS_SHAP:
                self._calculate_feature_importance(train_features)

            elapsed = format_time_elapsed(start_time)
            logger.info(f"Model training completed in {elapsed}")

            return {
                'success': True,
                'train_losses': train_losses,
                'val_losses': val_losses if val_loader is not None else None,
                'best_epoch': best_epoch if val_loader is not None else epochs - 1,
                'training_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error in model training: {e}")
            return {'success': False, 'error': str(e)}

    def _create_sliding_windows(self, features: np.ndarray, targets: np.ndarray,
                               window_size: int, prediction_horizon: int) -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sliding windows for sequence prediction.

        Args:
            features: Feature array
            targets: Target array
            window_size: Size of input window
            prediction_horizon: Number of future steps to predict

        Returns:
            Tuple of (input_windows, target_windows)
        """
        n_samples = len(features) - window_size - prediction_horizon + 1

        if n_samples <= 0:
            raise ValueError(f"Not enough data for window_size={window_size} and prediction_horizon={prediction_horizon}")

        # Create input windows
        input_windows = np.zeros((n_samples, window_size, features.shape[1]))
        for i in range(n_samples):
            input_windows[i] = features[i:i+window_size]

        # Create target windows (future values)
        target_windows = np.zeros((n_samples, prediction_horizon, targets.shape[1]))
        for i in range(n_samples):
            target_windows[i] = targets[i+window_size:i+window_size+prediction_horizon]

        return input_windows, target_windows

    def _calculate_feature_importance(self, features: pd.DataFrame) -> None:
        """
        Calculate feature importance using SHAP.

        Args:
            features: DataFrame with features
        """
        try:
            # Convert model to CPU for SHAP
            model_cpu = self.model.to('cpu')

            # Create background dataset
            background = features.sample(min(100, len(features)))
            background_tensor = torch.tensor(background.values, dtype=torch.float32)

            # Add batch dimension
            if len(background_tensor.shape) == 2:
                background_tensor = background_tensor.unsqueeze(0)

            # Create wrapper for SHAP
            class ModelWrapper(nn.Module):
                def __init__(self, model):
                    super().__init__()
                    self.model = model

                def forward(self, x):
                    predictions, _ = self.model(x)
                    return predictions

            wrapped_model = ModelWrapper(model_cpu)

            # Create explainer
            explainer = shap.DeepExplainer(wrapped_model, background_tensor)

            # Calculate SHAP values
            shap_values = explainer.shap_values(background_tensor)

            # Store feature importance
            if isinstance(shap_values, list):
                # Average across outputs and time steps
                avg_shap = np.abs(np.mean([sv for sv in shap_values], axis=0))
                importance = np.mean(avg_shap, axis=(0, 1))
            else:
                importance = np.mean(np.abs(shap_values), axis=(0, 1))

            # Map importance to feature names
            self.feature_importance = dict(zip(features.columns, importance))

            # Move model back to original device
            self.model = self.model.to(self.device)

        except Exception as e:
            logger.error(f"Error calculating feature importance: {e}")

    def _save_model_state(self, path: str) -> None:
        """
        Save model state to disk.

        Args:
            path: Path to save model state
        """
        if self.model is not None:
            try:
                torch.save(self.model.state_dict(), path)
            except Exception as e:
                logger.error(f"Error saving model state: {e}")

    def _load_model_state(self, path: str) -> None:
        """
        Load model state from disk.

        Args:
            path: Path to load model state from
        """
        if self.model is not None and os.path.exists(path):
            try:
                self.model.load_state_dict(torch.load(path, map_location=self.device))
            except Exception as e:
                logger.error(f"Error loading model state: {e}")

    def save(self, version: Optional[str] = None) -> str:
        """
        Save the model to the registry.

        Args:
            version: Optional version string

        Returns:
            Model ID
        """
        # Save model state if PyTorch model exists
        model_path = None
        if HAS_TORCH and self.model is not None:
            model_dir = os.path.join(os.getcwd(), 'models')
            os.makedirs(model_dir, exist_ok=True)
            model_path = os.path.join(model_dir, f"price_prediction_{version or 'latest'}.pt")
            self._save_model_state(model_path)

        # Save serialized model and metadata
        registry = get_model_registry()
        model_id = registry.register_model(
            model=self,
            model_name="price_prediction",
            model_type="predictive_analytics",
            metadata={
                "config": self.config,
                "model_path": model_path,
                "feature_importance": self.feature_importance
            },
            version=version
        )
        logger.info(f"Saved PricePredictionModel with ID: {model_id}")
        return model_id

    @classmethod
    def load(cls, model_id: str) -> Tuple['PricePredictionModel', Dict[str, Any]]:
        """
        Load a model from the registry.

        Args:
            model_id: ID of the model to load

        Returns:
            Tuple of (model, metadata)
        """
        registry = get_model_registry()
        model, metadata = registry.load_model(model_id, expected_type="predictive_analytics")

        # Load model state if available
        if HAS_TORCH and model is not None and 'model_path' in metadata:
            model_path = metadata['model_path']
            if os.path.exists(model_path):
                model._load_model_state(model_path)

        logger.info(f"Loaded PricePredictionModel with ID: {model_id}")
        return model, metadata


# Factory functions for creating model instances
def create_pattern_recognition_model(config: Optional[Dict[str, Any]] = None) -> PatternRecognitionModel:
    """
    Create and initialize a pattern recognition model.

    Args:
        config: Optional configuration dictionary

    Returns:
        Initialized PatternRecognitionModel
    """
    return PatternRecognitionModel(config).build_model()


def create_price_prediction_model(config: Optional[Dict[str, Any]] = None) -> PricePredictionModel:
    """
    Create and initialize a price prediction model.

    Args:
        config: Optional configuration dictionary

    Returns:
        Initialized PricePredictionModel
    """
    return PricePredictionModel(config).build_model()
