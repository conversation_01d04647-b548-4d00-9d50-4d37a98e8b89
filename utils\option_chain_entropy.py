#!/usr/bin/env python3
"""
Option-Chain Entropy Feature Calculator
Analyzes open-interest distribution to detect unusual convexity patterns
Feeds into Agent Zero for squeeze prediction
"""

import pandas as pd
import numpy as np
import scipy.stats as stats
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime, date
from pathlib import Path

logger = logging.getLogger(__name__)

class OptionChainEntropy:
    """
    Option chain entropy and convexity analysis
    Mathematical rigor: Statistical entropy, KL divergence, regime detection
    """
    
    def __init__(self):
        self.min_oi_threshold = 10  # Minimum OI for analysis
        self.historical_lookback = 20  # Days of historical data for comparison
        
    def calculate_oi_entropy(self, opts_df: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate Shannon entropy of open interest distribution
        Higher entropy = more distributed OI, Lower entropy = concentrated OI
        """
        try:
            if opts_df.empty or 'open_interest' not in opts_df.columns:
                return {'entropy': 0.0, 'normalized_entropy': 0.0}
            
            # Filter for liquid options
            liquid_opts = opts_df[opts_df['open_interest'] >= self.min_oi_threshold].copy()
            
            if len(liquid_opts) < 3:
                return {'entropy': 0.0, 'normalized_entropy': 0.0}
            
            # Get OI distribution
            oi_values = liquid_opts['open_interest'].values
            
            # Normalize to probability distribution
            oi_probs = oi_values / oi_values.sum()
            
            # Calculate Shannon entropy
            entropy = stats.entropy(oi_probs, base=2)
            
            # Normalize by maximum possible entropy (uniform distribution)
            max_entropy = np.log2(len(oi_probs))
            normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0.0
            
            return {
                'entropy': entropy,
                'normalized_entropy': normalized_entropy,
                'active_strikes': len(liquid_opts),
                'total_oi': oi_values.sum()
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate OI entropy: {e}")
            return {'entropy': 0.0, 'normalized_entropy': 0.0}
    
    def detect_gamma_walls(self, opts_df: pd.DataFrame, underlying_px: float) -> Dict[str, any]:
        """
        Detect gamma walls (high gamma concentration at specific strikes)
        Key indicator for potential squeezes
        """
        try:
            if opts_df.empty or 'gamma' not in opts_df.columns:
                return {'gamma_walls': [], 'max_gamma_strike': None}
            
            # Calculate net gamma by strike (calls - puts)
            call_gamma = opts_df[opts_df['type'] == 'CALL'].groupby('strike')['gamma'].sum()
            put_gamma = opts_df[opts_df['type'] == 'PUT'].groupby('strike')['gamma'].sum()
            
            # Net gamma (positive = call heavy, negative = put heavy)
            all_strikes = set(call_gamma.index) | set(put_gamma.index)
            net_gamma = pd.Series({
                strike: call_gamma.get(strike, 0) - put_gamma.get(strike, 0)
                for strike in all_strikes
            })
            
            # Find significant gamma concentrations
            gamma_threshold = net_gamma.std() * 2  # 2 standard deviations
            significant_strikes = net_gamma[abs(net_gamma) > gamma_threshold]
            
            # Classify as walls
            gamma_walls = []
            for strike, gamma in significant_strikes.items():
                distance_pct = (strike - underlying_px) / underlying_px
                
                wall_info = {
                    'strike': strike,
                    'net_gamma': gamma,
                    'distance_pct': distance_pct,
                    'type': 'call_wall' if gamma > 0 else 'put_wall',
                    'significance': abs(gamma) / gamma_threshold
                }
                gamma_walls.append(wall_info)
            
            # Sort by proximity to current price
            gamma_walls.sort(key=lambda x: abs(x['distance_pct']))
            
            # Find maximum gamma strike
            max_gamma_strike = net_gamma.abs().idxmax() if not net_gamma.empty else None
            
            return {
                'gamma_walls': gamma_walls[:5],  # Top 5 closest walls
                'max_gamma_strike': max_gamma_strike,
                'total_net_gamma': net_gamma.sum(),
                'gamma_concentration': net_gamma.std() / net_gamma.mean() if net_gamma.mean() != 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Failed to detect gamma walls: {e}")
            return {'gamma_walls': [], 'max_gamma_strike': None}
    
    def calculate_squeeze_probability(self, opts_df: pd.DataFrame, underlying_px: float, 
                                    historical_baseline: Optional[Dict] = None) -> Dict[str, any]:
        """
        Calculate probability of an upcoming squeeze based on option chain patterns
        Combines entropy, gamma walls, and unusual activity
        """
        try:
            # Component analyses
            entropy_metrics = self.calculate_oi_entropy(opts_df)
            gamma_analysis = self.detect_gamma_walls(opts_df, underlying_px)
            
            # Calculate call/put ratio
            call_oi = opts_df[opts_df['type'] == 'CALL']['open_interest'].sum()
            put_oi = opts_df[opts_df['type'] == 'PUT']['open_interest'].sum()
            cp_ratio = call_oi / put_oi if put_oi > 0 else float('inf')
            
            # Squeeze indicators (0-1 scale)
            indicators = {}
            
            # 1. Low entropy = concentrated OI (squeeze setup)
            indicators['concentration'] = 1 - entropy_metrics['normalized_entropy']
            
            # 2. Strong gamma walls near current price
            nearest_wall = None
            wall_strength = 0.0
            
            for wall in gamma_analysis['gamma_walls']:
                if abs(wall['distance_pct']) < 0.05:  # Within 5%
                    wall_strength = max(wall_strength, wall['significance'])
                    if not nearest_wall or abs(wall['distance_pct']) < abs(nearest_wall['distance_pct']):
                        nearest_wall = wall
            
            indicators['gamma_pressure'] = min(wall_strength / 3.0, 1.0)  # Normalize
            
            # 3. Call/put imbalance
            if cp_ratio == float('inf'):
                cp_imbalance = 1.0
            else:
                # Extreme ratios (>3 or <0.33) indicate imbalance
                cp_imbalance = max(0, min(1.0, abs(np.log(cp_ratio)) / np.log(3)))
            indicators['call_put_imbalance'] = cp_imbalance
            
            # Weighted squeeze probability
            weights = {
                'concentration': 0.40,
                'gamma_pressure': 0.40,
                'call_put_imbalance': 0.20
            }
            
            squeeze_prob = sum(indicators[k] * weights[k] for k in weights.keys())
            
            # Risk level classification
            if squeeze_prob >= 0.7:
                risk_level = 'high'
            elif squeeze_prob >= 0.4:
                risk_level = 'medium'
            else:
                risk_level = 'low'
            
            # Direction prediction based on gamma walls
            direction = 'neutral'
            if nearest_wall:
                if nearest_wall['type'] == 'call_wall' and underlying_px < nearest_wall['strike']:
                    direction = 'upward'  # Price likely to be pulled up
                elif nearest_wall['type'] == 'put_wall' and underlying_px > nearest_wall['strike']:
                    direction = 'downward'  # Price likely to be pushed down
            
            return {
                'squeeze_probability': squeeze_prob,
                'risk_level': risk_level,
                'direction': direction,
                'indicators': indicators,
                'nearest_gamma_wall': nearest_wall,
                'call_put_ratio': cp_ratio,
                'entropy_metrics': entropy_metrics,
                'gamma_analysis': gamma_analysis,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to calculate squeeze probability: {e}")
            return {
                'squeeze_probability': 0.0,
                'risk_level': 'unknown',
                'direction': 'neutral',
                'error': str(e)
            }

# Integration function for Agent Zero
def analyze_option_chain_entropy(symbol: str, opts_df: pd.DataFrame, underlying_px: float) -> Dict[str, any]:
    """
    Main integration function for Agent Zero
    Returns comprehensive option chain entropy analysis
    """
    try:
        analyzer = OptionChainEntropy()
        
        # Comprehensive analysis
        analysis = analyzer.calculate_squeeze_probability(opts_df, underlying_px)
        
        # Add symbol and metadata
        analysis['symbol'] = symbol
        analysis['underlying_price'] = underlying_px
        analysis['total_options_analyzed'] = len(opts_df)
        analysis['analysis_timestamp'] = datetime.now().isoformat()
        
        # Log significant findings
        if analysis['squeeze_probability'] > 0.6:
            logger.warning(f"HIGH SQUEEZE PROBABILITY for {symbol}: {analysis['squeeze_probability']:.1%}")
        elif analysis['squeeze_probability'] > 0.4:
            logger.info(f"Medium squeeze probability for {symbol}: {analysis['squeeze_probability']:.1%}")
        
        return analysis
        
    except Exception as e:
        logger.error(f"Failed to analyze option chain entropy for {symbol}: {e}")
        return {
            'symbol': symbol,
            'squeeze_probability': 0.0,
            'risk_level': 'unknown',
            'error': str(e)
        }

# Agent Zero training data format
def format_for_agent_zero(entropy_analysis: Dict) -> Dict[str, any]:
    """
    Format entropy analysis for Agent Zero consumption
    Structured for pattern recognition and learning
    """
    try:
        return {
            'feature_type': 'option_chain_entropy',
            'symbol': entropy_analysis.get('symbol'),
            'squeeze_probability': entropy_analysis.get('squeeze_probability', 0.0),
            'risk_level': entropy_analysis.get('risk_level', 'unknown'),
            'direction_bias': entropy_analysis.get('direction', 'neutral'),
            'key_indicators': {
                'concentration_score': entropy_analysis.get('indicators', {}).get('concentration', 0.0),
                'gamma_pressure': entropy_analysis.get('indicators', {}).get('gamma_pressure', 0.0),
                'call_put_imbalance': entropy_analysis.get('indicators', {}).get('call_put_imbalance', 0.0)
            },
            'nearest_gamma_wall': entropy_analysis.get('nearest_gamma_wall'),
            'confidence': min(1.0, entropy_analysis.get('squeeze_probability', 0.0) * 1.2),
            'timestamp': entropy_analysis.get('timestamp'),
            'data_quality': 'high' if entropy_analysis.get('total_options_analyzed', 0) > 50 else 'medium'
        }
        
    except Exception as e:
        logger.error(f"Failed to format entropy analysis for Agent Zero: {e}")
        return {
            'feature_type': 'option_chain_entropy',
            'squeeze_probability': 0.0,
            'error': str(e)
        }

if __name__ == "__main__":
    # Test the entropy calculator
    print("Testing Option Chain Entropy Calculator...")
    
    # Create test data
    test_data = []
    
    # Normal distribution
    for i in range(10):
        test_data.append({
            'symbol': f'TEST_C{170+i}',
            'type': 'CALL',
            'strike': 170 + i,
            'open_interest': 1000 + np.random.randint(-200, 200),
            'volume': 100 + np.random.randint(-50, 50),
            'gamma': 0.02 + np.random.normal(0, 0.005),
            'iv': 0.25 + np.random.normal(0, 0.02)
        })
    
    # Add concentrated OI (squeeze setup)
    test_data.append({
        'symbol': 'TEST_C175',
        'type': 'CALL', 
        'strike': 175,
        'open_interest': 5000,  # High concentration
        'volume': 1000,
        'gamma': 0.08,  # High gamma
        'iv': 0.30
    })
    
    test_df = pd.DataFrame(test_data)
    analyzer = OptionChainEntropy()
    
    # Test entropy calculation
    entropy_result = analyzer.calculate_oi_entropy(test_df)
    print(f"OI Entropy: {entropy_result}")
    
    # Test squeeze probability
    squeeze_result = analyzer.calculate_squeeze_probability(test_df, 175.0)
    print(f"Squeeze Probability: {squeeze_result['squeeze_probability']:.1%}")
    print(f"Risk Level: {squeeze_result['risk_level']}")
    
    # Test Agent Zero format
    agent_zero_format = format_for_agent_zero(squeeze_result)
    print(f"Agent Zero Format: {agent_zero_format}")
    
    print("Option chain entropy test completed")
