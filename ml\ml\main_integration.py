"""
Main Integration Module for ML Liquidity Analysis

This module provides the integration between the ML liquidity components
and the main liquidity analysis system.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import sys
import logging
import pandas as pd
from typing import Dict, List, Optional, Any, Union

# Import ML components
from ml_logging import get_logger
from liquidity_features import LiquidityFeatureExtractor
from liquidity_prediction import LiquidityPredictionEngine
from ml_liquidity_integration import MLLiquidityIntegration

# Setup logger
logger = get_logger('main_integration')

class MLLiquidityMainIntegration:
    """
    Main integration class for ML liquidity analysis.

    This class provides methods to integrate ML liquidity analysis
    with the main liquidity analysis system, including consensus builder
    integration and report enhancement.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the main integration.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.ml_integration = MLLiquidityIntegration(config)
        self.initialized = False

        logger.info("Initialized MLLiquidityMainIntegration")

    def initialize(self) -> bool:
        """
        Initialize the integration.

        Returns:
            True if initialization was successful, False otherwise
        """
        if self.initialized:
            logger.info("Main integration already initialized")
            return True

        try:
            # Initialize ML integration
            if not self.ml_integration.initialize():
                logger.error("Failed to initialize ML integration")
                return False

            self.initialized = True
            logger.info("Main integration initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize main integration: {str(e)}")
            return False

    def enhance_consensus_levels(self,
                               consensus_levels: Dict[str, List[Dict[str, Any]]],
                               price_data: pd.DataFrame,
                               options_data: Optional[pd.DataFrame] = None,
                               volume_profile: Optional[Dict[str, Any]] = None,
                               gex_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Enhance consensus levels with ML predictions.

        Args:
            consensus_levels: Dictionary with consensus levels from consensus builder
            price_data: DataFrame with OHLCV price data
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data

        Returns:
            Dictionary with enhanced consensus levels and ML metadata
        """
        # Ensure integration is initialized
        if not self.initialized:
            self.initialize()

        # Check if we have the required data
        if price_data is None or price_data.empty:
            logger.warning("No price data available for ML enhancement")
            return {'enhanced_levels': consensus_levels, 'confidence': 0.0}

        # Ensure we have valid consensus levels
        if not consensus_levels or not isinstance(consensus_levels, dict):
            logger.warning("Invalid consensus levels format for ML enhancement")
            consensus_levels = {'support': [], 'resistance': []}

        # Ensure options_data has required columns for ML features
        if options_data is not None and not options_data.empty:
            # Check for required columns
            required_columns = ['strike_price', 'call_oi', 'put_oi']
            missing_columns = [col for col in required_columns if col not in options_data.columns]

            if missing_columns:
                logger.warning(f"Options data missing required columns for ML enhancement: {missing_columns}")

                # Try to derive missing columns from available data
                if 'strike_price' not in options_data.columns and 'strike' in options_data.columns:
                    logger.info("Using 'strike' column as 'strike_price'")
                    options_data['strike_price'] = options_data['strike']
                    missing_columns.remove('strike_price') if 'strike_price' in missing_columns else None

                # Try to derive call_oi and put_oi from type and open_interest columns
                if ('call_oi' not in options_data.columns or 'put_oi' not in options_data.columns) and 'open_interest' in options_data.columns and 'type' in options_data.columns:
                    logger.info("Deriving call_oi and put_oi from type and open_interest columns")

                    # Create call_oi and put_oi columns
                    options_data['call_oi'] = 0
                    options_data['put_oi'] = 0

                    # Fill in values based on type
                    call_mask = options_data['type'].str.lower() == 'call'
                    put_mask = options_data['type'].str.lower() == 'put'

                    options_data.loc[call_mask, 'call_oi'] = options_data.loc[call_mask, 'open_interest']
                    options_data.loc[put_mask, 'put_oi'] = options_data.loc[put_mask, 'open_interest']

        # Format GEX data for ML feature extraction if it exists
        formatted_gex_data = None
        if gex_data is not None:
            # Check if gex_data is from OptimizedGEXAnalyzer
            if hasattr(gex_data, 'gex_results'):
                # Extract the results dictionary
                formatted_gex_data = gex_data.gex_results

                # Add required fields for ML features if they don't exist
                if 'gex_at_current' not in formatted_gex_data and hasattr(gex_data, 'current_price'):
                    # Find the closest price point to current price
                    prices = formatted_gex_data.get('prices', [])
                    net_gex = formatted_gex_data.get('net_gex', [])
                    if prices and net_gex and len(prices) == len(net_gex):
                        import numpy as np
                        current_idx = np.abs(np.array(prices) - gex_data.current_price).argmin()
                        formatted_gex_data['gex_at_current'] = net_gex[current_idx]

                # Add impact field if it doesn't exist
                if 'impact' not in formatted_gex_data and 'gex_at_current' in formatted_gex_data:
                    gex_value = formatted_gex_data['gex_at_current']
                    if gex_value > 0:
                        formatted_gex_data['impact'] = 'positive'
                    elif gex_value < 0:
                        formatted_gex_data['impact'] = 'negative'
                    else:
                        formatted_gex_data['impact'] = 'neutral'

                # Add liquidity_levels if they don't exist
                if 'liquidity_levels' not in formatted_gex_data:
                    # Try to get liquidity levels from the analyzer
                    if hasattr(gex_data, 'get_liquidity_levels'):
                        try:
                            formatted_gex_data['liquidity_levels'] = gex_data.get_liquidity_levels()
                        except Exception as e:
                            logger.warning(f"Failed to get liquidity levels from GEX analyzer: {str(e)}")
            else:
                # Use the gex_data directly
                formatted_gex_data = gex_data

                # Ensure required fields exist
                if 'gex_at_current' not in formatted_gex_data:
                    logger.warning("GEX data missing 'gex_at_current' field")
                    formatted_gex_data['gex_at_current'] = 0.0

                if 'impact' not in formatted_gex_data:
                    gex_value = formatted_gex_data['gex_at_current']
                    if gex_value > 0:
                        formatted_gex_data['impact'] = 'positive'
                    elif gex_value < 0:
                        formatted_gex_data['impact'] = 'negative'
                    else:
                        formatted_gex_data['impact'] = 'neutral'

        # Enhance liquidity analysis
        enhanced_result = self.ml_integration.enhance_liquidity_analysis(
            price_data=price_data,
            liquidity_levels=consensus_levels,
            options_data=options_data,
            volume_profile=volume_profile,
            gex_data=formatted_gex_data
        )

        # Get enhanced levels
        enhanced_levels = enhanced_result.get('enhanced_levels', consensus_levels)

        # Calculate hybrid liquidity score
        hybrid_score = self.ml_integration.get_hybrid_liquidity_score(
            price_data=price_data,
            liquidity_levels=enhanced_levels,
            price_reactions=enhanced_result.get('price_reactions', {}),
            anomalies=enhanced_result.get('anomalies', [])
        )

        # Add ML metadata
        enhanced_levels['ml_metadata'] = {
            'confidence': enhanced_result.get('confidence', 0.0),
            'hybrid_score': hybrid_score,
            'anomalies': enhanced_result.get('anomalies', []),
            'price_reactions': enhanced_result.get('price_reactions', {})
        }

        return enhanced_levels

    def enhance_text_report(self,
                          text_report: str,
                          ml_metadata: Dict[str, Any]) -> str:
        """
        Enhance text report with ML insights.

        Args:
            text_report: Original text report
            ml_metadata: ML metadata from enhanced consensus levels

        Returns:
            Enhanced text report
        """
        # Extract ML data
        hybrid_score = ml_metadata.get('hybrid_score', {})
        anomalies = ml_metadata.get('anomalies', [])
        price_reactions = ml_metadata.get('price_reactions', {})

        # Create ML section for the report
        ml_section = """
## ML-Enhanced Liquidity Analysis

### Hybrid Liquidity Score
"""

        # Add hybrid score details
        if hybrid_score:
            ml_section += f"- **Overall Liquidity Score**: {hybrid_score.get('overall_score', 0):.2f}/1.0\n"
            ml_section += f"- **Support Strength**: {hybrid_score.get('support_score', 0):.2f}/1.0\n"
            ml_section += f"- **Resistance Strength**: {hybrid_score.get('resistance_score', 0):.2f}/1.0\n"
            ml_section += f"- **Directional Bias**: {hybrid_score.get('directional_bias', 0):.2f} "

            # Add bias interpretation
            bias = hybrid_score.get('directional_bias', 0)
            if bias > 0.3:
                ml_section += "(Bullish)\n"
            elif bias < -0.3:
                ml_section += "(Bearish)\n"
            else:
                ml_section += "(Neutral)\n"

            ml_section += f"- **Risk/Reward Ratio**: {hybrid_score.get('risk_reward', 1.0):.2f}\n"
            ml_section += f"- **Confidence**: {hybrid_score.get('confidence', 0):.2f}/1.0\n"

        # Add anomalies section
        if anomalies:
            ml_section += "\n### Detected Anomalies\n"

            for anomaly in anomalies:
                severity = anomaly.get('severity', 'low').upper()
                description = anomaly.get('description', 'Unknown anomaly')
                ml_section += f"- **{severity}**: {description}\n"
        else:
            ml_section += "\n### Anomalies\nNo significant anomalies detected.\n"

        # Add price reactions section
        if price_reactions:
            ml_section += "\n### Price Reaction Predictions\n"

            # Add support reactions
            support_reactions = price_reactions.get('support', [])
            if support_reactions:
                ml_section += "\n#### Support Level Reactions\n"
                ml_section += "| Price | Bounce Probability | Break Probability | Expected Move |\n"
                ml_section += "|-------|-------------------|-------------------|---------------|\n"

                for reaction in support_reactions:
                    price = reaction.get('price', 0)
                    bounce_prob = reaction.get('bounce_probability', 0)
                    break_prob = reaction.get('break_probability', 0)
                    expected_move = reaction.get('expected_move', 0)

                    ml_section += f"| ${price:.2f} | {bounce_prob:.2f} | {break_prob:.2f} | ${expected_move:.2f} |\n"

            # Add resistance reactions
            resistance_reactions = price_reactions.get('resistance', [])
            if resistance_reactions:
                ml_section += "\n#### Resistance Level Reactions\n"
                ml_section += "| Price | Rejection Probability | Break Probability | Expected Move |\n"
                ml_section += "|-------|----------------------|-------------------|---------------|\n"

                for reaction in resistance_reactions:
                    price = reaction.get('price', 0)
                    rejection_prob = reaction.get('rejection_probability', 0)
                    break_prob = reaction.get('break_probability', 0)
                    expected_move = reaction.get('expected_move', 0)

                    ml_section += f"| ${price:.2f} | {rejection_prob:.2f} | {break_prob:.2f} | ${expected_move:.2f} |\n"

        # Add ML implications section
        ml_section += "\n### ML Trading Implications\n"

        if hybrid_score:
            bias = hybrid_score.get('directional_bias', 0)
            risk_reward = hybrid_score.get('risk_reward', 1.0)

            if bias > 0.3 and risk_reward > 1.5:
                ml_section += "- **Bullish bias** with favorable risk/reward suggests looking for long entries near support levels\n"
            elif bias < -0.3 and risk_reward > 1.5:
                ml_section += "- **Bearish bias** with favorable risk/reward suggests looking for short entries near resistance levels\n"
            elif abs(bias) <= 0.3:
                ml_section += "- **Neutral bias** suggests range-bound trading strategies between support and resistance levels\n"

            # Add specific level recommendations
            if 'support' in price_reactions and 'resistance' in price_reactions:
                # Find strongest support with high bounce probability
                support_reactions = price_reactions.get('support', [])
                strong_supports = [r for r in support_reactions if r.get('bounce_probability', 0) > 0.7]
                if strong_supports:
                    best_support = max(strong_supports, key=lambda x: x.get('bounce_probability', 0))
                    ml_section += f"- Strong support at ${best_support.get('price', 0):.2f} has {best_support.get('bounce_probability', 0):.0%} probability of holding\n"

                # Find strongest resistance with high rejection probability
                resistance_reactions = price_reactions.get('resistance', [])
                strong_resistances = [r for r in resistance_reactions if r.get('rejection_probability', 0) > 0.7]
                if strong_resistances:
                    best_resistance = max(strong_resistances, key=lambda x: x.get('rejection_probability', 0))
                    ml_section += f"- Strong resistance at ${best_resistance.get('price', 0):.2f} has {best_resistance.get('rejection_probability', 0):.0%} probability of rejection\n"

        # Insert ML section before the conclusion
        if "## Trading Implications" in text_report:
            enhanced_report = text_report.replace("## Trading Implications", ml_section + "\n## Trading Implications")
        else:
            enhanced_report = text_report + "\n" + ml_section

        return enhanced_report


# Singleton instance
_main_integration = None

def get_main_integration(config: Optional[Dict[str, Any]] = None) -> MLLiquidityMainIntegration:
    """
    Get the singleton main integration instance.

    Args:
        config: Optional configuration dictionary

    Returns:
        MLLiquidityMainIntegration instance
    """
    global _main_integration
    if _main_integration is None:
        _main_integration = MLLiquidityMainIntegration(config)
        _main_integration.initialize()
    return _main_integration

def enhance_consensus_levels(consensus_levels: Dict[str, List[Dict[str, Any]]],
                           price_data: pd.DataFrame,
                           options_data: Optional[pd.DataFrame] = None,
                           volume_profile: Optional[Dict[str, Any]] = None,
                           gex_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Enhance consensus levels with ML predictions.

    Args:
        consensus_levels: Dictionary with consensus levels from consensus builder
        price_data: DataFrame with OHLCV price data
        options_data: Optional DataFrame with options chain data
        volume_profile: Optional volume profile data
        gex_data: Optional gamma exposure data

    Returns:
        Dictionary with enhanced consensus levels and ML metadata
    """
    # Format GEX data for ML feature extraction if it exists
    formatted_gex_data = None
    if gex_data is not None:
        logger = get_logger('main_integration')
        # Check if gex_data is from OptimizedGEXAnalyzer
        if hasattr(gex_data, 'gex_results'):
            # Extract the results dictionary
            formatted_gex_data = gex_data.gex_results

            # Add required fields for ML features if they don't exist
            if 'gex_at_current' not in formatted_gex_data and hasattr(gex_data, 'current_price'):
                # Find the closest price point to current price
                prices = formatted_gex_data.get('prices', [])
                net_gex = formatted_gex_data.get('net_gex', [])
                if prices and net_gex and len(prices) == len(net_gex):
                    import numpy as np
                    current_idx = np.abs(np.array(prices) - gex_data.current_price).argmin()
                    formatted_gex_data['gex_at_current'] = net_gex[current_idx]

            # Add impact field if it doesn't exist
            if 'impact' not in formatted_gex_data and 'gex_at_current' in formatted_gex_data:
                gex_value = formatted_gex_data['gex_at_current']
                if gex_value > 0:
                    formatted_gex_data['impact'] = 'positive'
                elif gex_value < 0:
                    formatted_gex_data['impact'] = 'negative'
                else:
                    formatted_gex_data['impact'] = 'neutral'

            # Add liquidity_levels if they don't exist
            if 'liquidity_levels' not in formatted_gex_data:
                # Try to get liquidity levels from the analyzer
                if hasattr(gex_data, 'get_liquidity_levels'):
                    try:
                        formatted_gex_data['liquidity_levels'] = gex_data.get_liquidity_levels()
                    except Exception as e:
                        logger.warning(f"Failed to get liquidity levels from GEX analyzer: {str(e)}")
        else:
            # Use the gex_data directly
            formatted_gex_data = gex_data

            # Ensure required fields exist
            if 'gex_at_current' not in formatted_gex_data:
                logger.warning("GEX data missing 'gex_at_current' field")
                formatted_gex_data['gex_at_current'] = 0.0

            if 'impact' not in formatted_gex_data:
                gex_value = formatted_gex_data['gex_at_current']
                if gex_value > 0:
                    formatted_gex_data['impact'] = 'positive'
                elif gex_value < 0:
                    formatted_gex_data['impact'] = 'negative'
                else:
                    formatted_gex_data['impact'] = 'neutral'

    integration = get_main_integration()
    return integration.enhance_consensus_levels(
        consensus_levels=consensus_levels,
        price_data=price_data,
        options_data=options_data,
        volume_profile=volume_profile,
        gex_data=formatted_gex_data
    )

def enhance_text_report(text_report: str, ml_metadata: Dict[str, Any]) -> str:
    """
    Enhance text report with ML insights.

    Args:
        text_report: Original text report
        ml_metadata: ML metadata from enhanced consensus levels

    Returns:
        Enhanced text report
    """
    integration = get_main_integration()
    return integration.enhance_text_report(text_report, ml_metadata)
