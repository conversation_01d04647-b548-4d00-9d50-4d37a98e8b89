"""
Training pipeline for model training and validation.

This module provides classes and functions to train, validate, and optimize
machine learning models for trading.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging
import os
import datetime
import json
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader

from .trade_model import TradeModel

# Configure logging
logger = logging.getLogger(__name__)


class EarlyStopping:
    """
    Early stopping handler for training.
    
    This class monitors a validation metric and stops training when the metric
    stops improving for a specified number of epochs.
    """
    
    def __init__(
        self,
        patience: int = 10,
        min_delta: float = 0.0,
        mode: str = 'min',
        verbose: bool = True
    ):
        """
        Initialize the early stopping handler.
        
        Args:
            patience: Number of epochs to wait for improvement
            min_delta: Minimum change to qualify as improvement
            mode: 'min' for metrics to minimize, 'max' for metrics to maximize
            verbose: Whether to print messages
        """
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.verbose = verbose
        self.counter = 0
        self.best_score = None
        self.early_stop = False
        self.val_loss_min = np.Inf if mode == 'min' else -np.Inf
    
    def __call__(self, val_metric: float, model: nn.Module, save_path: str = None) -> bool:
        """
        Check if training should stop and optionally save the model.
        
        Args:
            val_metric: Current validation metric
            model: Model to save
            save_path: Path to save the model
            
        Returns:
            True if training should stop, False otherwise
        """
        score = -val_metric if self.mode == 'min' else val_metric

        if self.best_score is None:
            # First epoch
            self.best_score = score
            self.save_checkpoint(val_metric, model, save_path)
        elif score < self.best_score + self.min_delta:
            # Metric did not improve
            self.counter += 1
            if self.verbose:
                logger.info(f'EarlyStopping counter: {self.counter} out of {self.patience}')
            if self.counter >= self.patience:
                self.early_stop = True
        else:
            # Metric improved
            self.best_score = score
            self.save_checkpoint(val_metric, model, save_path)
            self.counter = 0
        
        return self.early_stop
    
    def save_checkpoint(self, val_metric: float, model: nn.Module, save_path: str) -> None:
        """
        Save model checkpoint.
        
        Args:
            val_metric: Current validation metric
            model: Model to save
            save_path: Path to save the model
        """
        if not save_path:
            return
            
        if self.verbose:
            metric_name = 'loss' if self.mode == 'min' else 'score'
            logger.info(f'Validation {metric_name} improved ({self.val_loss_min:.6f} --> {val_metric:.6f}). Saving model...')
        
        # Save model
        torch.save(model.state_dict(), save_path)
        self.val_loss_min = val_metric


class LRSchedulerHandler:
    """
    Learning rate scheduler handler.
    
    This class manages learning rate scheduling during training.
    """
    
    def __init__(
        self,
        scheduler_type: str = 'reduce_on_plateau',
        scheduler_params: Optional[Dict[str, Any]] = None,
        mode: str = 'min',
        verbose: bool = True
    ):
        """
        Initialize the LR scheduler handler.
        
        Args:
            scheduler_type: Type of scheduler ('reduce_on_plateau', 'step', 'cosine', etc.)
            scheduler_params: Parameters for the scheduler
            mode: 'min' for metrics to minimize, 'max' for metrics to maximize
            verbose: Whether to print messages
        """
        self.scheduler_type = scheduler_type
        self.scheduler_params = scheduler_params or {}
        self.mode = mode
        self.verbose = verbose
        self.scheduler = None
    
    def create_scheduler(self, optimizer: optim.Optimizer) -> optim.lr_scheduler._LRScheduler:
        """
        Create a learning rate scheduler.
        
        Args:
            optimizer: Optimizer to schedule
            
        Returns:
            Learning rate scheduler
        """
        if self.scheduler_type == 'reduce_on_plateau':
            params = {
                'patience': 5,
                'factor': 0.5,
                'min_lr': 1e-6,
                'mode': self.mode,
                'verbose': self.verbose
            }
            params.update(self.scheduler_params)
            
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                optimizer,
                **params
            )
        
        elif self.scheduler_type == 'step':
            params = {
                'step_size': 10,
                'gamma': 0.5,
                'verbose': self.verbose
            }
            params.update(self.scheduler_params)
            
            self.scheduler = optim.lr_scheduler.StepLR(
                optimizer,
                **params
            )
        
        elif self.scheduler_type == 'cosine':
            params = {
                'T_max': 100,
                'eta_min': 1e-6,
                'verbose': self.verbose
            }
            params.update(self.scheduler_params)
            
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                optimizer,
                **params
            )
        
        elif self.scheduler_type == 'cyclic':
            params = {
                'base_lr': 1e-4,
                'max_lr': 1e-2,
                'step_size_up': 10,
                'mode': 'triangular2',
                'verbose': self.verbose
            }
            params.update(self.scheduler_params)
            
            self.scheduler = optim.lr_scheduler.CyclicLR(
                optimizer,
                **params
            )
        
        elif self.scheduler_type == 'one_cycle':
            params = {
                'max_lr': 1e-2,
                'total_steps': 100,
                'verbose': self.verbose
            }
            params.update(self.scheduler_params)
            
            self.scheduler = optim.lr_scheduler.OneCycleLR(
                optimizer,
                **params
            )
        
        else:
            raise ValueError(f"Unknown scheduler type: {self.scheduler_type}")
        
        return self.scheduler
    
    def step(self, val_metric: Optional[float] = None) -> None:
        """
        Step the scheduler.
        
        Args:
            val_metric: Validation metric (required for some schedulers)
        """
        if self.scheduler is None:
            raise ValueError("Scheduler not created. Call create_scheduler first.")
        
        if self.scheduler_type == 'reduce_on_plateau':
            if val_metric is None:
                raise ValueError("Validation metric required for ReduceLROnPlateau scheduler")
            self.scheduler.step(val_metric)
        else:
            self.scheduler.step()


class ModelTrainer:
    """
    Model trainer for PyTorch models.
    
    This class handles training, validation, and evaluation of PyTorch models.
    """
    
    def __init__(
        self,
        model: nn.Module,
        optimizer_type: str = 'adam',
        optimizer_params: Optional[Dict[str, Any]] = None,
        loss_fn: Optional[nn.Module] = None,
        scheduler_type: Optional[str] = None,
        scheduler_params: Optional[Dict[str, Any]] = None,
        device: Optional[str] = None,
        metrics: Optional[Dict[str, Callable]] = None
    ):
        """
        Initialize the model trainer.
        
        Args:
            model: PyTorch model
            optimizer_type: Type of optimizer
            optimizer_params: Parameters for the optimizer
            loss_fn: Loss function
            scheduler_type: Type of learning rate scheduler
            scheduler_params: Parameters for the scheduler
            device: Device to use ('cuda' or 'cpu')
            metrics: Dictionary of evaluation metrics
        """
        self.model = model
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # Move model to device
        self.model.to(self.device)
        
        # Set optimizer
        self.optimizer_type = optimizer_type
        self.optimizer_params = optimizer_params or {}
        self.optimizer = self.create_optimizer()
        
        # Set loss function
        if loss_fn is None:
            self.loss_fn = nn.MSELoss()
        else:
            self.loss_fn = loss_fn
        
        # Set metrics
        self.metrics = metrics or {
            'mae': lambda y_true, y_pred: torch.mean(torch.abs(y_true - y_pred))
        }
        
        # Set scheduler
        self.scheduler_handler = None
        if scheduler_type:
            self.scheduler_handler = LRSchedulerHandler(
                scheduler_type=scheduler_type,
                scheduler_params=scheduler_params
            )
            self.scheduler = self.scheduler_handler.create_scheduler(self.optimizer)
        else:
            self.scheduler = None
        
        # Training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': [],
            'epoch_times': []
        }
        
        # Add metrics to history
        for metric_name in self.metrics:
            self.history[f'train_{metric_name}'] = []
            self.history[f'val_{metric_name}'] = []
    
    def create_optimizer(self) -> optim.Optimizer:
        """
        Create an optimizer.
        
        Returns:
            PyTorch optimizer
        """
        if self.optimizer_type == 'adam':
            params = {
                'lr': 1e-3,
                'weight_decay': 1e-6
            }
            params.update(self.optimizer_params)
            
            return optim.Adam(self.model.parameters(), **params)
        
        elif self.optimizer_type == 'sgd':
            params = {
                'lr': 1e-3,
                'momentum': 0.9,
                'weight_decay': 1e-6
            }
            params.update(self.optimizer_params)
            
            return optim.SGD(self.model.parameters(), **params)
        
        elif self.optimizer_type == 'rmsprop':
            params = {
                'lr': 1e-3,
                'weight_decay': 1e-6
            }
            params.update(self.optimizer_params)
            
            return optim.RMSprop(self.model.parameters(), **params)
        
        elif self.optimizer_type == 'adamw':
            params = {
                'lr': 1e-3,
                'weight_decay': 1e-2
            }
            params.update(self.optimizer_params)
            
            return optim.AdamW(self.model.parameters(), **params)
        
        else:
            raise ValueError(f"Unknown optimizer type: {self.optimizer_type}")
    
    def train_step(self, batch: Tuple[torch.Tensor, torch.Tensor]) -> Dict[str, float]:
        """
        Perform a single training step.
        
        Args:
            batch: Tuple of (features, targets)
            
        Returns:
            Dictionary of step metrics
        """
        features, targets = batch
        
        # Move data to device
        features = features.to(self.device)
        targets = targets.to(self.device)
        
        # Forward pass
        self.optimizer.zero_grad()
        outputs = self.model(features)
        
        # Ensure outputs and targets have the same shape
        if outputs.shape != targets.shape:
            outputs = outputs.view(targets.shape)
        
        # Compute loss
        loss = self.loss_fn(outputs, targets)
        
        # Backward pass
        loss.backward()
        
        # Update parameters
        self.optimizer.step()
        
        # Compute metrics
        step_metrics = {'loss': loss.item()}
        
        for metric_name, metric_fn in self.metrics.items():
            step_metrics[metric_name] = metric_fn(targets, outputs).item()
        
        return step_metrics
    
    def validation_step(self, batch: Tuple[torch.Tensor, torch.Tensor]) -> Dict[str, float]:
        """
        Perform a single validation step.
        
        Args:
            batch: Tuple of (features, targets)
            
        Returns:
            Dictionary of step metrics
        """
        features, targets = batch
        
        # Move data to device
        features = features.to(self.device)
        targets = targets.to(self.device)
        
        # Forward pass
        with torch.no_grad():
            outputs = self.model(features)
            
            # Ensure outputs and targets have the same shape
            if outputs.shape != targets.shape:
                outputs = outputs.view(targets.shape)
            
            # Compute loss
            loss = self.loss_fn(outputs, targets)
        
        # Compute metrics
        step_metrics = {'loss': loss.item()}
        
        for metric_name, metric_fn in self.metrics.items():
            step_metrics[metric_name] = metric_fn(targets, outputs).item()
        
        return step_metrics
    
    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """
        Train for one epoch.
        
        Args:
            train_loader: DataLoader for training data
            
        Returns:
            Dictionary of epoch metrics
        """
        self.model.train()
        
        # Initialize metrics
        epoch_metrics = {'loss': 0.0}
        for metric_name in self.metrics:
            epoch_metrics[metric_name] = 0.0
        
        # Train on batches
        for batch in train_loader:
            step_metrics = self.train_step(batch)
            
            # Update epoch metrics
            for metric_name, metric_value in step_metrics.items():
                epoch_metrics[metric_name] += metric_value
        
        # Compute average metrics
        for metric_name in epoch_metrics:
            epoch_metrics[metric_name] /= len(train_loader)
        
        return epoch_metrics
    
    def validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """
        Validate for one epoch.
        
        Args:
            val_loader: DataLoader for validation data
            
        Returns:
            Dictionary of epoch metrics
        """
        self.model.eval()
        
        # Initialize metrics
        epoch_metrics = {'loss': 0.0}
        for metric_name in self.metrics:
            epoch_metrics[metric_name] = 0.0
        
        # Validate on batches
        for batch in val_loader:
            step_metrics = self.validation_step(batch)
            
            # Update epoch metrics
            for metric_name, metric_value in step_metrics.items():
                epoch_metrics[metric_name] += metric_value
        
        # Compute average metrics
        for metric_name in epoch_metrics:
            epoch_metrics[metric_name] /= len(val_loader)
        
        return epoch_metrics
    
    def train(
        self,
        train_loader: DataLoader,
        val_loader: DataLoader,
        epochs: int = 100,
        early_stopping_patience: int = 10,
        checkpoint_dir: Optional[str] = None,
        model_name: Optional[str] = None,
        verbose: bool = True
    ) -> Dict[str, List[float]]:
        """
        Train the model.
        
        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data
            epochs: Number of epochs to train
            early_stopping_patience: Patience for early stopping
            checkpoint_dir: Directory to save checkpoints
            model_name: Name for saved model
            verbose: Whether to print progress
            
        Returns:
            Dictionary of training history
        """
        # Setup early stopping
        if early_stopping_patience > 0:
            early_stopping = EarlyStopping(
                patience=early_stopping_patience,
                verbose=verbose
            )
        else:
            early_stopping = None
        
        # Setup checkpoint saving
        if checkpoint_dir and model_name:
            os.makedirs(checkpoint_dir, exist_ok=True)
            checkpoint_path = os.path.join(checkpoint_dir, f"{model_name}_best.pth")
        else:
            checkpoint_path = None
        
        # Training loop
        for epoch in range(epochs):
            # Record start time
            start_time = datetime.datetime.now()
            
            # Train and validate
            train_metrics = self.train_epoch(train_loader)
            val_metrics = self.validate_epoch(val_loader)
            
            # Record end time
            end_time = datetime.datetime.now()
            epoch_time = (end_time - start_time).total_seconds()
            
            # Update history
            self.history['train_loss'].append(train_metrics['loss'])
            self.history['val_loss'].append(val_metrics['loss'])
            self.history['epoch_times'].append(epoch_time)
            
            # Update metric history
            for metric_name in self.metrics:
                self.history[f'train_{metric_name}'].append(train_metrics[metric_name])
                self.history[f'val_{metric_name}'].append(val_metrics[metric_name])
            
            # Get current learning rate
            if isinstance(self.optimizer, optim.Adam) or isinstance(self.optimizer, optim.SGD):
                current_lr = self.optimizer.param_groups[0]['lr']
                self.history['learning_rate'].append(current_lr)
            
            # Print progress
            if verbose and (epoch % 10 == 0 or epoch == epochs - 1):
                logger.info(
                    f"Epoch {epoch+1}/{epochs} - "
                    f"train_loss: {train_metrics['loss']:.6f}, "
                    f"val_loss: {val_metrics['loss']:.6f}, "
                    f"time: {epoch_time:.2f}s"
                )
            
            # Update learning rate scheduler
            if self.scheduler_handler:
                if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.scheduler_handler.step(val_metrics['loss'])
                else:
                    self.scheduler_handler.step()
            
            # Check early stopping
            if early_stopping and checkpoint_path:
                if early_stopping(val_metrics['loss'], self.model, checkpoint_path):
                    logger.info(f"Early stopping at epoch {epoch+1}")
                    break
        
        # Save final model
        if checkpoint_dir and model_name:
            final_path = os.path.join(checkpoint_dir, f"{model_name}_final.pth")
            torch.save(self.model.state_dict(), final_path)
            
            # Save training history
            history_path = os.path.join(checkpoint_dir, f"{model_name}_history.json")
            with open(history_path, 'w') as f:
                # Convert numpy arrays to lists for JSON serialization
                history_serializable = {}
                for key, value in self.history.items():
                    history_serializable[key] = [float(v) for v in value]
                
                json.dump(history_serializable, f, indent=4)
        
        return self.history
    
    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """
        Evaluate the model on test data.
        
        Args:
            test_loader: DataLoader for test data
            
        Returns:
            Dictionary of evaluation metrics
        """
        self.model.eval()
        
        # Initialize metrics
        metrics = {'loss': 0.0}
        for metric_name in self.metrics:
            metrics[metric_name] = 0.0
        
        # Custom trade model metrics
        if isinstance(self.model, TradeModel):
            metrics.update({
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'profit_factor': 0.0
            })
        
        # Evaluate on batches
        all_targets = []
        all_outputs = []
        
        for batch in test_loader:
            features, targets = batch
            
            # Move data to device
            features = features.to(self.device)
            targets = targets.to(self.device)
            
            # Forward pass
            with torch.no_grad():
                outputs = self.model(features)
                
                # Ensure outputs and targets have the same shape
                if outputs.shape != targets.shape:
                    outputs = outputs.view(targets.shape)
                
                # Compute loss
                loss = self.loss_fn(outputs, targets)
            
            # Update loss metric
            metrics['loss'] += loss.item()
            
            # Compute standard metrics
            for metric_name, metric_fn in self.metrics.items():
                metrics[metric_name] += metric_fn(targets, outputs).item()
            
            # Store targets and outputs for additional metrics
            all_targets.append(targets.cpu().numpy())
            all_outputs.append(outputs.cpu().numpy())
        
        # Compute average metrics
        for metric_name in list(metrics.keys()):
            metrics[metric_name] /= len(test_loader)
        
        # Compute additional metrics for trade models
        if isinstance(self.model, TradeModel) and all_targets and all_outputs:
            targets = np.concatenate(all_targets)
            outputs = np.concatenate(all_outputs)
            
            # The specific metrics would depend on the trade model implementation
            try:
                additional_metrics = self.model.compute_trade_metrics(targets, outputs)
                metrics.update(additional_metrics)
            except:
                logger.warning("Failed to compute trade-specific metrics")
        
        return metrics
    
    def save_model(self, path: str) -> None:
        """
        Save the model.
        
        Args:
            path: Path to save the model
        """
        torch.save(self.model.state_dict(), path)
    
    def load_model(self, path: str) -> None:
        """
        Load the model.
        
        Args:
            path: Path to load the model from
        """
        self.model.load_state_dict(torch.load(path, map_location=self.device))
        self.model.to(self.device)


class HyperparamOptimizer:
    """
    Hyperparameter optimizer for machine learning models.
    
    This class performs hyperparameter optimization using various search strategies.
    """
    
    def __init__(
        self,
        model_class: type,
        param_grid: Dict[str, List[Any]],
        search_strategy: str = 'grid',
        n_trials: int = 10,
        cv: int = 3,
        scoring: str = 'val_loss',
        direction: str = 'minimize',
        verbose: bool = True
    ):
        """
        Initialize the hyperparameter optimizer.
        
        Args:
            model_class: Model class to optimize
            param_grid: Grid of hyperparameters
            search_strategy: Search strategy ('grid', 'random', or 'bayesian')
            n_trials: Number of trials for random and Bayesian search
            cv: Number of cross-validation folds
            scoring: Metric to optimize
            direction: Direction of optimization ('minimize' or 'maximize')
            verbose: Whether to print progress
        """
        self.model_class = model_class
        self.param_grid = param_grid
        self.search_strategy = search_strategy
        self.n_trials = n_trials
        self.cv = cv
        self.scoring = scoring
        self.direction = direction
        self.verbose = verbose
        
        # Results
        self.results = []
        self.best_params = None
        self.best_score = float('inf') if direction == 'minimize' else float('-inf')
        self.best_model = None
    
    def _evaluate_params(
        self,
        params: Dict[str, Any],
        train_datasets: List[Dict[str, Any]]
    ) -> float:
        """
        Evaluate a set of hyperparameters.
        
        Args:
            params: Hyperparameters to evaluate
            train_datasets: List of training datasets
            
        Returns:
            Average score across folds
        """
        scores = []
        
        for fold_datasets in train_datasets:
            # Create and train model
            model = self.model_class(**params.get('model_params', {}))
            
            trainer = ModelTrainer(
                model=model,
                optimizer_type=params.get('optimizer_type', 'adam'),
                optimizer_params=params.get('optimizer_params', {}),
                loss_fn=params.get('loss_fn', nn.MSELoss()),
                scheduler_type=params.get('scheduler_type', None),
                scheduler_params=params.get('scheduler_params', {}),
                metrics=params.get('metrics', None),
                device=params.get('device', None)
            )
            
            # Train model
            trainer.train(
                train_loader=fold_datasets['train_loader'],
                val_loader=fold_datasets['val_loader'],
                epochs=params.get('epochs', 50),
                early_stopping_patience=params.get('early_stopping_patience', 10),
                verbose=False
            )
            
            # Evaluate model
            if self.scoring.startswith('val_'):
                # Extract metric from history
                metric_name = self.scoring[4:]  # Remove 'val_' prefix
                if metric_name in trainer.history:
                    score = trainer.history[metric_name][-1]
                else:
                    raise ValueError(f"Unknown metric: {metric_name}")
            else:
                # Evaluate on validation set
                val_metrics = trainer.evaluate(fold_datasets['val_loader'])
                score = val_metrics.get(self.scoring, float('inf'))
            
            scores.append(score)
        
        # Compute average score
        avg_score = sum(scores) / len(scores)
        
        # Print progress
        if self.verbose:
            param_str = ', '.join(f"{k}={v}" for k, v in params.items() if k != 'model_params')
            logger.info(f"Params: {param_str}, Score: {avg_score:.6f}")
        
        return avg_score
    
    def optimize(
        self,
        train_datasets: List[Dict[str, Any]],
        final_model_params: Optional[Dict[str, Any]] = None
    ) -> Tuple[Dict[str, Any], float]:
        """
        Optimize hyperparameters.
        
        Args:
            train_datasets: List of training datasets for cross-validation
            final_model_params: Parameters for the final model (optional)
            
        Returns:
            Tuple of (best_params, best_score)
        """
        if self.search_strategy == 'grid':
            self._grid_search(train_datasets)
        elif self.search_strategy == 'random':
            self._random_search(train_datasets)
        elif self.search_strategy == 'bayesian':
            self._bayesian_search(train_datasets)
        else:
            raise ValueError(f"Unknown search strategy: {self.search_strategy}")
        
        # Train final model with best params
        if final_model_params:
            # Update best params with final model params
            final_params = self.best_params.copy()
            final_params.update(final_model_params)
            
            # Train model on all data
            model = self.model_class(**final_params.get('model_params', {}))
            
            trainer = ModelTrainer(
                model=model,
                optimizer_type=final_params.get('optimizer_type', 'adam'),
                optimizer_params=final_params.get('optimizer_params', {}),
                loss_fn=final_params.get('loss_fn', nn.MSELoss()),
                scheduler_type=final_params.get('scheduler_type', None),
                scheduler_params=final_params.get('scheduler_params', {}),
                metrics=final_params.get('metrics', None),
                device=final_params.get('device', None)
            )
            
            # Combine all training data
            combined_train_loader = train_datasets[0]['train_loader']
            combined_val_loader = train_datasets[0]['val_loader']
            
            # Train model
            trainer.train(
                train_loader=combined_train_loader,
                val_loader=combined_val_loader,
                epochs=final_params.get('epochs', 100),
                early_stopping_patience=final_params.get('early_stopping_patience', 10),
                checkpoint_dir=final_params.get('checkpoint_dir', None),
                model_name=final_params.get('model_name', None),
                verbose=self.verbose
            )
            
            # Store best model
            self.best_model = trainer.model
        
        return self.best_params, self.best_score
    
    def _grid_search(self, train_datasets: List[Dict[str, Any]]) -> None:
        """
        Perform grid search.
        
        Args:
            train_datasets: List of training datasets for cross-validation
        """
        # Generate parameter combinations
        param_keys = list(self.param_grid.keys())
        param_values = list(self.param_grid.values())
        param_combinations = self._generate_param_combinations(param_keys, param_values)
        
        # Evaluate each combination
        for params in param_combinations:
            score = self._evaluate_params(params, train_datasets)
            
            # Update results
            self.results.append((params, score))
            
            # Update best params
            if (self.direction == 'minimize' and score < self.best_score) or \
               (self.direction == 'maximize' and score > self.best_score):
                self.best_score = score
                self.best_params = params
    
    def _random_search(self, train_datasets: List[Dict[str, Any]]) -> None:
        """
        Perform random search.
        
        Args:
            train_datasets: List of training datasets for cross-validation
        """
        # Generate parameter combinations
        param_keys = list(self.param_grid.keys())
        
        # Evaluate random combinations
        for _ in range(self.n_trials):
            # Generate random params
            params = {}
            for key in param_keys:
                values = self.param_grid[key]
                params[key] = np.random.choice(values)
            
            # Evaluate params
            score = self._evaluate_params(params, train_datasets)
            
            # Update results
            self.results.append((params, score))
            
            # Update best params
            if (self.direction == 'minimize' and score < self.best_score) or \
               (self.direction == 'maximize' and score > self.best_score):
                self.best_score = score
                self.best_params = params
    
    def _bayesian_search(self, train_datasets: List[Dict[str, Any]]) -> None:
        """
        Perform Bayesian optimization.
        
        Args:
            train_datasets: List of training datasets for cross-validation
        """
        # This would ideally use a library like optuna or skopt
        # For now, use random search as a fallback
        logger.warning("Bayesian optimization not fully implemented. Using random search instead.")
        self._random_search(train_datasets)
    
    def _generate_param_combinations(
        self,
        param_keys: List[str],
        param_values: List[List[Any]],
        current_idx: int = 0,
        current_params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate all combinations of parameters.
        
        Args:
            param_keys: List of parameter keys
            param_values: List of parameter values
            current_idx: Current parameter index
            current_params: Current parameter dictionary
            
        Returns:
            List of parameter dictionaries
        """
        if current_params is None:
            current_params = {}
        
        # Base case: all parameters assigned
        if current_idx == len(param_keys):
            return [current_params.copy()]
        
        # Recursive case: assign next parameter
        combinations = []
        key = param_keys[current_idx]
        values = param_values[current_idx]
        
        for value in values:
            current_params[key] = value
            combinations.extend(
                self._generate_param_combinations(
                    param_keys, param_values, current_idx + 1, current_params
                )
            )
        
        return combinations


# Helper functions for training workflow
def train_model(
    model: nn.Module,
    train_loader: DataLoader,
    val_loader: DataLoader,
    test_loader: Optional[DataLoader] = None,
    epochs: int = 100,
    optimizer_type: str = 'adam',
    optimizer_params: Optional[Dict[str, Any]] = None,
    loss_fn: Optional[nn.Module] = None,
    scheduler_type: Optional[str] = None,
    scheduler_params: Optional[Dict[str, Any]] = None,
    early_stopping_patience: int = 10,
    checkpoint_dir: Optional[str] = None,
    model_name: Optional[str] = None,
    metrics: Optional[Dict[str, Callable]] = None,
    device: Optional[str] = None,
    verbose: bool = True
) -> Tuple[nn.Module, Dict[str, Any]]:
    """
    Train a model with standard workflow.
    
    Args:
        model: PyTorch model
        train_loader: DataLoader for training data
        val_loader: DataLoader for validation data
        test_loader: DataLoader for test data (optional)
        epochs: Number of epochs to train
        optimizer_type: Type of optimizer
        optimizer_params: Parameters for the optimizer
        loss_fn: Loss function
        scheduler_type: Type of learning rate scheduler
        scheduler_params: Parameters for the scheduler
        early_stopping_patience: Patience for early stopping
        checkpoint_dir: Directory to save checkpoints
        model_name: Name for saved model
        metrics: Dictionary of evaluation metrics
        device: Device to use ('cuda' or 'cpu')
        verbose: Whether to print progress
        
    Returns:
        Tuple of (trained_model, results)
    """
    # Create trainer
    trainer = ModelTrainer(
        model=model,
        optimizer_type=optimizer_type,
        optimizer_params=optimizer_params,
        loss_fn=loss_fn,
        scheduler_type=scheduler_type,
        scheduler_params=scheduler_params,
        metrics=metrics,
        device=device
    )
    
    # Train model
    history = trainer.train(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=epochs,
        early_stopping_patience=early_stopping_patience,
        checkpoint_dir=checkpoint_dir,
        model_name=model_name,
        verbose=verbose
    )
    
    # Evaluate on test set if provided
    test_metrics = None
    if test_loader:
        test_metrics = trainer.evaluate(test_loader)
        
        if verbose:
            logger.info("Test metrics:")
            for metric_name, metric_value in test_metrics.items():
                logger.info(f"  {metric_name}: {metric_value:.6f}")
    
    # Prepare results
    results = {
        'history': history,
        'test_metrics': test_metrics
    }
    
    return trainer.model, results
