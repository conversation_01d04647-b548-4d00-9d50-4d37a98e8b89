#!/usr/bin/env python
"""
API Client Factory for Liquidity Reports

This module provides a centralized factory for creating properly configured API 
clients throughout the Liquidity Reports system, preventing duplicate implementations
and ensuring consistent configuration.
"""

import logging
import os
from typing import Dict, Any, Optional, Type

# Import API clients
from api_robustness.modules.data.loaders.enhanced_polygon_api import PolygonAPI
from api_robustness.modules.api_cache import ApiCache

# Configure logger
logger = logging.getLogger("Liquidity_Reports.api_client_factory")

class ApiClientFactory:
    """
    Factory class for providing properly configured API clients.
    
    This class acts as a central source for all API clients used in the system,
    ensuring consistent configuration and preventing duplicate implementations.
    """
    
    def __init__(self, cache_ttl: int = 3600, enable_caching: bool = True):
        """
        Initialize the API client factory.
        
        Args:
            cache_ttl (int, optional): Default cache TTL in seconds.
                Defaults to 3600 (1 hour).
            enable_caching (bool, optional): Whether to enable caching by default.
                Defaults to True.
        """
        self.logger = logger
        self.default_cache_ttl = cache_ttl
        self.enable_caching = enable_caching
        
        # Create a shared cache instance
        self.cache = ApiCache(default_ttl=cache_ttl)
        
        # Initialize empty client registry
        self._client_registry = {}
        
        # Initialize default configuration
        self._default_config = {
            "polygon": {
                "api_key": None,  # Will be loaded from environment/config
                "cache_ttl": cache_ttl,
                "enable_caching": enable_caching
            }
            # Add other APIs here as they become available
        }
        
        # Initialize client instance cache
        self._client_instances = {}
        
        self.logger.info("API Client Factory initialized")
    
    def get_polygon_client(self, api_key: Optional[str] = None, 
                          cache_ttl: Optional[int] = None,
                          enable_caching: Optional[bool] = None) -> PolygonAPI:
        """
        Get a properly configured Polygon API client.
        
        Args:
            api_key (str, optional): Polygon API key. If None, will be loaded
                from environment or configuration.
            cache_ttl (int, optional): Cache TTL in seconds. If None, uses
                the factory default.
            enable_caching (bool, optional): Whether to enable caching. If None,
                uses the factory default.
                
        Returns:
            PolygonAPI: Configured Polygon API client
        """
        # Generate a cache key
        cache_key = f"polygon_{api_key}_{cache_ttl}_{enable_caching}"
        
        # Check if we already have an instance with this configuration
        if cache_key in self._client_instances:
            return self._client_instances[cache_key]
        
        # Set parameters to defaults if not provided
        if cache_ttl is None:
            cache_ttl = self._default_config["polygon"]["cache_ttl"]
            
        if enable_caching is None:
            enable_caching = self._default_config["polygon"]["enable_caching"]
        
        # Create the client
        client = PolygonAPI(
            api_key=api_key,
            cache=self.cache,
            cache_ttl=cache_ttl,
            enable_caching=enable_caching
        )
        
        # Store the instance
        self._client_instances[cache_key] = client
        
        self.logger.info(f"Created new Polygon API client with cache_ttl={cache_ttl}s, "
                        f"enable_caching={enable_caching}")
        
        return client
    
    def register_client_type(self, client_name: str, client_class: Type,
                            default_config: Dict[str, Any]) -> None:
        """
        Register a new API client type with the factory.
        
        This allows for dynamically adding new client types at runtime.
        
        Args:
            client_name (str): Name to register the client under
            client_class (Type): Client class
            default_config (dict): Default configuration for the client
        """
        self._client_registry[client_name] = client_class
        self._default_config[client_name] = default_config
        
        self.logger.info(f"Registered new client type: {client_name}")
    
    def get_client(self, client_name: str, **kwargs) -> Any:
        """
        Get a client instance by name.
        
        This generic method can create any registered client type.
        
        Args:
            client_name (str): Name of the client type to create
            **kwargs: Configuration options for the client
            
        Returns:
            Any: Configured client instance
            
        Raises:
            ValueError: If the client type is not registered
        """
        if client_name not in self._client_registry:
            raise ValueError(f"Unknown client type: {client_name}")
        
        # Generate a cache key
        config_str = str(sorted(kwargs.items()))
        cache_key = f"{client_name}_{config_str}"
        
        # Check if we already have an instance with this configuration
        if cache_key in self._client_instances:
            return self._client_instances[cache_key]
        
        # Get client class and default config
        client_class = self._client_registry[client_name]
        default_config = self._default_config.get(client_name, {})
        
        # Merge default config with provided kwargs
        merged_config = {**default_config, **kwargs}
        
        # Create the client
        client = client_class(cache=self.cache, **merged_config)
        
        # Store the instance
        self._client_instances[cache_key] = client
        
        self.logger.info(f"Created new {client_name} API client with config: {merged_config}")
        
        return client
    
    def set_default_config(self, client_name: str, config: Dict[str, Any]) -> None:
        """
        Set default configuration for a client type.
        
        Args:
            client_name (str): Client type name
            config (dict): Default configuration
        """
        if client_name not in self._default_config:
            self._default_config[client_name] = {}
            
        self._default_config[client_name].update(config)
        
        self.logger.info(f"Updated default config for {client_name}: {config}")
    
    def clear_client_cache(self) -> None:
        """
        Clear all cached client instances.
        """
        self._client_instances.clear()
        self.logger.info("Cleared client instance cache")


# Create a global factory instance for convenience
default_factory = ApiClientFactory()

def get_polygon_client(**kwargs) -> PolygonAPI:
    """
    Convenience function to get a Polygon API client from the default factory.
    
    Args:
        **kwargs: Configuration options
        
    Returns:
        PolygonAPI: Configured Polygon API client
    """
    return default_factory.get_polygon_client(**kwargs)


# Example usage
if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Use the default factory
    polygon_client = get_polygon_client()
    print("Got Polygon client from default factory")
    
    # Test the client
    test_result = polygon_client.test_connection()
    print(f"Test connection result: {test_result}")
    
    # Create a custom factory
    custom_factory = ApiClientFactory(cache_ttl=1800, enable_caching=True)
    custom_polygon = custom_factory.get_polygon_client()
    print("Created custom factory with different cache settings")
    
    # Both factories should work but have different cache settings
    print(f"Default factory TTL: {default_factory.default_cache_ttl}")
    print(f"Custom factory TTL: {custom_factory.default_cache_ttl}")
