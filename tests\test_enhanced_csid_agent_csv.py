import unittest
import pandas as pd
from pathlib import Path
import json
from unittest import mock
import sys
import os
from datetime import datetime

# Add the parent directory to the sys.path to allow imports from the 'agents' directory
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.enhanced_csid_agent import EnhancedCSIDAgent

class TestEnhancedCSIDAgent(unittest.TestCase):

    TEST_DATA_DIR = Path(__file__).parent / "test_data"
    SAMPLE_CSV_PATH = TEST_DATA_DIR / "sample_csid_data.csv"
    OUTPUT_DIR = Path(__file__).parent.parent / "flow_phys"

    @classmethod
    def setUpClass(cls):
        """Set up test data and directories once for all tests."""
        cls.TEST_DATA_DIR.mkdir(parents=True, exist_ok=True)
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        # Create a dummy CSV file for testing
        data = {
            'timestamp': pd.to_datetime([f'2024-01-01 {i:02d}:00:00' for i in range(21)]),
            'open': [100 + i for i in range(21)],
            'high': [102 + i for i in range(21)],
            'low': [99 + i for i in range(21)],
            'close': [101 + i for i in range(21)],
            'volume': [1000 + i * 10 for i in range(21)]
        }
        df = pd.DataFrame(data)
        df.to_csv(cls.SAMPLE_CSV_PATH, index=False)
        print(f"Created dummy CSV file at: {cls.SAMPLE_CSV_PATH}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test data and directories after all tests."""
        if cls.SAMPLE_CSV_PATH.exists():
            cls.SAMPLE_CSV_PATH.unlink()
        if cls.TEST_DATA_DIR.exists() and not any(cls.TEST_DATA_DIR.iterdir()):
            cls.TEST_DATA_DIR.rmdir()
        
        today_str = datetime.now().strftime("%Y-%m-%d")
        output_file_path = cls.OUTPUT_DIR / today_str / "TEST_TICKER_csid.json"
        if output_file_path.exists():
            output_file_path.unlink()
        
        daily_output_dir = cls.OUTPUT_DIR / today_str
        if daily_output_dir.exists() and not any(daily_output_dir.iterdir()):
            daily_output_dir.rmdir()

    @mock.patch('pandas.read_parquet')
    def test_execute_csid_analysis_fallback(self, mock_read_parquet):
        """Test the execute method of EnhancedCSIDAgent using the fallback analyzer."""
        
        # Mock read_parquet to return our CSV data
        df = pd.read_csv(self.SAMPLE_CSV_PATH)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        mock_read_parquet.return_value = df
        
        agent = EnhancedCSIDAgent()
        ticker = "TEST_TICKER"
        
        # Ensure we are using the fallback analyzer
        self.assertIn("FallbackCSIDAnalyzer", str(type(agent.analyzer)))

        # Execute the agent
        output_file_path = agent.execute(ticker, str(self.SAMPLE_CSV_PATH))

        # Assert that an output file was created
        self.assertTrue(Path(output_file_path).exists())
        self.assertTrue(output_file_path.endswith(f"{ticker}_csid.json"))

        # Load the output file and verify its content
        with open(output_file_path, 'r') as f:
            result_data = json.load(f)
        
        self.assertEqual(result_data['ticker'], ticker)
        self.assertGreater(result_data['data_quality_score'], 0.6)
        self.assertIn(result_data['flow_regime'], ["stealth_accumulation", "stealth_distribution", "retail_fomo", "mixed"])
        self.assertIn('smart_money_index', result_data)
        self.assertIn('institutional_bias', result_data)

    @mock.patch('pathlib.Path.exists')
    @mock.patch('pandas.read_parquet')
    def test_execute_insufficient_data(self, mock_read_parquet, mock_exists):
        """Test with insufficient data to trigger low quality score."""
        
        # Mock file existence check
        mock_exists.return_value = True
        
        # Create insufficient data
        insufficient_data = {
            'timestamp': pd.to_datetime([f'2024-01-01 0{i}:00:00' for i in range(5)]),
            'open': [100, 101, 102, 103, 104],
            'high': [102, 103, 104, 105, 106],
            'low': [99, 100, 101, 102, 103],
            'close': [101, 102, 103, 104, 105],
            'volume': [1000, 1010, 1020, 1030, 1040]
        }
        insufficient_df = pd.DataFrame(insufficient_data)
        mock_read_parquet.return_value = insufficient_df
        
        agent = EnhancedCSIDAgent()

        # Expect a ValueError due to low quality score
        with self.assertRaises(ValueError) as cm:
            agent.execute("TEST_TICKER_LOW", "dummy_path.parquet")
        
        self.assertIn("CSID data quality too low", str(cm.exception))

if __name__ == '__main__':
    unittest.main()