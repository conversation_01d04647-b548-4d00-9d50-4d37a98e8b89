task_id: AGENT_ORCHESTRATOR
name: Agent Orchestrator - Command Center
version: 1.0.0
description: Central command coordinating all specialized trading agents with Schwab MCP integration
            for comprehensive market analysis and ensemble decision making.

inputs:
  - symbol: str                   # Ticker symbol to analyze
  - analysis_type: str            # 'full', 'accumulation', 'breakout', 'options'
  - timeframe: str               # Optional timeframe specification
  - custom_levels: Dict          # Optional support/resistance levels

outputs:
  data:
    type: Dict[str, Any]
    schema: schemas/agent_orchestrator_output_v1.json
    
success_criteria:
  perf_budget:
    max_runtime_ms: 15000
  precision_threshold: 0.001
  code_coverage_min: 0.90
  output_completeness: 1.00
  ensemble_accuracy_target: 0.80
  dependency_constraints:
    max_additional_deps: 3
    forbidden_packages:
      - tensorflow
      - torch
      - opencv-python
    allowed_packages:
      - scikit-learn  # ML operations
      - scipy         # Statistical calculations
      - aiohttp       # Async HTTP for MCP

mathematical_requirements:
  - numerical_stability: true
  - precision_validation: required
  - statistical_rigor: enforced
  - ensemble_voting_precision: 1e-4
  - confidence_aggregation_accuracy: 1e-3

orchestration_architecture:
  command_structure: "Central Command + Specialized Agents"
  coordination_method: "Parallel execution with ensemble voting"
  data_integration: "Schwab MCP + fallback synthetic data"
  
specialized_agents:
  accumulation_distribution:
    agent_class: "AccumulationDistributionAgent"
    focus: "THE MASTER of institutional pattern detection"
    accuracy_target: "78%+"
    execution_mode: "parallel"
  breakout_validation:
    agent_class: "BreakoutValidationAgent" 
    focus: "THE SPECIALIST in breakout validation"
    accuracy_target: "90%+"
    execution_mode: "parallel"
  options_flow:
    agent_class: "OptionsFlowDecoderAgent"
    focus: "THE EXPERT in institutional options flow"
    accuracy_target: "75%+"
    execution_mode: "parallel"

ensemble_decision_framework:
  voting_method: "Confidence-weighted ensemble"
  minimum_agents: 2
  confidence_threshold: 60.0
  agreement_threshold: 0.6
  decision_categories:
    - BULLISH (ensemble_score > 65)
    - BEARISH (ensemble_score < 35)
    - NEUTRAL (35 <= ensemble_score <= 65)
  strength_levels:
    - STRONG (score > 75 or < 25)
    - MODERATE (65 < score <= 75 or 25 <= score < 35)
    - WEAK (45 < score <= 65 or 35 <= score < 55)

analysis_types:
  full:
    description: "Execute all specialized agents in parallel"
    agents_used: ["accumulation_distribution", "breakout_validation", "options_flow"]
    ensemble_voting: true
    expected_runtime: "10-15 seconds"
  accumulation:
    description: "Focus on institutional accumulation/distribution patterns"
    agents_used: ["accumulation_distribution"]
    ensemble_voting: false
    expected_runtime: "3-5 seconds"
  breakout:
    description: "Validate breakout authenticity and momentum"
    agents_used: ["breakout_validation"]
    ensemble_voting: false
    expected_runtime: "3-5 seconds"
  options:
    description: "Decode institutional options flow and positioning"
    agents_used: ["options_flow"]
    ensemble_voting: false
    expected_runtime: "3-5 seconds"

data_sources:
  primary: schwab_mcp
  mcp_server: "api/schwab_mcp_server.py"
  mcp_url: "http://localhost:8000"
  fallback: "synthetic_data_generation"
  required_data_types:
    - price_data (OHLC)
    - volume_data
    - options_chain
    - implied_volatility
    - fundamentals (optional)

schwab_integration:
  connection_method: "JSON-RPC over HTTP"
  timeout_ms: 10000
  retry_policy: "3 attempts with exponential backoff"
  health_check: "automated"
  data_freshness_monitoring: "enabled"
  fallback_on_failure: "synthetic_data"

parallel_processing:
  enabled: true
  max_workers: 3
  thread_pool_executor: true
  timeout_per_agent: 5000ms
  failure_handling: "graceful_degradation"

validation_framework:
  input_validation: comprehensive
  agent_result_validation: per_agent_validation
  ensemble_consistency_check: required
  output_completeness_check: required
  data_freshness_validation: enabled

performance_monitoring:
  execution_time_tracking: enabled
  agent_performance_metrics: collected
  ensemble_accuracy_tracking: enabled
  data_quality_monitoring: enabled

health_monitoring:
  orchestrator_health: "self_monitoring"
  agent_health_checks: "periodic"
  mcp_connection_monitoring: "continuous"
  degraded_mode_operation: "fallback_data"

consensus_generation:
  individual_decisions_weighted: true
  confidence_score_aggregation: "weighted_average"
  agreement_level_calculation: "majority_consensus"
  recommendation_generation: "risk_adjusted"
  
risk_assessment:
  confidence_based_risk_levels:
    - HIGH (confidence < 60%)
    - MEDIUM (60% <= confidence < 80%)
    - LOW (confidence >= 80%)
  ensemble_disagreement_handling: "increased_risk_level"
  data_quality_risk_factor: "applied"

external_usage:
  orchestrate_analysis_function: "async convenience function"
  direct_orchestrator_instantiation: "full_control_mode"
  capabilities_query: "get_agent_capabilities()"
  health_status_query: "health_check()"
