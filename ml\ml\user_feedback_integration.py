"""
User Feedback Integration Module

This module provides functionality for collecting and incorporating user feedback
into the liquidity analysis system.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import logging
import time
from datetime import datetime
import uuid

# Set up logger
logger = logging.getLogger(__name__)
from dataclasses import dataclass, field
import threading
import queue

# Internal imports
from ml_logging import get_logger
from ml_model_registry import ModelRegistry

# Setup logger
logger = get_logger('user_feedback_integration')


@dataclass
class FeedbackItem:
    """Class for storing user feedback."""
    feedback_id: str
    prediction_id: str
    feedback_type: str
    feedback_value: Any
    user_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


class UserFeedbackManager:
    """
    Manager for collecting and processing user feedback.
    
    This class provides methods for collecting user feedback on predictions
    and incorporating it into model training.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the user feedback manager.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Set default configuration
        self.storage_path = self.config.get('storage_path', 'ml/feedback')
        self.feedback_types = self.config.get('feedback_types', [
            'accuracy', 'usefulness', 'confidence', 'custom'
        ])
        self.max_queue_size = self.config.get('max_queue_size', 1000)
        self.auto_save_interval = self.config.get('auto_save_interval', 300)  # seconds
        
        # Create storage directory if it doesn't exist
        os.makedirs(self.storage_path, exist_ok=True)
        
        # Initialize feedback storage
        self.feedback_items = {}
        self.feedback_by_prediction = {}
        self._load_feedback()
        
        # Initialize feedback queue for processing
        self.feedback_queue = queue.Queue(maxsize=self.max_queue_size)
        
        # Initialize model registry
        self.model_registry = ModelRegistry(self.config.get('model_registry_config'))
        
        # Initialize processing thread
        self.processing_thread = None
        self.stop_event = threading.Event()
        
        # Initialize auto-save thread
        self.auto_save_thread = None
        
        # Initialize metrics
        self.metrics = {
            'total_feedback': 0,
            'processed_feedback': 0,
            'feedback_by_type': {},
            'feedback_by_model': {}
        }
        
        logger.info("User feedback manager initialized")
    
    def start(self):
        """Start the feedback manager."""
        # Start processing thread
        self.stop_event.clear()
        self.processing_thread = threading.Thread(
            target=self._processing_loop,
            name="feedback-processor",
            daemon=True
        )
        self.processing_thread.start()
        
        # Start auto-save thread
        self.auto_save_thread = threading.Thread(
            target=self._auto_save_loop,
            name="feedback-auto-saver",
            daemon=True
        )
        self.auto_save_thread.start()
        
        logger.info("Started user feedback manager")
    
    def stop(self):
        """Stop the feedback manager."""
        # Signal threads to stop
        self.stop_event.set()
        
        # Wait for threads to finish
        if self.processing_thread:
            self.processing_thread.join(timeout=5.0)
        
        if self.auto_save_thread:
            self.auto_save_thread.join(timeout=5.0)
        
        # Save feedback
        self._save_feedback()
        
        logger.info("Stopped user feedback manager")
    
    def add_feedback(self,
                    prediction_id: str,
                    feedback_type: str,
                    feedback_value: Any,
                    user_id: Optional[str] = None,
                    metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Add user feedback.
        
        Args:
            prediction_id: ID of the prediction
            feedback_type: Type of feedback
            feedback_value: Feedback value
            user_id: Optional user ID
            metadata: Optional metadata
            
        Returns:
            Feedback ID
        """
        # Validate feedback type
        if feedback_type not in self.feedback_types and feedback_type != 'custom':
            logger.warning(f"Unknown feedback type: {feedback_type}, treating as 'custom'")
            feedback_type = 'custom'
        
        # Generate feedback ID
        feedback_id = str(uuid.uuid4())
        
        # Create feedback item
        feedback_item = FeedbackItem(
            feedback_id=feedback_id,
            prediction_id=prediction_id,
            feedback_type=feedback_type,
            feedback_value=feedback_value,
            user_id=user_id,
            metadata=metadata or {}
        )
        
        # Add to storage
        self.feedback_items[feedback_id] = feedback_item
        
        # Add to prediction index
        if prediction_id not in self.feedback_by_prediction:
            self.feedback_by_prediction[prediction_id] = []
        self.feedback_by_prediction[prediction_id].append(feedback_id)
        
        # Add to queue for processing
        try:
            self.feedback_queue.put(feedback_item, block=False)
        except queue.Full:
            logger.warning("Feedback queue is full, item will not be processed immediately")
        
        # Update metrics
        self.metrics['total_feedback'] += 1
        self.metrics['feedback_by_type'][feedback_type] = \
            self.metrics['feedback_by_type'].get(feedback_type, 0) + 1
        
        logger.info(f"Added feedback {feedback_id} for prediction {prediction_id}")
        return feedback_id
    
    def get_feedback(self, feedback_id: str) -> Optional[FeedbackItem]:
        """
        Get feedback by ID.
        
        Args:
            feedback_id: Feedback ID
            
        Returns:
            FeedbackItem object or None if not found
        """
        return self.feedback_items.get(feedback_id)
    
    def get_feedback_for_prediction(self, prediction_id: str) -> List[FeedbackItem]:
        """
        Get all feedback for a prediction.
        
        Args:
            prediction_id: Prediction ID
            
        Returns:
            List of FeedbackItem objects
        """
        feedback_ids = self.feedback_by_prediction.get(prediction_id, [])
        return [self.feedback_items[fid] for fid in feedback_ids if fid in self.feedback_items]
    
    def get_all_feedback(self) -> Dict[str, FeedbackItem]:
        """
        Get all feedback.
        
        Returns:
            Dictionary of feedback ID to FeedbackItem object
        """
        return self.feedback_items
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get feedback metrics.
        
        Returns:
            Dictionary with metrics
        """
        return self.metrics
    
    def _load_feedback(self):
        """Load feedback from storage."""
        try:
            feedback_file = os.path.join(self.storage_path, 'feedback.json')
            if os.path.exists(feedback_file):
                with open(feedback_file, 'r') as f:
                    feedback_data = json.load(f)
                
                for feedback_id, item_data in feedback_data.items():
                    # Convert string timestamp to datetime
                    item_data['timestamp'] = datetime.fromisoformat(item_data['timestamp'])
                    
                    # Create FeedbackItem object
                    feedback_item = FeedbackItem(**item_data)
                    
                    # Add to storage
                    self.feedback_items[feedback_id] = feedback_item
                    
                    # Add to prediction index
                    prediction_id = feedback_item.prediction_id
                    if prediction_id not in self.feedback_by_prediction:
                        self.feedback_by_prediction[prediction_id] = []
                    self.feedback_by_prediction[prediction_id].append(feedback_id)
                    
                    # Update metrics
                    self.metrics['total_feedback'] += 1
                    self.metrics['feedback_by_type'][feedback_item.feedback_type] = \
                        self.metrics['feedback_by_type'].get(feedback_item.feedback_type, 0) + 1
                
                logger.info(f"Loaded {len(self.feedback_items)} feedback items from storage")
            
        except Exception as e:
            logger.error(f"Error loading feedback: {str(e)}")
    
    def _save_feedback(self):
        """Save feedback to storage."""
        try:
            feedback_file = os.path.join(self.storage_path, 'feedback.json')
            
            # Convert feedback items to serializable format
            feedback_data = {}
            for feedback_id, item in self.feedback_items.items():
                item_dict = item.__dict__.copy()
                
                # Convert datetime to string
                item_dict['timestamp'] = item_dict['timestamp'].isoformat()
                
                feedback_data[feedback_id] = item_dict
            
            with open(feedback_file, 'w') as f:
                json.dump(feedback_data, f, indent=2)
            
            logger.info(f"Saved {len(self.feedback_items)} feedback items to storage")
            
        except Exception as e:
            logger.error(f"Error saving feedback: {str(e)}")
    
    def _processing_loop(self):
        """Processing loop for feedback items."""
        logger.info("Started feedback processing thread")
        
        while not self.stop_event.is_set():
            try:
                # Get feedback item from queue with timeout
                try:
                    feedback_item = self.feedback_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # Process feedback item
                try:
                    self._process_feedback(feedback_item)
                    
                    # Update metrics
                    self.metrics['processed_feedback'] += 1
                    
                    logger.debug(f"Processed feedback {feedback_item.feedback_id}")
                    
                except Exception as e:
                    logger.error(f"Error processing feedback {feedback_item.feedback_id}: {str(e)}")
                
                finally:
                    # Mark task as done
                    self.feedback_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in feedback processing thread: {str(e)}")
        
        logger.info("Stopped feedback processing thread")
    
    def _auto_save_loop(self):
        """Auto-save loop for feedback items."""
        logger.info("Started feedback auto-save thread")
        
        while not self.stop_event.is_set():
            try:
                # Sleep for auto-save interval
                for _ in range(int(self.auto_save_interval * 10)):
                    if self.stop_event.is_set():
                        break
                    time.sleep(0.1)
                
                # Save feedback
                if not self.stop_event.is_set():
                    self._save_feedback()
                
            except Exception as e:
                logger.error(f"Error in feedback auto-save thread: {str(e)}")
        
        logger.info("Stopped feedback auto-save thread")
    
    def _process_feedback(self, feedback_item: FeedbackItem):
        """
        Process a feedback item.
        
        Args:
            feedback_item: FeedbackItem object
        """
        # Extract model information from metadata
        model_id = feedback_item.metadata.get('model_id')
        model_version = feedback_item.metadata.get('model_version')
        feedback_type = feedback_item.feedback_type
        feedback_score = getattr(feedback_item, 'score', 0.5)  # Default score if not provided

        if model_id:
            # Update model metrics
            if model_id not in self.metrics['feedback_by_model']:
                self.metrics['feedback_by_model'][model_id] = {
                    'total': 0,
                    'by_type': {}
                }

            self.metrics['feedback_by_model'][model_id]['total'] += 1

            self.metrics['feedback_by_model'][model_id]['by_type'][feedback_type] = \
                self.metrics['feedback_by_model'][model_id]['by_type'].get(feedback_type, 0) + 1

            # Update model performance metrics based on feedback
            self._update_model_performance(model_id, feedback_type, feedback_score)

            # Process feedback for model improvements
            self._process_feedback_for_model_improvement(model_id, feedback_type, feedback_score)

            # Trigger model update if needed
            self._trigger_model_update_if_needed(model_id, feedback_type, feedback_score)
    
    def _update_model_performance(self, model_id: str, feedback_type: str, feedback_score: float) -> None:
        """
        Update model performance metrics based on user feedback.
        
        Args:
            model_id: ID of the model
            feedback_type: Type of feedback
            feedback_score: Feedback score
        """
        try:
            # Initialize model performance tracking if not exists
            if model_id not in self.model_performance:
                self.model_performance[model_id] = {
                    'accuracy_feedback': [],
                    'usability_feedback': [],
                    'overall_rating': 0.0,
                    'feedback_count': 0
                }
            
            # Store feedback score
            if feedback_type in ['accuracy', 'prediction_quality']:
                self.model_performance[model_id]['accuracy_feedback'].append(feedback_score)
            elif feedback_type in ['usability', 'user_experience']:
                self.model_performance[model_id]['usability_feedback'].append(feedback_score)
            
            # Update overall rating
            all_scores = (self.model_performance[model_id]['accuracy_feedback'] + 
                         self.model_performance[model_id]['usability_feedback'])
            
            if all_scores:
                self.model_performance[model_id]['overall_rating'] = sum(all_scores) / len(all_scores)
                self.model_performance[model_id]['feedback_count'] = len(all_scores)
            
            logger.info(f"Updated performance metrics for model {model_id}")
            
        except Exception as e:
            logger.error(f"Error updating model performance: {e}")
    
    def _process_feedback_for_model_improvement(self, model_id: str, feedback_type: str, feedback_score: float) -> None:
        """
        Process feedback to identify potential model improvements.
        
        Args:
            model_id: ID of the model
            feedback_type: Type of feedback
            feedback_score: Feedback score
        """
        try:
            # Check if feedback score indicates poor performance
            if feedback_score < 0.3:  # Poor performance threshold
                self._flag_model_for_retraining(model_id, feedback_type, feedback_score)
            
            # Check for consistent low ratings
            if model_id in self.model_performance:
                recent_scores = (self.model_performance[model_id]['accuracy_feedback'][-5:] + 
                               self.model_performance[model_id]['usability_feedback'][-5:])
                
                if len(recent_scores) >= 3:
                    avg_recent = sum(recent_scores) / len(recent_scores)
                    if avg_recent < 0.5:  # Consistent poor performance
                        self._flag_model_for_review(model_id, avg_recent)
            
            logger.debug(f"Processed feedback for model improvement: {model_id}")
            
        except Exception as e:
            logger.error(f"Error processing feedback for improvement: {e}")
    
    def _flag_model_for_retraining(self, model_id: str, feedback_type: str, score: float) -> None:
        """Flag a model for retraining based on poor feedback."""
        try:
            flag_data = {
                'model_id': model_id,
                'reason': f'Poor {feedback_type} feedback',
                'score': score,
                'timestamp': datetime.now().isoformat(),
                'priority': 'high' if score < 0.2 else 'medium'
            }
            
            # Store flag for review
            if not hasattr(self, 'retraining_flags'):
                self.retraining_flags = []
            
            self.retraining_flags.append(flag_data)
            logger.warning(f"Model {model_id} flagged for retraining due to poor {feedback_type} feedback (score: {score})")
            
        except Exception as e:
            logger.error(f"Error flagging model for retraining: {e}")
    
    def _flag_model_for_review(self, model_id: str, avg_score: float) -> None:
        """Flag a model for review based on consistently poor performance."""
        try:
            review_data = {
                'model_id': model_id,
                'reason': 'Consistently poor performance',
                'average_score': avg_score,
                'timestamp': datetime.now().isoformat(),
                'action_required': 'model_review'
            }
            
            # Store flag for review
            if not hasattr(self, 'review_flags'):
                self.review_flags = []
            
            self.review_flags.append(review_data)
            logger.warning(f"Model {model_id} flagged for review due to consistent poor performance (avg: {avg_score})")
            
        except Exception as e:
            logger.error(f"Error flagging model for review: {e}")

    def _trigger_model_update_if_needed(self, model_id: str, feedback_type: str, feedback_score: float) -> None:
        """
        Trigger model update based on feedback patterns.

        Args:
            model_id: ID of the model
            feedback_type: Type of feedback
            feedback_score: Feedback score
        """
        try:
            # Check if immediate update is needed based on feedback
            if feedback_score < 0.2:  # Very poor feedback
                self._schedule_immediate_model_update(model_id, 'critical_feedback')
                return

            # Check accumulated feedback for update triggers
            if model_id in self.model_performance:
                performance = self.model_performance[model_id]

                # Trigger update if we have enough feedback and performance is declining
                if performance['feedback_count'] >= 10:
                    if performance['overall_rating'] < 0.4:
                        self._schedule_model_update(model_id, 'poor_performance')
                    elif performance['feedback_count'] % 50 == 0:  # Regular updates
                        self._schedule_model_update(model_id, 'regular_update')

            logger.debug(f"Evaluated update trigger for model {model_id}")

        except Exception as e:
            logger.error(f"Error triggering model update: {e}")

    def _schedule_immediate_model_update(self, model_id: str, reason: str) -> None:
        """Schedule immediate model update."""
        try:
            update_request = {
                'model_id': model_id,
                'reason': reason,
                'priority': 'immediate',
                'timestamp': datetime.now().isoformat(),
                'status': 'pending'
            }

            # Store update request
            if not hasattr(self, 'update_requests'):
                self.update_requests = []

            self.update_requests.append(update_request)
            logger.warning(f"Scheduled immediate update for model {model_id}: {reason}")

        except Exception as e:
            logger.error(f"Error scheduling immediate update: {e}")

    def _schedule_model_update(self, model_id: str, reason: str) -> None:
        """Schedule regular model update."""
        try:
            update_request = {
                'model_id': model_id,
                'reason': reason,
                'priority': 'normal',
                'timestamp': datetime.now().isoformat(),
                'status': 'pending'
            }

            # Store update request
            if not hasattr(self, 'update_requests'):
                self.update_requests = []

            self.update_requests.append(update_request)
            logger.info(f"Scheduled update for model {model_id}: {reason}")

        except Exception as e:
            logger.error(f"Error scheduling update: {e}")
