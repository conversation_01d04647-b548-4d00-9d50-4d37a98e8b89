"""
Liquidity Sweep Strategy (Enhanced with Trap Pattern Detection) - v3

Trades liquidity sweep patterns where price takes out key S/R levels
to grab liquidity before reversing. Now enhanced with institutional
trap pattern detection for higher quality signals.

ENHANCEMENTS:
- Integrated Enhanced Trap Pattern Detector for institutional activity detection
- Added institutional confidence filtering (30%+ probability threshold)
- Enhanced signal quality through trap pattern confluence
- Improved false signal reduction via institutional intent analysis

NO PLACEHOLDERS. FULL IMPLEMENTATION. REAL DATA ONLY via UnifiedAPIGateway.
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import math # For math.exp in weighted hits
from scipy import stats  # For proper statistical calculations
import sys
import os
from pathlib import Path

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

# --- Standard Import for BaseStrategy ---
from strategies.base_strategy import BaseStrategy, StrategySignal, SignalDirection, SignalStrength

# --- Enhanced API Gateway Import ---
try:
    from api_robustness.unified_api_gateway import get_api_gateway
    from data.loaders.polygon_api import create_polygon_client
    UNIFIED_GATEWAY_AVAILABLE_FOR_LSS = True
    logging.info("Enhanced API Gateway loaded for LiquiditySweepStrategy")
except ImportError:
    try:
        # Fallback to basic API
        from api_robustness.unified_api_gateway import get_api_gateway
        UNIFIED_GATEWAY_AVAILABLE_FOR_LSS = True
        create_polygon_client = None
        logging.warning("Using fallback API Gateway for LiquiditySweepStrategy")
    except ImportError:
        UNIFIED_GATEWAY_AVAILABLE_FOR_LSS = False
        logging.warning("No API Gateway available for LiquiditySweepStrategy")
        get_api_gateway = None
        create_polygon_client = None
# --- End Enhanced API Gateway Import ---

# --- Enhanced Trap Pattern Detector Import ---
ENHANCED_TRAP_DETECTOR_AVAILABLE = False
EnhancedTrapPatternDetector = None

try:
    # Try absolute import from analyzers directory
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)  # Go up one level from strategies to project root
    analyzers_path = os.path.join(project_root, 'analyzers')

    # Add both project root and analyzers to path
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    if analyzers_path not in sys.path:
        sys.path.insert(0, analyzers_path)

    # Try direct import from analyzers path (already added to sys.path)
    from enhanced_trap_pattern_detector import EnhancedTrapPatternDetector
    ENHANCED_TRAP_DETECTOR_AVAILABLE = True

except ImportError:
    try:
        # Fallback: try importing directly from analyzers path
        from enhanced_trap_pattern_detector import EnhancedTrapPatternDetector
        ENHANCED_TRAP_DETECTOR_AVAILABLE = True
    except ImportError:
        ENHANCED_TRAP_DETECTOR_AVAILABLE = False
        EnhancedTrapPatternDetector = None

# --- Gamma Squeeze Detector Import ---
GAMMA_SQUEEZE_DETECTOR_AVAILABLE = False
GammaSqueezeDetector = None

try:
    # Import gamma squeeze detector from same analyzers path
    from gamma_squeeze_detector import GammaSqueezeDetector
    GAMMA_SQUEEZE_DETECTOR_AVAILABLE = True
except ImportError:
    GAMMA_SQUEEZE_DETECTOR_AVAILABLE = False
    GammaSqueezeDetector = None

# --- End Enhanced Analyzer Imports ---

logger = logging.getLogger(__name__)

# Log import status after logger is defined
if ENHANCED_TRAP_DETECTOR_AVAILABLE:
    logger.info("Enhanced Trap Pattern Detector imported successfully")
else:
    logger.warning("Enhanced Trap Pattern Detector not available - institutional filtering disabled")

class LiquiditySweepStrategy(BaseStrategy):

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration focused on robust liquidity sweep logic, not overfitted parameters."""
        return {
            'enabled': True,
            'min_confidence': 0.55, # Reasonable threshold based on strategy logic
            'max_signals_per_ticker': 3, # Allow multiple quality signals

            'timeframes_to_analyze': ['1h', '4h', '1d'], # Multi-timeframe analysis
            'data_lookback_days': 120, # Sufficient for level identification without overfitting

            # Enhanced quality filtering
            'quality_filters': {
                'enabled': True,
                'min_level_age_bars': 10,           # Levels must be at least 10 bars old
                'min_volume_spike_ratio': 1.8,      # Require significant volume spike
                'min_wick_rejection_ratio': 2.0,    # Strong rejection candles only
                'confluence_required': True,        # Require multiple timeframe confluence
                'trend_alignment_bonus': 0.15,      # Bonus for trend-aligned signals
            },

            # Adaptive thresholds based on market conditions
            'adaptive_parameters': {
                'enabled': True,
                'volatility_adjustment': True,
                'volume_adjustment': True,
                'trend_adjustment': True,
                'market_regime_detection': True,
                'confidence_scaling': True,         # Scale confidence based on market conditions
            },

            'level_identification': {
                'lookback_bars': 80,             # Reasonable lookback for level identification
                'min_touches_for_level': 2,      # Standard requirement - 2+ touches make a level
                'clustering_tolerance_pct': 0.003, # Reasonable clustering tolerance
                'min_level_strength_final': 0.50, # Balanced strength requirement
                'dynamic_precision_enabled': True,
                'weighted_hits_significance': 2.5, # Reasonable significance threshold
                'failed_breakout_bonus_weight': 0.3, # Moderate bonus for failed breakouts
                'adaptive_strength_threshold': True,
                'recent_touch_bonus': 0.15,      # Moderate bonus for recently tested levels
            },

            # Enhanced institutional analysis (replaces candle patterns)
            'institutional_analysis': {
                'enabled': True,
                'quiet_accumulation_detection': True,    # Key: detect low-volume accumulation
                'range_based_analysis': True,            # Focus on range-bound activity
                'net_liquidity_analysis': True,          # Analyze accumulation vs distribution
                'systematic_pattern_detection': True,    # Institutional footprint detection
                'min_campaign_duration_hours': 6,        # Minimum institutional campaign duration
                'max_campaign_duration_hours': 72,       # Maximum campaign duration
            },

            # Flow physics integration (NEW)
            'flow_physics_integration': {
                'enabled': True,
                'use_net_flow_bias': True,               # Primary signal from flow physics
                'flow_strength_threshold': 0.6,         # Minimum flow strength to consider
                'flow_consistency_threshold': 0.5,      # Minimum flow consistency
                'absorption_analysis_lookback': 30,     # Bars for absorption fallback analysis
                'quiet_flow_preference': True,          # Prefer normal volume flows
                'systematic_flow_detection': True,      # Look for systematic order working
                'accumulation_flow_patterns': True,     # Detect accumulation patterns
                'distribution_flow_patterns': True,     # Detect distribution patterns
                'timeframe_alignment_preference': ['15m', '30m', '1h', '4h'],  # TF priority order
            },

            'sweep_detection': {
                'min_penetration_pct': 0.001,           # Minimum meaningful penetration
                'max_penetration_pct': 0.025,           # Maximum before it's a real breakout
                'max_bars_for_sweep_rejection': 5,      # Maximum bars to look back for sweep rejection
                'adaptive_penetration_range': True,
                'use_candle_patterns': False,           # REMOVED: No candle pattern dependencies
                'use_volume_spikes': False,             # REMOVED: No volume spike requirements
                'focus_on_absorption': True,            # NEW: Focus on level absorption
                'systematic_pattern_preference': True,  # NEW: Prefer systematic patterns
            },

            # Volume analysis (modified approach)
            'volume_analysis': {
                'require_volume_spikes': False,         # REMOVED: No volume spike requirements
                'quiet_volume_preference': True,        # NEW: Prefer normal volume patterns
                'systematic_volume_patterns': True,     # NEW: Look for systematic patterns
                'absorption_volume_analysis': True,     # NEW: Analyze absorption efficiency
                'volume_distribution_analysis': True,   # NEW: Volume at price levels
            },

            # Range quality analysis (enhanced)
            'range_analysis': {
                'enabled': True,
                'min_range_quality': 0.6,              # Quality threshold for range consideration
                'accumulation_detection': True,         # Detect accumulation within ranges
                'distribution_detection': True,         # Detect distribution within ranges
                'campaign_stage_detection': True,       # Detect campaign progression stages
                'range_compression_analysis': True,     # Detect range compression (completion signal)
            },

            'reversal_confirmation': {
                'min_bars_back_above_below_level': 1,
                'min_reversal_move_from_extreme_pct': 0.005, # Larger reversal required
                'adaptive_reversal_threshold': True,
                'quick_reversal_bonus': 0.3,         # Higher bonus for fast reversals
                'momentum_confirmation': True,        # Require momentum confirmation
            },

            'risk_management': { # Balanced risk management
                'stop_loss_buffer_pct_beyond_sweep': 0.0025, # Balanced stop buffer
                'take_profit_r_multiples': [1.5, 2.5, 4.0], # Balanced R:R ratios
                'min_rr_ratio_required': 1.3,      # Balanced minimum R:R
                'adaptive_risk_sizing': True,
                'max_risk_per_trade': 0.018,       # Balanced risk per trade
                'position_sizing_by_confidence': True, # Size by confidence
            },

            'confidence_weights': { # Enhanced for institutional analysis
                'range_quality': 0.30,                    # NEW: Range quality and containment
                'net_flow_bias_strength': 0.25,           # NEW: Flow physics integration
                'institutional_footprint': 0.20,          # NEW: Systematic patterns
                'level_absorption_efficiency': 0.15,      # NEW: Absorption analysis
                'campaign_stage_progression': 0.10,       # NEW: Campaign stage assessment
            },

            'session_filtering': {
                'enabled': True,
                'asian_quiet_multiplier': 0.9,      # Light penalty for Asian session
                'london_active_multiplier': 1.1,    # Light bonus for London
                'overlap_multiplier': 1.2,          # Moderate bonus for overlap
                'transition_multiplier': 0.95,      # Very light penalty for transitions
                'weekend_filtering': True,
                'high_impact_news_filtering': False, # Keep it simple
            },

            'liquidity_analysis': {
                'enabled': True,
                'min_liquidity_concentration': 0.5,  # Balanced threshold
                'min_sweep_volume_ratio': 1.8,       # Balanced volume requirement
                'use_order_book_data': True,
                'use_options_liquidity': True,
                'stop_cluster_detection': True,
                'liquidity_gradient_analysis': True,
                'institutional_flow_confirmation': False, # Don't require institutional flow
            },

            # Enhanced market regime detection
            'market_regime': {
                'volatility_lookback': 30,
                'trend_lookback': 20,
                'volume_lookback': 25,
                'high_vol_threshold': 0.020,        # Lower threshold for high vol
                'low_vol_threshold': 0.008,         # Lower threshold for low vol
                'trending_threshold': 0.012,        # Lower threshold for trending
                'ranging_threshold': 0.006,         # Lower threshold for ranging
                'regime_stability_required': True,   # Require stable regime
            },

            # Quality assurance
            'quality_assurance': {
                'min_signal_separation_hours': 4,   # Minimum time between signals
                'max_daily_signals': 1,             # Maximum 1 signal per day per ticker
                'backtest_validation': True,        # Validate against recent performance
                'confidence_decay_rate': 0.05,      # Decay confidence over time
                'false_signal_penalty': 0.2,        # Penalty for recent false signals
            },

            # Enhanced Trap Pattern Detection Integration
            'trap_pattern_integration': {
                'enabled': True,                     # Enable trap pattern filtering
                'min_institutional_probability': 0.3, # Minimum 30% institutional probability
                'institutional_confidence_boost': 0.15, # Boost confidence for institutional patterns
                'require_institutional_confluence': False, # Don't require, but boost when present
                'trap_pattern_timeout_hours': 24,   # How long trap patterns remain valid
                'enhanced_pattern_weight': 0.2,     # Weight for enhanced pattern confidence
                'liquidity_imbalance_threshold': 0.2, # Threshold for significant liquidity imbalance
                'multi_level_sweep_bonus': 0.1,     # Bonus for multi-level sweep patterns
                'absorption_pattern_bonus': 0.08,   # Bonus for absorption patterns
            },
            
            # Gamma Squeeze Integration
            'gamma_squeeze_analysis': {
                'enabled': True,                     # Enable gamma squeeze analysis
                'gamma_concentration_threshold': 0.6, # 60% gamma concentration for high-grade signals
                'gamma_wall_proximity_bonus': 0.2,   # Bonus when sweep near gamma wall
                'dealer_imbalance_threshold': 0.3,   # 30% dealer imbalance threshold
                'charm_acceleration_bonus': 0.15,    # Bonus for charm acceleration zones
                'pin_risk_avoidance': True,          # Avoid signals near pin risk
                'min_gamma_for_analysis': 500,       # Minimum total gamma to analyze
                'gamma_wall_percentile': 85,         # Top 15% gamma levels as walls
                'near_gamma_wall_range': 0.015,      # 1.5% range around gamma walls
                'squeeze_confluence_multiplier': 1.4, # Multiply confidence when gamma + sweep align
            }
        }

    def __init__(self, config: Optional[Dict[str, Any]] = None, api_gateway_instance=None):
        super().__init__(config)

        # Initialize enhanced API components
        self.polygon_client = None
        
        if api_gateway_instance:
            self.api_gateway = api_gateway_instance
            logger.info(f"[{self.name}] Initialized with provided API Gateway.")
        elif UNIFIED_GATEWAY_AVAILABLE_FOR_LSS and get_api_gateway:
            try:
                self.api_gateway = get_api_gateway()
                
                # Also initialize enhanced Polygon client if available
                if create_polygon_client:
                    self.polygon_client = create_polygon_client(use_enhanced=True)
                    connection_test = self.polygon_client.test_connection()
                    if connection_test.get("connected"):
                        logger.info(f"[{self.name}] Enhanced Polygon client initialized and tested successfully.")
                    else:
                        logger.warning(f"[{self.name}] Polygon client connection test failed: {connection_test.get('error', 'unknown')}")
                
                logger.info(f"[{self.name}] Initialized enhanced API components.")
            except Exception as e_init_gw:
                logger.critical(f"FATAL [{self.name}]: Enhanced API initialization failed: {e_init_gw}", exc_info=True)
                raise RuntimeError(f"{self.name} requires API Gateway.")
        else:
            raise RuntimeError(f"{self.name} requires Unified API Gateway (module not found).")

        # Initialize Enhanced Trap Pattern Detector if available
        self.trap_detector = None
        if ENHANCED_TRAP_DETECTOR_AVAILABLE and self.config.get('trap_pattern_integration', {}).get('enabled', True):
            try:
                trap_config = {
                    'institutional_volume_threshold': 2.5,  # Slightly lower threshold for more sensitivity
                    'institutional_price_impact_threshold': 0.008,  # 0.8% price impact
                    'institutional_reversal_threshold': 0.004,  # 0.4% reversal threshold
                    'min_confidence': 0.5,  # Minimum confidence for patterns
                    'detect_institutional_activity': True,
                    'detect_liquidity_imbalance': True,
                    'detect_multi_level_sweep': True,
                    'detect_absorption_block': True,
                    'detect_engineered_liquidity': True,
                }
                self.trap_detector = EnhancedTrapPatternDetector(trap_config)
                logger.info(f"[{self.name}] Enhanced Trap Pattern Detector initialized successfully")
            except Exception as e_trap:
                logger.warning(f"[{self.name}] Failed to initialize trap detector: {e_trap}")
                self.trap_detector = None
        else:
            logger.info(f"[{self.name}] Trap pattern detection disabled or not available")

        # Initialize Gamma Squeeze Detector if available
        self.gamma_detector = None
        if GAMMA_SQUEEZE_DETECTOR_AVAILABLE and self.config.get('gamma_squeeze_analysis', {}).get('enabled', True):
            try:
                gamma_config = {
                    'gamma_concentration_threshold': self.config['gamma_squeeze_analysis']['gamma_concentration_threshold'],
                    'gamma_wall_percentile': self.config['gamma_squeeze_analysis']['gamma_wall_percentile'],
                    'dealer_imbalance_threshold': self.config['gamma_squeeze_analysis']['dealer_imbalance_threshold'],
                    'min_gamma_for_squeeze': self.config['gamma_squeeze_analysis']['min_gamma_for_analysis'],
                    'near_money_range': self.config['gamma_squeeze_analysis']['near_gamma_wall_range'],
                }
                self.gamma_detector = GammaSqueezeDetector(gamma_config)
                logger.info(f"[{self.name}] Gamma Squeeze Detector initialized successfully")
            except Exception as e_gamma:
                logger.warning(f"[{self.name}] Failed to initialize gamma detector: {e_gamma}")
                self.gamma_detector = None
        else:
            logger.info(f"[{self.name}] Gamma squeeze analysis disabled or not available")

        logger.info(f"{self.name} initialized successfully.")

    def analyze(self,
               ticker: str,
               data: Dict[str, Any], # Expects 'current_price', can have 'price_data': {'1h': df, '4h': df, '1d': df}
               analysis_results: Dict[str, Any]) -> List[StrategySignal]:
        """
        Analyze data for liquidity sweep signals.

        CRITICAL DATA STRUCTURE REQUIREMENTS:
        ====================================
        The 'data' parameter MUST contain:
        {
            'current_price': float,  # REQUIRED: Current market price
            'price_data': {          # REQUIRED: Historical price data
                '1h': pd.DataFrame,  # Hourly data with OHLCV columns
                '4h': pd.DataFrame,  # 4-hour data with OHLCV columns
                '1d': pd.DataFrame   # Daily data with OHLCV columns
            }
        }

        DataFrame Requirements:
        - Must have columns: ['open', 'high', 'low', 'close', 'volume']
        - Index must be DatetimeIndex (timestamps)
        - Must contain at least 100 bars for proper analysis

        COMMON INTEGRATION MISTAKES:
        ===========================
        1. Missing 'current_price' field -> Returns "Invalid current price" error
        2. Empty price_data dict -> Returns "No price data provided" error
        3. DataFrames with insufficient data -> No signals generated
        4. Wrong column names -> Analysis fails silently
        5. Non-datetime index -> Timestamp operations fail

        Args:
            ticker: Stock ticker symbol (e.g., 'AAPL', 'SPY')
            data: Dictionary containing price data and current price (see structure above)
            analysis_results: Results from all analyzers (not used in this strategy)

        Returns:
            List[StrategySignal]: List of detected liquidity sweep signals
        """
        if not self.is_enabled:
            return []
        
        # Add debug logging
        logger.info(f"[DEBUG] Starting analysis for {ticker} with adjusted parameters")
        logger.info(f"[DEBUG] min_confidence: {self.config['min_confidence']}")
        logger.info(f"[DEBUG] min_level_strength: {self.config['level_identification']['min_level_strength_final']}")
        logger.info(f"[DEBUG] min_penetration: {self.config['sweep_detection']['min_penetration_pct']}")
        logger.info(f"[DEBUG] max_penetration: {self.config['sweep_detection']['max_penetration_pct']}")
        
        # Validate input data
        is_valid, error_msg = self.validate_data(data)
        if not is_valid:
            logger.warning(f"Invalid data for {ticker}: {error_msg}")
            return []
        
        # Extract price data
        price_data = data.get('price_data', {})
        if not price_data:  # Check if price_data dict exists
            logger.warning(f"No price data provided for {ticker}")
            return []
        
        # Get current price
        current_price = self._get_current_price(data)
        if current_price <= 0:
            logger.warning(f"Invalid current price for {ticker}: {current_price}")
            return []
        
        logger.info(f"[DEBUG] Current price for {ticker}: {current_price}")

        signals: List[StrategySignal] = []
        timeframes_to_analyze = self.config['timeframes_to_analyze']
        lookback_days = self.config['data_lookback_days']
        from datetime import timezone
        now_dt = datetime.now(timezone.utc)
        from_dt_str = (now_dt - timedelta(days=lookback_days)).strftime('%Y-%m-%d')
        to_dt_str = now_dt.strftime('%Y-%m-%d')

        for tf_str_short in timeframes_to_analyze: # e.g., '1h', '4h', '1d'
            # Map short tf string to Polygon timespan and multiplier
            poly_timespan, poly_multiplier = self._map_timeframe_to_polygon(tf_str_short)
            if poly_timespan is None:
                logger.warning(f"[{ticker}] LSS: Unsupported timeframe string '{tf_str_short}'. Skipping.")
                continue

            logger.debug(f"[{ticker}] LSS: Analyzing timeframe {tf_str_short} (Polygon: {poly_multiplier}{poly_timespan})")

            # Get or fetch price data for this timeframe
            price_data_tf: Optional[pd.DataFrame] = None
            if 'price_data' in data and isinstance(data['price_data'], dict) and \
               tf_str_short in data['price_data'] and isinstance(data['price_data'][tf_str_short], pd.DataFrame):
                price_data_tf = data['price_data'][tf_str_short].copy()
                # Ensure sufficient length for analysis
                if len(price_data_tf) < self.config['level_identification']['lookback_bars']:
                    logger.debug(f"[{ticker}] LSS: Provided data for {tf_str_short} too short ({len(price_data_tf)} bars), will attempt fetch.")
                    price_data_tf = None # Force fetch

            if price_data_tf is None: # Fetch if not provided or too short
                try:
                    price_data_tf = self.api_gateway.get_price_data(
                        ticker=ticker, timespan=poly_timespan, multiplier=poly_multiplier,
                        from_date=from_dt_str, to_date=to_dt_str
                    )
                except Exception as e:
                    logger.error(f"[{ticker}] LSS: Failed to fetch price data for {tf_str_short}: {e}")
                    continue

            if price_data_tf is None or price_data_tf.empty or \
               len(price_data_tf) < self.config['level_identification']['lookback_bars'] or \
               not all(c in price_data_tf.columns for c in ['open','high','low','close','volume']):
                logger.warning(f"[{ticker}] LSS: Insufficient or malformed data for timeframe {tf_str_short}. Skipping TF.")
                continue

            # Ensure datetime index
            if not isinstance(price_data_tf.index, pd.DatetimeIndex):
                if 'timestamp' in price_data_tf.columns:
                    price_data_tf = price_data_tf.set_index(pd.to_datetime(price_data_tf['timestamp'])).sort_index()
                else:
                    logger.error(f"[{ticker}] LSS: Price data for {tf_str_short} has no datetime index or 'timestamp' column.")
                    continue

            # Calculate average volume for this timeframe's data
            lookback = min(60, len(price_data_tf) // 2)
            avg_volume_tf = price_data_tf['volume'].rolling(window=lookback, min_periods=10).median().iloc[-1]
            if pd.isna(avg_volume_tf) or avg_volume_tf == 0:
                avg_volume_tf = price_data_tf['volume'].mean() # Fallback if rolling mean is NaN
                if pd.isna(avg_volume_tf) or avg_volume_tf == 0:
                    logger.warning(f"[{ticker}] LSS: Could not determine valid average volume for {tf_str_short}. Volume checks may be unreliable.")
                    avg_volume_tf = 1 # Avoid division by zero, but makes volume ratio less meaningful

            identified_levels = self._identify_liquidity_levels(price_data_tf, current_price) # Pass current_price for precision calc
            if not identified_levels:
                logger.debug(f"[{ticker}] LSS: No significant liquidity levels found on {tf_str_short}.")
                continue

            logger.debug(f"[{ticker}] LSS: Found {len(identified_levels)} levels on {tf_str_short}. Checking for sweeps...")

            # Look for sweeps against these levels using the last few bars of price_data_tf
            # Max bars to check for a sweep = sweep_duration + confirmation_bars + buffer
            cfg_sweep = self.config['sweep_detection']
            cfg_reversal = self.config['reversal_confirmation']
            check_depth = cfg_sweep['max_bars_for_sweep_rejection'] + cfg_reversal['min_bars_back_above_below_level'] + 5

            if len(price_data_tf) < check_depth:
                logger.debug(f"[{ticker}] LSS: Not enough bars on {tf_str_short} ({len(price_data_tf)}) to check for full sweep patterns (need {check_depth}).")
                continue

            # Enhanced Analysis: Focus on institutional accumulation/distribution within ranges
            # NEW APPROACH: Use range-based institutional analysis instead of candle patterns
            
            # --- Get Flow Physics Data for this Timeframe ---
            # This assumes `analysis_results` is passed from an orchestrator
            # and contains MTF flow physics data.
            flow_physics_mtf_data = analysis_results.get('flow_physics_mtf_data', {})
            flow_physics_for_this_tf = flow_physics_mtf_data.get(tf_str_short)  # Get specific TF data
            
            # If flow_physics_for_this_tf is None, _analyze_institutional_ranges will use its fallback.
            # It's important that your Flow Physics suite can produce per-timeframe analysis.
            # If Flow Physics is only on one primary timeframe, you might pass that single result
            # and use it as context for all LSS timeframe analyses.
            
            # Prepare data for institutional range analysis
            institutional_data = {
                'current_price': current_price,
                'price_data': {tf_str_short: price_data_tf},
                'timeframe': tf_str_short
            }
            
            # Enhanced analysis_results with flow physics for this specific timeframe
            enhanced_analysis_results = analysis_results.copy()
            if flow_physics_for_this_tf:
                enhanced_analysis_results['flow_physics_data_for_tf'] = flow_physics_for_this_tf
            
            range_analysis = self._analyze_institutional_ranges(institutional_data, enhanced_analysis_results)
            
            if range_analysis and range_analysis.get('has_institutional_activity'):
                # Create signal from institutional campaign detection
                campaign_signal = range_analysis.get('campaign_signal')
                if campaign_signal:
                    signal = self._create_institutional_campaign_signal(
                        ticker, range_analysis['best_range'], campaign_signal['direction'], 
                        campaign_signal['campaign_stage'], tf_str_short, current_price
                    )
                    if signal:
                        signals.append(signal)
                        logger.info(f"[{ticker}] Generated institutional campaign signal on {tf_str_short}: {campaign_signal['direction']} (stage: {campaign_signal['campaign_stage']})")
            
            # Store range analysis for potential factor generation
            if range_analysis:
                if not hasattr(self, '_latest_range_analysis'):
                    self._latest_range_analysis = {}
                self._latest_range_analysis[tf_str_short] = range_analysis

            # Check if we have enough signals from this timeframe
            if signals and len(signals) >= self.config['max_signals_per_ticker']: 
                break  # Break out of timeframe loop

        logger.info(f"[{ticker}] LSS: Generated {len(signals)} total signals across timeframes.")

        # Apply trap pattern filtering if available
        if self.trap_detector and signals:
            signals = self._apply_trap_pattern_filtering(ticker, signals, data)
            logger.info(f"[{ticker}] LSS: {len(signals)} signals after trap pattern filtering.")

        return self.filter_signals(signals) # Base class filter for confidence and max signals

    def analyze_factors(self, ticker: str, data: Dict[str, Any], analysis_results: Dict[str, Any], timeframe: str) -> List[Dict[str, Any]]:
        """
        Generate factor data for institutional liquidity sweep analysis.
        
        This method replaces signal generation with factor generation for ML integration.
        Focuses on institutional footprint detection rather than retail patterns.
        
        Args:
            ticker: Stock ticker symbol
            data: Market data with current_price and price_data
            analysis_results: Results from other analyzers (flow physics, etc.)
            timeframe: Timeframe for analysis
            
        Returns:
            List[Dict[str, Any]]: List of factor dictionaries for ML enhancement
        """
        factors = []
        
        try:
            # Prepare data for institutional range analysis
            institutional_data = {
                'current_price': data.get('current_price'),
                'price_data': {timeframe: data.get('price_data')},
                'timeframe': timeframe
            }
            
            # Enhanced analysis_results with flow physics for this specific timeframe
            enhanced_analysis_results = analysis_results.copy()
            flow_physics_mtf_data = analysis_results.get('flow_physics_mtf_data', {})
            flow_physics_for_tf = flow_physics_mtf_data.get(timeframe)
            if flow_physics_for_tf:
                enhanced_analysis_results['flow_physics_data_for_tf'] = flow_physics_for_tf
            
            # Run institutional range analysis to get sweep patterns
            range_results = self._analyze_institutional_ranges(institutional_data, enhanced_analysis_results)
            
            if range_results:
                # Generate factors from institutional campaign analysis
                factors.extend(self._create_institutional_campaign_factors(ticker, range_results, timeframe))
                factors.extend(self._create_range_quality_factors(ticker, range_results, timeframe))
                factors.extend(self._create_absorption_efficiency_factors(ticker, range_results, timeframe))
                factors.extend(self._create_flow_bias_factors(ticker, range_results, timeframe))
            
            logger.info(f"Generated {len(factors)} liquidity sweep factors for {ticker} on {timeframe}")
            return factors
            
        except Exception as e:
            logger.error(f"Error generating liquidity sweep factors for {ticker}: {e}")
            return factors

    def _map_timeframe_to_polygon(self, tf_str_short: str) -> Tuple[Optional[str], Optional[int]]:
        """Maps short timeframe string (e.g., '1h', '15m', '1d') to Polygon timespan and multiplier."""
        if 'm' in tf_str_short:
            try:
                return 'minute', int(tf_str_short.replace('m', ''))
            except ValueError: return None, None
        elif 'h' in tf_str_short:
            try:
                return 'hour', int(tf_str_short.replace('h', ''))
            except ValueError: return None, None
        elif 'd' in tf_str_short:
            try:
                return 'day', int(tf_str_short.replace('d', ''))
            except ValueError: return None, None
        elif 'w' in tf_str_short:
            try:
                return 'week', int(tf_str_short.replace('w',''))
            except ValueError: return None,None
        return None, None
    def _identify_liquidity_levels(self, price_data_tf: pd.DataFrame, current_price_for_precision: float) -> List[Dict[str, Any]]:
        """
        Identifies significant S/R levels from price_data_tf.
        Uses configuration from `self.config['level_identification']`.
        """
        levels: List[Dict[str, Any]] = []
        cfg_level = self.config['level_identification']
        lookback = cfg_level['lookback_bars']

        if len(price_data_tf) < lookback:
            logger.debug(f"Not enough data ({len(price_data_tf)} bars) for level ID, need {lookback}.")
            return levels

        data_for_levels = price_data_tf.tail(lookback)

        price_precision = self._get_dynamic_precision(current_price_for_precision) if cfg_level['dynamic_precision_enabled'] else 0.01

        # 1. Find initial candidate levels (swing points and high hit frequency points)
        raw_highs = self._find_swing_points(data_for_levels['high'], 'high', lookback_bars=len(data_for_levels))
        raw_lows = self._find_swing_points(data_for_levels['low'], 'low', lookback_bars=len(data_for_levels))

        candidate_levels_swing = [(h, 'resistance') for h in raw_highs] + \
                                 [(l, 'support') for l in raw_lows]

        # Weighted price hits (more recent hits are more important)
        hit_frequency = self._find_weighted_price_hits(data_for_levels, price_precision)
        candidate_levels_hits = []
        for price, hit_data in hit_frequency.items():
            if hit_data['weighted_score'] >= cfg_level['weighted_hits_significance']:
                # Determine type based on relation to current price of *that timeframe's last bar*
                tf_current_price = data_for_levels['close'].iloc[-1]
                level_type = 'resistance' if price > tf_current_price else 'support'
                candidate_levels_hits.append((price, level_type))

        all_raw_levels = candidate_levels_swing + candidate_levels_hits
        if not all_raw_levels: return levels

        # 2. Cluster nearby raw levels
        clustered_raw_levels = self._cluster_levels_enhanced(all_raw_levels, price_precision, cfg_level['clustering_tolerance_pct'])

        # 3. Validate and score clustered levels with adaptive thresholds
        market_regime = self._detect_market_regime(price_data_tf)
        adaptive_strength_threshold = self._get_adaptive_level_strength_threshold(market_regime)

        for level_price, level_type in clustered_raw_levels:
            touches = self._count_level_touches(data_for_levels, level_price, price_precision * 2) # Wider tolerance for touch count
            if touches < cfg_level['min_touches_for_level']:
                continue

            # Calculate strength components
            base_strength_touches = min(1.0, touches / 5.0) # Normalized touch strength (max at 5 touches)

            time_weighted_score_at_level = 0
            # Find closest hit_frequency entry to level_price for its score
            closest_hit_price = min(hit_frequency.keys(), key=lambda p: abs(p-level_price), default=None)
            if closest_hit_price is not None and abs(closest_hit_price - level_price) < price_precision * 2:
                time_weighted_score_at_level = hit_frequency[closest_hit_price]['weighted_score']

            # Normalize time_weighted_score (e.g. if max expected score is 5-10)
            norm_time_weighted_strength = min(1.0, time_weighted_score_at_level / (cfg_level['weighted_hits_significance'] * 2))

            failed_breakout_bonus = self._check_failed_breakout(data_for_levels, level_price, level_type, price_precision)

            # Combine strength
            level_strength = base_strength_touches
            level_strength += norm_time_weighted_strength * 0.2 # Time weight adds up to 0.2
            level_strength += failed_breakout_bonus * cfg_level['failed_breakout_bonus_weight'] # Failed breakout adds up to its weight
            level_strength = min(1.0, level_strength) # Cap at 1.0

            # Get volume context
            volume_context = self._get_volume_context(data_for_levels, level_price)
            level_strength *= volume_context['volume_score']
            level_strength = min(1.0, level_strength)  # Re-cap after volume adjustment

            # Market regime bonuses for strong levels
            if market_regime == 'high_volatility' and level_strength > 0.7:
                level_strength *= 1.1  # 10% bonus for strong levels in volatile markets
            elif market_regime == 'trending' and level_strength > 0.6:
                level_strength *= 1.05  # 5% bonus for decent levels in trending markets
            elif market_regime == 'ranging' and level_strength > 0.5:
                level_strength *= 1.08  # 8% bonus for levels in ranging markets (more reliable)

            level_strength = min(1.0, level_strength)  # Re-cap after bonuses

            # Use adaptive threshold instead of static one
            if level_strength >= adaptive_strength_threshold:
                levels.append({
                    'price': level_price, 'type': level_type, 'touches': touches,
                    'strength': level_strength, 'market_regime': market_regime,
                    'debug_base_str': base_strength_touches, 'debug_time_w_str': norm_time_weighted_strength, 'debug_fail_bo_bonus': failed_breakout_bonus
                })

        # Sort by strength and return top N if needed, or all significant ones
        return sorted(levels, key=lambda x: x['strength'], reverse=True)

    def _analyze_institutional_ranges(self, data: Dict[str, Any], analysis_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Analyze institutional accumulation/distribution within ranges.
        Replaces candle pattern approach with net liquidity analysis.
        
        Args:
            data: Market data with current_price, price_data, and timeframe
            analysis_results: Results from other analyzers (flow physics, etc.)
            
        Returns:
            Dictionary with institutional range analysis results
        """
        try:
            # Extract required data
            current_price = data.get('current_price')
            price_data_dict = data.get('price_data', {})
            timeframe = data.get('timeframe', '1h')
            
            if not current_price or not price_data_dict:
                logger.warning("Missing required data for institutional range analysis")
                return None
            
            # Get price data for the specific timeframe
            if timeframe not in price_data_dict:
                logger.warning(f"Price data for timeframe {timeframe} not available")
                return None
                
            price_df = price_data_dict[timeframe]
            
            # Identify liquidity levels first
            levels = self._identify_liquidity_levels(price_df, current_price)
            
            if not levels:
                logger.debug("No significant liquidity levels found")
                return None
            
            # Group levels into potential ranges
            ranges = self._identify_active_ranges(price_df, levels, current_price)
            
            if not ranges:
                logger.debug("No active ranges identified")
                return None
            
            # Analyze the strongest range
            best_range = ranges[0]  # Already sorted by strength
            
            # Extract flow data safely - prioritize timeframe-specific data
            flow_data = self._extract_flow_data_from_analysis_results(analysis_results)
            
            # Analyze net liquidity bias within this range
            net_bias = self._determine_range_net_bias(price_df, best_range, flow_data)
            
            # Check for institutional campaign completion signals
            campaign_stage = self._assess_campaign_stage(price_df, best_range, net_bias)
            
            # Assess overall range quality
            range_quality = best_range['range_quality']
            
            # Calculate absorption efficiency
            absorption_efficiency = self._calculate_absorption_efficiency(price_df, best_range)
            
            # Return comprehensive analysis
            range_analysis = {
                'active_ranges': ranges,
                'best_range': best_range,
                'net_bias': net_bias,
                'campaign_stage': campaign_stage,
                'range_quality': range_quality,
                'absorption_efficiency': absorption_efficiency,
                'flow_strength': flow_data.get('flow_strength', 0.5) if flow_data else 0.5,
                'flow_consistency': flow_data.get('flow_consistency', 0.5) if flow_data else 0.5,
                'has_institutional_activity': campaign_stage in ['completion', 'acceleration'] and net_bias != 'neutral',
                'timeframe_analyzed': timeframe
            }
            
            # Generate campaign signal if conditions are met
            if range_analysis['has_institutional_activity']:
                campaign_signal = self._create_campaign_signal_data(best_range, net_bias, campaign_stage, range_quality)
                range_analysis['campaign_signal'] = campaign_signal
            
            return range_analysis
            
        except Exception as e:
            logger.error(f"Error in institutional range analysis: {e}")
            return None
    
    def _extract_flow_data_from_analysis_results(self, analysis_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract flow physics data from analysis results safely with timeframe alignment."""
        try:
            # First priority: timeframe-specific flow data
            if 'flow_physics_data_for_tf' in analysis_results:
                tf_specific_data = analysis_results['flow_physics_data_for_tf']
                if isinstance(tf_specific_data, dict) and tf_specific_data:
                    logger.debug("Using timeframe-specific flow physics data")
                    # Log the actual data being used for debugging
                    fp_direction = tf_specific_data.get('net_flow_direction', 'unknown')
                    fp_strength = tf_specific_data.get('flow_strength', 0)
                    logger.debug(f"Flow Physics TF-specific: Direction={fp_direction}, Strength={fp_strength:.3f}")
                    return tf_specific_data
            
            # Second priority: multi-timeframe flow data with intelligent selection
            flow_physics_mtf = analysis_results.get('flow_physics_mtf_data', {})
            if isinstance(flow_physics_mtf, dict) and flow_physics_mtf:
                # Timeframe priority order for flow physics application
                # Shorter timeframes generally provide better granular insights
                tf_priority_order = ['15m', '30m', '1h', '4h', '1d', '5m', '10m']
                
                for tf in tf_priority_order:
                    if tf in flow_physics_mtf and flow_physics_mtf[tf]:
                        flow_data = flow_physics_mtf[tf]
                        fp_direction = flow_data.get('net_flow_direction', 'unknown')
                        fp_strength = flow_data.get('flow_strength', 0)
                        logger.debug(f"Using flow physics data from {tf} timeframe: Direction={fp_direction}, Strength={fp_strength:.3f}")
                        
                        # Add metadata about timeframe alignment
                        flow_data_with_meta = flow_data.copy()
                        flow_data_with_meta['source_timeframe'] = tf
                        flow_data_with_meta['alignment_method'] = 'mtf_priority_selection'
                        return flow_data_with_meta
            
            # Third priority: try different possible locations for flow data
            flow_sources = [
                ('flow_physics_data', analysis_results.get('flow_physics_data', {})),
                ('flow_physics', analysis_results.get('flow_physics', {})),
                ('enhanced_flow_physics', analysis_results.get('enhanced_flow_physics', {})),
                ('direct_analysis_results', analysis_results)  # Direct in analysis_results
            ]
            
            for source_name, source in flow_sources:
                if isinstance(source, dict):
                    # Check for expected flow data keys
                    expected_keys = ['net_flow_direction', 'flow_strength', 'net_bias']
                    if any(key in source for key in expected_keys):
                        fp_direction = source.get('net_flow_direction', 'unknown')
                        fp_strength = source.get('flow_strength', 0)
                        logger.debug(f"Using fallback flow physics data from {source_name}: Direction={fp_direction}, Strength={fp_strength:.3f}")
                        
                        # Add metadata about source
                        flow_data_with_meta = source.copy()
                        flow_data_with_meta['source_method'] = source_name
                        flow_data_with_meta['alignment_method'] = 'fallback_extraction'
                        return flow_data_with_meta
            
            # Return default if no flow data found
            logger.debug("No flow physics data found, using neutral defaults")
            return {
                'net_flow_direction': 'neutral',
                'flow_strength': 0.5,
                'net_bias': 'neutral',
                'flow_consistency': 0.5,
                'source_method': 'default_fallback',
                'alignment_method': 'none'
            }
            
        except Exception as e:
            logger.warning(f"Error extracting flow data: {e}")
            return None
    
    def _calculate_absorption_efficiency(self, price_df: pd.DataFrame, range_info: Dict[str, Any]) -> float:
        """Calculate overall absorption efficiency for the range."""
        try:
            support_absorption = self._analyze_level_absorption_trend(price_df, range_info['support_level']['price'])
            resistance_absorption = self._analyze_level_absorption_trend(price_df, range_info['resistance_level']['price'])
            
            support_efficiency = support_absorption['absorption_efficiency']
            resistance_efficiency = resistance_absorption['absorption_efficiency']
            
            return (support_efficiency + resistance_efficiency) / 2
            
        except Exception as e:
            logger.warning(f"Error calculating absorption efficiency: {e}")
            return 0.5
    
    def _create_campaign_signal_data(self, range_info: Dict[str, Any], net_bias: str, 
                                   campaign_stage: str, range_quality: float) -> Dict[str, Any]:
        """Create institutional campaign signal data for factor generation."""
        
        # Calculate signal strength based on multiple factors
        stage_weights = {
            'completion': 1.0,
            'acceleration': 0.8,
            'building': 0.6,
            'initiation': 0.4
        }
        
        stage_strength = stage_weights.get(campaign_stage, 0.4)
        bias_strength = 1.0 if net_bias in ['bullish', 'bearish'] else 0.3
        
        signal_strength = (stage_strength * 0.4 + range_quality * 0.3 + bias_strength * 0.3)
        
        # Calculate confidence
        confidence = min(1.0, range_quality * 1.2)
        
        return {
            'direction': net_bias,
            'strength': signal_strength,
            'confidence': confidence,
            'campaign_stage': campaign_stage,
            'range_data': range_info,
            'signal_type': 'institutional_campaign'
        }
    
    def _identify_active_ranges(self, price_data_tf: pd.DataFrame, levels: List[Dict[str, Any]], 
                               current_price: float) -> List[Dict[str, Any]]:
        """Identify active ranges from liquidity levels"""
        ranges = []
        
        # Separate support and resistance levels
        support_levels = [l for l in levels if l['type'] == 'support' and l['price'] < current_price]
        resistance_levels = [l for l in levels if l['type'] == 'resistance' and l['price'] > current_price]
        
        # Find viable ranges where current price is contained
        for support in support_levels:
            for resistance in resistance_levels:
                if support['price'] < current_price < resistance['price']:
                    range_width_pct = (resistance['price'] - support['price']) / support['price']
                    
                    # Only consider reasonable range widths
                    if 0.005 <= range_width_pct <= 0.08:  # 0.5% to 8%
                        range_info = {
                            'support_level': support,
                            'resistance_level': resistance,
                            'range_width_pct': range_width_pct,
                            'current_position_pct': (current_price - support['price']) / 
                                                  (resistance['price'] - support['price']),
                            'combined_strength': (support['strength'] + resistance['strength']) / 2,
                            'range_quality': self._assess_range_quality(price_data_tf, support, resistance)
                        }
                        ranges.append(range_info)
        
        # Sort by combined strength and return best ranges
        return sorted(ranges, key=lambda x: x['combined_strength'], reverse=True)[:3]
    
    def _determine_range_net_bias(self, price_data_tf: pd.DataFrame, range_info: Dict[str, Any], 
                                 flow_data: Dict[str, Any]) -> str:
        """Determine net accumulation vs distribution bias within range, prioritizing flow physics."""
        
        cfg_fp_integration = self.config.get('flow_physics_integration', {})
        net_bias = 'neutral'  # Default

        # --- Primary: Use Flow Physics Data if available and enabled ---
        if cfg_fp_integration.get('enabled', True) and \
           cfg_fp_integration.get('use_net_flow_bias', True) and \
           flow_data:
            
            # Validate expected keys are present
            expected_keys = ['net_flow_direction', 'flow_strength']
            missing_keys = [key for key in expected_keys if key not in flow_data]
            
            if missing_keys:
                logger.warning(f"Flow Physics data missing expected keys: {missing_keys}. Available keys: {list(flow_data.keys())}")
            else:
                fp_direction = flow_data.get('net_flow_direction', 'neutral')
                fp_strength = flow_data.get('flow_strength', 0.0)
                fp_consistency = flow_data.get('flow_consistency', 0.0)
                source_tf = flow_data.get('source_timeframe', 'unknown')
                alignment_method = flow_data.get('alignment_method', 'unknown')
                
                # Log actual values for debugging
                logger.debug(f"Flow Physics Input - Direction: '{fp_direction}', Strength: {fp_strength:.3f}, "
                           f"Consistency: {fp_consistency:.3f}, Source TF: {source_tf}, Alignment: {alignment_method}")
                
                # Configurable thresholds
                min_fp_strength = cfg_fp_integration.get('flow_strength_threshold', 0.6)
                min_fp_consistency = cfg_fp_integration.get('flow_consistency_threshold', 0.5)
                
                # Enhanced flow strength validation
                strength_sufficient = fp_strength >= min_fp_strength
                consistency_sufficient = fp_consistency >= min_fp_consistency if fp_consistency > 0 else True
                
                logger.debug(f"Flow Physics Validation - Strength OK: {strength_sufficient} "
                           f"({fp_strength:.3f} >= {min_fp_strength}), Consistency OK: {consistency_sufficient} "
                           f"({fp_consistency:.3f} >= {min_fp_consistency})")

                if strength_sufficient and consistency_sufficient:
                    # Enhanced direction parsing
                    fp_direction_lower = fp_direction.lower()
                    
                    # Multiple ways to express accumulation/distribution
                    accumulation_terms = ['accumulation', 'strong_accumulation', 'bullish', 'buying', 'long_bias']
                    distribution_terms = ['distribution', 'strong_distribution', 'bearish', 'selling', 'short_bias']
                    
                    if any(term in fp_direction_lower for term in accumulation_terms):
                        net_bias = 'bullish'
                    elif any(term in fp_direction_lower for term in distribution_terms):
                        net_bias = 'bearish'
                    
                    logger.info(f"Range bias from Flow Physics: {net_bias} (Direction: '{fp_direction}', "
                              f"Strength: {fp_strength:.3f}, Source: {source_tf})")
                    
                    # If Flow Physics gives a strong directional bias, we can prioritize it.
                    if net_bias != 'neutral':
                        return net_bias
                else:
                    logger.debug(f"Flow Physics signal insufficient - Strength: {fp_strength:.3f} "
                               f"(min: {min_fp_strength}), Consistency: {fp_consistency:.3f} (min: {min_fp_consistency})")
        
        # --- Secondary: Analyze absorption efficiency trends (if Flow Physics was neutral or not used) ---
        logger.debug("Flow Physics neutral/insufficient or not primary bias source, checking absorption trends for range bias.")
        support_level = range_info['support_level']
        resistance_level = range_info['resistance_level']
        
        # Use a relevant portion of price_data_tf for absorption trend
        # Configurable lookback for absorption analysis
        absorption_lookback = cfg_fp_integration.get('absorption_analysis_lookback', 30)
        recent_data_for_absorption = price_data_tf.tail(absorption_lookback)
        
        logger.debug(f"Analyzing absorption trends using last {len(recent_data_for_absorption)} bars "
                    f"(requested: {absorption_lookback})")

        support_absorption = self._analyze_level_absorption_trend(recent_data_for_absorption, support_level['price'])
        resistance_absorption = self._analyze_level_absorption_trend(recent_data_for_absorption, resistance_level['price'])
        
        # Log absorption analysis results
        logger.debug(f"Absorption Analysis - Support: getting_stronger={support_absorption['getting_stronger']}, "
                    f"efficiency={support_absorption['absorption_efficiency']:.3f}")
        logger.debug(f"Absorption Analysis - Resistance: getting_stronger={resistance_absorption['getting_stronger']}, "
                    f"efficiency={resistance_absorption['absorption_efficiency']:.3f}")
        
        if support_absorption['getting_stronger'] and not resistance_absorption['getting_stronger']:
            net_bias = 'bullish'  # Support strengthening = accumulation
            logger.info(f"Range bias from Absorption: Bullish (Support absorbing better - "
                       f"Support efficiency: {support_absorption['absorption_efficiency']:.3f}, "
                       f"Resistance efficiency: {resistance_absorption['absorption_efficiency']:.3f})")
        elif resistance_absorption['getting_stronger'] and not support_absorption['getting_stronger']:
            net_bias = 'bearish'  # Resistance strengthening = distribution
            logger.info(f"Range bias from Absorption: Bearish (Resistance absorbing better - "
                       f"Resistance efficiency: {resistance_absorption['absorption_efficiency']:.3f}, "
                       f"Support efficiency: {support_absorption['absorption_efficiency']:.3f})")
        else:
            logger.debug(f"Range bias from Absorption: Neutral or Conflicting absorption trends.")
            net_bias = 'neutral'  # If both or neither are getting stronger, or trends conflict
            
        return net_bias
    
    def _analyze_level_absorption_trend(self, price_data: pd.DataFrame, level_price: float) -> Dict[str, Any]:
        """
        Analyze if a level's absorption is getting stronger over time.
        Optimized for performance with large datasets.
        """
        if len(price_data) < 3:
            logger.debug(f"Insufficient data for absorption analysis: {len(price_data)} bars")
            return {'getting_stronger': False, 'absorption_efficiency': 0.0, 'level_tests': 0}
        
        tolerance = level_price * 0.003  # 0.3% tolerance
        
        # Vectorized approach for better performance
        lows = price_data['low'].values
        highs = price_data['high'].values
        closes = price_data['close'].values
        
        # Find bars that tested the level using vectorized operations
        level_test_mask = (np.abs(lows - level_price) <= tolerance) | (np.abs(highs - level_price) <= tolerance)
        test_indices = np.where(level_test_mask)[0]
        
        if len(test_indices) < 3:
            logger.debug(f"Insufficient level tests for absorption analysis: {len(test_indices)} tests at ${level_price:.2f}")
            return {'getting_stronger': False, 'absorption_efficiency': 0.0, 'level_tests': len(test_indices)}
        
        # Analyze level holding effectiveness for each test
        held_levels = []
        for idx in test_indices:
            close_price = closes[idx]
            # Level holds if close stays on the right side of the level
            if level_price < close_price:  # Resistance level
                held = close_price < level_price + tolerance
            else:  # Support level
                held = close_price > level_price - tolerance
            held_levels.append(held)
        
        # Compare recent vs earlier performance
        num_tests = len(held_levels)
        recent_tests = 3  # Analyze last 3 tests
        
        if num_tests <= recent_tests:
            # All tests are "recent"
            recent_success_rate = sum(held_levels) / num_tests
            early_success_rate = recent_success_rate  # No improvement calculation possible
            trend_improvement = 0.0
        else:
            # Split into recent and earlier tests
            recent_holds = held_levels[-recent_tests:]
            early_holds = held_levels[:-recent_tests]
            
            recent_success_rate = sum(recent_holds) / len(recent_holds)
            early_success_rate = sum(early_holds) / len(early_holds)
            trend_improvement = recent_success_rate - early_success_rate
        
        getting_stronger = trend_improvement > 0.1  # 10% improvement threshold
        
        logger.debug(f"Absorption analysis for ${level_price:.2f}: {num_tests} tests, "
                    f"recent_rate={recent_success_rate:.2f}, early_rate={early_success_rate:.2f}, "
                    f"improvement={trend_improvement:.2f}, getting_stronger={getting_stronger}")
        
        return {
            'getting_stronger': getting_stronger,
            'absorption_efficiency': recent_success_rate,
            'trend_improvement': trend_improvement,
            'level_tests': num_tests,
            'recent_success_rate': recent_success_rate,
            'early_success_rate': early_success_rate
        }
    
    def _assess_campaign_stage(self, price_data_tf: pd.DataFrame, range_info: Dict[str, Any], 
                              net_bias: str) -> str:
        """Assess institutional campaign stage based on range compression and net bias."""
        
        # Check range compression (sign of campaign completion)
        recent_ranges = []
        lookback = min(20, len(price_data_tf))
        
        for i in range(lookback):
            bar = price_data_tf.iloc[-(i+1)]
            bar_range = (bar['high'] - bar['low']) / bar['close']
            recent_ranges.append(bar_range)
        
        avg_recent_range = np.mean(recent_ranges[:5])  # Last 5 bars
        avg_earlier_range = np.mean(recent_ranges[5:]) if len(recent_ranges) > 5 else avg_recent_range
        
        range_compression = (avg_earlier_range - avg_recent_range) / avg_earlier_range if avg_earlier_range > 0 else 0
        
        # Check time spent in range and position
        current_position = range_info.get('current_position_pct', 0.5)
        support_level = range_info['support_level']['price']
        resistance_level = range_info['resistance_level']['price']
        range_width = abs(resistance_level - support_level)
        
        # Calculate absorption efficiency trends
        support_absorption = self._analyze_level_absorption_trend(price_data_tf.tail(15), support_level)
        resistance_absorption = self._analyze_level_absorption_trend(price_data_tf.tail(15), resistance_level)
        
        overall_absorption_efficiency = (support_absorption['absorption_efficiency'] + 
                                       resistance_absorption['absorption_efficiency']) / 2
        
        # Enhanced campaign stage assessment
        if range_compression > 0.3 and net_bias != 'neutral' and overall_absorption_efficiency > 0.7:
            # High compression + clear bias + strong absorption = campaign completion
            return 'completion'
        elif range_compression > 0.15 and net_bias != 'neutral' and overall_absorption_efficiency > 0.6:
            # Moderate compression + bias + good absorption = acceleration phase
            return 'acceleration'
        elif net_bias != 'neutral' and overall_absorption_efficiency > 0.5:
            # Clear bias + decent absorption = building phase
            return 'building'
        elif overall_absorption_efficiency > 0.4:
            # Some absorption but no clear bias = initiation phase
            return 'initiation'
        else:
            # Low absorption = too early or no institutional activity
            return 'early_stage'
    
    def _assess_range_quality(self, price_data_tf: pd.DataFrame, support: Dict[str, Any], 
                             resistance: Dict[str, Any]) -> float:
        """Assess the quality of a range for institutional activity"""
        
        # Factor 1: Combined level strength
        strength_score = (support['strength'] + resistance['strength']) / 2
        
        # Factor 2: Range duration (longer = better for institutional campaigns)
        range_touches = support['touches'] + resistance['touches']
        duration_score = min(1.0, range_touches / 10.0)
        
        # Factor 3: Range width (not too tight, not too wide)
        range_width_pct = abs(resistance['price'] - support['price']) / support['price']
        if 0.01 <= range_width_pct <= 0.05:  # 1-5% is ideal
            width_score = 1.0
        elif 0.005 <= range_width_pct <= 0.08:  # 0.5-8% is acceptable
            width_score = 0.7
        else:
            width_score = 0.3
            
        # Combine scores
        quality_score = (strength_score * 0.5 + duration_score * 0.3 + width_score * 0.2)
        return min(1.0, quality_score)
    
    def _create_institutional_campaign_signal(self, ticker: str, range_info: Dict[str, Any], 
                                            net_bias: str, campaign_stage: str, timeframe_str: str,
                                            current_price: float) -> Optional[StrategySignal]:
        """Create signal based on institutional campaign analysis"""
        
        if net_bias == 'neutral':
            return None
            
        support_price = range_info['support_level']['price']
        resistance_price = range_info['resistance_level']['price']
        
        # Determine signal direction
        if net_bias == 'bullish':
            direction = SignalDirection.LONG
            entry_price = current_price
            stop_loss = support_price * 0.995  # Just below support
            take_profit = [resistance_price * 0.998]  # Just below resistance
            reason = f"Institutional accumulation {campaign_stage} in range ${support_price:.2f}-${resistance_price:.2f}"
        else:  # bearish
            direction = SignalDirection.SHORT
            entry_price = current_price
            stop_loss = resistance_price * 1.005  # Just above resistance
            take_profit = [support_price * 1.002]  # Just above support
            reason = f"Institutional distribution {campaign_stage} in range ${support_price:.2f}-${resistance_price:.2f}"
        
        # Calculate confidence based on new institutional analysis weights
        confidence_weights = self.config.get('confidence_weights', {})
        
        # Extract metrics for confidence calculation
        range_quality = range_info['range_quality']
        flow_strength = range_info.get('flow_strength', 0.5)  # From range analysis
        absorption_efficiency = range_info.get('absorption_efficiency', 0.5)
        
        # Calculate institutional footprint (systematic pattern strength)
        institutional_footprint = (range_quality + absorption_efficiency) / 2
        
        # Calculate campaign stage progression score
        stage_scores = {
            'completion': 1.0,
            'acceleration': 0.8,
            'building': 0.6,
            'initiation': 0.4,
            'early_stage': 0.2
        }
        campaign_progression = stage_scores.get(campaign_stage, 0.4)
        
        # Calculate weighted confidence using institutional analysis
        confidence = (
            range_quality * confidence_weights.get('range_quality', 0.30) +
            flow_strength * confidence_weights.get('net_flow_bias_strength', 0.25) +
            institutional_footprint * confidence_weights.get('institutional_footprint', 0.20) +
            absorption_efficiency * confidence_weights.get('level_absorption_efficiency', 0.15) +
            campaign_progression * confidence_weights.get('campaign_stage_progression', 0.10)
        )
        
        # Ensure confidence is within bounds
        confidence = max(0.0, min(1.0, confidence))
        
        # Apply session filter
        confidence, session_info = self._apply_session_filter(datetime.now(), confidence)
        
        # Calculate risk-reward
        risk_amount = abs(entry_price - stop_loss)
        reward_amount = abs(take_profit[0] - entry_price)
        risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
        
        if confidence >= self.config['min_confidence'] and risk_reward_ratio >= 1.2:
            signal = StrategySignal(
                ticker=ticker,
                strategy_name=self.name,
                direction=direction,
                strength=SignalStrength.STRONG if confidence > 0.7 else SignalStrength.MODERATE,
                confidence=confidence,
                entry=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                timeframe=timeframe_str,
                timestamp=datetime.now(),
                risk_reward=risk_reward_ratio,
                reason=reason + session_info,
                analysis={
                    'campaign_stage': campaign_stage,
                    'net_bias': net_bias,
                    'range_quality': range_info['range_quality'],
                    'support_price': support_price,
                    'resistance_price': resistance_price,
                    'range_position_pct': range_info['current_position_pct'],
                    'institutional_approach': 'net_liquidity_analysis'
                }
            )
            return signal
        
        return None

    # REMOVED: _check_one_bullish_sweep() and _check_one_bearish_sweep()
    # These candle pattern-based methods have been replaced with _analyze_institutional_ranges()
    # New approach focuses on net liquidity and accumulation/distribution rather than retail patterns
    
    # --- Helper methods for level identification ---

    def _find_swing_points(self, series: pd.Series, point_type: str, lookback_bars: int, swing_padding: int = 2) -> List[float]:
        """Find swing highs or lows in price series more robustly."""
        points = []
        # Ensure we have enough data for padding
        if len(series) < (2 * swing_padding + 1):
            return points

        for i in range(swing_padding, len(series) - swing_padding):
            is_swing = True
            current_val = series.iloc[i]
            for j in range(1, swing_padding + 1):
                if point_type == 'high':
                    if not (current_val > series.iloc[i-j] and current_val > series.iloc[i+j]):
                        is_swing = False
                        break
                else:  # low
                    if not (current_val < series.iloc[i-j] and current_val < series.iloc[i+j]):
                        is_swing = False
                        break
            if is_swing:
                points.append(current_val)
        return list(set(points)) # Return unique points

    def _get_dynamic_precision(self, price: float) -> float:
        """Calculate dynamic price precision based on price level."""
        if price <= 0: return 0.01 # Fallback for zero or negative price
        if price < 1: return 0.005 # Half-cent precision
        elif price < 10: return 0.01
        elif price < 50: return 0.05
        elif price < 100: return 0.10
        elif price < 500: return 0.25
        else: return 0.50

    def _find_weighted_price_hits(self, bars_df: pd.DataFrame, price_precision: float) -> Dict[float, Dict]:
        """Find price levels with weighted hit frequency."""
        from collections import defaultdict

        price_hits = defaultdict(lambda: {'count': 0, 'weighted_score': 0.0, 'last_hit_timestamp': None})
        if bars_df.empty: return price_hits

        total_bars = len(bars_df)
        current_time_ts = bars_df.index[-1] if isinstance(bars_df.index, pd.DatetimeIndex) else pd.Timestamp(datetime.now())

        for idx, (timestamp, bar) in enumerate(bars_df.iterrows()):
            time_weight = math.exp(-(total_bars - 1 - idx) / (total_bars * 0.2)) # Exponential decay, recent half gets [PARTIAL]60%+ weight

            # Consider high, low, open, close for hits
            prices_to_check = [bar['high'], bar['low'], bar['open'], bar['close']]
            unique_rounded_prices_for_bar = set()

            for p_val in prices_to_check:
                if pd.isna(p_val): continue
                rounded_p = round(p_val / price_precision) * price_precision if price_precision > 0 else p_val
                unique_rounded_prices_for_bar.add(rounded_p)

            for rounded_p in unique_rounded_prices_for_bar:
                price_hits[rounded_p]['count'] += 1
                price_hits[rounded_p]['weighted_score'] += time_weight
                price_hits[rounded_p]['last_hit_timestamp'] = timestamp
        return price_hits

    def _cluster_levels_enhanced(self, raw_levels: List[Tuple[float, str]],
                                 price_precision: float, clustering_tolerance_pct: float) -> List[Tuple[float, str]]:
        """Cluster nearby price levels."""
        if not raw_levels: return []

        sorted_levels = sorted(raw_levels, key=lambda x: x[0])
        clustered: List[Tuple[float, str]] = []
        if not sorted_levels: return clustered

        current_cluster_prices: List[float] = [sorted_levels[0][0]]
        current_cluster_types: List[str] = [sorted_levels[0][1]]

        for i in range(1, len(sorted_levels)):
            price, level_type = sorted_levels[i]
            # Dynamic tolerance based on the current cluster's average price
            avg_cluster_price = np.mean(current_cluster_prices)
            
            # Make tolerance smaller for higher-priced stocks
            if avg_cluster_price > 100:
                absolute_tolerance = avg_cluster_price * 0.001  # 0.1% for expensive stocks
            else:
                absolute_tolerance = avg_cluster_price * clustering_tolerance_pct

            if abs(price - avg_cluster_price) <= absolute_tolerance:
                current_cluster_prices.append(price)
                current_cluster_types.append(level_type)
            else:
                # Finalize current cluster
                final_cluster_price = np.mean(current_cluster_prices)
                final_cluster_type = max(set(current_cluster_types), key=current_cluster_types.count)
                clustered.append((final_cluster_price, final_cluster_type))

                # Start new cluster
                current_cluster_prices = [price]
                current_cluster_types = [level_type]

        # Finalize the last cluster
        if current_cluster_prices:
            final_cluster_price = np.mean(current_cluster_prices)
            final_cluster_type = max(set(current_cluster_types), key=current_cluster_types.count)
            clustered.append((final_cluster_price, final_cluster_type))

        return clustered

    def _count_level_touches(self, price_data_tf: pd.DataFrame, level_price: float, touch_tolerance_abs: float) -> int:
        """Counts touches considering high/low within an absolute tolerance of the level."""
        touches = 0
        for _, bar in price_data_tf.iterrows():
            # A touch occurs if the bar's range [low, high] overlaps with [level-tolerance, level+tolerance]
            if bar['low'] <= level_price + touch_tolerance_abs and bar['high'] >= level_price - touch_tolerance_abs:
                touches += 1
        return touches

    def _check_failed_breakout(self, price_data_tf: pd.DataFrame, level_price: float, level_type: str, price_precision: float) -> float:
        """Scores if level has seen recent failed breakouts. Higher score = more significant."""
        if len(price_data_tf) < 5: return 0.0 # Need a few bars to see a failed breakout

        failed_breakout_score = 0.0
        # Tolerance for penetration beyond the level before it's considered a breakout attempt
        penetration_tolerance = price_precision * 3

        # Look at last N bars for failed breakouts (e.g., last 10-20 bars)
        relevant_bars = price_data_tf.tail(15)

        for i in range(len(relevant_bars) - 2): # Need at least 2 bars after potential breakout bar
            breakout_candidate_bar = relevant_bars.iloc[i]
            confirmation_bar1 = relevant_bars.iloc[i+1]
            confirmation_bar2 = relevant_bars.iloc[i+2]

            if level_type == 'resistance':
                # Attempted breakout: high of candidate bar penetrates above level
                if breakout_candidate_bar['high'] > level_price + penetration_tolerance:
                    # Failure: close of candidate bar is below level, OR next bars close back below level
                    if breakout_candidate_bar['close'] < level_price or \
                       confirmation_bar1['close'] < level_price or \
                       confirmation_bar2['close'] < level_price:
                        failed_breakout_score += 0.1 # Add small score for each instance

            elif level_type == 'support':
                # Attempted breakdown: low of candidate bar penetrates below level
                if breakout_candidate_bar['low'] < level_price - penetration_tolerance:
                    # Failure: close of candidate bar is above level, OR next bars close back above level
                    if breakout_candidate_bar['close'] > level_price or \
                       confirmation_bar1['close'] > level_price or \
                       confirmation_bar2['close'] > level_price:
                        failed_breakout_score += 0.1

        return min(0.5, failed_breakout_score) # Cap the bonus from failed breakouts

    def validate_data(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate required data for liquidity sweep strategy."""
        if 'current_price' not in data or pd.isna(data['current_price']) or data['current_price'] <= 0:
            return False, "Missing or invalid 'current_price'."
        return True, ""

    def _calculate_atr(self, price_data: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range."""
        if len(price_data) < period + 1:
            return price_data['high'].iloc[-1] * 0.01  # Default to 1% if not enough data
        
        # Calculate True Range
        tr1 = price_data['high'] - price_data['low']
        tr2 = abs(price_data['high'] - price_data['close'].shift(1))
        tr3 = abs(price_data['low'] - price_data['close'].shift(1))
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean().iloc[-1]
        
        return atr

    def _get_dynamic_penetration_range(self, price_data: pd.DataFrame, level_price: float):
        """Get dynamic penetration range based on recent ATR and adaptive parameters."""
        try:
            # Check if adaptive penetration is enabled
            if not self.config['sweep_detection'].get('adaptive_penetration_range', False):
                return self.config['sweep_detection']['min_penetration_pct'], self.config['sweep_detection']['max_penetration_pct']

            atr = self._calculate_atr(price_data, 14)
            atr_pct = atr / level_price if level_price > 0 else 0.01

            # Get market regime for additional adjustment
            market_regime = self._detect_market_regime(price_data)

            # Dynamic range based on ATR and market regime
            base_min = self.config['sweep_detection']['min_penetration_pct']
            base_max = self.config['sweep_detection']['max_penetration_pct']

            # Adjust based on volatility - more aggressive scaling
            volatility_multiplier = min(4.0, max(0.3, atr_pct / 0.008))

            # Market regime adjustments
            if market_regime == 'high_volatility':
                volatility_multiplier *= 1.5
            elif market_regime == 'low_volatility':
                volatility_multiplier *= 0.7
            elif market_regime == 'trending':
                volatility_multiplier *= 1.2

            min_pen = base_min * volatility_multiplier
            max_pen = base_max * volatility_multiplier

            # Ensure reasonable bounds with more flexibility
            min_pen = max(0.0003, min(0.015, min_pen))  # 0.03% to 1.5%
            max_pen = max(min_pen * 1.5, min(0.08, max_pen))  # At least 1.5x min, max 8%

            return min_pen, max_pen

        except Exception as e:
            logger.warning(f"Error calculating dynamic penetration range: {e}")
            # Fallback to static values
            return self.config['sweep_detection']['min_penetration_pct'], self.config['sweep_detection']['max_penetration_pct']

    def _check_for_immediate_reversal(self, sweep_bar, confirmation_bars, level_price, sweep_type):
        """Detect if this is just noise, not a real sweep."""
        if len(confirmation_bars) < 2:
            return False
        
        # Check if price immediately went back through level in next 1-2 bars
        if sweep_type == 'bullish':
            # After sweeping below support, did it immediately break below again?
            for i in range(min(2, len(confirmation_bars))):
                if confirmation_bars['low'].iloc[i] < level_price * 0.998:
                    return True  # Likely just noise
        else:
            # After sweeping above resistance, did it immediately break above again?
            for i in range(min(2, len(confirmation_bars))):
                if confirmation_bars['high'].iloc[i] > level_price * 1.002:
                    return True  # Likely just noise
        
        return False

    def _create_bullish_signal(self, ticker: str, sweep_bar: pd.Series, confirmation_bars_df: pd.DataFrame,
                               support_level: Dict[str, Any], penetration_pct: float, rejection_quality_score: float,
                               timeframe_str: str, current_price_overall: float, analysis_results: Dict[str, Any] = None) -> StrategySignal:
        """Create a bullish liquidity sweep signal"""
        
        level_price = support_level['price']
        cfg_risk = self.config['risk_management']
        
        # Entry price (current price or last confirmation bar close)
        entry_price = confirmation_bars_df['close'].iloc[-1] if len(confirmation_bars_df) > 0 else current_price_overall
        
        # Stop loss: below the sweep low with buffer
        stop_loss = sweep_bar['low'] * (1 - cfg_risk['stop_loss_buffer_pct_beyond_sweep'])
        
        # Take profit levels based on R multiples
        risk_amount = entry_price - stop_loss
        take_profit_levels = []
        for r_multiple in cfg_risk['take_profit_r_multiples']:
            tp_price = entry_price + (risk_amount * r_multiple)
            take_profit_levels.append(tp_price)
        
        # Calculate confidence with gamma analysis
        options_data = self._extract_options_data_from_analysis_results(analysis_results or {})
        confidence = self._calculate_signal_confidence(
            support_level, penetration_pct, rejection_quality_score, 
            sweep_bar, confirmation_bars_df, timeframe_str,
            ticker=ticker, options_data=options_data
        )
        
        # Apply session filter
        confidence, session_info = self._apply_session_filter(sweep_bar.name, confidence)
        
        # Calculate risk-reward ratio
        risk_amount = entry_price - stop_loss
        first_tp = take_profit_levels[0] if take_profit_levels else entry_price * 1.03
        reward_amount = first_tp - entry_price
        risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
        
        # Create signal
        signal = StrategySignal(
            ticker=ticker,
            strategy_name=self.name,
            direction=SignalDirection.LONG,
            strength=SignalStrength.STRONG if confidence > 0.7 else SignalStrength.MODERATE,
            confidence=confidence,
            entry=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit_levels,
            timeframe=timeframe_str,
            timestamp=datetime.now(),
            risk_reward=risk_reward_ratio,
            reason=f"Bullish liquidity sweep at support ${level_price:.2f}, penetration {penetration_pct*100:.1f}%{session_info}",
            analysis={
                'level_price': level_price,
                'level_strength': support_level['strength'],
                'sweep_bar_low': sweep_bar['low'],
                'penetration_pct': penetration_pct,
                'rejection_quality': rejection_quality_score,
                'timeframe': timeframe_str
            }
        )
        
        return signal

    def _create_bearish_signal(self, ticker: str, sweep_bar: pd.Series, confirmation_bars_df: pd.DataFrame,
                               resistance_level: Dict[str, Any], penetration_pct: float, rejection_quality_score: float,
                               timeframe_str: str, current_price_overall: float, analysis_results: Dict[str, Any] = None) -> StrategySignal:
        """Create a bearish liquidity sweep signal"""
        
        level_price = resistance_level['price']
        cfg_risk = self.config['risk_management']
        
        # Entry price (current price or last confirmation bar close)
        entry_price = confirmation_bars_df['close'].iloc[-1] if len(confirmation_bars_df) > 0 else current_price_overall
        
        # Stop loss: above the sweep high with buffer
        stop_loss = sweep_bar['high'] * (1 + cfg_risk['stop_loss_buffer_pct_beyond_sweep'])
        
        # Take profit levels based on R multiples
        risk_amount = stop_loss - entry_price
        take_profit_levels = []
        for r_multiple in cfg_risk['take_profit_r_multiples']:
            tp_price = entry_price - (risk_amount * r_multiple)
            take_profit_levels.append(tp_price)
        
        # Calculate confidence with gamma analysis
        options_data = self._extract_options_data_from_analysis_results(analysis_results or {})
        confidence = self._calculate_signal_confidence(
            resistance_level, penetration_pct, rejection_quality_score, 
            sweep_bar, confirmation_bars_df, timeframe_str,
            ticker=ticker, options_data=options_data
        )
        
        # Apply session filter
        confidence, session_info = self._apply_session_filter(sweep_bar.name, confidence)
        
        # Calculate risk-reward ratio
        risk_amount = stop_loss - entry_price
        first_tp = take_profit_levels[0] if take_profit_levels else entry_price * 0.97
        reward_amount = entry_price - first_tp
        risk_reward_ratio = reward_amount / risk_amount if risk_amount > 0 else 0
        
        # Create signal
        signal = StrategySignal(
            ticker=ticker,
            strategy_name=self.name,
            direction=SignalDirection.SHORT,
            strength=SignalStrength.STRONG if confidence > 0.7 else SignalStrength.MODERATE,
            confidence=confidence,
            entry=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit_levels,
            timeframe=timeframe_str,
            timestamp=datetime.now(),
            risk_reward=risk_reward_ratio,
            reason=f"Bearish liquidity sweep at resistance ${level_price:.2f}, penetration {penetration_pct*100:.1f}%{session_info}",
            analysis={
                'level_price': level_price,
                'level_strength': resistance_level['strength'],
                'sweep_bar_high': sweep_bar['high'],
                'penetration_pct': penetration_pct,
                'rejection_quality': rejection_quality_score,
                'timeframe': timeframe_str
            }
        )
        
        return signal

    def _calculate_signal_confidence(self, level: Dict[str, Any], penetration_pct: float, 
                                     rejection_quality_score: float, sweep_bar: pd.Series, 
                                     confirmation_bars_df: pd.DataFrame, timeframe_str: str,
                                     ticker: str = None, options_data: Dict[str, Any] = None) -> float:
        """Calculate confidence score for the signal with gamma squeeze analysis"""
        
        cfg_weights = self.config['confidence_weights']
        
        # Level strength component (0-1)
        level_strength_score = level['strength']
        
        # Sweep depth quality (how well penetration fits the ideal range)
        min_pen = self.config['sweep_detection']['min_penetration_pct']
        max_pen = self.config['sweep_detection']['max_penetration_pct']
        ideal_pen = (min_pen + max_pen) / 2
        
        if min_pen <= penetration_pct <= max_pen:
            # Score based on how close to ideal penetration
            depth_quality = 1.0 - abs(penetration_pct - ideal_pen) / (max_pen - min_pen)
        else:
            depth_quality = 0.0  # Outside acceptable range
        
        # Rejection candle quality (already calculated)
        rejection_score = min(1.0, rejection_quality_score)
        
        # Volume confirmation strength - proper statistical analysis
        if 'price_data' in data and '1h' in data['price_data']:
            hourly_data = data['price_data']['1h']
            if len(hourly_data) >= 20:
                # Calculate volume moving average and standard deviation
                volume_series = hourly_data['volume'].tail(20)
                volume_mean = volume_series.mean()
                volume_std = volume_series.std()
                
                # Calculate z-score for current volume
                current_volume = sweep_bar['volume']
                if volume_std > 0:
                    volume_zscore = (current_volume - volume_mean) / volume_std
                    # Convert z-score to probability using normal CDF
                    volume_score = min(1.0, max(0.0, stats.norm.cdf(volume_zscore)))
                else:
                    volume_score = 0.5
            else:
                volume_score = 0.5
        else:
            volume_score = 0.5
        
        # Reversal confirmation speed - statistical analysis of price velocity
        if len(confirmation_bars_df) > 0:
            # Calculate price velocity (rate of reversal)
            price_changes = confirmation_bars_df['close'].diff().dropna()
            if len(price_changes) > 1:
                # Calculate momentum using linear regression slope
                x_values = np.arange(len(price_changes))
                if len(x_values) > 1:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x_values, price_changes.values)
                    # Convert slope to reversal speed score (higher slope = faster reversal)
                    reversal_speed = min(1.0, max(0.0, abs(slope) / (abs(slope) + 0.001)))
                else:
                    reversal_speed = 0.5
            else:
                reversal_speed = 0.5
        else:
            reversal_speed = 0.1
        
        # Base weighted confidence calculation
        base_confidence = (
            level_strength_score * cfg_weights['level_strength'] +
            depth_quality * cfg_weights['sweep_depth_quality'] +
            rejection_score * cfg_weights['rejection_candle_quality'] +
            volume_score * cfg_weights['volume_confirmation_strength'] +
            reversal_speed * cfg_weights['reversal_confirmation_speed']
        )
        
        # Apply gamma squeeze analysis if available
        gamma_boost = 0.0
        if self.gamma_detector and options_data and self.config['gamma_squeeze_analysis']['enabled']:
            try:
                gamma_analysis = self._analyze_gamma_squeeze_confluence(
                    level['price'], options_data, ticker
                )
                if gamma_analysis['has_confluence']:
                    gamma_boost = gamma_analysis['confidence_boost']
                    logger.debug(f"Gamma confluence detected for {ticker}: boost = {gamma_boost:.3f}")
            except Exception as e:
                logger.warning(f"Error in gamma analysis for {ticker}: {e}")
        
        # Apply gamma boost
        final_confidence = base_confidence + gamma_boost
        
        # Apply squeeze confluence multiplier if significant gamma boost
        if gamma_boost > 0.1:  # Significant gamma confluence
            multiplier = self.config['gamma_squeeze_analysis']['squeeze_confluence_multiplier']
            final_confidence *= multiplier
        
        return min(1.0, max(0.0, final_confidence))

    def _detect_market_regime(self, price_data: pd.DataFrame) -> str:
        """Detect current market regime for adaptive parameter adjustment."""
        try:
            if len(price_data) < 20:
                return 'normal'

            # Calculate recent volatility
            returns = price_data['close'].pct_change().dropna()
            recent_vol = returns.tail(self.config['market_regime']['volatility_lookback']).std()

            # Calculate trend strength
            price_change = (price_data['close'].iloc[-1] - price_data['close'].iloc[-self.config['market_regime']['trend_lookback']]) / price_data['close'].iloc[-self.config['market_regime']['trend_lookback']]

            # Determine regime
            if recent_vol > self.config['market_regime']['high_vol_threshold']:
                return 'high_volatility'
            elif recent_vol < self.config['market_regime']['low_vol_threshold']:
                return 'low_volatility'
            elif abs(price_change) > self.config['market_regime']['trending_threshold']:
                return 'trending'
            elif abs(price_change) < self.config['market_regime']['ranging_threshold']:
                return 'ranging'
            else:
                return 'normal'

        except Exception as e:
            logger.warning(f"Error detecting market regime: {e}")
            return 'normal'

    def _get_adaptive_volume_threshold(self, price_data: pd.DataFrame, base_threshold: float) -> float:
        """Get adaptive volume threshold based on recent volume patterns."""
        try:
            if not self.config['volume_confirmation'].get('adaptive_volume_threshold', False):
                return base_threshold

            if len(price_data) < 15:
                return base_threshold

            # Calculate volume percentile
            volume_percentile = self.config['volume_confirmation'].get('volume_percentile_threshold', 60)
            recent_volumes = price_data['volume'].tail(15)
            threshold_volume = recent_volumes.quantile(volume_percentile / 100.0)
            avg_volume = recent_volumes.mean()

            if avg_volume > 0:
                adaptive_multiplier = threshold_volume / avg_volume
                return max(0.8, min(2.0, adaptive_multiplier))  # Bound between 0.8x and 2.0x

            return base_threshold

        except Exception as e:
            logger.warning(f"Error calculating adaptive volume threshold: {e}")
            return base_threshold

    def _get_adaptive_level_strength_threshold(self, market_regime: str) -> float:
        """Get adaptive level strength threshold based on market conditions."""
        try:
            if not self.config['level_identification'].get('adaptive_strength_threshold', False):
                return self.config['level_identification']['min_level_strength_final']

            base_threshold = self.config['level_identification']['min_level_strength_final']

            # Adjust based on market regime
            if market_regime == 'high_volatility':
                return base_threshold * 0.9  # Slightly lower threshold in volatile markets
            elif market_regime == 'low_volatility':
                return base_threshold * 1.1  # Higher threshold in calm markets
            elif market_regime == 'trending':
                return base_threshold * 0.95  # Slightly lower in trending markets
            elif market_regime == 'ranging':
                return base_threshold * 1.05  # Slightly higher in ranging markets
            else:
                return base_threshold

        except Exception as e:
            logger.warning(f"Error calculating adaptive level strength threshold: {e}")
            return self.config['level_identification']['min_level_strength_final']

    def _apply_quality_filters(self, signal_data: Dict[str, Any], price_data: pd.DataFrame) -> bool:
        """Apply enhanced quality filters to improve signal accuracy."""
        try:
            if not self.config.get('quality_filters', {}).get('enabled', False):
                return True

            quality_config = self.config['quality_filters']

            # Check level age requirement
            if quality_config.get('min_level_age_bars', 0) > 0:
                # This would require tracking when levels were first identified
                # For now, we'll assume levels meet age requirements
                pass

            # Check volume spike requirement
            min_volume_spike = quality_config.get('min_volume_spike_ratio', 1.0)
            if 'volume_ratio' in signal_data:
                if signal_data['volume_ratio'] < min_volume_spike:
                    return False

            # Check wick rejection requirement
            min_wick_ratio = quality_config.get('min_wick_rejection_ratio', 1.0)
            if 'rejection_quality' in signal_data:
                if signal_data['rejection_quality'] < min_wick_ratio:
                    return False

            # Check confluence requirement
            if quality_config.get('confluence_required', False):
                # This would require checking multiple timeframe alignment
                # For now, we'll assume confluence is met if signal exists
                pass

            return True

        except Exception as e:
            logger.warning(f"Error applying quality filters: {e}")
            return True

    def _calculate_trend_alignment_bonus(self, price_data: pd.DataFrame, signal_direction: str) -> float:
        """Calculate bonus for trend-aligned signals."""
        try:
            if len(price_data) < 20:
                return 0.0

            # Calculate trend using moving averages
            short_ma = price_data['close'].rolling(10).mean().iloc[-1]
            long_ma = price_data['close'].rolling(20).mean().iloc[-1]
            current_price = price_data['close'].iloc[-1]

            # Determine trend
            if short_ma > long_ma and current_price > short_ma:
                trend = 'bullish'
            elif short_ma < long_ma and current_price < short_ma:
                trend = 'bearish'
            else:
                trend = 'neutral'

            # Apply bonus for aligned signals
            bonus = self.config.get('quality_filters', {}).get('trend_alignment_bonus', 0.0)

            if (signal_direction == 'LONG' and trend == 'bullish') or \
               (signal_direction == 'SHORT' and trend == 'bearish'):
                return bonus

            return 0.0

        except Exception as e:
            logger.warning(f"Error calculating trend alignment bonus: {e}")
            return 0.0

    def _check_signal_separation(self, ticker: str, timestamp: pd.Timestamp) -> bool:
        """Check if enough time has passed since last signal."""
        try:
            min_separation = self.config.get('quality_assurance', {}).get('min_signal_separation_hours', 0)
            if min_separation <= 0:
                return True

            # This would require tracking previous signals
            # For now, we'll assume separation is adequate
            return True

        except Exception as e:
            logger.warning(f"Error checking signal separation: {e}")
            return True

    def _apply_confidence_scaling(self, base_confidence: float, market_regime: str) -> float:
        """Scale confidence based on market conditions."""
        try:
            if not self.config.get('adaptive_parameters', {}).get('confidence_scaling', False):
                return base_confidence

            # Scale confidence based on market regime
            if market_regime == 'high_volatility':
                return base_confidence * 0.9  # Reduce confidence in volatile markets
            elif market_regime == 'low_volatility':
                return base_confidence * 1.1  # Increase confidence in calm markets
            elif market_regime == 'ranging':
                return base_confidence * 1.05  # Slight increase in ranging markets
            else:
                return base_confidence

        except Exception as e:
            logger.warning(f"Error scaling confidence: {e}")
            return base_confidence

    # --- MISSING HELPER METHODS IMPLEMENTATION ---

    def _is_valid_trading_time(self, timestamp: pd.Timestamp) -> bool:
        """Check if timestamp is during valid trading hours (basic implementation)."""
        try:
            # Convert to UTC if timezone aware
            if timestamp.tz is not None:
                timestamp = timestamp.tz_convert('UTC')
            
            # Basic market hours check (US market: 14:30-21:00 UTC on weekdays)
            # Skip weekends
            if timestamp.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return False
            
            hour = timestamp.hour
            # Allow signals during extended hours (13:00-22:00 UTC covers pre-market and after-hours)
            if 13 <= hour <= 22:
                return True
                
            return False
        except Exception as e:
            logger.warning(f"Error in _is_valid_trading_time: {e}")
            return True  # Default to allowing if error

    def _apply_session_filter(self, timestamp: pd.Timestamp, base_confidence: float) -> Tuple[float, str]:
        """Apply session-based confidence multipliers."""
        if not self.config['session_filtering']['enabled']:
            return base_confidence, ""
        
        try:
            # Convert to UTC if timezone aware
            if timestamp.tz is not None:
                timestamp = timestamp.tz_convert('UTC')
            
            hour = timestamp.hour
            session_multiplier = 1.0
            session_name = ""
            
            # Define session multipliers
            if 2 <= hour < 7:  # Asian quiet period
                session_multiplier = self.config['session_filtering']['asian_quiet_multiplier']
                session_name = " (Asian session)"
            elif 7 <= hour < 11:  # London active
                session_multiplier = self.config['session_filtering']['london_active_multiplier']
                session_name = " (London session)"
            elif 13 <= hour < 16:  # US-London overlap
                session_multiplier = self.config['session_filtering']['overlap_multiplier']
                session_name = " (US-London overlap)"
            elif hour in [21, 22, 6]:  # Transition periods
                session_multiplier = self.config['session_filtering']['transition_multiplier']
                session_name = " (Transition period)"
            
            adjusted_confidence = min(1.0, base_confidence * session_multiplier)
            return adjusted_confidence, session_name
            
        except Exception as e:
            logger.warning(f"Error in _apply_session_filter: {e}")
            return base_confidence, ""

    def _get_volume_context(self, price_data: pd.DataFrame, level_price: float) -> Dict[str, Any]:
        """Get volume context around a price level."""
        try:
            # Find bars that touched the level (within 0.5% tolerance)
            tolerance = level_price * 0.005
            level_bars = price_data[
                (price_data['low'] <= level_price + tolerance) & 
                (price_data['high'] >= level_price - tolerance)
            ]
            
            if len(level_bars) == 0:
                return {'volume_score': 1.0, 'avg_volume_at_level': 0}
            
            # Calculate average volume at this level
            avg_volume_at_level = level_bars['volume'].mean()
            overall_avg_volume = price_data['volume'].mean()
            
            # Volume score: higher volume at level = stronger level
            if overall_avg_volume > 0:
                volume_ratio = avg_volume_at_level / overall_avg_volume
                volume_score = min(1.2, max(0.8, volume_ratio))  # Cap between 0.8 and 1.2
            else:
                volume_score = 1.0
            
            return {
                'volume_score': volume_score,
                'avg_volume_at_level': avg_volume_at_level,
                'volume_ratio': volume_ratio if overall_avg_volume > 0 else 1.0
            }
            
        except Exception as e:
            logger.warning(f"Error in _get_volume_context: {e}")
            return {'volume_score': 1.0, 'avg_volume_at_level': 0}

    def _get_current_price(self, data: Dict[str, Any]) -> float:
        """
        Extract current price from data structure.

        CRITICAL NOTE: This method expects the FULL data structure passed to analyze(),
        NOT just the price_data portion. The data structure should be:
        {
            'current_price': float,  # Direct price value (preferred)
            'price_data': {          # Timeframe data (fallback)
                '1h': DataFrame,
                '4h': DataFrame,
                '1d': DataFrame
            }
        }

        COMMON MISTAKE: Calling this with data['price_data'] instead of data
        will cause it to return 0.0 because it won't find the 'current_price' field.

        Args:
            data: Full data structure from analyze() method

        Returns:
            float: Current price, or 0.0 if not found
        """
        # First check if current_price is directly provided
        if isinstance(data, dict) and 'current_price' in data:
            current_price = data['current_price']
            if current_price is not None and current_price > 0:
                return float(current_price)

        # Fallback: extract from price_data if available
        if isinstance(data, dict) and 'price_data' in data and data['price_data']:
            # Get from the latest bar of any timeframe
            for tf_data in data['price_data'].values():
                if isinstance(tf_data, pd.DataFrame) and not tf_data.empty:
                    return float(tf_data['close'].iloc[-1])

        # Direct DataFrame case
        elif isinstance(data, pd.DataFrame) and not data.empty:
            return float(data['close'].iloc[-1])

        return 0.0

    def get_historical_signals(self, ticker: str, start_date: str, end_date: str,
                              timeframes: List[str] = None) -> List[Dict[str, Any]]:
        """
        Get historical signals for backtesting.

        Args:
            ticker: Stock ticker symbol
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            timeframes: List of timeframes to analyze

        Returns:
            List of historical signals with timestamps
        """
        if timeframes is None:
            timeframes = self.config['timeframes_to_analyze']

        historical_signals = []

        try:
            # Get price data for all timeframes
            price_data_dict = {}
            for tf in timeframes:
                poly_timespan, poly_multiplier = self._map_timeframe_to_polygon(tf)
                if poly_timespan is None:
                    continue

                df = self.api_gateway.get_price_data(
                    ticker=ticker,
                    timespan=poly_timespan,
                    multiplier=poly_multiplier,
                    from_date=start_date,
                    to_date=end_date
                )

                if df is not None and not df.empty:
                    # Set timestamp as index for strategy compatibility
                    if 'timestamp' in df.columns:
                        df = df.set_index('timestamp')
                    price_data_dict[tf] = df

            if not price_data_dict:
                logger.warning(f"No price data available for {ticker}")
                return historical_signals

            # Use the highest timeframe as the main timeframe for iteration
            main_tf = max(timeframes, key=lambda x: self._get_timeframe_minutes(x))
            main_df = price_data_dict.get(main_tf)

            if main_df is None or main_df.empty:
                return historical_signals

            # Iterate through each bar and generate signals
            lookback_bars = max(100, self.config['level_identification']['lookback_bars'])

            for i in range(lookback_bars, len(main_df)):
                current_timestamp = main_df.index[i]
                current_price = main_df['close'].iloc[i]

                # Create data dict for this point in time
                data_for_analysis = {
                    'current_price': current_price,
                    'price_data': {}
                }

                # Get historical data up to this point for each timeframe
                for tf, df in price_data_dict.items():
                    # Find the corresponding index in this timeframe
                    tf_data = df[df.index <= current_timestamp]
                    if len(tf_data) >= lookback_bars:
                        data_for_analysis['price_data'][tf] = tf_data

                # Generate signals for this timestamp
                signals = self.analyze(ticker, data_for_analysis, {})

                # Add timestamp and convert to dict format
                for signal in signals:
                    signal_dict = {
                        'timestamp': current_timestamp,
                        'ticker': signal.ticker,
                        'direction': signal.direction.value,
                        'strength': signal.strength.value,
                        'confidence': signal.confidence,
                        'entry': signal.entry,
                        'stop_loss': signal.stop_loss,
                        'take_profit': signal.take_profit,
                        'timeframe': signal.timeframe,
                        'risk_reward': signal.risk_reward,
                        'reason': signal.reason,
                        'analysis': signal.analysis
                    }
                    historical_signals.append(signal_dict)

                    # Log the signal
                    logger.info(f"Historical signal for {ticker} at {current_timestamp}: "
                              f"{signal.direction.value} {signal.strength.value} "
                              f"(confidence: {signal.confidence:.2f})")

        except Exception as e:
            logger.error(f"Error generating historical signals for {ticker}: {e}")

        return historical_signals

    def _get_timeframe_minutes(self, timeframe: str) -> int:
        """Convert timeframe string to minutes for comparison."""
        if 'm' in timeframe:
            return int(timeframe.replace('m', ''))
        elif 'h' in timeframe:
            return int(timeframe.replace('h', '')) * 60
        elif 'd' in timeframe:
            return int(timeframe.replace('d', '')) * 1440
        elif 'w' in timeframe:
            return int(timeframe.replace('w', '')) * 10080
        return 60  # Default to 1 hour

    def _apply_trap_pattern_filtering(self, ticker: str, signals: List[StrategySignal], data: Dict[str, Any]) -> List[StrategySignal]:
        """
        Apply Enhanced Trap Pattern Detection filtering to improve signal quality.

        This method analyzes price data for institutional trap patterns and either:
        1. Boosts confidence for signals with institutional backing
        2. Filters out signals that lack institutional support (if required)
        3. Adds pattern-specific bonuses based on trap type

        Args:
            ticker: Stock ticker symbol
            signals: List of liquidity sweep signals to filter
            data: Price data dictionary

        Returns:
            List of filtered/enhanced signals
        """
        if not self.trap_detector or not signals:
            return signals

        try:
            trap_config = self.config.get('trap_pattern_integration', {})
            if not trap_config.get('enabled', True):
                return signals

            enhanced_signals = []

            # Analyze each signal's timeframe for trap patterns
            for signal in signals:
                try:
                    # Get the timeframe data for this signal
                    timeframe = signal.timeframe
                    price_data_tf = data.get('price_data', {}).get(timeframe)

                    if price_data_tf is None or price_data_tf.empty:
                        logger.debug(f"[{ticker}] No price data for timeframe {timeframe}, keeping signal unchanged")
                        enhanced_signals.append(signal)
                        continue

                    # Set data for trap detector and analyze
                    self.trap_detector.data = price_data_tf.copy()
                    trap_analysis = self.trap_detector.analyze()

                    # Extract institutional activity information
                    institutional_activity = trap_analysis.get('institutional_activity', {})
                    institutional_detected = institutional_activity.get('detected', False)
                    institutional_probability = institutional_activity.get('probability', 0.0)

                    # Check if signal meets institutional threshold
                    min_institutional_prob = trap_config.get('min_institutional_probability', 0.3)
                    require_institutional = trap_config.get('require_institutional_confluence', False)

                    if require_institutional and (not institutional_detected or institutional_probability < min_institutional_prob):
                        logger.debug(f"[{ticker}] Signal filtered out - insufficient institutional activity ({institutional_probability:.2f})")
                        continue

                    # Calculate confidence boost based on trap patterns
                    confidence_boost = 0.0
                    pattern_bonuses = []

                    # Institutional confidence boost
                    if institutional_detected and institutional_probability >= min_institutional_prob:
                        institutional_boost = trap_config.get('institutional_confidence_boost', 0.15)
                        confidence_boost += institutional_boost * institutional_probability
                        pattern_bonuses.append(f"institutional({institutional_probability:.2f})")

                    # Enhanced pattern bonuses
                    enhanced_traps = trap_analysis.get('enhanced_traps', [])
                    for trap in enhanced_traps:
                        trap_type = trap.get('type', '')
                        trap_confidence = trap.get('confidence', 0.0)

                        # Multi-level sweep bonus
                        if 'multi_level_sweep' in trap_type:
                            bonus = trap_config.get('multi_level_sweep_bonus', 0.1) * trap_confidence
                            confidence_boost += bonus
                            pattern_bonuses.append(f"multi_sweep({trap_confidence:.2f})")

                        # Absorption pattern bonus
                        elif 'absorption' in trap_type:
                            bonus = trap_config.get('absorption_pattern_bonus', 0.08) * trap_confidence
                            confidence_boost += bonus
                            pattern_bonuses.append(f"absorption({trap_confidence:.2f})")

                        # Liquidity imbalance bonus
                        elif 'liquidity_imbalance' in trap_type:
                            imbalance = trap.get('liquidity_imbalance', 0.0)
                            if abs(imbalance) >= trap_config.get('liquidity_imbalance_threshold', 0.2):
                                bonus = 0.05 * abs(imbalance)
                                confidence_boost += bonus
                                pattern_bonuses.append(f"imbalance({imbalance:.2f})")

                    # Apply confidence boost (capped at reasonable levels)
                    max_boost = 0.25  # Maximum 25% confidence boost
                    confidence_boost = min(confidence_boost, max_boost)

                    # Create enhanced signal with trap pattern data in analysis field
                    enhanced_analysis = {
                        **signal.analysis,
                        'trap_pattern_analysis': {
                            'institutional_detected': institutional_detected,
                            'institutional_probability': institutional_probability,
                            'confidence_boost': confidence_boost,
                            'pattern_bonuses': pattern_bonuses,
                            'enhanced_traps_count': len(enhanced_traps),
                            'trap_analysis_timestamp': datetime.now().isoformat()
                        }
                    }

                    enhanced_signal = StrategySignal(
                        ticker=signal.ticker,
                        strategy_name=signal.strategy_name,
                        direction=signal.direction,
                        strength=signal.strength,
                        confidence=min(0.95, signal.confidence + confidence_boost),  # Cap at 95%
                        entry=signal.entry,
                        stop_loss=signal.stop_loss,
                        take_profit=signal.take_profit,
                        reason=signal.reason,
                        analysis=enhanced_analysis,
                        risk_reward=signal.risk_reward,
                        timestamp=signal.timestamp,
                        timeframe=signal.timeframe
                    )

                    enhanced_signals.append(enhanced_signal)

                    if pattern_bonuses:
                        logger.info(f"[{ticker}] Signal enhanced with trap patterns: {', '.join(pattern_bonuses)} (+{confidence_boost:.3f} confidence)")

                except Exception as e_signal:
                    logger.warning(f"[{ticker}] Error analyzing trap patterns for signal: {e_signal}")
                    enhanced_signals.append(signal)  # Keep original signal if analysis fails

            logger.info(f"[{ticker}] Trap pattern filtering: {len(signals)} -> {len(enhanced_signals)} signals")
            return enhanced_signals

        except Exception as e:
            logger.error(f"[{ticker}] Error in trap pattern filtering: {e}")
            return signals  # Return original signals if filtering fails


# --- main() function for standalone testing ---
def main():
    """Standalone test function for the Liquidity Sweep Strategy."""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s')

    try:
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        api_path = os.path.join(current_dir, '..', 'api_robustness')
        if api_path not in sys.path:
            sys.path.insert(0, api_path)
        from api_robustness.unified_api_gateway import UnifiedAPIGateway
        gateway = UnifiedAPIGateway()
    except Exception as e:
        logger.critical(f"Standalone LSS test cannot run: Unified API Gateway not available. {e}")
        return

    lss_strategy = LiquiditySweepStrategy(api_gateway_instance=gateway)
    logger.info(f"Standalone LSS Test: Strategy '{lss_strategy.name}' initialized. Min Confidence: {lss_strategy.config['min_confidence']}")

    test_tickers = ['AAPL', 'MSFT', 'NVDA', 'SPY']

    for ticker_symbol in test_tickers:
        logger.info(f"\n{'='*25} TESTING LSS FOR {ticker_symbol} {'='*25}")
        try:
            current_spot = gateway.get_spot_price(ticker_symbol)
            if current_spot is None or pd.isna(current_spot) or current_spot <= 0:
                logger.warning(f"Could not get current price for {ticker_symbol}. Skipping.")
                continue

            data_for_lss = {'current_price': current_spot, 'price_data': {}}

            generated_signals = lss_strategy.analyze(ticker_symbol, data_for_lss, {})

            if generated_signals:
                logger.info(f"Found {len(generated_signals)} LSS signals for {ticker_symbol}:")
                for idx, sig in enumerate(generated_signals):
                    logger.info(f"  Signal {idx+1}: TF:{sig.timeframe} {sig.direction.value} {sig.strength.value} (Conf: {sig.confidence:.2f}) @ Entry: ${sig.entry:.2f}")
                    logger.info(f"     SL: ${sig.stop_loss:.2f}, TP: {[f'${tp:.2f}' for tp in sig.take_profit]}")
                    logger.info(f"     Reason: {sig.reason}")
                    if sig.analysis:
                         level_price = sig.analysis.get('level_price', 0)
                         sweep_extreme = sig.analysis.get('sweep_bar_low') if sig.direction.value == 'LONG' else sig.analysis.get('sweep_bar_high')
                         logger.info(f"     Level: ${level_price:.2f}, Sweep Extreme: ${sweep_extreme:.2f}")
            else:
                logger.info(f"No LSS signals generated for {ticker_symbol} with current data/config.")

        except Exception as e:
            logger.error(f"Error testing {ticker_symbol}: {e}", exc_info=True)

    logger.info(f"\n{'-'*20} LSS Standalone Test Complete {'-'*20}\n")


if __name__ == "__main__":
    main()



    def _analyze_gamma_squeeze_confluence(self, level_price: float, options_data: Dict[str, Any], 
                                        ticker: str) -> Dict[str, Any]:
        """
        Analyze gamma squeeze confluence with liquidity sweep level.
        
        Returns:
            Dict with 'has_confluence' bool and 'confidence_boost' float
        """
        result = {
            'has_confluence': False,
            'confidence_boost': 0.0,
            'gamma_wall_proximity': 0.0,
            'dealer_imbalance': 0.0,
            'charm_acceleration': 0.0
        }
        
        try:
            if not options_data or 'options_chain' not in options_data:
                return result
            
            # Convert options data to DataFrame for gamma detector
            options_df = self._prepare_options_data_for_gamma_analysis(options_data, ticker)
            if options_df is None or options_df.empty:
                return result
            
            # Run gamma squeeze analysis
            gamma_analysis = self.gamma_detector.analyze(options_df)
            if not gamma_analysis:
                return result
            
            cfg_gamma = self.config['gamma_squeeze_analysis']
            total_boost = 0.0
            
            # 1. Check proximity to gamma walls
            gamma_walls = gamma_analysis.get('gamma_walls', [])
            for wall in gamma_walls:
                wall_price = wall.get('strike', 0)
                if wall_price > 0:
                    distance_pct = abs(level_price - wall_price) / level_price
                    if distance_pct <= cfg_gamma['near_gamma_wall_range']:
                        proximity_boost = cfg_gamma['gamma_wall_proximity_bonus'] * (1 - distance_pct / cfg_gamma['near_gamma_wall_range'])
                        total_boost += proximity_boost
                        result['gamma_wall_proximity'] = proximity_boost
                        logger.debug(f"Gamma wall proximity boost for {ticker}: {proximity_boost:.3f}")
                        break
            
            # 2. Check dealer positioning imbalance
            dealer_positioning = gamma_analysis.get('dealer_positioning', {})
            imbalance = abs(dealer_positioning.get('net_gamma_exposure', 0))
            if imbalance >= cfg_gamma['dealer_imbalance_threshold']:
                imbalance_boost = cfg_gamma['gamma_wall_proximity_bonus'] * 0.5  # Half of wall bonus
                total_boost += imbalance_boost
                result['dealer_imbalance'] = imbalance_boost
                logger.debug(f"Dealer imbalance boost for {ticker}: {imbalance_boost:.3f}")
            
            # 3. Check charm acceleration zones
            charm_zones = gamma_analysis.get('charm_acceleration_zones', [])
            for zone in charm_zones:
                zone_center = zone.get('price_center', 0)
                zone_range = zone.get('price_range', 0)
                if zone_center > 0 and zone_range > 0:
                    if abs(level_price - zone_center) <= zone_range:
                        charm_boost = cfg_gamma['charm_acceleration_bonus']
                        total_boost += charm_boost
                        result['charm_acceleration'] = charm_boost
                        logger.debug(f"Charm acceleration boost for {ticker}: {charm_boost:.3f}")
                        break
            
            # 4. Check gamma concentration
            gamma_concentration = gamma_analysis.get('gamma_concentration', {})
            concentration_ratio = gamma_concentration.get('concentration_ratio', 0)
            if concentration_ratio >= cfg_gamma['gamma_concentration_threshold']:
                concentration_boost = 0.1  # Fixed boost for high concentration
                total_boost += concentration_boost
                logger.debug(f"Gamma concentration boost for {ticker}: {concentration_boost:.3f}")
            
            # Set results
            result['confidence_boost'] = min(0.3, total_boost)  # Cap total boost at 30%
            result['has_confluence'] = total_boost > 0.05  # Minimum 5% boost for confluence
            
            return result
            
        except Exception as e:
            logger.error(f"Error in gamma squeeze confluence analysis for {ticker}: {e}")
            return result

    def _prepare_options_data_for_gamma_analysis(self, options_data: Dict[str, Any], ticker: str) -> Optional[pd.DataFrame]:
        """
        Prepare options data for gamma squeeze detector analysis.
        
        Expected options_data format:
        {
            'options_chain': {
                'calls': [{'strike': float, 'gamma': float, 'open_interest': int, ...}, ...],
                'puts': [{'strike': float, 'gamma': float, 'open_interest': int, ...}, ...]
            }
        }
        """
        try:
            options_chain = options_data.get('options_chain', {})
            calls = options_chain.get('calls', [])
            puts = options_chain.get('puts', [])
            
            if not calls and not puts:
                logger.debug(f"No options data available for gamma analysis: {ticker}")
                return None
            
            # Convert to DataFrame format expected by gamma detector
            rows = []
            
            # Process calls
            for call in calls:
                if 'strike' in call and 'gamma' in call:
                    rows.append({
                        'strike': call['strike'],
                        'call_gamma': call.get('gamma', 0),
                        'put_gamma': 0,  # This row represents call data
                        'call_open_interest': call.get('open_interest', 0),
                        'put_open_interest': 0,
                        'call_volume': call.get('volume', 0),
                        'put_volume': 0
                    })
            
            # Process puts
            for put in puts:
                if 'strike' in put and 'gamma' in put:
                    # Find if we already have this strike from calls
                    existing_row = None
                    for row in rows:
                        if row['strike'] == put['strike']:
                            existing_row = row
                            break
                    
                    if existing_row:
                        # Update existing row with put data
                        existing_row['put_gamma'] = put.get('gamma', 0)
                        existing_row['put_open_interest'] = put.get('open_interest', 0)
                        existing_row['put_volume'] = put.get('volume', 0)
                    else:
                        # Create new row for put
                        rows.append({
                            'strike': put['strike'],
                            'call_gamma': 0,
                            'put_gamma': put.get('gamma', 0),
                            'call_open_interest': 0,
                            'put_open_interest': put.get('open_interest', 0),
                            'call_volume': 0,
                            'put_volume': put.get('volume', 0)
                        })
            
            if not rows:
                logger.debug(f"No valid options data for gamma analysis: {ticker}")
                return None
            
            df = pd.DataFrame(rows)
            df = df.sort_values('strike').reset_index(drop=True)
            
            logger.debug(f"Prepared {len(df)} option strikes for gamma analysis: {ticker}")
            return df
            
        except Exception as e:
            logger.error(f"Error preparing options data for gamma analysis: {e}")
            return None

    def _extract_options_data_from_analysis_results(self, analysis_results: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Extract options data from analysis results for gamma analysis.
        
        Expected structure in analysis_results:
        {
            'options_flow_analyzer': {
                'options_chain': {...}
            },
            'gamma_squeeze_detector': {
                'options_data': {...}
            },
            # or direct options data
            'options_data': {...},
            'options_chain': {...}
        }
        """
        try:
            # Try different possible locations for options data
            options_sources = [
                analysis_results.get('options_flow_analyzer', {}),
                analysis_results.get('gamma_squeeze_detector', {}),
                analysis_results.get('options_analyzer', {}),
                analysis_results  # Direct in analysis_results
            ]
            
            for source in options_sources:
                if not isinstance(source, dict):
                    continue
                    
                # Check for options_chain directly
                if 'options_chain' in source:
                    return {'options_chain': source['options_chain']}
                
                # Check for options_data
                if 'options_data' in source:
                    options_data = source['options_data']
                    if isinstance(options_data, dict) and 'options_chain' in options_data:
                        return options_data
                    
                # Check for nested structure
                for key, value in source.items():
                    if isinstance(value, dict) and 'options_chain' in value:
                        return value
            
            return None
            
        except Exception as e:
            logger.debug(f"Error extracting options data from analysis results: {e}")
            return None

    def generate_factors(self, ticker: str, data: Dict[str, Any], analysis_results: Dict[str, Any], timeframe: str = "1d") -> List[Dict[str, Any]]:
        """
        Generate FactorData objects from liquidity sweep analysis for confluence engine.
        
        Args:
            ticker: Trading symbol
            data: Market data dictionary with price_data and current_price
            analysis_results: Results from other analyzers (flow physics, etc.)
            timeframe: Analysis timeframe
            
        Returns:
            List of FactorData objects (as dictionaries) for factor-based system
        """
        factors = []
        
        try:
            # Run institutional range analysis to get sweep patterns
            range_results = self._analyze_institutional_ranges(data, analysis_results)
            
            if range_results:
                # Generate factors from institutional campaign analysis
                factors.extend(self._create_institutional_campaign_factors(ticker, range_results, timeframe))
                factors.extend(self._create_range_quality_factors(ticker, range_results, timeframe))
                factors.extend(self._create_absorption_efficiency_factors(ticker, range_results, timeframe))
                factors.extend(self._create_flow_bias_factors(ticker, range_results, timeframe))
            
            logger.info(f"Generated {len(factors)} liquidity sweep factors for {ticker} on {timeframe}")
            return factors
            
        except Exception as e:
            logger.error(f"Error generating liquidity sweep factors for {ticker}: {e}")
            return factors
    
    def _create_institutional_campaign_factors(self, ticker: str, range_results: Dict[str, Any], timeframe: str) -> List[Dict[str, Any]]:
        """Create factors based on institutional campaign detection."""
        factors = []
        
        try:
            campaign_signal = range_results.get('campaign_signal')
            if not campaign_signal:
                return factors
                
            direction = campaign_signal.get('direction', 'neutral')
            strength = campaign_signal.get('strength', 0)
            confidence = campaign_signal.get('confidence', 0)
            
            if strength > 0.3 and direction != 'neutral':  # Significant institutional activity
                factor = {
                    'factor_name': f"LSS_InstitutionalCampaign_{ticker}_{timeframe}",
                    'factor_type': 'institutional_campaign',
                    'strength_score': min(1.0, strength),
                    'direction_bias': 'Bullish' if direction == 'bullish' else 'Bearish',
                    'timeframe': timeframe,
                    'confidence': confidence,
                    'timestamp': datetime.now(),
                    'supporting_data': {
                        'campaign_stage': range_results.get('campaign_stage', 'unknown'),
                        'net_bias': range_results.get('net_bias', 'neutral'),
                        'range_quality': range_results.get('range_quality', 0),
                        'absorption_efficiency': range_results.get('absorption_efficiency', 0)
                    },
                    'metadata': {
                        'analyzer': 'LiquiditySweepStrategy',
                        'pattern_type': 'institutional_campaign',
                        'strategy_version': 'v3_institutional'
                    }
                }
                factors.append(factor)
                
        except Exception as e:
            logger.warning(f"Error creating institutional campaign factors: {e}")
            
        return factors
    
    def _create_range_quality_factors(self, ticker: str, range_results: Dict[str, Any], timeframe: str) -> List[Dict[str, Any]]:
        """Create factors based on range quality analysis."""
        factors = []
        
        try:
            range_quality = range_results.get('range_quality', 0)
            active_ranges = range_results.get('active_ranges', [])
            
            if range_quality > 0.6 and active_ranges:  # High quality ranges
                factor = {
                    'factor_name': f"LSS_RangeQuality_{ticker}_{timeframe}",
                    'factor_type': 'range_quality',
                    'strength_score': range_quality,
                    'direction_bias': 'Neutral',  # Range quality is directionally neutral
                    'timeframe': timeframe,
                    'confidence': 0.8,
                    'timestamp': datetime.now(),
                    'supporting_data': {
                        'num_active_ranges': len(active_ranges),
                        'quality_score': range_quality,
                        'strongest_range': active_ranges[0] if active_ranges else None
                    },
                    'metadata': {
                        'analyzer': 'LiquiditySweepStrategy',
                        'pattern_type': 'range_quality'
                    }
                }
                factors.append(factor)
                
        except Exception as e:
            logger.warning(f"Error creating range quality factors: {e}")
            
        return factors
    
    def _create_absorption_efficiency_factors(self, ticker: str, range_results: Dict[str, Any], timeframe: str) -> List[Dict[str, Any]]:
        """Create factors based on level absorption efficiency."""
        factors = []
        
        try:
            absorption_efficiency = range_results.get('absorption_efficiency', 0)
            absorption_direction = range_results.get('absorption_direction', 'neutral')
            
            if absorption_efficiency > 0.5:  # Significant absorption
                factor = {
                    'factor_name': f"LSS_AbsorptionEfficiency_{ticker}_{timeframe}",
                    'factor_type': 'absorption_efficiency',
                    'strength_score': absorption_efficiency,
                    'direction_bias': 'Bullish' if absorption_direction == 'bullish' else 'Bearish' if absorption_direction == 'bearish' else 'Neutral',
                    'timeframe': timeframe,
                    'confidence': 0.7,
                    'timestamp': datetime.now(),
                    'supporting_data': {
                        'efficiency_score': absorption_efficiency,
                        'absorption_direction': absorption_direction,
                        'absorption_trend': range_results.get('absorption_trend', 'stable')
                    },
                    'metadata': {
                        'analyzer': 'LiquiditySweepStrategy',
                        'pattern_type': 'absorption_efficiency'
                    }
                }
                factors.append(factor)
                
        except Exception as e:
            logger.warning(f"Error creating absorption efficiency factors: {e}")
            
        return factors
    
    def _create_flow_bias_factors(self, ticker: str, range_results: Dict[str, Any], timeframe: str) -> List[Dict[str, Any]]:
        """Create factors based on flow physics bias within ranges."""
        factors = []
        
        try:
            net_bias = range_results.get('net_bias', 'neutral')
            flow_strength = range_results.get('flow_strength', 0)
            
            if flow_strength > 0.4 and net_bias != 'neutral':  # Strong flow bias
                factor = {
                    'factor_name': f"LSS_FlowBias_{ticker}_{timeframe}",
                    'factor_type': 'flow_bias',
                    'strength_score': flow_strength,
                    'direction_bias': 'Bullish' if net_bias == 'accumulation' else 'Bearish' if net_bias == 'distribution' else 'Neutral',
                    'timeframe': timeframe,
                    'confidence': 0.8,  # High confidence in flow physics integration
                    'timestamp': datetime.now(),
                    'supporting_data': {
                        'net_bias': net_bias,
                        'flow_strength': flow_strength,
                        'flow_consistency': range_results.get('flow_consistency', 0)
                    },
                    'metadata': {
                        'analyzer': 'LiquiditySweepStrategy',
                        'pattern_type': 'flow_bias',
                        'integration': 'flow_physics'
                    }
                }
                factors.append(factor)
                
        except Exception as e:
            logger.warning(f"Error creating flow bias factors: {e}")
            
        return factors
