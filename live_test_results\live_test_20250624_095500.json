[{"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:55:02.537682", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:55:02.537426", "success": false, "latency_ms": 2384.1495999949984, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:55:02.539384", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:55:02.539001", "success": false, "latency_ms": 0.022599997464567423, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:55:02.541929", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:55:02.541803", "success": false, "latency_ms": 0.02600002335384488, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:00:02.544422", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:00:02.544251", "success": false, "latency_ms": 0.030800001695752144, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:00:02.545324", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:00:02.545188", "success": false, "latency_ms": 0.011099997209385037, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:00:02.546106", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:00:02.545939", "success": false, "latency_ms": 0.00899998121894896, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:05:02.548145", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:05:02.547973", "success": false, "latency_ms": 0.02190002123825252, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:05:02.549148", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:05:02.549008", "success": false, "latency_ms": 0.012500007869675756, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:05:02.550299", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:05:02.550218", "success": false, "latency_ms": 0.014399993233382702, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_095500", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:05:02.604431", "result": {"timestamp": "2025-06-24T10:05:02.604221", "total_execution_time_ms": 53.01609999150969, "budget_compliance": false, "error": "cannot import name 'get_config' from 'config.settings' (D:\\script-work\\CORE\\config\\settings.py)", "overall_success": false}}]