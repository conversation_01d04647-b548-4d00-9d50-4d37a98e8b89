"""
OrderBookWallAnalyzer - Professional Implementation

Analyzes Level 2 order book data to detect significant liquidity walls,
institutional footprints, and market depth imbalances that can act as
support/resistance levels and provide trading opportunities.

Key Features:
- Real bid/ask wall detection using L2 market depth
- Wall strength analysis based on size and persistence
- Institutional footprint detection from large orders
- Market depth imbalance analysis
- Wall break/hold pattern recognition
- Multi-timeframe wall confluence

Data Sources:
- L2 market depth via get_market_depth()
- Real-time bid/ask spreads via snapshots
- Historical price action for wall validation
- Volume analysis for wall interaction patterns
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict

# Import factor specification
try:
    from factor_specification import FactorData, DirectionBias, TimeFrame
except ImportError:
    try:
        from .factor_specification import FactorData, DirectionBias, TimeFrame
    except ImportError:
        # Fallback classes
        class FactorData:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)
        
        class DirectionBias:
            BULLISH = "BULLISH"
            BEARISH = "BEARISH"
            NEUTRAL = "NEUTRAL"
        
        class TimeFrame:
            MIN_1 = "1m"
            MIN_5 = "5m"
            MIN_15 = "15m"
            HOUR_1 = "1h"
            HOUR_4 = "4h"
            DAY_1 = "1d"

# Import base analyzer
try:
    from base_analyzer import BaseAnalyzer
except ImportError:
    try:
        from base_analyzer import BaseAnalyzer
    except ImportError:
        class BaseAnalyzer:
            def __init__(self, config=None):
                self.config = config or {}
            
            def _get_default_config(self):
                return {}

logger = logging.getLogger(__name__)


class OrderBookWallAnalyzer(BaseAnalyzer):
    """
    Professional Order Book Wall Analyzer
    
    Detects and analyzes liquidity walls in Level 2 market depth data.
    Generates comprehensive factors for institutional trading decisions.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None,
                 system_config: Optional[Dict[str, Any]] = None,
                 api_gateway_instance=None):
        """Initialize the OrderBookWallAnalyzer."""
        super().__init__(config, system_config, api_gateway_instance)
        self.system_config = system_config or {}
        self.api_gateway = api_gateway_instance
        
        # Update config with defaults
        default_config = self._get_default_config()
        if config:
            default_config.update(config)
        self.config = default_config
        
        # Initialize API gateway if needed
        if not self.api_gateway:
            self._initialize_api_gateway()
        
        logger.info("OrderBookWallAnalyzer initialized with professional wall detection")

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for order book wall analysis."""
        return {
            # Wall Detection Thresholds
            'wall_size_threshold': 5000,           # Minimum size to be considered a wall
            'wall_size_multiplier': 2.5,           # Size relative to average depth
            'significant_wall_multiplier': 5.0,    # Major institutional walls
            'wall_proximity_pct': 0.02,            # 2% from current price for relevance
            
            # Market Depth Analysis
            'max_depth_levels': 5,                 # Use all available L2 levels
            'min_level_size': 100,                 # Minimum size per level
            'depth_imbalance_threshold': 0.3,      # 30% imbalance significant
            'wall_concentration_threshold': 0.6,   # 60% of depth in one level
            
            # Wall Strength Calculation
            'price_distance_weight': 0.3,          # Weight for price proximity
            'size_weight': 0.4,                    # Weight for absolute size
            'relative_size_weight': 0.3,           # Weight for relative size
            
            # Institutional Detection
            'institutional_size_threshold': 25000, # Large institutional orders
            'block_trade_threshold': 10000,        # Block trade size
            'unusual_size_multiplier': 10.0,       # Unusual order size detection
            
            # Wall Persistence
            'wall_persistence_minutes': 5,         # Track wall over time
            'wall_refresh_detection': True,        # Detect wall refreshes
            'wall_absorption_tracking': True,      # Track wall consumption
        }

    def _initialize_api_gateway(self):
        """Initialize API gateway for order book data."""
        try:
            # Import and initialize the unified API gateway
            from api_robustness.unified_api_gateway import get_api_gateway
            self.api_gateway = get_api_gateway()
            logger.info("OrderBook API gateway initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize API gateway: {e}")
            self.api_gateway = None

    def analyze(self, ticker: str, mtf_data: Dict[str, pd.DataFrame],
                current_price: float, **kwargs) -> List[FactorData]:
        """
        Required method from BaseAnalyzer - delegates to analyze_factors.
        """
        data_package = {
            'ticker': ticker,
            'current_price': current_price,
            'mtf_market_data': mtf_data
        }
        data_package.update(kwargs)
        return self.analyze_factors(data_package=data_package)

    def analyze_factors(self, data_package=None, **kwargs) -> List[FactorData]:
        """
        ORCHESTRATOR COMPATIBLE: Analyze order book walls and generate factors.
        
        The orchestrator calls this method with a standardized data_package:
        {
            'ticker': str,
            'current_price': float,
            'mtf_market_data': Dict[str, pd.DataFrame],  # String keys like '1m', '5m', '15m'
            'mtf_data_enum': Dict[TimeFrame, pd.DataFrame],  # Optional enum keys
            'options_data': Optional[pd.DataFrame],
            'order_book': Optional[Dict],
            # ... other additional data
        }
        
        Returns:
            List of FactorData objects for confluence engine
        """
        try:
            # Handle multiple interface approaches from orchestrator
            if data_package:
                # Method 1: data_package approach (preferred)
                ticker = data_package.get('ticker', 'UNKNOWN')
                current_price = data_package.get('current_price')
                mtf_data = data_package.get('mtf_market_data', {})
                order_book_data = data_package.get('order_book')
                options_data = data_package.get('options_data')
                
            else:
                # Method 2: kwargs approach (fallback)
                ticker = kwargs.get('ticker', 'UNKNOWN')
                current_price = kwargs.get('current_price')
                mtf_data = kwargs.get('mtf_data', {}) or kwargs.get('mtf_market_data', {})
                order_book_data = kwargs.get('order_book')
                options_data = kwargs.get('options_data')

            if not ticker or ticker == 'UNKNOWN':
                logger.error("No ticker provided for order book analysis")
                return []

            if not current_price or current_price <= 0:
                logger.error(f"[{ticker}] Invalid current price: {current_price}")
                return []

            logger.info(f"[{ticker}] Starting OrderBook wall analysis at ${current_price:.2f}")

            # Initialize factors list
            factors = []

            # Priority 1: Get fresh order book data via API
            if not order_book_data:
                order_book_data = self._fetch_order_book_data(ticker)
            
            if order_book_data and order_book_data.get('bids') and order_book_data.get('asks'):
                # Full L2 analysis with real order book data
                l2_factors = self._analyze_l2_walls(ticker, current_price, order_book_data)
                factors.extend(l2_factors)
                
                imbalance_factors = self._analyze_depth_imbalances(ticker, current_price, order_book_data)
                factors.extend(imbalance_factors)
                
                institutional_factors = self._detect_institutional_walls(ticker, current_price, order_book_data)
                factors.extend(institutional_factors)
                
                logger.info(f"[{ticker}] Generated {len(factors)} L2 factors")
            
            # Priority 2: Synthetic wall analysis from price data if no L2 data
            if not factors and mtf_data:
                logger.error(f"[{ticker}] WARNING: No real L2 order book data available - falling back to synthetic analysis")
                synthetic_factors = self._analyze_synthetic_walls(ticker, current_price, mtf_data)
                factors.extend(synthetic_factors)
                logger.warning(f"[{ticker}] Generated {len(factors)} SYNTHETIC factors (not real L2 data)")

            # Priority 3: Always generate at least basic market structure factor
            if not factors:
                logger.error(f"[{ticker}] CRITICAL: No order book data OR price data available - using basic fallback")
                basic_factors = self._generate_basic_factors(ticker, current_price)
                factors.extend(basic_factors)
                logger.warning(f"[{ticker}] Generated {len(factors)} BASIC fallback factors (no real market data)")

            # Final validation and enhancement
            validated_factors = self._validate_and_enhance_factors(ticker, factors, current_price)

            logger.info(f"[{ticker}] OrderBookWallAnalyzer completed: {len(validated_factors)} factors")
            return validated_factors

        except Exception as e:
            logger.error(f"[{ticker}] Error in OrderBookWallAnalyzer: {e}")
            return self._generate_error_recovery_factors(ticker, current_price)

    def _validate_and_enhance_factors(self, ticker: str, factors: List[FactorData], current_price: float) -> List[FactorData]:
        """Validate and enhance factors for orchestrator compatibility."""
        validated_factors = []
        
        for factor in factors:
            try:
                # Ensure required fields are populated
                if not factor.analyzer_name:
                    factor.analyzer_name = "OrderBookWallAnalyzer"
                
                if not factor.ticker:
                    factor.ticker = ticker
                
                # Validate strength score normalization (0.0 to 1.0)
                if factor.strength_score < 0.0:
                    factor.strength_score = 0.0
                elif factor.strength_score > 1.0:
                    factor.strength_score = 1.0
                
                # Validate data quality score
                if not hasattr(factor, 'data_quality_score') or factor.data_quality_score is None:
                    factor.data_quality_score = 0.8  # Default good quality
                
                # Ensure price levels are reasonable
                if factor.key_level_price and factor.key_level_price <= 0:
                    factor.key_level_price = current_price
                
                # Add timing context to details
                if not factor.details:
                    factor.details = {}
                
                factor.details.update({
                    'analysis_timestamp': datetime.now().isoformat(),
                    'current_market_price': current_price,
                    'price_proximity_pct': abs(factor.key_level_price - current_price) / current_price if factor.key_level_price else 0.0
                })
                
                validated_factors.append(factor)
                
            except Exception as e:
                logger.warning(f"[{ticker}] Error validating factor {factor.factor_name}: {e}")
                continue
        
        return validated_factors

    def get_liquidity_levels(self, ticker: str, current_price: float, order_book_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Extract liquidity levels for enhanced reporting integration.
        
        This method provides level data to the EnhancedFactorReporter for 
        comprehensive level intelligence reporting.
        
        Returns:
            List of liquidity level dictionaries with:
            - price: Level price
            - level_type: 'bid_wall', 'ask_wall', 'institutional_wall'
            - strength: 0.0 to 1.0 strength score
            - size: Order size at this level
            - distance_pct: Distance from current price
        """
        levels = []
        
        try:
            if not order_book_data or not order_book_data.get('bids') or not order_book_data.get('asks'):
                return levels
            
            bids = order_book_data.get('bids', [])
            asks = order_book_data.get('asks', [])
            
            # Extract significant bid levels (support)
            for bid in bids:
                if bid['size'] >= self.config['wall_size_threshold']:
                    distance_pct = abs(bid['price'] - current_price) / current_price
                    if distance_pct <= self.config['wall_proximity_pct']:
                        
                        strength = self._calculate_wall_strength(
                            bid['size'], bid['price'], current_price, 
                            self.config['wall_size_threshold'], 
                            self.config['wall_size_threshold'] * self.config['significant_wall_multiplier']
                        )
                        
                        levels.append({
                            'price': bid['price'],
                            'level_type': 'bid_wall',
                            'strength': strength,
                            'size': bid['size'],
                            'distance_pct': distance_pct,
                            'side': 'support',
                            'level': bid.get('level', 1),
                            'source': 'order_book_l2'
                        })
            
            # Extract significant ask levels (resistance)
            for ask in asks:
                if ask['size'] >= self.config['wall_size_threshold']:
                    distance_pct = abs(ask['price'] - current_price) / current_price
                    if distance_pct <= self.config['wall_proximity_pct']:
                        
                        strength = self._calculate_wall_strength(
                            ask['size'], ask['price'], current_price,
                            self.config['wall_size_threshold'],
                            self.config['wall_size_threshold'] * self.config['significant_wall_multiplier']
                        )
                        
                        levels.append({
                            'price': ask['price'],
                            'level_type': 'ask_wall', 
                            'strength': strength,
                            'size': ask['size'],
                            'distance_pct': distance_pct,
                            'side': 'resistance',
                            'level': ask.get('level', 1),
                            'source': 'order_book_l2'
                        })
            
            # Sort by proximity to current price
            levels.sort(key=lambda x: x['distance_pct'])
            
        except Exception as e:
            logger.error(f"[{ticker}] Error extracting liquidity levels: {e}")
        
        return levels

    def get_wall_summary_stats(self, ticker: str, order_book_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get summary statistics for wall analysis reporting.
        
        Returns:
            Dictionary with wall analysis summary statistics
        """
        stats = {
            'total_bid_size': 0,
            'total_ask_size': 0,
            'num_significant_bid_walls': 0,
            'num_significant_ask_walls': 0,
            'largest_bid_wall': 0,
            'largest_ask_wall': 0,
            'depth_imbalance': 0.0,
            'institutional_walls_detected': 0,
            'analysis_quality': 'no_data'
        }
        
        try:
            if not order_book_data or not order_book_data.get('bids') or not order_book_data.get('asks'):
                return stats
            
            bids = order_book_data.get('bids', [])
            asks = order_book_data.get('asks', [])
            
            # Calculate basic statistics
            bid_sizes = [bid['size'] for bid in bids]
            ask_sizes = [ask['size'] for ask in asks]
            
            stats['total_bid_size'] = sum(bid_sizes)
            stats['total_ask_size'] = sum(ask_sizes)
            stats['largest_bid_wall'] = max(bid_sizes) if bid_sizes else 0
            stats['largest_ask_wall'] = max(ask_sizes) if ask_sizes else 0
            
            # Count significant walls
            wall_threshold = self.config['wall_size_threshold']
            stats['num_significant_bid_walls'] = sum(1 for size in bid_sizes if size >= wall_threshold)
            stats['num_significant_ask_walls'] = sum(1 for size in ask_sizes if size >= wall_threshold)
            
            # Calculate depth imbalance
            total_depth = stats['total_bid_size'] + stats['total_ask_size']
            if total_depth > 0:
                bid_ratio = stats['total_bid_size'] / total_depth
                ask_ratio = stats['total_ask_size'] / total_depth
                stats['depth_imbalance'] = bid_ratio - ask_ratio  # Positive = bid heavy
            
            # Detect institutional walls
            institutional_threshold = self.config['institutional_size_threshold']
            all_sizes = bid_sizes + ask_sizes
            stats['institutional_walls_detected'] = sum(1 for size in all_sizes if size >= institutional_threshold)
            
            # Determine analysis quality
            if len(bids) >= 3 and len(asks) >= 3:
                stats['analysis_quality'] = 'high'
            elif len(bids) >= 1 and len(asks) >= 1:
                stats['analysis_quality'] = 'medium'
            else:
                stats['analysis_quality'] = 'low'
                
        except Exception as e:
            logger.error(f"[{ticker}] Error calculating wall summary stats: {e}")
        
        return stats

    def _fetch_order_book_data(self, ticker: str) -> Optional[Dict[str, Any]]:
        """Fetch real-time Level 2 order book data with synthetic fallback."""
        try:
            if not self.api_gateway:
                logger.warning(f"[{ticker}] No API gateway available for order book data")
                return None

            # Try real L2 order book first
            order_book = self.api_gateway.get_order_book(ticker, depth=5)
            if order_book and ('bids' in order_book and 'asks' in order_book):
                logger.debug(f"[{ticker}] Retrieved REAL order book with {len(order_book['bids'])} bids, {len(order_book['asks'])} asks")
                return order_book

            # Try market depth fallback
            depth_df = self.api_gateway.get_market_depth(ticker)
            if not depth_df.empty:
                logger.debug(f"[{ticker}] Retrieved market depth with {len(depth_df)} levels")
                return self._convert_depth_to_order_book(depth_df)

            # ENHANCED: Create synthetic order book using spread estimation
            logger.info(f"[{ticker}] No real L2 data available - creating synthetic order book")
            synthetic_order_book = self._create_synthetic_order_book(ticker)
            if synthetic_order_book:
                logger.info(f"[{ticker}] Created synthetic order book with estimated spreads")
                return synthetic_order_book

            logger.warning(f"[{ticker}] No order book data available (real or synthetic)")
            return None

        except Exception as e:
            logger.error(f"[{ticker}] Error fetching order book data: {e}")
            return None

    def _create_synthetic_order_book(self, ticker: str) -> Optional[Dict[str, Any]]:
        """Create synthetic order book using spread estimation."""
        try:
            # Import the spread estimator
            from synthetic_spread_estimator import SyntheticSpreadEstimator
            
            # Get current price and 15m data
            current_price = self._get_current_price_from_api(ticker)
            if not current_price:
                return None
                
            price_data_15m = self._get_15m_price_data(ticker)
            if price_data_15m is None or price_data_15m.empty:
                return None
            
            # Create spread estimator and estimate
            estimator = SyntheticSpreadEstimator()
            spread_estimate = estimator.estimate_spread(ticker, current_price, price_data_15m)
            
            # Create synthetic order book
            synthetic_order_book = estimator.create_synthetic_order_book(spread_estimate, depth_levels=5)
            
            # Mark as synthetic for tracking
            synthetic_order_book['is_synthetic'] = True
            synthetic_order_book['spread_confidence'] = spread_estimate.confidence
            
            logger.info(f"[{ticker}] Synthetic order book created: Bid=${spread_estimate.bid:.2f}, Ask=${spread_estimate.ask:.2f}, Spread={spread_estimate.spread_pct:.3f}%")
            
            return synthetic_order_book
            
        except ImportError:
            logger.error(f"[{ticker}] SyntheticSpreadEstimator not available - cannot create synthetic order book")
            return None
        except Exception as e:
            logger.error(f"[{ticker}] Error creating synthetic order book: {e}")
            return None

    def _get_current_price_from_api(self, ticker: str) -> Optional[float]:
        """Get current price from API."""
        try:
            price_data = self.api_gateway.get_price_data(ticker, timespan="day", multiplier=1)
            if not price_data.empty:
                return float(price_data['close'].iloc[-1])
            return None
        except Exception as e:
            logger.error(f"[{ticker}] Error getting current price: {e}")
            return None

    def _get_15m_price_data(self, ticker: str) -> Optional[pd.DataFrame]:
        """Get 15m price data for spread estimation."""
        try:
            price_data = self.api_gateway.get_price_data(ticker, timespan="minute", multiplier=15)
            if not price_data.empty:
                return price_data.tail(100)  # Last 100 bars for analysis
            return None
        except Exception as e:
            logger.error(f"[{ticker}] Error getting 15m price data: {e}")
            return None

    def _convert_depth_to_order_book(self, depth_df: pd.DataFrame) -> Dict[str, Any]:
        """Convert market depth DataFrame to order book format."""
        try:
            bids = []
            asks = []
            
            for _, row in depth_df.iterrows():
                order_data = {
                    'price': float(row['price']),
                    'size': float(row['size']),
                    'level': int(row['level'])
                }
                
                if row['side'] == 'bid':
                    bids.append(order_data)
                elif row['side'] == 'ask':
                    asks.append(order_data)
            
            # Sort bids (highest price first) and asks (lowest price first)
            bids.sort(key=lambda x: x['price'], reverse=True)
            asks.sort(key=lambda x: x['price'])
            
            return {
                'bids': bids,
                'asks': asks,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error converting depth to order book: {e}")
            return {'bids': [], 'asks': [], 'timestamp': datetime.now()}

    def _analyze_l2_walls(self, ticker: str, current_price: float, order_book: Dict[str, Any]) -> List[FactorData]:
        """Analyze Level 2 order book for significant bid/ask walls."""
        factors = []
        
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids and not asks:
                return factors

            # Calculate wall thresholds
            all_sizes = [order['size'] for order in bids + asks]
            if not all_sizes:
                return factors
                
            avg_size = np.mean(all_sizes)
            wall_threshold = max(self.config['wall_size_threshold'], avg_size * self.config['wall_size_multiplier'])
            significant_threshold = avg_size * self.config['significant_wall_multiplier']

            # Analyze bid walls (support)
            bid_walls = self._detect_walls_in_side(bids, 'bid', current_price, wall_threshold, significant_threshold)
            for wall in bid_walls:
                factor = self._create_wall_factor(ticker, wall, DirectionBias.BULLISH, 'L2BidWall')
                if factor:
                    factors.append(factor)

            # Analyze ask walls (resistance)
            ask_walls = self._detect_walls_in_side(asks, 'ask', current_price, wall_threshold, significant_threshold)
            for wall in ask_walls:
                factor = self._create_wall_factor(ticker, wall, DirectionBias.BEARISH, 'L2AskWall')
                if factor:
                    factors.append(factor)

            logger.debug(f"[{ticker}] Detected {len(bid_walls)} bid walls, {len(ask_walls)} ask walls")
            
        except Exception as e:
            logger.error(f"[{ticker}] Error analyzing L2 walls: {e}")
            
        return factors

    def _detect_walls_in_side(self, orders: List[Dict], side: str, current_price: float, 
                             wall_threshold: float, significant_threshold: float) -> List[Dict[str, Any]]:
        """Detect walls in bid or ask side."""
        walls = []
        
        try:
            for order in orders:
                price = order['price']
                size = order['size']
                
                # Check if this order qualifies as a wall
                if size < wall_threshold:
                    continue
                
                # Check proximity to current price
                price_distance_pct = abs(price - current_price) / current_price
                if price_distance_pct > self.config['wall_proximity_pct']:
                    continue
                
                # Calculate wall strength
                strength = self._calculate_wall_strength(
                    size, price, current_price, wall_threshold, significant_threshold
                )
                
                # Create wall data
                wall = {
                    'side': side,
                    'price': price,
                    'size': size,
                    'strength': strength,
                    'level': order.get('level', 1),
                    'price_distance_pct': price_distance_pct,
                    'is_significant': size >= significant_threshold,
                    'wall_type': 'significant' if size >= significant_threshold else 'standard'
                }
                
                walls.append(wall)
                
        except Exception as e:
            logger.error(f"Error detecting walls in {side} side: {e}")
            
        return walls

    def _calculate_wall_strength(self, size: float, price: float, current_price: float,
                                wall_threshold: float, significant_threshold: float) -> float:
        """Calculate wall strength score (0.0 to 1.0)."""
        try:
            # Size component (normalized)
            size_score = min(1.0, size / significant_threshold)
            
            # Proximity component (closer = stronger)
            price_distance_pct = abs(price - current_price) / current_price
            proximity_score = max(0.0, 1.0 - (price_distance_pct / self.config['wall_proximity_pct']))
            
            # Relative size component
            relative_score = min(1.0, size / wall_threshold)
            
            # Weighted combination
            strength = (
                size_score * self.config['size_weight'] +
                proximity_score * self.config['price_distance_weight'] +
                relative_score * self.config['relative_size_weight']
            )
            
            return min(1.0, max(0.0, strength))
            
        except Exception as e:
            logger.error(f"Error calculating wall strength: {e}")
            return 0.5

    def _create_wall_factor(self, ticker: str, wall: Dict[str, Any], direction: DirectionBias, factor_type: str) -> Optional[FactorData]:
        """Create a FactorData object from wall analysis."""
        try:
            factor_name = f"OBW_{factor_type}_{TimeFrame.MIN_1.value}"
            
            # Create detailed information aligned with your factor system
            details = {
                'wall_price': wall['price'],
                'wall_size': wall['size'],
                'wall_level': wall['level'],
                'price_distance_pct': wall['price_distance_pct'],
                'is_significant_wall': wall['is_significant'],
                'wall_type': wall['wall_type'],
                'wall_side': wall['side'],
                'depth_ratio': wall.get('depth_ratio', 1.0),
                'institutional_flag': wall['is_significant']
            }
            
            factor = FactorData(
                factor_name=factor_name,
                ticker=ticker,
                timestamp=datetime.now(),
                timeframe=TimeFrame.MIN_1,
                direction_bias=direction,
                strength_score=wall['strength'],
                key_level_price=wall['price'],
                details=details,
                reason_short=f"{wall['wall_type'].title()} {wall['side']} wall at ${wall['price']:.2f} ({wall['size']:,.0f})",
                analyzer_name="OrderBookWallAnalyzer",
                data_quality_score=0.95 if wall['is_significant'] else 0.85
            )
            
            return factor
            
        except Exception as e:
            logger.error(f"Error creating wall factor: {e}")
            return None

    def _analyze_depth_imbalances(self, ticker: str, current_price: float, order_book: Dict[str, Any]) -> List[FactorData]:
        """Analyze market depth imbalances between bid and ask sides."""
        factors = []
        
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return factors
            
            # Calculate total depth on each side
            total_bid_size = sum(order['size'] for order in bids)
            total_ask_size = sum(order['size'] for order in asks)
            total_depth = total_bid_size + total_ask_size
            
            if total_depth == 0:
                return factors
            
            # Calculate imbalance ratio
            bid_ratio = total_bid_size / total_depth
            ask_ratio = total_ask_size / total_depth
            imbalance = bid_ratio - ask_ratio  # Positive = bid heavy, Negative = ask heavy
            
            # Check if imbalance is significant
            imbalance_threshold = self.config['depth_imbalance_threshold']
            if abs(imbalance) >= imbalance_threshold:
                
                # Determine direction and strength
                if imbalance > 0:
                    direction = DirectionBias.BULLISH
                    imbalance_type = "bid_heavy"
                else:
                    direction = DirectionBias.BEARISH
                    imbalance_type = "ask_heavy"
                
                strength = min(1.0, abs(imbalance) / 0.5)  # Normalize to 0-1
                
                details = {
                    'total_bid_size': total_bid_size,
                    'total_ask_size': total_ask_size,
                    'bid_ratio': bid_ratio,
                    'ask_ratio': ask_ratio,
                    'imbalance_ratio': imbalance,
                    'imbalance_type': imbalance_type,
                    'num_bid_levels': len(bids),
                    'num_ask_levels': len(asks)
                }
                
                factor = FactorData(
                    factor_name=f"OBW_DepthImbalance_{TimeFrame.MIN_1.value}",
                    ticker=ticker,
                    timestamp=datetime.now(),
                    timeframe=TimeFrame.MIN_1,
                    direction_bias=direction,
                    strength_score=strength,
                    key_level_price=current_price,
                    details=details,
                    reason_short=f"Market depth {imbalance_type} imbalance: {abs(imbalance):.1%}",
                    analyzer_name="OrderBookWallAnalyzer",
                    data_quality_score=0.9
                )
                
                factors.append(factor)
                
        except Exception as e:
            logger.error(f"[{ticker}] Error analyzing depth imbalances: {e}")
            
        return factors

    def _detect_institutional_walls(self, ticker: str, current_price: float, order_book: Dict[str, Any]) -> List[FactorData]:
        """Detect institutional-sized walls and unusual order patterns."""
        factors = []
        
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            all_orders = bids + asks
            
            if not all_orders:
                return factors
            
            # Calculate size statistics
            sizes = [order['size'] for order in all_orders]
            avg_size = np.mean(sizes)
            std_size = np.std(sizes)
            
            institutional_threshold = max(
                self.config['institutional_size_threshold'],
                avg_size + (2 * std_size)  # 2 standard deviations above mean
            )
            
            # Find institutional-sized orders
            for order in all_orders:
                size = order['size']
                price = order['price']
                
                if size >= institutional_threshold:
                    
                    # Calculate how unusual this size is
                    size_z_score = (size - avg_size) / std_size if std_size > 0 else 0
                    is_extremely_large = size >= (avg_size * self.config['unusual_size_multiplier'])
                    
                    # Determine side and direction
                    is_bid = order in bids
                    direction = DirectionBias.BULLISH if is_bid else DirectionBias.BEARISH
                    side_name = "bid" if is_bid else "ask"
                    
                    # Calculate strength based on size relative to normal
                    strength = min(1.0, size / (institutional_threshold * 2))
                    
                    details = {
                        'institutional_size': size,
                        'average_market_size': avg_size,
                        'size_z_score': size_z_score,
                        'is_extremely_large': is_extremely_large,
                        'institutional_threshold': institutional_threshold,
                        'order_side': side_name,
                        'price_level': price,
                        'size_vs_threshold_ratio': size / institutional_threshold
                    }
                    
                    factor = FactorData(
                        factor_name=f"OBW_InstitutionalWall_{TimeFrame.MIN_1.value}",
                        ticker=ticker,
                        timestamp=datetime.now(),
                        timeframe=TimeFrame.MIN_1,
                        direction_bias=direction,
                        strength_score=strength,
                        key_level_price=price,
                        details=details,
                        reason_short=f"Institutional {side_name} wall: {size:,.0f} shares at ${price:.2f}",
                        analyzer_name="OrderBookWallAnalyzer",
                        data_quality_score=0.92
                    )
                    
                    factors.append(factor)
                    
        except Exception as e:
            logger.error(f"[{ticker}] Error detecting institutional walls: {e}")
            
        return factors

    def _analyze_synthetic_walls(self, ticker: str, current_price: float, mtf_data: Dict[str, pd.DataFrame]) -> List[FactorData]:
        """Analyze synthetic walls from price action when L2 data unavailable."""
        factors = []
        
        logger.warning(f"[{ticker}] SYNTHETIC ANALYSIS: Using price/volume data instead of real L2 order book")
        
        try:
            # Use the most granular timeframe available
            timeframes = ['1m', '5m', '15m', '1h']
            price_data = None
            
            for tf in timeframes:
                if tf in mtf_data and not mtf_data[tf].empty:
                    price_data = mtf_data[tf]
                    break
            
            if price_data is None or len(price_data) < 20:
                return factors
            
            # Analyze recent price action for synthetic walls
            recent_data = price_data.tail(20)
            
            # Look for price levels with high volume (potential walls)
            volume_threshold = recent_data['volume'].quantile(0.8)
            high_volume_bars = recent_data[recent_data['volume'] >= volume_threshold]
            
            if not high_volume_bars.empty:
                for _, bar in high_volume_bars.iterrows():
                    
                    # Check if this level is close to current price
                    level_price = (bar['high'] + bar['low']) / 2
                    distance_pct = abs(level_price - current_price) / current_price
                    
                    if distance_pct <= self.config['wall_proximity_pct']:
                        
                        # Determine if this was support or resistance based on closes
                        closes_above = (recent_data['close'] > level_price).sum()
                        closes_below = (recent_data['close'] < level_price).sum()
                        
                        if closes_above > closes_below:
                            direction = DirectionBias.BULLISH
                            wall_type = "SyntheticSupport"
                        else:
                            direction = DirectionBias.BEARISH
                            wall_type = "SyntheticResistance"
                        
                        # Calculate strength based on volume and proximity
                        volume_strength = min(1.0, bar['volume'] / volume_threshold)
                        proximity_strength = 1.0 - (distance_pct / self.config['wall_proximity_pct'])
                        strength = (volume_strength + proximity_strength) / 2
                        
                        details = {
                            'synthetic_wall_price': level_price,
                            'volume_at_level': bar['volume'],
                            'volume_threshold': volume_threshold,
                            'distance_from_current': distance_pct,
                            'closes_above': closes_above,
                            'closes_below': closes_below,
                            'wall_basis': 'volume_concentration',
                            'data_quality_warning': 'SYNTHETIC_ANALYSIS_NOT_REAL_L2'
                        }
                        
                        factor = FactorData(
                            factor_name=f"OBW_{wall_type}_{TimeFrame.MIN_15.value}",
                            ticker=ticker,
                            timestamp=datetime.now(),
                            timeframe=TimeFrame.MIN_15,
                            direction_bias=direction,
                            strength_score=strength,
                            key_level_price=level_price,
                            details=details,
                            reason_short=f"SYNTHETIC {wall_type.lower()} from volume (NOT REAL L2)",
                            analyzer_name="OrderBookWallAnalyzer",
                            data_quality_score=0.4  # Reduced quality for synthetic analysis with warning
                        )
                        
                        factors.append(factor)
                        
        except Exception as e:
            logger.error(f"[{ticker}] Error analyzing synthetic walls: {e}")
            logger.error(f"[{ticker}] SYNTHETIC ANALYSIS FAILED - no wall factors generated")
            
        return factors

    def _generate_basic_factors(self, ticker: str, current_price: float) -> List[FactorData]:
        """Generate basic market structure factors as fallback."""
        factors = []
        
        logger.error(f"[{ticker}] BASIC FALLBACK: No real order book data available for analysis")
        
        try:
            # Generate a basic market structure analysis factor
            factor = FactorData(
                factor_name=f"OBW_MarketStructure_{TimeFrame.MIN_1.value}",
                ticker=ticker,
                timestamp=datetime.now(),
                timeframe=TimeFrame.MIN_1,
                direction_bias=DirectionBias.NEUTRAL,
                strength_score=0.4,
                key_level_price=current_price,
                details={
                    'analysis_type': 'basic_fallback',
                    'current_price': current_price,
                    'data_availability': 'NO_REAL_ORDER_BOOK_DATA',
                    'quality_warning': 'BASIC_FALLBACK_NOT_REAL_ANALYSIS'
                },
                reason_short="BASIC fallback - NO REAL order book data available",
                analyzer_name="OrderBookWallAnalyzer",
                data_quality_score=0.5
            )
            
            factors.append(factor)
            
        except Exception as e:
            logger.error(f"[{ticker}] Error generating basic factors: {e}")
            
        return factors

    def _generate_error_recovery_factors(self, ticker: str, current_price: float) -> List[FactorData]:
        """Generate error recovery factors when analysis fails."""
        try:
            factor = FactorData(
                factor_name=f"OBW_ErrorRecovery_{TimeFrame.MIN_1.value}",
                ticker=ticker,
                timestamp=datetime.now(),
                timeframe=TimeFrame.MIN_1,
                direction_bias=DirectionBias.NEUTRAL,
                strength_score=0.2,
                key_level_price=current_price or 0,
                details={
                    'analysis_status': 'error_recovery',
                    'fallback_mode': True
                },
                reason_short="OrderBook analysis encountered errors - basic structure provided",
                analyzer_name="OrderBookWallAnalyzer",
                data_quality_score=0.3
            )
            
            return [factor]
            
        except Exception as e:
            logger.error(f"[{ticker}] Error in error recovery: {e}")
            return []
