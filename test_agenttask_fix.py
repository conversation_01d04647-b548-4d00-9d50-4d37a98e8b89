#!/usr/bin/env python3
"""
Test AgentTask Constructor Fix
Verify that all Extended Analysis agents can create AgentTask objects correctly
"""

import sys
import traceback
from datetime import datetime
from agents.agent_base import AgentTask, TaskPriority

def test_agenttask_creation():
    """Test AgentTask creation with correct parameters"""
    print("Testing AgentTask creation...")
    
    try:
        # Test creating AgentTask with correct parameters
        task = AgentTask(
            task_id="test-123",
            task_type='test_analysis',
            agent_type='test_agent',
            priority=TaskPriority.NORMAL,
            inputs={'ticker': 'MSFT'},
            workflow_file='test_workflow.json',
            quality_standards='Test standards',
            performance_targets={'execution_time': 5.0},
            dependencies=[],
            training_data_tags=['test'],
            timestamp=datetime.now()
        )
        
        print("✅ AgentTask creation successful")
        print(f"   Task ID: {task.task_id}")
        print(f"   Agent Type: {task.agent_type}")
        return True
        
    except Exception as e:
        print(f"❌ AgentTask creation failed: {e}")
        traceback.print_exc()
        return False

def test_extended_analysis_agents():
    """Test all Extended Analysis agents execute methods"""
    
    agents_to_test = [
        ('FVGSpecialist', 'agents.fvg_specialist'),
        ('MeanReversionSpecialist', 'agents.mean_reversion_specialist'),
        ('PivotPointSpecialist', 'agents.pivot_point_specialist'),
        ('SignalConvergenceOrchestrator', 'agents.signal_convergence_orchestrator'),
        ('MathValidatorAgent', 'agents.math_validator_agent'),
        ('SignalQualityAgent', 'agents.signal_quality_agent')
    ]
    
    results = {}
    
    for agent_name, module_name in agents_to_test:
        print(f"\nTesting {agent_name}...")
        
        try:
            # Import the agent
            module = __import__(module_name, fromlist=[agent_name])
            agent_class = getattr(module, agent_name)
            
            # Create agent instance
            agent = agent_class()
            
            # Test execute method
            result = agent.execute('MSFT')
            
            print(f"✅ {agent_name} execute successful")
            print(f"   Result type: {type(result)}")
            if isinstance(result, dict):
                print(f"   Keys: {list(result.keys())}")
            
            results[agent_name] = {'status': 'success', 'result': result}
            
        except Exception as e:
            print(f"❌ {agent_name} failed: {e}")
            traceback.print_exc()
            results[agent_name] = {'status': 'failed', 'error': str(e)}
    
    return results

def main():
    """Main test function"""
    print("EXTENDED ANALYSIS AGENTS - AGENTTASK FIX TEST")
    print("=" * 60)
    
    # Test 1: AgentTask creation
    agenttask_success = test_agenttask_creation()
    
    if not agenttask_success:
        print("\n❌ AgentTask creation failed - cannot proceed with agent tests")
        return 1
    
    # Test 2: Extended Analysis agents
    print("\n" + "=" * 60)
    print("TESTING EXTENDED ANALYSIS AGENTS")
    print("=" * 60)
    
    results = test_extended_analysis_agents()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    successful = [name for name, result in results.items() if result['status'] == 'success']
    failed = [name for name, result in results.items() if result['status'] == 'failed']
    
    print(f"✅ Successful: {len(successful)}")
    for name in successful:
        print(f"   - {name}")
    
    print(f"❌ Failed: {len(failed)}")
    for name in failed:
        print(f"   - {name}: {results[name]['error']}")
    
    if len(failed) == 0:
        print("\n🎉 ALL TESTS PASSED - AgentTask fix successful!")
        return 0
    else:
        print(f"\n⚠️  {len(failed)} agents still failing")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
