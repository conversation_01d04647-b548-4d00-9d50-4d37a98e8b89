"""
Enhanced Flow Physics Strategy with Performance Monitoring

Integrates performance monitoring to track component effectiveness and enable
adaptive weight adjustment based on real trading results.
"""

import logging
import uuid
from typing import Dict, List, Optional, Any
import numpy as np
import pandas as pd
import sys
import os
from pathlib import Path

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

from strategies.enhanced_flow_physics_strategy import EnhancedFlowPhysicsStrategy
from strategies.performance_monitor import PerformanceMonitor, create_performance_monitor
from strategies.base_strategy import StrategySignal, SignalDirection

logger = logging.getLogger(__name__)


class MonitoredEnhancedFlowPhysicsStrategy(EnhancedFlowPhysicsStrategy):
    """
    Enhanced Flow Physics Strategy with integrated performance monitoring.
    
    Features:
    - Real-time performance tracking for each component
    - Adaptive weight adjustment based on component success rates
    - Signal attribution and confluence analysis
    - Performance-based recommendations
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize strategy with performance monitoring."""
        super().__init__(config)
        
        # Initialize performance monitor
        monitor_config = self.config.get('performance_monitor', {})
        self.performance_monitor = create_performance_monitor(monitor_config)
        
        # Adaptive weights (start with defaults, will be updated)
        self.adaptive_weights_enabled = self.config.get('enable_adaptive_weights', True)
        self.current_weights = self.config.get('confidence_weights', {
            'flow_physics': 0.45,
            'pure_liquidity': 0.30,
            'enhanced_greeks': 0.25
        })
        
        # Signal tracking
        self.active_signals: Dict[str, str] = {}  # signal_id -> ticker
        
        # Performance update frequency
        self.performance_update_interval = self.config.get('performance_update_interval', 100)  # signals
        self.signals_processed = 0
        
        logger.info("Initialized Monitored Enhanced Flow Physics Strategy")
    
    def analyze(self,
               ticker: str,
               data: Dict[str, Any],
               analysis_results: Dict[str, Any]) -> List[StrategySignal]:
        """
        Analyze with performance monitoring and adaptive weights.
        """
        # Update adaptive weights if enabled
        if self.adaptive_weights_enabled:
            self._update_adaptive_weights()
        
        # Get signals using potentially updated weights
        signals = super().analyze(ticker, data, analysis_results)
        
        # Record signals for performance tracking
        current_price = data.get('current_price', 0)
        for signal in signals:
            self._record_signal(signal, ticker, current_price)
        
        # Update price for existing signals
        self._update_existing_signals(ticker, current_price)
        
        # Periodic performance evaluation
        self.signals_processed += 1
        if self.signals_processed % self.performance_update_interval == 0:
            self._evaluate_performance()
        
        return signals
    
    def _update_adaptive_weights(self) -> None:
        """Update component weights based on recent performance."""
        try:
            adaptive_weights = self.performance_monitor.get_adaptive_weights()
            
            # Only update if we have sufficient data
            total_signals = sum(
                self.performance_monitor.component_metrics[comp].total_signals 
                for comp in adaptive_weights.keys()
            )
            
            min_signals = self.performance_monitor.config.get('min_signals_for_stats', 10)
            if total_signals >= min_signals:
                old_weights = self.current_weights.copy()
                self.current_weights.update(adaptive_weights)
                
                # Update strategy config
                self.config['confidence_weights'] = self.current_weights
                
                # Log significant changes
                for component in adaptive_weights:
                    old_weight = old_weights.get(component, 0)
                    new_weight = adaptive_weights[component]
                    change = abs(new_weight - old_weight)
                    
                    if change > 0.05:  # 5% change threshold
                        logger.info(f"Adaptive weight update: {component} {old_weight:.2f} -> {new_weight:.2f}")
                        
        except Exception as e:
            logger.warning(f"Failed to update adaptive weights: {e}")
    
    def _record_signal(self, signal: StrategySignal, ticker: str, current_price: float) -> None:
        """Record a new signal for performance tracking."""
        try:
            signal_id = str(uuid.uuid4())
            
            # Determine primary component and contributing components
            component = 'confluence'  # Default for multi-component signals
            contributing_components = []
            component_weights = {}
            
            # Analyze signal source from the analysis data
            if hasattr(signal, 'analysis') and signal.analysis:
                analysis_component = signal.analysis.get('component', 'unknown')
                
                if analysis_component == 'confluence':
                    # Multi-component signal
                    individual_signals = signal.analysis.get('individual_signals', [])
                    for ind_signal in individual_signals:
                        comp = ind_signal.get('component', 'unknown')
                        if comp in self.current_weights:
                            contributing_components.append(comp)
                            component_weights[comp] = self.current_weights[comp]
                else:
                    # Single component signal
                    component = analysis_component
                    contributing_components = [component]
                    component_weights = {component: 1.0}
            
            if not contributing_components:
                # Fallback: assign based on confidence and current weights
                component = 'flow_physics'  # Default to strongest component
                contributing_components = [component]
                component_weights = {component: 1.0}
            
            # Record in performance monitor
            self.performance_monitor.record_signal(
                signal_id=signal_id,
                ticker=ticker,
                direction=signal.direction.value,
                confidence=signal.confidence,
                component=component,
                entry_price=signal.entry,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                contributing_components=contributing_components,
                component_weights=component_weights
            )
            
            # Track active signal
            self.active_signals[signal_id] = ticker
            
            logger.info(f"Recorded signal {signal_id[:8]} for {ticker}: {component} ({signal.confidence:.2f})")
            
        except Exception as e:
            logger.error(f"Failed to record signal: {e}")
    
    def _update_existing_signals(self, ticker: str, current_price: float) -> None:
        """Update current prices for existing signals of this ticker."""
        try:
            signals_to_update = [
                signal_id for signal_id, signal_ticker in self.active_signals.items()
                if signal_ticker == ticker
            ]
            
            for signal_id in signals_to_update:
                self.performance_monitor.update_signal_price(signal_id, current_price)
                
                # Check if signal was automatically closed
                signal = self.performance_monitor.signals.get(signal_id)
                if signal and signal.is_closed:
                    # Remove from active tracking
                    self.active_signals.pop(signal_id, None)
                    
        except Exception as e:
            logger.error(f"Failed to update existing signals: {e}")
    
    def _evaluate_performance(self) -> None:
        """Evaluate strategy performance and log insights."""
        try:
            report = self.performance_monitor.generate_performance_report()
            
            # Log key performance metrics
            summary = report['summary']
            logger.info(f"Performance Update: {summary['closed_signals']} closed signals, "
                       f"{summary['open_signals']} open signals")
            
            # Log component performance
            for component, perf in report['component_performance'].items():
                if perf['total_signals'] > 0:
                    logger.info(f"{component}: {perf['total_signals']} signals, "
                               f"{perf['success_rate']:.1%} win rate, "
                               f"{perf['recent_performance']:.1%} recent performance")
            
            # Log recommendations
            if report['recommendations']:
                logger.info("Performance Recommendations:")
                for rec in report['recommendations']:
                    logger.info(f"  - {rec}")
            
            # Save performance data
            self.performance_monitor.save_performance_data()
            
        except Exception as e:
            logger.error(f"Failed to evaluate performance: {e}")
    
    def close_signal(self, signal_id: str, exit_price: float, close_reason: str = 'manual') -> None:
        """Manually close a signal."""
        try:
            self.performance_monitor.close_signal(signal_id, exit_price, close_reason)
            self.active_signals.pop(signal_id, None)
            logger.info(f"Manually closed signal {signal_id[:8]}: {close_reason}")
            
        except Exception as e:
            logger.error(f"Failed to close signal {signal_id}: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        return self.performance_monitor.generate_performance_report()
    
    def get_current_weights(self) -> Dict[str, float]:
        """Get current component weights."""
        return self.current_weights.copy()
    
    def get_component_performance(self, component: str) -> Dict[str, Any]:
        """Get performance metrics for a specific component."""
        metrics = self.performance_monitor.get_component_performance(component)
        return {
            'total_signals': metrics.total_signals,
            'success_rate': metrics.win_rate,
            'avg_pnl': metrics.avg_pnl,
            'total_pnl': metrics.total_pnl,
            'recent_performance': metrics.recent_performance,
            'sharpe_ratio': metrics.sharpe_ratio,
            'profit_factor': metrics.profit_factor,
            'avg_win': metrics.avg_win,
            'avg_loss': metrics.avg_loss
        }
    
    def reset_performance_data(self) -> None:
        """Reset all performance data (use with caution)."""
        self.performance_monitor.signals.clear()
        self.active_signals.clear()
        
        for metrics in self.performance_monitor.component_metrics.values():
            metrics.total_signals = 0
            metrics.successful_signals = 0
            metrics.failed_signals = 0
            metrics.total_pnl = 0.0
            metrics.avg_pnl = 0.0
            metrics.recent_performance = 0.0
        
        logger.warning("Performance data reset")
    
    def export_performance_data(self, filepath: str) -> None:
        """Export performance data to CSV for external analysis."""
        try:
            signals_data = []
            
            for signal in self.performance_monitor.signals.values():
                signals_data.append({
                    'signal_id': signal.signal_id,
                    'timestamp': signal.timestamp,
                    'ticker': signal.ticker,
                    'direction': signal.direction,
                    'confidence': signal.confidence,
                    'component': signal.component,
                    'entry_price': signal.entry_price,
                    'exit_price': signal.exit_price,
                    'realized_pnl': signal.realized_pnl,
                    'max_favorable': signal.max_favorable,
                    'max_adverse': signal.max_adverse,
                    'days_open': signal.days_open,
                    'is_closed': signal.is_closed,
                    'close_reason': signal.close_reason,
                    'contributing_components': ','.join(signal.contributing_components)
                })
            
            df = pd.DataFrame(signals_data)
            df.to_csv(filepath, index=False)
            
            logger.info(f"Exported {len(signals_data)} signals to {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to export performance data: {e}")


def create_monitored_strategy(config: Optional[Dict[str, Any]] = None) -> MonitoredEnhancedFlowPhysicsStrategy:
    """Factory function to create a monitored strategy."""
    default_config = {
        'enable_adaptive_weights': True,
        'performance_update_interval': 50,
        'performance_monitor': {
            'performance_window_days': 30,
            'min_signals_for_stats': 10,
            'weight_adjustment_factor': 0.15,
            'max_signal_days': 30
        }
    }
    
    if config:
        default_config.update(config)
    
    return MonitoredEnhancedFlowPhysicsStrategy(default_config)
