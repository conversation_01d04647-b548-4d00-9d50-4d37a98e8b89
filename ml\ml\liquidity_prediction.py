"""
Liquidity Prediction Engine

This module provides prediction capabilities for liquidity analysis,
focusing on level strength prediction, price reaction prediction,
and confidence scoring.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import time
import os
import pickle
from datetime import datetime

# Internal imports
from ml_logging import get_logger
from liquidity_features import LiquidityFeatureExtractor
from feature_compatibility import align_features

# Setup logger
logger = get_logger('liquidity_prediction')

class LiquidityPredictionEngine:
    """Predicts liquidity-related outcomes using ML models."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the liquidity prediction engine.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.feature_extractor = LiquidityFeatureExtractor(config)
        self.models = {}
        self.initialized = False

        # Default model paths
        self.model_dir = self.config.get('model_dir', os.path.join(os.path.dirname(__file__), 'models'))

        # Default model names
        self.level_strength_model_name = self.config.get('level_strength_model', 'level_strength_model.pkl')
        self.price_reaction_model_name = self.config.get('price_reaction_model', 'price_reaction_model.pkl')

        # Performance tracking
        self.performance_data = {}

        logger.info("Initialized LiquidityPredictionEngine")

    def initialize(self) -> bool:
        """
        Initialize the prediction engine by loading models.

        Returns:
            True if initialization was successful, False otherwise
        """
        if self.initialized:
            logger.info("Prediction engine already initialized")
            return True

        try:
            # Create model directory if it doesn't exist
            os.makedirs(self.model_dir, exist_ok=True)

            # Load level strength model if it exists
            level_strength_path = os.path.join(self.model_dir, self.level_strength_model_name)
            if os.path.exists(level_strength_path):
                with open(level_strength_path, 'rb') as f:
                    self.models['level_strength'] = pickle.load(f)
                logger.info(f"Loaded level strength model from {level_strength_path}")
            else:
                logger.warning(f"Level strength model not found at {level_strength_path}")
                # Create a simple fallback model
                self.models['level_strength'] = self._create_fallback_level_strength_model()
                logger.info("Created fallback level strength model")

            # Load price reaction model if it exists
            price_reaction_path = os.path.join(self.model_dir, self.price_reaction_model_name)
            if os.path.exists(price_reaction_path):
                with open(price_reaction_path, 'rb') as f:
                    self.models['price_reaction'] = pickle.load(f)
                logger.info(f"Loaded price reaction model from {price_reaction_path}")
            else:
                logger.warning(f"Price reaction model not found at {price_reaction_path}")
                # Create a simple fallback model
                self.models['price_reaction'] = self._create_fallback_price_reaction_model()
                logger.info("Created fallback price reaction model")

            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"Failed to initialize prediction engine: {str(e)}")
            return False

    def _create_fallback_level_strength_model(self) -> Any:
        """
        Create a simple fallback model for level strength prediction.

        Returns:
            A simple model object
        """
        # Simple rule-based model
        class FallbackLevelStrengthModel:
            def __init__(self):
                # Add metadata with required features
                self.metadata = {
                    'features': [
                        'support_strength', 'resistance_strength',
                        'support_source_count', 'resistance_source_count',
                        'support_confluence', 'resistance_confluence',
                        'current_gex', 'distance_to_negative_gamma',
                        'distance_to_positive_gamma', 'distance_to_poc',
                        'distance_to_value_area'
                    ]
                }

            def predict(self, features: pd.DataFrame) -> np.ndarray:
                """
                Predict level strength based on simple rules.

                Args:
                    features: DataFrame with features

                Returns:
                    Array of predicted strengths
                """
                predictions = np.zeros(len(features))

                # Use support/resistance strength if available
                if 'support_strength' in features.columns:
                    predictions += features['support_strength'].values

                if 'resistance_strength' in features.columns:
                    predictions += features['resistance_strength'].values

                # Add source count contribution
                if 'support_source_count' in features.columns:
                    predictions += 0.1 * features['support_source_count'].values

                if 'resistance_source_count' in features.columns:
                    predictions += 0.1 * features['resistance_source_count'].values

                # Add confluence contribution
                if 'support_confluence' in features.columns:
                    predictions += 0.2 * features['support_confluence'].values

                if 'resistance_confluence' in features.columns:
                    predictions += 0.2 * features['resistance_confluence'].values

                # Add GEX contribution if available
                if 'current_gex' in features.columns:
                    predictions += 0.1 * features['current_gex'].values

                # Add volume profile contribution if available
                if 'distance_to_poc' in features.columns:
                    # Closer to POC = stronger
                    dist_to_poc = features['distance_to_poc'].values
                    predictions += 0.1 * (1.0 - np.clip(np.abs(dist_to_poc), 0, 1))

                # Normalize to 0-1 range
                predictions = np.clip(predictions / 10, 0, 1)

                return predictions

        return FallbackLevelStrengthModel()

    def _create_fallback_price_reaction_model(self) -> Any:
        """
        Create a simple fallback model for price reaction prediction.

        Returns:
            A simple model object
        """
        # Simple rule-based model
        class FallbackPriceReactionModel:
            def __init__(self):
                # Add metadata with required features
                self.metadata = {
                    'features': [
                        'distance_to_support', 'distance_to_resistance',
                        'gex_impact', 'dealer_positioning',
                        'current_gex', 'distance_to_negative_gamma',
                        'distance_to_positive_gamma', 'distance_to_poc',
                        'distance_to_value_area', 'support_strength',
                        'resistance_strength', 'liquidity_imbalance'
                    ]
                }

            def predict(self, features: pd.DataFrame) -> np.ndarray:
                """
                Predict price reaction based on simple rules.

                Args:
                    features: DataFrame with features

                Returns:
                    Array of predicted reactions (-1 to 1)
                """
                predictions = np.zeros(len(features))

                # Use distance to support/resistance
                if 'distance_to_support' in features.columns:
                    # Negative distance to support (price below) -> positive reaction
                    predictions -= np.clip(features['distance_to_support'].values, -0.05, 0.05) * 10

                if 'distance_to_resistance' in features.columns:
                    # Negative distance to resistance (price above) -> negative reaction
                    predictions -= np.clip(features['distance_to_resistance'].values, -0.05, 0.05) * 10

                # Add GEX contribution if available
                if 'gex_impact' in features.columns:
                    predictions += 0.3 * features['gex_impact'].values
                elif 'current_gex' in features.columns:
                    # Use current_gex as a fallback
                    predictions += 0.2 * features['current_gex'].values

                # Add dealer positioning contribution if available
                if 'dealer_positioning' in features.columns:
                    predictions += 0.2 * features['dealer_positioning'].values

                # Add liquidity imbalance contribution if available
                if 'liquidity_imbalance' in features.columns:
                    predictions += 0.2 * features['liquidity_imbalance'].values

                # Add volume profile contribution if available
                if 'distance_to_poc' in features.columns:
                    # Closer to POC = stronger mean reversion
                    dist_to_poc = features['distance_to_poc'].values
                    predictions -= 0.1 * np.sign(dist_to_poc) * (1.0 - np.clip(np.abs(dist_to_poc), 0, 1))

                # Normalize to -1 to 1 range
                predictions = np.clip(predictions, -1, 1)

                return predictions

        return FallbackPriceReactionModel()

    def predict_level_strength(self,
                              price_data: pd.DataFrame,
                              liquidity_levels: Dict[str, List[Dict[str, Any]]],
                              options_data: Optional[pd.DataFrame] = None,
                              volume_profile: Optional[Dict[str, Any]] = None,
                              gex_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Predict the strength of liquidity levels.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data

        Returns:
            Dictionary with enhanced liquidity levels
        """
        start_time = time.time()

        # Ensure engine is initialized
        if not self.initialized:
            self.initialize()

        # Extract features
        features = self.feature_extractor.extract_features(
            price_data=price_data,
            options_data=options_data,
            volume_profile=volume_profile,
            gex_data=gex_data,
            liquidity_levels=liquidity_levels
        )

        if features.empty:
            logger.error("Failed to extract features for level strength prediction")
            return {'enhanced_levels': liquidity_levels, 'confidence': 0.0}

        # Get model
        model = self.models.get('level_strength')
        if model is None:
            logger.error("Level strength model not available")
            return {'enhanced_levels': liquidity_levels, 'confidence': 0.0}

        # Make predictions
        try:
            # Get required features from model metadata if available
            required_features = []
            if hasattr(model, 'feature_names_in_'):
                required_features = model.feature_names_in_.tolist()
            elif hasattr(model, 'metadata') and isinstance(model.metadata, dict):
                required_features = model.metadata.get('features', [])

            # If we have required features, align the features
            if required_features:
                logger.info(f"Aligning {len(features.columns)} features with {len(required_features)} required features")
                aligned_features = align_features(features, required_features)
                logger.info(f"After alignment: {len(aligned_features.columns)} features")
            else:
                aligned_features = features

            # Predict strength adjustment
            strength_adjustments = model.predict(aligned_features)

            # Apply adjustments to liquidity levels
            enhanced_levels = self._enhance_liquidity_levels(liquidity_levels, strength_adjustments)

            # Calculate confidence
            confidence = self._calculate_confidence(features, strength_adjustments)

            elapsed = time.time() - start_time
            logger.info(f"Level strength prediction completed in {elapsed:.2f} seconds")

            return {
                'enhanced_levels': enhanced_levels,
                'confidence': confidence,
                'processing_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error in level strength prediction: {str(e)}")
            return {'enhanced_levels': liquidity_levels, 'confidence': 0.0}

    def _enhance_liquidity_levels(self,
                                 liquidity_levels: Dict[str, List[Dict[str, Any]]],
                                 strength_adjustments: np.ndarray) -> Dict[str, List[Dict[str, Any]]]:
        """
        Enhance liquidity levels with predicted strength adjustments.

        Args:
            liquidity_levels: Dictionary with liquidity levels
            strength_adjustments: Array of strength adjustments

        Returns:
            Dictionary with enhanced liquidity levels
        """
        # Create a deep copy of the liquidity levels
        enhanced_levels = {
            'support': [level.copy() for level in liquidity_levels.get('support', [])],
            'resistance': [level.copy() for level in liquidity_levels.get('resistance', [])]
        }

        # Apply a global adjustment based on the prediction
        # This is a simplified approach; in a real model, we would have
        # individual predictions for each level
        if len(strength_adjustments) > 0:
            avg_adjustment = float(np.mean(strength_adjustments))

            # Apply to all support levels
            for level in enhanced_levels['support']:
                # Adjust strength by up to 20% based on prediction
                current_strength = level.get('strength', 0.5)
                adjusted_strength = current_strength * (1 + 0.2 * avg_adjustment)
                level['strength'] = min(1.0, max(0.0, adjusted_strength))
                level['ml_enhanced'] = True
                level['ml_adjustment'] = avg_adjustment

            # Apply to all resistance levels
            for level in enhanced_levels['resistance']:
                # Adjust strength by up to 20% based on prediction
                current_strength = level.get('strength', 0.5)
                adjusted_strength = current_strength * (1 + 0.2 * avg_adjustment)
                level['strength'] = min(1.0, max(0.0, adjusted_strength))
                level['ml_enhanced'] = True
                level['ml_adjustment'] = avg_adjustment

        return enhanced_levels

    def _calculate_confidence(self, features: pd.DataFrame, predictions: np.ndarray) -> float:
        """
        Calculate confidence score for predictions.

        Args:
            features: DataFrame with features
            predictions: Array of predictions

        Returns:
            Confidence score (0-1)
        """
        # Simple confidence calculation based on feature quality
        # In a real model, this would be more sophisticated

        # Check for missing values
        missing_ratio = features.isna().mean().mean()

        # Check for feature coverage
        expected_features = [
            'support_strength', 'resistance_strength',
            'support_source_count', 'resistance_source_count',
            'distance_to_support', 'distance_to_resistance'
        ]

        feature_coverage = sum(1 for f in expected_features if f in features.columns) / len(expected_features)

        # Check prediction variance
        prediction_variance = np.var(predictions) if len(predictions) > 0 else 0

        # Calculate confidence
        confidence = (
            (1 - missing_ratio) * 0.4 +
            feature_coverage * 0.4 +
            min(1.0, prediction_variance * 10) * 0.2
        )

        return float(confidence)

    def predict_price_reaction(self,
                              price_data: pd.DataFrame,
                              liquidity_levels: Dict[str, List[Dict[str, Any]]],
                              options_data: Optional[pd.DataFrame] = None,
                              volume_profile: Optional[Dict[str, Any]] = None,
                              gex_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Predict price reaction at liquidity levels.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data

        Returns:
            Dictionary with price reaction predictions
        """
        start_time = time.time()

        # Ensure engine is initialized
        if not self.initialized:
            self.initialize()

        # Extract features
        features = self.feature_extractor.extract_features(
            price_data=price_data,
            options_data=options_data,
            volume_profile=volume_profile,
            gex_data=gex_data,
            liquidity_levels=liquidity_levels
        )

        if features.empty:
            logger.error("Failed to extract features for price reaction prediction")
            return {'reactions': {}, 'confidence': 0.0}

        # Get model
        model = self.models.get('price_reaction')
        if model is None:
            logger.error("Price reaction model not available")
            return {'reactions': {}, 'confidence': 0.0}

        # Make predictions
        try:
            # Get required features from model metadata if available
            required_features = []
            if hasattr(model, 'feature_names_in_'):
                required_features = model.feature_names_in_.tolist()
            elif hasattr(model, 'metadata') and isinstance(model.metadata, dict):
                required_features = model.metadata.get('features', [])

            # If we have required features, align the features
            if required_features:
                logger.info(f"Aligning {len(features.columns)} features with {len(required_features)} required features")
                aligned_features = align_features(features, required_features)
                logger.info(f"After alignment: {len(aligned_features.columns)} features")
            else:
                aligned_features = features

            # Check for NaN values in aligned_features
            if aligned_features.isna().any().any():
                nan_columns = aligned_features.columns[aligned_features.isna().any()].tolist()
                logger.warning(f"NaN values found in {len(nan_columns)} columns before prediction: {nan_columns}")

                # Use the robust feature imputer
                try:
                    from feature_imputer import FeatureImputer
                    logger.info("Using FeatureImputer to handle missing values")

                    # Create imputer with auto strategy
                    imputer = FeatureImputer(strategy='auto')

                    # Impute missing values
                    aligned_features = imputer.fit_transform(aligned_features)

                    logger.info("Successfully imputed missing values with FeatureImputer")
                except ImportError:
                    logger.warning("FeatureImputer not available, falling back to basic imputation")

                    # Basic imputation as fallback
                    aligned_features = aligned_features.fillna(0.0)
                    logger.info("Filled all NaN values with 0.0 using basic imputation")
                except Exception as e:
                    logger.error(f"Error using FeatureImputer: {str(e)}")
                    logger.warning("Falling back to basic imputation")

                    # Basic imputation as fallback
                    aligned_features = aligned_features.fillna(0.0)
                    logger.info("Filled all NaN values with 0.0 using basic imputation")

                # Verify all NaNs have been handled
                if aligned_features.isna().any().any():
                    remaining_nan_cols = aligned_features.columns[aligned_features.isna().any()].tolist()
                    logger.warning(f"Still have NaN values in columns after imputation: {remaining_nan_cols}")
                    # Last resort: fill any remaining NaNs with 0
                    aligned_features = aligned_features.fillna(0.0)
                    logger.info("Filled all remaining NaN values with 0.0")
                else:
                    logger.info("All NaN values successfully imputed before prediction")

            # Predict price reactions
            reaction_scores = model.predict(aligned_features)

            # Generate reaction predictions for each level
            reactions = self._generate_level_reactions(liquidity_levels, reaction_scores)

            # Calculate confidence
            confidence = self._calculate_confidence(features, reaction_scores)

            elapsed = time.time() - start_time
            logger.info(f"Price reaction prediction completed in {elapsed:.2f} seconds")

            return {
                'reactions': reactions,
                'confidence': confidence,
                'processing_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error in price reaction prediction: {str(e)}")
            return {'reactions': {}, 'confidence': 0.0}

    def _generate_level_reactions(self,
                                 liquidity_levels: Dict[str, List[Dict[str, Any]]],
                                 reaction_scores: np.ndarray) -> Dict[str, List[Dict[str, Any]]]:
        """
        Generate price reaction predictions for each liquidity level.

        Args:
            liquidity_levels: Dictionary with liquidity levels
            reaction_scores: Array of reaction scores

        Returns:
            Dictionary with price reaction predictions for each level
        """
        # Create a dictionary to store reactions
        reactions = {
            'support': [],
            'resistance': []
        }

        # Get average reaction score
        avg_score = float(np.mean(reaction_scores)) if len(reaction_scores) > 0 else 0

        # Generate reactions for support levels
        for level in liquidity_levels.get('support', []):
            price = level.get('price', 0)
            strength = level.get('strength', 0.5)

            # Calculate reaction probability
            # Higher strength = higher probability of bounce
            bounce_prob = min(0.9, strength * (1 + 0.2 * max(0, avg_score)))

            # Calculate expected price move
            # Base move is 0.5% of price, adjusted by strength and reaction score
            expected_move = price * 0.005 * strength * (1 + avg_score)

            # Create reaction prediction
            reaction = {
                'price': price,
                'type': 'support',
                'bounce_probability': bounce_prob,
                'break_probability': 1 - bounce_prob,
                'expected_move': expected_move,
                'reaction_score': avg_score,
                'confidence': strength * (0.7 + 0.3 * abs(avg_score))
            }

            reactions['support'].append(reaction)

        # Generate reactions for resistance levels
        for level in liquidity_levels.get('resistance', []):
            price = level.get('price', 0)
            strength = level.get('strength', 0.5)

            # Calculate reaction probability
            # Higher strength = higher probability of rejection
            rejection_prob = min(0.9, strength * (1 + 0.2 * max(0, -avg_score)))

            # Calculate expected price move
            # Base move is 0.5% of price, adjusted by strength and reaction score
            expected_move = price * 0.005 * strength * (1 - avg_score)

            # Create reaction prediction
            reaction = {
                'price': price,
                'type': 'resistance',
                'rejection_probability': rejection_prob,
                'break_probability': 1 - rejection_prob,
                'expected_move': expected_move,
                'reaction_score': avg_score,
                'confidence': strength * (0.7 + 0.3 * abs(avg_score))
            }

            reactions['resistance'].append(reaction)

        return reactions
