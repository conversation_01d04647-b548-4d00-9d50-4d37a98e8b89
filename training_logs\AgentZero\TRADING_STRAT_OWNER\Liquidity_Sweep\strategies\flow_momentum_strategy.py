"""
Flow Momentum Strategy

Trades based on institutional flow momentum using velocity, acceleration,
and divergence analysis.

NO PLACEHOLDERS. FULL IMPLEMENTATION. REAL DATA ONLY.
"""

import logging
from typing import Dict, List, Optional, Any
import numpy as np
import pandas as pd
import sys
import os
from pathlib import Path

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

# Import handling for both direct execution and module execution
from strategies.base_strategy import BaseStrategy, StrategySignal, SignalDirection

logger = logging.getLogger(__name__)


class FlowMomentumStrategy(BaseStrategy):
    """
    Strategy for trading flow momentum patterns.

    Identifies when:
    1. Flow velocity is accelerating in one direction
    2. Volume confirms the flow direction
    3. Price and flow are aligned (no divergence)
    4. Institutional patterns detected in flow
    """

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for flow momentum strategy."""
        return {
            'enabled': True,
            'min_confidence': 0.75,
            'max_signals_per_ticker': 3,

            # Flow parameters
            'min_flow_velocity': 0.5,        # Minimum normalized flow velocity
            'min_flow_acceleration': 0.2,    # Minimum flow acceleration
            'flow_lookback': 20,             # Bars to analyze flow
            'institutional_threshold': 0.7,   # Threshold for institutional flow

            # Volume parameters
            'min_volume_ratio': 1.5,         # Volume vs average
            'min_volume_surge': 2.0,         # Volume surge threshold
            'volume_confirmation': True,      # Require volume confirmation

            # Divergence parameters
            'max_divergence': 0.3,           # Maximum allowed divergence
            'divergence_lookback': 10,       # Bars for divergence check

            # Momentum parameters
            'momentum_period': 10,           # Period for momentum calculation
            'min_momentum_score': 0.6,       # Minimum momentum score

            # Risk parameters
            'stop_loss_atr': 2.5,
            'default_stop_percent': 0.025,
            'confidence_weights': {
                'flow_velocity': 0.3,
                'flow_acceleration': 0.2,
                'volume_confirmation': 0.2,
                'momentum_alignment': 0.2,
                'institutional_pattern': 0.1
            }
        }

    def analyze(self,
               ticker: str,
               data: Dict[str, Any],
               analysis_results: Dict[str, Any]) -> List[StrategySignal]:
        """
        Analyze for flow momentum opportunities.

        Args:
            ticker: Stock ticker
            data: Market data
            analysis_results: Results from analyzers

        Returns:
            List of flow momentum signals
        """
        # Validate data
        is_valid, error_msg = self.validate_data(data)
        if not is_valid:
            logger.warning(f"Invalid data for {ticker}: {error_msg}")
            return []

        signals = []

        # Get analysis components
        flow_analysis = analysis_results.get('flow_analysis', {})
        volume_analysis = analysis_results.get('volume_analysis', {})
        divergence_analysis = analysis_results.get('divergence_analysis', {})

        # Check multiple timeframes
        timeframes = ['1d', '4h', '1h']

        for timeframe in timeframes:
            # Get timeframe-specific data
            tf_price_data = data.get('price_data', {}).get(timeframe)
            if tf_price_data is None or tf_price_data.empty:
                continue

            tf_flow = flow_analysis.get(timeframe, {})
            tf_volume = volume_analysis.get(timeframe, {})

            # Analyze bullish flow momentum
            bullish_signal = self._analyze_bullish_flow(
                ticker, data, tf_price_data, tf_flow, tf_volume,
                divergence_analysis, timeframe
            )

            if bullish_signal:
                signals.append(bullish_signal)

            # Analyze bearish flow momentum
            bearish_signal = self._analyze_bearish_flow(
                ticker, data, tf_price_data, tf_flow, tf_volume,
                divergence_analysis, timeframe
            )

            if bearish_signal:
                signals.append(bearish_signal)

        # Filter and return best signals
        return self.filter_signals(signals)

    def _analyze_bullish_flow(self,
                            ticker: str,
                            data: Dict[str, Any],
                            price_data: pd.DataFrame,
                            flow_analysis: Dict[str, Any],
                            volume_analysis: Dict[str, Any],
                            divergence_analysis: Dict[str, Any],
                            timeframe: str) -> Optional[StrategySignal]:
        """Analyze for bullish flow momentum."""
        current_price = data['current_price']

        # Factor 1: Flow velocity and acceleration
        flow_velocity = flow_analysis.get('flow_velocity', 0)
        flow_acceleration = flow_analysis.get('flow_acceleration', 0)

        if flow_velocity < self.config['min_flow_velocity']:
            return None

        if flow_acceleration < self.config['min_flow_acceleration']:
            return None

        # Calculate flow score
        velocity_score = min(1.0, flow_velocity / (self.config['min_flow_velocity'] * 2))
        acceleration_score = min(1.0, flow_acceleration / (self.config['min_flow_acceleration'] * 2))

        # Factor 2: Volume confirmation
        volume_ratio = volume_analysis.get('volume_ratio', 1.0)
        volume_trend = volume_analysis.get('trend', 'neutral')

        if self.config['volume_confirmation']:
            if volume_ratio < self.config['min_volume_ratio']:
                return None

            if volume_trend != 'increasing':
                volume_score = 0.5
            else:
                volume_score = min(1.0, volume_ratio / self.config['min_volume_surge'])
        else:
            volume_score = 0.7  # Default if not required

        # Factor 3: Check for divergence
        price_flow_divergence = divergence_analysis.get('price_flow_divergence', 0)

        if abs(price_flow_divergence) > self.config['max_divergence']:
            return None  # Too much divergence

        alignment_score = 1.0 - (abs(price_flow_divergence) / self.config['max_divergence'])

        # Factor 4: Price momentum (using shared base method)
        momentum = self.calculate_price_momentum(price_data, self.config['momentum_period'])
        if momentum < 0:
            return None  # Need positive momentum for bullish signal

        momentum_score = min(1.0, momentum * 10)  # Scale momentum

        # Factor 5: Institutional pattern
        institutional_score = self._detect_institutional_pattern(
            flow_analysis, volume_analysis, 'bullish'
        )

        # Calculate overall confidence
        confidence_factors = {
            'flow_velocity': velocity_score,
            'flow_acceleration': acceleration_score,
            'volume_confirmation': volume_score,
            'momentum_alignment': alignment_score * momentum_score,
            'institutional_pattern': institutional_score
        }

        confidence = self.calculate_confidence(confidence_factors)

        if confidence < self.min_confidence:
            return None

        # Calculate entry and risk levels
        entry = current_price

        # Dynamic stop based on flow characteristics (using shared base method)
        atr = self.calculate_atr(price_data)
        flow_volatility = flow_analysis.get('flow_volatility', 1.0)
        adjusted_atr = atr * (1 + flow_volatility * 0.5)  # Adjust for flow volatility

        stop_loss = self.calculate_stop_loss(
            entry,
            SignalDirection.LONG,
            atr=adjusted_atr
        )

        # Targets based on flow projection
        flow_projection = flow_analysis.get('flow_projection', {})
        projected_targets = flow_projection.get('targets', [])

        if projected_targets:
            take_profit = [min(pt, entry * 1.10) for pt in projected_targets[:3]]
        else:
            take_profit = self.calculate_take_profit(
                entry, stop_loss, SignalDirection.LONG
            )

        # Create signal
        reason = f"Bullish flow momentum: Velocity {flow_velocity:.2f}, " \
                f"Acceleration {flow_acceleration:.2f}, Volume {volume_ratio:.1f}x"

        analysis = {
            'timeframe': timeframe,
            'flow_velocity': flow_velocity,
            'flow_acceleration': flow_acceleration,
            'volume_ratio': volume_ratio,
            'divergence': price_flow_divergence,
            'momentum': momentum,
            'institutional_score': institutional_score,
            'confidence_factors': confidence_factors
        }

        return self.create_signal(
            ticker=ticker,
            direction=SignalDirection.LONG,
            entry=entry,
            stop_loss=stop_loss,
            take_profit=take_profit,
            confidence=confidence,
            reason=reason,
            analysis=analysis,
            timeframe=timeframe
        )

    def _analyze_bearish_flow(self,
                            ticker: str,
                            data: Dict[str, Any],
                            price_data: pd.DataFrame,
                            flow_analysis: Dict[str, Any],
                            volume_analysis: Dict[str, Any],
                            divergence_analysis: Dict[str, Any],
                            timeframe: str) -> Optional[StrategySignal]:
        """Analyze for bearish flow momentum."""
        current_price = data['current_price']

        # Factor 1: Negative flow velocity and acceleration
        flow_velocity = flow_analysis.get('flow_velocity', 0)
        flow_acceleration = flow_analysis.get('flow_acceleration', 0)

        if flow_velocity > -self.config['min_flow_velocity']:
            return None

        if flow_acceleration > -self.config['min_flow_acceleration']:
            return None

        # Calculate flow score (using absolute values)
        velocity_score = min(1.0, abs(flow_velocity) / (self.config['min_flow_velocity'] * 2))
        acceleration_score = min(1.0, abs(flow_acceleration) / (self.config['min_flow_acceleration'] * 2))

        # Factor 2: Volume confirmation
        volume_ratio = volume_analysis.get('volume_ratio', 1.0)
        selling_pressure = volume_analysis.get('selling_pressure', 0.5)

        if self.config['volume_confirmation']:
            if volume_ratio < self.config['min_volume_ratio']:
                return None

            volume_score = min(1.0, volume_ratio / self.config['min_volume_surge']) * selling_pressure
        else:
            volume_score = 0.7

        # Factor 3: Check for divergence
        price_flow_divergence = divergence_analysis.get('price_flow_divergence', 0)

        if abs(price_flow_divergence) > self.config['max_divergence']:
            return None

        alignment_score = 1.0 - (abs(price_flow_divergence) / self.config['max_divergence'])

        # Factor 4: Negative price momentum (using shared base method)
        momentum = self.calculate_price_momentum(price_data, self.config['momentum_period'])
        if momentum > 0:
            return None

        momentum_score = min(1.0, abs(momentum) * 10)

        # Factor 5: Institutional distribution pattern
        institutional_score = self._detect_institutional_pattern(
            flow_analysis, volume_analysis, 'bearish'
        )

        # Calculate confidence
        confidence_factors = {
            'flow_velocity': velocity_score,
            'flow_acceleration': acceleration_score,
            'volume_confirmation': volume_score,
            'momentum_alignment': alignment_score * momentum_score,
            'institutional_pattern': institutional_score
        }

        confidence = self.calculate_confidence(confidence_factors)

        if confidence < self.min_confidence:
            return None

        # Entry and risk management
        entry = current_price

        atr = self.calculate_atr(price_data)
        flow_volatility = flow_analysis.get('flow_volatility', 1.0)
        adjusted_atr = atr * (1 + flow_volatility * 0.5)

        stop_loss = self.calculate_stop_loss(
            entry,
            SignalDirection.SHORT,
            atr=adjusted_atr
        )

        # Targets
        flow_projection = flow_analysis.get('flow_projection', {})
        projected_targets = flow_projection.get('targets', [])

        if projected_targets:
            take_profit = [max(pt, entry * 0.90) for pt in projected_targets[:3]]
        else:
            take_profit = self.calculate_take_profit(
                entry, stop_loss, SignalDirection.SHORT
            )

        reason = f"Bearish flow momentum: Velocity {flow_velocity:.2f}, " \
                f"Acceleration {flow_acceleration:.2f}, Selling pressure {selling_pressure:.2f}"

        analysis = {
            'timeframe': timeframe,
            'flow_velocity': flow_velocity,
            'flow_acceleration': flow_acceleration,
            'volume_ratio': volume_ratio,
            'selling_pressure': selling_pressure,
            'divergence': price_flow_divergence,
            'momentum': momentum,
            'institutional_score': institutional_score,
            'confidence_factors': confidence_factors
        }

        return self.create_signal(
            ticker=ticker,
            direction=SignalDirection.SHORT,
            entry=entry,
            stop_loss=stop_loss,
            take_profit=take_profit,
            confidence=confidence,
            reason=reason,
            analysis=analysis,
            timeframe=timeframe
        )

    # Note: _calculate_price_momentum and _calculate_atr methods moved to BaseStrategy
    # as calculate_price_momentum() and calculate_atr() for shared use across all strategies

    def _detect_institutional_pattern(self,
                                    flow_analysis: Dict[str, Any],
                                    volume_analysis: Dict[str, Any],
                                    direction: str) -> float:
        """Detect institutional flow patterns using both flow and volume data."""
        score = 0.0

        # Check for block trades
        block_trades = flow_analysis.get('block_trades', 0)
        if block_trades > 0:
            score += 0.3

        # Check for sweep patterns
        sweep_count = flow_analysis.get('sweep_count', 0)
        if sweep_count > 2:
            score += 0.3

        # Volume confirmation of institutional activity
        volume_ratio = volume_analysis.get('volume_ratio', 1.0)
        if volume_ratio > 2.0:  # High volume suggests institutional participation
            score += 0.2

        # Check for accumulation/distribution patterns
        if direction == 'bullish':
            accumulation_score = flow_analysis.get('accumulation_score', 0)
            score += accumulation_score * 0.3
            # Volume profile supporting accumulation
            if volume_analysis.get('buying_pressure', 0.5) > 0.6:
                score += 0.1
        else:
            distribution_score = flow_analysis.get('distribution_score', 0)
            score += distribution_score * 0.3
            # Volume profile supporting distribution
            if volume_analysis.get('selling_pressure', 0.5) > 0.6:
                score += 0.1

        return min(1.0, score)


if __name__ == "__main__":
    # Demo/test execution
    print("Flow Momentum Strategy - Class Definition Loaded Successfully")
    print("This strategy analyzes institutional flow momentum using:")
    print("- Flow velocity and acceleration")
    print("- Volume confirmation")
    print("- Price-flow divergence analysis")
    print("- Momentum alignment")
    print("- Institutional pattern detection")

    # Create an instance to verify the class works
    try:
        strategy = FlowMomentumStrategy()
        print(f"Strategy initialized with confidence threshold: {strategy.min_confidence}")
        print("Strategy configuration loaded successfully")
    except Exception as e:
        print(f"Error initializing strategy: {e}")
        import traceback
        traceback.print_exc()