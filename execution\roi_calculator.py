#!/usr/bin/env python3
"""
Core ROI Calculator Module
Comprehensive ROI calculations for stocks and options trading
"""

import numpy as np
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timedelta
import math

@dataclass
class ROIResult:
    """Result from ROI calculation"""
    roi_percentage: float
    absolute_return: float
    investment_amount: float
    risk_amount: float
    risk_reward_ratio: float
    time_horizon_days: int
    annualized_roi: float
    confidence: float

@dataclass
class OptionsROIResult(ROIResult):
    """Extended ROI result for options trading"""
    premium_cost: float
    intrinsic_value: float
    time_value: float
    max_profit: float
    max_loss: float
    breakeven_price: float
    theta_decay_impact: float
    probability_of_profit: float

class ROICalculator:
    """
    Mathematical ROI calculations for trading system
    Supports stocks, options, and complex strategies
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.min_roi_threshold = self.config.get('min_roi_threshold', 15.0)  # 15% minimum
        self.options_min_roi = self.config.get('options_min_roi', 25.0)     # 25% for options
        self.annualization_days = self.config.get('annualization_days', 365)
        
    def calculate_stock_roi(self, 
                           entry_price: float,
                           target_price: float,
                           stop_loss: float,
                           position_size: float,
                           holding_period_days: int = 30) -> ROIResult:
        """
        Calculate ROI for stock positions
        ROI = (Gain - Investment Cost) / Investment Cost * 100
        """
        
        investment_amount = entry_price * position_size
        risk_amount = abs(entry_price - stop_loss) * position_size
        
        # Calculate potential return
        if target_price > entry_price:  # Long position
            absolute_return = (target_price - entry_price) * position_size
        else:  # Short position
            absolute_return = (entry_price - target_price) * position_size
        
        # ROI calculation
        roi_percentage = (absolute_return / investment_amount) * 100
        
        # Risk-reward ratio
        potential_reward = abs(target_price - entry_price) * position_size
        risk_reward_ratio = potential_reward / risk_amount if risk_amount > 0 else 0
        
        # Annualized ROI
        if holding_period_days > 0:
            annualized_roi = ((1 + roi_percentage/100) ** (self.annualization_days/holding_period_days) - 1) * 100
        else:
            annualized_roi = roi_percentage
        
        # Confidence based on risk-reward ratio
        confidence = min(0.95, max(0.1, risk_reward_ratio / 3.0))
        
        return ROIResult(
            roi_percentage=roi_percentage,
            absolute_return=absolute_return,
            investment_amount=investment_amount,
            risk_amount=risk_amount,
            risk_reward_ratio=risk_reward_ratio,
            time_horizon_days=holding_period_days,
            annualized_roi=annualized_roi,
            confidence=confidence
        )
    
    def calculate_options_roi(self,
                             option_premium: float,
                             target_premium: float,
                             stop_premium: float,
                             contracts: int,
                             underlying_price: float,
                             strike_price: float,
                             days_to_expiry: int,
                             option_type: str = 'call',
                             theta_daily: float = 0.0) -> OptionsROIResult:
        """
        Calculate ROI for options positions
        Premium-based ROI calculation with Greeks consideration
        """
        
        # Contract multiplier (typically 100 for equity options)
        multiplier = 100
        
        # Investment amounts
        premium_cost = option_premium * contracts * multiplier
        investment_amount = premium_cost
        
        # Calculate potential returns
        target_value = target_premium * contracts * multiplier
        stop_value = stop_premium * contracts * multiplier
        
        absolute_return = target_value - premium_cost
        risk_amount = premium_cost - stop_value
        
        # ROI calculation
        roi_percentage = (absolute_return / premium_cost) * 100
        
        # Risk-reward ratio
        potential_reward = abs(target_premium - option_premium) * contracts * multiplier
        risk_reward_ratio = potential_reward / abs(risk_amount) if risk_amount != 0 else 0
        
        # Calculate intrinsic and time value
        if option_type.lower() == 'call':
            intrinsic_value = max(0, underlying_price - strike_price)
            breakeven_price = strike_price + option_premium
        else:  # put
            intrinsic_value = max(0, strike_price - underlying_price)
            breakeven_price = strike_price - option_premium
        
        time_value = option_premium - intrinsic_value
        
        # Time decay impact
        theta_decay_impact = theta_daily * days_to_expiry
        
        # Maximum profit and loss
        if option_type.lower() == 'call':
            max_profit = float('inf')  # Theoretically unlimited for calls
            max_loss = premium_cost
        else:  # put
            max_profit = (strike_price - option_premium) * contracts * multiplier
            max_loss = premium_cost
        
        # Probability of profit (simplified Black-Scholes approximation)
        probability_of_profit = self._estimate_probability_of_profit(
            underlying_price, breakeven_price, days_to_expiry, option_type
        )
        
        # Annualized ROI
        if days_to_expiry > 0:
            annualized_roi = ((1 + roi_percentage/100) ** (self.annualization_days/days_to_expiry) - 1) * 100
        else:
            annualized_roi = roi_percentage
        
        # Confidence based on probability of profit and time remaining
        time_factor = max(0.1, min(1.0, days_to_expiry / 30))  # 30 days = full confidence
        confidence = probability_of_profit * time_factor
        
        return OptionsROIResult(
            roi_percentage=roi_percentage,
            absolute_return=absolute_return,
            investment_amount=investment_amount,
            risk_amount=abs(risk_amount),
            risk_reward_ratio=risk_reward_ratio,
            time_horizon_days=days_to_expiry,
            annualized_roi=annualized_roi,
            confidence=confidence,
            premium_cost=premium_cost,
            intrinsic_value=intrinsic_value,
            time_value=time_value,
            max_profit=max_profit,
            max_loss=max_loss,
            breakeven_price=breakeven_price,
            theta_decay_impact=theta_decay_impact,
            probability_of_profit=probability_of_profit
        )
    
    def calculate_liquidity_pocket_roi(self,
                                      entry_price: float,
                                      stop_loss: float,
                                      liquidity_targets: List[Dict],
                                      position_size: float) -> List[Dict]:
        """
        Calculate ROI for multiple liquidity pocket targets
        Based on the liquidity flow engine implementation
        """
        
        risk_amount = abs(entry_price - stop_loss)
        if risk_amount == 0:
            return []
        
        roi_targets = []
        
        for i, target in enumerate(liquidity_targets):
            target_price = target['price']
            liquidity_strength = target.get('strength', 0.5)
            
            # Calculate reward and ROI
            reward = abs(target_price - entry_price)
            roi_percentage = (reward / risk_amount) * 100
            
            # Skip targets below minimum threshold
            if roi_percentage < self.min_roi_threshold:
                continue
            
            # Calculate absolute return
            absolute_return = reward * position_size
            investment_amount = entry_price * position_size
            
            # Risk-reward ratio
            risk_reward_ratio = reward / risk_amount
            
            # Position size factor based on liquidity strength
            position_size_factor = min(1.0, liquidity_strength * target.get('volume_score', 0.5))
            
            roi_targets.append({
                'target_number': i + 1,
                'price': round(target_price, 2),
                'roi_percentage': round(roi_percentage, 1),
                'absolute_return': round(absolute_return, 2),
                'investment_amount': round(investment_amount, 2),
                'risk_reward_ratio': round(risk_reward_ratio, 2),
                'liquidity_strength': round(liquidity_strength, 3),
                'position_size_factor': round(position_size_factor, 2),
                'pocket_type': target.get('pocket_classification', 'Unknown'),
                'confidence': round(target.get('combined_score', 0.5), 3)
            })
        
        return roi_targets
    
    def validate_roi_threshold(self, roi_result: Union[ROIResult, OptionsROIResult], 
                              trade_type: str = 'stock') -> bool:
        """
        Validate if ROI meets minimum thresholds
        """
        if trade_type == 'options':
            return roi_result.roi_percentage >= self.options_min_roi
        else:
            return roi_result.roi_percentage >= self.min_roi_threshold
    
    def compare_roi_opportunities(self, roi_results: List[Union[ROIResult, OptionsROIResult]]) -> Dict:
        """
        Compare multiple ROI opportunities and rank them
        """
        if not roi_results:
            return {}
        
        # Sort by risk-adjusted ROI (ROI * confidence)
        ranked_results = sorted(
            roi_results, 
            key=lambda x: x.roi_percentage * x.confidence, 
            reverse=True
        )
        
        best_roi = ranked_results[0]
        avg_roi = np.mean([r.roi_percentage for r in roi_results])
        
        return {
            'best_opportunity': {
                'roi_percentage': best_roi.roi_percentage,
                'risk_reward_ratio': best_roi.risk_reward_ratio,
                'confidence': best_roi.confidence,
                'time_horizon': best_roi.time_horizon_days
            },
            'average_roi': round(avg_roi, 2),
            'total_opportunities': len(roi_results),
            'above_threshold': len([r for r in roi_results if self.validate_roi_threshold(r)]),
            'ranked_results': ranked_results
        }
    
    def _estimate_probability_of_profit(self, current_price: float, breakeven: float, 
                                       days_to_expiry: int, option_type: str) -> float:
        """
        Estimate probability of profit using simplified model
        """
        try:
            # Simplified probability estimation
            # In reality, this would use Black-Scholes or other option pricing models
            
            price_distance = abs(breakeven - current_price) / current_price
            time_factor = max(0.1, days_to_expiry / 30)  # 30 days baseline
            
            # Basic probability estimation
            if option_type.lower() == 'call':
                if current_price >= breakeven:
                    base_prob = 0.7  # Already in the money
                else:
                    base_prob = max(0.2, 0.6 - price_distance * 2)
            else:  # put
                if current_price <= breakeven:
                    base_prob = 0.7  # Already in the money
                else:
                    base_prob = max(0.2, 0.6 - price_distance * 2)
            
            # Adjust for time
            probability = base_prob * time_factor
            return min(0.95, max(0.05, probability))
            
        except Exception:
            return 0.5  # Default probability

if __name__ == "__main__":
    # Test the ROI calculator
    calculator = ROICalculator()
    
    # Test stock ROI
    stock_roi = calculator.calculate_stock_roi(
        entry_price=100.0,
        target_price=120.0,
        stop_loss=95.0,
        position_size=100,
        holding_period_days=30
    )
    
    print("Stock ROI Results:")
    print(f"ROI: {stock_roi.roi_percentage:.1f}%")
    print(f"Risk-Reward: {stock_roi.risk_reward_ratio:.2f}:1")
    print(f"Annualized ROI: {stock_roi.annualized_roi:.1f}%")
    
    # Test options ROI
    options_roi = calculator.calculate_options_roi(
        option_premium=2.50,
        target_premium=5.00,
        stop_premium=1.00,
        contracts=10,
        underlying_price=105.0,
        strike_price=100.0,
        days_to_expiry=30,
        option_type='call'
    )
    
    print(f"\nOptions ROI Results:")
    print(f"ROI: {options_roi.roi_percentage:.1f}%")
    print(f"Premium Cost: ${options_roi.premium_cost:.2f}")
    print(f"Max Profit: ${options_roi.max_profit:.2f}")
    print(f"Probability of Profit: {options_roi.probability_of_profit:.1%}")
