@echo off
REM Multi-ticker CORE execution script
REM Sets environment and executes batch analysis

echo ========================================
echo CORE Multi-Ticker Pipeline Execution
echo ========================================

REM Set environment variables
set ACCOUNT_EQUITY=25000
set PYTHONPATH=%CD%

REM Validate required files exist
if not exist "orchestrator.py" (
    echo ERROR: orchestrator.py not found
    exit /b 1
)

if not exist "multi_orchestrator.py" (
    echo ERROR: multi_orchestrator.py not found
    exit /b 1
)

REM Execute multi-ticker batch
echo Executing multi-ticker analysis...
py multi_orchestrator.py ^
    --tickers AAPL,TSLA,NVDA ^
    --option_prices 1.50,2.10,3.25 ^
    --target_prices 5.00,6.00,10.40 ^
    --source mcp-http ^
    --account_equity %ACCOUNT_EQUITY%

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Multi-ticker execution completed
) else (
    echo FAILED: Multi-ticker execution failed with code %ERRORLEVEL%
)

echo ========================================
echo Execution finished. Check outputs/ directory for results.
echo ========================================
pause
