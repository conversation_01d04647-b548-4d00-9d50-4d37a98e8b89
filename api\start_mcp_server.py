#!/usr/bin/env python3
"""
MCP Server Launcher
Start the CORE MCP server with proper configuration
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path

def start_mcp_server():
    """Start the MCP server"""
    script_dir = Path(__file__).parent
    server_script = script_dir / "mcp_server_production.py"
    pid_file = script_dir / "mcp_server.pid"
    log_file = script_dir / "mcp_server_output.log"
    
    if not server_script.exists():
        print(f"ERROR: Server script not found: {server_script}")
        return False
        
    print("Starting MCP Server...")
    
    try:
        # Start server process
        process = subprocess.Popen(
            [sys.executable, str(server_script)],
            stdout=open(log_file, 'w'),
            stderr=subprocess.STDOUT,
            cwd=script_dir
        )
        
        # Save PID
        with open(pid_file, 'w') as f:
            f.write(str(process.pid))
            
        print(f"MCP Server started with PID {process.pid}")
        print(f"Logs: {log_file}")
        
        return True
        
    except Exception as e:
        print(f"ERROR: Failed to start MCP server: {e}")
        return False

if __name__ == "__main__":
    start_mcp_server()
