#!/usr/bin/env python3
"""
HTTP Wrapper for MCP Server
Provides HTTP endpoints that communicate with your stdio MCP server
"""

import json
import subprocess
import asyncio
import logging
from flask import Flask, request, jsonify
from typing import Dict, Any, Optional
import threading
import queue
import time

class MCPHttpWrapper:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """HTTP wrapper for stdio MCP server communication"""
    
    def __init__(self, mcp_server_path: str = "mcp_server_production.py"):
        self.mcp_server_path = mcp_server_path
        self.process = None
        self.request_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.request_id_counter = 1
        
    def start_mcp_server(self, ticker: str = None):
        """Start the MCP server process"""
        try:
            self.process = subprocess.Popen(
                ["python", self.mcp_server_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=0
            )
            
            # Start communication threads
            threading.Thread(target=self._read_responses, daemon=True).start()
            threading.Thread(target=self._send_requests, daemon=True).start()
            
            return True
        except Exception as e:
            logging.error(f"Failed to start MCP server: {e}")
            return False
    
    def _send_requests(self, ticker: str = None):
        """Send requests to MCP server"""
        while self.process and self.process.poll() is None:
            try:
                request = self.request_queue.get(timeout=1)
                if request:
                    self.process.stdin.write(json.dumps(request) + '\n')
                    self.process.stdin.flush()
            except queue.Empty:
                continue
            except Exception as e:
                logging.error(f"Error sending request: {e}")
    
    def _read_responses(self, ticker: str = None):
        """Read responses from MCP server"""
        while self.process and self.process.poll() is None:
            try:
                line = self.process.stdout.readline()
                if line:
                    response = json.loads(line.strip())
                    self.response_queue.put(response)
            except Exception as e:
                logging.error(f"Error reading response: {e}")
    
    def send_mcp_request(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Send request to MCP server and wait for response"""
        request_id = self.request_id_counter
        self.request_id_counter += 1
        
        request = {
            "jsonrpc": "2.0",
            "id": request_id,
            "method": method,
            "params": params
        }
        
        # Send request
        self.request_queue.put(request)
        
        # Wait for response (with timeout)
        start_time = time.time()
        while time.time() - start_time < 10:  # 10 second timeout
            try:
                response = self.response_queue.get(timeout=0.1)
                if response.get("id") == request_id:
                    return response
                else:
                    # Put back if not our response
                    self.response_queue.put(response)
            except queue.Empty:
                continue
        
        return {"error": "Timeout waiting for MCP response"}

# Create Flask app
app = Flask(__name__)
mcp_wrapper = MCPHttpWrapper()

@app.route('/')
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "ok", "service": "MCP HTTP Wrapper"})

@app.route('/bars')
def get_bars():
    """Get bars data via MCP"""
    ticker = request.args.get('tk') or request.args.get('ticker')
    timeframe = request.args.get('tf') or request.args.get('timeframe', '1')
    
    if not ticker:
        return jsonify({"error": "Missing ticker parameter"}), 400
    
    # Send request to MCP server using the correct method
    response = mcp_wrapper.send_mcp_request("get_price_data", {
        "ticker": ticker,
        "timeframe": timeframe
    })
    
    if "error" in response:
        return jsonify(response), 500
    
    # Convert result to expected format
    result = response.get("result", [])
    if isinstance(result, dict) and "data" in result:
        data = result["data"]
    elif isinstance(result, list):
        data = result
    else:
        data = []
    
    return jsonify({
        "data": data
    })

@app.route('/options')
def get_options():
    """Get options data via MCP"""
    ticker = request.args.get('tk') or request.args.get('ticker')
    
    if not ticker:
        return jsonify({"error": "Missing ticker parameter"}), 400
    
    # Send request to MCP server
    response = mcp_wrapper.send_mcp_request("get_options_chain", {
        "ticker": ticker
    })
    
    if "error" in response:
        return jsonify(response), 500
    
    # Convert result to expected format
    result = response.get("result", [])
    if isinstance(result, dict) and "data" in result:
        data = result["data"]
    elif isinstance(result, list):
        data = result
    else:
        data = []
    
    return jsonify({
        "data": data
    })

@app.route('/api/bars')
def get_bars_api():
    """Alternative API endpoint for bars"""
    return get_bars()

@app.route('/api/options') 
def get_options_api():
    """Alternative API endpoint for options"""
    return get_options()

def start_http_wrapper(port: int = 8000):
    """Start the HTTP wrapper server"""
    print(f"Starting MCP HTTP Wrapper on port {port}")
    
    # Start MCP server
    if not mcp_wrapper.start_mcp_server():
        print("Failed to start MCP server")
        return
    
    print("MCP server started successfully")
    print(f"HTTP endpoints available at http://localhost:{port}")
    print("Endpoints:")
    print(f"  GET /bars?tk=AAPL&tf=1")
    print(f"  GET /options?tk=AAPL")
    print(f"  GET /api/bars?tk=AAPL&tf=1") 
    print(f"  GET /api/options?tk=AAPL")
    
    # Start Flask server
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="MCP HTTP Wrapper")
    parser.add_argument("--port", type=int, default=8000, help="HTTP server port")
    parser.add_argument("--mcp-server", default="mcp_server_production.py", help="MCP server script path")
    
    args = parser.parse_args()
    
    mcp_wrapper.mcp_server_path = args.mcp_server
    start_http_wrapper(args.port)
