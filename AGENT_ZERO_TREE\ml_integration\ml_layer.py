#!/usr/bin/env python3
"""
Agent Zero ML Integration Layer
Seamless integration with existing ML systems
"""

import sys
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class MLFeatures:
    """Feature set for ML prediction"""
    signal_confidence: float
    signal_strength: float
    execution_recommendation: str
    math_accuracy: float
    math_precision: float
    market_volatility: float
    trend_strength: float
    volume_profile: float
    
@dataclass
class MLPrediction:
    """ML system prediction result"""
    action: str
    confidence: float
    probability_scores: Dict[str, float]
    feature_importance: Dict[str, float]
    model_version: str
    prediction_time_ms: float

class MLIntegrationLayer:
    """
    ML Integration Layer for Agent Zero
    Provides seamless integration with existing ML infrastructure
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.ml_available = False
        self.ml_system = None
        
        # Initialize ML system
        self._initialize_ml_system()
        
        # Fallback configuration
        self.fallback_config = {
            'confidence_boost': 0.1,
            'accuracy_weight': 0.3,
            'signal_weight': 0.7
        }
    
    def _initialize_ml_system(self):
        """Initialize existing ML system if available"""
        try:
            # Check for existing ML system in CORE
            ml_path = Path(__file__).parent.parent.parent / "ml" / "ml"
            if ml_path.exists():
                sys.path.append(str(ml_path))
                
                # Try to import existing ML system
                from ml_system import get_ml_system
                self.ml_system = get_ml_system()
                self.ml_available = True
                logger.info("ML system integration successful")
                
        except ImportError as e:
            logger.info(f"ML system not available: {e}")
            self.ml_available = False
        except Exception as e:
            logger.warning(f"ML system initialization failed: {e}")
            self.ml_available = False
    
    def predict(self, features: MLFeatures) -> MLPrediction:
        """
        Generate ML prediction with fallback
        """
        if self.ml_available and self.ml_system:
            return self._ml_prediction(features)
        else:
            return self._fallback_prediction(features)
    
    def _ml_prediction(self, features: MLFeatures) -> MLPrediction:
        """Generate prediction using existing ML system"""
        try:
            # Prepare features for existing ML system
            feature_dict = {
                'signal_confidence': features.signal_confidence,
                'signal_strength': features.signal_strength,
                'execution_recommendation': features.execution_recommendation,
                'math_accuracy': features.math_accuracy,
                'math_precision': features.math_precision,
                'market_volatility': features.market_volatility,
                'trend_strength': features.trend_strength,
                'volume_profile': features.volume_profile
            }
            
            # Get ML prediction
            start_time = self._get_time_ms()
            ml_result = self.ml_system.predict(feature_dict)
            prediction_time = self._get_time_ms() - start_time
            
            # Convert ML result to standardized format
            return MLPrediction(
                action=ml_result.get('action', 'hold'),
                confidence=ml_result.get('confidence', 0.5),
                probability_scores=ml_result.get('probabilities', {}),
                feature_importance=ml_result.get('feature_importance', {}),
                model_version=ml_result.get('model_version', 'unknown'),
                prediction_time_ms=prediction_time
            )
            
        except Exception as e:
            logger.error(f"ML prediction failed: {e}")
            return self._fallback_prediction(features)
    
    def _fallback_prediction(self, features: MLFeatures) -> MLPrediction:
        """Fallback prediction when ML is unavailable"""
        
        # Simple rule-based prediction
        signal_score = (features.signal_confidence + features.signal_strength) / 2
        accuracy_bonus = features.math_accuracy * self.fallback_config['accuracy_weight']
        
        composite_score = (
            signal_score * self.fallback_config['signal_weight'] + 
            accuracy_bonus
        )
        
        # Determine action
        if composite_score >= 0.75:
            action = 'execute'
            confidence = min(composite_score + self.fallback_config['confidence_boost'], 1.0)
        elif composite_score >= 0.25:
            action = 'hold'
            confidence = composite_score
        else:
            action = 'avoid'
            confidence = max(1.0 - composite_score, 0.1)
        
        return MLPrediction(
            action=action,
            confidence=confidence,
            probability_scores={
                'execute': composite_score if action == 'execute' else 1.0 - composite_score,
                'hold': 0.5,
                'avoid': 1.0 - composite_score if action == 'avoid' else composite_score
            },
            feature_importance={
                'signal_confidence': 0.3,
                'signal_strength': 0.3,
                'math_accuracy': 0.2,
                'execution_recommendation': 0.2
            },
            model_version='fallback_v1.0',
            prediction_time_ms=0.1
        )
    
    def update_model(self, training_data: List[Dict]):
        """Update ML model with new training data"""
        if self.ml_available and self.ml_system:
            try:
                if hasattr(self.ml_system, 'update_model'):
                    self.ml_system.update_model(training_data)
                    logger.info(f"ML model updated with {len(training_data)} samples")
            except Exception as e:
                logger.error(f"ML model update failed: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get ML model information"""
        if self.ml_available and self.ml_system:
            try:
                if hasattr(self.ml_system, 'get_model_info'):
                    return self.ml_system.get_model_info()
            except Exception as e:
                logger.error(f"Failed to get model info: {e}")
        
        return {
            'model_type': 'fallback',
            'version': 'v1.0',
            'ml_available': self.ml_available,
            'features_supported': 8,
            'last_updated': 'N/A'
        }
    
    def _get_time_ms(self) -> float:
        """Get current time in milliseconds"""
        import time
        return time.time() * 1000

class MLFeatureExtractor:
    """Extract ML features from raw trading data"""
    
    @staticmethod
    def extract_features(signal_data: Dict, math_data: Dict, market_context: Dict) -> MLFeatures:
        """Extract standardized features for ML prediction"""
        
        return MLFeatures(
            signal_confidence=signal_data.get('confidence', 0.5),
            signal_strength=signal_data.get('strength', 0.5),
            execution_recommendation=signal_data.get('execution_recommendation', 'hold'),
            math_accuracy=math_data.get('accuracy_score', 0.5),
            math_precision=math_data.get('precision', 0.01),
            market_volatility=market_context.get('volatility', 0.02),
            trend_strength=market_context.get('trend_strength', 0.5),
            volume_profile=market_context.get('volume_profile', 1.0)
        )

if __name__ == "__main__":
    # Test ML integration
    ml_layer = MLIntegrationLayer()
    
    test_features = MLFeatures(
        signal_confidence=0.8,
        signal_strength=0.7,
        execution_recommendation='execute',
        math_accuracy=0.9,
        math_precision=0.001,
        market_volatility=0.02,
        trend_strength=0.6,
        volume_profile=1.2
    )
    
    prediction = ml_layer.predict(test_features)
    print(f"ML Available: {ml_layer.ml_available}")
    print(f"Action: {prediction.action}")
    print(f"Confidence: {prediction.confidence:.3f}")
    print(f"Model: {prediction.model_version}")
