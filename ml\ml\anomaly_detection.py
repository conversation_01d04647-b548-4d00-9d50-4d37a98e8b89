"""
Anomaly Detection Module

This module provides advanced anomaly detection capabilities for liquidity analysis,
implementing multiple detection algorithms and ensemble methods for robust detection.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import sys
import logging
import time
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
from enum import Enum
import matplotlib.pyplot as plt

# Try to import optional dependencies
try:
    from sklearn.ensemble import IsolationForest
    from sklearn.neighbors import LocalOutlierFactor
    from sklearn.svm import OneClassSVM
    from sklearn.covariance import EllipticEnvelope
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torch.utils.data import DataLoader, TensorDataset
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

# Internal imports
from ml_logging import get_logger

# Setup logger
logger = get_logger('anomaly_detection')

class AnomalyType(Enum):
    """Types of anomalies in liquidity data."""
    PRICE_SPIKE = "price_spike"
    VOLUME_SPIKE = "volume_spike"
    LIQUIDITY_GAP = "liquidity_gap"
    UNUSUAL_OPTIONS = "unusual_options"
    CONFLICTING_SIGNALS = "conflicting_signals"
    PATTERN_BREAK = "pattern_break"
    REGIME_CHANGE = "regime_change"
    CUSTOM = "custom"

class AnomalySeverity(Enum):
    """Severity levels for anomalies."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AnomalyDetector:
    """
    Advanced anomaly detector for liquidity analysis.
    
    This class provides methods for detecting anomalies in liquidity data
    using multiple detection algorithms and ensemble methods.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the anomaly detector.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Set default configuration
        self.detection_methods = self.config.get('detection_methods', [
            'statistical', 'isolation_forest', 'local_outlier_factor'
        ])
        
        self.ensemble_method = self.config.get('ensemble_method', 'majority_vote')
        self.threshold = self.config.get('threshold', 0.7)
        self.contamination = self.config.get('contamination', 0.05)
        self.n_estimators = self.config.get('n_estimators', 100)
        self.random_state = self.config.get('random_state', 42)
        
        # Initialize models
        self.models = {}
        self.initialized = False
        
        # Performance tracking
        self.performance_data = {}
        
        logger.info(f"Initialized AnomalyDetector with {len(self.detection_methods)} detection methods")
    
    def initialize(self) -> bool:
        """
        Initialize anomaly detection models.
        
        Returns:
            True if initialization was successful
        """
        if not HAS_SKLEARN and 'statistical' not in self.detection_methods:
            logger.error("scikit-learn is required for most anomaly detection methods")
            return False
        
        try:
            # Initialize statistical detection
            if 'statistical' in self.detection_methods:
                self.models['statistical'] = {
                    'model': None,  # No model needed for statistical detection
                    'threshold': self.config.get('statistical_threshold', 3.0),
                    'window_size': self.config.get('statistical_window', 20)
                }
            
            # Initialize Isolation Forest
            if 'isolation_forest' in self.detection_methods and HAS_SKLEARN:
                self.models['isolation_forest'] = {
                    'model': IsolationForest(
                        n_estimators=self.n_estimators,
                        contamination=self.contamination,
                        random_state=self.random_state
                    ),
                    'trained': False
                }
            
            # Initialize Local Outlier Factor
            if 'local_outlier_factor' in self.detection_methods and HAS_SKLEARN:
                self.models['local_outlier_factor'] = {
                    'model': LocalOutlierFactor(
                        n_neighbors=20,
                        contamination=self.contamination
                    ),
                    'trained': False
                }
            
            # Initialize One-Class SVM
            if 'one_class_svm' in self.detection_methods and HAS_SKLEARN:
                self.models['one_class_svm'] = {
                    'model': OneClassSVM(
                        nu=self.contamination,
                        kernel='rbf',
                        gamma='scale'
                    ),
                    'trained': False
                }
            
            # Initialize Robust Covariance
            if 'robust_covariance' in self.detection_methods and HAS_SKLEARN:
                self.models['robust_covariance'] = {
                    'model': EllipticEnvelope(
                        contamination=self.contamination,
                        random_state=self.random_state
                    ),
                    'trained': False
                }
            
            # Initialize Autoencoder if PyTorch is available
            if 'autoencoder' in self.detection_methods and HAS_TORCH:
                self.models['autoencoder'] = {
                    'model': self._create_autoencoder(),
                    'trained': False,
                    'threshold': None  # Will be set during training
                }
            
            self.initialized = True
            logger.info(f"Initialized {len(self.models)} anomaly detection models")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing anomaly detection models: {str(e)}")
            return False
    
    def _create_autoencoder(self) -> nn.Module:
        """
        Create autoencoder model for anomaly detection.
        
        Returns:
            Autoencoder model
        """
        if not HAS_TORCH:
            logger.error("PyTorch is required for autoencoder")
            return None
        
        # Define autoencoder architecture
        class Autoencoder(nn.Module):
            def __init__(self, input_dim: int, hidden_dims: List[int]):
                super().__init__()
                
                # Encoder layers
                encoder_layers = []
                prev_dim = input_dim
                
                for hidden_dim in hidden_dims:
                    encoder_layers.append(nn.Linear(prev_dim, hidden_dim))
                    encoder_layers.append(nn.ReLU())
                    prev_dim = hidden_dim
                
                self.encoder = nn.Sequential(*encoder_layers)
                
                # Decoder layers
                decoder_layers = []
                hidden_dims.reverse()
                
                for i, hidden_dim in enumerate(hidden_dims[1:]):
                    decoder_layers.append(nn.Linear(hidden_dims[i], hidden_dim))
                    decoder_layers.append(nn.ReLU())
                
                decoder_layers.append(nn.Linear(hidden_dims[-1], input_dim))
                
                self.decoder = nn.Sequential(*decoder_layers)
            
            def forward(self, x):
                encoded = self.encoder(x)
                decoded = self.decoder(encoded)
                return decoded
        
        # Create model with default architecture
        input_dim = self.config.get('autoencoder_input_dim', 10)
        hidden_dims = self.config.get('autoencoder_hidden_dims', [8, 4, 2])
        
        return Autoencoder(input_dim, hidden_dims)
    
    def train(self, 
             features: pd.DataFrame,
             contamination: Optional[float] = None) -> Dict[str, Any]:
        """
        Train anomaly detection models.
        
        Args:
            features: Feature DataFrame
            contamination: Optional contamination parameter
            
        Returns:
            Dictionary with training results
        """
        if not self.initialized:
            self.initialize()
        
        if contamination is not None:
            self.contamination = contamination
        
        start_time = time.time()
        results = {}
        
        try:
            # Train each model
            for method, model_info in self.models.items():
                if method == 'statistical':
                    # No training needed for statistical method
                    continue
                
                model = model_info['model']
                
                if method == 'autoencoder':
                    if HAS_TORCH:
                        results[method] = self._train_autoencoder(features)
                else:
                    # Train scikit-learn models
                    if hasattr(model, 'fit'):
                        model.fit(features)
                        model_info['trained'] = True
                        results[method] = {'success': True}
                    else:
                        logger.warning(f"Model {method} does not have fit method")
                        results[method] = {'success': False, 'error': 'No fit method'}
            
            elapsed = time.time() - start_time
            logger.info(f"Trained anomaly detection models in {elapsed:.2f} seconds")
            
            return {
                'success': True,
                'elapsed_time': elapsed,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"Error training anomaly detection models: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _train_autoencoder(self, features: pd.DataFrame) -> Dict[str, Any]:
        """
        Train autoencoder model.
        
        Args:
            features: Feature DataFrame
            
        Returns:
            Dictionary with training results
        """
        if not HAS_TORCH:
            return {'success': False, 'error': 'PyTorch not available'}
        
        try:
            # Convert to tensor
            X = torch.tensor(features.values, dtype=torch.float32)
            
            # Create data loader
            dataset = TensorDataset(X, X)
            batch_size = min(64, len(X))
            data_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
            
            # Get model
            model = self.models['autoencoder']['model']
            
            # Training parameters
            epochs = self.config.get('autoencoder_epochs', 100)
            learning_rate = self.config.get('autoencoder_lr', 0.001)
            
            # Optimizer
            optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)
            
            # Train model
            model.train()
            losses = []
            
            for epoch in range(epochs):
                epoch_loss = 0.0
                
                for batch_X, _ in data_loader:
                    # Forward pass
                    outputs = model(batch_X)
                    loss = F.mse_loss(outputs, batch_X)
                    
                    # Backward pass and optimize
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()
                    
                    epoch_loss += loss.item()
                
                avg_loss = epoch_loss / len(data_loader)
                losses.append(avg_loss)
                
                if (epoch + 1) % 10 == 0:
                    logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {avg_loss:.6f}")
            
            # Calculate reconstruction errors for threshold
            model.eval()
            reconstruction_errors = []
            
            with torch.no_grad():
                for batch_X, _ in data_loader:
                    outputs = model(batch_X)
                    batch_errors = torch.mean((outputs - batch_X) ** 2, dim=1)
                    reconstruction_errors.extend(batch_errors.tolist())
            
            # Set threshold as percentile of reconstruction errors
            threshold_percentile = 100 * (1 - self.contamination)
            threshold = np.percentile(reconstruction_errors, threshold_percentile)
            
            # Update model info
            self.models['autoencoder']['trained'] = True
            self.models['autoencoder']['threshold'] = threshold
            
            return {
                'success': True,
                'final_loss': losses[-1],
                'threshold': threshold
            }
            
        except Exception as e:
            logger.error(f"Error training autoencoder: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
