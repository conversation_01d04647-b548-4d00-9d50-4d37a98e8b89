#!/usr/bin/env python3
"""
API Cache Module - Mathematical LRU implementation
Statistical cache performance analysis
Zero data corruption tolerance
"""

import time
import asyncio
import hashlib
import pickle
from typing import Dict, Any, Optional, Tuple
from collections import OrderedDict
from dataclasses import dataclass
import logging

@dataclass
class CacheEntry:
    """Cache entry with mathematical validation."""
    key: str
    value: Any
    timestamp: float
    ttl: float
    access_count: int = 0
    
    def is_expired(self) -> bool:
        """Check if entry is expired with precision."""
        return time.time() > (self.timestamp + self.ttl)
    
    def get_age(self) -> float:
        """Get entry age in seconds."""
        return time.time() - self.timestamp

class ApiCache:
    """Production API cache with mathematical precision."""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 300.0):
        """Initialize cache with mathematical constraints."""
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache = OrderedDict()
        self._lock = asyncio.Lock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'errors': 0
        }
        self.logger = logging.getLogger(__name__)
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate deterministic cache key."""
        key_data = str(args) + str(sorted(kwargs.items()))
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def get(self, key: str) -> Tuple[bool, Optional[Any]]:
        """Retrieve value from cache."""
        async with self._lock:
            if key in self._cache:
                entry = self._cache[key]
                
                if entry.is_expired():
                    del self._cache[key]
                    self.stats['misses'] += 1
                    return False, None
                
                # Move to end (LRU)
                self._cache.move_to_end(key)
                entry.access_count += 1
                self.stats['hits'] += 1
                return True, entry.value
            
            self.stats['misses'] += 1
            return False, None
    
    async def put(self, key: str, value: Any, ttl: Optional[float] = None) -> bool:
        """Store value in cache."""
        async with self._lock:
            try:
                ttl = ttl or self.default_ttl
                entry = CacheEntry(
                    key=key,
                    value=value,
                    timestamp=time.time(),
                    ttl=ttl
                )
                
                # Remove existing entry if present
                if key in self._cache:
                    del self._cache[key]
                
                # Evict if at capacity
                while len(self._cache) >= self.max_size:
                    oldest_key = next(iter(self._cache))
                    del self._cache[oldest_key]
                    self.stats['evictions'] += 1
                
                self._cache[key] = entry
                return True
                
            except Exception as e:
                self.logger.error(f"Cache put error: {e}")
                self.stats['errors'] += 1
                return False
    
    async def cached_call(self, func, *args, ttl: Optional[float] = None, **kwargs):
        """Execute function with caching."""
        cache_key = self._generate_key(func.__name__, *args, **kwargs)
        
        # Try cache first
        hit, value = await self.get(cache_key)
        if hit:
            return value
        
        # Execute function
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # Store in cache
            await self.put(cache_key, result, ttl)
            return result
            
        except Exception as e:
            self.logger.error(f"Cached call error: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_ratio = self.stats['hits'] / total_requests if total_requests > 0 else 0
        
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'utilization': len(self._cache) / self.max_size,
            'hit_ratio': hit_ratio,
            'statistics': self.stats.copy()
        }
    
    async def clear_expired(self) -> int:
        """Clear expired entries and return count."""
        async with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
            
            return len(expired_keys)
