#!/usr/bin/env python3
"""
AGENT ZERO ENHANCED OPTIONS INTEGRATION - FINAL PHASE
Complete integration of Enhanced Options Agent with Agent Zero core system
"""

import sys
import json
import requests
from pathlib import Path
from typing import Dict, Any, Optional
from datetime import datetime
import logging

# Add CORE to path
sys.path.append(str(Path(__file__).parent.parent))

# Import Agent Zero core
from agents.agent_zero import AgentZeroAdvisor

# Import enhanced options agent
from enhanced_agent_zero_options_agent import (
    EnhancedAgentZeroOptionsAgent, 
    create_enhanced_options_task
)

logger = logging.getLogger(__name__)

class AgentZeroWithEnhancedOptions(AgentZeroAdvisor):
    """
    Agent Zero with complete options intelligence integration
    
    Capabilities:
    - Original 50% liquidity weighting (empirically validated)
    - Complete Greek Engine integration
    - Sophisticated options analysis and selection
    - Risk-aware position sizing
    - Comprehensive exit strategies
    - Real-time options data integration
    """
    
    def __init__(self):
        super().__init__()
        
        # Initialize enhanced options agent
        self.enhanced_options_agent = EnhancedAgentZeroOptionsAgent()
        
        # MCP server configuration for options data
        self.mcp_url = "http://localhost:8005"
        
        logger.info("Agent Zero with Enhanced Options Intelligence initialized")
    
    def get_complete_trading_decision(self, signal_data: Dict, math_data: Dict, 
                                    market_context: Dict, ticker: str,
                                    fetch_options_data: bool = True) -> Dict[str, Any]:
        """
        Complete trading decision with options intelligence
        
        Args:
            signal_data: Technical signal data
            math_data: Mathematical validation data
            market_context: Market environment data
            ticker: Symbol to analyze
            fetch_options_data: Whether to fetch real options data
            
        Returns:
            Complete trading decision with options specification
        """
        try:
            # Step 1: Get base Agent Zero decision (50% liquidity weighting)
            base_decision = self.predict(signal_data, math_data, market_context)
            
            logger.info(f"Agent Zero base decision for {ticker}: {base_decision['action']}")
            
            # Step 2: If non-directional, return base decision
            if base_decision['action'] not in ['buy_calls', 'buy_puts']:
                return {
                    'decision_type': 'non_directional',
                    'base_decision': base_decision,
                    'final_recommendation': {
                        'action': base_decision['action'],
                        'confidence': base_decision['confidence'],
                        'reasoning': base_decision.get('reasoning', ['Agent Zero base decision']),
                        'ticker': ticker
                    }
                }
            
            # Step 3: Fetch options data if needed
            if fetch_options_data:
                options_data = self._fetch_options_data(ticker)
                if not options_data:
                    logger.warning(f"Could not fetch options data for {ticker}")
                    return self._create_fallback_decision(base_decision, ticker, "No options data available")
            else:
                options_data = self._create_mock_options_data(ticker, market_context)
            
            # Step 4: Enhance with options intelligence
            enhanced_decision = self._enhance_with_options_intelligence(
                base_decision, ticker, options_data, market_context
            )
            
            return enhanced_decision
            
        except Exception as e:
            logger.error(f"Complete trading decision failed for {ticker}: {e}")
            return self._create_error_decision(ticker, str(e))
    
    def _fetch_options_data(self, ticker: str) -> Optional[Dict[str, Any]]:
        """Fetch real options data from MCP server"""
        try:
            response = requests.get(f"{self.mcp_url}/options/{ticker}", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                logger.warning(f"Options data fetch failed: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.warning(f"Options data fetch error: {e}")
            return None
    
    def _create_mock_options_data(self, ticker: str, market_context: Dict) -> Dict[str, Any]:
        """Create mock options data for testing"""
        current_price = market_context.get('current_price', 100.0)
        
        # Create simple options chain
        strikes = []
        for i in range(-2, 3):  # 5 strikes around current price
            strike = round(current_price + (i * 5), 0)
            strikes.append(strike)
        
        calls = []
        puts = []
        
        for strike in strikes:
            # Simple option pricing
            call_intrinsic = max(0, current_price - strike)
            put_intrinsic = max(0, strike - current_price)
            
            time_value = 2.0  # Simple time value
            
            calls.append({
                'strike': strike,
                'bid': call_intrinsic + time_value - 0.1,
                'ask': call_intrinsic + time_value + 0.1,
                'volume': 100,
                'open_interest': 1000
            })
            
            puts.append({
                'strike': strike,
                'bid': put_intrinsic + time_value - 0.1,
                'ask': put_intrinsic + time_value + 0.1,
                'volume': 100,
                'open_interest': 1000
            })
        
        return {
            'symbol': ticker,
            'underlying_price': current_price,
            'expiration_date': '2025-01-17',
            'calls': calls,
            'puts': puts
        }
    
    def _enhance_with_options_intelligence(self, base_decision: Dict, ticker: str,
                                         options_data: Dict, market_context: Dict) -> Dict[str, Any]:
        """Enhance base decision with complete options intelligence"""
        try:
            # Select appropriate option based on signal
            if base_decision['action'] == 'buy_calls':
                selected_option = self._select_optimal_call(options_data, market_context)
            else:  # buy_puts
                selected_option = self._select_optimal_put(options_data, market_context)
            
            if not selected_option:
                return self._create_fallback_decision(base_decision, ticker, "No suitable options found")
            
            # Create enhanced options task
            options_task = create_enhanced_options_task(
                ticker=ticker,
                underlying_price=options_data['underlying_price'],
                strike=selected_option['strike'],
                expiration_date=options_data.get('expiration_date', '2025-01-17'),
                option_type='call' if base_decision['action'] == 'buy_calls' else 'put',
                implied_volatility=market_context.get('implied_volatility', 0.25),
                agent_zero_signal=base_decision['action'],
                agent_zero_confidence=base_decision['confidence'],
                iv_rank=market_context.get('iv_rank', 50.0),
                price_trend=market_context.get('price_trend', 'neutral'),
                volatility_regime=market_context.get('volatility_regime', 'normal_vol')
            )
            
            # Process through enhanced options agent
            options_result = self.enhanced_options_agent.process_task(options_task)
            
            if options_result.status.value == 'completed':
                return self._create_enhanced_decision(base_decision, options_result.outputs, ticker)
            else:
                return self._create_fallback_decision(
                    base_decision, ticker, f"Options analysis failed: {options_result.error_details}"
                )
                
        except Exception as e:
            logger.error(f"Options enhancement failed: {e}")
            return self._create_fallback_decision(base_decision, ticker, f"Enhancement error: {e}")
    
    def _select_optimal_call(self, options_data: Dict, market_context: Dict) -> Optional[Dict]:
        """Select optimal call option"""
        calls = options_data.get('calls', [])
        if not calls:
            return None
        
        # Simple selection - ATM or slightly OTM
        underlying_price = options_data['underlying_price']
        
        best_call = None
        best_score = 0
        
        for call in calls:
            strike = call['strike']
            moneyness = underlying_price / strike
            
            # Score based on moneyness (prefer ATM to slight OTM)
            if 0.95 <= moneyness <= 1.05:  # ATM
                score = 1.0
            elif 0.90 <= moneyness <= 1.10:  # Close to ATM
                score = 0.8
            else:
                score = 0.3
            
            if score > best_score:
                best_score = score
                best_call = call
        
        return best_call
    
    def _select_optimal_put(self, options_data: Dict, market_context: Dict) -> Optional[Dict]:
        """Select optimal put option"""
        puts = options_data.get('puts', [])
        if not puts:
            return None
        
        # Simple selection - ATM or slightly OTM
        underlying_price = options_data['underlying_price']
        
        best_put = None
        best_score = 0
        
        for put in puts:
            strike = put['strike']
            moneyness = strike / underlying_price
            
            # Score based on moneyness (prefer ATM to slight OTM)
            if 0.95 <= moneyness <= 1.05:  # ATM
                score = 1.0
            elif 0.90 <= moneyness <= 1.10:  # Close to ATM
                score = 0.8
            else:
                score = 0.3
            
            if score > best_score:
                best_score = score
                best_put = put
        
        return best_put
    
    def _create_enhanced_decision(self, base_decision: Dict, options_analysis: Dict, ticker: str) -> Dict[str, Any]:
        """Create enhanced decision with complete options intelligence"""
        return {
            'decision_type': 'enhanced_options',
            'ticker': ticker,
            'base_decision': base_decision,
            'options_analysis': options_analysis,
            'final_recommendation': {
                'action': options_analysis['final_action'],
                'confidence': base_decision['confidence'],
                
                # Specific trade details
                'strike': options_analysis['strike'],
                'option_type': options_analysis['option_type'],
                'position_size': options_analysis['position_size'],
                'expiration_date': options_analysis['expiration_date'],
                
                # Greek-based risk metrics
                'estimated_delta': options_analysis['estimated_delta'],
                'daily_theta_cost': options_analysis['daily_theta_cost'],
                'iv_sensitivity': options_analysis['iv_sensitivity'],
                'gamma_risk': options_analysis['gamma_risk'],
                
                # Trade characteristics
                'trade_classification': options_analysis['trade_classification'],
                'leverage_factor': options_analysis['leverage_factor'],
                'probability_estimate': options_analysis['probability_estimate'],
                'breakeven_move_pct': options_analysis['breakeven_move_pct'],
                
                # Exit strategy
                'max_hold_days': options_analysis['max_hold_days'],
                'profit_targets': options_analysis['profit_targets'],
                'stop_losses': options_analysis['stop_losses'],
                'primary_exit_rule': options_analysis['primary_exit_rule'],
                
                # Risk assessment
                'overall_risk_level': options_analysis['overall_risk_level'],
                'execution_quality': options_analysis['execution_quality'],
                'primary_risks': options_analysis['primary_risks'],
                
                # Complete reasoning
                'reasoning': options_analysis['reasoning'],
                'greek_engine_quality': options_analysis['greek_engine_quality']
            }
        }
    
    def _create_fallback_decision(self, base_decision: Dict, ticker: str, reason: str) -> Dict[str, Any]:
        """Create fallback decision when options analysis fails"""
        return {
            'decision_type': 'fallback',
            'ticker': ticker,
            'base_decision': base_decision,
            'fallback_reason': reason,
            'final_recommendation': {
                'action': base_decision['action'],
                'confidence': base_decision['confidence'] * 0.8,  # Reduce confidence
                'reasoning': base_decision.get('reasoning', []) + [f"Options analysis unavailable: {reason}"],
                'fallback_mode': True
            }
        }
    
    def _create_error_decision(self, ticker: str, error: str) -> Dict[str, Any]:
        """Create error decision when everything fails"""
        return {
            'decision_type': 'error',
            'ticker': ticker,
            'error': error,
            'final_recommendation': {
                'action': 'avoid',
                'confidence': 0.0,
                'reasoning': [f"System error: {error}"],
                'error_mode': True
            }
        }

# Demonstration function
def demonstrate_complete_system():
    """Demonstrate the complete Agent Zero + Enhanced Options system"""
    print("=" * 70)
    print("AGENT ZERO WITH ENHANCED OPTIONS - COMPLETE SYSTEM DEMONSTRATION")
    print("=" * 70)
    
    try:
        # Initialize enhanced Agent Zero
        enhanced_agent_zero = AgentZeroWithEnhancedOptions()
        print("[OK] Enhanced Agent Zero initialized")
        
        # Create test market data
        signal_data = {
            'confidence': 0.75,
            'strength': 0.6,
            'execution_recommendation': 'execute'
        }
        
        math_data = {
            'accuracy_score': 0.95,
            'precision': 0.001
        }
        
        market_context = {
            'current_price': 455.0,
            'liquidity_score': 0.7,
            'volume_analysis': {'relative_volume': 25000000},
            'flow_analysis': {'momentum': 0.3, 'strength': 0.6},
            'iv_dynamics_analysis': {'iv_rank': 45},
            'b_series_analysis': {'confidence': 0.8, 'pattern_strength': 0.9},
            'implied_volatility': 0.25,
            'iv_rank': 45.0,
            'price_trend': 'bullish',
            'volatility_regime': 'normal_vol'
        }
        
        # Get complete trading decision
        complete_decision = enhanced_agent_zero.get_complete_trading_decision(
            signal_data=signal_data,
            math_data=math_data,
            market_context=market_context,
            ticker='SPY',
            fetch_options_data=False  # Use mock data for demo
        )
        
        print(f"[OK] Complete decision generated: {complete_decision['decision_type']}")
        
        # Display results
        final_rec = complete_decision['final_recommendation']
        print(f"\nFINAL RECOMMENDATION:")
        print(f"  Action: {final_rec['action']}")
        print(f"  Confidence: {final_rec['confidence']:.1%}")
        
        if complete_decision['decision_type'] == 'enhanced_options':
            print(f"  Strike: ${final_rec['strike']}")
            print(f"  Position Size: {final_rec['position_size']:.1f} contracts")
            print(f"  Max Hold: {final_rec['max_hold_days']} days")
            print(f"  Delta: {final_rec['estimated_delta']:.3f}")
            print(f"  Trade Type: {final_rec['trade_classification']}")
            print(f"  Risk Level: {final_rec['overall_risk_level']}")
            print(f"  Execution Quality: {final_rec['execution_quality']}")
            print(f"  Greek Engine Quality: {final_rec['greek_engine_quality']:.3f}")
        
        print(f"\nREASONING:")
        if isinstance(final_rec.get('reasoning'), list):
            for reason in final_rec['reasoning'][:3]:  # Show first 3 reasons
                print(f"  - {reason}")
        else:
            print(f"  {final_rec.get('reasoning', 'No reasoning provided')}")
        
        print("\n[SUCCESS] Complete system demonstration successful")
        return True
        
    except Exception as e:
        print(f"[ERROR] Demonstration failed: {e}")
        return False

if __name__ == "__main__":
    demonstrate_complete_system()
