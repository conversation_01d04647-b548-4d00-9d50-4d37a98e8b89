#!/usr/bin/env python3
"""
CORE Flow Detection System - Liquidity Analyzer

Stripped, optimized liquidity sweep analyzer focusing on:
- Institutional accumulation/distribution detection
- Campaign stage analysis (building/accumulation/distribution)
- Volume absorption quality at key levels
- Range-based flow analysis

Clean implementation with essential institutional pattern recognition.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Import from CORE data layer
from data.factor_spec import FactorData, DirectionBias, TimeFrame, create_factor
from config.constants import LIQUIDITY_ANALYSIS

class LiquidityAnalyzer:
    """
    Core liquidity sweep analyzer - institutional pattern detection.
    Clean, focused implementation for accumulation/distribution analysis.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Core thresholds from constants
        self.accumulation_threshold = LIQUIDITY_ANALYSIS['ACCUMULATION_THRESHOLD']
        self.distribution_threshold = LIQUIDITY_ANALYSIS['DISTRIBUTION_THRESHOLD']
        self.absorption_threshold = LIQUIDITY_ANALYSIS['ABSORPTION_EFFICIENCY_THRESHOLD']
        self.campaign_confidence = LIQUIDITY_ANALYSIS['CAMPAIGN_STAGE_CONFIDENCE']
        self.level_strength_threshold = LIQUIDITY_ANALYSIS['LEVEL_STRENGTH_THRESHOLD']
        self.institutional_bias_threshold = LIQUIDITY_ANALYSIS['INSTITUTIONAL_BIAS_THRESHOLD']
        
        # Level tracking
        self._level_cache = {}
    
    def analyze_factors(self, data_package: Dict[str, Any]) -> List[FactorData]:
        """
        Main analyzer interface - generate liquidity factors.
        
        Args:
            data_package: {ticker, current_price, mtf_data, timestamp}
            
        Returns:
            List[FactorData]: Liquidity analysis factors
        """
        ticker = data_package.get('ticker', 'UNKNOWN')
        current_price = data_package.get('current_price', 0.0)
        mtf_data = data_package.get('mtf_data', {})
        timestamp = data_package.get('timestamp', datetime.now())
        
        factors = []
        
        try:
            # Get primary timeframe data
            price_data = self._get_primary_timeframe_data(mtf_data)
            if price_data is None or price_data.empty:
                return factors
            
            # Perform liquidity analysis
            liquidity_result = self._analyze_liquidity_patterns(ticker, price_data, current_price)
            
            # Generate factors from analysis
            factors.extend(self._create_liquidity_factors(
                ticker, liquidity_result, timestamp, current_price
            ))
            
            return factors
            
        except Exception as e:
            print(f"Liquidity analysis error for {ticker}: {e}")
            return factors
    
    def _get_primary_timeframe_data(self, mtf_data: Dict[str, pd.DataFrame]) -> Optional[pd.DataFrame]:
        """Get best available timeframe data."""
        # Prefer 1h for liquidity analysis, fallback to available
        preferred_order = ['1h', '15m', '30m', '4h', '5m', '1m']
        
        for tf in preferred_order:
            if tf in mtf_data and not mtf_data[tf].empty:
                return mtf_data[tf]
        
        # Return any available data
        for tf_data in mtf_data.values():
            if not tf_data.empty:
                return tf_data
        
        return None
    
    def _analyze_liquidity_patterns(self, ticker: str, price_data: pd.DataFrame, 
                                  current_price: float) -> Dict[str, Any]:
        """Core liquidity pattern analysis."""
        
        try:
            # Identify key liquidity levels
            support_level, resistance_level = self._identify_key_levels(price_data, current_price)
            
            # Analyze range characteristics
            range_info = self._analyze_range_quality(price_data, support_level, resistance_level, current_price)
            
            # Detect institutional patterns
            institutional_patterns = self._detect_institutional_activity(
                price_data, support_level, resistance_level
            )
            
            # Calculate campaign stage
            campaign_stage = self._determine_campaign_stage(institutional_patterns, range_info)
            
            # Analyze volume absorption
            absorption_quality = self._analyze_volume_absorption(
                price_data, support_level, resistance_level
            )
            
            return {
                'support_level': support_level,
                'resistance_level': resistance_level,
                'range_info': range_info,
                'institutional_patterns': institutional_patterns,
                'campaign_stage': campaign_stage,
                'absorption_quality': absorption_quality,
                'analysis_confidence': self._calculate_analysis_confidence(
                    price_data, range_info, institutional_patterns
                )
            }
            
        except Exception as e:
            print(f"Liquidity pattern analysis error: {e}")
            return self._empty_analysis_result()
    
    def _identify_key_levels(self, price_data: pd.DataFrame, current_price: float) -> Tuple[float, float]:
        """Identify key support and resistance levels."""
        try:
            # Find swing highs and lows
            highs = self._find_swing_points(price_data['high'], 'high')
            lows = self._find_swing_points(price_data['low'], 'low')
            
            if not highs or not lows:
                # Fallback to recent high/low
                support_level = price_data['low'].min()
                resistance_level = price_data['high'].max()
            else:
                # Find most significant levels
                support_level = self._find_most_significant_level(lows, current_price, 'support')
                resistance_level = self._find_most_significant_level(highs, current_price, 'resistance')
            
            return support_level, resistance_level
            
        except Exception as e:
            print(f"Level identification error: {e}")
            return current_price * 0.98, current_price * 1.02
    
    def _find_swing_points(self, series: pd.Series, point_type: str, window: int = 3) -> List[float]:
        """Find swing highs or lows."""
        points = []
        
        try:
            for i in range(window, len(series) - window):
                current_val = series.iloc[i]
                is_swing = True
                
                # Check if this is a valid swing point
                for j in range(1, window + 1):
                    if point_type == 'high':
                        if current_val <= series.iloc[i-j] or current_val <= series.iloc[i+j]:
                            is_swing = False
                            break
                    else:  # low
                        if current_val >= series.iloc[i-j] or current_val >= series.iloc[i+j]:
                            is_swing = False
                            break
                
                if is_swing:
                    points.append(current_val)
            
            return points
            
        except Exception:
            return []
    
    def _find_most_significant_level(self, levels: List[float], current_price: float, level_type: str) -> float:
        """Find most significant level based on proximity and frequency."""
        if not levels:
            return current_price * (0.98 if level_type == 'support' else 1.02)
        
        try:
            # Count touches for each level (with tolerance)
            level_scores = {}
            tolerance = current_price * 0.005  # 0.5% tolerance
            
            for level in levels:
                score = 0
                for other_level in levels:
                    if abs(level - other_level) <= tolerance:
                        score += 1
                
                # Add proximity bonus (closer to current price = more significant)
                proximity_factor = 1 / (1 + abs(level - current_price) / current_price)
                score *= proximity_factor
                
                level_scores[level] = score
            
            # Return level with highest score
            best_level = max(level_scores.items(), key=lambda x: x[1])[0]
            return best_level
            
        except Exception:
            return levels[0] if levels else current_price
    
    def _analyze_range_quality(self, price_data: pd.DataFrame, support_level: float, 
                             resistance_level: float, current_price: float) -> Dict[str, Any]:
        """Analyze range quality and characteristics."""
        try:
            range_size = resistance_level - support_level
            if range_size <= 0:
                return {'quality_score': 0.0, 'current_position_pct': 0.5}
            
            # Calculate current position in range
            current_position_pct = (current_price - support_level) / range_size
            current_position_pct = max(0.0, min(1.0, current_position_pct))
            
            # Calculate range containment (how much price stayed in range)
            in_range_count = 0
            for _, bar in price_data.iterrows():
                if support_level <= bar['low'] and bar['high'] <= resistance_level:
                    in_range_count += 1
            
            containment_ratio = in_range_count / len(price_data)
            
            # Calculate range compression (tightening over time)
            recent_range = price_data['high'].tail(10).max() - price_data['low'].tail(10).min()
            full_range = price_data['high'].max() - price_data['low'].min()
            compression_ratio = recent_range / full_range if full_range > 0 else 1.0
            
            # Overall quality score
            quality_score = (containment_ratio * 0.5) + (compression_ratio * 0.3) + (0.2)  # Base score
            quality_score = max(0.0, min(1.0, quality_score))
            
            return {
                'quality_score': quality_score,
                'current_position_pct': current_position_pct,
                'containment_ratio': containment_ratio,
                'compression_ratio': compression_ratio,
                'range_size': range_size,
                'range_size_pct': range_size / current_price
            }
            
        except Exception as e:
            print(f"Range quality analysis error: {e}")
            return {'quality_score': 0.5, 'current_position_pct': 0.5}
    
    def _detect_institutional_activity(self, price_data: pd.DataFrame, 
                                     support_level: float, resistance_level: float) -> Dict[str, Any]:
        """Detect institutional accumulation/distribution patterns."""
        try:
            # Analyze volume at support and resistance
            support_analysis = self._analyze_level_volume(price_data, support_level, 'support')
            resistance_analysis = self._analyze_level_volume(price_data, resistance_level, 'resistance')
            
            # Calculate accumulation vs distribution scores
            accumulation_score = support_analysis['absorption_quality']
            distribution_score = resistance_analysis['absorption_quality']
            
            # Determine institutional bias
            if accumulation_score > self.accumulation_threshold and distribution_score < 0.4:
                institutional_bias = 'accumulation'
                bias_strength = accumulation_score
            elif distribution_score > self.distribution_threshold and accumulation_score < 0.4:
                institutional_bias = 'distribution'
                bias_strength = distribution_score
            else:
                institutional_bias = 'neutral'
                bias_strength = max(accumulation_score, distribution_score)
            
            return {
                'institutional_bias': institutional_bias,
                'bias_strength': bias_strength,
                'accumulation_score': accumulation_score,
                'distribution_score': distribution_score,
                'support_analysis': support_analysis,
                'resistance_analysis': resistance_analysis
            }
            
        except Exception as e:
            print(f"Institutional activity detection error: {e}")
            return {
                'institutional_bias': 'neutral',
                'bias_strength': 0.5,
                'accumulation_score': 0.5,
                'distribution_score': 0.5
            }
    
    def _analyze_level_volume(self, price_data: pd.DataFrame, level: float, level_type: str) -> Dict[str, Any]:
        """Analyze volume patterns at a specific level."""
        try:
            tolerance = level * 0.005  # 0.5% tolerance
            
            # Find touches at this level
            touches = []
            for i, bar in price_data.iterrows():
                touched = False
                if level_type == 'support':
                    touched = abs(bar['low'] - level) <= tolerance
                else:  # resistance
                    touched = abs(bar['high'] - level) <= tolerance
                
                if touched:
                    # Determine if it was a rejection or break attempt
                    rejection = (level_type == 'support' and bar['close'] > bar['open']) or \
                              (level_type == 'resistance' and bar['close'] < bar['open'])
                    
                    touches.append({
                        'volume': bar['volume'],
                        'rejection': rejection
                    })
            
            if not touches:
                return {'absorption_quality': 0.5, 'touch_count': 0}
            
            # Calculate absorption quality
            total_volume = sum(touch['volume'] for touch in touches)
            rejection_volume = sum(touch['volume'] for touch in touches if touch['rejection'])
            
            if total_volume > 0:
                rejection_ratio = rejection_volume / total_volume
            else:
                rejection_ratio = 0.0
            
            # Compare volume to average
            avg_volume = price_data['volume'].mean()
            touch_volumes = [touch['volume'] for touch in touches]
            avg_touch_volume = np.mean(touch_volumes) if touch_volumes else 0
            
            volume_ratio = avg_touch_volume / avg_volume if avg_volume > 0 else 1.0
            
            # Calculate absorption quality
            absorption_quality = (rejection_ratio * 0.6) + (min(1.0, volume_ratio) * 0.4)
            absorption_quality = max(0.0, min(1.0, absorption_quality))
            
            return {
                'absorption_quality': absorption_quality,
                'touch_count': len(touches),
                'rejection_ratio': rejection_ratio,
                'volume_ratio': volume_ratio
            }
            
        except Exception as e:
            print(f"Level volume analysis error: {e}")
            return {'absorption_quality': 0.5, 'touch_count': 0}
    
    def _determine_campaign_stage(self, institutional_patterns: Dict[str, Any], 
                                range_info: Dict[str, Any]) -> Dict[str, Any]:
        """Determine current institutional campaign stage."""
        try:
            accumulation_score = institutional_patterns['accumulation_score']
            distribution_score = institutional_patterns['distribution_score']
            range_quality = range_info['quality_score']
            
            # Determine stage based on scores and patterns
            if accumulation_score > 0.7 and range_quality > 0.6:
                stage = 'accumulation'
                confidence = min(1.0, (accumulation_score + range_quality) / 2)
            elif distribution_score > 0.7 and range_quality > 0.6:
                stage = 'distribution'
                confidence = min(1.0, (distribution_score + range_quality) / 2)
            elif accumulation_score > 0.5 or distribution_score > 0.5:
                stage = 'building'
                confidence = max(accumulation_score, distribution_score)
            else:
                stage = 'early_stage'
                confidence = 0.3
            
            # Estimate completion percentage for active campaigns
            if stage in ['accumulation', 'distribution']:
                # Simple completion estimate based on range position and time
                completion_pct = min(0.9, range_quality * 1.2)
            elif stage == 'building':
                completion_pct = 0.3 + (max(accumulation_score, distribution_score) * 0.4)
            else:
                completion_pct = 0.1
            
            return {
                'stage': stage,
                'confidence': confidence,
                'completion_pct': completion_pct,
                'estimated_direction': institutional_patterns['institutional_bias']
            }
            
        except Exception as e:
            print(f"Campaign stage determination error: {e}")
            return {
                'stage': 'early_stage',
                'confidence': 0.3,
                'completion_pct': 0.1,
                'estimated_direction': 'neutral'
            }
    
    def _analyze_volume_absorption(self, price_data: pd.DataFrame, 
                                 support_level: float, resistance_level: float) -> Dict[str, Any]:
        """Analyze overall volume absorption efficiency."""
        try:
            support_absorption = self._analyze_level_volume(price_data, support_level, 'support')
            resistance_absorption = self._analyze_level_volume(price_data, resistance_level, 'resistance')
            
            # Calculate overall absorption efficiency
            support_efficiency = support_absorption['absorption_quality']
            resistance_efficiency = resistance_absorption['absorption_quality']
            
            overall_efficiency = (support_efficiency + resistance_efficiency) / 2
            
            # Determine which level is more significant
            support_touches = support_absorption['touch_count']
            resistance_touches = resistance_absorption['touch_count']
            
            if support_touches > resistance_touches:
                dominant_level = 'support'
                dominant_efficiency = support_efficiency
            elif resistance_touches > support_touches:
                dominant_level = 'resistance'
                dominant_efficiency = resistance_efficiency
            else:
                dominant_level = 'balanced'
                dominant_efficiency = overall_efficiency
            
            return {
                'overall_efficiency': overall_efficiency,
                'support_efficiency': support_efficiency,
                'resistance_efficiency': resistance_efficiency,
                'dominant_level': dominant_level,
                'dominant_efficiency': dominant_efficiency,
                'efficiency_quality': 'high' if overall_efficiency > self.absorption_threshold else 'medium' if overall_efficiency > 0.5 else 'low'
            }
            
        except Exception as e:
            print(f"Volume absorption analysis error: {e}")
            return {
                'overall_efficiency': 0.5,
                'support_efficiency': 0.5,
                'resistance_efficiency': 0.5,
                'dominant_level': 'balanced',
                'dominant_efficiency': 0.5,
                'efficiency_quality': 'medium'
            }
    
    def _calculate_analysis_confidence(self, price_data: pd.DataFrame, 
                                     range_info: Dict[str, Any], 
                                     institutional_patterns: Dict[str, Any]) -> float:
        """Calculate overall analysis confidence."""
        try:
            # Data quality factors
            data_quality = min(1.0, len(price_data) / 50)  # Normalize to 50 bars
            
            # Range quality factor
            range_quality = range_info.get('quality_score', 0.5)
            
            # Pattern clarity factor
            bias_strength = institutional_patterns.get('bias_strength', 0.5)
            
            # Combine factors
            confidence = (data_quality * 0.3) + (range_quality * 0.4) + (bias_strength * 0.3)
            
            return max(0.1, min(1.0, confidence))
            
        except Exception:
            return 0.5
    
    def _empty_analysis_result(self) -> Dict[str, Any]:
        """Return empty analysis result."""
        return {
            'support_level': 0.0,
            'resistance_level': 0.0,
            'range_info': {'quality_score': 0.0, 'current_position_pct': 0.5},
            'institutional_patterns': {'institutional_bias': 'neutral', 'bias_strength': 0.5},
            'campaign_stage': {'stage': 'early_stage', 'confidence': 0.1},
            'absorption_quality': {'overall_efficiency': 0.5},
            'analysis_confidence': 0.1
        }
    
    def _create_liquidity_factors(self, ticker: str, liquidity_result: Dict[str, Any], 
                                timestamp: datetime, current_price: float) -> List[FactorData]:
        """Create FactorData objects from liquidity analysis."""
        factors = []
        
        try:
            # Campaign stage factor
            campaign_stage = liquidity_result['campaign_stage']
            if campaign_stage['confidence'] > self.campaign_confidence:
                
                stage = campaign_stage['stage']
                direction = DirectionBias.NEUTRAL
                
                if stage == 'accumulation':
                    direction = DirectionBias.BULLISH
                elif stage == 'distribution':
                    direction = DirectionBias.BEARISH
                
                factor = create_factor(
                    analyzer_name="LiquidityAnalysis",
                    factor_name=f"Campaign_Stage_{stage}",
                    direction=direction,
                    strength=campaign_stage['confidence'],
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=liquidity_result['analysis_confidence'],
                    raw_value=campaign_stage['completion_pct'],
                    metadata={
                        'stage': stage,
                        'completion_pct': campaign_stage['completion_pct'],
                        'estimated_direction': campaign_stage['estimated_direction']
                    }
                )
                factors.append(factor)
            
            # Institutional bias factor
            institutional_patterns = liquidity_result['institutional_patterns']
            if institutional_patterns['bias_strength'] > self.institutional_bias_threshold:
                
                bias = institutional_patterns['institutional_bias']
                direction = DirectionBias.NEUTRAL
                
                if bias == 'accumulation':
                    direction = DirectionBias.BULLISH
                elif bias == 'distribution':
                    direction = DirectionBias.BEARISH
                
                factor = create_factor(
                    analyzer_name="LiquidityAnalysis",
                    factor_name=f"Institutional_{bias}",
                    direction=direction,
                    strength=institutional_patterns['bias_strength'],
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=liquidity_result['analysis_confidence'],
                    raw_value=institutional_patterns['bias_strength'],
                    metadata={
                        'institutional_bias': bias,
                        'accumulation_score': institutional_patterns['accumulation_score'],
                        'distribution_score': institutional_patterns['distribution_score']
                    }
                )
                factors.append(factor)
            
            # Volume absorption factor
            absorption_quality = liquidity_result['absorption_quality']
            if absorption_quality['overall_efficiency'] > self.absorption_threshold:
                
                direction = DirectionBias.NEUTRAL  # High absorption suggests range-bound
                
                factor = create_factor(
                    analyzer_name="LiquidityAnalysis",
                    factor_name="Volume_Absorption_High",
                    direction=direction,
                    strength=absorption_quality['overall_efficiency'],
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=liquidity_result['analysis_confidence'],
                    raw_value=absorption_quality['overall_efficiency'],
                    metadata={
                        'overall_efficiency': absorption_quality['overall_efficiency'],
                        'dominant_level': absorption_quality['dominant_level'],
                        'efficiency_quality': absorption_quality['efficiency_quality']
                    }
                )
                factors.append(factor)
            
            # Range quality factor
            range_info = liquidity_result['range_info']
            if range_info['quality_score'] > 0.6:
                
                # Range position bias
                position_pct = range_info['current_position_pct']
                if position_pct > 0.7:
                    direction = DirectionBias.BEARISH  # High in range
                elif position_pct < 0.3:
                    direction = DirectionBias.BULLISH  # Low in range
                else:
                    direction = DirectionBias.NEUTRAL  # Mid-range
                
                factor = create_factor(
                    analyzer_name="LiquidityAnalysis",
                    factor_name="Range_Position",
                    direction=direction,
                    strength=range_info['quality_score'],
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=liquidity_result['analysis_confidence'],
                    raw_value=position_pct,
                    metadata={
                        'current_position_pct': position_pct,
                        'range_quality': range_info['quality_score'],
                        'support_level': liquidity_result['support_level'],
                        'resistance_level': liquidity_result['resistance_level']
                    }
                )
                factors.append(factor)
            
        except Exception as e:
            print(f"Error creating liquidity factors: {e}")
        
        return factors
