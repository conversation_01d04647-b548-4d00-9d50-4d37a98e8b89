import os
import sys
import requests
import pandas as pd
import json
import logging
import time
from pathlib import Path
from datetime import date

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from agents.agent_base import BaseAgent

# Add path for Schwab production API
import os
sys.path.append(os.path.join(os.getcwd(), "SCHWAB_MCP_PRODUCTION", "core"))
sys.path.append(os.path.join(os.getcwd(), "api", "modules"))

class LiveDataGatewayAgent(BaseAgent):
    task_id = "D-01"

    # Configuration constants - REAL SCHWAB API ONLY
    # No more localhost:8005 mock server references

    # Source priority: Real Schwab API ONLY - No mock fallbacks
    SOURCE_PRIORITY = ["schwab_production"]
    
    def __init__(self, agent_id="live_data_gateway_agent"):
        """Initialize Live Data Gateway Agent with Real Schwab API Integration"""
        super().__init__(agent_id)
        self.logger = logging.getLogger(agent_id)

        # Initialize real Schwab API client
        try:
            from schwab_production_api import SchwabProductionClient, SchwabAPI
            self.schwab_client = SchwabProductionClient()
            self.schwab_api = SchwabAPI()
            self.has_real_api = True
            self.logger.info("✓ Real Schwab API client initialized - Production mode")
        except ImportError as e:
            self.schwab_client = None
            self.schwab_api = None
            self.has_real_api = False
            self.logger.error(f"✗ Failed to initialize real Schwab API: {e}")
            raise Exception("Real Schwab API required - no mock fallbacks allowed")

        # Direct Schwab API only - no broker layer needed
        self.enhanced_agent = None
        self.broker_integration_enabled = False
        self.logger.info("Using direct Schwab API only - broker layer removed")
    
    def execute_task(self, task):
        """Execute the data ingestion task"""
        ticker_list = task.inputs.get("ticker_list", [])
        source = task.inputs.get("source", "schwab")
        api_key = task.inputs.get("api_key")
        bar_tf = task.inputs.get("bar_tf", "1")
        
        return self.execute(ticker_list, source=source, bar_tf=bar_tf, api_key=api_key)
    
    def validate_inputs(self, task):
        """Validate task inputs meet requirements"""
        inputs = task.inputs
        required = ["ticker_list"]
        has_required = all(key in inputs for key in required)
        
        # If using polygon, api_key is required
        if inputs.get("source") == "schwab" and not inputs.get("api_key"):
            return False
            
        return has_required
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        quality_metrics = {}
        
        if isinstance(outputs, dict) and outputs.get("status") == "OK":
            meta = outputs.get("meta", {})
            total_bars = sum(ticker_data.get("bars", 0) for ticker_data in meta.values())
            total_opts = sum(ticker_data.get("opts", 0) for ticker_data in meta.values())
            
            quality_metrics["data_retrieved"] = 1.0
            quality_metrics["bars_sufficient"] = 1.0 if total_bars >= 10 else 0.0
            quality_metrics["options_sufficient"] = 1.0 if total_opts >= 50 else 0.0
        else:
            quality_metrics["data_retrieved"] = 0.0
            quality_metrics["bars_sufficient"] = 0.0
            quality_metrics["options_sufficient"] = 0.0
        
        return quality_metrics
    
    def log_training(self, data):
        """Log training data for Agent Zero learning"""
        print(f"Training logged: {json.dumps(data, indent=2, default=str)}")
    
    def execute(self, ticker_list, *, source="auto", bar_tf="1", api_key=None):
        """
        Execute data ingestion with real Schwab API only - no mock fallbacks

        Source priority:
        1. "auto" - Use real Schwab API directly
        2. "schwab_production" - Force real Schwab API only
        """
        today = date.today().isoformat()
        out_dir = Path(f"data/live/{today}")
        out_dir.mkdir(parents=True, exist_ok=True)

        # Use real Schwab API directly
        if not self.has_real_api:
            raise Exception("Real Schwab API not available - no mock fallbacks allowed")

        return self._execute_with_real_schwab_api(ticker_list, out_dir, bar_tf)

    def _execute_with_real_schwab_api(self, ticker_list, out_dir, bar_tf):
        """Execute using real Schwab API directly - no mock systems"""
        results = []

        for ticker in ticker_list:
            try:
                # Get real-time quote
                quote_data = self.schwab_client.get_real_time_quote(ticker)

                # Get historical price data
                price_history = self.schwab_api.get_price_history(
                    ticker,
                    period=1,  # 1 day
                    frequency=int(bar_tf) if bar_tf.isdigit() else 5
                )

                # Get options data if available
                try:
                    options_data = self.schwab_api.get_option_chains(ticker)
                except Exception as e:
                    self.logger.warning(f"Options data not available for {ticker}: {e}")
                    options_data = None

                # Save data
                ticker_file = out_dir / f"{ticker}_live_data.json"
                data_package = {
                    "ticker": ticker,
                    "quote": quote_data,
                    "price_history": price_history,
                    "options": options_data,
                    "timestamp": time.time(),
                    "source": "schwab_production_api"
                }

                with open(ticker_file, 'w') as f:
                    json.dump(data_package, f, indent=2, default=str)

                results.append({
                    "ticker": ticker,
                    "status": "OK",
                    "file": str(ticker_file),
                    "data_points": len(price_history) if price_history else 0
                })

                self.logger.info(f"✓ Real data fetched for {ticker}")

            except Exception as e:
                self.logger.error(f"✗ Failed to fetch real data for {ticker}: {e}")
                results.append({
                    "ticker": ticker,
                    "status": "ERROR",
                    "error": str(e)
                })

        return {
            "status": "OK" if all(r["status"] == "OK" for r in results) else "PARTIAL",
            "results": results,
            "source": "schwab_production_api",
            "timestamp": time.time()
        }
    
    def _execute_with_broker_integration(self, ticker_list, out_dir, bar_tf):
        """Execute using enhanced broker API integration with formulated fallback"""
        meta = {}
        successful_tickers = []
        source_usage = {"schwab": 0, "schwab": 0, "formulated": 0, "failed": 0}
        
        for tk in ticker_list:
            try:
                # Get market data with automatic source selection and fallback
                market_data_result = self.enhanced_agent.get_market_data(tk, use_fallback=True)
                
                if market_data_result["data"]:
                    # Extract data based on source
                    data_source = market_data_result["source"]
                    market_data = market_data_result["data"]
                    
                    # Track source usage
                    if "schwab" in data_source:
                        source_usage["schwab"] += 1
                    elif "schwab" in data_source:
                        source_usage["schwab"] += 1
                    elif "formulated" in data_source:
                        source_usage["formulated"] += 1
                    
                    # Get bars and options using best available source
                    bars, opts = self._get_comprehensive_data(tk, data_source, bar_tf)
                    
                    # Enhance data with broker bid/ask if available
                    if 'bid' in market_data and 'ask' in market_data:
                        # Add real bid/ask to bars data
                        if not bars.empty:
                            bars['real_bid'] = market_data['bid']
                            bars['real_ask'] = market_data['ask']
                            bars['spread_pct'] = ((market_data['ask'] - market_data['bid']) / market_data['last_price']) * 100
                            
                            # Handle quality metrics properly
                            quality_metrics = market_data_result.get('quality_metrics')
                            if quality_metrics:
                                if hasattr(quality_metrics, 'overall_quality'):
                                    bars['data_quality'] = quality_metrics.overall_quality
                                elif isinstance(quality_metrics, dict):
                                    bars['data_quality'] = quality_metrics.get('overall_quality', 0.0)
                                else:
                                    bars['data_quality'] = 0.0
                            else:
                                bars['data_quality'] = 0.0
                    
                    # Save enhanced data
                    bars_path = out_dir / f"{tk}_bars.parquet"
                    opts_path = out_dir / f"{tk}_options.parquet"
                    
                    bars.to_parquet(bars_path, index=False)
                    opts.to_parquet(opts_path, index=False)
                    
                    meta[tk] = {
                        "bars": len(bars),
                        "opts": len(opts),
                        "data_source": data_source,
                        "has_real_bid_ask": 'bid' in market_data and 'ask' in market_data,
                        "bars_file": str(bars_path),
                        "opts_file": str(opts_path)
                    }
                    
                    # Handle quality score properly
                    quality_metrics = market_data_result.get('quality_metrics')
                    if quality_metrics:
                        if hasattr(quality_metrics, 'overall_quality'):
                            meta[tk]["quality_score"] = quality_metrics.overall_quality
                        elif isinstance(quality_metrics, dict):
                            meta[tk]["quality_score"] = quality_metrics.get('overall_quality', 0.0)
                        else:
                            meta[tk]["quality_score"] = 0.0
                    else:
                        meta[tk]["quality_score"] = 0.0
                    successful_tickers.append(tk)
                    
                else:
                    source_usage["failed"] += 1
                    self.logger.error(f"Failed to get market data for {tk}")
                    meta[tk] = {"error": "No data from any source", "data_source": "none"}
                    
            except Exception as e:
                source_usage["failed"] += 1
                self.logger.error(f"Error processing {tk}: {str(e)}")
                meta[tk] = {"error": str(e), "data_source": "error"}
        
        result = {
            "status": "OK" if successful_tickers else "PARTIAL",
            "meta": meta,
            "successful_tickers": successful_tickers,
            "source_used": "broker_api_integration",
            "source_usage": source_usage,
            "enhancement": "real_bid_ask_data",
            "output_dir": str(out_dir)
        }
        
        # Log training data
        self.log_training({
            "inputs": {"ticker_list": ticker_list, "mode": "broker_integration"},
            "outputs": result
        })
        
        return result
    
    def _get_comprehensive_data(self, ticker, data_source, bar_tf):
        """Get comprehensive bars and options data from best available source"""
        try:
            # Try Schwab MCP first for comprehensive data
            if "schwab" in data_source or data_source == "auto":
                try:
                    return self._pull_schwab_mcp(ticker, bar_tf)
                except Exception as e:
                    self.logger.warning(f"Schwab MCP failed for {ticker}: {str(e)}")
            
            # Fallback to regular MCP
            try:
                return self._pull_mcp(ticker, bar_tf)
            except Exception as e:
                self.logger.warning(f"Regular MCP failed for {ticker}: {str(e)}")
            
            # Last resort: empty dataframes
            return pd.DataFrame(), pd.DataFrame()
            
        except Exception as e:
            self.logger.error(f"Error getting comprehensive data for {ticker}: {str(e)}")
            return pd.DataFrame(), pd.DataFrame()
    
    def _pull_schwab_mcp(self, ticker, bar_tf):
        """Pull HISTORICAL data from Schwab MCP server using proper bars endpoint"""
        try:
            # FIXED: Use bars endpoint for historical data instead of quotes
            # This fetches actual historical price bars, not just current quote
            bars_response = requests.get(
                f"{self.SCHWAB_MCP_BASE}/bars",
                params={
                    'tk': ticker,
                    'tf': bar_tf
                },
                timeout=15
            )

            if bars_response.status_code == 200:
                bars_data = bars_response.json()

                # Handle different response formats
                if isinstance(bars_data, dict):
                    if 'data' in bars_data:
                        data_list = bars_data['data']
                    elif 'result' in bars_data:
                        data_list = bars_data['result']
                    else:
                        data_list = [bars_data]  # Single record
                elif isinstance(bars_data, list):
                    data_list = bars_data
                else:
                    data_list = []

                # Convert to DataFrame with proper column mapping
                if data_list:
                    bars_df = pd.DataFrame(data_list)

                    # Ensure required columns exist and add ticker
                    if not bars_df.empty:
                        # Add ticker column if missing
                        if 'ticker' not in bars_df.columns:
                            bars_df['ticker'] = ticker.upper()

                        # Ensure timestamp column exists
                        if 't' not in bars_df.columns and 'timestamp' in bars_df.columns:
                            bars_df['t'] = bars_df['timestamp']
                        elif 't' not in bars_df.columns:
                            bars_df['t'] = int(time.time())

                        self.logger.info(f"[{ticker}] Retrieved {len(bars_df)} historical price bars from Schwab MCP")
                else:
                    bars_df = pd.DataFrame()
                    self.logger.warning(f"[{ticker}] No historical data returned from Schwab MCP bars endpoint")
            else:
                self.logger.warning(f"[{ticker}] Schwab MCP bars endpoint failed: {bars_response.status_code}")
                bars_df = pd.DataFrame()
            
            # HTTP REST request for options
            options_response = requests.get(
                f"{self.SCHWAB_MCP_BASE}/options/{ticker}",
                timeout=15
            )
            
            if options_response.status_code == 200:
                options_data = options_response.json()
                
                # Convert options chain to DataFrame
                options_list = []
                
                # Process calls
                for call in options_data.get('calls', []):
                    options_list.append({
                        'ticker': ticker.upper(),
                        'strike': call.get('strike'),
                        'bid': call.get('bid'),
                        'ask': call.get('ask'),
                        'last': call.get('last'),
                        'volume': call.get('volume', 0),
                        'open_interest': call.get('open_interest', 0),
                        'type': 'call'
                    })
                
                # Process puts
                for put in options_data.get('puts', []):
                    options_list.append({
                        'ticker': ticker.upper(),
                        'strike': put.get('strike'),
                        'bid': put.get('bid'),
                        'ask': put.get('ask'),
                        'last': put.get('last'),
                        'volume': put.get('volume', 0),
                        'open_interest': put.get('open_interest', 0),
                        'type': 'put'
                    })
                
                opts_df = pd.DataFrame(options_list)
            else:
                opts_df = pd.DataFrame()
            
            return bars_df, opts_df

        except Exception as e:
            self.logger.error(f"Schwab MCP error for {ticker}: {str(e)}")
            return pd.DataFrame(), pd.DataFrame()
    
    # ---------- Legacy methods removed - using real Schwab API only ----------

    def get_options_chain(self, ticker):
        """Get options chain data for the specified ticker"""
        try:
            if not self.has_real_api:
                self.logger.error(f"No real Schwab API available for options chain: {ticker}")
                return None

            # Use Schwab API to get options chain
            options_data = self.schwab_api.get_options_chain(ticker)
            if options_data:
                self.logger.info(f"Retrieved options chain for {ticker}")
                return options_data
            else:
                self.logger.warning(f"No options data available for {ticker}")
                return None

        except Exception as e:
            self.logger.error(f"Failed to get options chain for {ticker}: {e}")
            return None


def main():
    """Command-line interface for LiveDataGatewayAgent"""
    import argparse

    parser = argparse.ArgumentParser(description="Live Data Gateway Agent - Real Schwab API")
    parser.add_argument("--tickers", nargs="+", required=True, help="List of ticker symbols")
    parser.add_argument("--source", choices=["auto", "schwab_production"], default="auto", help="Data source")
    parser.add_argument("--timeframe", default="1", help="Bar timeframe in minutes")

    args = parser.parse_args()

    # Create agent and execute
    agent = LiveDataGatewayAgent()

    try:
        result = agent.execute(
            ticker_list=args.tickers,
            source=args.source,
            bar_tf=args.timeframe
        )

        print("SUCCESS: REAL DATA INGESTION COMPLETED")
        print("=" * 50)
        print(f"Source: {result['source']}")
        print(f"Status: {result['status']}")
        print(f"Results: {len(result['results'])} tickers processed")

        print("\nData Summary:")
        for result_item in result['results']:
            if result_item['status'] == 'OK':
                print(f"  ✓ {result_item['ticker']}: {result_item['data_points']} data points")
            else:
                print(f"  ✗ {result_item['ticker']}: ERROR - {result_item['error']}")

        return 0

    except Exception as e:
        print(f"ERROR: Real data ingestion failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
