import json, os, re
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import date, datetime
from agents.agent_base import BaseAgent

class RiskGuardAgent(BaseAgent):
    task_id = "R-02"
    
    def __init__(self, agent_id="risk_guard_agent"):
        super().__init__(agent_id)
        
        # Initialize real-time data agent for live position monitoring
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.real_time_agent = EnhancedDataAgent()
            self.has_real_time = True
            self.logger.info("Risk Guard initialized with real-time position monitoring")
        except ImportError:
            self.real_time_agent = None
            self.has_real_time = False
            self.logger.warning("Real-time agent unavailable - using delayed position monitoring")
        
        # Spread thresholds for dynamic risk adjustment
        self.spread_thresholds = {
            'normal': 0.05,      # 5% bid-ask spread = normal
            'wide': 0.15,        # 15% = wide spreads
            'blown_out': 0.30    # 30% = blown out spreads
        }
    
    def _analyze_option_spreads(self, ticker: str) -> dict:
        """
        Analyze current option bid-ask spreads for dynamic risk adjustment
        Uses Schwab real-time data for spread analysis
        """
        try:
            # Get latest options data
            today = date.today().isoformat()
            opts_file = Path(f"data/live/{today}/{ticker}_options.parquet")
            
            if not opts_file.exists():
                self.logger.warning(f"No recent options data for {ticker}")
                return {'spread_condition': 'unknown', 'avg_spread_pct': 0.15}
            
            opts_df = pd.read_parquet(opts_file)
            
            if opts_df.empty:
                return {'spread_condition': 'unknown', 'avg_spread_pct': 0.15}
            
            # Calculate bid-ask spreads as percentage of mark price
            opts_df['spread_pct'] = ((opts_df['ask'] - opts_df['bid']) / 
                                   opts_df['mark'].replace(0, np.nan)).fillna(0)
            
            # Focus on near-term, liquid options (next 30 days, reasonable volume)
            if 'days_to_expiry' in opts_df.columns:
                liquid_opts = opts_df[
                    (opts_df['days_to_expiry'] <= 30) & 
                    (opts_df['days_to_expiry'] > 0) &
                    (opts_df['volume'] > 0)
                ]
            else:
                liquid_opts = opts_df[opts_df['volume'] > 0]
            
            if liquid_opts.empty:
                liquid_opts = opts_df  # Fall back to all options
            
            # Calculate spread statistics
            avg_spread_pct = liquid_opts['spread_pct'].mean()
            max_spread_pct = liquid_opts['spread_pct'].max()
            
            # Determine spread condition
            if avg_spread_pct <= self.spread_thresholds['normal']:
                spread_condition = 'normal'
            elif avg_spread_pct <= self.spread_thresholds['wide']:
                spread_condition = 'wide'
            else:
                spread_condition = 'blown_out'
            
            # Time-based adjustments (early morning = wider spreads expected)
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 10:  # Market open hour
                if spread_condition == 'normal' and avg_spread_pct > 0.08:
                    spread_condition = 'wide'  # More conservative in morning
            
            result = {
                'spread_condition': spread_condition,
                'avg_spread_pct': avg_spread_pct,
                'max_spread_pct': max_spread_pct,
                'liquid_options_count': len(liquid_opts),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"Spread analysis for {ticker}: {spread_condition} "
                           f"(avg: {avg_spread_pct:.1%}, max: {max_spread_pct:.1%})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to analyze spreads for {ticker}: {e}")
            return {'spread_condition': 'unknown', 'avg_spread_pct': 0.15}
    
    def _calculate_spread_adjusted_stops(self, limit_price: float, ticker: str, 
                                       base_stop_pct: float = 0.20) -> dict:
        """
        Calculate spread-adjusted stop losses
        Widens stops when spreads blow out to prevent premature exits
        """
        try:
            spread_analysis = self._analyze_option_spreads(ticker)
            spread_condition = spread_analysis['spread_condition']
            avg_spread_pct = spread_analysis['avg_spread_pct']
            
            # Base stop loss percentage
            base_stop_loss = limit_price * (1 - base_stop_pct)
            
            # Spread adjustment factors
            spread_adjustments = {
                'normal': 1.0,      # No adjustment
                'wide': 1.3,        # 30% wider stops
                'blown_out': 1.8,   # 80% wider stops  
                'unknown': 1.2      # Conservative default
            }
            
            adjustment_factor = spread_adjustments.get(spread_condition, 1.2)
            
            # Calculate adjusted stop
            adjusted_stop_pct = base_stop_pct * adjustment_factor
            adjusted_stop_loss = limit_price * (1 - adjusted_stop_pct)
            
            # Additional adjustment for extreme spreads
            if avg_spread_pct > 0.25:  # Very wide spreads
                extreme_adjustment = 1 + (avg_spread_pct - 0.25) * 2  # Scale with spread width
                adjusted_stop_pct *= extreme_adjustment
                adjusted_stop_loss = limit_price * (1 - adjusted_stop_pct)
            
            result = {
                'base_stop_loss': base_stop_loss,
                'adjusted_stop_loss': adjusted_stop_loss,
                'base_stop_pct': base_stop_pct,
                'adjusted_stop_pct': adjusted_stop_pct,
                'adjustment_factor': adjustment_factor,
                'spread_condition': spread_condition,
                'reasoning': f"Spreads are {spread_condition} ({avg_spread_pct:.1%}), "
                           f"widened stops by {adjustment_factor:.1f}x"
            }
            
            self.logger.info(f"Spread-adjusted stops for {ticker}: "
                           f"${adjusted_stop_loss:.2f} ({adjusted_stop_pct:.1%}) vs "
                           f"base ${base_stop_loss:.2f} ({base_stop_pct:.1%})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to calculate spread-adjusted stops: {e}")
            return {
                'base_stop_loss': limit_price * (1 - base_stop_pct),
                'adjusted_stop_loss': limit_price * (1 - base_stop_pct * 1.2),
                'reasoning': 'Error in spread analysis, used conservative default'
            }
    
    def _extract_limit_price(self, plan_text):
        """Extract limit price from execution plan"""
        # First check if action is AVOID
        if "Action: AVOID" in plan_text:
            return None
            
        # Try to find limit price
        m = re.search(r"Limit Price\s*:\s*\$(\d+\.?\d*)", plan_text)
        if m:
            return float(m.group(1))
            
        # Fallback: extract from other price patterns
        price_patterns = [
            r"Premium\s*:\s*\$(\d+\.?\d*)",
            r"Entry Price\s*:\s*\$(\d+\.?\d*)",
            r"Strike Price\s*:\s*\$(\d+\.?\d*)"
        ]
        
        for pattern in price_patterns:
            m = re.search(pattern, plan_text)
            if m:
                return float(m.group(1))
                
        return None
    
    def execute(self, ua_path, plan_path,
                *, account_equity=None, daily_loss_cap_pct=3):
        """Execute risk guard with position sizing and drawdown limits
        **ENHANCED: Real-time position monitoring with current candle data**
        """
        
        ua   = json.loads(Path(ua_path).read_text())
        plan = Path(plan_path).read_text()
        acct = account_equity or float(os.getenv("ACCOUNT_EQUITY", "25000"))
        ticker = ua.get('ticker', 'UNKNOWN')
        
        # Real-time risk assessment with current market data
        current_market_price = None
        real_time_risk_factor = 1.0
        
        if self.has_real_time:
            try:
                result = self.real_time_agent.get_market_data([ticker])
                
                if result and result.get("source") == "schwab_broker":
                    ticker_data = result["data"][ticker]
                    
                    if ticker_data.get("is_current_candle"):
                        current_market_price = ticker_data["last_price"]
                        bid = ticker_data.get("bid")
                        ask = ticker_data.get("ask")
                        
                        # Calculate real-time risk adjustment
                        if bid and ask:
                            spread_percentage = (ask - bid) / ask
                            # Higher spreads indicate higher risk - reduce position size
                            if spread_percentage > 0.02:  # 2% spread threshold
                                real_time_risk_factor = 0.8  # Reduce position by 20%
                                self.logger.warning(f"[{ticker}] High spread detected: {spread_percentage:.3f}, reducing position size")
                            
                        self.logger.info(f"[{ticker}] Real-time risk assessment: ${current_market_price:.2f}, factor: {real_time_risk_factor:.2f}")
                    else:
                        self.logger.info(f"[{ticker}] Using most recent available data for risk assessment")
                        
            except Exception as e:
                self.logger.warning(f"[{ticker}] Real-time risk assessment failed: {e}")
        
        # Check if action is AVOID - skip risk guard for avoided trades
        if "Action: AVOID" in plan:
            verdict = "SKIPPED - Action is AVOID"
            out_dir = Path(f"guarded/{date.today().isoformat()}/{ua['ticker']}")
            out_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy original plan as-is
            (out_dir / "execution_plan_ok.md").write_text(plan)
            
            risk_log = {
                "verdict": verdict,
                "action": "AVOID",
                "ticker": ua['ticker'],
                "account_equity": acct,
                "timestamp": f"{date.today().isoformat()}T{__import__('datetime').datetime.now().strftime('%H:%M:%S')}Z"
            }
            
            with open(out_dir / "risk_guard_log.json", "w") as f:
                json.dump(risk_log, f, indent=2)
            
            print(f"[R-02] Action: AVOID, verdict {verdict}")
            return str(out_dir / "execution_plan_ok.md")
        
        price = self._extract_limit_price(plan)
        
        if not price:
            raise ValueError("Could not extract limit price from execution plan")
        
        # --- position sizing with real-time risk adjustment & spread analysis ---
        
        # Get spread analysis for dynamic risk adjustment
        spread_info = self._analyze_option_spreads(ticker)
        spread_adjusted_stops = self._calculate_spread_adjusted_stops(price, ticker)
        
        max_risk_dollars = acct * 0.01                     # 1% per trade
        contract_cost    = price * 100                     # 1 contract
        base_qty = max(1, round(max_risk_dollars / contract_cost))
        
        # Apply spread-based risk adjustment
        spread_adjustment = 1.0
        if spread_info['spread_condition'] == 'wide':
            spread_adjustment = 0.8  # Reduce size by 20% for wide spreads
        elif spread_info['spread_condition'] == 'blown_out':
            spread_adjustment = 0.6  # Reduce size by 40% for blown out spreads
        
        # Combined risk adjustments
        total_risk_factor = real_time_risk_factor * spread_adjustment
        
        adjusted_qty = max(1, round(base_qty * total_risk_factor))
        
        # Apply combined risk adjustment (was using only real_time_risk_factor)
        qty = adjusted_qty
        
        # Update plan with spread-aware quantity and stop loss information
        adjusted_plan = re.sub(r"Quantity\s*:.*",
                               f"Quantity      : {qty} contracts   "
                               f"# 1% of equity (${max_risk_dollars:,.0f}) "
                               f"with spread-adjusted risk factor {total_risk_factor:.2f}",
                               plan)
        
        # Add spread-adjusted stop loss information to plan
        stop_info = (f"\n\n## Spread-Adjusted Risk Management\n"
                    f"- **Spread Condition**: {spread_info['spread_condition'].title()} "
                    f"({spread_info['avg_spread_pct']:.1%} average)\n"
                    f"- **Base Stop Loss**: ${spread_adjusted_stops['base_stop_loss']:.2f} "
                    f"({spread_adjusted_stops['base_stop_pct']:.1%})\n"
                    f"- **Adjusted Stop Loss**: ${spread_adjusted_stops['adjusted_stop_loss']:.2f} "
                    f"({spread_adjusted_stops['adjusted_stop_pct']:.1%})\n"
                    f"- **Reasoning**: {spread_adjusted_stops['reasoning']}\n")
        
        adjusted_plan += stop_info
        
        # --- daily draw-down check -------------------------------------------
        pnl_file = Path("risk_state/daily_loss.json")
        pnl_file.parent.mkdir(exist_ok=True)
        day_key = date.today().isoformat()
        day_loss = 0.0
        
        if pnl_file.exists():
            pnl_data = json.loads(pnl_file.read_text())
            day_loss = pnl_data.get(day_key, 0.0)
        
        daily_loss_limit = acct * (daily_loss_cap_pct / 100)
        if abs(day_loss) > daily_loss_limit:
            verdict = f"REJECTED  daily loss cap breached (${abs(day_loss):,.0f} > ${daily_loss_limit:,.0f})"
        else:
            verdict = "APPROVED"
        
        # --- outputs ---------------------------------------------------------
        out_dir = Path(f"guarded/{day_key}/{ua['ticker']}")
        out_dir.mkdir(parents=True, exist_ok=True)
        
        (out_dir / "execution_plan_ok.md").write_text(adjusted_plan)
        
        risk_log = {
            "verdict": verdict,
            "qty": qty,
            "account_equity": acct,
            "max_loss_today": day_loss,
            "daily_loss_cap_pct": daily_loss_cap_pct,
            "daily_loss_limit": daily_loss_limit,
            "limit_price": price,
            "contract_cost": contract_cost,
            "max_risk_dollars": max_risk_dollars,
            "ticker": ua['ticker'],
            "timestamp": f"{date.today().isoformat()}T{__import__('datetime').datetime.now().strftime('%H:%M:%S')}Z",
            # Enhanced spread-aware risk metrics
            "spread_analysis": spread_info,
            "spread_adjusted_stops": spread_adjusted_stops,
            "base_qty": base_qty,
            "real_time_risk_factor": real_time_risk_factor,
            "spread_adjustment": spread_adjustment,
            "total_risk_factor": total_risk_factor,
            "current_market_price": current_market_price
        }
        
        with open(out_dir / "risk_guard_log.json", "w") as f:
            json.dump(risk_log, f, indent=2)
        
        # Shadow mode logging - capture risk guard decisions with enhanced source ID
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            # Determine decision confidence based on risk factors
            risk_confidence = 1.0 - (total_risk_factor - 1.0) if total_risk_factor > 1.0 else 0.85
            is_rejected = verdict.startswith("REJECTED")
            
            signal_data = {
                'confidence': risk_confidence,
                'strength': 0.90,  # Risk guard high strength
                'execution_recommendation': 'avoid' if is_rejected else 'execute'
            }
            
            math_data = {
                'accuracy_score': 0.95,  # Risk guard high accuracy
                'precision': 0.0003
            }
            
            # ENHANCED MARKET CONTEXT WITH CLEAR SOURCE IDENTIFICATION
            market_context = {
                'system': 'RISK_MANAGEMENT_GUARDIAN',  # Clear source name
                'source_file': 'risk_guard_agent.py',
                'source_agent': 'RISK_GUARDIAN',
                'intelligence_type': 'RISK_ASSESSMENT_DECISION',
                'decision_source': 'risk_guard_agent',
                'data_provider': 'risk_calculation_engine',
                
                # Risk-specific context
                'ticker': ticker,
                'account_equity': acct,
                'position_size': qty,
                'total_risk_factor': total_risk_factor,
                'verdict': verdict,
                'rejected': is_rejected,
                'current_market_price': current_market_price,
                'real_time_risk_factor': real_time_risk_factor,
                'risk_assessment_details': {
                    'daily_loss_cap_applied': True,
                    'position_sizing_method': 'risk_adjusted',
                    'spread_analysis_included': True
                }
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'risk_guard_verdict', 'verdict': verdict, 'quantity': qty, 'source': 'RISK_GUARDIAN'},
                outcome=1.0 if not is_rejected else 0.0,  # Success/failure as outcome
                market_context=market_context
            )
            self.logger.info("Shadow mode: [RISK_GUARDIAN] decision logged")
            
        except Exception as e:
            self.logger.warning(f"Shadow mode logging failed: {e}")
        
        if verdict.startswith("REJECTED"):
            raise ValueError(verdict)
            
        print(f"[R-02] qty {qty}, verdict {verdict} -> ticket dispatched")
        return str(out_dir / "execution_plan_ok.md")
    
    def execute_task(self, task):
        """Execute task interface"""
        inputs = task.inputs
        return self.execute(
            inputs['unified_analysis_json'],
            inputs['execution_plan_md'],
            account_equity=inputs.get('account_equity'),
            daily_loss_cap_pct=inputs.get('daily_loss_cap_pct', 3)
        )
    
    def validate_inputs(self, task):
        """Validate task inputs"""
        required = ['unified_analysis_json', 'execution_plan_md']
        return all(key in task.inputs for key in required)
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        if outputs and "execution_plan_ok.md" in outputs:
            return {"risk_guard_success": 1.0}
        return {"risk_guard_success": 0.0}
