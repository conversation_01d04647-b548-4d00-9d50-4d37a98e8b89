"""
Base Strategy Interface

Defines the abstract base class for all liquidity-focused trading strategies.

NO PLACEHOLDERS. FULL IMPLEMENTATION. REAL DATA ONLY.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import logging
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)


class SignalDirection(Enum):
    """Signal direction enumeration."""
    LONG = "LONG"
    SHORT = "SHORT"
    NEUTRAL = "NEUTRAL"


class SignalStrength(Enum):
    """Signal strength enumeration."""
    WEAK = "WEAK"
    MODERATE = "MODERATE"
    STRONG = "STRONG"
    VERY_STRONG = "VERY_STRONG"


class LiquidityDivergenceType(Enum):
    """Types of liquidity divergences for strategy analysis."""
    FLOW_VELOCITY_BULLISH = "flow_velocity_bullish"
    FLOW_VELOCITY_BEARISH = "flow_velocity_bearish"
    VOLUME_PROFILE_BULLISH = "volume_profile_bullish"
    VOLUME_PROFILE_BEARISH = "volume_profile_bearish"
    OPTIONS_POSITIONING_BULLISH = "options_positioning_bullish"
    OPTIONS_POSITIONING_BEARISH = "options_positioning_bearish"
    INSTITUTIONAL_FLOW_BULLISH = "institutional_flow_bullish"
    INSTITUTIONAL_FLOW_BEARISH = "institutional_flow_bearish"


class LiquidityType(Enum):
    """Types of liquidity in the market."""
    BUY = "buy"
    SELL = "sell"
    NEUTRAL = "neutral"
    INSTITUTIONAL = "institutional"
    RETAIL = "retail"


@dataclass
class StrategySignal:
    """
    Standardized signal output from strategies.
    """
    ticker: str
    strategy_name: str
    direction: SignalDirection
    strength: SignalStrength
    confidence: float  # 0.0 to 1.0
    entry: float
    stop_loss: float
    take_profit: List[float]  # Multiple TP levels
    reason: str
    analysis: Dict[str, Any]
    risk_reward: float
    timestamp: datetime
    timeframe: str

    def to_dict(self) -> Dict[str, Any]:
        """Convert signal to dictionary."""
        return {
            'ticker': self.ticker,
            'strategy': self.strategy_name,
            'direction': self.direction.value,
            'strength': self.strength.value,
            'confidence': self.confidence,
            'entry': self.entry,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'target': self.take_profit[0] if self.take_profit else self.entry * 1.03,
            'reason': self.reason,
            'analysis': self.analysis,
            'risk_reward': self.risk_reward,
            'timestamp': self.timestamp.isoformat(),
            'timeframe': self.timeframe
        }


class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies.

    All strategies must inherit from this class and implement
    the required abstract methods.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the base strategy.

        Args:
            config: Strategy-specific configuration
        """
        self.name = self.__class__.__name__
        
        # Properly merge provided config with default config
        default_config = self._get_default_config()
        if config:
            # Deep merge: provided config overrides default config
            self.config = self._deep_merge_config(default_config, config)
        else:
            self.config = default_config
            
        self.is_enabled = self.config.get('enabled', True)
        self.min_confidence = self.config.get('min_confidence', 0.7)
        self.max_signals_per_ticker = self.config.get('max_signals_per_ticker', 3)

        logger.info(f"Initialized {self.name} strategy")

    def _deep_merge_config(self, default_config: Dict[str, Any], provided_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge provided config with default config.
        
        Args:
            default_config: Default configuration dictionary
            provided_config: User-provided configuration dictionary
            
        Returns:
            Merged configuration dictionary
        """
        import copy
        merged = copy.deepcopy(default_config)
        
        for key, value in provided_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                # Recursively merge nested dictionaries
                merged[key] = self._deep_merge_config(merged[key], value)
            else:
                # Override with provided value
                merged[key] = value
                
        return merged

    @abstractmethod
    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration for the strategy.

        Returns:
            Default configuration dictionary
        """
        pass

    @abstractmethod
    def analyze(self,
               ticker: str,
               data: Dict[str, Any],
               analysis_results: Dict[str, Any]) -> List[StrategySignal]:
        """
        Analyze data and generate trading signals.

        Args:
            ticker: Stock ticker symbol
            data: Market data including price and options
            analysis_results: Results from all analyzers

        Returns:
            List of strategy signals
        """
        pass

    def analyze_multi(self, data: Dict[str, Any]) -> List[StrategySignal]:
        """
        Analyze multiple tickers and generate signals.

        This is a convenience method for testing and factory usage.

        Args:
            data: Dictionary with ticker as key and market data as value

        Returns:
            List of all strategy signals
        """
        all_signals = []

        for ticker, ticker_data in data.items():
            try:
                # Extract analysis results if embedded in data
                analysis_results = {
                    'liquidity_analysis': ticker_data.get('liquidity_analysis', {}),
                    'options_flow': ticker_data.get('options_flow', {}),
                    'gex_analysis': ticker_data.get('gex_analysis', {}),
                    'flow_analysis': ticker_data.get('flow_analysis', {}),
                    'volume_analysis': ticker_data.get('volume_analysis', {}),
                    'divergence_analysis': ticker_data.get('divergence_analysis', {}),
                    'price_action': ticker_data.get('price_action', {}),
                    'institutional_flow': ticker_data.get('institutional_flow', {})
                }

                signals = self.analyze(ticker, ticker_data, analysis_results)
                if signals:
                    all_signals.extend(signals)

            except Exception as e:
                logger.error(f"Error analyzing {ticker} with {self.name}: {e}")
                # Log more details for debugging without breaking other strategies
                logger.debug(f"Available data keys for {ticker}: {list(ticker_data.keys())}")

        return all_signals

    def validate_data(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate input data has required fields for liquidity analysis.

        Args:
            data: Input data dictionary

        Returns:
            Tuple of (is_valid, error_message)
        """
        required_fields = ['price_data', 'current_price']

        for field in required_fields:
            if field not in data:
                return False, f"Missing required field: {field}"

        # Check price data is not empty
        price_data = data.get('price_data', {})
        if isinstance(price_data, pd.DataFrame):
            if price_data.empty:
                return False, "Price data is empty"
        elif not price_data:
            return False, "Price data is empty"

        # Validate liquidity-specific data if present
        if 'liquidity_levels' in data:
            levels = data['liquidity_levels']
            if not isinstance(levels, list):
                return False, "Liquidity levels must be a list"

        if 'volume_data' in data:
            volume_data = data['volume_data']
            if not isinstance(volume_data, dict):
                return False, "Volume data must be a dictionary"

        if 'flow_data' in data:
            flow_data = data['flow_data']
            if not isinstance(flow_data, dict):
                return False, "Flow data must be a dictionary"

        return True, ""

    def calculate_stop_loss(self,
                          entry: float,
                          direction: SignalDirection,
                          atr: Optional[float] = None,
                          support: Optional[float] = None,
                          resistance: Optional[float] = None) -> float:
        """
        Calculate stop loss for a position.

        Args:
            entry: Entry price
            direction: Trade direction
            atr: Average True Range
            support: Support level
            resistance: Resistance level

        Returns:
            Stop loss price
        """
        # Use ATR-based stop if available
        if atr:
            stop_distance = atr * self.config.get('stop_loss_atr', 2.0)
            if direction == SignalDirection.LONG:
                stop_loss = entry - stop_distance
            else:
                stop_loss = entry + stop_distance

        # Use structure-based stop if available
        elif direction == SignalDirection.LONG and support:
            stop_loss = support * 0.99  # Just below support
        elif direction == SignalDirection.SHORT and resistance:
            stop_loss = resistance * 1.01  # Just above resistance

        # Default percentage-based stop
        else:
            stop_percent = self.config.get('default_stop_percent', 0.02)
            if direction == SignalDirection.LONG:
                stop_loss = entry * (1 - stop_percent)
            else:
                stop_loss = entry * (1 + stop_percent)

        return round(stop_loss, 2)

    def calculate_take_profit(self,
                            entry: float,
                            stop_loss: float,
                            direction: SignalDirection,
                            resistance: Optional[float] = None,
                            support: Optional[float] = None) -> List[float]:
        """
        Calculate take profit levels.

        Args:
            entry: Entry price
            stop_loss: Stop loss price
            direction: Trade direction
            resistance: Resistance level for longs
            support: Support level for shorts

        Returns:
            List of take profit levels
        """
        risk = abs(entry - stop_loss)
        tp_levels = []

        # TP1: 1:1 risk/reward
        if direction == SignalDirection.LONG:
            tp1 = entry + risk
            # TP2: 2:1 or resistance
            tp2 = min(entry + (risk * 2), resistance) if resistance else entry + (risk * 2)
            # TP3: 3:1
            tp3 = entry + (risk * 3)
        else:
            tp1 = entry - risk
            # TP2: 2:1 or support
            tp2 = max(entry - (risk * 2), support) if support else entry - (risk * 2)
            # TP3: 3:1
            tp3 = entry - (risk * 3)

        tp_levels = [round(tp1, 2), round(tp2, 2), round(tp3, 2)]

        # Ensure TPs are in correct order
        if direction == SignalDirection.LONG:
            tp_levels.sort()
        else:
            tp_levels.sort(reverse=True)

        return tp_levels

    def calculate_confidence(self, factors: Dict[str, float]) -> float:
        """
        Calculate overall confidence score from multiple factors.

        Args:
            factors: Dictionary of factor_name -> score (0-1)

        Returns:
            Overall confidence score (0-1)
        """
        if not factors:
            return 0.0

        # Weight factors based on strategy
        weights = self.config.get('confidence_weights', {})

        weighted_sum = 0.0
        total_weight = 0.0

        for factor, score in factors.items():
            weight = weights.get(factor, 1.0)
            weighted_sum += score * weight
            total_weight += weight

        confidence = weighted_sum / total_weight if total_weight > 0 else 0.0

        return min(1.0, max(0.0, confidence))

    def determine_strength(self, confidence: float) -> SignalStrength:
        """
        Determine signal strength from confidence score.

        Args:
            confidence: Confidence score (0-1)

        Returns:
            Signal strength enum
        """
        if confidence >= 0.9:
            return SignalStrength.VERY_STRONG
        elif confidence >= 0.8:
            return SignalStrength.STRONG
        elif confidence >= 0.7:
            return SignalStrength.MODERATE
        else:
            return SignalStrength.WEAK

    def create_signal(self,
                     ticker: str,
                     direction: SignalDirection,
                     entry: float,
                     stop_loss: float,
                     take_profit: List[float],
                     confidence: float,
                     reason: str,
                     analysis: Dict[str, Any],
                     timeframe: str = "1d") -> StrategySignal:
        """
        Create a standardized strategy signal.

        Args:
            ticker: Stock ticker
            direction: Trade direction
            entry: Entry price
            stop_loss: Stop loss price
            take_profit: Take profit levels
            confidence: Confidence score
            reason: Signal reason
            analysis: Detailed analysis
            timeframe: Timeframe

        Returns:
            StrategySignal object
        """
        # Calculate risk/reward
        risk = abs(entry - stop_loss)
        reward = abs(take_profit[0] - entry) if take_profit else risk
        risk_reward = reward / risk if risk > 0 else 0

        return StrategySignal(
            ticker=ticker,
            strategy_name=self.name,
            direction=direction,
            strength=self.determine_strength(confidence),
            confidence=confidence,
            entry=entry,
            stop_loss=stop_loss,
            take_profit=take_profit,
            reason=reason,
            analysis=analysis,
            risk_reward=risk_reward,
            timestamp=datetime.now(),
            timeframe=timeframe
        )

    def find_nearest_liquidity_level(self,
                                   price: float,
                                   levels: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        Find the nearest liquidity level to a given price.

        Args:
            price: Target price
            levels: List of liquidity level dictionaries

        Returns:
            Nearest liquidity level or None
        """
        if not levels:
            return None

        nearest = None
        min_distance = float('inf')

        for level in levels:
            level_price = level.get('price', 0)
            distance = abs(price - level_price)

            if distance < min_distance:
                min_distance = distance
                nearest = level

        return nearest

    def calculate_liquidity_confluence(self,
                                     price: float,
                                     levels: List[Dict[str, Any]],
                                     threshold: float = 0.02) -> float:
        """
        Calculate confluence factor based on proximity to liquidity levels.

        Args:
            price: Current price
            levels: List of liquidity levels
            threshold: Distance threshold (as percentage)

        Returns:
            Confluence factor (0.0 to 1.0)
        """
        if not levels:
            return 0.0

        nearby_levels = []
        for level in levels:
            level_price = level.get('price', 0)
            distance = abs(price - level_price) / price

            if distance <= threshold:
                strength = level.get('strength', 0.5)
                nearby_levels.append(strength)

        if not nearby_levels:
            return 0.0

        # Average strength of nearby levels
        return min(1.0, sum(nearby_levels) / len(nearby_levels))

    def analyze_flow_divergence(self,
                              price_data: pd.DataFrame,
                              flow_data: Dict[str, Any]) -> Dict[str, float]:
        """
        Analyze divergence between price and flow data.

        Args:
            price_data: Price DataFrame with OHLCV data
            flow_data: Flow analysis results

        Returns:
            Dictionary with divergence metrics
        """
        if len(price_data) < 5:
            return {'divergence_strength': 0.0, 'flow_direction': 0.0}

        # Calculate recent price trend
        recent_bars = min(10, len(price_data))
        price_change = (price_data['close'].iloc[-1] - price_data['close'].iloc[-recent_bars]) / price_data['close'].iloc[-recent_bars]

        # Get flow direction from flow data
        flow_direction = flow_data.get('net_flow_direction', 0.0)
        flow_strength = flow_data.get('flow_strength', 0.0)

        # Calculate divergence (opposite directions indicate divergence)
        divergence_strength = 0.0
        if (price_change > 0.01 and flow_direction < -0.1) or (price_change < -0.01 and flow_direction > 0.1):
            divergence_strength = min(1.0, abs(price_change) + abs(flow_direction)) * flow_strength

        return {
            'divergence_strength': divergence_strength,
            'flow_direction': flow_direction,
            'price_change': price_change,
            'flow_strength': flow_strength
        }

    def calculate_atr(self, price_data: pd.DataFrame, period: int = 14) -> float:
        """
        Calculate Average True Range - shared utility for all strategies.

        Args:
            price_data: DataFrame with OHLC data
            period: ATR calculation period

        Returns:
            ATR value
        """
        if len(price_data) < period:
            # Fallback to simple range calculation
            return (price_data['high'].mean() - price_data['low'].mean())

        high = price_data['high'].tail(period)
        low = price_data['low'].tail(period)
        close = price_data['close'].tail(period)

        # True Range calculation
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.mean()

        return atr

    def calculate_price_momentum(self, price_data: pd.DataFrame, period: int = 10) -> float:
        """
        Calculate price momentum - shared utility for all strategies.

        Args:
            price_data: DataFrame with price data
            period: Momentum calculation period

        Returns:
            Momentum as rate of change
        """
        if len(price_data) < period:
            return 0.0

        # Calculate rate of change
        close_prices = price_data['close'].tail(period)
        momentum = (close_prices.iloc[-1] - close_prices.iloc[0]) / close_prices.iloc[0]

        return momentum

    def detect_volume_surge(self,
                          volume_data: pd.DataFrame,
                          current_volume: float,
                          lookback: int = 20) -> Dict[str, float]:
        """
        Detect volume surges - shared utility for all strategies.

        Args:
            volume_data: DataFrame with volume data
            current_volume: Current bar volume
            lookback: Period for average calculation

        Returns:
            Dictionary with volume metrics
        """
        if len(volume_data) < lookback:
            return {'volume_ratio': 1.0, 'volume_percentile': 0.5}

        recent_volume = volume_data['volume'].tail(lookback)
        avg_volume = recent_volume.mean()
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0

        # Calculate percentile
        volume_percentile = (recent_volume < current_volume).sum() / len(recent_volume)

        return {
            'volume_ratio': volume_ratio,
            'volume_percentile': volume_percentile,
            'avg_volume': avg_volume,
            'volume_surge': volume_ratio > 1.5
        }

    def get_timeframe_priority(self, timeframe: str) -> int:
        """
        Get priority weight for different timeframes - shared utility.

        Args:
            timeframe: Timeframe string (e.g., '1d', '4h', '1h')

        Returns:
            Priority weight (higher = more important)
        """
        timeframe_weights = {
            '1d': 100,    # Daily - highest priority
            '4h': 80,     # 4-hour - high priority
            '1h': 60,     # 1-hour - medium priority
            '30m': 40,    # 30-minute - lower priority
            '15m': 30     # 15-minute - minimum timeframe for liquidity analysis
        }
        return timeframe_weights.get(timeframe, 50)  # Default medium priority

    def validate_timeframe_data(self, data: Dict[str, Any], required_timeframes: List[str]) -> bool:
        """
        Validate that required timeframe data is available.

        Args:
            data: Market data dictionary
            required_timeframes: List of required timeframes

        Returns:
            True if all required timeframes have data
        """
        price_data = data.get('price_data', {})

        for tf in required_timeframes:
            tf_data = price_data.get(tf)
            if tf_data is None or (hasattr(tf_data, 'empty') and tf_data.empty):
                logger.debug(f"Missing or empty data for timeframe {tf}")
                return False

        return True

    def filter_signals(self, signals: List[StrategySignal]) -> List[StrategySignal]:
        """
        Filter signals based on strategy criteria.

        Args:
            signals: List of generated signals

        Returns:
            Filtered list of signals
        """
        # Filter by minimum confidence
        filtered = [s for s in signals if s.confidence >= self.min_confidence]

        # Sort by confidence
        filtered.sort(key=lambda x: x.confidence, reverse=True)

        # Limit signals per ticker
        ticker_counts = {}
        final_signals = []

        for signal in filtered:
            count = ticker_counts.get(signal.ticker, 0)
            if count < self.max_signals_per_ticker:
                final_signals.append(signal)
                ticker_counts[signal.ticker] = count + 1

        return final_signals