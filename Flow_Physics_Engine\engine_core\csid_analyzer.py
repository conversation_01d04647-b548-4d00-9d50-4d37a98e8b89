"""CSID (Cumulative Volume Delta Institutional Divergence) Analyzer - External Engine

Implements CSID calculations to detect institutional divergence from retail flow patterns.
Identifies smart money accumulation/distribution through volume delta analysis.

Mathematical Foundation:
- Cumulative Volume Delta = Sum(Volume * (Close - Open) / (High - Low))
- Institutional Divergence = CSID vs Price divergence detection
- Smart Money Flow = Institutional buying/selling pressure measurement
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class CSIDResult:
    """CSID analysis result structure."""
    timestamp: datetime
    symbol: str
    
    # Core CSID metrics
    cumulative_delta: float
    delta_velocity: float
    delta_acceleration: float
    institutional_divergence: float
    
    # Smart money detection
    smart_money_flow: float
    institutional_bias: str  # 'accumulation', 'distribution', 'neutral'
    smart_money_strength: float
    
    # Divergence analysis
    price_csid_divergence: bool
    divergence_strength: float
    divergence_direction: str  # 'bullish', 'bearish', 'none'
    
    # Volume profile analysis
    delta_volume_ratio: float
    institutional_participation: float
    
    # Quality metrics
    data_quality: float
    confidence_score: float
    
    # Metadata
    analysis_metadata: Dict[str, Any]


class CSIDAnalyzer:
    """External Engine CSID Analyzer for institutional flow detection."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize CSID analyzer."""
        self.config = {
            'lookback_periods': 50,
            'divergence_threshold': 0.15,
            'institutional_velocity_threshold': 0.2,
            'smart_money_strength_threshold': 0.3,
            'delta_smoothing_factor': 0.7,
            'divergence_confirmation_periods': 5,
            'volume_delta_min_threshold': 1000,
            'institutional_participation_threshold': 0.4
        }
        if config:
            self.config.update(config)
        
        # State tracking
        self._csid_history = {}
        self._price_history = {}
        self._divergence_tracking = {}
        
        logger.info("External CSID Analyzer initialized")
    
    def calculate_csid(self, symbol: str, price_data: pd.DataFrame) -> CSIDResult:
        """Calculate CSID metrics for given price/volume data."""
        try:
            if price_data.empty:
                return self._create_default_result(symbol)
            
            timestamp = datetime.now()
            
            # Calculate volume delta
            volume_delta = self._calculate_volume_delta(price_data)
            
            # Calculate cumulative delta
            cumulative_delta = self._calculate_cumulative_delta(symbol, volume_delta)
            
            # Calculate CSID derivatives
            delta_velocity, delta_acceleration = self._calculate_csid_derivatives(symbol)
            
            # Detect institutional divergence
            divergence_metrics = self._detect_institutional_divergence(
                symbol, price_data, cumulative_delta
            )
            
            # Analyze smart money flow
            smart_money_metrics = self._analyze_smart_money_flow(
                volume_delta, cumulative_delta, delta_velocity
            )
            
            # Calculate volume profile metrics
            volume_metrics = self._analyze_volume_profile(price_data, volume_delta)
            
            # Calculate quality scores
            quality_metrics = self._calculate_quality_metrics(
                symbol, price_data, volume_delta
            )
            
            # Create result
            result = CSIDResult(
                timestamp=timestamp,
                symbol=symbol,
                cumulative_delta=cumulative_delta,
                delta_velocity=delta_velocity,
                delta_acceleration=delta_acceleration,
                institutional_divergence=divergence_metrics['divergence_strength'],
                smart_money_flow=smart_money_metrics['flow'],
                institutional_bias=smart_money_metrics['bias'],
                smart_money_strength=smart_money_metrics['strength'],
                price_csid_divergence=divergence_metrics['divergence_detected'],
                divergence_strength=divergence_metrics['divergence_strength'],
                divergence_direction=divergence_metrics['direction'],
                delta_volume_ratio=volume_metrics['delta_ratio'],
                institutional_participation=volume_metrics['institutional_participation'],
                data_quality=quality_metrics['data_quality'],
                confidence_score=quality_metrics['confidence'],
                analysis_metadata={
                    'lookback_size': len(self._csid_history.get(symbol, [])),
                    'divergence_periods': divergence_metrics.get('periods', 0),
                    'volume_delta_sum': np.sum(volume_delta),
                    'config': self.config
                }
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating CSID for {symbol}: {e}")
            return self._create_default_result(symbol)
    
    def _calculate_volume_delta(self, price_data: pd.DataFrame) -> np.ndarray:
        """Calculate volume delta for each bar."""
        try:
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in price_data.columns for col in required_cols):
                logger.warning("Missing required OHLCV columns for volume delta calculation")
                return np.zeros(len(price_data))
            
            price_range = price_data['high'] - price_data['low']
            price_range = np.where(price_range == 0, 1e-6, price_range)
            
            price_movement = price_data['close'] - price_data['open']
            volume_delta = price_data['volume'] * (price_movement / price_range)
            
            smoothing_factor = self.config['delta_smoothing_factor']
            if len(volume_delta) > 1:
                volume_delta = pd.Series(volume_delta).ewm(alpha=1-smoothing_factor).mean().values
            
            return volume_delta
            
        except Exception as e:
            logger.error(f"Error calculating volume delta: {e}")
            return np.zeros(len(price_data))
    
    def _calculate_cumulative_delta(self, symbol: str, volume_delta: np.ndarray) -> float:
        """Calculate cumulative volume delta."""
        try:
            if symbol not in self._csid_history:
                self._csid_history[symbol] = []
            
            for delta in volume_delta:
                self._csid_history[symbol].append({
                    'timestamp': datetime.now(),
                    'delta': delta
                })
            
            max_history = self.config['lookback_periods'] * 2
            if len(self._csid_history[symbol]) > max_history:
                self._csid_history[symbol] = self._csid_history[symbol][-max_history:]
            
            total_delta = sum(item['delta'] for item in self._csid_history[symbol])
            return total_delta
            
        except Exception as e:
            logger.error(f"Error calculating cumulative delta: {e}")
            return 0.0
    
    def _calculate_csid_derivatives(self, symbol: str) -> Tuple[float, float]:
        """Calculate CSID velocity and acceleration."""
        try:
            history = self._csid_history.get(symbol, [])
            
            if len(history) < 3:
                return 0.0, 0.0
            
            recent_deltas = [item['delta'] for item in history[-10:]]
            recent_times = [item['timestamp'].timestamp() for item in history[-10:]]
            
            velocity = self._calculate_derivative(recent_deltas, recent_times, order=1)
            acceleration = self._calculate_derivative(recent_deltas, recent_times, order=2)
            
            return velocity, acceleration
            
        except Exception as e:
            logger.error(f"Error calculating CSID derivatives: {e}")
            return 0.0, 0.0
    
    def _calculate_derivative(self, values: List[float], timestamps: List[float], order: int) -> float:
        """Calculate nth derivative of values over time."""
        try:
            if len(values) < order + 1:
                return 0.0
            
            result = np.array(values, dtype=float)
            time_array = np.array(timestamps, dtype=float)
            
            for _ in range(order):
                if len(result) < 2:
                    return 0.0
                
                value_diff = np.diff(result)
                time_diff = np.diff(time_array[:len(result)])
                time_diff[time_diff == 0] = 1e-6
                
                result = value_diff / time_diff
                time_array = time_array[1:len(result)+1]
            
            return float(result[-1]) if len(result) > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating derivative: {e}")
            return 0.0
    
    def _detect_institutional_divergence(self, symbol: str, price_data: pd.DataFrame, 
                                       cumulative_delta: float) -> Dict[str, Any]:
        """Detect divergence between price and CSID."""
        try:
            if symbol not in self._divergence_tracking:
                self._divergence_tracking[symbol] = {
                    'price_history': [],
                    'csid_history': []
                }
            
            current_price = price_data['close'].iloc[-1] if not price_data.empty else 0
            tracking = self._divergence_tracking[symbol]
            
            tracking['price_history'].append(current_price)
            tracking['csid_history'].append(cumulative_delta)
            
            max_periods = self.config['lookback_periods']
            if len(tracking['price_history']) > max_periods:
                tracking['price_history'] = tracking['price_history'][-max_periods:]
                tracking['csid_history'] = tracking['csid_history'][-max_periods:]
            
            if len(tracking['price_history']) < self.config['divergence_confirmation_periods']:
                return {
                    'divergence_detected': False,
                    'divergence_strength': 0.0,
                    'direction': 'none',
                    'periods': 0
                }
            
            price_trend = self._calculate_trend(tracking['price_history'])
            csid_trend = self._calculate_trend(tracking['csid_history'])
            
            divergence_detected = False
            divergence_strength = 0.0
            direction = 'none'
            
            # Bullish divergence: price declining, CSID rising
            if price_trend < -self.config['divergence_threshold'] and csid_trend > self.config['divergence_threshold']:
                divergence_detected = True
                divergence_strength = abs(price_trend) + abs(csid_trend)
                direction = 'bullish'
            
            # Bearish divergence: price rising, CSID declining
            elif price_trend > self.config['divergence_threshold'] and csid_trend < -self.config['divergence_threshold']:
                divergence_detected = True
                divergence_strength = abs(price_trend) + abs(csid_trend)
                direction = 'bearish'
            
            return {
                'divergence_detected': divergence_detected,
                'divergence_strength': min(1.0, divergence_strength),
                'direction': direction,
                'periods': len(tracking['price_history']),
                'price_trend': price_trend,
                'csid_trend': csid_trend
            }
            
        except Exception as e:
            logger.error(f"Error detecting institutional divergence: {e}")
            return {
                'divergence_detected': False,
                'divergence_strength': 0.0,
                'direction': 'none',
                'periods': 0
            }
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend strength for a series of values."""
        try:
            if len(values) < 2:
                return 0.0
            
            x = np.arange(len(values))
            y = np.array(values)
            
            slope = np.polyfit(x, y, 1)[0]
            
            value_range = max(values) - min(values)
            if value_range > 0:
                normalized_slope = slope / value_range
            else:
                normalized_slope = 0.0
            
            return max(-1.0, min(1.0, normalized_slope * len(values)))
            
        except Exception as e:
            logger.error(f"Error calculating trend: {e}")
            return 0.0
    
    def _analyze_smart_money_flow(self, volume_delta: np.ndarray, cumulative_delta: float, 
                                delta_velocity: float) -> Dict[str, Any]:
        """Analyze smart money flow patterns."""
        try:
            recent_delta_sum = np.sum(volume_delta[-5:]) if len(volume_delta) >= 5 else np.sum(volume_delta)
            flow_strength = abs(recent_delta_sum) / (np.sum(np.abs(volume_delta)) + 1e-6)
            
            if cumulative_delta > 0 and delta_velocity > self.config['institutional_velocity_threshold']:
                bias = 'accumulation'
                strength = min(1.0, (cumulative_delta + delta_velocity) / 2)
            elif cumulative_delta < 0 and delta_velocity < -self.config['institutional_velocity_threshold']:
                bias = 'distribution'
                strength = min(1.0, (abs(cumulative_delta) + abs(delta_velocity)) / 2)
            else:
                bias = 'neutral'
                strength = flow_strength
            
            smart_money_strength = min(1.0, flow_strength * (1 + abs(delta_velocity)))
            
            return {
                'flow': recent_delta_sum,
                'bias': bias,
                'strength': smart_money_strength,
                'flow_strength': flow_strength
            }
            
        except Exception as e:
            logger.error(f"Error analyzing smart money flow: {e}")
            return {
                'flow': 0.0,
                'bias': 'neutral',
                'strength': 0.0,
                'flow_strength': 0.0
            }
    
    def _analyze_volume_profile(self, price_data: pd.DataFrame, volume_delta: np.ndarray) -> Dict[str, Any]:
        """Analyze volume profile and institutional participation."""
        try:
            if price_data.empty or len(volume_delta) == 0:
                return {
                    'delta_ratio': 0.0,
                    'institutional_participation': 0.0
                }
            
            total_volume = np.sum(price_data['volume'].values)
            total_delta = np.sum(np.abs(volume_delta))
            delta_ratio = total_delta / (total_volume + 1e-6)
            
            institutional_participation = min(1.0, delta_ratio * 2)
            
            return {
                'delta_ratio': delta_ratio,
                'institutional_participation': institutional_participation
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume profile: {e}")
            return {
                'delta_ratio': 0.0,
                'institutional_participation': 0.0
            }
    
    def _calculate_quality_metrics(self, symbol: str, price_data: pd.DataFrame, 
                                 volume_delta: np.ndarray) -> Dict[str, Any]:
        """Calculate data quality and confidence metrics."""
        try:
            data_quality = 1.0
            
            if price_data.empty:
                data_quality *= 0.3
            elif len(price_data) < 10:
                data_quality *= 0.6
            
            zero_volume_ratio = np.sum(price_data['volume'] == 0) / len(price_data) if not price_data.empty else 1.0
            data_quality *= (1 - zero_volume_ratio * 0.5)
            
            if len(volume_delta) > 0:
                delta_std = np.std(volume_delta)
                delta_mean = np.mean(volume_delta)
                outlier_threshold = 3 * delta_std
                outlier_ratio = np.sum(np.abs(volume_delta - delta_mean) > outlier_threshold) / len(volume_delta)
                data_quality *= (1 - outlier_ratio * 0.3)
            
            history_size = len(self._csid_history.get(symbol, []))
            if history_size < 10:
                confidence = 0.4
            elif history_size < 30:
                confidence = 0.7
            else:
                confidence = 0.9
            
            confidence *= data_quality
            
            return {
                'data_quality': max(0.1, min(1.0, data_quality)),
                'confidence': max(0.1, min(1.0, confidence))
            }
            
        except Exception as e:
            logger.error(f"Error calculating quality metrics: {e}")
            return {
                'data_quality': 0.5,
                'confidence': 0.5
            }
    
    def _create_default_result(self, symbol: str) -> CSIDResult:
        """Create default CSID result when calculation fails."""
        return CSIDResult(
            timestamp=datetime.now(),
            symbol=symbol,
            cumulative_delta=0.0,
            delta_velocity=0.0,
            delta_acceleration=0.0,
            institutional_divergence=0.0,
            smart_money_flow=0.0,
            institutional_bias='neutral',
            smart_money_strength=0.0,
            price_csid_divergence=False,
            divergence_strength=0.0,
            divergence_direction='none',
            delta_volume_ratio=0.0,
            institutional_participation=0.0,
            data_quality=0.1,
            confidence_score=0.1,
            analysis_metadata={}
        )
    
    def clear_history(self, symbol: Optional[str] = None):
        """Clear historical data for symbol or all symbols."""
        if symbol:
            if symbol in self._csid_history:
                del self._csid_history[symbol]
            if symbol in self._price_history:
                del self._price_history[symbol]
            if symbol in self._divergence_tracking:
                del self._divergence_tracking[symbol]
        else:
            self._csid_history.clear()
            self._price_history.clear()
            self._divergence_tracking.clear()
