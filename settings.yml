profiles:
  vm_iso:
    account_equity: 25000
    data_source: "schwab"
    data_fallback: "polygon"
    agent_zero_mode: "shadow"       # {off|shadow|active} - learn but don't veto
    schedule_cron: "*/30 13-20 * * 1-5"
    tradier_token_env: "TRADIER_TOKEN"
    fills_dir: "/home/<USER>/fills"
    logs_dir: "/home/<USER>/logs"
    training_logs_dir: "/home/<USER>/training_logs"
    
  win_quick:
    account_equity: 10000
    data_source: "polygon"
    agent_zero_mode: "shadow"       # shadow mode for training data collection
    schedule_cron: null             # no scheduler; manual run
    tradier_token_env: null         # manual tickets, no auto-broker
    fills_dir: "D:\\script-work\\CORE\\fills"
    logs_dir: "D:\\script-work\\CORE\\logs"
    training_logs_dir: "D:\\script-work\\CORE\\training_logs"
