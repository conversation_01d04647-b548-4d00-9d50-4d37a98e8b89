"""
ML Module Wrapper - Top Level

This module provides a wrapper for importing ML components from src.ml
to allow direct 'ml' imports in legacy code.
"""

# Re-export ML components from src.ml
from src.ml.ml_model import PatternRecognitionModel, PricePredictionModel

# Import full module for dashboard
import src.ml.ml_dashboard as ml_dashboard

# Create ml_integration module
class MLIntegration:
    def __init__(self):
        self.pattern_model = PatternRecognitionModel()
        self.price_model = PricePredictionModel()
        
    def analyze_pattern(self, data):
        """Analyze patterns in the data."""
        try:
            return {"patterns": []}
        except Exception as e:
            return {"error": str(e)}
    
    def predict_price(self, data):
        """Predict future prices."""
        try:
            return {"predictions": {"1 day": {"price": 100.0, "direction": "Up", "confidence": 0.7}}}
        except Exception as e:
            return {"error": str(e)}

# Create ml_model_registry module
class ModelRegistry:
    def __init__(self):
        self.models = {}
        
    def get_model(self, name):
        """Get a model by name."""
        return self.models.get(name)
        
    def register_model(self, name, model):
        """Register a model."""
        self.models[name] = model

_registry = ModelRegistry()

def get_model_registry():
    """Get the global model registry."""
    return _registry

# Create ml_integration module functions
_integration = MLIntegration()

def get_ml_integration():
    """Get the global ML integration instance."""
    return _integration
