#!/usr/bin/env python3
"""
CORE System Cleanup Executor
===========================
Execute the cleanup plan to remove all redundant test/diagnostic scripts.
"""

import json
from pathlib import Path
from core_cleanup_analyzer import CoreSystemAnalyzer

def execute_actual_cleanup():
    """Execute the actual cleanup operation"""
    
    print("CORE SYSTEM CLEANUP EXECUTION")
    print("="*50)
    
    # Load the analyzer
    analyzer = CoreSystemAnalyzer()
    
    # Perform analysis first
    analyzer.analyze_directory_structure()
    
    # Show summary before cleanup
    summary = analyzer.analysis_results['cleanup_summary']
    print(f"About to remove {summary['total_candidates']} files")
    print(f"This will free {summary['total_size_mb']} MB of disk space")
    print("\nBreakdown:")
    for reason, stats in summary['breakdown_by_reason'].items():
        print(f"  - {reason}: {stats['count']} files")
    
    # Execute the cleanup
    print("\nExecuting ACTUAL cleanup...")
    cleanup_results = analyzer.execute_cleanup(dry_run=False)
    
    # Report results
    print(f"\nCLEANUP COMPLETE!")
    print(f"Files removed: {cleanup_results['removed_files']}")
    print(f"Files preserved: {cleanup_results['preserved_files']}")
    print(f"Errors: {cleanup_results['errors']}")
    print(f"Disk space freed: {cleanup_results['total_size_freed_mb']} MB")
    
    # Save final results
    results_file = analyzer.core_path / "CORE_CLEANUP_RESULTS.json"
    with open(results_file, 'w') as f:
        json.dump(cleanup_results, f, indent=2)
    
    print(f"\nResults saved to: {results_file}")
    
    # Show any errors
    if cleanup_results['errors']:
        print("\nErrors encountered:")
        for error in cleanup_results['errors']:
            print(f"  - {error['path']}: {error['error']}")
    
    return cleanup_results

if __name__ == "__main__":
    execute_actual_cleanup()
