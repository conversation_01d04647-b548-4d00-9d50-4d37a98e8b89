#!/usr/bin/env python3
"""
CORE Flow Detection System - Data Handler

Essential data structures and processing functions.
Simple multi-timeframe aggregation with minimal complexity.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from data.factor_spec import TimeFrame

class DataHandler:
    """
    Clean data processing and validation.
    Essential functionality only - no bloat.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.required_columns = ['open', 'high', 'low', 'close', 'volume']
        self.timeframe_mapping = {
            '1m': TimeFrame.MIN_1,
            '5m': TimeFrame.MIN_5, 
            '15m': TimeFrame.MIN_15,
            '30m': TimeFrame.MIN_30,
            '1h': TimeFrame.HOUR_1,
            '4h': TimeFrame.HOUR_4,
            '1d': TimeFrame.DAY_1,
            '1w': TimeFrame.WEEK_1
        }
    
    def validate_ohlcv_data(self, data: pd.DataFrame, min_bars: int = 50) -> <PERSON><PERSON>[bool, str]:
        """
        Validate OHLCV data quality.
        
        Args:
            data: OHLCV DataFrame
            min_bars: Minimum bars required
            
        Returns:
            tuple: (is_valid, error_message)
        """
        try:
            # Check if DataFrame is valid
            if data is None or data.empty:
                return False, "Data is empty"
            
            # Check required columns
            missing_cols = [col for col in self.required_columns if col not in data.columns]
            if missing_cols:
                return False, f"Missing columns: {missing_cols}"
            
            # Check minimum bars
            if len(data) < min_bars:
                return False, f"Insufficient data: {len(data)} bars, need {min_bars}"
            
            # Check for excessive NaN values
            for col in self.required_columns:
                nan_pct = data[col].isna().sum() / len(data)
                if nan_pct > 0.05:  # 5% threshold
                    return False, f"Too many NaN values in {col}: {nan_pct:.1%}"
            
            # Check for negative prices/volumes
            if (data[['open', 'high', 'low', 'close']] <= 0).any().any():
                return False, "Negative or zero prices detected"
            
            if (data['volume'] < 0).any():
                return False, "Negative volume detected"
            
            # Check OHLC relationships
            ohlc_invalid = (
                (data['high'] < data['low']) |
                (data['high'] < data['open']) |
                (data['high'] < data['close']) |
                (data['low'] > data['open']) |
                (data['low'] > data['close'])
            ).any()
            
            if ohlc_invalid:
                return False, "Invalid OHLC relationships detected"
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    def clean_ohlcv_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Clean OHLCV data with minimal processing.
        
        Args:
            data: Raw OHLCV DataFrame
            
        Returns:
            pd.DataFrame: Cleaned data
        """
        if data is None or data.empty:
            return data
        
        # Make a copy to avoid modifying original
        cleaned = data.copy()
        
        # Ensure proper data types
        for col in ['open', 'high', 'low', 'close']:
            cleaned[col] = pd.to_numeric(cleaned[col], errors='coerce')
        cleaned['volume'] = pd.to_numeric(cleaned['volume'], errors='coerce')
        
        # Forward fill small gaps (max 3 consecutive NaN)
        for col in self.required_columns:
            cleaned[col] = cleaned[col].fillna(method='ffill', limit=3)
        
        # Drop rows with remaining NaN values
        cleaned = cleaned.dropna(subset=self.required_columns)
        
        # Sort by index (timestamp) if needed
        if not cleaned.index.is_monotonic_increasing:
            cleaned = cleaned.sort_index()
        
        return cleaned
    
    def aggregate_timeframes(self, data_1m: pd.DataFrame, 
                           target_timeframes: List[str]) -> Dict[str, pd.DataFrame]:
        """
        Aggregate 1-minute data to multiple timeframes.
        
        Args:
            data_1m: 1-minute OHLCV data
            target_timeframes: List of target timeframes
            
        Returns:
            Dict: {timeframe: aggregated_data}
        """
        result = {}
        
        if data_1m is None or data_1m.empty:
            return result
        
        # Add 1m data if requested
        if '1m' in target_timeframes:
            result['1m'] = data_1m.copy()
        
        # Aggregation rules
        agg_rules = {
            'open': 'first',
            'high': 'max', 
            'low': 'min',
            'close': 'last',
            'volume': 'sum'
        }
        
        # Timeframe mapping for pandas resample
        freq_mapping = {
            '5m': '5T',
            '15m': '15T', 
            '30m': '30T',
            '1h': '1H',
            '4h': '4H',
            '1d': '1D',
            '1w': '1W'
        }
        
        for tf in target_timeframes:
            if tf == '1m':
                continue
                
            if tf not in freq_mapping:
                continue
                
            try:
                # Resample and aggregate
                agg_data = data_1m.resample(freq_mapping[tf]).agg(agg_rules)
                
                # Remove incomplete periods
                agg_data = agg_data.dropna()
                
                if not agg_data.empty:
                    result[tf] = agg_data
                    
            except Exception as e:
                print(f"Warning: Failed to aggregate {tf}: {e}")
                continue
        
        return result
    
    def create_data_package(self, ticker: str, current_price: float,
                          mtf_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Create standardized data package for analyzers.
        
        Args:
            ticker: Asset ticker
            current_price: Current price
            mtf_data: Multi-timeframe data
            
        Returns:
            Dict: Standardized data package
        """
        return {
            'ticker': ticker,
            'current_price': current_price,
            'timestamp': datetime.now(),
            'mtf_data': mtf_data,
            'primary_timeframe': self._get_primary_timeframe(mtf_data),
            'data_quality': self._assess_data_quality(mtf_data)
        }
    
    def _get_primary_timeframe(self, mtf_data: Dict[str, pd.DataFrame]) -> str:
        """Get best timeframe for analysis."""
        # Prefer 15m, fallback to available data
        preferred_order = ['15m', '1h', '30m', '5m', '1m', '4h', '1d']
        
        for tf in preferred_order:
            if tf in mtf_data and not mtf_data[tf].empty:
                return tf
        
        # Return first available
        return list(mtf_data.keys())[0] if mtf_data else '15m'
    
    def _assess_data_quality(self, mtf_data: Dict[str, pd.DataFrame]) -> float:
        """
        Assess overall data quality score.
        
        Returns:
            float: Quality score 0.0-1.0
        """
        if not mtf_data:
            return 0.0
        
        total_score = 0.0
        valid_timeframes = 0
        
        for tf, data in mtf_data.items():
            is_valid, _ = self.validate_ohlcv_data(data, min_bars=20)
            if is_valid:
                # Simple quality scoring
                completeness = 1.0 - (data.isna().sum().sum() / (len(data) * len(self.required_columns)))
                recency = 1.0  # Assume recent data for now
                consistency = 1.0  # Basic assumption
                
                tf_score = (completeness + recency + consistency) / 3
                total_score += tf_score
                valid_timeframes += 1
        
        return total_score / valid_timeframes if valid_timeframes > 0 else 0.0
    
    def calculate_basic_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate basic indicators needed for analysis.
        
        Args:
            data: OHLCV DataFrame
            
        Returns:
            pd.DataFrame: Data with basic indicators
        """
        if data is None or data.empty:
            return data
        
        result = data.copy()
        
        try:
            # Typical Price for volume calculations
            result['typical_price'] = (result['high'] + result['low'] + result['close']) / 3
            
            # Price change
            result['price_change'] = result['close'].pct_change()
            
            # Volume moving average (simple 20-period)
            result['volume_ma'] = result['volume'].rolling(20, min_periods=10).mean()
            
            # Volume ratio
            result['volume_ratio'] = result['volume'] / result['volume_ma']
            
            # True Range for volatility
            result['true_range'] = np.maximum(
                result['high'] - result['low'],
                np.maximum(
                    abs(result['high'] - result['close'].shift(1)),
                    abs(result['low'] - result['close'].shift(1))
                )
            )
            
        except Exception as e:
            print(f"Warning: Failed to calculate indicators: {e}")
        
        return result
    
    def get_timeframe_enum(self, timeframe_str: str) -> Optional[TimeFrame]:
        """Convert timeframe string to enum."""
        return self.timeframe_mapping.get(timeframe_str)
