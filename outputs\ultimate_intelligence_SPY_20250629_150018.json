{"ticker": "SPY", "pipeline_components": ["DATA_INIT", "B-Series", "A-01", "C-02", "F-02", "SPECIALIZED_ARMY", "EXTENDED_ANALYSIS"], "timestamp": "2025-06-29T15:00:14.281854", "intelligence_level": "MAXIMUM_WITH_ALL_ANALYZERS", "data_initialization": {"status": "SUCCESS", "source": "schwab_mcp_localhost_8005", "bars_records": 1, "options_records": 14, "single_source_compliance": true, "data_timestamp": "2025-06-29T15:00:15.355036"}, "b_series": {"status": "SUCCESS", "feature_count": 52, "output_file": "data\\features\\SPY_features.parquet", "greek_features": ["gamma_calc_mean", "gamma_calc_sum", "gamma_calc_std", "vanna_calc_mean", "vanna_calc_sum", "vanna_calc_std", "charm_calc_mean", "charm_calc_sum", "charm_calc_std", "delta_calc_mean", "delta_calc_sum", "theta_calc_mean", "theta_calc_sum", "gamma_calc_mean_roc", "gamma_calc_mean_roc_2", "gamma_calc_sum_roc", "gamma_calc_sum_roc_2", "vanna_calc_mean_roc", "vanna_calc_mean_roc_2", "vanna_calc_sum_roc", "vanna_calc_sum_roc_2", "charm_calc_mean_roc", "charm_calc_mean_roc_2", "charm_calc_sum_roc", "charm_calc_sum_roc_2"]}, "a01_anomalies": {"ticker": "SPY", "date": "2025-06-29", "timestamp": "2025-06-29T15:00:15.467938", "anomaly_count": 0, "anomalies": []}, "f01_csid": {"timestamp": "2025-06-29 15:00:15.496015", "symbol": "SPY", "cumulative_volume_delta": 0.0, "cvd_velocity": 0.0, "cvd_acceleration": 0.0, "cvd_momentum": 0.0, "order_flow_imbalance": 0.0, "stealth_retail_ratio": 1.0, "imbalance_strength": 0.0, "flow_persistence": 0.0, "institutional_stealth_score": 0.0, "stealth_periods": 0, "retail_fomo_periods": 0, "institutional_bias": "error", "smart_money_index": 0.0, "smart_money_direction": "neutral", "accumulation_distribution": 0.5, "stealth_participation": 0.0, "flow_z_score": 0.0, "imbalance_significance": 0.0, "trend_consistency": 0.0, "data_quality_score": 0.0, "calculation_confidence": 0.0, "error_bounds": [0.0, 0.0], "statistical_significance": 0.0, "metadata": {"error": "Data quality validation failed", "version": "enhanced_money_flow_error"}, "ticker": "SPY", "flow_regime": "mixed", "analysis_timestamp": "2025-06-29T15:00:15.496341"}, "csid_file": "flow_phys\\history\\SPY_csid.json", "c02_iv_dynamics": {"current_regime": "NORMAL_VOLATILITY", "regime_confidence": 0.5, "volatility_trend": "STABLE", "current_iv": 0.04898376971186391, "iv_percentile": 64.28571428571429, "iv_range": 0.004019353855531663, "quality_score": 0.48000000000000004, "contracts_analyzed": 14, "analysis_timestamp": "2025-06-29T15:00:16.786019", "has_greeks": true, "greeks": {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0, "rho": 0.0, "calculated_iv": 0.04898376971186391, "greeks_quality": "calculated"}}, "c02_regime": {"current_regime": "NORMAL_VOLATILITY", "regime_confidence": 0.5, "volatility_trend": "STABLE", "current_iv": 0.04898376971186391, "iv_percentile": 64.28571428571429, "iv_range": 0.004019353855531663, "quality_score": 0.48000000000000004, "contracts_analyzed": 14, "analysis_timestamp": "2025-06-29T15:00:16.786019", "has_greeks": true, "greeks": {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0, "rho": 0.0, "calculated_iv": 0.04898376971186391, "greeks_quality": "calculated"}}, "f02_flow_physics": {"error": "flow_phys\\2025-06-29\\SPY_flowphysics.json"}, "specialized_army": {"enhanced_accumulation_distribution": {"decision": "NEUTRAL", "confidence": 50.0, "signal": {"strength": 0.5, "direction": "neutral"}, "error": "Enhanced agent not available"}, "breakout_validation": {"status": "SUCCESS", "ticker": "SPY", "output_file": "army_analysis\\2025-06-29\\SPY_breakout_validation.json", "breakout_validity": 50.0, "confidence": 10.0, "volume_confirmation": false, "quality_score": 0.1}, "options_flow": {"error": "Options data not available"}}, "options_intelligence": {"error": "'AgentResult' object has no attribute 'result'"}, "extended_analysis": {"fvg_analysis": {"error": "FVG analysis failed: Unknown error"}, "mean_reversion_analysis": {"error": "Mean reversion analysis failed: Unknown error"}, "pivot_point_analysis": {"error": "Pivot point analysis failed: Unknown error"}, "signal_convergence": {"confluence_probability": 67.0, "signal_strength": "WEAK", "direction": "NEUTRAL", "entry_price": 100.0, "target_levels": [98.0], "stop_loss": 100.6, "position_size": 2.0, "risk_reward_ratio": 3.333333333333365, "confluence_factors": [], "time_decay_warning": false, "execution_urgency": "WAIT", "mathematical_validation": {"probability_bounds_valid": true, "risk_reward_adequate": true, "position_size_safe": true, "numerical_precision": true, "mathematical_consistency": true, "overall_valid": true}, "calculation_timestamp": "2025-06-29T15:00:18.339128"}, "math_validation": {"error": "Math validation failed: Unknown error"}, "signal_quality": {"signal_generated": true, "signal_type": "neutral", "confidence_score": 0.0, "signal_strength": "none", "execution_recommendation": "avoid", "supporting_factors": {"reason": "No confluence data"}, "risk_metrics": {"position_size": "minimal", "stop_loss": "tight"}, "execution_timing": {"recommendation": "avoid"}, "quality_metrics": {"signal_accuracy": 0.1, "execution_time": 0.01}, "quality_validation_passed": true, "training_data": {"decisions_made": 0, "signal_patterns": [], "confluence_decisions": [], "execution_decisions": []}}}, "agent_zero_intelligence": {"timestamp": "2025-06-29T15:00:18.361034", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.02, "action": "hold", "confidence": 0.5883, "reasoning": ["Moderate confidence hold: composite score 0.588", "LIQUIDITY SCORE: 0.356 (50% weight - DOMINANT)", "  - Vol Liquidity: 0.02", "  - Flow Liquidity: 0.50", "  - IV Liquidity: 0.50", "  - Structure Liquidity: 0.50", "Signal confidence: 1.000 (18% weight)", "Signal strength: 0.500 (12% weight)", "Execution rec: execute -> 1.0 (12% weight)", "Math accuracy: 1.000 (5% weight)", "Math precision: 0.0010 (3% weight)", "ML system not available - using rule-based logic"], "composite_score": 0.5883, "liquidity_score": 0.35600000000000004, "weights_used": {"liquidity_score": 0.5, "signal_confidence": 0.18, "signal_strength": 0.12, "execution_recommendation": 0.12, "math_accuracy": 0.05, "math_precision": 0.03}, "method": "empirically_validated_50pct_liquidity"}, "ensemble_intelligence": {"final_decision": "NEUTRAL", "strength": "WEAK", "confidence": 100.0, "ensemble_score": 50.0, "agent_zero_recommendation": "HOLD - Neutral confluence", "component_signals": {"b_series": 0.5, "anomalies": 0.5, "csid": 0.5, "iv_dynamics": 0.5, "flow_physics": 0.5, "accumulation": 0.5, "breakout": 0.5, "options_flow": 0.5, "fvg": 0.5, "mean_reversion": 0.5, "pivot_points": 0.5, "signal_convergence": 0.5, "math_validation": 0.5, "signal_quality": 0.5}, "component_weights": {"accumulation": 0.25, "breakout": 0.15, "options_flow": 0.05, "csid": 0.1, "flow_physics": 0.1, "anomalies": 0.05, "iv_dynamics": 0.05, "fvg": 0.08, "mean_reversion": 0.07, "pivot_points": 0.05, "signal_convergence": 0.03, "math_validation": 0.01, "signal_quality": 0.01}, "intelligence_sources": ["b_series", "anomalies", "csid", "iv_dynamics", "flow_physics", "accumulation", "breakout", "options_flow", "fvg", "mean_reversion", "pivot_points", "signal_convergence", "math_validation", "signal_quality"]}, "agent_zero_charts": {"error": "'confluence_result'"}, "agent_zero_chart_paths": []}