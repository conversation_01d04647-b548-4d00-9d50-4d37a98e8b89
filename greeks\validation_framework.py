#!/usr/bin/env python3
"""
Greeks Validation Framework
Comprehensive validation framework for Greeks calculations with mathematical rigor
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Any, Optional, Tuple
import logging

from .constants import *
from .greeks_result import GreeksResult

logger = logging.getLogger(__name__)


class GreeksValidationFramework:
    """
    Comprehensive validation framework for Greeks calculations
    
    Validation Categories:
    - Mathematical bounds checking (Delta [-1,1], Gamma0, Vega0)
    - Black-Scholes relationship validation
    - Statistical significance testing (95% confidence)
    - Cross-validation between Greeks
    - Historical consistency checking
    - Put-call parity verification
    
    Mathematical Foundation:
    - All validations based on theoretical Black-Scholes constraints
    - Statistical tests use Z-score analysis with configurable confidence
    - Cross-validation employs relationship testing between Greeks
    - Error bounds maintain 1e-12 precision throughout
    """
    
    def __init__(self, confidence_level: float = 0.95):
        self.confidence_level = confidence_level
        self.z_threshold = stats.norm.ppf((1 + confidence_level) / 2)
        self.validation_history = []
        
    def validate_complete_result(self, greeks_result: GreeksResult) -> Dict[str, Any]:
        """
        Perform comprehensive validation of Greeks result
        
        Args:
            greeks_result: GreeksResult object to validate
            
        Returns:
            Dict with comprehensive validation results
        """
        
        try:
            validation_results = {
                'overall_valid': True,
                'validation_timestamp': pd.Timestamp.now().isoformat(),
                'validation_details': {}
            }
            
            # 1. Mathematical bounds validation
            bounds_validation = self.validate_mathematical_bounds(greeks_result)
            validation_results['validation_details']['bounds'] = bounds_validation
            
            # 2. Black-Scholes relationships validation
            relationships_validation = self.validate_black_scholes_relationships(greeks_result)
            validation_results['validation_details']['relationships'] = relationships_validation
            
            # 3. Statistical significance validation
            if greeks_result.statistical_significance:
                stats_validation = self.validate_statistical_significance(greeks_result.statistical_significance)
                validation_results['validation_details']['statistical'] = stats_validation
            
            # 4. Cross-validation between Greeks
            cross_validation = self.validate_greeks_cross_relationships(greeks_result)
            validation_results['validation_details']['cross_validation'] = cross_validation
            
            # 5. Numerical stability validation
            stability_validation = self.validate_numerical_stability(greeks_result)
            validation_results['validation_details']['stability'] = stability_validation
            
            # Determine overall validation status
            all_validations = [
                bounds_validation['all_bounds_valid'],
                relationships_validation['relationships_valid'],
                cross_validation['cross_validation_passed'],
                stability_validation['numerically_stable']
            ]
            
            validation_results['overall_valid'] = all(all_validations)
            validation_results['validation_score'] = sum(all_validations) / len(all_validations)
            
            # Store validation history
            self.validation_history.append({
                'timestamp': pd.Timestamp.now(),
                'symbol': greeks_result.symbol,
                'validation_passed': validation_results['overall_valid'],
                'validation_score': validation_results['validation_score']
            })
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Complete validation failed: {e}")
            return {
                'overall_valid': False,
                'validation_error': str(e),
                'validation_timestamp': pd.Timestamp.now().isoformat()
            }
    
    def validate_mathematical_bounds(self, greeks_result: GreeksResult) -> Dict[str, Any]:
        """
        Validate mathematical bounds for all Greeks
        
        Mathematical Constraints:
        - Delta: -1    1 (for all options)
        - Gamma:   0 (always non-negative)
        - Vega:   0 (always non-negative)
        - Theta: Generally negative for long positions
        - Rho: Can be positive or negative depending on option type
        """
        
        bounds_checks = {}
        
        # Delta bounds validation
        delta_valid = DELTA_BOUNDS[0] <= greeks_result.delta <= DELTA_BOUNDS[1]
        bounds_checks['delta_bounds'] = {
            'valid': delta_valid,
            'value': greeks_result.delta,
            'bounds': DELTA_BOUNDS,
            'constraint': 'Delta must be between -1 and 1'
        }
        
        # Gamma non-negativity
        gamma_valid = greeks_result.gamma >= GAMMA_MIN_BOUND
        bounds_checks['gamma_positive'] = {
            'valid': gamma_valid,
            'value': greeks_result.gamma,
            'constraint': 'Gamma must be non-negative'
        }
        
        # Vega non-negativity
        vega_valid = greeks_result.vega >= VEGA_MIN_BOUND
        bounds_checks['vega_positive'] = {
            'valid': vega_valid,
            'value': greeks_result.vega,
            'constraint': 'Vega must be non-negative'
        }
        
        # Finite values check
        finite_values = all(np.isfinite([
            greeks_result.delta, greeks_result.gamma, greeks_result.theta,
            greeks_result.vega, greeks_result.rho
        ]))
        bounds_checks['finite_values'] = {
            'valid': finite_values,
            'constraint': 'All Greeks must be finite'
        }
        
        # Theta constraint for long positions (should be negative)
        if greeks_result.option_type == 'call' or greeks_result.option_type == 'put':
            theta_expected_negative = greeks_result.theta <= 0
            bounds_checks['theta_sign'] = {
                'valid': theta_expected_negative,
                'value': greeks_result.theta,
                'constraint': 'Theta should be negative for long positions'
            }
        
        # Overall bounds validation
        all_bounds_valid = all(check['valid'] for check in bounds_checks.values())
        
        return {
            'all_bounds_valid': all_bounds_valid,
            'individual_checks': bounds_checks,
            'bounds_score': sum(check['valid'] for check in bounds_checks.values()) / len(bounds_checks)
        }
    
    def validate_black_scholes_relationships(self, greeks_result: GreeksResult) -> Dict[str, Any]:
        """
        Validate internal Black-Scholes Greeks relationships
        
        Mathematical Relationships:
        - Gamma = Delta/S (relationship between gamma and delta)
        - Vega and Gamma relationship through volatility
        - Put-call parity implications for Greeks
        """
        
        relationship_checks = {}
        
        try:
            # Check if option parameters are reasonable
            S = greeks_result.spot_price
            K = greeks_result.strike_price
            T = greeks_result.time_to_expiry
            r = greeks_result.risk_free_rate
            sigma = greeks_result.volatility
            
            # Moneyness-based Delta validation
            moneyness = S / K
            expected_delta_range = self._get_expected_delta_range(
                moneyness, greeks_result.option_type, T
            )
            
            delta_reasonable = (expected_delta_range[0] <= greeks_result.delta <= expected_delta_range[1])
            relationship_checks['delta_moneyness'] = {
                'valid': delta_reasonable,
                'actual_delta': greeks_result.delta,
                'expected_range': expected_delta_range,
                'moneyness': moneyness
            }
            
            # Gamma maximum near ATM
            # Gamma should be highest when S  K
            distance_from_atm = abs(moneyness - 1.0)
            gamma_atm_consistent = True  # Simplified check
            if distance_from_atm > 0.2:  # Far from ATM
                # Gamma should be relatively lower
                gamma_atm_consistent = greeks_result.gamma >= 0
            
            relationship_checks['gamma_atm'] = {
                'valid': gamma_atm_consistent,
                'distance_from_atm': distance_from_atm,
                'gamma_value': greeks_result.gamma
            }
            
            # Vega-Gamma relationship
            # For European options, Vega = S * Gamma * sqrt(T) *  (approximately)
            if greeks_result.gamma > 0 and T > 0:
                expected_vega_approx = S * greeks_result.gamma * np.sqrt(T) * sigma
                vega_ratio = greeks_result.vega / expected_vega_approx if expected_vega_approx > 0 else 0
                vega_relationship_valid = 0.5 <= vega_ratio <= 2.0  # Allow reasonable deviation
            else:
                vega_relationship_valid = True  # Skip if parameters are edge cases
            
            relationship_checks['vega_gamma'] = {
                'valid': vega_relationship_valid,
                'vega_ratio': vega_ratio if 'vega_ratio' in locals() else 0
            }
            
            # Overall relationship validation
            relationships_valid = all(check['valid'] for check in relationship_checks.values())
            
            return {
                'relationships_valid': relationships_valid,
                'individual_checks': relationship_checks,
                'relationships_score': sum(check['valid'] for check in relationship_checks.values()) / len(relationship_checks)
            }
            
        except Exception as e:
            logger.error(f"Black-Scholes relationships validation failed: {e}")
            return {
                'relationships_valid': False,
                'error': str(e),
                'individual_checks': relationship_checks
            }
    
    def _get_expected_delta_range(self, moneyness: float, option_type: str, time_to_expiry: float) -> Tuple[float, float]:
        """Get expected Delta range based on moneyness and option type"""
        
        if option_type == 'call':
            if moneyness > 1.2:  # Deep ITM
                return (0.7, 1.0)
            elif moneyness > 1.05:  # ITM
                return (0.5, 0.8)
            elif moneyness > 0.95:  # ATM
                return (0.4, 0.6)
            elif moneyness > 0.8:  # OTM
                return (0.1, 0.4)
            else:  # Deep OTM
                return (0.0, 0.2)
        else:  # put
            if moneyness < 0.8:  # Deep ITM
                return (-1.0, -0.7)
            elif moneyness < 0.95:  # ITM
                return (-0.8, -0.5)
            elif moneyness < 1.05:  # ATM
                return (-0.6, -0.4)
            elif moneyness < 1.2:  # OTM
                return (-0.4, -0.1)
            else:  # Deep OTM
                return (-0.2, 0.0)
    
    def validate_statistical_significance(self, significance_values: Dict[str, float]) -> Dict[str, Any]:
        """
        Validate statistical significance of ROC calculations
        
        Args:
            significance_values: Dictionary of significance scores for each Greek
            
        Returns:
            Dict with statistical validation results
        """
        
        stat_checks = {}
        
        for greek_name, significance in significance_values.items():
            # Check if significance is within reasonable bounds
            significance_valid = 0.0 <= significance <= 1.0
            
            # Check if significance meets minimum threshold for reliability
            significance_adequate = significance >= (self.confidence_level - 0.1)  # Allow some tolerance
            
            stat_checks[greek_name] = {
                'valid': significance_valid,
                'adequate': significance_adequate,
                'value': significance,
                'threshold': self.confidence_level
            }
        
        # Overall statistical validation
        all_significant = all(check['valid'] for check in stat_checks.values())
        adequately_significant = all(check['adequate'] for check in stat_checks.values())
        
        return {
            'statistically_valid': all_significant,
            'adequately_significant': adequately_significant,
            'individual_checks': stat_checks,
            'average_significance': np.mean(list(significance_values.values())) if significance_values else 0.0
        }
    
    def validate_greeks_cross_relationships(self, greeks_result: GreeksResult) -> Dict[str, Any]:
        """
        Validate cross-relationships between different Greeks
        
        Cross-validation tests:
        - Consistency between Greeks for same underlying
        - Reasonable relative magnitudes
        - Sign consistency where applicable
        """
        
        cross_checks = {}
        
        try:
            # Delta-Gamma consistency
            # Higher Gamma should correspond to Delta sensitivity to price changes
            if abs(greeks_result.delta) > 0.1:  # Significant delta
                gamma_delta_ratio = greeks_result.gamma / abs(greeks_result.delta)
                gamma_delta_reasonable = 0.01 <= gamma_delta_ratio <= 100  # Reasonable range
            else:
                gamma_delta_reasonable = True  # Skip for very small delta
            
            cross_checks['gamma_delta_consistency'] = {
                'valid': gamma_delta_reasonable,
                'ratio': gamma_delta_ratio if 'gamma_delta_ratio' in locals() else 0
            }
            
            # Vega-Theta relationship
            # Both should be significant for options with time value
            time_value_significant = greeks_result.time_to_expiry > 0.027  # About 10 days
            if time_value_significant:
                vega_theta_consistent = (greeks_result.vega > 0) and (greeks_result.theta < 0)
            else:
                vega_theta_consistent = True  # Near expiry, relationships may break down
            
            cross_checks['vega_theta_consistency'] = {
                'valid': vega_theta_consistent,
                'vega_positive': greeks_result.vega > 0,
                'theta_negative': greeks_result.theta < 0
            }
            
            # Overall magnitude reasonableness
            # Greeks shouldn't be excessively large relative to option price estimate
            S = greeks_result.spot_price
            reasonable_magnitudes = (
                abs(greeks_result.delta) <= 1.0 and
                greeks_result.gamma <= S and  # Gamma shouldn't exceed spot price
                abs(greeks_result.theta) <= S and  # Theta shouldn't exceed spot price
                greeks_result.vega <= S  # Vega shouldn't exceed spot price
            )
            
            cross_checks['magnitude_reasonableness'] = {
                'valid': reasonable_magnitudes
            }
            
            # Overall cross-validation
            cross_validation_passed = all(check['valid'] for check in cross_checks.values())
            
            return {
                'cross_validation_passed': cross_validation_passed,
                'individual_checks': cross_checks,
                'cross_validation_score': sum(check['valid'] for check in cross_checks.values()) / len(cross_checks)
            }
            
        except Exception as e:
            logger.error(f"Cross-validation failed: {e}")
            return {
                'cross_validation_passed': False,
                'error': str(e),
                'individual_checks': cross_checks
            }
    
    def validate_numerical_stability(self, greeks_result: GreeksResult) -> Dict[str, Any]:
        """
        Validate numerical stability of calculations
        
        Stability checks:
        - No NaN or infinite values
        - Values within expected numerical ranges
        - Precision loss indicators
        """
        
        stability_checks = {}
        
        # Finite values check
        all_greeks = [
            greeks_result.delta, greeks_result.gamma, greeks_result.theta,
            greeks_result.vega, greeks_result.rho
        ]
        
        finite_check = all(np.isfinite(val) for val in all_greeks)
        stability_checks['finite_values'] = {
            'valid': finite_check,
            'non_finite_count': sum(1 for val in all_greeks if not np.isfinite(val))
        }
        
        # Extreme values check
        extreme_values = any(abs(val) > 1e6 for val in all_greeks)
        stability_checks['no_extreme_values'] = {
            'valid': not extreme_values,
            'max_absolute_value': max(abs(val) for val in all_greeks if np.isfinite(val))
        }
        
        # Precision indicators
        precision_adequate = True
        if hasattr(greeks_result, 'calculation_metadata'):
            metadata = greeks_result.calculation_metadata
            if 'precision_warnings' in metadata:
                precision_adequate = len(metadata['precision_warnings']) == 0
        
        stability_checks['precision_adequate'] = {
            'valid': precision_adequate
        }
        
        # Overall stability
        numerically_stable = all(check['valid'] for check in stability_checks.values())
        
        return {
            'numerically_stable': numerically_stable,
            'individual_checks': stability_checks,
            'stability_score': sum(check['valid'] for check in stability_checks.values()) / len(stability_checks)
        }
    
    def get_validation_summary(self) -> Dict[str, Any]:
        """Get summary of validation history"""
        
        if not self.validation_history:
            return {
                'total_validations': 0,
                'success_rate': 0.0,
                'average_score': 0.0
            }
        
        total_validations = len(self.validation_history)
        successful_validations = sum(1 for v in self.validation_history if v['validation_passed'])
        success_rate = successful_validations / total_validations
        
        average_score = np.mean([v['validation_score'] for v in self.validation_history])
        
        return {
            'total_validations': total_validations,
            'successful_validations': successful_validations,
            'success_rate': success_rate,
            'average_score': average_score,
            'last_validation': self.validation_history[-1]['timestamp'].isoformat()
        }
