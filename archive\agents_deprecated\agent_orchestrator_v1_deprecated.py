#!/usr/bin/env python3
"""
Agent Orchestrator - Command Center for Specialized Trading Agents

Coordinates all specialized agents and integrates with Schwab MCP for real-time analysis.
Simplified version focused on agent zero integration and data flow validation.
"""

import logging
import asyncio
import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)

@dataclass
class OrchestratorConfig:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Configuration for the agent orchestrator"""
    timeout_ms: int = 15000
    schwab_mcp_url: str = "localhost:8002"  # Fixed invalid URL format
    enable_parallel_processing: bool = True
    confidence_threshold: float = 60.0
    ensemble_voting: bool = True
    real_time_mode: bool = True

class AgentOrchestrator:
    """
    Agent Orchestrator - The Command Center
    
    Coordinates specialized trading agents and provides data to agent zero.
    Simplified for testing and integration validation.
    """
    
    def __init__(self, config: OrchestratorConfig):
        self.config = config
        self.agents = {}  # Placeholder for specialized agents
        self.schwab_mcp_url = config.schwab_mcp_url
        
        logger.info(f"AgentOrchestrator initialized - Test mode")
    
    async def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main orchestration process
        
        Args:
            data: Analysis request data
            
        Returns:
            Comprehensive analysis results
        """
        try:
            symbol = data['symbol']
            analysis_type = data.get('analysis_type', 'full')
            
            logger.info(f"Processing {symbol} - Type: {analysis_type}")
            
            # Generate test ensemble decision
            ensemble_decision = self._generate_test_ensemble_decision()
            
            # Compile result
            result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'analysis_type': analysis_type,
                'agent_results': {},
                'ensemble_decision': ensemble_decision,
                'orchestrator_metadata': {
                    'agents_executed': [],
                    'execution_time_ms': 50.0,
                    'data_freshness': 'FRESH',
                    'confidence_level': ensemble_decision.get('confidence', 75.0)
                }
            }
            
            # Shadow mode logging - capture agent orchestrator decisions
            try:
                from agents.agent_zero import AgentZeroAdvisor
                shadow_agent = AgentZeroAdvisor()
                
                # DYNAMIC FEED IMPLEMENTATION for Agent Orchestrator
                from dynamic_feed_calculator import DynamicFeedCalculator
                
                # Create market context from ensemble decision
                market_context = {
                    'b_series_analysis': {
                        'features': {},
                        'confidence': ensemble_decision.get('confidence', 75.0) / 100,
                        'pattern_strength': ensemble_decision.get('strength', 0.7)
                    },
                    'flow_analysis': {
                        'momentum': ensemble_decision.get('momentum', 0.0),
                        'direction': 'bullish' if ensemble_decision.get('confidence', 50) > 70 else 'bearish' if ensemble_decision.get('confidence', 50) < 30 else 'neutral',
                        'strength': ensemble_decision.get('strength', 0.5)
                    },
                    'anomaly_analysis': {
                        'anomaly_detected': ensemble_decision.get('confidence', 50) < 30,  # Low confidence indicates potential anomaly
                        'anomaly_score': max(0, (50 - ensemble_decision.get('confidence', 50)) / 50)
                    },
                    'iv_dynamics_analysis': {
                        'iv_rank': 50.0,  # Default for agent orchestrator
                        'iv_expansion': False,
                        'volatility_regime': 'normal'
                    },
                    'market_regime': {
                        'trend': 'uptrend' if ensemble_decision.get('confidence', 50) > 60 else 'downtrend' if ensemble_decision.get('confidence', 50) < 40 else 'sideways',
                        'volatility': 'medium'
                    },
                    'system': 'agent_orchestrator',
                    'symbol': symbol,
                    'analysis_type': analysis_type,
                    'agents_executed': result['orchestrator_metadata']['agents_executed']
                }
                
                # Use dynamic feed calculator
                calculator = DynamicFeedCalculator()
                dynamic_inputs = calculator.calculate_all_dynamic_inputs(market_context)
                
                # Extract dynamic data
                signal_data = dynamic_inputs['signal_data']
                math_data = dynamic_inputs['math_data']
                full_market_context = dynamic_inputs['market_context']
                
                print(f"Agent Orchestrator Dynamic Feed:")
                print(f"  Confidence: {signal_data['confidence']:.4f}")
                print(f"  Execution: {signal_data['execution_recommendation']}")
                
                
                shadow_agent.log_training_data(
                    signal_data=signal_data,
                    math_data=math_data,
                    decision=ensemble_decision,
                    outcome=0.0,
                    market_context=full_market_context
                )
                logger.info("Shadow mode: Agent orchestrator dynamic data logged")
                
            except Exception as shadow_error:
                logger.warning(f"Shadow mode logging failed: {shadow_error}")
            
            return result
            
        except Exception as e:
            logger.error(f"Orchestrator processing failed: {e}")
            raise
    
    def _generate_test_ensemble_decision(self) -> Dict[str, Any]:
        """Generate test ensemble decision for validation"""
        return {
            'ensemble_decision': 'BULLISH',
            'decision_strength': 'MODERATE',
            'confidence': 75.5,
            'ensemble_score': 72.3,
            'agreement_level': 85.0,
            'participating_agents': 3,
            'recommendation': 'BUY - Moderate bullish ensemble',
            'risk_level': 'MEDIUM',
            'reasoning': ['Test ensemble decision for validation']
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check"""
        return {
            'orchestrator': 'healthy',
            'agents': {},
            'schwab_mcp': 'test_mode',
            'timestamp': datetime.now().isoformat()
        }
    
    def get_agent_capabilities(self) -> Dict[str, Any]:
        """Get capabilities summary"""
        return {
            'orchestrator_info': {
                'name': 'AgentOrchestrator',
                'version': '1.0.0_Test',
                'description': 'Test orchestrator for agent zero integration',
                'parallel_processing': self.config.enable_parallel_processing,
                'ensemble_voting': self.config.ensemble_voting
            },
            'specialized_agents': {},
            'data_integration': {
                'primary_source': 'Test Mode',
                'real_time_capability': False,
                'fallback_data': 'Test data generation'
            },
            'analysis_types_supported': ['full', 'test']
        }

# Utility function for external orchestrator usage
async def orchestrate_analysis(symbol: str, analysis_type: str = 'full', **kwargs) -> Dict[str, Any]:
    """
    Convenience function for external orchestrator usage
    
    Args:
        symbol: Ticker symbol to analyze
        analysis_type: Type of analysis
        **kwargs: Additional parameters
        
    Returns:
        Complete orchestrated analysis
    """
    config = OrchestratorConfig()
    orchestrator = AgentOrchestrator(config)
    
    request_data = {
        'symbol': symbol,
        'analysis_type': analysis_type,
        **kwargs
    }
    
    return await orchestrator.process(request_data)

# Example usage and testing
if __name__ == "__main__":
    import asyncio
    
    async def test_orchestrator():
        """Test the orchestrator"""
        config = OrchestratorConfig()
        orchestrator = AgentOrchestrator(config)
        
        # Test health check
        health = await orchestrator.health_check()
        print("Health Check:", json.dumps(health, indent=2))
        
        # Test capabilities
        capabilities = orchestrator.get_agent_capabilities()
        print("Capabilities:", json.dumps(capabilities, indent=2))
        
        # Test analysis
        test_data = {
            'symbol': 'SPY',
            'analysis_type': 'full'
        }
        
        try:
            result = await orchestrator.process(test_data)
            print("Analysis Result:", json.dumps(result, indent=2, default=str))
        except Exception as e:
            print(f"Analysis failed: {e}")
    
    # Run test
    asyncio.run(test_orchestrator())
