#!/usr/bin/env python3
"""
Black-Scholes Engine
Advanced Black-Scholes Greeks calculator with mathematical precision
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import logging

from .constants import *
from .greeks_result import GreeksResult

logger = logging.getLogger(__name__)


class BlackScholesEngine:
    """
    Advanced Black-Scholes Greeks calculator with mathematical precision
    
    Mathematical Foundation:
    - Analytical Black-Scholes formulas with 1e-12 precision
    - Bounds validation for all Greeks
    - Error propagation analysis
    - Statistical significance testing
    
    Precision Standards:
    - Delta: V/S with bounds [-1, 1]
    - Gamma: V/S with   0 constraint
    - Theta: V/t with time decay validation
    - Vega: V/ with vega  0 constraint
    - Rho: V/r for interest rate sensitivity
    """
    
    def __init__(self):
        self.calculation_count = 0
        self.error_count = 0
        self.precision_warnings = []
    
    def calculate_greeks(self, 
                        spot_price: float,
                        strike_price: float, 
                        time_to_expiry: float,
                        risk_free_rate: float,
                        volatility: float,
                        option_type: str = 'call',
                        dividend_yield: float = 0.0,
                        symbol: str = 'UNKNOWN') -> GreeksResult:
        """
        Calculate all Greeks using analytical Black-Scholes formulas
        
        Args:
            spot_price: Current underlying price
            strike_price: Option strike price
            time_to_expiry: Time to expiration in years
            risk_free_rate: Risk-free interest rate
            volatility: Implied volatility
            option_type: 'call' or 'put'
            dividend_yield: Dividend yield (default 0.0)
            symbol: Symbol for identification
            
        Returns:
            GreeksResult: Complete Greeks analysis with validation
            
        Raises:
            ValueError: If inputs are invalid or calculation fails
        """
        
        try:
            # Validate inputs
            self._validate_inputs(spot_price, strike_price, time_to_expiry, 
                                risk_free_rate, volatility, option_type)
            
            # Calculate d1 and d2 with high precision
            d1, d2 = self._calculate_d1_d2(spot_price, strike_price, time_to_expiry,
                                         risk_free_rate, volatility, dividend_yield)
            
            # Calculate option price for validation
            option_price = self._calculate_option_price(spot_price, strike_price, time_to_expiry,
                                                      risk_free_rate, volatility, dividend_yield,
                                                      option_type, d1, d2)
            
            # Calculate Greeks using analytical formulas
            delta = self._calculate_delta(d1, option_type, time_to_expiry, dividend_yield)
            gamma = self._calculate_gamma(spot_price, d1, time_to_expiry, volatility, dividend_yield)
            theta = self._calculate_theta(spot_price, strike_price, time_to_expiry,
                                        risk_free_rate, volatility, dividend_yield,
                                        option_type, d1, d2)
            vega = self._calculate_vega(spot_price, d1, time_to_expiry, dividend_yield)
            rho = self._calculate_rho(strike_price, time_to_expiry, risk_free_rate,
                                    option_type, d2)
            
            # Validate mathematical bounds
            validation_results = self._validate_mathematical_bounds(delta, gamma, theta, vega, rho)
            
            # Create result object
            result = GreeksResult(
                timestamp=datetime.now(),
                symbol=symbol,
                option_type=option_type,
                spot_price=spot_price,
                strike_price=strike_price,
                time_to_expiry=time_to_expiry,
                risk_free_rate=risk_free_rate,
                volatility=volatility,
                dividend_yield=dividend_yield,
                delta=delta,
                gamma=gamma,
                theta=theta,
                vega=vega,
                rho=rho,
                delta_roc=0.0,  # Will be calculated by ROC calculator
                gamma_roc=0.0,
                theta_roc=0.0,
                vega_roc=0.0,
                rho_roc=0.0,
                calculation_quality=validation_results['quality_score'],
                bounds_validation=validation_results['bounds_check'],
                statistical_significance={},  # Will be filled by ROC calculator
                greek_anomalies=[],  # Will be filled by anomaly detector
                anomaly_count=0,
                anomaly_severity='none',
                put_call_parity_check=self._validate_put_call_parity(
                    spot_price, strike_price, time_to_expiry, risk_free_rate, volatility, dividend_yield
                ),
                greeks_consistency_score=0.0,  # Will be calculated in post_init
                quality_score=0.0,  # Will be calculated in post_init
                calculation_metadata={
                    'd1': d1,
                    'd2': d2,
                    'option_price': option_price,
                    'calculation_time': datetime.now().isoformat(),
                    'precision_tolerance': GREEKS_PRECISION_TOLERANCE,
                    'input_validation_passed': True
                },
                error_flags=[]
            )
            
            self.calculation_count += 1
            logger.debug(f"Greeks calculation completed for {symbol}: Delta={delta:.6f}, Gamma={gamma:.6f}")
            
            return result
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Greeks calculation failed: {e}")
            raise ValueError(f"Black-Scholes calculation failed: {e}")
    
    def _validate_inputs(self, spot_price: float, strike_price: float, 
                        time_to_expiry: float, risk_free_rate: float,
                        volatility: float, option_type: str):
        """Validate Black-Scholes inputs against mathematical bounds"""
        
        if not (MIN_SPOT_PRICE <= spot_price <= MAX_SPOT_PRICE):
            raise ValueError(f"Spot price {spot_price} outside valid range [{MIN_SPOT_PRICE}, {MAX_SPOT_PRICE}]")
        
        if not (MIN_STRIKE_PRICE <= strike_price <= MAX_STRIKE_PRICE):
            raise ValueError(f"Strike price {strike_price} outside valid range [{MIN_STRIKE_PRICE}, {MAX_STRIKE_PRICE}]")
        
        if not (MIN_TIME_TO_EXPIRY <= time_to_expiry <= MAX_TIME_TO_EXPIRY):
            raise ValueError(f"Time to expiry {time_to_expiry} outside valid range [{MIN_TIME_TO_EXPIRY}, {MAX_TIME_TO_EXPIRY}]")
        
        if not (MIN_RISK_FREE_RATE <= risk_free_rate <= MAX_RISK_FREE_RATE):
            raise ValueError(f"Risk-free rate {risk_free_rate} outside valid range [{MIN_RISK_FREE_RATE}, {MAX_RISK_FREE_RATE}]")
        
        if not (MIN_VOLATILITY <= volatility <= MAX_VOLATILITY):
            raise ValueError(f"Volatility {volatility} outside valid range [{MIN_VOLATILITY}, {MAX_VOLATILITY}]")
        
        if option_type.lower() not in ['call', 'put']:
            raise ValueError(f"Option type must be 'call' or 'put', got: {option_type}")
        
        # Check for finite values
        if not all(np.isfinite([spot_price, strike_price, time_to_expiry, risk_free_rate, volatility])):
            raise ValueError("All input parameters must be finite")
    
    def _calculate_d1_d2(self, S: float, K: float, T: float, r: float, sigma: float, q: float = 0.0) -> Tuple[float, float]:
        """
        Calculate d1 and d2 for Black-Scholes formula with high precision
        
        Mathematical formulas:
        d1 = [ln(S/K) + (r - q + /2)T] / (T)
        d2 = d1 - T
        """
        
        try:
            # High precision calculation
            sqrt_T = np.sqrt(T)
            sigma_sqrt_T = sigma * sqrt_T
            
            # Natural logarithm with high precision
            ln_S_K = np.log(S / K)
            
            # Calculate d1 with full precision
            numerator = ln_S_K + (r - q + 0.5 * sigma * sigma) * T
            d1 = numerator / sigma_sqrt_T
            
            # Calculate d2
            d2 = d1 - sigma_sqrt_T
            
            # Validate finite results
            if not (np.isfinite(d1) and np.isfinite(d2)):
                raise ValueError(f"Non-finite d1/d2 values: d1={d1}, d2={d2}")
            
            return d1, d2
            
        except Exception as e:
            raise ValueError(f"Failed to calculate d1/d2: {e}")
    
    def _calculate_option_price(self, S: float, K: float, T: float, r: float, 
                              sigma: float, q: float, option_type: str,
                              d1: float, d2: float) -> float:
        """Calculate Black-Scholes option price for validation"""
        
        try:
            N_d1 = stats.norm.cdf(d1)
            N_d2 = stats.norm.cdf(d2)
            
            if option_type.lower() == 'call':
                price = S * np.exp(-q * T) * N_d1 - K * np.exp(-r * T) * N_d2
            else:  # put
                price = K * np.exp(-r * T) * stats.norm.cdf(-d2) - S * np.exp(-q * T) * stats.norm.cdf(-d1)
            
            return price
            
        except Exception as e:
            raise ValueError(f"Failed to calculate option price: {e}")
    
    def _calculate_delta(self, d1: float, option_type: str, T: float, q: float = 0.0) -> float:
        """
        Calculate Delta: V/S
        
        Mathematical formulas:
        Call Delta = e^(-qT) * N(d1)
        Put Delta = e^(-qT) * (N(d1) - 1)
        """
        
        try:
            exp_minus_qT = np.exp(-q * T)
            N_d1 = stats.norm.cdf(d1)
            
            if option_type.lower() == 'call':
                delta = exp_minus_qT * N_d1
            else:  # put
                delta = exp_minus_qT * (N_d1 - 1.0)
            
            # Validate bounds
            if not (DELTA_BOUNDS[0] <= delta <= DELTA_BOUNDS[1]):
                logger.warning(f"Delta {delta} outside bounds {DELTA_BOUNDS}")
            
            return delta
            
        except Exception as e:
            raise ValueError(f"Failed to calculate Delta: {e}")
    
    def _calculate_gamma(self, S: float, d1: float, T: float, sigma: float, q: float = 0.0) -> float:
        """
        Calculate Gamma: V/S
        
        Mathematical formula:
        Gamma = e^(-qT) * (d1) / (S *  * T)
        where (d1) is the standard normal PDF
        """
        
        try:
            exp_minus_qT = np.exp(-q * T)
            phi_d1 = stats.norm.pdf(d1)
            sqrt_T = np.sqrt(T)
            
            gamma = exp_minus_qT * phi_d1 / (S * sigma * sqrt_T)
            
            # Validate non-negativity
            if gamma < GAMMA_MIN_BOUND:
                logger.warning(f"Gamma {gamma} is negative")
            
            return gamma
            
        except Exception as e:
            raise ValueError(f"Failed to calculate Gamma: {e}")
    
    def _calculate_theta(self, S: float, K: float, T: float, r: float, 
                        sigma: float, q: float, option_type: str,
                        d1: float, d2: float) -> float:
        """
        Calculate Theta: V/t (per day, so divide by 365)
        
        Mathematical formulas:
        Call Theta = [-S*(d1)**e^(-qT)/(2T) - r*K*e^(-rT)*N(d2) + q*S*e^(-qT)*N(d1)] / 365
        Put Theta = [-S*(d1)**e^(-qT)/(2T) + r*K*e^(-rT)*N(-d2) - q*S*e^(-qT)*N(-d1)] / 365
        """
        
        try:
            exp_minus_qT = np.exp(-q * T)
            exp_minus_rT = np.exp(-r * T)
            phi_d1 = stats.norm.pdf(d1)
            sqrt_T = np.sqrt(T)
            
            # Common term
            first_term = -S * phi_d1 * sigma * exp_minus_qT / (2 * sqrt_T)
            
            if option_type.lower() == 'call':
                N_d1 = stats.norm.cdf(d1)
                N_d2 = stats.norm.cdf(d2)
                theta = (first_term - r * K * exp_minus_rT * N_d2 + q * S * exp_minus_qT * N_d1) / DAYS_PER_YEAR
            else:  # put
                N_minus_d1 = stats.norm.cdf(-d1)
                N_minus_d2 = stats.norm.cdf(-d2)
                theta = (first_term + r * K * exp_minus_rT * N_minus_d2 - q * S * exp_minus_qT * N_minus_d1) / DAYS_PER_YEAR
            
            return theta
            
        except Exception as e:
            raise ValueError(f"Failed to calculate Theta: {e}")
    
    def _calculate_vega(self, S: float, d1: float, T: float, q: float = 0.0) -> float:
        """
        Calculate Vega: V/ (per 1% vol change)
        
        Mathematical formula:
        Vega = S * e^(-qT) * (d1) * T / 100
        """
        
        try:
            exp_minus_qT = np.exp(-q * T)
            phi_d1 = stats.norm.pdf(d1)
            sqrt_T = np.sqrt(T)
            
            vega = S * exp_minus_qT * phi_d1 * sqrt_T / 100.0  # Per 1% vol change
            
            # Validate non-negativity
            if vega < VEGA_MIN_BOUND:
                logger.warning(f"Vega {vega} is negative")
            
            return vega
            
        except Exception as e:
            raise ValueError(f"Failed to calculate Vega: {e}")
    
    def _calculate_rho(self, K: float, T: float, r: float, option_type: str, d2: float) -> float:
        """
        Calculate Rho: V/r (per 1% rate change)
        
        Mathematical formulas:
        Call Rho = K * T * e^(-rT) * N(d2) / 100
        Put Rho = -K * T * e^(-rT) * N(-d2) / 100
        """
        
        try:
            exp_minus_rT = np.exp(-r * T)
            
            if option_type.lower() == 'call':
                N_d2 = stats.norm.cdf(d2)
                rho = K * T * exp_minus_rT * N_d2 / 100.0  # Per 1% rate change
            else:  # put
                N_minus_d2 = stats.norm.cdf(-d2)
                rho = -K * T * exp_minus_rT * N_minus_d2 / 100.0
            
            return rho
            
        except Exception as e:
            raise ValueError(f"Failed to calculate Rho: {e}")
    
    def _validate_mathematical_bounds(self, delta: float, gamma: float, theta: float, 
                                    vega: float, rho: float) -> Dict[str, Any]:
        """Validate Greeks mathematical constraints"""
        
        bounds_check = {
            'delta_bounds': DELTA_BOUNDS[0] <= delta <= DELTA_BOUNDS[1],
            'gamma_positive': gamma >= GAMMA_MIN_BOUND,
            'vega_positive': vega >= VEGA_MIN_BOUND,
            'finite_values': all(np.isfinite([delta, gamma, theta, vega, rho]))
        }
        
        # Calculate quality score
        quality_score = sum(bounds_check.values()) / len(bounds_check)
        
        return {
            'bounds_check': bounds_check,
            'quality_score': quality_score
        }
    
    def _validate_put_call_parity(self, S: float, K: float, T: float, r: float, 
                                 sigma: float, q: float = 0.0) -> bool:
        """
        Validate put-call parity: C - P = S*e^(-qT) - K*e^(-rT)
        """
        
        try:
            # Calculate both call and put prices
            d1, d2 = self._calculate_d1_d2(S, K, T, r, sigma, q)
            
            call_price = self._calculate_option_price(S, K, T, r, sigma, q, 'call', d1, d2)
            put_price = self._calculate_option_price(S, K, T, r, sigma, q, 'put', d1, d2)
            
            # Put-call parity relationship
            left_side = call_price - put_price
            right_side = S * np.exp(-q * T) - K * np.exp(-r * T)
            
            # Check if parity holds within tolerance
            parity_error = abs(left_side - right_side)
            parity_valid = parity_error < GREEKS_PRECISION_TOLERANCE * max(abs(left_side), abs(right_side))
            
            if not parity_valid:
                logger.warning(f"Put-call parity violation: error={parity_error}")
            
            return parity_valid
            
        except Exception as e:
            logger.error(f"Put-call parity validation failed: {e}")
            return False
    
    def get_calculation_stats(self) -> Dict[str, Any]:
        """Get calculation statistics"""
        return {
            'total_calculations': self.calculation_count,
            'error_count': self.error_count,
            'success_rate': (self.calculation_count - self.error_count) / max(self.calculation_count, 1),
            'precision_warnings': len(self.precision_warnings),
            'last_calculation': datetime.now().isoformat()
        }
