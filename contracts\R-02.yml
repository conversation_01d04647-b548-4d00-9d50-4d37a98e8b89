task_id: R-02
name: Risk-Guard Agent
version: 1.0.0
description: >
  Enforce position sizing and daily-drawdown limits before a trade leaves the
  pipeline. Rejects or resizes the execution_plan.md.

inputs:
  - unified_analysis_json          # path
  - execution_plan_md              # path
  - account_equity                 # float  (ENV ACCOUNT_EQUITY)
  - daily_loss_cap_pct: 3          # max % acct value that may be lost in a day

outputs:
  files:
    - path: guarded/{{date}}/{{ticker}}/execution_plan_ok.md
      must_exist: true
    - path: guarded/{{date}}/{{ticker}}/risk_guard_log.json
      must_exist: true

success_criteria:
  perf_budget: { max_runtime_ms: 150 }
  code_coverage_min: 0.95
