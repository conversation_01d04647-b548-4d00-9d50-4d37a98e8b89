"""Advanced Velocity Analyzer

Enhanced velocity analyzer that calculates flow velocity (first derivative)
with multi-timeframe correlation and institutional flow detection.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from scipy import signal
from scipy.interpolate import interp1d

from . import FLOW_PHYSICS_CONSTANTS

logger = logging.getLogger(__name__)


@dataclass
class VelocityProfile:
    """Represents a velocity profile at a point in time."""
    timestamp: datetime
    raw_velocity: float
    smoothed_velocity: float
    velocity_direction: str  # 'positive', 'negative', 'neutral'
    velocity_magnitude: str  # 'low', 'medium', 'high', 'extreme'
    
    # Multi-timeframe components
    timeframe_velocities: Dict[str, float]
    timeframe_correlation: float
    dominant_timeframe: str
    
    # Institutional detection
    institutional_velocity: bool
    institutional_confidence: float
    
    # Statistical properties
    velocity_percentile: float  # Where this velocity ranks historically
    velocity_zscore: float
    velocity_momentum: float  # Rate of change of velocity
    
    # Pattern detection
    velocity_pattern: Optional[str]  # 'accelerating', 'decelerating', 'steady', 'oscillating'
    pattern_strength: float
    

class AdvancedVelocityAnalyzer:
    """Advanced analyzer for flow velocity with institutional detection."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the advanced velocity analyzer.
        
        Args:
            config: Optional configuration overrides
        """
        self.config = config or {}
        self.constants = FLOW_PHYSICS_CONSTANTS
        
        # Analysis parameters
        self.lookback_window = self.config.get('lookback_window', 20)
        self.smoothing_factor = self.config.get('smoothing_factor', 0.3)
        self.timeframes = self.config.get('timeframes', ['1m', '5m', '15m', '1h'])
        
        # Thresholds
        self.low_velocity_threshold = self.constants['MIN_VELOCITY_THRESHOLD']
        self.medium_velocity_threshold = self.constants['INSTITUTIONAL_VELOCITY_THRESHOLD']
        self.high_velocity_threshold = self.constants['EXTREME_VELOCITY_THRESHOLD']
        
        # Historical data storage
        self._velocity_history = {}
        self._statistics_cache = {}
        
        logger.info("Advanced Velocity Analyzer initialized")
        
    def analyze(self,
                symbol: str,
                flow_data: pd.DataFrame,
                current_flow: float,
                timestamp: Optional[datetime] = None) -> VelocityProfile:
        """Analyze flow velocity with advanced features.
        
        Args:
            symbol: Symbol being analyzed
            flow_data: Historical flow data (must have 'timestamp' and 'flow' columns)
            current_flow: Current flow value
            timestamp: Current timestamp (defaults to now)
            
        Returns:
            VelocityProfile with comprehensive velocity analysis
        """
        timestamp = timestamp or datetime.now()
        
        try:
            # Calculate raw velocity
            raw_velocity = self._calculate_raw_velocity(flow_data, current_flow)
            
            # Apply smoothing
            smoothed_velocity = self._smooth_velocity(symbol, raw_velocity)
            
            # Multi-timeframe analysis
            mtf_analysis = self._analyze_multi_timeframe(symbol, flow_data)
            
            # Detect institutional velocity
            institutional_detection = self._detect_institutional_velocity(
                smoothed_velocity, mtf_analysis
            )
            
            # Calculate statistical properties
            stats = self._calculate_velocity_statistics(symbol, smoothed_velocity)
            
            # Detect velocity patterns
            pattern_detection = self._detect_velocity_patterns(symbol, smoothed_velocity)
            
            # Determine velocity characteristics
            direction = self._determine_direction(smoothed_velocity)
            magnitude = self._determine_magnitude(smoothed_velocity)
            
            # Create velocity profile
            profile = VelocityProfile(
                timestamp=timestamp,
                raw_velocity=raw_velocity,
                smoothed_velocity=smoothed_velocity,
                velocity_direction=direction,
                velocity_magnitude=magnitude,
                timeframe_velocities=mtf_analysis['velocities'],
                timeframe_correlation=mtf_analysis['correlation'],
                dominant_timeframe=mtf_analysis['dominant'],
                institutional_velocity=institutional_detection['detected'],
                institutional_confidence=institutional_detection['confidence'],
                velocity_percentile=stats['percentile'],
                velocity_zscore=stats['zscore'],
                velocity_momentum=stats['momentum'],
                velocity_pattern=pattern_detection['pattern'],
                pattern_strength=pattern_detection['strength']
            )
            
            # Store in history
            self._update_history(symbol, profile)
            
            return profile
            
        except Exception as e:
            logger.error(f"Error in velocity analysis: {e}")
            return self._create_default_profile(timestamp, current_flow)
            
    def _calculate_raw_velocity(self, 
                               flow_data: pd.DataFrame,
                               current_flow: float) -> float:
        """Calculate raw velocity from flow data."""
        if flow_data.empty or len(flow_data) < 2:
            return 0.0
            
        # Get last few points for velocity calculation
        recent_data = flow_data.tail(5).copy()
        
        # Add current point
        current_point = pd.DataFrame({
            'timestamp': [datetime.now()],
            'flow': [current_flow]
        })
        recent_data = pd.concat([recent_data, current_point], ignore_index=True)
        
        # Convert timestamps to seconds
        recent_data['time_seconds'] = recent_data['timestamp'].apply(
            lambda x: x.timestamp() if isinstance(x, datetime) else x
        )
        
        # Calculate velocity using numpy gradient
        flows = recent_data['flow'].values
        times = recent_data['time_seconds'].values
        
        if len(flows) >= 2:
            # Use gradient for smoother derivative
            velocities = np.gradient(flows, times)
            return float(velocities[-1])
        else:
            return 0.0
            
    def _smooth_velocity(self, symbol: str, raw_velocity: float) -> float:
        """Apply exponential smoothing to velocity."""
        if symbol not in self._velocity_history:
            return raw_velocity
            
        history = self._velocity_history[symbol]
        if not history:
            return raw_velocity
            
        # Get last smoothed value
        last_smoothed = history[-1].smoothed_velocity
        
        # Apply exponential smoothing
        alpha = self.smoothing_factor
        smoothed = alpha * raw_velocity + (1 - alpha) * last_smoothed
        
        return smoothed
        
    def _analyze_multi_timeframe(self, 
                               symbol: str,
                               flow_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze velocity across multiple timeframes."""
        velocities = {}
        
        for timeframe in self.timeframes:
            # Resample data to timeframe
            resampled = self._resample_to_timeframe(flow_data, timeframe)
            
            if len(resampled) >= 2:
                # Calculate velocity for this timeframe
                tf_velocity = self._calculate_timeframe_velocity(resampled)
                velocities[timeframe] = tf_velocity
            else:
                velocities[timeframe] = 0.0
                
        # Calculate correlation between timeframes
        correlation = self._calculate_timeframe_correlation(velocities)
        
        # Determine dominant timeframe
        dominant = max(velocities.items(), key=lambda x: abs(x[1]))[0]
        
        return {
            'velocities': velocities,
            'correlation': correlation,
            'dominant': dominant
        }
        
    def _resample_to_timeframe(self, 
                             flow_data: pd.DataFrame,
                             timeframe: str) -> pd.DataFrame:
        """Resample flow data to specific timeframe."""
        # Convert timeframe string to pandas frequency
        freq_map = {
            '1m': '1min',
            '5m': '5min',
            '15m': '15min',
            '1h': '1H',
            '4h': '4H',
            '1d': '1D'
        }
        
        freq = freq_map.get(timeframe, '5min')
        
        # Ensure timestamp is datetime
        flow_data = flow_data.copy()
        flow_data['timestamp'] = pd.to_datetime(flow_data['timestamp'])
        flow_data.set_index('timestamp', inplace=True)
        
        # Resample
        resampled = flow_data.resample(freq).agg({
            'flow': 'sum'  # Sum flows within each period
        }).dropna()
        
        return resampled.reset_index()
        
    def _calculate_timeframe_velocity(self, data: pd.DataFrame) -> float:
        """Calculate velocity for a specific timeframe."""
        if len(data) < 2:
            return 0.0
            
        # Get last two points
        last_points = data.tail(2)
        
        flow_diff = last_points['flow'].iloc[-1] - last_points['flow'].iloc[0]
        time_diff = (last_points['timestamp'].iloc[-1] - 
                    last_points['timestamp'].iloc[0]).total_seconds()
        
        if time_diff > 0:
            return flow_diff / time_diff
        else:
            return 0.0
            
    def _calculate_timeframe_correlation(self, velocities: Dict[str, float]) -> float:
        """Calculate correlation between timeframe velocities."""
        if len(velocities) < 2:
            return 1.0
            
        # Get velocity values
        values = list(velocities.values())
        
        # Check if all velocities have same sign
        signs = [np.sign(v) for v in values if v != 0]
        if not signs:
            return 0.0
            
        same_sign_ratio = sum(1 for s in signs if s == signs[0]) / len(signs)
        
        # Calculate magnitude correlation
        if len(values) > 1:
            # Normalize by maximum absolute value
            max_abs = max(abs(v) for v in values)
            if max_abs > 0:
                normalized = [v / max_abs for v in values]
                # Calculate standard deviation as inverse correlation metric
                std_dev = np.std(normalized)
                magnitude_correlation = 1 - min(1, std_dev)
            else:
                magnitude_correlation = 1.0
        else:
            magnitude_correlation = 1.0
            
        # Combined correlation
        return same_sign_ratio * magnitude_correlation
        
    def _detect_institutional_velocity(self,
                                     velocity: float,
                                     mtf_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Detect institutional velocity patterns."""
        # Check absolute velocity threshold
        abs_velocity = abs(velocity)
        velocity_institutional = abs_velocity > self.medium_velocity_threshold
        
        # Check multi-timeframe alignment
        mtf_aligned = mtf_analysis['correlation'] > 0.7
        
        # Check if multiple timeframes show significant velocity
        significant_timeframes = sum(
            1 for v in mtf_analysis['velocities'].values()
            if abs(v) > self.low_velocity_threshold
        )
        mtf_significant = significant_timeframes >= len(self.timeframes) * 0.5
        
        # Institutional detected if velocity is high AND timeframes align
        detected = velocity_institutional and (mtf_aligned or mtf_significant)
        
        # Calculate confidence
        confidence = 0.0
        if detected:
            velocity_score = min(1.0, abs_velocity / self.high_velocity_threshold)
            alignment_score = mtf_analysis['correlation']
            confidence = (velocity_score + alignment_score) / 2
            
        return {
            'detected': detected,
            'confidence': confidence,
            'velocity_institutional': velocity_institutional,
            'mtf_aligned': mtf_aligned,
            'mtf_significant': mtf_significant
        }
        
    def _calculate_velocity_statistics(self,
                                     symbol: str,
                                     velocity: float) -> Dict[str, Any]:
        """Calculate statistical properties of velocity."""
        # Get historical velocities
        if symbol not in self._velocity_history:
            return {
                'percentile': 50.0,
                'zscore': 0.0,
                'momentum': 0.0
            }
            
        history = self._velocity_history[symbol]
        if len(history) < 3:
            return {
                'percentile': 50.0,
                'zscore': 0.0,
                'momentum': 0.0
            }
            
        # Extract historical velocities
        historical_velocities = [h.smoothed_velocity for h in history]
        
        # Calculate percentile
        percentile = self._calculate_percentile(velocity, historical_velocities)
        
        # Calculate z-score
        zscore = self._calculate_zscore(velocity, historical_velocities)
        
        # Calculate momentum (rate of change of velocity)
        momentum = self._calculate_velocity_momentum(historical_velocities)
        
        return {
            'percentile': percentile,
            'zscore': zscore,
            'momentum': momentum
        }
        
    def _calculate_percentile(self, value: float, historical: List[float]) -> float:
        """Calculate percentile rank of value in historical data."""
        if not historical:
            return 50.0
            
        sorted_hist = sorted(historical)
        pos = np.searchsorted(sorted_hist, value)
        percentile = (pos / len(sorted_hist)) * 100
        
        return percentile
        
    def _calculate_zscore(self, value: float, historical: List[float]) -> float:
        """Calculate z-score of value relative to historical data."""
        if len(historical) < 2:
            return 0.0
            
        mean = np.mean(historical)
        std = np.std(historical)
        
        if std > 0:
            return (value - mean) / std
        else:
            return 0.0
            
    def _calculate_velocity_momentum(self, velocities: List[float]) -> float:
        """Calculate momentum (rate of change) of velocity."""
        if len(velocities) < 2:
            return 0.0
            
        # Get recent velocities
        recent = velocities[-5:] if len(velocities) >= 5 else velocities
        
        # Calculate rate of change
        if len(recent) >= 2:
            momentum = recent[-1] - recent[-2]
            return momentum
        else:
            return 0.0
            
    def _detect_velocity_patterns(self,
                                symbol: str,
                                velocity: float) -> Dict[str, Any]:
        """Detect patterns in velocity behavior."""
        if symbol not in self._velocity_history or len(self._velocity_history[symbol]) < 5:
            return {'pattern': None, 'strength': 0.0}
            
        history = self._velocity_history[symbol]
        recent_velocities = [h.smoothed_velocity for h in history[-10:]]
        recent_velocities.append(velocity)
        
        # Detect acceleration/deceleration
        if self._is_accelerating(recent_velocities):
            pattern = 'accelerating'
            strength = self._calculate_trend_strength(recent_velocities)
        elif self._is_decelerating(recent_velocities):
            pattern = 'decelerating'
            strength = self._calculate_trend_strength(recent_velocities)
        elif self._is_oscillating(recent_velocities):
            pattern = 'oscillating'
            strength = self._calculate_oscillation_strength(recent_velocities)
        else:
            pattern = 'steady'
            strength = 1.0 - np.std(recent_velocities) / (np.mean(np.abs(recent_velocities)) + 1e-6)
            
        return {
            'pattern': pattern,
            'strength': min(1.0, max(0.0, strength))
        }
        
    def _is_accelerating(self, velocities: List[float]) -> bool:
        """Check if velocity is consistently increasing."""
        if len(velocities) < 3:
            return False
            
        # Calculate differences
        diffs = np.diff(velocities)
        
        # Check if mostly positive
        positive_ratio = sum(1 for d in diffs if d > 0) / len(diffs)
        return positive_ratio > 0.7
        
    def _is_decelerating(self, velocities: List[float]) -> bool:
        """Check if velocity is consistently decreasing."""
        if len(velocities) < 3:
            return False
            
        # Calculate differences
        diffs = np.diff(velocities)
        
        # Check if mostly negative
        negative_ratio = sum(1 for d in diffs if d < 0) / len(diffs)
        return negative_ratio > 0.7
        
    def _is_oscillating(self, velocities: List[float]) -> bool:
        """Check if velocity is oscillating."""
        if len(velocities) < 4:
            return False
            
        # Count sign changes
        signs = [np.sign(v) for v in velocities if v != 0]
        if len(signs) < 2:
            return False
            
        sign_changes = sum(1 for i in range(1, len(signs)) if signs[i] != signs[i-1])
        change_ratio = sign_changes / (len(signs) - 1)
        
        return change_ratio > 0.3
        
    def _calculate_trend_strength(self, velocities: List[float]) -> float:
        """Calculate strength of trend in velocities."""
        if len(velocities) < 2:
            return 0.0
            
        # Fit linear trend
        x = np.arange(len(velocities))
        y = np.array(velocities)
        
        # Calculate correlation coefficient
        if np.std(y) > 0:
            correlation = np.corrcoef(x, y)[0, 1]
            return abs(correlation)
        else:
            return 0.0
            
    def _calculate_oscillation_strength(self, velocities: List[float]) -> float:
        """Calculate strength of oscillation pattern."""
        if len(velocities) < 4:
            return 0.0
            
        # Use FFT to find dominant frequency
        try:
            fft = np.fft.fft(velocities)
            freqs = np.fft.fftfreq(len(velocities))
            
            # Find peak frequency (excluding DC component)
            magnitudes = np.abs(fft[1:])
            if len(magnitudes) > 0:
                peak_idx = np.argmax(magnitudes) + 1
                peak_magnitude = magnitudes[peak_idx - 1]
                
                # Normalize by total power
                total_power = np.sum(magnitudes)
                if total_power > 0:
                    return peak_magnitude / total_power
        except:
            pass
            
        return 0.0
        
    def _determine_direction(self, velocity: float) -> str:
        """Determine velocity direction."""
        if abs(velocity) < 0.1:  # Simple fixed threshold
            return 'neutral'
        elif velocity > 0:
            return 'positive'
        else:
            return 'negative'
            
    def _determine_magnitude(self, velocity: float) -> str:
        """Determine velocity magnitude category."""
        abs_velocity = abs(velocity)
        
        if abs_velocity < self.low_velocity_threshold:
            return 'low'
        elif abs_velocity < self.medium_velocity_threshold:
            return 'medium'
        elif abs_velocity < self.high_velocity_threshold:
            return 'high'
        else:
            return 'extreme'
            
    def _update_history(self, symbol: str, profile: VelocityProfile):
        """Update velocity history for symbol."""
        if symbol not in self._velocity_history:
            self._velocity_history[symbol] = []
            
        self._velocity_history[symbol].append(profile)
        
        # Limit history size
        max_history = self.lookback_window * 2
        if len(self._velocity_history[symbol]) > max_history:
            self._velocity_history[symbol] = self._velocity_history[symbol][-max_history:]
            
    def _create_default_profile(self, timestamp: datetime, flow: float) -> VelocityProfile:
        """Create default profile when analysis fails."""
        return VelocityProfile(
            timestamp=timestamp,
            raw_velocity=0.0,
            smoothed_velocity=0.0,
            velocity_direction='neutral',
            velocity_magnitude='low',
            timeframe_velocities={},
            timeframe_correlation=0.0,
            dominant_timeframe='unknown',
            institutional_velocity=False,
            institutional_confidence=0.0,
            velocity_percentile=50.0,
            velocity_zscore=0.0,
            velocity_momentum=0.0,
            velocity_pattern=None,
            pattern_strength=0.0
        )
        
    def get_velocity_summary(self, symbol: str) -> Dict[str, Any]:
        """Get summary of velocity analysis for symbol."""
        if symbol not in self._velocity_history:
            return {'status': 'no_data'}
            
        history = self._velocity_history[symbol]
        if not history:
            return {'status': 'no_data'}
            
        recent = history[-1]
        velocities = [h.smoothed_velocity for h in history]
        
        return {
            'status': 'active',
            'current_velocity': recent.smoothed_velocity,
            'direction': recent.velocity_direction,
            'magnitude': recent.velocity_magnitude,
            'institutional': recent.institutional_velocity,
            'pattern': recent.velocity_pattern,
            'avg_velocity': np.mean(velocities),
            'max_velocity': max(velocities),
            'min_velocity': min(velocities),
            'volatility': np.std(velocities)
        }