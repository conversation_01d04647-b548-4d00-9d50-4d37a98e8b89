@echo off
REM Schwab MCP Server Startup - Direct replacement for Polygon MCP
REM Status Quo: Same interface, superior data source

echo =========================================================
echo SCHWAB MCP SERVER - POLYGON REPLACEMENT
echo =========================================================
echo Starting Schwab MCP on port 8005 (Polygon MCP uses 8004)
echo Interface: 100%% compatible with existing agent architecture
echo Fallback: Polygon MCP remains available on port 8004
echo =========================================================
echo.

cd /d "D:\script-work\CORE\api"

REM Refresh token if needed
echo [INFO] Refreshing Schwab authentication token...
py refresh_schwab_token.py

echo.
echo [INFO] Starting Schwab MCP server...
py schwab_mcp_server.py

pause
