{"ticker": "AAPL", "pipeline_components": ["DATA_INIT", "B-Series", "A-01", "C-02", "F-02", "SPECIALIZED_ARMY"], "timestamp": "2025-06-29T12:13:30.099083", "intelligence_level": "MAXIMUM_WITH_SPECIALIZED_AGENTS", "data_initialization": {"status": "SUCCESS", "source": "schwab_mcp_localhost_8005", "bars_records": 1, "options_records": 14, "single_source_compliance": true, "data_timestamp": "2025-06-29T12:13:34.345298"}, "b_series": {"status": "SUCCESS", "feature_count": 52, "output_file": "data\\features\\AAPL_features.parquet", "greek_features": ["gamma_calc_mean", "gamma_calc_sum", "gamma_calc_std", "vanna_calc_mean", "vanna_calc_sum", "vanna_calc_std", "charm_calc_mean", "charm_calc_sum", "charm_calc_std", "delta_calc_mean", "delta_calc_sum", "theta_calc_mean", "theta_calc_sum", "gamma_calc_mean_roc", "gamma_calc_mean_roc_2", "gamma_calc_sum_roc", "gamma_calc_sum_roc_2", "vanna_calc_mean_roc", "vanna_calc_mean_roc_2", "vanna_calc_sum_roc", "vanna_calc_sum_roc_2", "charm_calc_mean_roc", "charm_calc_mean_roc_2", "charm_calc_sum_roc", "charm_calc_sum_roc_2"]}, "a01_anomalies": {"ticker": "AAPL", "date": "2025-06-29", "timestamp": "2025-06-29T12:13:47.257466", "anomaly_count": 0, "anomalies": []}, "f01_csid": {"timestamp": "2025-06-29 12:13:47.287213", "symbol": "AAPL", "cumulative_volume_delta": 0.0, "cvd_velocity": 0.0, "cvd_acceleration": 0.0, "cvd_momentum": 0.0, "order_flow_imbalance": 0.0, "stealth_retail_ratio": 1.0, "imbalance_strength": 0.0, "flow_persistence": 0.0, "institutional_stealth_score": 0.0, "stealth_periods": 0, "retail_fomo_periods": 0, "institutional_bias": "error", "smart_money_index": 0.0, "smart_money_direction": "neutral", "accumulation_distribution": 0.5, "stealth_participation": 0.0, "flow_z_score": 0.0, "imbalance_significance": 0.0, "trend_consistency": 0.0, "data_quality_score": 0.0, "calculation_confidence": 0.0, "error_bounds": [0.0, 0.0], "statistical_significance": 0.0, "metadata": {"error": "Data quality validation failed", "version": "enhanced_money_flow_error"}, "ticker": "AAPL", "flow_regime": "mixed", "analysis_timestamp": "2025-06-29T12:13:47.287770"}, "csid_file": "flow_phys\\history\\AAPL_csid.json", "c02_iv_dynamics": {"current_regime": "NORMAL_VOLATILITY", "regime_confidence": 0.5, "volatility_trend": "STABLE", "current_iv": 0.04867234247559384, "iv_percentile": 35.714285714285715, "iv_range": 0.002342374075378628, "quality_score": 0.48000000000000004, "contracts_analyzed": 14, "analysis_timestamp": "2025-06-29T12:13:51.446429", "has_greeks": true, "greeks": {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0, "rho": 0.0, "calculated_iv": 0.04867234247559384, "greeks_quality": "calculated"}}, "c02_regime": {"current_regime": "NORMAL_VOLATILITY", "regime_confidence": 0.5, "volatility_trend": "STABLE", "current_iv": 0.04867234247559384, "iv_percentile": 35.714285714285715, "iv_range": 0.002342374075378628, "quality_score": 0.48000000000000004, "contracts_analyzed": 14, "analysis_timestamp": "2025-06-29T12:13:51.446429", "has_greeks": true, "greeks": {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0, "rho": 0.0, "calculated_iv": 0.04867234247559384, "greeks_quality": "calculated"}}, "f02_flow_physics": {"error": "flow_phys\\2025-06-29\\AAPL_flowphysics.json"}, "specialized_army": {"enhanced_accumulation_distribution": {"decision": "NEUTRAL", "confidence": 50.0, "signal": {"strength": 0.5, "direction": "neutral"}, "error": "Enhanced agent not available"}, "breakout_validation": {"status": "SUCCESS", "ticker": "AAPL", "output_file": "army_analysis\\2025-06-29\\AAPL_breakout_validation.json", "breakout_validity": 50.0, "confidence": 10.0, "volume_confirmation": false, "quality_score": 0.1}, "options_flow": {"error": "Options data not available"}}, "options_intelligence": {"error": "AgentTask.__init__() got an unexpected keyword argument 'agent_id'"}, "agent_zero_intelligence": {"timestamp": "2025-06-29T12:13:59.762943", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.04, "action": "hold", "confidence": 0.5831999999999999, "reasoning": ["Moderate confidence hold: composite score 0.583", "LIQUIDITY SCORE: 0.356 (50% weight - DOMINANT)", "  - Vol Liquidity: 0.02", "  - Flow Liquidity: 0.50", "  - IV Liquidity: 0.50", "  - Structure Liquidity: 0.50", "Signal confidence: 0.970 (18% weight)", "Signal strength: 0.515 (12% weight)", "Execution rec: execute -> 1.0 (12% weight)", "Math accuracy: 0.970 (5% weight)", "Math precision: 0.0010 (3% weight)", "ML system not available - using rule-based logic"], "composite_score": 0.5831999999999999, "liquidity_score": 0.35600000000000004, "weights_used": {"liquidity_score": 0.5, "signal_confidence": 0.18, "signal_strength": 0.12, "execution_recommendation": 0.12, "math_accuracy": 0.05, "math_precision": 0.03}, "method": "empirically_validated_50pct_liquidity"}, "ensemble_intelligence": {"final_decision": "NEUTRAL", "strength": "WEAK", "confidence": 97.0, "ensemble_score": 51.5, "agent_zero_recommendation": "HOLD - Neutral confluence", "component_signals": {"b_series": 0.5, "anomalies": 0.5, "csid": 0.5, "iv_dynamics": 0.5, "flow_physics": 0.5, "accumulation": 0.5, "breakout": 0.5, "options_flow": 0.5}, "component_weights": {"accumulation": 0.3, "breakout": 0.18, "options_flow": 0.15, "csid": 0.15, "flow_physics": 0.15, "anomalies": 0.05, "iv_dynamics": 0.05}, "intelligence_sources": ["b_series", "anomalies", "csid", "iv_dynamics", "flow_physics", "accumulation", "breakout", "options_flow"]}}