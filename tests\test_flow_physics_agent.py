
import unittest
import pandas as pd
from pathlib import Path
import json
from unittest import mock
import sys
import os
from datetime import datetime

# Add the parent directory to the sys.path to allow imports from the 'agents' directory
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.flow_physics_agent import FlowPhysicsAgent
from flowphysics import FlowPhysicsIntegrator, FlowPhysicsResult # Assuming these are in 'flowphysics.py'

class TestFlowPhysicsAgent(unittest.TestCase):

    TEST_DATA_DIR = Path(__file__).parent / "test_data"
    SAMPLE_PARQUET_PATH = TEST_DATA_DIR / "sample_flow_physics_data.parquet"
    OUTPUT_DIR = Path(__file__).parent.parent / "flow_phys" # Agent creates this

    @classmethod
    def setUpClass(cls):
        """Set up test data and directories once for all tests."""
        cls.TEST_DATA_DIR.mkdir(parents=True, exist_ok=True)
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True) # Ensure output dir exists

        # Create a dummy <PERSON>rquet file for testing
        data = {
            'timestamp': [
                datetime(2024, 1, 1, 9, 0, 0),
                datetime(2024, 1, 1, 9, 5, 0),
                datetime(2024, 1, 1, 9, 10, 0),
                datetime(2024, 1, 1, 9, 15, 0),
                datetime(2024, 1, 1, 9, 20, 0)
            ],
            'flow_value': [100.0, 105.0, 112.0, 108.0, 115.0],
            't': [1704063600000, 1704063900000, 1704064200000, 1704064500000, 1704064800000], # Example 't' column
            'v': [1000, 1050, 1120, 1080, 1150], # Example 'v' column
            'c': [0.1, 0.1, 0.1, 0.1, 0.1] # Example 'c' column
        }
        df = pd.DataFrame(data)
        df.to_parquet(cls.SAMPLE_PARQUET_PATH, index=False)
        print(f"Created dummy parquet file at: {cls.SAMPLE_PARQUET_PATH}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test data and directories after all tests."""
        # Clean up the dummy parquet file
        if cls.SAMPLE_PARQUET_PATH.exists():
            cls.SAMPLE_PARQUET_PATH.unlink()
        # Clean up the test data directory if empty
        if cls.TEST_DATA_DIR.exists() and not any(cls.TEST_DATA_DIR.iterdir()):
            cls.TEST_DATA_DIR.rmdir()
        
        # Clean up agent output directory
        # Be careful with rmtree, ensure it's only for test-generated content
        # For this example, we'll just remove the specific output file
        today_str = datetime.now().strftime("%Y-%m-%d")
        output_file_path = cls.OUTPUT_DIR / today_str / "TEST_TICKER_flowphysics.json"
        if output_file_path.exists():
            output_file_path.unlink()
        
        # Remove the daily output directory if empty
        daily_output_dir = cls.OUTPUT_DIR / today_str
        if daily_output_dir.exists() and not any(daily_output_dir.iterdir()):
            daily_output_dir.rmdir()

    @mock.patch('agents.flow_physics_agent.FlowPhysicsIntegrator')
    def test_execute_flow_physics_analysis(self, MockFlowPhysicsIntegrator):
        """Test the execute method of FlowPhysicsAgent."""
        
        # Configure the mock integrator to return a predictable result
        mock_integrator_instance = MockFlowPhysicsIntegrator.return_value
        
        # Create a mock FlowPhysicsResult object
        mock_result = FlowPhysicsResult(
            timestamp=datetime.now(),
            symbol="TEST_TICKER",
            flow_value=115.0,
            flow_velocity=1.0,
            flow_acceleration=0.1,
            flow_jerk=0.01,
            normalized_velocity=0.5,
            normalized_acceleration=0.5,
            normalized_jerk=0.5,
            current_regime="STEADY_FLOW",
            regime_confidence=0.8,
            regime_duration=None,
            institutional_activity=True,
            institutional_direction="accumulation",
            institutional_strength=0.7,
            momentum_shift_detected=False,
            regime_change_detected=False,
            extreme_flow_detected=False,
            timeframe_alignment=0.9,
            dominant_timeframe="5m",
            quality_score=0.95, # High quality score for success
            analysis_metadata={}
        )
        mock_integrator_instance.analyze.return_value = mock_result

        agent = FlowPhysicsAgent()
        ticker = "TEST_TICKER"
        
        # Execute the agent
        output_file_path = agent.execute(ticker, str(self.SAMPLE_PARQUET_PATH))

        # Assert that the integrator's analyze method was called
        MockFlowPhysicsIntegrator.assert_called_once()
        mock_integrator_instance.analyze.assert_called_once()
        
        # Verify the arguments passed to analyze
        args, kwargs = mock_integrator_instance.analyze.call_args
        self.assertEqual(kwargs['symbol'], ticker)
        self.assertIn('flow_data', kwargs)
        self.assertIn('lookback_data', kwargs)
        self.assertIsInstance(kwargs['lookback_data'], pd.DataFrame)
        self.assertGreater(len(kwargs['lookback_data']), 0)

        # Assert that an output file was created
        self.assertTrue(Path(output_file_path).exists())
        self.assertTrue(output_file_path.endswith(f"{ticker}_flowphysics.json"))

        # Load the output file and verify its content
        with open(output_file_path, 'r') as f:
            result_data = json.load(f)
        
        self.assertEqual(result_data['symbol'], ticker)
        self.assertEqual(result_data['quality_score'], mock_result.quality_score)
        self.assertEqual(result_data['current_regime'], mock_result.current_regime)
        self.assertEqual(result_data['institutional_direction'], mock_result.institutional_direction)

        # Test with a low quality score to trigger ValueError
        mock_result_low_quality = FlowPhysicsResult(
            timestamp=datetime.now(),
            symbol="TEST_TICKER_LOW",
            flow_value=100.0, flow_velocity=0.0, flow_acceleration=0.0, flow_jerk=0.0,
            normalized_velocity=0.0, normalized_acceleration=0.0, normalized_jerk=0.0,
            current_regime="UNKNOWN", regime_confidence=0.1, regime_duration=None,
            institutional_activity=False, institutional_direction="neutral", institutional_strength=0.0,
            momentum_shift_detected=False, regime_change_detected=False, extreme_flow_detected=False,
            timeframe_alignment=0.0, dominant_timeframe="5m",
            quality_score=0.1, # Low quality score
            analysis_metadata={}
        )
        mock_integrator_instance.analyze.return_value = mock_result_low_quality
        
        # Reset mock for the second call
        mock_integrator_instance.analyze.reset_mock()

        # Expect a ValueError due to low quality score
        with self.assertRaises(ValueError) as cm:
            agent.execute("TEST_TICKER_LOW", str(self.SAMPLE_PARQUET_PATH))
        
        self.assertIn("Flow-physics quality below threshold", str(cm.exception))
        
        # Clean up the low quality output file if it was created before the exception
        today_str = datetime.now().strftime("%Y-%m-%d")
        low_quality_output_file_path = self.OUTPUT_DIR / today_str / "TEST_TICKER_LOW_flowphysics.json"
        if low_quality_output_file_path.exists():
            low_quality_output_file_path.unlink()

if __name__ == '__main__':
    unittest.main()
