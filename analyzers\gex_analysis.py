#!/usr/bin/env python3
"""
CORE Flow Detection System - GEX Analyzer

Stripped, optimized Gamma Exposure analyzer focusing on:
- Zero-gamma level identification (key inflection points)
- Dealer positioning analysis (gamma concentration zones)
- GEX flip point detection (market structure changes)
- Options flow confirmation for equity moves

Clean implementation with essential gamma exposure mathematics.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from scipy.stats import norm

# Import from CORE data layer
from data.factor_spec import FactorData, DirectionBias, TimeFrame, create_factor
from config.constants import GEX_ANALYSIS

class GEXAnalyzer:
    """
    Core GEX analyzer - gamma exposure calculations.
    Clean, focused implementation for dealer positioning analysis.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Core parameters from constants
        self.gamma_threshold = GEX_ANALYSIS['GAMMA_CONCENTRATION_THRESHOLD']
        self.zero_gamma_tolerance = GEX_ANALYSIS['ZERO_GAMMA_TOLERANCE']
        self.dealer_threshold = GEX_ANALYSIS['DEALER_POSITIONING_THRESHOLD']
        self.min_open_interest = GEX_ANALYSIS['OPTIONS_MIN_OPEN_INTEREST']
        self.strike_range_pct = GEX_ANALYSIS['STRIKE_RANGE_PERCENTAGE']
        self.expiration_days_filter = GEX_ANALYSIS['EXPIRATION_DAYS_FILTER']
        
        # Risk-free rate for Greeks calculation
        self.risk_free_rate = 0.05
        
        # GEX calculation parameters
        self.price_steps = 100
        
    def analyze_factors(self, data_package: Dict[str, Any]) -> List[FactorData]:
        """
        Main analyzer interface - generate GEX factors.
        
        Args:
            data_package: {ticker, current_price, mtf_data, options_data, timestamp}
            
        Returns:
            List[FactorData]: GEX analysis factors
        """
        ticker = data_package.get('ticker', 'UNKNOWN')
        current_price = data_package.get('current_price', 0.0)
        options_data = data_package.get('options_data')
        timestamp = data_package.get('timestamp', datetime.now())
        
        factors = []
        
        try:
            # Check for options data
            if options_data is None or (isinstance(options_data, pd.DataFrame) and options_data.empty):
                # Create synthetic options data for testing
                options_data = self._create_synthetic_options_data(ticker, current_price)
            
            # Perform GEX analysis
            gex_result = self._analyze_gamma_exposure(ticker, current_price, options_data)
            
            # Generate factors from analysis
            factors.extend(self._create_gex_factors(
                ticker, gex_result, timestamp, current_price
            ))
            
            return factors
            
        except Exception as e:
            print(f"GEX analysis error for {ticker}: {e}")
            return factors
    
    def _analyze_gamma_exposure(self, ticker: str, current_price: float, 
                               options_data: pd.DataFrame) -> Dict[str, Any]:
        """Core gamma exposure analysis."""
        
        try:
            # Preprocess options data
            processed_options = self._preprocess_options_data(options_data, current_price)
            
            if processed_options.empty:
                return self._empty_gex_result()
            
            # Define price range for analysis
            price_min = current_price * (1 - self.strike_range_pct)
            price_max = current_price * (1 + self.strike_range_pct)
            price_range = np.linspace(price_min, price_max, self.price_steps)
            
            # Calculate GEX at each price point
            net_gex, call_gex, put_gex = self._calculate_gex_profile(processed_options, price_range)
            
            # Find current GEX level
            current_idx = np.argmin(np.abs(price_range - current_price))
            gex_at_current = net_gex[current_idx]
            
            # Find zero-gamma levels (flip points)
            zero_gamma_levels = self._find_zero_gamma_levels(price_range, net_gex)
            
            # Find gamma concentration zones
            concentration_zones = self._find_gamma_concentration_zones(price_range, net_gex)
            
            # Calculate dealer positioning
            dealer_positioning = self._calculate_dealer_positioning(processed_options, current_price)
            
            return {
                'gex_at_current': gex_at_current,
                'price_range': price_range,
                'net_gex': net_gex,
                'call_gex': call_gex,
                'put_gex': put_gex,
                'zero_gamma_levels': zero_gamma_levels,
                'concentration_zones': concentration_zones,
                'dealer_positioning': dealer_positioning,
                'options_processed': len(processed_options)
            }
            
        except Exception as e:
            print(f"GEX analysis error: {e}")
            return self._empty_gex_result()
    
    def _preprocess_options_data(self, options_data: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """Preprocess and validate options data."""
        try:
            if options_data.empty:
                return pd.DataFrame()
            
            df = options_data.copy()
            
            # Ensure required columns exist
            required_columns = ['strike_price', 'option_type', 'open_interest']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                print(f"Missing required columns: {missing_columns}")
                return pd.DataFrame()
            
            # Calculate days to expiry if missing
            if 'days_to_expiry' not in df.columns:
                if 'expiration_date' in df.columns:
                    df['expiration_date'] = pd.to_datetime(df['expiration_date'])
                    df['days_to_expiry'] = (df['expiration_date'] - datetime.now()).dt.days
                else:
                    df['days_to_expiry'] = 30  # Default
            
            # Add implied volatility if missing
            if 'implied_volatility' not in df.columns:
                df['implied_volatility'] = 0.25  # Default IV
            
            # Filter relevant options
            df = df[
                (df['open_interest'] >= self.min_open_interest) &
                (df['days_to_expiry'] > 0) &
                (df['days_to_expiry'] <= self.expiration_days_filter) &
                (df['strike_price'] > 0)
            ].copy()
            
            # Calculate Greeks
            df = self._calculate_greeks(df, current_price)
            
            return df
            
        except Exception as e:
            print(f"Options preprocessing error: {e}")
            return pd.DataFrame()
    
    def _calculate_greeks(self, df: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """Calculate option Greeks using Black-Scholes."""
        
        greeks_data = []
        
        for _, row in df.iterrows():
            try:
                time_to_expiry = max(row['days_to_expiry'] / 365.0, 1/365)
                iv = row.get('implied_volatility', 0.25)
                
                delta, gamma = self._black_scholes_greeks(
                    current_price,
                    row['strike_price'],
                    time_to_expiry,
                    iv,
                    self.risk_free_rate,
                    str(row['option_type']).lower()
                )
                
                greeks_data.append({
                    'delta': delta,
                    'gamma': gamma
                })
                
            except Exception as e:
                # Use default values for problematic rows
                greeks_data.append({
                    'delta': 0.5 if row['option_type'].lower() == 'call' else -0.5,
                    'gamma': 0.01
                })
        
        greeks_df = pd.DataFrame(greeks_data)
        df['delta'] = greeks_df['delta']
        df['gamma'] = greeks_df['gamma']
        
        return df
    
    def _black_scholes_greeks(self, spot: float, strike: float, time_to_expiry: float,
                             volatility: float, risk_free_rate: float, 
                             option_type: str) -> Tuple[float, float]:
        """Calculate Black-Scholes delta and gamma."""
        
        if time_to_expiry <= 0 or volatility <= 0:
            delta = 0.5 if option_type == 'call' else -0.5
            gamma = 0.01
            return delta, gamma
        
        try:
            # Black-Scholes calculations
            d1 = (np.log(spot / strike) + (risk_free_rate + 0.5 * volatility**2) * time_to_expiry) / \
                 (volatility * np.sqrt(time_to_expiry))
            
            # Delta
            if option_type == 'call':
                delta = norm.cdf(d1)
            else:
                delta = norm.cdf(d1) - 1.0
            
            # Gamma (same for calls and puts)
            gamma = norm.pdf(d1) / (spot * volatility * np.sqrt(time_to_expiry))
            
            # Bounds checking
            delta = max(-1.0, min(1.0, delta))
            gamma = max(0.0, min(10.0, gamma))
            
            return delta, gamma
            
        except Exception:
            # Fallback values
            delta = 0.5 if option_type == 'call' else -0.5
            gamma = 0.01
            return delta, gamma
    
    def _calculate_gex_profile(self, options_df: pd.DataFrame, price_range: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Calculate GEX profile across price range."""
        
        net_gex = np.zeros(len(price_range))
        call_gex = np.zeros(len(price_range))
        put_gex = np.zeros(len(price_range))
        
        try:
            for i, price in enumerate(price_range):
                # Recalculate gamma at each price point for accuracy
                for _, option in options_df.iterrows():
                    
                    # Recalculate gamma at this price
                    time_to_expiry = max(option['days_to_expiry'] / 365.0, 1/365)
                    iv = option.get('implied_volatility', 0.25)
                    
                    _, gamma_at_price = self._black_scholes_greeks(
                        price,
                        option['strike_price'],
                        time_to_expiry,
                        iv,
                        self.risk_free_rate,
                        str(option['option_type']).lower()
                    )
                    
                    # Calculate GEX contribution
                    # GEX = Gamma * Open Interest * 100 * Spot^2 * 0.01
                    gex_contribution = gamma_at_price * option['open_interest'] * 100 * price * price * 0.01
                    
                    # Dealer positioning: dealers are short options (opposite sign)
                    dealer_gex = -gex_contribution
                    
                    if option['option_type'].lower() == 'call':
                        call_gex[i] += dealer_gex
                    else:
                        put_gex[i] += dealer_gex
                    
                    net_gex[i] += dealer_gex
            
            return net_gex, call_gex, put_gex
            
        except Exception as e:
            print(f"GEX profile calculation error: {e}")
            return net_gex, call_gex, put_gex
    
    def _find_zero_gamma_levels(self, price_range: np.ndarray, net_gex: np.ndarray) -> List[float]:
        """Find zero-gamma levels (GEX flip points)."""
        
        zero_levels = []
        
        try:
            # Find sign changes in GEX
            for i in range(1, len(net_gex)):
                if net_gex[i-1] * net_gex[i] < 0:  # Sign change
                    # Interpolate exact zero crossing
                    if abs(net_gex[i] - net_gex[i-1]) > 1e-10:
                        ratio = net_gex[i-1] / (net_gex[i-1] - net_gex[i])
                        zero_price = price_range[i-1] + ratio * (price_range[i] - price_range[i-1])
                        zero_levels.append(zero_price)
            
            # Filter out levels that are too close together
            if zero_levels:
                filtered_levels = [zero_levels[0]]
                for level in zero_levels[1:]:
                    if abs(level - filtered_levels[-1]) / filtered_levels[-1] > 0.02:  # 2% apart
                        filtered_levels.append(level)
                zero_levels = filtered_levels
            
            # Limit to most significant levels
            return zero_levels[:5]
            
        except Exception as e:
            print(f"Zero gamma level detection error: {e}")
            return []
    
    def _find_gamma_concentration_zones(self, price_range: np.ndarray, net_gex: np.ndarray) -> List[Dict[str, Any]]:
        """Find zones of high gamma concentration."""
        
        concentration_zones = []
        
        try:
            # Find local extrema in absolute GEX
            abs_gex = np.abs(net_gex)
            
            # Simple peak detection
            for i in range(1, len(abs_gex) - 1):
                if abs_gex[i] > abs_gex[i-1] and abs_gex[i] > abs_gex[i+1]:
                    # Local maximum
                    if abs_gex[i] > self.gamma_threshold:
                        zone = {
                            'price': price_range[i],
                            'gex_magnitude': abs_gex[i],
                            'gex_value': net_gex[i],
                            'zone_type': 'resistance' if net_gex[i] > 0 else 'support'
                        }
                        concentration_zones.append(zone)
            
            # Sort by magnitude and limit
            concentration_zones.sort(key=lambda x: x['gex_magnitude'], reverse=True)
            return concentration_zones[:3]
            
        except Exception as e:
            print(f"Concentration zone detection error: {e}")
            return []
    
    def _calculate_dealer_positioning(self, options_df: pd.DataFrame, current_price: float) -> Dict[str, Any]:
        """Calculate overall dealer positioning metrics."""
        
        try:
            total_call_oi = options_df[options_df['option_type'].str.lower() == 'call']['open_interest'].sum()
            total_put_oi = options_df[options_df['option_type'].str.lower() == 'put']['open_interest'].sum()
            
            total_oi = total_call_oi + total_put_oi
            
            if total_oi == 0:
                return {
                    'put_call_ratio': 1.0,
                    'dealer_bias': 'neutral',
                    'positioning_strength': 0.0
                }
            
            # Put/Call ratio
            pcr = total_put_oi / total_call_oi if total_call_oi > 0 else float('inf')
            
            # Calculate dealer bias
            if pcr > 1.2:
                dealer_bias = 'bearish'  # More puts = dealers short puts = bearish hedge
                positioning_strength = min(1.0, (pcr - 1.0) / 2.0)
            elif pcr < 0.8:
                dealer_bias = 'bullish'  # More calls = dealers short calls = bullish hedge
                positioning_strength = min(1.0, (1.0 - pcr) / 0.5)
            else:
                dealer_bias = 'neutral'
                positioning_strength = 0.3
            
            # Calculate money-ness distribution
            itm_calls = options_df[(options_df['option_type'].str.lower() == 'call') & 
                                  (options_df['strike_price'] < current_price)]['open_interest'].sum()
            otm_calls = options_df[(options_df['option_type'].str.lower() == 'call') & 
                                  (options_df['strike_price'] > current_price)]['open_interest'].sum()
            
            itm_puts = options_df[(options_df['option_type'].str.lower() == 'put') & 
                                 (options_df['strike_price'] > current_price)]['open_interest'].sum()
            otm_puts = options_df[(options_df['option_type'].str.lower() == 'put') & 
                                 (options_df['strike_price'] < current_price)]['open_interest'].sum()
            
            return {
                'put_call_ratio': pcr,
                'dealer_bias': dealer_bias,
                'positioning_strength': positioning_strength,
                'total_call_oi': total_call_oi,
                'total_put_oi': total_put_oi,
                'itm_calls': itm_calls,
                'otm_calls': otm_calls,
                'itm_puts': itm_puts,
                'otm_puts': otm_puts
            }
            
        except Exception as e:
            print(f"Dealer positioning calculation error: {e}")
            return {
                'put_call_ratio': 1.0,
                'dealer_bias': 'neutral',
                'positioning_strength': 0.0
            }
    
    def _create_synthetic_options_data(self, ticker: str, current_price: float) -> pd.DataFrame:
        """Create synthetic options data when real data unavailable."""
        
        try:
            # Generate strike range
            strikes = np.arange(
                current_price * 0.85,
                current_price * 1.15,
                current_price * 0.01
            )
            
            # Generate expiration dates
            exp_dates = [
                datetime.now() + timedelta(days=30),
                datetime.now() + timedelta(days=60),
                datetime.now() + timedelta(days=90)
            ]
            
            synthetic_data = []
            
            for strike in strikes:
                for exp_date in exp_dates:
                    days_to_exp = (exp_date - datetime.now()).days
                    
                    # Synthetic open interest (higher near the money)
                    moneyness = abs(strike - current_price) / current_price
                    base_oi = max(100, int(1000 * np.exp(-moneyness * 10)))
                    
                    # Create call and put
                    for option_type in ['call', 'put']:
                        synthetic_data.append({
                            'strike_price': strike,
                            'option_type': option_type,
                            'expiration_date': exp_date,
                            'days_to_expiry': days_to_exp,
                            'open_interest': base_oi,
                            'implied_volatility': 0.25 + moneyness * 0.5
                        })
            
            return pd.DataFrame(synthetic_data)
            
        except Exception as e:
            print(f"Synthetic options data creation error: {e}")
            return pd.DataFrame()
    
    def _empty_gex_result(self) -> Dict[str, Any]:
        """Return empty GEX result."""
        return {
            'gex_at_current': 0.0,
            'price_range': np.array([]),
            'net_gex': np.array([]),
            'call_gex': np.array([]),
            'put_gex': np.array([]),
            'zero_gamma_levels': [],
            'concentration_zones': [],
            'dealer_positioning': {
                'put_call_ratio': 1.0,
                'dealer_bias': 'neutral',
                'positioning_strength': 0.0
            },
            'options_processed': 0
        }
    
    def _create_gex_factors(self, ticker: str, gex_result: Dict[str, Any], 
                          timestamp: datetime, current_price: float) -> List[FactorData]:
        """Create FactorData objects from GEX analysis."""
        factors = []
        
        try:
            # Overall GEX factor
            gex_at_current = gex_result['gex_at_current']
            if abs(gex_at_current) > self.gamma_threshold:
                
                if gex_at_current > 0:
                    direction = DirectionBias.BULLISH  # Positive GEX = support
                    gex_type = "Support"
                else:
                    direction = DirectionBias.BEARISH  # Negative GEX = resistance
                    gex_type = "Resistance"
                
                # Normalize strength
                strength = min(1.0, abs(gex_at_current) / (self.gamma_threshold * 3))
                
                factor = create_factor(
                    analyzer_name="GEXAnalysis",
                    factor_name=f"GEX_{gex_type}",
                    direction=direction,
                    strength=strength,
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=0.8,
                    raw_value=gex_at_current,
                    metadata={
                        'gex_at_current': gex_at_current,
                        'gex_type': gex_type,
                        'options_processed': gex_result['options_processed']
                    }
                )
                factors.append(factor)
            
            # Zero-gamma level factors
            zero_gamma_levels = gex_result['zero_gamma_levels']
            for level in zero_gamma_levels:
                
                # Check proximity to current price
                price_diff_pct = abs(current_price - level) / current_price
                
                if price_diff_pct < 0.05:  # Within 5%
                    direction = DirectionBias.NEUTRAL  # Zero-gamma is inflection point
                    strength = max(0.6, 1.0 - (price_diff_pct * 10))  # Closer = stronger
                    
                    factor = create_factor(
                        analyzer_name="GEXAnalysis",
                        factor_name="Zero_Gamma_Level",
                        direction=direction,
                        strength=strength,
                        timeframe=TimeFrame.HOUR_1,
                        timestamp=timestamp,
                        confidence_score=0.7,
                        raw_value=level,
                        metadata={
                            'zero_gamma_level': level,
                            'price_diff_pct': price_diff_pct,
                            'proximity_strength': strength
                        }
                    )
                    factors.append(factor)
            
            # Concentration zone factors
            concentration_zones = gex_result['concentration_zones']
            for zone in concentration_zones:
                
                zone_price = zone['price']
                price_diff_pct = abs(current_price - zone_price) / current_price
                
                if price_diff_pct < 0.03:  # Within 3%
                    
                    if zone['zone_type'] == 'support':
                        direction = DirectionBias.BULLISH
                    else:  # resistance
                        direction = DirectionBias.BEARISH
                    
                    # Strength based on GEX magnitude and proximity
                    magnitude_strength = min(1.0, zone['gex_magnitude'] / (self.gamma_threshold * 2))
                    proximity_strength = max(0.5, 1.0 - (price_diff_pct * 20))
                    strength = (magnitude_strength + proximity_strength) / 2
                    
                    factor = create_factor(
                        analyzer_name="GEXAnalysis",
                        factor_name=f"GEX_Concentration_{zone['zone_type'].title()}",
                        direction=direction,
                        strength=strength,
                        timeframe=TimeFrame.HOUR_1,
                        timestamp=timestamp,
                        confidence_score=0.75,
                        raw_value=zone_price,
                        metadata={
                            'concentration_price': zone_price,
                            'gex_magnitude': zone['gex_magnitude'],
                            'zone_type': zone['zone_type'],
                            'price_diff_pct': price_diff_pct
                        }
                    )
                    factors.append(factor)
            
            # Dealer positioning factor
            dealer_positioning = gex_result['dealer_positioning']
            if dealer_positioning['positioning_strength'] > self.dealer_threshold:
                
                dealer_bias = dealer_positioning['dealer_bias']
                
                if dealer_bias == 'bullish':
                    direction = DirectionBias.BULLISH
                elif dealer_bias == 'bearish':
                    direction = DirectionBias.BEARISH
                else:
                    direction = DirectionBias.NEUTRAL
                
                factor = create_factor(
                    analyzer_name="GEXAnalysis",
                    factor_name=f"Dealer_Positioning_{dealer_bias.title()}",
                    direction=direction,
                    strength=dealer_positioning['positioning_strength'],
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=0.6,
                    raw_value=dealer_positioning['put_call_ratio'],
                    metadata={
                        'dealer_bias': dealer_bias,
                        'put_call_ratio': dealer_positioning['put_call_ratio'],
                        'total_call_oi': dealer_positioning.get('total_call_oi', 0),
                        'total_put_oi': dealer_positioning.get('total_put_oi', 0)
                    }
                )
                factors.append(factor)
            
        except Exception as e:
            print(f"Error creating GEX factors: {e}")
        
        return factors
