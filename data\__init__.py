#!/usr/bin/env python3
"""
CORE Flow Detection System - Data Package

Clean data handling components for flow detection.
"""

from .factor_spec import (
    FactorData, StrategySignal, DirectionBias, TimeFrame, 
    SignalDirection, SignalStrength, validate_factor, 
    validate_signal, create_factor, create_signal
)
from .data_handler import DataHandler
from .api_gateway import APIGateway, get_api_gateway

__all__ = [
    'FactorData',
    'StrategySignal', 
    'DirectionBias',
    'TimeFrame',
    'SignalDirection',
    'SignalStrength',
    'validate_factor',
    'validate_signal',
    'create_factor',
    'create_signal',
    'DataHandler',
    'APIGateway',
    'get_api_gateway'
]
