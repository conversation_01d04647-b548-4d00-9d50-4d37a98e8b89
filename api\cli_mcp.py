#!/usr/bin/env python3
"""
CLI Integration Script for Liquidity Sweep MCP System

This script demonstrates how to integrate the MCP server with your existing
CLI infrastructure and provides examples of all command integrations.
"""

import argparse
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_mcp_argument_parser():
    """Create argument parser with MCP command integration."""
    
    parser = argparse.ArgumentParser(
        description="Liquidity Sweep MCP Command Line Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Start MCP server
  python cli_mcp.py mcp --action server
  
  # Start MCP server interactively
  python cli_mcp.py mcp --action server --interactive
  
  # Test MCP functionality
  python cli_mcp.py mcp --action test --ticker AAPL
  
  # Benchmark MCP performance
  python cli_mcp.py mcp --action benchmark --ticker SPY --iterations 50
  
  # Run MCP diagnostics
  python cli_mcp.py mcp --action diagnose
  
  # Validate AI training readiness
  python cli_mcp.py mcp --action validate
  
  # Check MCP status
  python cli_mcp.py mcp --action status
  
  # List available MCP tools
  python cli_mcp.py mcp --action tools
  
  # Use custom configuration
  python cli_mcp.py mcp --action server --mcp-config config/custom_mcp.json
        """
    )
    
    # Main command (mcp is implied as the command)
    parser.add_argument(
        'command',
        choices=['mcp'],
        help='Command to execute (mcp for MCP operations)'
    )
    
    # MCP-specific arguments
    parser.add_argument(
        '--action',
        choices=['server', 'test', 'benchmark', 'diagnose', 'validate', 'status', 'tools'],
        default='server',
        help='MCP action to perform (default: server)'
    )
    
    parser.add_argument(
        '--ticker',
        type=str,
        help='Ticker symbol for testing/benchmarking (default: SPY)'
    )
    
    parser.add_argument(
        '--iterations',
        type=int,
        default=20,
        help='Number of iterations for benchmarking (default: 20)'
    )
    
    parser.add_argument(
        '--interactive',
        action='store_true',
        help='Start MCP server in interactive mode'
    )
    
    parser.add_argument(
        '--mcp-config',
        type=str,
        help='Path to MCP configuration file'
    )
    
    parser.add_argument(
        '--api-key',
        type=str,
        help='Polygon API key (overrides config file)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='Logging level (default: INFO)'
    )
    
    # Add the action as mcp_action for compatibility
    parser.add_argument(
        '--mcp-action',
        dest='mcp_action',
        help=argparse.SUPPRESS  # Hidden argument for internal use
    )
    
    return parser

def setup_logging(log_level: str):
    """Setup logging configuration."""
    import logging
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def execute_mcp_command(args):
    """Execute MCP command using the command factory."""
    try:
        # Import command factory
        from commands.command_factory import CommandFactory
        
        # Set mcp_action for the command
        args.mcp_action = args.action
        
        # Create command factory and get MCP command
        factory = CommandFactory()
        mcp_command = factory.get_command('mcp')
        
        # Validate arguments
        if not mcp_command.validate(args):
            print(" Invalid arguments provided")
            return 1
        
        # Execute command
        return mcp_command.execute(args)
        
    except Exception as e:
        print(f" Command execution failed: {str(e)}")
        return 1

def print_welcome_banner():
    """Print welcome banner."""
    banner = """

                    Liquidity Sweep MCP Command Interface                     
                                                                              
  Production-grade MCP server with full controller integration               
  Mathematical precision  Zero-tolerance error handling  AI-optimized     

    """
    print(banner)

def print_quick_start():
    """Print quick start guide."""
    print(" Quick Start Guide:")
    print()
    print("1. Start MCP Server:")
    print("   python cli_mcp.py mcp --action server")
    print()
    print("2. Test Functionality:")
    print("   python cli_mcp.py mcp --action test --ticker AAPL")
    print()
    print("3. Interactive Mode:")
    print("   python cli_mcp.py mcp --action server --interactive")
    print()
    print("4. Performance Benchmark:")
    print("   python cli_mcp.py mcp --action benchmark --ticker SPY")
    print()
    print("5. System Diagnostics:")
    print("   python cli_mcp.py mcp --action diagnose")
    print()

def main():
    """Main CLI entry point."""
    try:
        # Print welcome banner
        print_welcome_banner()
        
        # Parse arguments
        parser = create_mcp_argument_parser()
        
        # If no arguments provided, show help and quick start
        if len(sys.argv) == 1:
            parser.print_help()
            print()
            print_quick_start()
            return 0
        
        args = parser.parse_args()
        
        # Setup logging
        setup_logging(args.log_level)
        
        # Execute command
        if args.command == 'mcp':
            return execute_mcp_command(args)
        else:
            print(f" Unknown command: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print("\n Operation cancelled by user")
        return 1
    except Exception as e:
        print(f" CLI error: {str(e)}")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
