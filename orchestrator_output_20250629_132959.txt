Ultimate Orchestrator Test Output
Timestamp: 2025-06-29 13:29:59.894389
Command: py ultimate_orchestrator.py MSFT
Return Code: 0
============================================================
STDOUT:
ULTIMATE TRADING INTELLIGENCE: MSFT
======================================================================
0: DATA INITIALIZATION: Single-source fetch from Schwab MCP...
Training logged: {
  "inputs": {
    "ticker_list": [
      "MSFT"
    ],
    "source": "schwab"
  },
  "outputs": {
    "status": "OK",
    "meta": {
      "MSFT": {
        "bars": 100,
        "opts": 14,
        "source": "schwab",
        "bars_file": "data\\live\\2025-06-29\\MSFT_bars.parquet",
        "opts_file": "data\\live\\2025-06-29\\MSFT_options.parquet"
      }
    },
    "source_used": "schwab",
    "successful_tickers": [
      "MSFT"
    ],
    "output_dir": "data\\live\\2025-06-29"
  }
}
   OK Bars data: 100 records
   OK Options data: 14 records
   OK Single-source data initialization complete (localhost:8005)
1: B-Series: Building Greek features with ROC derivatives...
   Built 52 features
2: A-01: Detecting Greek/IV statistical anomalies...
    Detected 0 anomalies
2.5: F-01: Enhanced CSID institutional flow detection...
    Smart Money Index: 0.000
    Institutional Bias: 0.500
    Flow Regime: mixed
    CSID Signals: 0
3: C-02: Analyzing IV ROC dynamics & regime detection...
Training logged: {
  "inputs": {
    "ticker_list": [
      "MSFT"
    ],
    "source": "schwab"
  },
  "outputs": {
    "status": "OK",
    "meta": {
      "MSFT": {
        "bars": 100,
        "opts": 14,
        "source": "schwab",
        "bars_file": "data\\live\\2025-06-29\\MSFT_bars.parquet",
        "opts_file": "data\\live\\2025-06-29\\MSFT_options.parquet"
      }
    },
    "source_used": "schwab",
    "successful_tickers": [
      "MSFT"
    ],
    "output_dir": "data\\live\\2025-06-29"
  }
}
   IV ROC signals: 0, Regime shift: False
4: F-02: Flow physics modeling & final CSID calculations...
   Flow physics analysis error: Flow-physics quality below threshold: 0.300 < 0.6
5: SPECIALIZED ARMY: Deploying orchestrated masters...
    Using data agent for real-time market data...
    Real-time data acquired: $159.59
    Deploying ENHANCED AccumulationDistribution Master (Agent Zero Ready)...
       ENHANCED Analysis:
         Signal: NEUTRAL (Strength: 0.50)
         Confidence: 0.00 | Tier: UNKNOWN
         Institutional Flow: +0.00
         Market Regime: UNKNOWN
         Agent Zero Ready: 
    Deploying BreakoutValidation Specialist...
Training logged: {
  "inputs": {
    "ticker_list": [
      "MSFT"
    ],
    "source": "schwab"
  },
  "outputs": {
    "status": "OK",
    "meta": {
      "MSFT": {
        "bars": 100,
        "opts": 14,
        "source": "schwab",
        "bars_file": "data\\live\\2025-06-29\\MSFT_bars.parquet",
        "opts_file": "data\\live\\2025-06-29\\MSFT_options.parquet"
      }
    },
    "source_used": "schwab",
    "successful_tickers": [
      "MSFT"
    ],
    "output_dir": "data\\live\\2025-06-29"
  }
}
      Breakout: 0.0% valid (unknown)
    Deploying OptionsFlow Expert...
    Could not get options data: expected an indented block after class definition on line 30 (unified_api_gateway.py, line 32)
      Options data not available
2.7: OPTIONS INTELLIGENCE: Advanced options analysis with learning...
[OK] Greek Engine Adapter initialized
       Options Intelligence analysis failed: AgentTask.__init__() got an unexpected keyword argument 'agent_id'

6: EXTENDED ANALYSIS: Integrating all missing analyzers...
   Running FVG Specialist...
   FVG Analysis: NEUTRAL
   Running Mean Reversion Specialist...
   Mean Reversion: NEUTRAL
   Running Pivot Point Specialist...
   Pivot Points: NEUTRAL
   Running Signal Convergence Orchestrator...
Signal Convergence Dynamic Feed:
  Confidence: 0.5000
  Execution: execute
   Signal Convergence: 0.00
   Running Math Validator...
   Math Validation: 0.00
   Running Signal Quality Agent...
   Signal Quality: 0.00

7: AGENT ZERO: Final ensemble decision making...
    ENSEMBLE INTELLIGENCE SUMMARY:
       Ensemble Score: 42.5%
       Ensemble Decision: NEUTRAL
       Ensemble Confidence: 85.0%
       Active Signals: 14
    AGENT ZERO FINAL DECISION:
       Final Decision: hold
       Confidence: 54.5%
       Enhanced Features: False
       ML Powered: False
       Decision Method: unknown

8: AGENT ZERO CHART GENERATION: Creating decision visualization...
   Agent Zero chart generation failed: ChartGeneratorAgent.__init__() missing 1 required positional argument: 'agent_id'

 ULTIMATE INTELLIGENCE COMPLETE
======================================================================
 Complete intelligence saved: outputs/ultimate_intelligence_MSFT_20250629_132959.json

 Ultimate intelligence pipeline completed for MSFT

 AGENT ZERO ENHANCED INTELLIGENCE SUMMARY:
   Action: hold
   Confidence: 54.5%
   Enhanced: False
   ML Powered: False
   Decision Method: unknown
    Reasoning:
      1. Moderate confidence hold: composite score 0.545
      2. LIQUIDITY SCORE: 0.356 (50% weight - DOMINANT)
      3.   - Vol Liquidity: 0.02
Ultimate Orchestrator Dynamic Feed:
  Confidence: 0.5000
  Execution: hold
Shadow mode: Ultimate orchestrator dynamic data logged

============================================================
STDERR:
2025-06-29 13:29:29,636 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:34,390 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:34,390 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:36,454 - INFO - [enhanced_data_agent] - [['MSFT']] Schwab MCP server healthy - fetching REAL-TIME broker data
2025-06-29 13:29:38,521 - INFO - [enhanced_data_agent] - [['MSFT']] REAL-TIME Schwab broker data validated - Bid: $37.9600, Ask: $38.0000
2025-06-29 13:29:38,521 - INFO - [enhanced_data_agent] - [['MSFT']] REAL-TIME current candle data validated
2025-06-29 13:29:38,521 - INFO - [enhanced_data_agent] - [['MSFT']] Real-time broker spread validated: 0.0011%
2025-06-29 13:29:38,522 - INFO - [enhanced_data_agent] - [['MSFT']] Primary: Schwab broker API data validated
[MSFT] MCP has no options chain data - falling back to BSM calculations
[MSFT] MCP Greeks data unavailable - using BSM calculated Greeks
2025-06-29 13:29:40,606 - INFO - [enhanced_data_agent] - [['MSFT']] Schwab MCP server healthy - fetching REAL-TIME broker data
2025-06-29 13:29:42,676 - INFO - [enhanced_data_agent] - [['MSFT']] REAL-TIME Schwab broker data validated - Bid: $37.9600, Ask: $38.0000
2025-06-29 13:29:42,676 - INFO - [enhanced_data_agent] - [['MSFT']] REAL-TIME current candle data validated
2025-06-29 13:29:42,676 - INFO - [enhanced_data_agent] - [['MSFT']] Real-time broker spread validated: 0.0011%
2025-06-29 13:29:42,676 - INFO - [enhanced_data_agent] - [['MSFT']] Primary: Schwab broker API data validated
[MSFT] MCP has no options chain data - falling back to BSM calculations
[MSFT] MCP Greeks data unavailable - using BSM calculated Greeks
2025-06-29 13:29:44,762 - INFO - [enhanced_data_agent] - [['MSFT']] Schwab MCP server healthy - fetching REAL-TIME broker data
2025-06-29 13:29:46,827 - INFO - [enhanced_data_agent] - [['MSFT']] REAL-TIME Schwab broker data validated - Bid: $37.9600, Ask: $38.0000
2025-06-29 13:29:46,827 - INFO - [enhanced_data_agent] - [['MSFT']] REAL-TIME current candle data validated
2025-06-29 13:29:46,827 - INFO - [enhanced_data_agent] - [['MSFT']] Real-time broker spread validated: 0.0011%
2025-06-29 13:29:46,827 - INFO - [enhanced_data_agent] - [['MSFT']] Primary: Schwab broker API data validated
[MSFT] Real-time anomaly detection failed: 'MSFT'
2025-06-29 13:29:46,847 - agents.agent_zero - INFO - Agent Zero initialized - Training dir: D:\script-work\CORE\training_logs\AgentZero
2025-06-29 13:29:46,847 - agents.agent_zero - INFO - Agent Zero initialized - ML Available: False
2025-06-29 13:29:46,852 - agents.agent_zero - INFO - Training data logged: training_20250629_132946_847.json
2025-06-29 13:29:46,852 - greek_anomaly_agent - INFO - Shadow mode: Anomaly detection logged
2025-06-29 13:29:46,852 - greek_anomaly_agent - INFO - Detected 0 anomalies for MSFT (0 real-time)
2025-06-29 13:29:46,861 - agent.enhanced_csid_agent - INFO - Initializing agent: enhanced_csid_agent
2025-06-29 13:29:46,862 - agent.enhanced_csid_agent - INFO - Agent enhanced_csid_agent initialized successfully
2025-06-29 13:29:46,862 - agent.live_data_gateway_agent - INFO - Initializing agent: live_data_gateway_agent
2025-06-29 13:29:46,863 - agent.live_data_gateway_agent - INFO - Agent live_data_gateway_agent initialized successfully
2025-06-29 13:29:46,863 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:46,863 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:46,864 - live_data_gateway_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:46,864 - agent.enhanced_csid_agent - INFO - Enhanced CSID Agent: Using Data Ingestion Agent for market/options data
2025-06-29 13:29:46,867 - enhanced_csid_analyzer - INFO - Enhanced CSID Analyzer initialized in enhanced_money_flow directory
2025-06-29 13:29:46,867 - agent.enhanced_csid_agent - INFO - Enhanced CSID analyzer initialized successfully from Flow Physics Engine
2025-06-29 13:29:46,870 - agent.enhanced_csid_agent - INFO - Loaded 100 price bars for MSFT
2025-06-29 13:29:46,871 - agent.enhanced_csid_agent - INFO - CSID analysis completed: flow_phys\history\MSFT_csid.json
2025-06-29 13:29:46,879 - agent.iv_dynamics_agent - INFO - Initializing agent: iv_dynamics_agent
2025-06-29 13:29:46,880 - agent.iv_dynamics_agent - INFO - Agent iv_dynamics_agent initialized successfully
2025-06-29 13:29:46,880 - agent.iv_dynamics_agent - INFO - IV Dynamics Agent: Greek Enhancement Engine initialized
2025-06-29 13:29:46,881 - agent.live_data_gateway_agent - INFO - Initializing agent: live_data_gateway_agent
2025-06-29 13:29:46,882 - agent.live_data_gateway_agent - INFO - Agent live_data_gateway_agent initialized successfully
2025-06-29 13:29:46,882 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:46,882 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:46,882 - live_data_gateway_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:46,882 - agent.iv_dynamics_agent - INFO - IV Dynamics Agent: Using Data Ingestion Agent for data access
2025-06-29 13:29:46,882 - agent.iv_dynamics_agent - INFO - Starting IV dynamics analysis for MSFT
2025-06-29 13:29:48,952 - live_data_gateway_agent - INFO - [MSFT] Retrieved 100 historical price bars from Schwab MCP
2025-06-29 13:29:51,029 - agent.iv_dynamics_agent - INFO - Loaded 14 options contracts for MSFT
2025-06-29 13:29:51,031 - agent.iv_dynamics_agent - INFO - Greek Enhancement Engine calculated Greeks for MSFT
2025-06-29 13:29:51,033 - agent.iv_dynamics_agent - INFO - IV analysis saved to: iv_analysis\2025-06-29\MSFT_iv_dynamics.json
2025-06-29 13:29:51,041 - agent.flow_physics_agent - INFO - Initializing agent: flow_physics_agent
2025-06-29 13:29:51,042 - agent.flow_physics_agent - INFO - Agent flow_physics_agent initialized successfully
2025-06-29 13:29:51,042 - Flow_Physics_Engine.advanced.flow_physics_integrator - INFO - Flow Physics Integrator initialized
2025-06-29 13:29:51,042 - agent.flow_physics_agent - INFO - Flow Physics Agent initialized with advanced integrator
2025-06-29 13:29:51,045 - agent.flow_physics_agent - INFO - Loaded 100 price bars for MSFT
2025-06-29 13:29:51,046 - agent.flow_physics_agent - INFO - Created timestamp from 't' column
2025-06-29 13:29:51,046 - agent.flow_physics_agent - INFO - Created flow_value from v * c (live data format)
2025-06-29 13:29:51,047 - agent.flow_physics_agent - ERROR - Flow physics execution failed for MSFT: Flow-physics quality below threshold: 0.300 < 0.6
2025-06-29 13:29:53,124 - INFO - [enhanced_data_agent] - [MSFT] Schwab MCP server healthy - fetching REAL-TIME broker data
2025-06-29 13:29:53,124 - enhanced_data_agent - INFO - [MSFT] Schwab MCP server healthy - fetching REAL-TIME broker data
2025-06-29 13:29:55,181 - INFO - [enhanced_data_agent] - [MSFT] REAL-TIME Schwab broker data validated - Bid: $159.5700, Ask: $159.6000
2025-06-29 13:29:55,181 - enhanced_data_agent - INFO - [MSFT] REAL-TIME Schwab broker data validated - Bid: $159.5700, Ask: $159.6000
2025-06-29 13:29:55,182 - INFO - [enhanced_data_agent] - [MSFT] REAL-TIME current candle data validated
2025-06-29 13:29:55,182 - enhanced_data_agent - INFO - [MSFT] REAL-TIME current candle data validated
2025-06-29 13:29:55,182 - INFO - [enhanced_data_agent] - [MSFT] Real-time broker spread validated: 0.0002%
2025-06-29 13:29:55,182 - enhanced_data_agent - INFO - [MSFT] Real-time broker spread validated: 0.0002%
2025-06-29 13:29:55,182 - INFO - [enhanced_data_agent] - [MSFT] Primary: Schwab broker API data validated
2025-06-29 13:29:55,182 - enhanced_data_agent - INFO - [MSFT] Primary: Schwab broker API data validated
2025-06-29 13:29:55,186 - agent.BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) - INFO - Initializing agent: BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True)
2025-06-29 13:29:55,187 - agent.BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) - INFO - Agent BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) initialized successfully
2025-06-29 13:29:55,187 - agent.live_data_gateway_agent - INFO - Initializing agent: live_data_gateway_agent
2025-06-29 13:29:55,188 - agent.live_data_gateway_agent - INFO - Agent live_data_gateway_agent initialized successfully
2025-06-29 13:29:55,188 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:55,188 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:55,188 - live_data_gateway_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:55,188 - agent.BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) - INFO - Breakout Validation Agent: Using Data Ingestion Agent
2025-06-29 13:29:55,188 - agent.BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) - INFO - Starting breakout validation for MSFT
2025-06-29 13:29:57,258 - live_data_gateway_agent - INFO - [MSFT] Retrieved 100 historical price bars from Schwab MCP
2025-06-29 13:29:59,338 - agent.BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) - INFO - Loaded 100 price bars for MSFT
2025-06-29 13:29:59,340 - agent.BreakoutValidationConfig(timeout_ms=5000, precision_threshold=0.001, enable_validation=True) - ERROR - Failed to save validation: Object of type bool is not JSON serializable
2025-06-29 13:29:59,344 - agent.options_intelligence_continuous_learning - INFO - Initializing agent: options_intelligence_continuous_learning
2025-06-29 13:29:59,345 - agent.options_intelligence_continuous_learning - INFO - Agent options_intelligence_continuous_learning initialized successfully
2025-06-29 13:29:59,346 - agent.live_data_gateway_agent - INFO - Initializing agent: live_data_gateway_agent
2025-06-29 13:29:59,347 - agent.live_data_gateway_agent - INFO - Agent live_data_gateway_agent initialized successfully
2025-06-29 13:29:59,347 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,347 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,347 - live_data_gateway_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,347 - agent.options_intelligence_continuous_learning - INFO - CONSOLIDATED Options Agent: Using Data Ingestion Agent for options/market data
2025-06-29 13:29:59,347 - options_intelligence_services - INFO - Options Intelligence Service initialized with Greek Engine integration
2025-06-29 13:29:59,347 - agent.options_intelligence_continuous_learning - INFO - Advanced OptionsIntelligenceService integrated
2025-06-29 13:29:59,347 - agent.options_intelligence_continuous_learning - WARNING - Learning framework not available
2025-06-29 13:29:59,347 - agents.agent_zero_options_intelligence - INFO - Agent Zero Options Intelligence initialized
2025-06-29 13:29:59,347 - agent.options_intelligence_continuous_learning - INFO - Pure options intelligence module integrated
2025-06-29 13:29:59,347 - agents.agent_zero_options_agent - INFO - CONSOLIDATED Agent Zero Options Agent initialized
2025-06-29 13:29:59,347 - agents.agent_zero_options_agent - INFO - Data Agent: True
2025-06-29 13:29:59,347 - agents.agent_zero_options_agent - INFO - Advanced Intelligence: True
2025-06-29 13:29:59,347 - agents.agent_zero_options_agent - INFO - Learning Framework: False
2025-06-29 13:29:59,347 - agents.agent_zero_options_agent - INFO - Pure Intelligence: True
2025-06-29 13:29:59,348 - agent.fvg_specialist - INFO - Initializing agent: fvg_specialist
2025-06-29 13:29:59,349 - agent.fvg_specialist - INFO - Agent fvg_specialist initialized successfully
2025-06-29 13:29:59,349 - agents.fvg_specialist - INFO - FVG Specialist Agent initialized
2025-06-29 13:29:59,349 - agents.fvg_specialist - ERROR - FVG analysis failed: 'market_data'
2025-06-29 13:29:59,350 - agent.mean_reversion_specialist - INFO - Initializing agent: mean_reversion_specialist
2025-06-29 13:29:59,351 - agent.mean_reversion_specialist - INFO - Agent mean_reversion_specialist initialized successfully
2025-06-29 13:29:59,351 - agents.mean_reversion_specialist - INFO - Mean Reversion Specialist Agent initialized
2025-06-29 13:29:59,351 - agents.mean_reversion_specialist - ERROR - Mean reversion analysis failed: 'market_data'
2025-06-29 13:29:59,351 - agent.pivot_point_specialist - INFO - Initializing agent: pivot_point_specialist
2025-06-29 13:29:59,352 - agent.pivot_point_specialist - INFO - Agent pivot_point_specialist initialized successfully
2025-06-29 13:29:59,352 - agents.pivot_point_specialist - INFO - Pivot Point Specialist Agent initialized
2025-06-29 13:29:59,352 - agents.pivot_point_specialist - ERROR - Pivot point analysis failed: 'market_data'
2025-06-29 13:29:59,353 - agent.signal_convergence_orchestrator - INFO - Initializing agent: signal_convergence_orchestrator
2025-06-29 13:29:59,354 - agent.signal_convergence_orchestrator - INFO - Agent signal_convergence_orchestrator initialized successfully
2025-06-29 13:29:59,354 - agents.signal_convergence_orchestrator - INFO - Signal Convergence Orchestrator initialized
2025-06-29 13:29:59,360 - agents.agent_zero - INFO - Agent Zero initialized - Training dir: D:\script-work\CORE\training_logs\AgentZero
2025-06-29 13:29:59,360 - agents.agent_zero - INFO - Agent Zero initialized - ML Available: False
2025-06-29 13:29:59,360 - dynamic_feed_calculator - INFO - Dynamic Feed Calculator initialized with mathematical calibration
2025-06-29 13:29:59,360 - dynamic_feed_calculator - ERROR - Signal confidence calculation failed: can't multiply sequence by non-int of type 'float'
2025-06-29 13:29:59,360 - dynamic_feed_calculator - ERROR - Signal strength calculation failed: '<' not supported between instances of 'str' and 'float'
2025-06-29 13:29:59,360 - dynamic_feed_calculator - INFO - Dynamic inputs calculated: conf=0.5, strength=0.5, exec=execute, acc=0.9104, prec=0.00354
2025-06-29 13:29:59,361 - agents.agent_zero - INFO - Training data logged: training_20250629_132959_360.json
2025-06-29 13:29:59,361 - agents.signal_convergence_orchestrator - INFO - Shadow mode: Signal convergence dynamic data logged
2025-06-29 13:29:59,362 - agent.math_validator_fadc69c9 - INFO - Initializing agent: math_validator_fadc69c9
2025-06-29 13:29:59,395 - agent.math_validator_fadc69c9 - INFO - Agent math_validator_fadc69c9 initialized successfully
2025-06-29 13:29:59,395 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,395 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,395 - enhanced_greeks - INFO - Enhanced Greeks Engine: MCP primary + BSM fallback initialized
2025-06-29 13:29:59,395 - enhanced_greeks - INFO - BSM Greeks calculator available for fallback
2025-06-29 13:29:59,395 - agent.math_validator_fadc69c9 - INFO - Agent enhanced with MCP+Calculated Greeks capability
2025-06-29 13:29:59,396 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,396 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,396 - agent.math_validator_fadc69c9 - INFO - Math Validator: Enhanced Greeks validation + real-time capability enabled
2025-06-29 13:29:59,396 - agent.math_validator_fadc69c9 - INFO - Received task data with keys: ['symbol', 'task_type', 'timestamp', 'flow_derivatives', 'volume_profiles', 'gex_calculations']
2025-06-29 13:29:59,396 - agent.math_validator_fadc69c9 - INFO - Available data keys: ['symbol', 'task_type', 'timestamp', 'flow_derivatives', 'volume_profiles', 'gex_calculations']
2025-06-29 13:29:59,396 - agent.signal_quality_c090c468 - INFO - Initializing agent: signal_quality_c090c468
2025-06-29 13:29:59,399 - agent.signal_quality_c090c468 - INFO - Agent signal_quality_c090c468 initialized successfully
2025-06-29 13:29:59,399 - INFO - [enhanced_data_agent] - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,399 - enhanced_data_agent - INFO - Enhanced Data Agent initialized - Broker API primary mode
2025-06-29 13:29:59,399 - agent.signal_quality_c090c468 - INFO - Signal Quality Agent initialized with real-time data enhancement
2025-06-29 13:29:59,404 - agents.agent_zero - INFO - Agent Zero initialized - Training dir: D:\script-work\CORE\training_logs\AgentZero
2025-06-29 13:29:59,404 - agents.agent_zero - INFO - Agent Zero initialized - ML Available: False
2025-06-29 13:29:59,410 - agents.agent_zero - INFO - Agent Zero initialized - Training dir: D:\script-work\CORE\training_logs\AgentZero
2025-06-29 13:29:59,410 - agents.agent_zero - INFO - Agent Zero initialized - ML Available: False
2025-06-29 13:29:59,410 - dynamic_feed_calculator - INFO - Dynamic Feed Calculator initialized with mathematical calibration
2025-06-29 13:29:59,410 - dynamic_feed_calculator - INFO - Dynamic inputs calculated: conf=0.5, strength=0.14, exec=hold, acc=0.8845, prec=0.003593
2025-06-29 13:29:59,411 - agents.agent_zero - INFO - Training data logged: training_20250629_132959_410.json
