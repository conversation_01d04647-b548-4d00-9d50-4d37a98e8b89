"""
Unified API Gateway  v13-fixed (Greeks-Enriched)
-------------------------------------------------
* get_options_chain() auto-fills missing Greeks / IV / OI / volume
  by calling get_snapshot_option_contract() only for rows whose delta is NaN.
* Works with any polygon-api-client version (shim for SnapshotClient).
* Falls back to delayed daily bar when Stocks real-time endpoints arent entitled.
"""

from __future__ import annotations

import json
import logging
import os
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import numpy as np
import pandas as pd

# 
# Polygon client compatibility
# 
try:
    from polygon import RESTClient, SnapshotClient  # type: ignore
except ImportError:  # very old polygon-api-client builds
    from polygon import RESTClient  # type: ignore

    class SnapshotClient(RESTClient):  # type: ignore[override]
        pass

def get_default_ticker() -> str:
    """Get default ticker from environment or config"""
    return os.getenv('DEFAULT_TICKER', 'SPY')

def get_active_tickers() -> List[str]:
    """Get list of active tickers from config"""
    config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
    return [t.strip() for t in config_tickers if t.strip()]

def validate_ticker(ticker: str) -> bool:
    """Validate ticker format and availability"""
    if not ticker or len(ticker) < 1 or len(ticker) > 5:
        return False
    return ticker.upper().isalpha()

def get_data_path(ticker: str, suffix: str = '') -> str:
    """Generate standardized data path for ticker"""
    base_path = os.getenv('DATA_PATH', 'data')
    return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"


class SnapshotClientShim:
    """Alias when Snapshot endpoints absent in older client."""

    def get_snapshot_ticker(self, *_, **__):  # noqa: D401
        raise NotImplementedError


# 
# Import enhanced components
# 
try:
    from api_robustness.modules.data.loaders.enhanced_polygon_api import PolygonAPI
    from api_robustness.modules.endpoint_registry import get_endpoint_registry
    ENHANCED_COMPONENTS_AVAILABLE = True
except ImportError:
    ENHANCED_COMPONENTS_AVAILABLE = False
    logging.warning("Enhanced API components not available, using fallback implementation")

# 
# Configuration and setup
# 
def _load_api_config():
    """Load API configuration with fallback options."""
    get_api_config = None
    for _mod in (
        "api_config",
        "config.api_config", 
        "Liquidity_Reports.config.api_config",
    ):
        try:
            _tmp = __import__(_mod, fromlist=["get_api_config"])
            get_api_config = _tmp.get_api_config  # type: ignore[attr-defined]
            break
        except (ImportError, AttributeError):
            continue
    
    if get_api_config is None:
        # Return default config if none found
        return {
            "polygon": {
                "api_key": os.getenv("POLYGON_API_KEY", ""),
                "cache_ttl": int(os.getenv("API_CACHE_TTL", "300")),
                "rate_limit_tier": os.getenv("POLYGON_TIER", "free")
            }
        }
    return get_api_config()

_cfg = _load_api_config().get("polygon", {})
_API_KEY = os.getenv("POLYGON_API_KEY") or _cfg.get("api_key", "")
_CACHE_TTL = int(os.getenv("API_CACHE_TTL", _cfg.get("cache_ttl", 300)))
_RATE_LIMIT_TIER = os.getenv("POLYGON_TIER", _cfg.get("rate_limit_tier", "starter"))

logger = logging.getLogger(__name__)
logger.addHandler(logging.NullHandler())

# 
# Production-Grade Unified API Gateway
# 
class UnifiedAPIGateway:
    """
    Production-grade API gateway with enterprise features.
    
    Features:
    - Mathematical rate limiting with adaptive algorithms
    - Intelligent caching with performance optimization
    - Comprehensive error handling and circuit breaker protection
    - Full Polygon.io endpoint coverage
    - AI agent training optimization
    - Performance monitoring and health metrics
    """
    _instance: Optional["UnifiedAPIGateway"] = None

    def __new__(cls, *args, **kwargs):
        """Singleton pattern for global gateway instance."""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, api_key: str | None = None, cache_ttl: int = _CACHE_TTL,
                 rate_limit_tier: str = _RATE_LIMIT_TIER, use_enhanced: bool = True):
        """
        Initialize the unified API gateway.
        
        Args:
            api_key: Polygon API key
            cache_ttl: Default cache TTL in seconds
            rate_limit_tier: Rate limiting tier (free, starter, developer, professional)
            use_enhanced: Use enhanced components if available
        """
        if getattr(self, "_init", False):
            return
            
        self.api_key = api_key or _API_KEY
        if not self.api_key:
            logger.warning("No API key provided - some functionality may be limited")
        
        # Initialize components based on availability
        if ENHANCED_COMPONENTS_AVAILABLE and use_enhanced and self.api_key:
            self._init_enhanced_components(cache_ttl, rate_limit_tier)
        else:
            self._init_fallback_components(cache_ttl)
        
        self._init = True
        logger.info(f"Unified API Gateway initialized (enhanced={self.enhanced_mode})")
    
    def _init_enhanced_components(self, cache_ttl: int, rate_limit_tier: str):
        """Initialize enhanced API components."""
        self.enhanced_mode = True
        
        # Initialize enhanced API client
        self.enhanced_client = PolygonAPI(
            api_key=self.api_key,
            cache_ttl=cache_ttl,
            rate_limit=self._get_rate_limit_for_tier(rate_limit_tier)
        )
        
        # Initialize endpoint registry
        self.endpoint_registry = get_endpoint_registry()
        
        # Enhanced cache and rate limiter are handled by PolygonAPI
        # Initialize fallback cache for compatibility
        self._cache: dict[str, tuple[Any, float]] = {}
        self._ttl = cache_ttl
        
        # Access enhanced cache if available
        try:
            self._enhanced_cache = self.enhanced_client.cache
        except:
            self._enhanced_cache = None
        
        # Fallback clients for compatibility
        self._client = RESTClient(self.api_key) if self.api_key else None
        self._snap = SnapshotClient(self.api_key) if self.api_key else None
        
        logger.info(f"Enhanced components initialized with {rate_limit_tier} tier")
    
    def _init_fallback_components(self, cache_ttl: int):
        """Initialize fallback components for basic functionality."""
        self.enhanced_mode = False
        
        # Basic polygon clients
        if self.api_key:
            self._client = RESTClient(self.api_key)
            self._snap = SnapshotClient(self.api_key)
        else:
            self._client = None
            self._snap = None
        
        # Basic cache implementation
        self._cache: dict[str, tuple[Any, float]] = {}
        self._ttl = cache_ttl
        
        logger.info("Fallback components initialized")
    
    def _key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        key_parts = [str(arg) for arg in args]
        key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
        return "_".join(key_parts)
    
    def _get(self, key: str) -> Any:
        """Get cached value if not expired."""
        # Try enhanced cache first if available
        if hasattr(self, '_enhanced_cache') and self._enhanced_cache:
            try:
                # Fix: Handle async get() method properly
                if hasattr(self._enhanced_cache, 'get'):
                    import asyncio
                    try:
                        loop = asyncio.get_event_loop()
                        found, value = loop.run_until_complete(self._enhanced_cache.get(key))
                        return value if found else None
                    except RuntimeError:
                        # No event loop running, create new one
                        found, value = asyncio.run(self._enhanced_cache.get(key))
                        return value if found else None
                else:
                    return self._enhanced_cache.get(key)
            except Exception as e:
                logger.warning(f"Enhanced cache get failed: {e}")
        
        # Fallback to basic cache
        if key in self._cache:
            value, timestamp = self._cache[key]
            if time.time() - timestamp < self._ttl:
                return value
            else:
                del self._cache[key]
        return None
    
    def _put(self, key: str, value: Any) -> None:
        """Store value in cache with timestamp."""
        # Try enhanced cache first if available
        if hasattr(self, '_enhanced_cache') and self._enhanced_cache:
            try:
                # Fix: Use put() instead of set() to match ApiCache interface
                if hasattr(self._enhanced_cache, 'put'):
                    import asyncio
                    try:
                        loop = asyncio.get_event_loop()
                        loop.run_until_complete(self._enhanced_cache.put(key, value, ttl=self._ttl))
                    except RuntimeError:
                        # No event loop running, create new one
                        asyncio.run(self._enhanced_cache.put(key, value, ttl=self._ttl))
                    return
                elif hasattr(self._enhanced_cache, 'set'):
                    self._enhanced_cache.set(key, value, ttl=self._ttl)
                    return
            except Exception as e:
                logger.warning(f"Enhanced cache put failed: {e}")
        
        # Fallback to basic cache
        self._cache[key] = (value, time.time())
    
    def _get_rate_limit_for_tier(self, tier: str) -> int:
        """Get rate limit for subscription tier."""
        tier_limits = {
            "free": 5,
            "starter": 300,
            "developer": 3000, 
            "professional": 6000
        }
        return tier_limits.get(tier.lower(), 5)
    
    # ---------------------------------------------------------------- Enhanced API Methods
    def ping(self) -> bool:
        """Test API connectivity."""
        if self.enhanced_mode:
            try:
                result = self.enhanced_client.test_connection()
                return result.get("connected", False)
            except Exception as exc:
                logger.error("Enhanced ping failed: %s", exc)
                return False
        else:
            # Fallback implementation
            try:
                if not self._client:
                    return False
                return bool(self._client.get_market_status().market)
            except Exception as exc:
                logger.error("Fallback ping failed: %s", exc)
                return False

    def get_spot_price(self, ticker: str) -> float:
        """Get current spot price for a ticker."""
        if self.enhanced_mode:
            result = self.enhanced_client.get_stock_price(ticker)
            # Fix: Handle potential coroutine return
            if hasattr(result, '__await__'):
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    result = loop.run_until_complete(result)
                except RuntimeError:
                    result = asyncio.run(result)
            return result
        else:
            # Fallback implementation
            key = self._key("spot", t=ticker)
            if (v := self._get(key)) is not None:
                return v

            def fallback_bar() -> float:
                to_ = datetime.utcnow().strftime("%Y-%m-%d")
                for back in range(1, 8):  # look up to 7 days back
                    frm = (datetime.utcnow() - timedelta(days=back)).strftime("%Y-%m-%d")
                    try:
                        if not self._client:
                            return 0.0
                        bars = list(
                            self._client.list_aggs(
                                ticker=ticker,
                                multiplier=1,
                                timespan="day",
                                from_=frm,
                                to=to_,
                            )
                        )
                        if bars:
                            return float(bars[-1].close)
                    except Exception:
                        continue
                return 0.0

            try:
                if not self._client:
                    return 0.0
                trade = self._client.get_last_trade(ticker)
                price = float(getattr(trade, "price", 0.0)) or float(
                    getattr(self._client.get_last_quote(ticker), "ask_price", 0.0)
                )
                self._put(key, price)
                return price
            except Exception as exc:  # noqa: BLE001
                if "NOT_AUTHORIZED" in str(exc):
                    price = fallback_bar()
                    self._put(key, price)
                    return price
                logger.error("Spot price fetch failed for %s: %s", ticker, exc)
                return 0.0

    def get_options_chain(self, ticker: str, expiry: str | None = None) -> pd.DataFrame:
        """Get options chain with enhanced Greeks and metadata."""
        if self.enhanced_mode:
            # Use enhanced client for better performance and data quality
            options_data = self.enhanced_client.get_options_chain(
                ticker, expiry_date=expiry
            )
            # Fix: Handle potential coroutine return
            if hasattr(options_data, '__await__'):
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    options_data = loop.run_until_complete(options_data)
                except RuntimeError:
                    options_data = asyncio.run(options_data)
                    
            if options_data:
                return pd.DataFrame(options_data)
            return pd.DataFrame()
        else:
            # Fallback to original implementation
            key = self._key("chain", t=ticker, exp=expiry)
            if (df := self._get(key)) is not None:
                return df
            try:
                if not self._client:
                    return pd.DataFrame()
                contracts = list(
                    self._client.list_snapshot_options_chain(underlying_asset=ticker)
                )
                rows: List[Dict[str, Any]] = []
                for c in contracts:
                    d, day, g = c.details, c.day, (c.greeks or None)
                    if expiry and d.expiration_date != expiry:
                        continue
                    rows.append(
                        {
                            "contract": d.ticker,
                            "strike": d.strike_price,
                            "type": d.contract_type.lower(),
                            "expiry": d.expiration_date,
                            "bid": getattr(day, "bid_price", None)
                            or getattr(day, "close", 0.0),
                            "ask": getattr(day, "ask_price", None)
                            or getattr(day, "close", 0.0),
                            "last": getattr(day, "last_price", None)
                            or getattr(day, "close", 0.0),
                            "volume": getattr(day, "volume", 0),
                            "open_interest": getattr(c, "open_interest", 0),
                            "implied_volatility": getattr(c, "implied_volatility", 0.0),
                            "delta": getattr(g, "delta", np.nan) if g else np.nan,
                            "gamma": getattr(g, "gamma", np.nan) if g else np.nan,
                            "theta": getattr(g, "theta", np.nan) if g else np.nan,
                            "vega": getattr(g, "vega", np.nan) if g else np.nan,
                        }
                    )
                df = pd.DataFrame(rows)

                # Enrich missing Greeks using individual contract calls
                missing = df[df["delta"].isna()].index
                for idx in missing:
                    sym = df.at[idx, "contract"]
                    try:
                        snap = self._client.get_snapshot_option_contract(sym)
                        gg = snap.greeks
                        if gg:
                            df.loc[idx, ["delta", "gamma", "theta", "vega"]] = [
                                gg.delta,
                                gg.gamma,
                                gg.theta,
                                gg.vega,
                            ]
                            df.loc[idx, "implied_volatility"] = snap.implied_volatility
                            df.loc[idx, "open_interest"] = snap.open_interest
                            if snap.day:
                                df.loc[idx, "volume"] = snap.day.volume
                    except Exception:
                        continue

                self._put(key, df)
                return df
            except Exception as exc:  # noqa: BLE001
                logger.error("Options chain fetch failed for %s: %s", ticker, exc)
                return pd.DataFrame()

    # ----------------------------------------------------------- PCR
    def get_pcr(self, ticker: str, expiry: str | None = None) -> float:
        df = self.get_options_chain(ticker, expiry)
        if df.empty:
            return 0.0
        puts = df[df.type == "put"].open_interest.sum()
        calls = df[df.type == "call"].open_interest.sum()
        return puts / calls if calls else 0.0

    # ----------------------------------------------------- market depth
    def get_market_depth(self, ticker: str) -> pd.DataFrame:
        try:
            snap = self._snap.get_snapshot_ticker(ticker)
            book = getattr(snap, "book", None)
            if not book:
                return pd.DataFrame()
            rec: list[dict[str, Any]] = []
            for side, lvls in (("bid", book.bids[:5]), ("ask", book.asks[:5])):
                for lvl, ob in enumerate(lvls, 1):
                    rec.append(
                        {"side": side, "level": lvl, "price": ob.price, "size": ob.size}
                    )
            return pd.DataFrame(rec)
        except Exception as exc:  # noqa: BLE001
            if "NOT_AUTHORIZED" in str(exc) or isinstance(exc, NotImplementedError):
                return pd.DataFrame()
            logger.error("Depth fetch failed for %s: %s", ticker, exc)
            return pd.DataFrame()

    # --------------------------------------------------- order book (alias)
    def get_order_book(self, ticker: str, depth: int = 10) -> Optional[Dict[str, Any]]:
        """
        Fetches Level 2 order book data (snapshot) - enhanced format.
        This is an enhanced version of get_market_depth() that returns
        a dictionary format compatible with trading systems.

        Args:
            ticker: Stock ticker symbol
            depth: Number of levels per side (currently limited to 5 by Polygon)

        Returns:
            Dictionary with 'bids', 'asks', and 'timestamp' or None if failed
        """
        key = self._key("order_book", t=ticker, d=depth)
        if (cached := self._get(key)) is not None:
            return cached

        try:
            # Get market depth data
            depth_df = self.get_market_depth(ticker)
            if depth_df.empty:
                logger.warning(f"No order book data available for {ticker}")
                return None

            # Convert to enhanced format
            bids = []
            asks = []

            for _, row in depth_df.iterrows():
                entry = {"price": float(row["price"]), "size": int(row["size"])}
                if row["side"] == "bid":
                    bids.append(entry)
                else:
                    asks.append(entry)

            # Sort bids descending (highest first), asks ascending (lowest first)
            bids.sort(key=lambda x: x["price"], reverse=True)
            asks.sort(key=lambda x: x["price"])

            result = {
                "bids": bids[:depth],
                "asks": asks[:depth],
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }

            self._put(key, result)
            return result

        except Exception as exc:  # noqa: BLE001
            logger.error("Order book fetch failed for %s: %s", ticker, exc)
            return None

    # --------------------------------------------------- Legacy compatibility methods
    def get_current_price(self, ticker: str) -> float:
        """Legacy compatibility: get current spot price."""
        return self.get_spot_price(ticker)
        
    def get_price(self, ticker: str) -> float:
        """Legacy compatibility: get current spot price."""
        return self.get_spot_price(ticker)
        
    def get_historical_data(self, ticker: str, timeframe: str = "1", days: int = 7, **kwargs) -> pd.DataFrame:
        """Legacy compatibility: get historical price data."""
        return self.get_price_data(ticker, timeframe, lookback=days*24, **kwargs)
        
    def get_market_data(self, ticker: str) -> Dict[str, Any]:
        """Get comprehensive market data for a ticker."""
        try:
            current_price = self.get_spot_price(ticker)
            options_chain = self.get_options_chain(ticker)
            market_depth = self.get_market_depth(ticker)
            pcr = self.get_pcr(ticker)
            
            return {
                "ticker": ticker,
                "current_price": current_price,
                "options_chain": options_chain,
                "market_depth": market_depth,
                "pcr": pcr,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as exc:
            logger.error("Market data fetch failed for %s: %s", ticker, exc)
            return {}

    def get_real_market_data(self, ticker: str) -> Dict[str, Any]:
        """Alias for get_market_data to support legacy naming."""
        return self.get_market_data(ticker)

    # --------------------------------------------------- Options specific methods
    def get_options_snapshot(self, option_symbol: str) -> Dict[str, Any]:
        """Get snapshot data for a specific option contract."""
        try:
            if not self._client:
                return {}
            snap = self._client.get_snapshot_option_contract(option_symbol)
            
            result = {
                "contract": option_symbol,
                "last_price": getattr(snap.day, "last_price", 0.0) if snap.day else 0.0,
                "bid": getattr(snap.day, "bid_price", 0.0) if snap.day else 0.0,
                "ask": getattr(snap.day, "ask_price", 0.0) if snap.day else 0.0,
                "volume": getattr(snap.day, "volume", 0) if snap.day else 0,
                "open_interest": getattr(snap, "open_interest", 0),
                "implied_volatility": getattr(snap, "implied_volatility", 0.0),
                "delta": getattr(snap.greeks, "delta", 0.0) if snap.greeks else 0.0,
                "gamma": getattr(snap.greeks, "gamma", 0.0) if snap.greeks else 0.0,
                "theta": getattr(snap.greeks, "theta", 0.0) if snap.greeks else 0.0,
                "vega": getattr(snap.greeks, "vega", 0.0) if snap.greeks else 0.0,
                "timestamp": datetime.utcnow().isoformat()
            }
            return result
        except Exception as exc:
            logger.error("Options snapshot failed for %s: %s", option_symbol, exc)
            return {}

    def get_greeks(self, option_symbol: str) -> Dict[str, float]:
        """Get Greeks for a specific option contract."""
        try:
            snapshot = self.get_options_snapshot(option_symbol)
            return {
                "delta": snapshot.get("delta", 0.0),
                "gamma": snapshot.get("gamma", 0.0),
                "theta": snapshot.get("theta", 0.0),
                "vega": snapshot.get("vega", 0.0)
            }
        except Exception as exc:
            logger.error("Greeks fetch failed for %s: %s", option_symbol, exc)
            return {"delta": 0.0, "gamma": 0.0, "theta": 0.0, "vega": 0.0}

    def get_volume_profile(self, ticker: str, **kwargs) -> pd.DataFrame:
        """Get volume profile data."""
        try:
            # Use price data as basis for volume profile
            price_data = self.get_price_data(ticker, **kwargs)
            if price_data.empty:
                return pd.DataFrame()
            
            # Create basic volume profile from OHLCV data
            price_data['price_range'] = (price_data['high'] + price_data['low']) / 2
            volume_profile = price_data.groupby(
                pd.cut(price_data['price_range'], bins=20)
            )['volume'].sum().reset_index()
            
            return volume_profile
        except Exception as exc:
            logger.error("Volume profile fetch failed for %s: %s", ticker, exc)
            return pd.DataFrame()

    # --------------------------------------------------- Market status methods
    def get_market_status(self) -> Dict[str, Any]:
        """Get current market status."""
        try:
            if not self._client:
                return {"market": "unknown", "timestamp": datetime.utcnow().isoformat()}
            
            status = self._client.get_market_status()
            return {
                "market": getattr(status, "market", "unknown"),
                "servertime": getattr(status, "servertime", datetime.utcnow().isoformat()),
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as exc:
            logger.error("Market status fetch failed: %s", exc)
            return {"market": "unknown", "timestamp": datetime.utcnow().isoformat()}

    # --------------------------------------------------- Quote methods
    def get_last_trade(self, ticker: str) -> Dict[str, Any]:
        """Get last trade data for a ticker."""
        try:
            if not self._client:
                return {}
            
            trade = self._client.get_last_trade(ticker)
            return {
                "price": getattr(trade, "price", 0.0),
                "size": getattr(trade, "size", 0),
                "timestamp": getattr(trade, "participant_timestamp", 0),
                "exchange": getattr(trade, "exchange", ""),
                "ticker": ticker
            }
        except Exception as exc:
            logger.error("Last trade fetch failed for %s: %s", ticker, exc)
            return {}

    def get_last_quote(self, ticker: str) -> Dict[str, Any]:
        """Get last quote data for a ticker."""
        try:
            if not self._client:
                return {}
            
            quote = self._client.get_last_quote(ticker)
            return {
                "bid_price": getattr(quote, "bid_price", 0.0),
                "ask_price": getattr(quote, "ask_price", 0.0),
                "bid_size": getattr(quote, "bid_size", 0),
                "ask_size": getattr(quote, "ask_size", 0),
                "timestamp": getattr(quote, "participant_timestamp", 0),
                "ticker": ticker
            }
        except Exception as exc:
            logger.error("Last quote fetch failed for %s: %s", ticker, exc)
            return {}

    def get_latest_quote(self, ticker: str) -> Dict[str, Any]:
        """Alias for get_last_quote."""
        return self.get_last_quote(ticker)

    # --------------------------------------------------- Aggregation methods
    def list_aggs(self, ticker: str, multiplier: int = 1, timespan: str = "day", 
                  from_: str = None, to: str = None, **kwargs):
        """List aggregates (OHLCV) data - direct polygon client interface."""
        try:
            if not self._client:
                return []
            
            return list(self._client.list_aggs(
                ticker=ticker,
                multiplier=multiplier,
                timespan=timespan,
                from_=from_,
                to=to,
                **kwargs
            ))
        except Exception as exc:
            logger.error("Aggregates fetch failed for %s: %s", ticker, exc)
            return []

    # --------------------------------------------------- Options chain methods
    def list_snapshot_options_chain(self, underlying_asset: str, **kwargs):
        """List options chain snapshot - direct polygon client interface."""
        try:
            if not self._client:
                return []
            
            return list(self._client.list_snapshot_options_chain(
                underlying_asset=underlying_asset, **kwargs
            ))
        except Exception as exc:
            logger.error("Options chain snapshot failed for %s: %s", underlying_asset, exc)
            return []

    def get_snapshot_option_contract(self, option_symbol: str):
        """Get option contract snapshot - direct polygon client interface."""
        try:
            if not self._client:
                return None
            
            return self._client.get_snapshot_option_contract(option_symbol)
        except Exception as exc:
            logger.error("Option contract snapshot failed for %s: %s", option_symbol, exc)
            return None

    def get_snapshot_ticker(self, ticker: str):
        """Get ticker snapshot - direct polygon client interface."""
        try:
            if not self._snap:
                return None
            
            return self._snap.get_snapshot_ticker(ticker)
        except Exception as exc:
            logger.error("Ticker snapshot failed for %s: %s", ticker, exc)
            return None

    # --------------------------------------------------- Trading methods
    def list_trades(self, ticker: str, **kwargs):
        """List trades - direct polygon client interface."""
        try:
            if not self._client:
                return []
            
            return list(self._client.list_trades(ticker=ticker, **kwargs))
        except Exception as exc:
            logger.error("Trades list failed for %s: %s", ticker, exc)
            return []

    # --------------------------------------------------- options trades
    def get_options_trades(self, option_symbol: str, limit: int = 1000) -> pd.DataFrame:
        """
        Fetches recent trades for a specific option contract.

        Args:
            option_symbol: Option contract symbol (Polygon format: O:SPY240119C00400000)
            limit: Maximum number of trades to fetch

        Returns:
            DataFrame with trade data including timestamp, price, size, conditions, exchange
        """
        key = self._key("opt_trades", sym=option_symbol, lim=limit)
        if (df := self._get(key)) is not None:
            return df

        try:
            # Ensure option symbol is in correct Polygon format
            if not option_symbol.startswith("O:"):
                logger.warning(f"Option symbol {option_symbol} might not be in Polygon format (O:...). API call might fail.")

            # Fetch trades using Polygon client
            trades = list(self._client.list_trades(
                ticker=option_symbol,
                limit=limit,
                order="desc"  # Most recent first
            ))

            if not trades:
                logger.info(f"No trades found for option {option_symbol}")
                return pd.DataFrame()

            # Convert to DataFrame
            trades_data = []
            for trade in trades:
                trades_data.append({
                    "timestamp": pd.Timestamp(trade.participant_timestamp, unit="ns"),
                    "price": float(trade.price),
                    "size": int(trade.size),
                    "conditions": getattr(trade, "conditions", []),
                    "exchange": getattr(trade, "exchange", None),
                    "sip_timestamp": pd.Timestamp(getattr(trade, "sip_timestamp", trade.participant_timestamp), unit="ns")
                })

            df = pd.DataFrame(trades_data)
            if not df.empty:
                df = df.sort_values(by="timestamp", ascending=False).reset_index(drop=True)

            self._put(key, df)
            return df

        except Exception as exc:  # noqa: BLE001
            logger.error("Options trades fetch failed for %s: %s", option_symbol, exc)
            return pd.DataFrame()

    # --------------------------------------------------------- OHLCV
    def get_price_data(
        self,
        ticker: str,
        timeframe: str,
        lookback: int = 100,
        *,
        timespan: str = "day",
        multiplier: int = 1,
        from_date: str | None = None,
        to_date: str | None = None,
    ) -> pd.DataFrame:
        key = self._key(
            "price", t=ticker, ts=timespan, m=multiplier, f=from_date, to=to_date
        )
        if (df := self._get(key)) is not None:
            return df

        to_date = to_date or datetime.utcnow().strftime("%Y-%m-%d")
        from_date = from_date or (datetime.utcnow() - timedelta(days=30)).strftime(
            "%Y-%m-%d"
        )

        try:
            rows = [
                {
                    "timestamp": pd.Timestamp(b.timestamp, unit="ms"),
                    "open": b.open,
                    "high": b.high,
                    "low": b.low,
                    "close": b.close,
                    "volume": b.volume,
                    "vwap": b.vwap,
                }
                for b in self._client.list_aggs(
                    ticker=ticker,
                    multiplier=multiplier,
                    timespan=timespan,
                    from_=from_date,
                    to=to_date,
                )
            ]
            df = pd.DataFrame(rows)
            self._put(key, df)
            return df
        except Exception as exc:  # noqa: BLE001
            logger.error("Agg fetch failed for %s: %s", ticker, exc)
            return pd.DataFrame()


# 
# Singleton accessor
# 
_api_gateway: Optional[UnifiedAPIGateway] = None


def get_api_gateway() -> UnifiedAPIGateway:
    """Return shared UnifiedAPIGateway instance."""
    global _api_gateway  # noqa: PLW0603
    if _api_gateway is None:
        _api_gateway = UnifiedAPIGateway()
    return _api_gateway

    def test_connection(self) -> Dict[str, Any]:
        """Test MCP connection status"""
        try:
            # Test basic API functionality
            test_ticker = os.getenv("TEST_TICKER", "SPY")
            price = self.get_current_price(test_ticker)
            
            if price and price > 0:
                return {
                    'connected': True,
                    'status': 'OK',
                    'test_price': price,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'connected': False,
                    'status': 'No data received',
                    'error': 'Failed to get test price'
                }
        except Exception as e:
            return {
                'connected': False,
                'status': 'Error',
                'error': str(e)
            }
