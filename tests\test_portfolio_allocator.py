import unittest
import sys
from pathlib import Path
from datetime import datetime

# Add the parent directory to the sys.path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from Flow_Physics_Engine.portfolio_allocator import (
    PortfolioAllocator, AllocationStrategy, RiskLevel, AllocationResult, 
    get_portfolio_allocator
)


class TestAllocationResult(unittest.TestCase):
    """Test cases for the AllocationResult class."""

    def test_allocation_result_initialization(self):
        """Test AllocationResult initialization."""
        symbol = "AAPL"
        target_weight = 0.1
        position_size = 100.0
        risk_adjusted_size = 90.0
        confidence = 0.8
        reasoning = "Test reasoning"
        
        result = AllocationResult(symbol, target_weight, position_size, 
                                risk_adjusted_size, confidence, reasoning)
        
        self.assertEqual(result.symbol, symbol)
        self.assertEqual(result.target_weight, target_weight)
        self.assertEqual(result.position_size, position_size)
        self.assertEqual(result.risk_adjusted_size, risk_adjusted_size)
        self.assertEqual(result.confidence, confidence)
        self.assertEqual(result.reasoning, reasoning)
        self.assertIsInstance(result.timestamp, datetime)


class TestPortfolioAllocator(unittest.TestCase):
    """Test cases for the PortfolioAllocator class."""

    def setUp(self):
        """Set up test fixtures."""
        self.allocator = PortfolioAllocator(
            total_capital=100000.0,
            max_position_size=0.1,
            risk_level=RiskLevel.MODERATE
        )

    def test_initialization(self):
        """Test portfolio allocator initialization."""
        self.assertEqual(self.allocator.total_capital, 100000.0)
        self.assertEqual(self.allocator.max_position_size, 0.1)
        self.assertEqual(self.allocator.risk_level, RiskLevel.MODERATE)
        self.assertEqual(self.allocator.strategy, AllocationStrategy.RISK_PARITY)

    def test_set_strategy(self):
        """Test strategy setting."""
        self.allocator.set_strategy(AllocationStrategy.MOMENTUM_BASED)
        self.assertEqual(self.allocator.strategy, AllocationStrategy.MOMENTUM_BASED)

    def test_calculate_position_size_basic(self):
        """Test basic position size calculation."""
        result = self.allocator.calculate_position_size("AAPL", 0.8, 0.2, 150.0)
        
        self.assertIsInstance(result, AllocationResult)
        self.assertEqual(result.symbol, "AAPL")
        self.assertGreater(result.target_weight, 0.0)
        self.assertLessEqual(result.target_weight, self.allocator.max_position_size)
        self.assertGreater(result.position_size, 0.0)
        self.assertGreater(result.confidence, 0.0)
        self.assertLessEqual(result.confidence, 1.0)

    def test_calculate_position_size_high_signal(self):
        """Test position size with high signal strength."""
        result = self.allocator.calculate_position_size("AAPL", 1.0, 0.1, 150.0)
        
        self.assertGreater(result.target_weight, 0.05)  # Should be above base
        self.assertGreater(result.confidence, 0.8)  # High confidence with low vol

    def test_calculate_position_size_high_volatility(self):
        """Test position size with high volatility."""
        result = self.allocator.calculate_position_size("AAPL", 0.8, 0.5, 150.0)
        
        # High volatility should reduce position size and confidence
        self.assertLess(result.risk_adjusted_size, result.position_size)
        self.assertLess(result.confidence, 0.8)

    def test_calculate_position_size_zero_price(self):
        """Test position size calculation with zero price."""
        result = self.allocator.calculate_position_size("AAPL", 0.8, 0.2, 0.0)
        
        self.assertEqual(result.position_size, 0.0)
        self.assertGreater(result.target_weight, 0.0)  # Weight should still be calculated

    def test_calculate_position_size_max_limit(self):
        """Test position size capping at maximum limit."""
        # Set very high signal and low volatility to trigger max limit
        result = self.allocator.calculate_position_size("AAPL", 1.0, 0.01, 100.0)
        
        self.assertEqual(result.target_weight, self.allocator.max_position_size)
        self.assertIn("capped at maximum", result.reasoning)

    def test_calculate_position_size_error_handling(self):
        """Test error handling in position size calculation."""
        # Pass invalid values to trigger error path
        with unittest.mock.patch.object(self.allocator, '_calculate_base_weight', 
                                       side_effect=Exception("Test error")):
            result = self.allocator.calculate_position_size("AAPL", 0.8, 0.2, 150.0)
            
            self.assertEqual(result.target_weight, 0.0)
            self.assertEqual(result.position_size, 0.0)
            self.assertEqual(result.confidence, 0.0)
            self.assertIn("Error in calculation", result.reasoning)

    def test_equal_weight_strategy(self):
        """Test equal weight allocation strategy."""
        self.allocator.set_strategy(AllocationStrategy.EQUAL_WEIGHT)
        
        result1 = self.allocator.calculate_position_size("AAPL", 0.5, 0.2, 150.0)
        result2 = self.allocator.calculate_position_size("GOOGL", 0.9, 0.3, 2500.0)
        
        # Base weights should be similar (before risk adjustments)
        base_weight1 = 0.05 * self.allocator.risk_multipliers[RiskLevel.MODERATE]
        base_weight2 = 0.05 * self.allocator.risk_multipliers[RiskLevel.MODERATE]
        
        self.assertAlmostEqual(result1.target_weight, base_weight1, places=3)
        self.assertAlmostEqual(result2.target_weight, base_weight2, places=3)

    def test_momentum_based_strategy(self):
        """Test momentum-based allocation strategy."""
        self.allocator.set_strategy(AllocationStrategy.MOMENTUM_BASED)
        
        result_low = self.allocator.calculate_position_size("AAPL", 0.2, 0.2, 150.0)
        result_high = self.allocator.calculate_position_size("GOOGL", 0.9, 0.2, 2500.0)
        
        # Higher signal strength should result in higher allocation
        self.assertLess(result_low.target_weight, result_high.target_weight)

    def test_risk_level_conservative(self):
        """Test conservative risk level."""
        conservative_allocator = PortfolioAllocator(
            total_capital=100000.0,
            risk_level=RiskLevel.CONSERVATIVE
        )
        
        moderate_result = self.allocator.calculate_position_size("AAPL", 0.8, 0.2, 150.0)
        conservative_result = conservative_allocator.calculate_position_size("AAPL", 0.8, 0.2, 150.0)
        
        # Conservative should have smaller position
        self.assertLess(conservative_result.target_weight, moderate_result.target_weight)

    def test_risk_level_aggressive(self):
        """Test aggressive risk level."""
        aggressive_allocator = PortfolioAllocator(
            total_capital=100000.0,
            risk_level=RiskLevel.AGGRESSIVE,
            max_position_size=0.2  # Increase max to allow difference to show
        )
        
        moderate_result = self.allocator.calculate_position_size("AAPL", 0.8, 0.2, 150.0)
        aggressive_result = aggressive_allocator.calculate_position_size("AAPL", 0.8, 0.2, 150.0)
        
        # Aggressive should have larger position (1.5x multiplier vs 1.0x)
        self.assertGreater(aggressive_result.target_weight, moderate_result.target_weight)

    def test_calculate_portfolio_allocation_basic(self):
        """Test basic portfolio allocation calculation."""
        signals = {
            "AAPL": {"signal_strength": 0.8, "volatility": 0.2, "price": 150.0},
            "GOOGL": {"signal_strength": 0.6, "volatility": 0.3, "price": 2500.0},
            "MSFT": {"signal_strength": 0.7, "volatility": 0.25, "price": 300.0}
        }
        
        allocations = self.allocator.calculate_portfolio_allocation(signals)
        
        self.assertEqual(len(allocations), 3)
        self.assertIn("AAPL", allocations)
        self.assertIn("GOOGL", allocations)
        self.assertIn("MSFT", allocations)
        
        # Check that all allocations are AllocationResult instances
        for symbol, allocation in allocations.items():
            self.assertIsInstance(allocation, AllocationResult)
            self.assertEqual(allocation.symbol, symbol)

    def test_calculate_portfolio_allocation_normalization(self):
        """Test portfolio allocation with normalization when total > 1.0."""
        # Create signals that would normally exceed 100% allocation
        signals = {
            "AAPL": {"signal_strength": 1.0, "volatility": 0.1, "price": 150.0},
            "GOOGL": {"signal_strength": 1.0, "volatility": 0.1, "price": 2500.0},
            "MSFT": {"signal_strength": 1.0, "volatility": 0.1, "price": 300.0},
            "TSLA": {"signal_strength": 1.0, "volatility": 0.1, "price": 800.0},
            "NVDA": {"signal_strength": 1.0, "volatility": 0.1, "price": 900.0}
        }
        
        allocations = self.allocator.calculate_portfolio_allocation(signals)
        total_weight = sum(a.target_weight for a in allocations.values())
        
        # Total weight should be approximately 1.0 or less after normalization
        self.assertLessEqual(total_weight, 1.01)  # Allow small rounding error
        
        # Check that normalization was applied (should be in reasoning)
        for allocation in allocations.values():
            if "Normalized" in allocation.reasoning:
                break
        else:
            # If no explicit normalization in reasoning, weights should naturally be <= 1.0
            pass

    def test_calculate_portfolio_allocation_empty(self):
        """Test portfolio allocation with empty signals."""
        allocations = self.allocator.calculate_portfolio_allocation({})
        self.assertEqual(len(allocations), 0)

    def test_get_rebalancing_recommendations(self):
        """Test rebalancing recommendations."""
        current_positions = {
            "AAPL": 0.15,
            "GOOGL": 0.05,
            "MSFT": 0.0
        }
        
        target_allocations = {
            "AAPL": AllocationResult("AAPL", 0.1, 100, 90, 0.8, "test"),
            "GOOGL": AllocationResult("GOOGL", 0.08, 50, 45, 0.7, "test"),
            "MSFT": AllocationResult("MSFT", 0.12, 75, 70, 0.75, "test")
        }
        
        recommendations = self.allocator.get_rebalancing_recommendations(
            current_positions, target_allocations, threshold=0.02
        )
        
        self.assertEqual(recommendations["AAPL"], "sell")  # 0.15 > 0.1
        self.assertEqual(recommendations["GOOGL"], "buy")  # 0.05 < 0.08 (difference = 0.03 > threshold 0.02)
        self.assertEqual(recommendations["MSFT"], "buy")   # 0.0 < 0.12

    def test_get_rebalancing_recommendations_tight_threshold(self):
        """Test rebalancing with very tight threshold."""
        current_positions = {"AAPL": 0.10}
        target_allocations = {
            "AAPL": AllocationResult("AAPL", 0.101, 100, 90, 0.8, "test")
        }
        
        recommendations = self.allocator.get_rebalancing_recommendations(
            current_positions, target_allocations, threshold=0.001
        )
        
        self.assertEqual(recommendations["AAPL"], "buy")

    def test_get_allocation_summary_empty(self):
        """Test allocation summary with empty allocations."""
        summary = self.allocator.get_allocation_summary({})
        
        expected_keys = ["total_symbols", "total_weight", "average_confidence"]
        for key in expected_keys:
            self.assertIn(key, summary)
        
        self.assertEqual(summary["total_symbols"], 0)
        self.assertEqual(summary["total_weight"], 0.0)
        self.assertEqual(summary["average_confidence"], 0.0)

    def test_get_allocation_summary_with_data(self):
        """Test allocation summary with actual allocations."""
        allocations = {
            "AAPL": AllocationResult("AAPL", 0.1, 100, 90, 0.8, "test"),
            "GOOGL": AllocationResult("GOOGL", 0.08, 50, 45, 0.6, "test"),
            "MSFT": AllocationResult("MSFT", 0.12, 75, 70, 0.9, "test")
        }
        
        summary = self.allocator.get_allocation_summary(allocations)
        
        self.assertEqual(summary["total_symbols"], 3)
        self.assertEqual(summary["total_weight"], 0.3)  # 0.1 + 0.08 + 0.12
        self.assertEqual(summary["average_confidence"], (0.8 + 0.6 + 0.9) / 3)
        self.assertEqual(summary["max_position_weight"], 0.12)
        self.assertEqual(summary["min_position_weight"], 0.08)
        self.assertEqual(summary["strategy"], AllocationStrategy.RISK_PARITY.value)
        self.assertEqual(summary["risk_level"], RiskLevel.MODERATE.value)
        self.assertIn("timestamp", summary)


class TestPortfolioAllocatorDefaults(unittest.TestCase):
    """Test default instance functionality."""

    def test_get_portfolio_allocator(self):
        """Test getting default portfolio allocator."""
        allocator1 = get_portfolio_allocator()
        allocator2 = get_portfolio_allocator()
        
        # Should return the same instance
        self.assertIs(allocator1, allocator2)
        self.assertIsInstance(allocator1, PortfolioAllocator)


class TestAllocationStrategies(unittest.TestCase):
    """Test different allocation strategies in detail."""

    def setUp(self):
        """Set up test fixtures."""
        self.allocator = PortfolioAllocator(total_capital=100000.0)

    def test_risk_parity_strategy(self):
        """Test risk parity strategy calculations."""
        self.allocator.set_strategy(AllocationStrategy.RISK_PARITY)
        
        # High volatility should get lower allocation
        high_vol_result = self.allocator.calculate_position_size("HIGH_VOL", 0.5, 0.4, 100.0)
        low_vol_result = self.allocator.calculate_position_size("LOW_VOL", 0.5, 0.1, 100.0)
        
        self.assertLess(high_vol_result.target_weight, low_vol_result.target_weight)

    def test_volatility_adjusted_strategy(self):
        """Test volatility adjusted strategy."""
        self.allocator.set_strategy(AllocationStrategy.VOLATILITY_ADJUSTED)
        
        # Test with different signal strengths and volatilities
        result1 = self.allocator.calculate_position_size("TEST1", 0.9, 0.2, 100.0)
        result2 = self.allocator.calculate_position_size("TEST2", 0.1, 0.2, 100.0)
        
        # Higher signal should get higher allocation
        self.assertGreater(result1.target_weight, result2.target_weight)

    def test_custom_strategy_fallback(self):
        """Test custom strategy falls back to default."""
        self.allocator.set_strategy(AllocationStrategy.CUSTOM)
        
        result = self.allocator.calculate_position_size("TEST", 0.5, 0.2, 100.0)
        
        # Should fall back to 0.05 base weight
        expected_weight = 0.05 * self.allocator.risk_multipliers[RiskLevel.MODERATE]
        self.assertAlmostEqual(result.target_weight, expected_weight, places=3)


if __name__ == '__main__':
    # Mock unittest to avoid import issues
    import unittest.mock
    unittest.main()