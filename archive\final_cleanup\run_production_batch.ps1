# PowerShell script for CORE multi-ticker execution
# Sets environment and runs the multi-orchestrator

Write-Host "========================================" -ForegroundColor Green
Write-Host "CORE Multi-Ticker Production Execution" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# Set environment variable
$env:ACCOUNT_EQUITY = "25000"

# Execute multi-ticker analysis
Write-Host "Running multi-ticker analysis..." -ForegroundColor Yellow

py multi_orchestrator.py `
    --tickers AAPL,TSLA,NVDA `
    --option_prices 1.50,2.10,3.25 `
    --target_prices 5.00,6.00,10.40 `
    --source mcp-http

if ($LASTEXITCODE -eq 0) {
    Write-Host "SUCCESS: Multi-ticker execution completed" -ForegroundColor Green
} else {
    Write-Host "FAILED: Multi-ticker execution failed with error $LASTEXITCODE" -ForegroundColor Red
}

Write-Host "========================================" -ForegroundColor Green
Write-Host "Check outputs/ directory for results" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
