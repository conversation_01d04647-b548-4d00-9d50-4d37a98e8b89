"""Advanced Acceleration Analyzer

Enhanced acceleration analyzer that calculates flow acceleration (second derivative)
with higher precision detection and institutional intent analysis.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from scipy import signal
from scipy.stats import linregress
import warnings

from . import FLOW_PHYSICS_CONSTANTS

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=RuntimeWarning)


@dataclass
class AccelerationProfile:
    """Represents an acceleration profile at a point in time."""
    timestamp: datetime
    raw_acceleration: float
    smoothed_acceleration: float
    acceleration_direction: str  # 'positive', 'negative', 'neutral'
    acceleration_magnitude: str  # 'low', 'medium', 'high', 'extreme'
    
    # Precision analysis
    precision_score: float  # How precisely calculated (0-1)
    noise_ratio: float  # Signal to noise ratio
    confidence_interval: Tuple[float, float]
    
    # Institutional intent
    institutional_intent: str  # 'accumulating', 'distributing', 'testing', 'neutral'
    intent_confidence: float
    intent_urgency: str  # 'low', 'medium', 'high', 'extreme'
    
    # Regime classification
    acceleration_regime: str  # 'impulse', 'sustained', 'fading', 'reversal'
    regime_stability: float
    
    # Momentum analysis
    momentum_alignment: bool  # Is acceleration aligned with velocity?
    momentum_strength: float
    divergence_detected: bool
    
    # Statistical properties
    acceleration_percentile: float
    acceleration_zscore: float
    volatility_adjusted_acceleration: float
    
    # Predictive indicators
    projected_velocity_change: float
    reversal_probability: float
    continuation_probability: float
    

class AdvancedAccelerationAnalyzer:
    """Advanced analyzer for flow acceleration with institutional intent detection."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the advanced acceleration analyzer.
        
        Args:
            config: Optional configuration overrides
        """
        self.config = config or {}
        self.constants = FLOW_PHYSICS_CONSTANTS
        
        # Analysis parameters
        self.lookback_window = self.config.get('lookback_window', 20)
        self.smoothing_window = self.config.get('smoothing_window', 5)
        self.noise_threshold = self.config.get('noise_threshold', 0.1)
        
        # Thresholds
        self.low_accel_threshold = self.constants['MIN_ACCELERATION_THRESHOLD']
        self.medium_accel_threshold = self.constants['INSTITUTIONAL_ACCELERATION_THRESHOLD']
        self.high_accel_threshold = self.constants['EXTREME_ACCELERATION_THRESHOLD']
        
        # Institutional detection parameters
        self.intent_lookback = self.config.get('intent_lookback', 10)
        self.urgency_multiplier = self.config.get('urgency_multiplier', 2.0)
        
        # Historical data storage
        self._acceleration_history = {}
        self._regime_history = {}
        self._statistics_cache = {}
        
        logger.info("Advanced Acceleration Analyzer initialized")
        
    def analyze(self,
                symbol: str,
                velocity_data: pd.DataFrame,
                current_velocity: float,
                current_flow: float,
                timestamp: Optional[datetime] = None) -> AccelerationProfile:
        """Analyze flow acceleration with advanced features.
        
        Args:
            symbol: Symbol being analyzed
            velocity_data: Historical velocity data
            current_velocity: Current velocity value
            current_flow: Current flow value
            timestamp: Current timestamp
            
        Returns:
            AccelerationProfile with comprehensive acceleration analysis
        """
        timestamp = timestamp or datetime.now()
        
        try:
            # Calculate raw acceleration
            raw_acceleration = self._calculate_raw_acceleration(
                velocity_data, current_velocity
            )
            
            # Apply advanced smoothing
            smoothed_acceleration, precision_score = self._smooth_acceleration(
                symbol, raw_acceleration
            )
            
            # Calculate noise ratio
            noise_ratio = self._calculate_noise_ratio(symbol, raw_acceleration)
            
            # Calculate confidence interval
            confidence_interval = self._calculate_confidence_interval(
                symbol, smoothed_acceleration, precision_score
            )
            
            # Detect institutional intent
            intent_analysis = self._detect_institutional_intent(
                symbol, smoothed_acceleration, current_velocity, current_flow
            )
            
            # Classify acceleration regime
            regime_analysis = self._classify_acceleration_regime(
                symbol, smoothed_acceleration, current_velocity
            )
            
            # Analyze momentum alignment
            momentum_analysis = self._analyze_momentum_alignment(
                smoothed_acceleration, current_velocity
            )
            
            # Calculate statistical properties
            stats = self._calculate_acceleration_statistics(
                symbol, smoothed_acceleration
            )
            
            # Generate predictive indicators
            predictions = self._generate_predictions(
                symbol, smoothed_acceleration, current_velocity
            )
            
            # Determine basic properties
            direction = self._determine_direction(smoothed_acceleration)
            magnitude = self._determine_magnitude(smoothed_acceleration)
            
            # Create acceleration profile
            profile = AccelerationProfile(
                timestamp=timestamp,
                raw_acceleration=raw_acceleration,
                smoothed_acceleration=smoothed_acceleration,
                acceleration_direction=direction,
                acceleration_magnitude=magnitude,
                precision_score=precision_score,
                noise_ratio=noise_ratio,
                confidence_interval=confidence_interval,
                institutional_intent=intent_analysis['intent'],
                intent_confidence=intent_analysis['confidence'],
                intent_urgency=intent_analysis['urgency'],
                acceleration_regime=regime_analysis['regime'],
                regime_stability=regime_analysis['stability'],
                momentum_alignment=momentum_analysis['aligned'],
                momentum_strength=momentum_analysis['strength'],
                divergence_detected=momentum_analysis['divergence'],
                acceleration_percentile=stats['percentile'],
                acceleration_zscore=stats['zscore'],
                volatility_adjusted_acceleration=stats['vol_adjusted'],
                projected_velocity_change=predictions['velocity_change'],
                reversal_probability=predictions['reversal_prob'],
                continuation_probability=predictions['continuation_prob']
            )
            
            # Store in history
            self._update_history(symbol, profile)
            
            return profile
            
        except Exception as e:
            logger.error(f"Error in acceleration analysis: {e}")
            return self._create_default_profile(timestamp, current_velocity)
            
    def _calculate_raw_acceleration(self,
                                  velocity_data: pd.DataFrame,
                                  current_velocity: float) -> float:
        """Calculate raw acceleration from velocity data."""
        if velocity_data.empty or len(velocity_data) < 2:
            return 0.0
            
        # Get recent velocity points
        recent_data = velocity_data.tail(5).copy()
        
        # Add current point
        current_point = pd.DataFrame({
            'timestamp': [datetime.now()],
            'velocity': [current_velocity]
        })
        recent_data = pd.concat([recent_data, current_point], ignore_index=True)
        
        # Convert timestamps to seconds
        recent_data['time_seconds'] = recent_data['timestamp'].apply(
            lambda x: x.timestamp() if isinstance(x, datetime) else x
        )
        
        # Calculate acceleration using numpy gradient
        velocities = recent_data['velocity'].values
        times = recent_data['time_seconds'].values
        
        if len(velocities) >= 2:
            # Use gradient for smoother derivative
            accelerations = np.gradient(velocities, times)
            return float(accelerations[-1])
        else:
            return 0.0
            
    def _smooth_acceleration(self, 
                           symbol: str,
                           raw_acceleration: float) -> Tuple[float, float]:
        """Apply advanced smoothing with precision scoring."""
        if symbol not in self._acceleration_history:
            return raw_acceleration, 0.5
            
        history = self._acceleration_history[symbol]
        if len(history) < self.smoothing_window:
            return raw_acceleration, 0.5
            
        # Get recent accelerations
        recent_accels = [h.raw_acceleration for h in history[-self.smoothing_window:]]
        recent_accels.append(raw_acceleration)
        
        # Apply Savitzky-Golay filter for smooth derivative
        if len(recent_accels) >= 5:
            try:
                smoothed = signal.savgol_filter(recent_accels, 5, 2)[-1]
            except:
                smoothed = np.mean(recent_accels)
        else:
            smoothed = np.mean(recent_accels)
            
        # Calculate precision score based on data consistency
        precision_score = self._calculate_precision_score(recent_accels)
        
        return smoothed, precision_score
        
    def _calculate_precision_score(self, accelerations: List[float]) -> float:
        """Calculate how precisely we can determine acceleration."""
        if len(accelerations) < 3:
            return 0.3
            
        # Check data consistency
        std_dev = np.std(accelerations)
        mean_abs = np.mean(np.abs(accelerations))
        
        if mean_abs > 0:
            consistency = 1 - min(1, std_dev / mean_abs)
        else:
            consistency = max(0.2, min(0.9, 1 - (std_dev / (mean_abs + 0.001))))
            
        # Check for outliers
        q1, q3 = np.percentile(accelerations, [25, 75])
        iqr = q3 - q1
        outliers = sum(1 for a in accelerations 
                      if a < q1 - 1.5*iqr or a > q3 + 1.5*iqr)
        outlier_ratio = outliers / len(accelerations)
        
        # Combined precision score
        precision = consistency * (1 - outlier_ratio)
        return max(0.1, min(1.0, precision))
        
    def _calculate_noise_ratio(self, symbol: str, raw_acceleration: float) -> float:
        """Calculate signal to noise ratio."""
        if symbol not in self._acceleration_history or len(self._acceleration_history[symbol]) < 5:
            return 0.5
            
        history = self._acceleration_history[symbol]
        recent_raw = [h.raw_acceleration for h in history[-10:]]
        recent_raw.append(raw_acceleration)
        
        # Calculate signal (trend) vs noise (variance from trend)
        if len(recent_raw) >= 5:
            # Fit linear trend
            x = np.arange(len(recent_raw))
            y = np.array(recent_raw)
            
            try:
                slope, intercept, _, _, _ = linregress(x, y)
                trend = slope * x + intercept
                
                # Calculate residuals
                residuals = y - trend
                signal_power = np.var(trend)
                noise_power = np.var(residuals)
                
                if noise_power > 0:
                    snr = signal_power / noise_power
                    return min(10.0, max(0.1, snr))
            except:
                pass
                
        return 1.0
        
    def _calculate_confidence_interval(self,
                                     symbol: str,
                                     acceleration: float,
                                     precision_score: float) -> Tuple[float, float]:
        """Calculate confidence interval for acceleration."""
        if symbol not in self._acceleration_history or len(self._acceleration_history[symbol]) < 5:
            # Wide interval for low data
            margin = abs(acceleration) * 0.5
            return (acceleration - margin, acceleration + margin)
            
        history = self._acceleration_history[symbol]
        recent_smoothed = [h.smoothed_acceleration for h in history[-20:]]
        
        # Calculate standard error
        std_error = np.std(recent_smoothed) / np.sqrt(len(recent_smoothed))
        
        # Adjust by precision score
        adjusted_error = std_error / precision_score
        
        # 95% confidence interval (approximately 2 standard errors)
        margin = 2 * adjusted_error
        
        return (acceleration - margin, acceleration + margin)
        
    def _detect_institutional_intent(self,
                                   symbol: str,
                                   acceleration: float,
                                   velocity: float,
                                   flow: float) -> Dict[str, Any]:
        """Detect institutional intent from acceleration patterns."""
        # Determine basic intent from acceleration direction and magnitude
        abs_accel = abs(acceleration)
        
        if abs_accel < self.low_accel_threshold:
            intent = 'neutral'
            confidence = 0.3
        else:
            # Check acceleration-velocity alignment
            if acceleration > 0 and velocity > 0:
                intent = 'accumulating'
            elif acceleration < 0 and velocity < 0:
                intent = 'distributing'
            elif abs(acceleration) > abs(velocity) * self.urgency_multiplier:
                intent = 'testing'  # Rapid acceleration suggests position testing
            else:
                intent = 'neutral'
                
            # Calculate confidence based on magnitude and consistency
            magnitude_score = min(1.0, abs_accel / self.high_accel_threshold)
            
            # Check historical consistency
            consistency_score = self._calculate_intent_consistency(symbol, intent)
            
            confidence = (magnitude_score + consistency_score) / 2
            
        # Determine urgency
        if abs_accel > self.high_accel_threshold:
            urgency = 'extreme'
        elif abs_accel > self.medium_accel_threshold:
            urgency = 'high'
        elif abs_accel > self.low_accel_threshold:
            urgency = 'medium'
        else:
            urgency = 'low'
            
        return {
            'intent': intent,
            'confidence': confidence,
            'urgency': urgency
        }
        
    def _calculate_intent_consistency(self, symbol: str, current_intent: str) -> float:
        """Calculate how consistent the intent has been."""
        if symbol not in self._acceleration_history or len(self._acceleration_history[symbol]) < self.intent_lookback:
            return 0.5
            
        history = self._acceleration_history[symbol]
        recent_intents = [h.institutional_intent for h in history[-self.intent_lookback:]]
        
        # Count matching intents
        matching = sum(1 for intent in recent_intents if intent == current_intent)
        consistency = matching / len(recent_intents)
        
        return consistency
        
    def _classify_acceleration_regime(self,
                                    symbol: str,
                                    acceleration: float,
                                    velocity: float) -> Dict[str, Any]:
        """Classify the acceleration regime."""
        if symbol not in self._acceleration_history or len(self._acceleration_history[symbol]) < 3:
            return {'regime': 'unknown', 'stability': 0.0}
            
        history = self._acceleration_history[symbol]
        recent_accels = [h.smoothed_acceleration for h in history[-5:]]
        recent_accels.append(acceleration)
        
        # Analyze acceleration pattern
        if self._is_impulse_acceleration(recent_accels):
            regime = 'impulse'
        elif self._is_sustained_acceleration(recent_accels):
            regime = 'sustained'
        elif self._is_fading_acceleration(recent_accels):
            regime = 'fading'
        elif self._is_reversal_acceleration(recent_accels, velocity):
            regime = 'reversal'
        else:
            regime = 'neutral'
            
        # Calculate regime stability
        stability = self._calculate_regime_stability(symbol, regime)
        
        # Update regime history
        self._update_regime_history(symbol, regime)
        
        return {
            'regime': regime,
            'stability': stability
        }
        
    def _is_impulse_acceleration(self, accelerations: List[float]) -> bool:
        """Check if acceleration shows impulse pattern (sudden spike)."""
        if len(accelerations) < 3:
            return False
            
        latest = abs(accelerations[-1])
        previous_avg = np.mean([abs(a) for a in accelerations[:-1]])
        
        return latest > previous_avg * 3
        
    def _is_sustained_acceleration(self, accelerations: List[float]) -> bool:
        """Check if acceleration is sustained in one direction."""
        if len(accelerations) < 3:
            return False
            
        # Check if all have same sign and significant magnitude
        signs = [np.sign(a) for a in accelerations if abs(a) > self.low_accel_threshold]
        
        if len(signs) >= 3:
            return all(s == signs[0] for s in signs)
        return False
        
    def _is_fading_acceleration(self, accelerations: List[float]) -> bool:
        """Check if acceleration is fading (decreasing magnitude)."""
        if len(accelerations) < 3:
            return False
            
        # Check if magnitude is decreasing
        magnitudes = [abs(a) for a in accelerations]
        
        # Count decreasing steps
        decreasing = sum(1 for i in range(1, len(magnitudes)) 
                        if magnitudes[i] < magnitudes[i-1])
        
        return decreasing >= len(magnitudes) * 0.6
        
    def _is_reversal_acceleration(self, accelerations: List[float], velocity: float) -> bool:
        """Check if acceleration indicates reversal (opposite to velocity)."""
        if len(accelerations) < 2:
            return False
            
        latest_accel = accelerations[-1]
        
        # Strong acceleration opposite to velocity indicates reversal
        return (np.sign(latest_accel) != np.sign(velocity) and 
                abs(latest_accel) > self.medium_accel_threshold)
        
    def _calculate_regime_stability(self, symbol: str, current_regime: str) -> float:
        """Calculate stability of current regime."""
        if symbol not in self._regime_history:
            return 0.0
            
        regime_hist = self._regime_history[symbol]
        if not regime_hist or regime_hist['current'] != current_regime:
            return 0.0
            
        # Calculate duration in current regime
        duration = datetime.now() - regime_hist['start_time']
        duration_minutes = duration.total_seconds() / 60
        
        # Stability increases with duration, up to a point
        stability = min(1.0, duration_minutes / 30)  # Max stability at 30 minutes
        
        return stability
        
    def _update_regime_history(self, symbol: str, regime: str):
        """Update regime history tracking."""
        if symbol not in self._regime_history:
            self._regime_history[symbol] = {
                'current': regime,
                'start_time': datetime.now(),
                'history': []
            }
        else:
            regime_hist = self._regime_history[symbol]
            if regime_hist['current'] != regime:
                # Regime changed
                regime_hist['history'].append({
                    'regime': regime_hist['current'],
                    'duration': datetime.now() - regime_hist['start_time']
                })
                regime_hist['current'] = regime
                regime_hist['start_time'] = datetime.now()
                
                # Limit history
                if len(regime_hist['history']) > 20:
                    regime_hist['history'] = regime_hist['history'][-20:]
                    
    def _analyze_momentum_alignment(self,
                                  acceleration: float,
                                  velocity: float) -> Dict[str, Any]:
        """Analyze alignment between acceleration and velocity (momentum)."""
        # Check if acceleration and velocity have same sign
        aligned = np.sign(acceleration) == np.sign(velocity)
        
        # Calculate momentum strength
        if aligned:
            # Reinforcing momentum
            strength = min(1.0, (abs(acceleration) + abs(velocity)) / 2)
        else:
            # Opposing forces
            strength = abs(abs(acceleration) - abs(velocity)) / max(abs(acceleration), abs(velocity), 1e-6)
            
        # Detect divergence
        divergence = not aligned and abs(acceleration) > self.medium_accel_threshold
        
        return {
            'aligned': aligned,
            'strength': strength,
            'divergence': divergence
        }
        
    def _calculate_acceleration_statistics(self,
                                         symbol: str,
                                         acceleration: float) -> Dict[str, Any]:
        """Calculate statistical properties of acceleration."""
        if symbol not in self._acceleration_history or len(self._acceleration_history[symbol]) < 5:
            return {
                'percentile': 50.0,
                'zscore': 0.0,
                'vol_adjusted': acceleration
            }
            
        history = self._acceleration_history[symbol]
        historical_accels = [h.smoothed_acceleration for h in history]
        
        # Calculate percentile
        sorted_accels = sorted(historical_accels)
        pos = np.searchsorted(sorted_accels, acceleration)
        percentile = (pos / len(sorted_accels)) * 100
        
        # Calculate z-score
        mean = np.mean(historical_accels)
        std = np.std(historical_accels)
        zscore = (acceleration - mean) / std if std > 0 else 0.0
        
        # Calculate volatility-adjusted acceleration
        if std > 0:
            vol_adjusted = acceleration / std
        else:
            vol_adjusted = acceleration
            
        return {
            'percentile': percentile,
            'zscore': zscore,
            'vol_adjusted': vol_adjusted
        }
        
    def _generate_predictions(self,
                            symbol: str,
                            acceleration: float,
                            velocity: float) -> Dict[str, Any]:
        """Generate predictive indicators based on acceleration."""
        # Project velocity change
        time_horizon = 300  # 5 minutes in seconds
        projected_velocity_change = acceleration * time_horizon
        
        # Calculate reversal probability
        reversal_prob = self._calculate_reversal_probability(
            symbol, acceleration, velocity
        )
        
        # Calculate continuation probability
        continuation_prob = 1 - reversal_prob
        
        return {
            'velocity_change': projected_velocity_change,
            'reversal_prob': reversal_prob,
            'continuation_prob': continuation_prob
        }
        
    def _calculate_reversal_probability(self,
                                      symbol: str,
                                      acceleration: float,
                                      velocity: float) -> float:
        """Calculate probability of flow reversal."""
        prob = 0.0
        
        # Factor 1: Opposing acceleration and velocity
        if np.sign(acceleration) != np.sign(velocity):
            prob += 0.3
            
        # Factor 2: Extreme acceleration
        if abs(acceleration) > self.high_accel_threshold:
            prob += 0.2
            
        # Factor 3: Historical reversal patterns
        if symbol in self._regime_history:
            regime_hist = self._regime_history[symbol]
            if regime_hist['current'] == 'reversal':
                prob += 0.3
                
        # Factor 4: Fading momentum
        if symbol in self._acceleration_history and len(self._acceleration_history[symbol]) >= 5:
            recent = self._acceleration_history[symbol][-5:]
            if all(np.sign(h.smoothed_acceleration) != np.sign(velocity) for h in recent):
                prob += 0.2
                
        return min(1.0, prob)
        
    def _determine_direction(self, acceleration: float) -> str:
        """Determine acceleration direction."""
        if abs(acceleration) < self.low_accel_threshold * 0.5:
            return 'neutral'
        elif acceleration > 0:
            return 'positive'
        else:
            return 'negative'
            
    def _determine_magnitude(self, acceleration: float) -> str:
        """Determine acceleration magnitude category."""
        abs_accel = abs(acceleration)
        
        if abs_accel < self.low_accel_threshold:
            return 'low'
        elif abs_accel < self.medium_accel_threshold:
            return 'medium'
        elif abs_accel < self.high_accel_threshold:
            return 'high'
        else:
            return 'extreme'
            
    def _update_history(self, symbol: str, profile: AccelerationProfile):
        """Update acceleration history for symbol."""
        if symbol not in self._acceleration_history:
            self._acceleration_history[symbol] = []
            
        self._acceleration_history[symbol].append(profile)
        
        # Limit history size
        max_history = self.lookback_window * 2
        if len(self._acceleration_history[symbol]) > max_history:
            self._acceleration_history[symbol] = self._acceleration_history[symbol][-max_history:]
            
    def _create_default_profile(self, timestamp: datetime, velocity: float) -> AccelerationProfile:
        """Create default profile when analysis fails."""
        return AccelerationProfile(
            timestamp=timestamp,
            raw_acceleration=0.0,
            smoothed_acceleration=0.0,
            acceleration_direction='neutral',
            acceleration_magnitude='low',
            precision_score=0.0,
            noise_ratio=1.0,
            confidence_interval=(0.0, 0.0),
            institutional_intent='neutral',
            intent_confidence=0.0,
            intent_urgency='low',
            acceleration_regime='unknown',
            regime_stability=0.0,
            momentum_alignment=False,
            momentum_strength=0.0,
            divergence_detected=False,
            acceleration_percentile=50.0,
            acceleration_zscore=0.0,
            volatility_adjusted_acceleration=0.0,
            projected_velocity_change=0.0,
            reversal_probability=0.5,
            continuation_probability=0.5
        )
        
    def get_acceleration_summary(self, symbol: str) -> Dict[str, Any]:
        """Get summary of acceleration analysis for symbol."""
        if symbol not in self._acceleration_history:
            return {'status': 'no_data'}
            
        history = self._acceleration_history[symbol]
        if not history:
            return {'status': 'no_data'}
            
        recent = history[-1]
        accelerations = [h.smoothed_acceleration for h in history]
        
        return {
            'status': 'active',
            'current_acceleration': recent.smoothed_acceleration,
            'direction': recent.acceleration_direction,
            'magnitude': recent.acceleration_magnitude,
            'institutional_intent': recent.institutional_intent,
            'regime': recent.acceleration_regime,
            'momentum_aligned': recent.momentum_alignment,
            'avg_acceleration': np.mean(accelerations),
            'max_acceleration': max(accelerations),
            'min_acceleration': min(accelerations),
            'volatility': np.std(accelerations)
        }