"""
SYSTEM CONSOLIDATION VALIDATOR
Mathematical validation of agent system consolidation opportunities
"""

import os
import sys
import json
import time
import requests
from pathlib import Path

class ConsolidationValidator:
    def __init__(self, base_path="D:/script-work/CORE"):
        self.base_path = Path(base_path)
        self.agents_path = self.base_path / "agents"
        self.results = {
            "validation_timestamp": time.strftime("%Y%m%d_%H%M%S"),
            "mcp_status": {},
            "agent_analysis": {},
            "consolidation_opportunities": [],
            "efficiency_calculations": {}
        }
    
    def validate_mcp_connectivity(self):
        """Validate MCP server operational status"""
        try:
            health_response = requests.get("http://localhost:8005/health", timeout=5)
            spy_quotes = requests.get("http://localhost:8005/quotes/SPY", timeout=5)
            spy_options = requests.get("http://localhost:8005/options/SPY", timeout=5)
            
            self.results["mcp_status"] = {
                "health_status": health_response.status_code,
                "quotes_status": spy_quotes.status_code,
                "options_status": spy_options.status_code,
                "operational": all([
                    health_response.status_code == 200,
                    spy_quotes.status_code == 200,
                    spy_options.status_code == 200
                ])
            }
            return self.results["mcp_status"]["operational"]
        except Exception as e:
            self.results["mcp_status"] = {
                "operational": False,
                "error": str(e)
            }
            return False
    
    def analyze_agent_files(self):
        """Analyze current agent file structure"""
        agent_files = list(self.agents_path.glob("*.py"))
        agent_dirs = [d for d in self.agents_path.iterdir() if d.is_dir()]
        
        self.results["agent_analysis"] = {
            "total_agent_files": len(agent_files),
            "total_agent_directories": len(agent_dirs),
            "agent_files": [f.name for f in agent_files],
            "agent_directories": [d.name for d in agent_dirs]
        }
        
        # Identify consolidation opportunities
        self.identify_consolidation_opportunities()
        return self.results["agent_analysis"]
    
    def identify_consolidation_opportunities(self):
        """Mathematical identification of consolidation opportunities"""
        opportunities = []
        
        # CSID Agent Analysis
        csid_files = [f for f in self.results["agent_analysis"]["agent_files"] 
                     if "csid" in f.lower()]
        if len(csid_files) > 1:
            opportunities.append({
                "type": "CSID_CONSOLIDATION",
                "files": csid_files,
                "potential_reduction": len(csid_files) - 1,
                "description": "Multiple CSID agent versions detected"
            })
        
        # Liquidity Agent Analysis
        liquidity_files = [f for f in self.results["agent_analysis"]["agent_files"] 
                          if "liquidity" in f.lower()]
        if len(liquidity_files) > 1:
            main_agents = [f for f in liquidity_files if f == "liquidity_agent.py"]
            utility_files = [f for f in liquidity_files if f != "liquidity_agent.py"]
            if main_agents and utility_files:
                opportunities.append({
                    "type": "LIQUIDITY_CONSOLIDATION",
                    "main_agent": main_agents[0],
                    "utility_files": utility_files,
                    "potential_reduction": len(utility_files),
                    "description": "Liquidity utility files can be integrated"
                })
        
        # Signal Agent Analysis
        signal_files = [f for f in self.results["agent_analysis"]["agent_files"] 
                       if "signal" in f.lower()]
        if len(signal_files) >= 3:
            opportunities.append({
                "type": "SIGNAL_CONSOLIDATION",
                "files": signal_files,
                "potential_reduction": 1,  # Conservative estimate
                "description": "Signal processing agents may be consolidatable"
            })
        
        self.results["consolidation_opportunities"] = opportunities
        return opportunities
    
    def calculate_efficiency_gains(self):
        """Mathematical calculation of potential efficiency gains"""
        current_agents = self.results["agent_analysis"]["total_agent_files"]
        total_potential_reduction = sum(
            opp["potential_reduction"] for opp in self.results["consolidation_opportunities"]
        )
        
        if current_agents > 0:
            potential_efficiency_gain = (total_potential_reduction / current_agents) * 100
        else:
            potential_efficiency_gain = 0
        
        self.results["efficiency_calculations"] = {
            "current_agent_count": current_agents,
            "potential_reductions": total_potential_reduction,
            "potential_efficiency_gain_percent": round(potential_efficiency_gain, 2),
            "previous_consolidation_gain": 14.8,  # From FINAL_AGENT_CONSOLIDATION_COMPLETE
            "cumulative_potential_gain": round(14.8 + potential_efficiency_gain, 2)
        }
        
        return self.results["efficiency_calculations"]
    
    def validate_consolidated_agents(self):
        """Validate previously consolidated agents are operational"""
        consolidated_agents = [
            "order_router_agent.py",
            "agent_zero_options_agent.py"
        ]
        
        consolidated_status = {}
        for agent in consolidated_agents:
            agent_path = self.agents_path / agent
            consolidated_status[agent] = {
                "exists": agent_path.exists(),
                "size_bytes": agent_path.stat().st_size if agent_path.exists() else 0
            }
        
        # Check orchestrator
        orchestrator_path = self.agents_path / "agent_orchestrator" / "agent_orchestrator.py"
        consolidated_status["agent_orchestrator.py"] = {
            "exists": orchestrator_path.exists(),
            "size_bytes": orchestrator_path.stat().st_size if orchestrator_path.exists() else 0
        }
        
        self.results["consolidated_agent_status"] = consolidated_status
        return consolidated_status
    
    def generate_consolidation_report(self):
        """Generate comprehensive consolidation analysis report"""
        report = {
            "CONSOLIDATION_ANALYSIS_SUMMARY": {
                "timestamp": self.results["validation_timestamp"],
                "mcp_operational": self.results["mcp_status"]["operational"],
                "current_agent_count": self.results["agent_analysis"]["total_agent_files"],
                "consolidation_opportunities": len(self.results["consolidation_opportunities"]),
                "potential_efficiency_gain": f"{self.results['efficiency_calculations']['potential_efficiency_gain_percent']}%"
            },
            "DETAILED_ANALYSIS": self.results
        }
        
        return report
    
    def run_full_validation(self):
        """Execute complete consolidation validation process"""
        print("SYSTEM CONSOLIDATION VALIDATOR INITIATED")
        print("=" * 60)
        
        # Step 1: MCP Connectivity
        print("Validating MCP Server Connectivity...")
        mcp_status = self.validate_mcp_connectivity()
        print(f"   MCP Status: {'OPERATIONAL' if mcp_status else 'OFFLINE'}")
        
        # Step 2: Agent Analysis
        print("\nAnalyzing Agent Structure...")
        agent_analysis = self.analyze_agent_files()
        print(f"   Total Agents: {agent_analysis['total_agent_files']}")
        print(f"   Agent Directories: {agent_analysis['total_agent_directories']}")
        
        # Step 3: Consolidation Opportunities
        print("\nIdentifying Consolidation Opportunities...")
        opportunities = self.results["consolidation_opportunities"]
        for opp in opportunities:
            print(f"   {opp['type']}: {opp['potential_reduction']} agents reducible")
        
        # Step 4: Efficiency Calculations
        print("\nCalculating Efficiency Gains...")
        efficiency = self.calculate_efficiency_gains()
        print(f"   Potential Gain: {efficiency['potential_efficiency_gain_percent']}%")
        print(f"   Cumulative Gain: {efficiency['cumulative_potential_gain']}%")
        
        # Step 5: Consolidated Agent Validation
        print("\nValidating Previously Consolidated Agents...")
        consolidated_status = self.validate_consolidated_agents()
        for agent, status in consolidated_status.items():
            status_icon = "OK" if status["exists"] else "MISSING"
            print(f"   {status_icon} {agent}: {status['size_bytes']} bytes")
        
        # Generate Final Report
        report = self.generate_consolidation_report()
        
        print("\n" + "=" * 60)
        print("CONSOLIDATION VALIDATION COMPLETE")
        print(f"Efficiency Opportunities: {len(opportunities)} identified")
        print(f"Potential System Gain: {efficiency['potential_efficiency_gain_percent']}%")
        
        return report

def main():
    """Execute consolidation validation"""
    validator = ConsolidationValidator()
    report = validator.run_full_validation()
    
    # Save detailed report
    report_path = Path("D:/script-work/CORE") / f"consolidation_analysis_{report['CONSOLIDATION_ANALYSIS_SUMMARY']['timestamp']}.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\nDetailed report saved: {report_path}")
    return report

if __name__ == "__main__":
    main()
