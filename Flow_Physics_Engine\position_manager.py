"""
Position Manager Module - External Engine Version

Simplified position management for the External Flow Physics Engine.
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class Position:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Simplified position class."""
    
    def __init__(self, symbol: str, size: float, entry_price: float):
        """Initialize position."""
        self.symbol = symbol
        self.size = size
        self.entry_price = entry_price
        self.entry_time = datetime.now()
        self.current_price = entry_price
    
    def update_price(self, new_price: float):
        """Update current price."""
        self.current_price = new_price
    
    def get_pnl(self) -> float:
        """Get current P&L."""
        return (self.current_price - self.entry_price) * self.size
    
    def get_pnl_percent(self) -> float:
        """Get P&L as percentage."""
        if self.entry_price == 0:
            return 0.0
        return (self.current_price - self.entry_price) / self.entry_price


class PositionManager:
    """Simplified position manager for external engine."""
    
    def __init__(self):
        """Initialize position manager."""
        self.positions: Dict[str, Position] = {}
        self.total_value = 100000.0  # Default portfolio value
        logger.info("Position manager initialized")
    
    def add_position(self, symbol: str, size: float, entry_price: float) -> bool:
        """Add a new position."""
        try:
            position = Position(symbol, size, entry_price)
            self.positions[symbol] = position
            logger.info(f"Added position: {symbol} size={size} price={entry_price}")
            return True
        except Exception as e:
            logger.error(f"Failed to add position {symbol}: {e}")
            return False
    
    def close_position(self, symbol: str) -> Optional[float]:
        """Close a position and return P&L."""
        if symbol in self.positions:
            position = self.positions[symbol]
            pnl = position.get_pnl()
            del self.positions[symbol]
            logger.info(f"Closed position: {symbol} P&L={pnl}")
            return pnl
        return None
    
    def update_price(self, symbol: str, new_price: float):
        """Update position price."""
        if symbol in self.positions:
            self.positions[symbol].update_price(new_price)
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for symbol."""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """Get all positions."""
        return self.positions.copy()
    
    def get_total_pnl(self) -> float:
        """Get total portfolio P&L."""
        return sum(pos.get_pnl() for pos in self.positions.values())
    
    def get_position_count(self) -> int:
        """Get number of open positions."""
        return len(self.positions)
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary."""
        total_pnl = self.get_total_pnl()
        position_count = self.get_position_count()
        
        return {
            "total_value": self.total_value,
            "total_pnl": total_pnl,
            "total_value_current": self.total_value + total_pnl,
            "position_count": position_count,
            "pnl_percent": total_pnl / self.total_value if self.total_value > 0 else 0.0,
            "positions": {symbol: {
                "size": pos.size,
                "entry_price": pos.entry_price,
                "current_price": pos.current_price,
                "pnl": pos.get_pnl(),
                "pnl_percent": pos.get_pnl_percent()
            } for symbol, pos in self.positions.items()}
        }


# Default instance
default_position_manager = PositionManager()


def get_position_manager() -> PositionManager:
    """Get default position manager instance."""
    return default_position_manager


if __name__ == "__main__":
    # Test the position manager
    pm = PositionManager()
    
    print("Testing External Engine Position Manager...")
    
    # Test adding position
    pm.add_position("AAPL", 100, 150.0)
    print(f"Added AAPL position")
    
    # Test updating price
    pm.update_price("AAPL", 155.0)
    print(f"Updated AAPL price to $155")
    
    # Test portfolio summary
    summary = pm.get_portfolio_summary()
    print(f"Portfolio summary: {summary}")
    
    # Test closing position
    pnl = pm.close_position("AAPL")
    print(f"Closed AAPL position with P&L: {pnl}")
