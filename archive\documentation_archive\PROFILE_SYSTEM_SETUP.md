# CORE Profile System - Complete Setup

##  **DUAL-ENVIRONMENT CONFIGURATION SYSTEM**

The CORE system now supports **dual-environment deployment** with clean profile separation for VM production and Windows development environments.

##  **IMPLEMENTATION COMPLETE**

### ** Configuration Files**
- `settings.yml` - Profile definitions (vm_iso, win_quick)
- `utils/profile_loader.py` - Profile management system
- `run_core_vm.sh` - VM production runner
- `run_core_win.ps1` - Windows quick tester
- Enhanced orchestrators with profile support

### ** Profile Specifications**

#### **VM Production Profile (vm_iso)**
```yaml
account_equity: 25000
data_source: "mcp-http"
agent_zero_mode: "active"
schedule_cron: "*/30 13-20 * * 1-5"  # Every 30 min, trading hours, weekdays
tradier_token_env: "TRADIER_TOKEN"
fills_dir: "/home/<USER>/fills"
logs_dir: "/home/<USER>/logs"
training_logs_dir: "/home/<USER>/training_logs"
```

#### **Windows Development Profile (win_quick)**
```yaml
account_equity: 10000
data_source: "polygon"
agent_zero_mode: "off"
schedule_cron: null                  # Manual execution only
tradier_token_env: null              # No auto broker
fills_dir: "D:\\script-work\\CORE\\fills"
logs_dir: "D:\\script-work\\CORE\\logs"
training_logs_dir: null              # No training logs when Agent Zero off
```

##  **ENVIRONMENT SEPARATION**

### **VM Production Environment**
- **Purpose**: Live paper trading with Agent Zero learning
- **Schedule**: Automated execution every 30 minutes during trading hours
- **Features**: Full system including auto broker, Agent Zero active
- **Output**: Linux paths (`/home/<USER>/`)
- **Data**: MCP-HTTP with high reliability

### **Windows Development Environment**  
- **Purpose**: Quick testing and development validation
- **Schedule**: Manual execution only
- **Features**: Analysis only, no auto broker, Agent Zero disabled
- **Output**: Windows paths (`D:\script-work\CORE\`)
- **Data**: Polygon API for development testing

##  **USAGE INSTRUCTIONS**

### **Profile Management**
```bash
# List available profiles
python utils/profile_loader.py --list

# Apply a profile manually
python utils/profile_loader.py --apply vm_iso

# Show profile details
python utils/profile_loader.py --show win_quick
```

### **VM Production Execution**
```bash
# Manual run with VM profile
python orchestrator.py --profile vm_iso --ticker AAPL --option_price 1.50 --target_price 5.00

python multi_orchestrator.py --profile vm_iso --tickers AAPL,TSLA,NVDA --option_prices 1.55,2.10,3.20 --target_prices 5.0,6.0,10.0

# Automated run (for cron scheduling)
./run_core_vm.sh
```

### **Windows Development Execution**
```powershell
# Manual run with Windows profile
python orchestrator.py --profile win_quick --ticker AAPL --option_price 1.50 --target_price 5.00

# Quick test script
.\run_core_win.ps1
```

##  **AGENT ZERO INTEGRATION**

### **Profile-Based Agent Zero Control**
```python
# Automatic profile-based control
agent_zero_mode = os.getenv("AGENT_ZERO_MODE", "off")

if agent_zero_mode == "active":
    # Full Agent Zero decision making
    decision = AgentZeroAdvisor().should_execute(signal, risk)
    if decision == "veto":
        raise ValueError("Agent Zero vetoed trade.")
        
elif agent_zero_mode == "shadow":
    # Agent Zero observes for training, doesn't affect execution
    AgentZeroObserver().record_decision(signal, risk, execution_result)
    
else:  # "off"
    # No Agent Zero involvement
    pass
```

### **Agent Zero Modes**
- **Active**: Agent Zero can veto trades and make decisions
- **Shadow**: Agent Zero observes for training but doesn't interfere  
- **Off**: Agent Zero completely disabled

##  **DIRECTORY MANAGEMENT**

### **Profile-Specific Paths**
```python
# Automatic directory creation based on profile
fills_dir = os.getenv("FILLS_DIR", "fills")
logs_dir = os.getenv("LOGS_DIR", "logs")
training_logs_dir = os.getenv("TRAINING_LOGS_DIR")

# Directories created automatically when profile applied
Path(fills_dir).mkdir(parents=True, exist_ok=True)
```

### **Directory Structure**
```
VM Environment (/home/<USER>/):
 fills/           # Trading fill confirmations
 logs/            # System execution logs
 training_logs/   # Agent Zero learning data

Windows Environment (D:\script-work\CORE\):
 fills\           # Development fill confirmations
 logs\            # Development execution logs  
 outputs\         # Analysis outputs
```

##  **SCHEDULING SETUP**

### **VM Cron Schedule**
```bash
# Add to crontab for automated execution
*/30 13-20 * * 1-5 /path/to/CORE/run_core_vm.sh

# Or use the cron string from profile
crontab -e
# Add: */30 13-20 * * 1-5 /home/<USER>/CORE/run_core_vm.sh
```

### **Windows Task Scheduler**
- **Manual execution**: Double-click `run_core_win.ps1`
- **Scheduled**: Use Windows Task Scheduler if needed
- **Development**: Typically manual execution for testing

##  **DEPLOYMENT WORKFLOW**

### **1. VM Production Setup**
```bash
# Set environment
export TRADIER_TOKEN="Bearer YOUR_SANDBOX_TOKEN"

# Test profile
python utils/profile_loader.py --apply vm_iso

# Test execution
./run_core_vm.sh

# Add to cron
crontab -e
# */30 13-20 * * 1-5 /home/<USER>/CORE/run_core_vm.sh
```

### **2. Windows Development Setup**
```powershell
# Test profile
python utils/profile_loader.py --apply win_quick

# Test execution
.\run_core_win.ps1

# Manual testing as needed
python orchestrator.py --profile win_quick --ticker AAPL --option_price 1.50 --target_price 5.00
```

##  **KEY BENEFITS**

### **Clean Separation**
- **No code changes** required to switch environments
- **Profile-based configuration** handles all differences
- **Zero environment variable conflicts**

### **Development Safety**
- **Windows profile** prevents accidental live trading
- **Agent Zero disabled** in development environment
- **Separate directories** prevent data mixing

### **Production Reliability**
- **VM profile** optimized for automated execution
- **Agent Zero active** for intelligent decision making
- **Automated scheduling** for consistent execution

### **Flexibility**
- **Override capability** for any profile setting
- **Manual execution** possible in any environment
- **Easy profile switching** without code modification

##  **NEXT STEPS**

1. **Set up VM environment** with vm_iso profile
2. **Configure Tradier sandbox** tokens for auto broker
3. **Test scheduling** with cron automation
4. **Monitor execution** logs and fill confirmations
5. **Develop Agent Zero** learning capabilities

---

**Status**:  **PRODUCTION READY** dual-environment system  
**Profiles**:  **VM production** and **Windows development** configured  
**Agent Zero**:  **Profile-controlled** activation system  
**Automation**:  **Cron-ready** with scheduling support
