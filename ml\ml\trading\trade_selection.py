"""
Trade Selection module for Trading System

This module implements different trade selection algorithms for filtering
and prioritizing trade signals.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional
import logging

# Import from project root
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# from src.ml.trading.trade_model import TradeSignal, MarketRegime  # External dependency
try:
    from .trade_model import TradeSignal, MarketRegime
except ImportError:
    # Fallback definitions
    class TradeSignal:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)
    
    class MarketRegime:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

# Set up logging
logger = logging.getLogger(__name__)

class TradeSelector:
    """Base class for trade selectors."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the trade selector.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
    
    def select_trades(self, 
                      signals: List[TradeSignal], 
                      market_data: pd.DataFrame,
                      regime: Optional[MarketRegime] = None) -> List[TradeSignal]:
        """
        Select and prioritize trade signals.
        
        Args:
            signals: List of trade signals
            market_data: Market data
            regime: Optional market regime
            
        Returns:
            List of selected trade signals
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def save(self) -> Dict[str, Any]:
        """
        Save the selector state.
        
        Returns:
            Serializable state dictionary
        """
        return {"config": self.config}
    
    def load(self, state: Dict[str, Any]) -> bool:
        """
        Load selector state.
        
        Args:
            state: State dictionary
            
        Returns:
            True if successful, False otherwise
        """
        if "config" in state:
            self.config = state["config"]
            return True
        return False


class ProbabilityTradeSelector(TradeSelector):
    """Trade selector that filters by probability and risk-reward."""
    
    def select_trades(self, 
                      signals: List[TradeSignal], 
                      market_data: pd.DataFrame,
                      regime: Optional[MarketRegime] = None) -> List[TradeSignal]:
        """
        Select trades based on probability and risk-reward.
        
        Args:
            signals: List of trade signals
            market_data: Market data
            regime: Optional market regime
            
        Returns:
            List of selected trade signals
        """
        # Get filter settings
        min_probability = self.config.get('min_probability', 0.65)
        min_risk_reward = self.config.get('min_risk_reward', 1.5)
        min_liquidity_score = self.config.get('min_liquidity_score', 0.7)
        
        # Apply filters
        selected_signals = []
        for signal in signals:
            if signal.probability >= min_probability and \
               signal.risk_reward_ratio >= min_risk_reward and \
               signal.liquidity_score >= min_liquidity_score:
                selected_signals.append(signal)
        
        # Sort by expected value (probability-weighted returns)
        selected_signals.sort(key=lambda s: s.expected_value, reverse=True)
        
        return selected_signals


class ExpectedValueTradeSelector(TradeSelector):
    """Trade selector that filters by expected value."""
    
    def select_trades(self, 
                      signals: List[TradeSignal], 
                      market_data: pd.DataFrame,
                      regime: Optional[MarketRegime] = None) -> List[TradeSignal]:
        """
        Select trades based on expected value.
        
        Args:
            signals: List of trade signals
            market_data: Market data
            regime: Optional market regime
            
        Returns:
            List of selected trade signals
        """
        # Get filter settings
        min_expected_value = self.config.get('min_expected_value', 0.02)  # 2% EV
        min_liquidity_score = self.config.get('min_liquidity_score', 0.7)
        
        # Apply filters
        selected_signals = []
        for signal in signals:
            if signal.expected_value >= min_expected_value and \
               signal.liquidity_score >= min_liquidity_score:
                selected_signals.append(signal)
        
        # Sort by expected value
        selected_signals.sort(key=lambda s: s.expected_value, reverse=True)
        
        return selected_signals


class RegimeAwareTradeSelector(TradeSelector):
    """Trade selector that adjusts criteria based on market regime."""
    
    def select_trades(self, 
                      signals: List[TradeSignal], 
                      market_data: pd.DataFrame,
                      regime: Optional[MarketRegime] = None) -> List[TradeSignal]:
        """
        Select trades with regime-specific criteria.
        
        Args:
            signals: List of trade signals
            market_data: Market data
            regime: Optional market regime
            
        Returns:
            List of selected trade signals
        """
        # Default settings
        min_probability = self.config.get('min_probability', 0.65)
        min_risk_reward = self.config.get('min_risk_reward', 1.5)
        min_liquidity_score = self.config.get('min_liquidity_score', 0.7)
        
        # Adjust based on regime
        if regime is not None:
            if regime == MarketRegime.BULL:
                # More aggressive in bull market
                min_probability -= 0.05
                min_risk_reward -= 0.2
            elif regime == MarketRegime.BEAR:
                # More conservative in bear market
                min_probability += 0.05
                min_risk_reward += 0.5
            elif regime == MarketRegime.VOLATILE:
                # Higher liquidity requirements in volatile markets
                min_liquidity_score += 0.1
        
        # Apply filters
        selected_signals = []
        for signal in signals:
            # In bear market, prefer shorts
            if regime == MarketRegime.BEAR and signal.direction == "long":
                # Increase threshold for longs in bear market
                if signal.probability >= (min_probability + 0.1) and \
                   signal.risk_reward_ratio >= (min_risk_reward + 0.5) and \
                   signal.liquidity_score >= min_liquidity_score:
                    selected_signals.append(signal)
            # In bull market, prefer longs
            elif regime == MarketRegime.BULL and signal.direction == "short":
                # Increase threshold for shorts in bull market
                if signal.probability >= (min_probability + 0.1) and \
                   signal.risk_reward_ratio >= (min_risk_reward + 0.5) and \
                   signal.liquidity_score >= min_liquidity_score:
                    selected_signals.append(signal)
            # Normal filtering for aligned direction or other regimes
            elif signal.probability >= min_probability and \
                 signal.risk_reward_ratio >= min_risk_reward and \
                 signal.liquidity_score >= min_liquidity_score:
                selected_signals.append(signal)
        
        # Sort by expected value
        selected_signals.sort(key=lambda s: s.expected_value, reverse=True)
        
        return selected_signals


def create_trade_selector(selector_type: str, config: Dict[str, Any] = None) -> TradeSelector:
    """
    Create a trade selector of the specified type.
    
    Args:
        selector_type: Type of selector to create
        config: Configuration dictionary
        
    Returns:
        Trade selector instance
    """
    if config is None:
        config = {}
    
    if selector_type == 'probability':
        return ProbabilityTradeSelector(config)
    elif selector_type == 'expected_value':
        return ExpectedValueTradeSelector(config)
    elif selector_type == 'regime_aware':
        return RegimeAwareTradeSelector(config)
    else:
        raise ValueError(f"Unknown trade selector type: {selector_type}")
