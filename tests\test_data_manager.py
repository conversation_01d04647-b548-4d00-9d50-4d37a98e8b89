import unittest
import sys
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import tempfile
import shutil
from unittest.mock import patch, MagicMock

# Add the parent directory to the sys.path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from backtesting.data_manager import (
    DataManager, DataFrequency, DataQualityLevel, DataQualityReport, DataSummary
)


class TestDataFrequency(unittest.TestCase):
    """Test cases for DataFrequency enum."""

    def test_data_frequency_values(self):
        """Test DataFrequency enum values."""
        self.assertEqual(DataFrequency.MINUTE.value, "1min")
        self.assertEqual(DataFrequency.DAILY.value, "1d")
        self.assertEqual(DataFrequency.WEEKLY.value, "1w")


class TestDataQualityReport(unittest.TestCase):
    """Test cases for DataQualityReport dataclass."""

    def test_data_quality_report_creation(self):
        """Test DataQualityReport creation."""
        report = DataQualityReport(
            symbol="AAPL",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 1, 31),
            total_points=100,
            missing_points=5,
            duplicate_points=2,
            out_of_order_points=1,
            zero_volume_points=3,
            extreme_price_moves=1,
            completeness_ratio=0.95,
            quality_level=DataQualityLevel.GOOD,
            issues=["Minor data gaps"],
            recommendations=["Fill missing data"]
        )
        
        self.assertEqual(report.symbol, "AAPL")
        self.assertEqual(report.total_points, 100)
        self.assertEqual(report.quality_level, DataQualityLevel.GOOD)
        self.assertEqual(len(report.issues), 1)


class TestDataSummary(unittest.TestCase):
    """Test cases for DataSummary dataclass."""

    def test_data_summary_creation(self):
        """Test DataSummary creation."""
        summary = DataSummary(
            symbol="AAPL",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 1, 31),
            frequency=DataFrequency.DAILY,
            total_records=31,
            avg_volume=1000000.0,
            avg_price=150.0,
            volatility=0.02,
            max_price=160.0,
            min_price=140.0,
            price_range=20.0
        )
        
        self.assertEqual(summary.symbol, "AAPL")
        self.assertEqual(summary.total_records, 31)
        self.assertEqual(summary.avg_price, 150.0)
        self.assertEqual(summary.volatility, 0.02)


class TestDataManager(unittest.TestCase):
    """Test cases for DataManager class."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.data_manager = DataManager(data_path=self.temp_dir, cache_enabled=True)

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """Test DataManager initialization."""
        self.assertEqual(str(self.data_manager.data_path), self.temp_dir)
        self.assertTrue(self.data_manager.cache_enabled)
        self.assertEqual(len(self.data_manager._cache), 0)
        self.assertEqual(self.data_manager.max_price_change_ratio, 0.5)

    def test_initialization_default_path(self):
        """Test DataManager initialization with default path."""
        dm = DataManager()
        self.assertEqual(dm.data_path, Path("data"))

    def create_sample_data(self, symbol: str = "AAPL", days: int = 10) -> pd.DataFrame:
        """Helper method to create sample data."""
        dates = pd.date_range(start="2023-01-01", periods=days, freq="1D")
        base_price = 150.0
        
        data = []
        for i, date in enumerate(dates):
            price = base_price + np.random.normal(0, 2)
            data.append({
                'timestamp': date,
                'symbol': symbol,
                'open': price + np.random.normal(0, 0.5),
                'high': price + abs(np.random.normal(0, 1)),
                'low': price - abs(np.random.normal(0, 1)),
                'close': price,
                'volume': 10000 + np.random.randint(-2000, 2000)
            })
        
        return pd.DataFrame(data)

    def test_generate_synthetic_data(self):
        """Test synthetic data generation."""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        data = self.data_manager._generate_synthetic_data(
            "AAPL", start_date, end_date, DataFrequency.DAILY
        )
        
        self.assertFalse(data.empty)
        self.assertGreater(len(data), 5)  # Should have multiple days
        
        # Check required columns
        required_cols = ['timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            self.assertIn(col, data.columns)
        
        # Check OHLC relationships
        self.assertTrue((data['high'] >= data['low']).all())
        self.assertTrue((data['high'] >= data['open']).all())
        self.assertTrue((data['high'] >= data['close']).all())
        self.assertTrue((data['low'] <= data['open']).all())
        self.assertTrue((data['low'] <= data['close']).all())

    def test_validate_ohlc_relationships(self):
        """Test OHLC relationship validation."""
        # Create data with invalid OHLC relationships
        data = pd.DataFrame({
            'timestamp': [datetime(2023, 1, 1)],
            'open': [150.0],
            'high': [149.0],  # Invalid: high < open
            'low': [151.0],   # Invalid: low > open
            'close': [150.5],
            'volume': [10000]
        })
        
        validated_data = self.data_manager._validate_ohlc_relationships(data)
        
        # Should fix the relationships
        self.assertGreaterEqual(validated_data['high'].iloc[0], validated_data['open'].iloc[0])
        self.assertGreaterEqual(validated_data['high'].iloc[0], validated_data['close'].iloc[0])
        self.assertLessEqual(validated_data['low'].iloc[0], validated_data['open'].iloc[0])
        self.assertLessEqual(validated_data['low'].iloc[0], validated_data['close'].iloc[0])

    def test_remove_extreme_movements(self):
        """Test removal of extreme price movements."""
        # Create data with extreme movement
        data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=5, freq='1D'),
            'close': [100.0, 101.0, 200.0, 102.0, 103.0],  # 200.0 is extreme
            'symbol': ['AAPL'] * 5,
            'volume': [10000] * 5
        })
        
        cleaned_data = self.data_manager._remove_extreme_movements(data, "AAPL")
        
        # Should remove the extreme point
        self.assertLess(len(cleaned_data), len(data))
        self.assertNotIn(200.0, cleaned_data['close'].values)

    def test_fill_missing_values(self):
        """Test missing value filling."""
        data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=5, freq='1D'),
            'open': [100.0, np.nan, 102.0, np.nan, 104.0],
            'close': [100.0, np.nan, 102.0, np.nan, 104.0],
            'volume': [10000, np.nan, 12000, np.nan, 14000]
        })
        
        filled_data = self.data_manager._fill_missing_values(data)
        
        # Check that price NaNs are forward filled
        self.assertFalse(filled_data['open'].isna().any())
        self.assertFalse(filled_data['close'].isna().any())
        
        # Check that volume NaNs are filled with 0
        self.assertEqual(filled_data['volume'].isna().sum(), 0)

    def test_filter_by_date_range(self):
        """Test date range filtering."""
        data = self.create_sample_data(days=10)
        start_date = datetime(2023, 1, 3)
        end_date = datetime(2023, 1, 7)
        
        filtered_data = self.data_manager._filter_by_date_range(data, start_date, end_date)
        
        self.assertLessEqual(len(filtered_data), len(data))
        self.assertTrue((filtered_data['timestamp'] >= start_date).all())
        self.assertTrue((filtered_data['timestamp'] <= end_date).all())

    def test_validate_and_clean_data(self):
        """Test complete data validation and cleaning."""
        # Create data with various issues
        data = pd.DataFrame({
            'timestamp': [datetime(2023, 1, 1), datetime(2023, 1, 2), datetime(2023, 1, 1), datetime(2023, 1, 3)],
            'open': [100.0, 101.0, 100.0, 102.0],
            'high': [101.0, 102.0, 101.0, 103.0],
            'low': [99.0, 100.0, 99.0, 101.0],
            'close': [100.5, 101.5, 100.5, 102.5],
            'volume': [10000, 11000, 10000, 12000]
        })
        
        cleaned_data = self.data_manager._validate_and_clean_data(data, "TEST")
        
        # Should remove duplicate timestamp
        self.assertEqual(len(cleaned_data), 3)
        self.assertIn('symbol', cleaned_data.columns)
        self.assertTrue((cleaned_data['symbol'] == "TEST").all())

    def test_load_data_synthetic(self):
        """Test data loading with synthetic data generation."""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        # Should generate synthetic data since no file exists
        data = self.data_manager.load_data("AAPL", start_date, end_date, DataFrequency.DAILY)
        
        self.assertFalse(data.empty)
        self.assertIn('timestamp', data.columns)
        self.assertIn('close', data.columns)

    def test_load_data_caching(self):
        """Test data caching functionality."""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 5)
        
        # First load
        data1 = self.data_manager.load_data("AAPL", start_date, end_date, DataFrequency.DAILY)
        
        # Second load should come from cache
        data2 = self.data_manager.load_data("AAPL", start_date, end_date, DataFrequency.DAILY)
        
        pd.testing.assert_frame_equal(data1, data2)
        self.assertEqual(len(self.data_manager._cache), 1)

    def test_clear_cache(self):
        """Test cache clearing."""
        # Load some data to populate cache
        self.data_manager.load_data("AAPL", datetime(2023, 1, 1), datetime(2023, 1, 5))
        self.assertGreater(len(self.data_manager._cache), 0)
        
        # Clear cache
        self.data_manager.clear_cache()
        self.assertEqual(len(self.data_manager._cache), 0)

    def test_load_multiple_symbols(self):
        """Test loading data for multiple symbols."""
        symbols = ["AAPL", "GOOGL", "MSFT"]
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        data_dict = self.data_manager.load_multiple_symbols(symbols, start_date, end_date)
        
        self.assertEqual(len(data_dict), len(symbols))
        for symbol in symbols:
            self.assertIn(symbol, data_dict)
            self.assertFalse(data_dict[symbol].empty)

    def test_align_data_empty(self):
        """Test data alignment with empty input."""
        aligned_data = self.data_manager.align_data({})
        self.assertTrue(aligned_data.empty)

    def test_align_data_single_symbol(self):
        """Test data alignment with single symbol."""
        data = self.create_sample_data("AAPL", 5)
        data_dict = {"AAPL": data}
        
        aligned_data = self.data_manager.align_data(data_dict)
        
        # Should return copy of original data
        self.assertEqual(len(aligned_data), len(data))
        pd.testing.assert_frame_equal(aligned_data, data)

    def test_align_data_multiple_symbols(self):
        """Test data alignment with multiple symbols."""
        aapl_data = self.create_sample_data("AAPL", 5)
        googl_data = self.create_sample_data("GOOGL", 5)
        
        data_dict = {"AAPL": aapl_data, "GOOGL": googl_data}
        aligned_data = self.data_manager.align_data(data_dict)
        
        self.assertFalse(aligned_data.empty)
        self.assertIn('timestamp', aligned_data.columns)
        # Should have columns prefixed with symbol names
        aapl_columns = [col for col in aligned_data.columns if col.startswith('AAPL_')]
        googl_columns = [col for col in aligned_data.columns if col.startswith('GOOGL_')]
        self.assertGreater(len(aapl_columns), 0)
        self.assertGreater(len(googl_columns), 0)

    def test_assess_data_quality_empty(self):
        """Test data quality assessment with empty data."""
        empty_data = pd.DataFrame()
        report = self.data_manager.assess_data_quality(empty_data, "AAPL")
        
        self.assertEqual(report.symbol, "AAPL")
        self.assertEqual(report.total_points, 0)
        self.assertEqual(report.quality_level, DataQualityLevel.UNACCEPTABLE)
        self.assertIn("No data available", report.issues)

    def test_assess_data_quality_good(self):
        """Test data quality assessment with good data."""
        data = self.create_sample_data("AAPL", 20)
        report = self.data_manager.assess_data_quality(data, "AAPL")
        
        self.assertEqual(report.symbol, "AAPL")
        self.assertEqual(report.total_points, 20)
        self.assertGreaterEqual(report.completeness_ratio, 0.9)
        # Should be good quality with clean synthetic data
        self.assertIn(report.quality_level, [DataQualityLevel.EXCELLENT, DataQualityLevel.GOOD])

    def test_assess_data_quality_with_issues(self):
        """Test data quality assessment with problematic data."""
        # Create data with issues
        data = pd.DataFrame({
            'timestamp': pd.date_range('2023-01-01', periods=10, freq='1D'),
            'close': [100.0, np.nan, 102.0, np.nan, 104.0, 105.0, np.nan, 107.0, 108.0, np.nan],
            'volume': [0, 10000, 0, 12000, 0, 14000, 15000, 0, 17000, 18000],
            'symbol': ['AAPL'] * 10
        })
        
        report = self.data_manager.assess_data_quality(data, "AAPL")
        
        self.assertGreater(report.missing_points, 0)
        self.assertGreater(report.zero_volume_points, 0)
        self.assertGreater(len(report.issues), 0)

    def test_get_data_summary_empty(self):
        """Test data summary with empty data."""
        empty_data = pd.DataFrame()
        summary = self.data_manager.get_data_summary(empty_data, "AAPL", DataFrequency.DAILY)
        
        self.assertEqual(summary.symbol, "AAPL")
        self.assertEqual(summary.total_records, 0)
        self.assertEqual(summary.avg_price, 0.0)
        self.assertEqual(summary.volatility, 0.0)

    def test_get_data_summary_with_data(self):
        """Test data summary with actual data."""
        data = self.create_sample_data("AAPL", 10)
        summary = self.data_manager.get_data_summary(data, "AAPL", DataFrequency.DAILY)
        
        self.assertEqual(summary.symbol, "AAPL")
        self.assertEqual(summary.total_records, 10)
        self.assertEqual(summary.frequency, DataFrequency.DAILY)
        self.assertGreater(summary.avg_price, 0)
        self.assertGreater(summary.max_price, summary.min_price)
        self.assertGreaterEqual(summary.volatility, 0)

    def test_save_data_csv(self):
        """Test saving data to CSV."""
        data = self.create_sample_data("TEST", 5)
        
        self.data_manager.save_data(data, "TEST", DataFrequency.DAILY, "csv")
        
        # Check that file was created
        expected_file = Path(self.temp_dir) / "TEST_1d.csv"
        self.assertTrue(expected_file.exists())
        
        # Load and verify content
        loaded_data = pd.read_csv(expected_file)
        self.assertEqual(len(loaded_data), 5)

    def test_save_data_parquet(self):
        """Test saving data to Parquet."""
        data = self.create_sample_data("TEST", 5)
        
        try:
            self.data_manager.save_data(data, "TEST", DataFrequency.DAILY, "parquet")
            
            # Check that file was created
            expected_file = Path(self.temp_dir) / "TEST_1d.parquet"
            self.assertTrue(expected_file.exists())
        except ImportError:
            # Skip if pyarrow not available
            self.skipTest("Parquet support not available")

    def test_save_data_empty(self):
        """Test saving empty data."""
        empty_data = pd.DataFrame()
        
        # Should handle empty data gracefully
        self.data_manager.save_data(empty_data, "TEST", DataFrequency.DAILY, "csv")
        
        # Should not create file
        expected_file = Path(self.temp_dir) / "TEST_1d.csv"
        self.assertFalse(expected_file.exists())

    def test_save_data_invalid_format(self):
        """Test saving data with invalid format."""
        data = self.create_sample_data("TEST", 5)
        
        with self.assertRaises(ValueError):
            self.data_manager.save_data(data, "TEST", DataFrequency.DAILY, "invalid")

    @patch('backtesting.data_manager.logger')
    def test_load_data_with_errors(self, mock_logger):
        """Test data loading error handling."""
        # Test with invalid source
        with patch.object(self.data_manager, '_load_csv_data', side_effect=Exception("Test error")):
            data = self.data_manager.load_data("AAPL", datetime(2023, 1, 1), datetime(2023, 1, 5))
            
            # Should return empty DataFrame on error
            self.assertTrue(data.empty)

    def test_load_csv_data_missing_file(self):
        """Test CSV loading with missing file."""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 5)
        
        # Should generate synthetic data when file doesn't exist
        data = self.data_manager._load_csv_data("MISSING", start_date, end_date, DataFrequency.DAILY)
        
        self.assertFalse(data.empty)
        self.assertIn('timestamp', data.columns)

    def test_load_parquet_data_missing_file(self):
        """Test Parquet loading with missing file."""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 5)
        
        # Should generate synthetic data when file doesn't exist
        data = self.data_manager._load_parquet_data("MISSING", start_date, end_date, DataFrequency.DAILY)
        
        self.assertFalse(data.empty)
        self.assertIn('timestamp', data.columns)


class TestDataManagerIntegration(unittest.TestCase):
    """Integration tests for DataManager."""

    def setUp(self):
        """Set up integration test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.data_manager = DataManager(data_path=self.temp_dir)

    def tearDown(self):
        """Clean up integration test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_complete_workflow(self):
        """Test complete data management workflow."""
        # 1. Load data (synthetic)
        symbols = ["AAPL", "GOOGL"]
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        data_dict = self.data_manager.load_multiple_symbols(symbols, start_date, end_date)
        self.assertEqual(len(data_dict), 2)
        
        # 2. Assess quality
        for symbol, data in data_dict.items():
            quality_report = self.data_manager.assess_data_quality(data, symbol)
            self.assertIsInstance(quality_report, DataQualityReport)
            self.assertEqual(quality_report.symbol, symbol)
        
        # 3. Get summaries
        for symbol, data in data_dict.items():
            summary = self.data_manager.get_data_summary(data, symbol, DataFrequency.DAILY)
            self.assertIsInstance(summary, DataSummary)
            self.assertEqual(summary.symbol, symbol)
            self.assertGreater(summary.total_records, 0)
        
        # 4. Align data
        aligned_data = self.data_manager.align_data(data_dict)
        self.assertFalse(aligned_data.empty)
        
        # 5. Save data
        for symbol, data in data_dict.items():
            self.data_manager.save_data(data, symbol, DataFrequency.DAILY, "csv")
            
            # Verify file exists
            expected_file = Path(self.temp_dir) / f"{symbol}_1d.csv"
            self.assertTrue(expected_file.exists())


if __name__ == '__main__':
    unittest.main()