task_id: C-04
name: Output Coordinator Agent
version: 1.0.0
description: Merge math-validation, signal, and chart payloads into a single
             trading analysis packet plus risk and execution guide.
inputs:
  - signal_result          # JSON {direction, confidence, }
  - chart_data_paths       # list[str] PNGs
  - mathematical_validation  # JSON {precision, checks}
outputs:
  files:
    - path: outputs/{{date}}/{{ticker}}/unified_analysis.json
      json_schema: schemas/unified_analysis_v1.json
    - path: outputs/{{date}}/{{ticker}}/execution_plan.md
      must_exist: true
    - path: outputs/{{date}}/{{ticker}}/risk_metrics.csv
      must_exist: true
success_criteria:
  perf_budget:
    max_runtime_ms: 3000
  # Long-options trades must clear a minimum ROI hurdle
  roi_floor:
    min_expected_roi: 1.75          #  175% matches your "2 premium" rule
  code_coverage_min: 0.95
  output_completeness: 1.00          # all keys present
  dependency_constraints:
    max_additional_deps: 2          # Block heavy libs without review
    forbidden_packages:
      - tensorflow
      - torch
      - opencv-python
      - jupyter
    allowed_heavy_packages:          # Exceptions requiring approval
      - scikit-learn                 # Already approved for ML
      - scipy                        # Mathematical operations
