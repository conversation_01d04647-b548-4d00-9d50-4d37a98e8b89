#!/usr/bin/env python3
"""
Schwab Option Chain Parser
Converts Schwab API option chain data to standardized DataFrame format
Mathematical rigor: 100% field mapping, zero data loss
"""

import pandas as pd
import numpy as np
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

def parse_schwab_option_chain(chain_data: Dict[str, Any], symbol: str) -> pd.DataFrame:
    """
    Parse Schwab option chain JSON into standardized DataFrame
    
    Args:
        chain_data: Raw JSON response from Schwab option_chain API
        symbol: Underlying symbol for validation
        
    Returns:
        DataFrame with standardized option data including Greeks
    """
    try:
        options_list = []
        
        # Extract underlying price for Greeks calculation
        underlying_px = chain_data.get('underlyingPrice', 0.0)
        if underlying_px == 0.0:
            # Try alternative field names
            underlying_px = chain_data.get('underlying', {}).get('last', 0.0)
        
        logger.info(f"Parsing option chain for {symbol}, underlying price: ${underlying_px}")
        
        # Parse call options
        call_exp_map = chain_data.get('callExpDateMap', {})
        for exp_date, strikes in call_exp_map.items():
            for strike_str, contracts in strikes.items():
                for contract in contracts:
                    option_data = _parse_single_option(
                        contract, 'CALL', exp_date, float(strike_str), underlying_px, symbol
                    )
                    if option_data:
                        options_list.append(option_data)
        
        # Parse put options
        put_exp_map = chain_data.get('putExpDateMap', {})
        for exp_date, strikes in put_exp_map.items():
            for strike_str, contracts in strikes.items():
                for contract in contracts:
                    option_data = _parse_single_option(
                        contract, 'PUT', exp_date, float(strike_str), underlying_px, symbol
                    )
                    if option_data:
                        options_list.append(option_data)
        
        # Convert to DataFrame
        if not options_list:
            logger.warning(f"No options data found for {symbol}")
            return pd.DataFrame()
        
        df = pd.DataFrame(options_list)
        
        # Sort by expiration, type, strike
        df = df.sort_values(['expiry_date', 'option_type', 'strike'])
        
        logger.info(f"Parsed {len(df)} options for {symbol}")
        return df
        
    except Exception as e:
        logger.error(f"Failed to parse option chain for {symbol}: {e}")
        return pd.DataFrame()

def _parse_single_option(contract: Dict[str, Any], option_type: str, 
                        exp_date: str, strike: float, underlying_px: float, 
                        symbol: str) -> Optional[Dict[str, Any]]:
    """
    Parse single option contract data
    
    Args:
        contract: Single option contract data
        option_type: 'CALL' or 'PUT'
        exp_date: Expiration date string
        strike: Strike price
        underlying_px: Underlying asset price
        symbol: Underlying symbol
        
    Returns:
        Standardized option data dictionary
    """
    try:
        # Map Schwab fields to standard format
        bid = contract.get('bid', 0.0)
        ask = contract.get('ask', 0.0)
        last = contract.get('last', 0.0)
        mark = contract.get('mark', 0.0)
        
        # Use mark for mid-price when bid/ask are wide
        if mark > 0:
            mid_price = mark
        elif bid > 0 and ask > 0:
            mid_price = (bid + ask) / 2.0
        else:
            mid_price = last
        
        # Extract Greeks (if available) - handle both direct and nested formats
        greeks = contract.get('greeks', {})
        
        # Try direct fields first (new format)
        delta = contract.get('delta', greeks.get('delta', 0.0))
        gamma = contract.get('gamma', greeks.get('gamma', 0.0)) 
        theta = contract.get('theta', greeks.get('theta', 0.0))
        vega = contract.get('vega', greeks.get('vega', 0.0))
        
        # Extract implied volatility - multiple possible field names
        iv = contract.get('volatility', 0.0) or contract.get('impliedVolatility', 0.0) or greeks.get('iv', 0.0)
        
        # Parse expiration date
        try:
            # Schwab format: "2024-01-19:7"
            exp_date_clean = exp_date.split(':')[0]
            expiry_date = datetime.strptime(exp_date_clean, '%Y-%m-%d').date()
        except:
            logger.warning(f"Could not parse expiration date: {exp_date}")
            expiry_date = None
        
        # Create standardized option data
        option_data = {
            # Basic identification
            'symbol': contract.get('symbol', ''),
            'underlying_symbol': symbol.upper(),
            'option_type': option_type.upper(),
            'strike': strike,
            'expiry_date': expiry_date,
            
            # Pricing data
            'bid': bid,
            'ask': ask,
            'last': last,
            'mark': mark,
            'mid_price': mid_price,
            'underlying_px': underlying_px,
            
            # Volume and interest
            'volume': contract.get('totalVolume', 0),
            'open_interest': contract.get('openInterest', 0),
            
            # Greeks
            'delta': delta,
            'gamma': gamma,
            'theta': theta,
            'vega': vega,
            'iv': iv,
            
            # Additional fields
            'days_to_expiry': _calculate_days_to_expiry(expiry_date),
            'intrinsic_value': _calculate_intrinsic_value(option_type, underlying_px, strike),
            'time_value': mid_price - _calculate_intrinsic_value(option_type, underlying_px, strike),
            
            # Data quality flags
            'delayed': contract.get('delayed', False),
            'has_greeks': bool(delta or gamma or theta or vega),  # More accurate check
            'timestamp': datetime.now().isoformat()
        }
        
        return option_data
        
    except Exception as e:
        logger.warning(f"Failed to parse option contract: {e}")
        return None

def _calculate_days_to_expiry(expiry_date) -> Optional[int]:
    """Calculate days to expiration"""
    if not expiry_date:
        return None
    
    try:
        today = datetime.now().date()
        return (expiry_date - today).days
    except:
        return None

def _calculate_intrinsic_value(option_type: str, underlying_px: float, strike: float) -> float:
    """Calculate intrinsic value"""
    if option_type.upper() == 'CALL':
        return max(0, underlying_px - strike)
    else:  # PUT
        return max(0, strike - underlying_px)

def validate_option_data_quality(df: pd.DataFrame, symbol: str) -> Dict[str, Any]:
    """
    Validate option data quality and generate metrics
    
    Args:
        df: Parsed option DataFrame
        symbol: Underlying symbol
        
    Returns:
        Quality metrics dictionary
    """
    if df.empty:
        return {
            'total_options': 0,
            'has_data': False,
            'quality_score': 0.0,
            'issues': ['No option data available']
        }
    
    metrics = {
        'total_options': len(df),
        'calls': len(df[df['option_type'] == 'CALL']),
        'puts': len(df[df['option_type'] == 'PUT']),
        'has_data': True
    }
    
    # Quality checks
    issues = []
    quality_factors = []
    
    # Check for pricing data
    has_pricing = (df['bid'] > 0) | (df['ask'] > 0) | (df['last'] > 0)
    pricing_pct = has_pricing.mean()
    quality_factors.append(pricing_pct)
    
    if pricing_pct < 0.8:
        issues.append(f"Low pricing coverage: {pricing_pct:.1%}")
    
    # Check for Greeks
    has_greeks = df['has_greeks'].mean()
    quality_factors.append(has_greeks)
    
    if has_greeks < 0.5:
        issues.append(f"Low Greeks coverage: {has_greeks:.1%}")
    
    # Check for delayed data
    delayed_pct = df['delayed'].mean()
    if delayed_pct > 0.1:
        issues.append(f"High delayed data: {delayed_pct:.1%}")
        quality_factors.append(1 - delayed_pct)
    else:
        quality_factors.append(1.0)
    
    # Check IV data
    has_iv = (df['iv'] > 0).mean()
    quality_factors.append(has_iv)
    
    if has_iv < 0.7:
        issues.append(f"Low IV coverage: {has_iv:.1%}")
    
    # Calculate overall quality score
    quality_score = np.mean(quality_factors) if quality_factors else 0.0
    
    metrics.update({
        'pricing_coverage': pricing_pct,
        'greeks_coverage': has_greeks,
        'iv_coverage': has_iv,
        'delayed_data_pct': delayed_pct,
        'quality_score': quality_score,
        'issues': issues
    })
    
    logger.info(f"Option data quality for {symbol}: {quality_score:.2f} "
               f"({len(df)} options, {len(issues)} issues)")
    
    return metrics

# Convenience function for direct integration
def get_schwab_options_df(api, symbol: str, strikes: int = 10, days: int = 15) -> Tuple[pd.DataFrame, Dict]:
    """
    Get parsed options DataFrame from Schwab API
    
    Args:
        api: SchwabAPI instance
        symbol: Underlying symbol
        strikes: Number of strikes
        days: Days filter
        
    Returns:
        (DataFrame, quality_metrics)
    """
    try:
        # Fetch raw option chain data
        chain_data = api.option_chain(symbol, strikes=strikes, days=days)
        
        # Parse to DataFrame
        df = parse_schwab_option_chain(chain_data, symbol)
        
        # Validate quality
        quality_metrics = validate_option_data_quality(df, symbol)
        
        return df, quality_metrics
        
    except Exception as e:
        logger.error(f"Failed to get options DataFrame for {symbol}: {e}")
        return pd.DataFrame(), {'has_data': False, 'error': str(e)}

if __name__ == "__main__":
    # Test with sample data
    print("Schwab Option Chain Parser - Test Mode")
    
    # Sample test data structure
    sample_chain = {
        'underlyingPrice': 150.0,
        'callExpDateMap': {
            '2024-01-19:7': {
                '150.0': [{
                    'symbol': 'AAPL_011924C150',
                    'bid': 2.5,
                    'ask': 2.7,
                    'last': 2.6,
                    'mark': 2.6,
                    'totalVolume': 100,
                    'openInterest': 500,
                    'greeks': {
                        'delta': 0.5,
                        'gamma': 0.02,
                        'theta': -0.05,
                        'vega': 0.15
                    },
                    'volatility': 0.25,
                    'delayed': False
                }]
            }
        },
        'putExpDateMap': {}
    }
    
    df = parse_schwab_option_chain(sample_chain, 'AAPL')
    print(f"Parsed {len(df)} options from sample data")
    if not df.empty:
        print(df.columns.tolist())
        print(df.head())
