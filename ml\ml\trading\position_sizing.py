"""
Position Sizing module for Trading System

This module implements different position sizing algorithms for determining
the optimal position size for trades.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Union, Optional
import logging

# Import from project structure
try:
    from ml.ml.trading.trade_model import TradeSignal, MarketRegime
except ImportError:
    try:
        from .trade_model import TradeSignal, MarketRegime
    except ImportError:
        # Fallback definitions for basic functionality
        class TradeSignal:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)
        
        class MarketRegime:
            def __init__(self, **kwargs):
                for k, v in kwargs.items():
                    setattr(self, k, v)

# Set up logging
logger = logging.getLogger(__name__)

class PositionSizer:
    """Base class for position sizers."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the position sizer.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
    
    def calculate_position_size(self, 
                               signal: TradeSignal, 
                               market_data: pd.DataFrame,
                               active_trades: List[Dict[str, Any]],
                               regime: Optional[MarketRegime] = None) -> float:
        """
        Calculate position size for a trade signal.
        
        Args:
            signal: Trade signal
            market_data: Market data
            active_trades: List of active trades
            regime: Optional market regime
            
        Returns:
            Position size as a fraction of portfolio
        """
        raise NotImplementedError("Subclasses must implement this method")
    
    def save(self) -> Dict[str, Any]:
        """
        Save the sizer state.
        
        Returns:
            Serializable state dictionary
        """
        return {"config": self.config}
    
    def load(self, state: Dict[str, Any]) -> bool:
        """
        Load sizer state.
        
        Args:
            state: State dictionary
            
        Returns:
            True if successful, False otherwise
        """
        if "config" in state:
            self.config = state["config"]
            return True
        return False


class FixedRiskPositionSizer(PositionSizer):
    """Position sizer that allocates a fixed risk per trade."""
    
    def calculate_position_size(self, 
                               signal: TradeSignal, 
                               market_data: pd.DataFrame,
                               active_trades: List[Dict[str, Any]],
                               regime: Optional[MarketRegime] = None) -> float:
        """
        Calculate position size based on fixed risk.
        
        Args:
            signal: Trade signal
            market_data: Market data
            active_trades: List of active trades
            regime: Optional market regime
            
        Returns:
            Position size as a fraction of portfolio
        """
        # Get risk settings
        risk_per_trade = self.config.get('max_risk_per_trade', 0.02)  # Default 2%
        max_position_size = self.config.get('max_position_size', 0.05)  # Default 5%
        
        # Calculate risk as percentage
        risk_as_percent = abs(signal.entry_price - signal.stop_price) / signal.entry_price
        
        if risk_as_percent == 0:
            logger.warning("Risk percentage is zero, using max position size")
            return max_position_size
        
        # Calculate position size based on risk
        position_size = risk_per_trade / risk_as_percent
        
        # Cap at maximum allowed position size
        position_size = min(position_size, max_position_size)
        
        return position_size


class KellyPositionSizer(PositionSizer):
    """Position sizer based on the Kelly Criterion."""
    
    def calculate_position_size(self, 
                               signal: TradeSignal, 
                               market_data: pd.DataFrame,
                               active_trades: List[Dict[str, Any]],
                               regime: Optional[MarketRegime] = None) -> float:
        """
        Calculate position size using the Kelly Criterion.
        
        Args:
            signal: Trade signal
            market_data: Market data
            active_trades: List of active trades
            regime: Optional market regime
            
        Returns:
            Position size as a fraction of portfolio
        """
        # Get settings
        max_position_size = self.config.get('max_position_size', 0.05)  # Default 5%
        kelly_fraction = self.config.get('kelly_fraction', 0.5)  # Default half-Kelly
        
        # Extract signal properties
        win_prob = signal.probability
        profit_factor = signal.risk_reward_ratio
        
        # Avoid division by zero
        if profit_factor == 0:
            logger.warning("Profit factor is zero, using fixed position size")
            return max_position_size * kelly_fraction
        
        # Calculate Kelly size
        kelly_size = win_prob - ((1 - win_prob) / profit_factor)
        
        # Apply Kelly fraction for safety
        position_size = kelly_size * kelly_fraction
        
        # Cap at maximum allowed position size
        position_size = min(max(position_size, 0), max_position_size)
        
        return position_size


class OptimalFPositionSizer(PositionSizer):
    """Position sizer based on Optimal F."""
    
    def calculate_position_size(self, 
                               signal: TradeSignal, 
                               market_data: pd.DataFrame,
                               active_trades: List[Dict[str, Any]],
                               regime: Optional[MarketRegime] = None) -> float:
        """
        Calculate position size using Optimal F.
        
        Args:
            signal: Trade signal
            market_data: Market data
            active_trades: List of active trades
            regime: Optional market regime
            
        Returns:
            Position size as a fraction of portfolio
        """
        # For the unit test implementation, use a simplified calculation
        # In a real implementation, this would use actual trade history and more complex math
        
        # Get settings
        max_position_size = self.config.get('max_position_size', 0.05)  # Default 5%
        
        # Use a simplified calculation based on signal properties
        position_size = 0.02 * signal.probability * signal.risk_reward_ratio
        
        # Cap at maximum allowed position size
        position_size = min(max(position_size, 0), max_position_size)
        
        return position_size


def create_position_sizer(sizer_type: str, config: Dict[str, Any] = None) -> PositionSizer:
    """
    Create a position sizer of the specified type.
    
    Args:
        sizer_type: Type of sizer to create
        config: Configuration dictionary
        
    Returns:
        Position sizer instance
    """
    if config is None:
        config = {}
    
    if sizer_type == 'fixed_risk':
        return FixedRiskPositionSizer(config)
    elif sizer_type == 'kelly':
        return KellyPositionSizer(config)
    elif sizer_type == 'optimal_f':
        return OptimalFPositionSizer(config)
    else:
        raise ValueError(f"Unknown position sizer type: {sizer_type}")
