task_id: F-01
name: Enhanced CSID Flow-Physics Analyzer
version: 1.0.0
description: |
  Advanced institutional flow detection with stealth accumulation/distribution patterns.
  Implements CSID (Custom Sentiment Indicator) with mathematical rigor and statistical validation.
  
mathematical_foundation:
  - Smart Money Index calculation with momentum analysis
  - Institutional vs retail bias detection (Parkinson volatility estimator)
  - Volume flow regime classification using statistical thresholds
  - Time-decay weighted institutional pattern recognition

inputs:
  required:
    - price_df_parquet  # per-ticker minute/5-min bars with OHLCV
    - ticker            # symbol for analysis
  optional:
    - lookback_periods  # default: 100 periods
    - quality_threshold # default: 0.6

outputs:
  files:
    - path: flow_phys/{{date}}/{{ticker}}_csid.json
      must_exist: true
      schema:
        institutional_bias: float  # 0.0-1.0 scale
        smart_money_index: float   # momentum strength
        flow_regime: string        # stealth_accumulation|distribution|mixed
        data_quality_score: float  # >0.6 required
        csid_signals: array        # trading signals with confidence
  
validation_requirements:
  mathematical_precision: 1e-10
  statistical_significance: 0.95
  bounds_checking: true
  error_propagation_max: 1e-8

success_criteria:
  performance:
    max_runtime_ms: 80
    memory_limit_mb: 50
  quality:
    data_quality_min: 0.6
    institutional_detection_accuracy: 0.8
    signal_confidence_min: 0.7
  testing:
    code_coverage_min: 0.95
    unit_tests_pass: true
    integration_tests_pass: true

dependencies:
  - data_acquisition  # Phase 1
  - mathematical_validation  # Phase 2

training_data_capture:
  decision_points:
    - regime_classification_logic
    - threshold_application_decisions
    - institutional_pattern_recognition
  performance_metrics:
    - execution_efficiency
    - precision_maintenance
    - error_recovery_patterns

agent_zero_integration:
  learning_objectives:
    - Institutional vs retail pattern recognition
    - Stealth accumulation detection timing
    - Distribution phase identification
    - Signal confidence optimization
  training_features:
    - Volume flow characteristics
    - Price momentum patterns
    - Volatility regime indicators
    - Time-decay weighted signals