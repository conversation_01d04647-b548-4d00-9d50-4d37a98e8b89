{"ticker": "AAPL", "pipeline_components": ["DATA_INIT", "B-Series", "A-01", "C-02", "F-02", "SPECIALIZED_ARMY"], "timestamp": "2025-06-29T11:33:12.230255", "intelligence_level": "MAXIMUM_WITH_SPECIALIZED_AGENTS", "data_initialization": {"status": "SUCCESS", "source": "schwab_mcp_localhost_8005", "bars_records": 1, "options_records": 14, "single_source_compliance": true, "data_timestamp": "2025-06-29T11:33:16.448550"}, "b_series": {"status": "SUCCESS", "feature_count": 52, "output_file": "data\\features\\AAPL_features.parquet", "greek_features": ["gamma_calc_mean", "gamma_calc_sum", "gamma_calc_std", "vanna_calc_mean", "vanna_calc_sum", "vanna_calc_std", "charm_calc_mean", "charm_calc_sum", "charm_calc_std", "delta_calc_mean", "delta_calc_sum", "theta_calc_mean", "theta_calc_sum", "gamma_calc_mean_roc", "gamma_calc_mean_roc_2", "gamma_calc_sum_roc", "gamma_calc_sum_roc_2", "vanna_calc_mean_roc", "vanna_calc_mean_roc_2", "vanna_calc_sum_roc", "vanna_calc_sum_roc_2", "charm_calc_mean_roc", "charm_calc_mean_roc_2", "charm_calc_sum_roc", "charm_calc_sum_roc_2"]}, "a01_anomalies": {"ticker": "AAPL", "date": "2025-06-29", "timestamp": "2025-06-29T11:33:29.300775", "anomaly_count": 0, "anomalies": []}, "f01_csid": {"timestamp": "2025-06-29 11:33:29.346499", "symbol": "AAPL", "cumulative_volume_delta": 0.0, "cvd_velocity": 0.0, "cvd_acceleration": 0.0, "cvd_momentum": 0.0, "order_flow_imbalance": 0.0, "stealth_retail_ratio": 1.0, "imbalance_strength": 0.0, "flow_persistence": 0.0, "institutional_stealth_score": 0.0, "stealth_periods": 0, "retail_fomo_periods": 0, "institutional_bias": "error", "smart_money_index": 0.0, "smart_money_direction": "neutral", "accumulation_distribution": 0.5, "stealth_participation": 0.0, "flow_z_score": 0.0, "imbalance_significance": 0.0, "trend_consistency": 0.0, "data_quality_score": 0.0, "calculation_confidence": 0.0, "error_bounds": [0.0, 0.0], "statistical_significance": 0.0, "metadata": {"error": "Data quality validation failed", "version": "enhanced_money_flow_error"}, "ticker": "AAPL", "flow_regime": "mixed", "analysis_timestamp": "2025-06-29T11:33:29.346810"}, "csid_file": "flow_phys\\history\\AAPL_csid.json", "c02_iv_dynamics": {"error": "{'status': 'ERROR', 'error': 'Data fetch failed: PARTIAL', 'ticker': 'AAPL'}"}, "c02_regime": {"error": "{'status': 'ERROR', 'error': 'Data fetch failed: PARTIAL', 'ticker': 'AAPL'}"}, "f02_flow_physics": {"error": "flow_phys\\2025-06-29\\AAPL_flowphysics.json"}, "specialized_army": {"error": "Market data not available"}, "agent_zero_intelligence": {"error": "can't multiply sequence by non-int of type 'float'"}}