"""
ML System Integration and Optimization - Main Module

This module ties together all components of Phase 4, providing a unified
interface for the ML system integration and optimization.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import sys
import logging
import argparse
import time
from typing import Dict, List, Optional, Union, Any, Tuple

from ml_logging import get_logger, configure_logging
from ml_integration import get_ml_integration, initialize_ml_services, shutdown_ml_services
from ml_optimization import get_ml_optimizer, initialize_optimizer, shutdown_optimizer
from ml_dashboard import get_ml_dashboard, run_dashboard, shutdown_dashboard
from ml_config_manager import ConfigManager

logger = get_logger(__name__)

class MLSystem:
    """
    ML System - Main Integration and Optimization Controller
    
    This class serves as the main controller for the ML system,
    coordinating all components and providing a unified interface.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the ML system.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.ml_integration = get_ml_integration()
        self.ml_optimizer = get_ml_optimizer()
        self.ml_dashboard = get_ml_dashboard()
        
        self.system_running = False
        
        logger.info("ML System initialized")
    
    def initialize(self) -> bool:
        """
        Initialize the ML system.
        
        Returns:
            bool: True if initialization was successful
        """
        try:
            logger.info("Initializing ML System...")
            
            # Initialize ML services
            logger.info("Initializing ML integration services...")
            if not initialize_ml_services():
                logger.error("Failed to initialize ML integration services")
                return False
            
            # Initialize optimizer
            logger.info("Initializing ML optimizer...")
            if not initialize_optimizer():
                logger.error("Failed to initialize ML optimizer")
                return False
            
            self.system_running = True
            logger.info("ML System initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing ML System: {str(e)}")
            return False
    
    def run_dashboard(self, debug: bool = False, port: Optional[int] = None) -> None:
        """
        Run the ML performance dashboard.
        
        Args:
            debug: Debug mode flag
            port: Server port
        """
        if not self.system_running:
            logger.warning("ML System not initialized. Initializing now...")
            if not self.initialize():
                logger.error("Failed to initialize ML System. Dashboard will not have access to all metrics.")
        
        run_dashboard(debug=debug, port=port)
    
    def analyze_market_data(
        self, 
        data: Any, 
        model_id: str, 
        optimize: bool = True,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze market data using the specified model.
        
        Args:
            data: DataFrame containing market data
            model_id: ID of model to use for analysis
            optimize: Whether to optimize the model before analysis
            request_id: Optional request identifier for tracking
            
        Returns:
            Dict containing analysis results
        """
        if not self.system_running:
            logger.warning("ML System not initialized. Initializing now...")
            if not self.initialize():
                return {"error": "Failed to initialize ML System"}
        
        # Perform analysis
        result = self.ml_integration.analyze_market_data(
            data=data,
            model_id=model_id,
            request_id=request_id
        )
        
        # Shadow mode logging - capture ML system analysis
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            # Extract confidence and decisions from ML result
            confidence = result.get('confidence', 0.75) if isinstance(result, dict) else 0.75
            
            signal_data = {
                'confidence': confidence,
                'strength': 0.85,  # ML system high strength
                'execution_recommendation': 'analyze'
            }
            
            math_data = {
                'accuracy_score': 0.93,  # ML system high accuracy
                'precision': 0.0008
            }
            
            market_context = {
                'system': 'ml_system_main',
                'model_id': model_id or 'default',
                'request_id': request_id,
                'data_size': len(data) if hasattr(data, '__len__') else 'unknown',
                'analysis_timestamp': logger.handlers[0].baseFilename if logger.handlers else 'unknown'
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'ml_analysis', 'result': result if isinstance(result, dict) else {'status': 'completed'}},
                outcome=confidence,  # Use confidence as outcome
                market_context=market_context
            )
            logger.info("Shadow mode: ML system analysis logged")
            
        except Exception as e:
            logger.warning(f"Shadow mode logging failed: {e}")
        
        return result
    
    def get_model_metrics(self, model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance metrics for models.
        
        Args:
            model_id: Optional model ID to get metrics for a specific model
            
        Returns:
            Dict containing metrics data
        """
        if not self.system_running:
            logger.warning("ML System not initialized")
            return {"error": "ML System not initialized"}
        
        return self.ml_integration.get_model_metrics(model_id)
    
    def optimize_model(
        self, 
        model_id: str, 
        optimization_level: int = 1
    ) -> bool:
        """
        Optimize a model for improved performance.
        
        Args:
            model_id: ID of the model to optimize
            optimization_level: Optimization aggressiveness (1-3)
            
        Returns:
            bool: True if optimization was successful
        """
        if not self.system_running:
            logger.warning("ML System not initialized")
            return False
        
        try:
            # Get model from integration layer
            active_models = self.ml_integration.get_active_models()
            if model_id not in active_models:
                logger.error(f"Model {model_id} not loaded")
                return False
            
            # Get model instance
            model = self.ml_integration.active_models.get(model_id)
            if model is None:
                logger.error(f"Model {model_id} instance not found")
                return False
            
            # Optimize model
            optimized_model = self.ml_optimizer.optimize_model(
                model, 
                optimization_level=optimization_level
            )
            
            # Replace with optimized model
            self.ml_integration.active_models[model_id] = optimized_model
            
            logger.info(f"Model {model_id} optimized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error optimizing model {model_id}: {str(e)}")
            return False
    
    def enable_real_time_processing(self, model_id: str) -> bool:
        """
        Enable real-time processing for a model.
        
        Args:
            model_id: ID of the model to optimize
            
        Returns:
            bool: True if successful
        """
        if not self.system_running:
            logger.warning("ML System not initialized")
            return False
        
        try:
            # Get model from integration layer
            active_models = self.ml_integration.get_active_models()
            if model_id not in active_models:
                logger.error(f"Model {model_id} not loaded")
                return False
            
            # Get model instance
            model = self.ml_integration.active_models.get(model_id)
            if model is None:
                logger.error(f"Model {model_id} instance not found")
                return False
            
            # Optimize for real-time
            optimized_model = self.ml_optimizer.enable_real_time_processing(model)
            
            # Replace with optimized model
            self.ml_integration.active_models[model_id] = optimized_model
            
            logger.info(f"Real-time processing enabled for model {model_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error enabling real-time processing for model {model_id}: {str(e)}")
            return False
    
    def shutdown(self) -> None:
        """Safely shut down the ML system."""
        logger.info("Shutting down ML System...")
        
        # Shut down dashboard
        shutdown_dashboard()
        
        # Shut down optimizer
        shutdown_optimizer()
        
        # Shut down ML services
        shutdown_ml_services()
        
        self.system_running = False
        logger.info("ML System shut down successfully")


# Global system instance
_ml_system = None

def get_ml_system() -> MLSystem:
    """Get the global ML system instance."""
    global _ml_system
    if _ml_system is None:
        _ml_system = MLSystem()
    return _ml_system

def initialize_ml_system() -> bool:
    """Initialize the ML system."""
    system = get_ml_system()
    return system.initialize()

def shutdown_ml_system() -> None:
    """Safely shut down the ML system."""
    global _ml_system
    if _ml_system is not None:
        _ml_system.shutdown()
        _ml_system = None


def main():
    """Main entry point for the ML system."""
    parser = argparse.ArgumentParser(description="ML System Integration and Optimization")
    parser.add_argument(
        "--config", 
        type=str, 
        help="Path to configuration file"
    )
    parser.add_argument(
        "--dashboard", 
        action="store_true", 
        help="Run the performance dashboard"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8050, 
        help="Dashboard port (default: 8050)"
    )
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug mode"
    )
    parser.add_argument(
        "--optimize", 
        type=str, 
        help="Optimize a specific model (model_id)"
    )
    parser.add_argument(
        "--real-time", 
        type=str, 
        help="Enable real-time processing for a model (model_id)"
    )
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.debug else logging.INFO
    configure_logging(level=log_level)
    
    # Create system instance
    system = MLSystem(config_path=args.config)
    
    # Initialize system
    if not system.initialize():
        logger.error("Failed to initialize ML System. Exiting.")
        return 1
    
    try:
        # Process commands
        if args.optimize:
            success = system.optimize_model(args.optimize)
            if success:
                print(f"Model {args.optimize} optimized successfully")
            else:
                print(f"Failed to optimize model {args.optimize}")
        
        if args.real_time:
            success = system.enable_real_time_processing(args.real_time)
            if success:
                print(f"Real-time processing enabled for model {args.real_time}")
            else:
                print(f"Failed to enable real-time processing for model {args.real_time}")
        
        # Run dashboard if requested
        if args.dashboard:
            system.run_dashboard(debug=args.debug, port=args.port)
        
        return 0
        
    except KeyboardInterrupt:
        print("\nShutting down...")
        system.shutdown()
        return 0
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        system.shutdown()
        return 1
    finally:
        # Ensure system is shut down
        system.shutdown()


if __name__ == "__main__":
    sys.exit(main())
