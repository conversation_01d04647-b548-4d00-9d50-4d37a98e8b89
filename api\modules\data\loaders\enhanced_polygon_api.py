"""
Enhanced Polygon API Client - Complete Implementation

This module provides a production-ready Polygon API client with:
- Adaptive rate limiting (5 req/min free, 100 req/sec paid)
- Comprehensive endpoint coverage
- Intelligent caching and connection pooling
- Mathematical optimization for AI agent training
"""

import os
import time
import json
import logging
import asyncio
import hashlib
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Any, Union
from threading import Lock, RLock
from collections import defaultdict, deque
import urllib3
from urllib3.util.retry import Retry
from urllib3.poolmanager import PoolManager

try:
    from polygon import RESTClient
    HAS_POLYGON_CLIENT = True
except ImportError:
    HAS_POLYGON_CLIENT = False
    RESTClient = None

logger = logging.getLogger(__name__)


class TokenBucket:
    """Token bucket implementation for rate limiting with mathematical precision."""
    
    def __init__(self, capacity: int, refill_rate: float):
        """
        Initialize token bucket.
        
        Args:
            capacity: Maximum tokens in bucket
            refill_rate: Tokens per second refill rate
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = capacity
        self.last_refill = time.time()
        self.lock = Lock()
    
    def acquire(self, tokens: int = 1) -> bool:
        """
        Acquire tokens from bucket.
        
        Args:
            tokens: Number of tokens to acquire
            
        Returns:
            bool: True if tokens acquired, False otherwise
        """
        with self.lock:
            now = time.time()
            # Refill tokens based on elapsed time
            time_passed = now - self.last_refill
            self.tokens = min(
                self.capacity,
                self.tokens + (time_passed * self.refill_rate)
            )
            self.last_refill = now
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    def wait_time(self, tokens: int = 1) -> float:
        """Calculate wait time for tokens to be available."""
        with self.lock:
            if self.tokens >= tokens:
                return 0.0
            return (tokens - self.tokens) / self.refill_rate


class AdaptiveRateLimiter:
    """Adaptive rate limiter with performance optimization."""
    
    def __init__(self, base_rate: int = 5, burst_capacity: int = 10):
        """
        Initialize adaptive rate limiter.
        
        Args:
            base_rate: Base requests per second
            burst_capacity: Maximum burst capacity
        """
        self.bucket = TokenBucket(burst_capacity, base_rate)
        self.response_times = deque(maxlen=100)
        self.error_counts = defaultdict(int)
        self.adaptive_factor = 1.0
        self.lock = RLock()
    
    def acquire_permit(self, timeout: float = 10.0) -> bool:
        """
        Acquire permission to make request.
        
        Args:
            timeout: Maximum wait time in seconds
            
        Returns:
            bool: True if permit acquired
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.bucket.acquire():
                return True
            
            wait_time = min(
                self.bucket.wait_time(),
                timeout - (time.time() - start_time)
            )
            
            if wait_time > 0:
                time.sleep(wait_time)
        
        return False
    
    def record_response(self, response_time: float, status_code: int):
        """Record response metrics for adaptive adjustment."""
        with self.lock:
            self.response_times.append(response_time)
            
            if status_code == 429:  # Rate limited
                self.adaptive_factor *= 0.8  # Reduce rate
                self.error_counts['rate_limit'] += 1
            elif status_code >= 500:  # Server error
                self.adaptive_factor *= 0.9
                self.error_counts['server_error'] += 1
            elif status_code == 200 and response_time < 1.0:
                self.adaptive_factor = min(1.2, self.adaptive_factor * 1.01)
            
            # Adjust bucket refill rate
            if len(self.response_times) >= 10:
                avg_response_time = sum(self.response_times) / len(self.response_times)
                if avg_response_time > 2.0:
                    self.bucket.refill_rate *= 0.95


class EndpointRegistry:
    """Registry for Polygon.io API endpoints with version management."""
    
    ENDPOINTS = {
        # Market Data
        'market_status': '/v1/marketstatus/now',
        'market_holidays': '/v1/marketstatus/upcoming',
        
        # Stocks
        'stock_aggregates': '/v2/aggs/ticker/{ticker}/range/{multiplier}/{timespan}/{from_}/{to}',
        'stock_trades': '/v2/trades/{ticker}',
        'stock_quotes': '/v2/quotes/{ticker}',
        'stock_last_trade': '/v2/last/trade/{ticker}',
        'stock_last_quote': '/v2/last/nbbo/{ticker}',
        'stock_snapshot': '/v2/snapshot/ticker/{ticker}',
        'stock_snapshot_all': '/v2/snapshot/tickers',
        
        # Options
        'options_chain': '/v3/snapshot/options/{underlying_asset}',
        'options_contract': '/v3/reference/options/contracts/{options_ticker}',
        'options_contracts_list': '/v3/reference/options/contracts',
        'options_trades': '/v2/trades/{options_ticker}',
        'options_quotes': '/v2/quotes/{options_ticker}',
        'options_aggregates': '/v2/aggs/ticker/{options_ticker}/range/{multiplier}/{timespan}/{from_}/{to}',
        'options_last_trade': '/v2/last/trade/{options_ticker}',
        
        # Reference Data
        'tickers': '/v3/reference/tickers',
        'ticker_details': '/v3/reference/tickers/{ticker}',
        'exchanges': '/v3/reference/exchanges',
        'market_data': '/v3/reference/market-data',
    }
    
    @classmethod
    def get_endpoint(cls, name: str, **kwargs) -> str:
        """Get formatted endpoint URL."""
        if name not in cls.ENDPOINTS:
            raise ValueError(f"Unknown endpoint: {name}")
        
        endpoint = cls.ENDPOINTS[name]
        try:
            return endpoint.format(**kwargs)
        except KeyError as e:
            raise ValueError(f"Missing parameter {e} for endpoint {name}")
    
    @classmethod
    def get_all_endpoints(cls) -> Dict[str, str]:
        """Get all available endpoints."""
        return cls.ENDPOINTS.copy()


class ConnectionManager:
    """Manages HTTP connection pooling and health monitoring."""
    
    def __init__(self, max_connections: int = 30, timeout: int = 30):
        """
        Initialize connection manager.
        
        Args:
            max_connections: Maximum connection pool size
            timeout: Request timeout in seconds
        """
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        self.pool = PoolManager(
            num_pools=15,
            maxsize=max_connections,
            block=False,
            timeout=urllib3.Timeout(connect=timeout, read=timeout),
            retries=retry_strategy
        )
        
        self.health_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0
        }
        self.lock = Lock()
    
    def make_request(self, method: str, url: str, **kwargs) -> Any:
        """Make HTTP request with health monitoring."""
        start_time = time.time()
        
        with self.lock:
            self.health_stats['total_requests'] += 1
        
        try:
            response = self.pool.request(method, url, **kwargs)
            response_time = time.time() - start_time
            
            with self.lock:
                self.health_stats['successful_requests'] += 1
                # Update average response time
                total = self.health_stats['total_requests']
                current_avg = self.health_stats['avg_response_time']
                self.health_stats['avg_response_time'] = (
                    (current_avg * (total - 1) + response_time) / total
                )
            
            return response
            
        except Exception as e:
            with self.lock:
                self.health_stats['failed_requests'] += 1
            raise e
    
    def get_health_stats(self) -> Dict[str, Any]:
        """Get connection health statistics."""
        with self.lock:
            stats = self.health_stats.copy()
            
        if stats['total_requests'] > 0:
            stats['success_rate'] = (
                stats['successful_requests'] / stats['total_requests']
            )
        else:
            stats['success_rate'] = 0.0
            
        return stats


class PolygonAPI:
    """
    Enhanced Polygon API client with production-ready features.
    
    Features:
    - Adaptive rate limiting based on subscription tier
    - Comprehensive endpoint coverage
    - Intelligent caching with TTL management
    - Connection pooling and health monitoring
    - Error handling with exponential backoff
    - AI agent training compatibility
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: str = "https://api.polygon.io",
        rate_limit: int = 5,
        burst_capacity: int = 10,
        cache_ttl: int = 3600,
        enable_caching: bool = True,
        cache: Optional[Any] = None,
        max_connections: int = 30
    ):
        """
        Initialize enhanced Polygon API client.
        
        Args:
            api_key: Polygon API key (from env if None)
            base_url: API base URL
            rate_limit: Requests per second limit
            burst_capacity: Maximum burst requests
            cache_ttl: Cache TTL in seconds
            enable_caching: Enable response caching
            cache: External cache instance
            max_connections: Max connection pool size
        """
        # API Configuration
        self.api_key = api_key or os.getenv("POLYGON_API_KEY")
        if not self.api_key:
            raise ValueError("Polygon API key required")
        
        self.base_url = base_url.rstrip('/')
        
        # Components
        self.rate_limiter = AdaptiveRateLimiter(rate_limit, burst_capacity)
        self.endpoints = EndpointRegistry()
        self.connection_manager = ConnectionManager(max_connections)
        
        # Caching
        self.enable_caching = enable_caching
        if enable_caching and cache is None:
            from api_robustness.modules.api_cache import ApiCache
            self.cache = ApiCache(default_ttl=cache_ttl)
        else:
            self.cache = cache
        
        # Initialize Polygon client if available
        if HAS_POLYGON_CLIENT and self.api_key:
            self.polygon_client = RESTClient(self.api_key)
        else:
            self.polygon_client = None
            
        logger.info(
            f"Polygon API client initialized: rate_limit={rate_limit}/s, "
            f"caching={enable_caching}, connections={max_connections}"
        )
    
    def _make_request(
        self,
        endpoint_name: str,
        method: str = "GET",
        params: Optional[Dict[str, Any]] = None,
        cache_key: Optional[str] = None,
        **endpoint_params
    ) -> Dict[str, Any]:
        """
        Make API request with rate limiting and caching.
        
        Args:
            endpoint_name: Registered endpoint name
            method: HTTP method
            params: Query parameters
            cache_key: Custom cache key
            **endpoint_params: Parameters for endpoint formatting
            
        Returns:
            dict: API response data
        """
        # Check cache first
        if self.enable_caching and cache_key:
            cached_response = self.cache.get(endpoint_name, {"key": cache_key})
            if cached_response is not None:
                return cached_response
        
        # Acquire rate limit permit
        if not self.rate_limiter.acquire_permit():
            raise Exception("Rate limit exceeded - could not acquire permit")
        
        # Build URL
        endpoint_path = self.endpoints.get_endpoint(endpoint_name, **endpoint_params)
        url = f"{self.base_url}{endpoint_path}"
        
        # Add API key to params
        if params is None:
            params = {}
        params['apikey'] = self.api_key
        
        # Make request
        start_time = time.time()
        try:
            response = self.connection_manager.make_request(
                method, url, fields=params
            )
            response_time = time.time() - start_time
            
            # Record metrics
            self.rate_limiter.record_response(response_time, response.status)
            
            if response.status == 200:
                data = json.loads(response.data.decode('utf-8'))
                
                # Cache successful response
                if self.enable_caching and cache_key:
                    # Fix: Use put() instead of set() to match ApiCache interface
                    if hasattr(self.cache, 'put'):
                        import asyncio
                        try:
                            loop = asyncio.get_event_loop()
                            loop.run_until_complete(self.cache.put(cache_key, data))
                        except RuntimeError:
                            asyncio.run(self.cache.put(cache_key, data))
                    elif hasattr(self.cache, 'set'):
                        self.cache.set(endpoint_name, {"key": cache_key}, data)
                
                return data
            else:
                raise Exception(f"API request failed: {response.status} - {response.data}")
                
        except Exception as e:
            logger.error(f"Request failed for {endpoint_name}: {str(e)}")
            raise
    
    def test_connection(self) -> Dict[str, Any]:
        """Test API connection and return status."""
        try:
            response = self._make_request('market_status')
            return {
                "status": "success",
                "connected": True,
                "market_status": response.get("market"),
                "api_key_valid": True
            }
        except Exception as e:
            return {
                "status": "error",
                "connected": False,
                "error": str(e),
                "api_key_valid": False
            }
    
    def get_options_chain(
        self,
        underlying_ticker: str,
        expiry_date: Optional[str] = None,
        contract_type: Optional[str] = None,
        strike_price: Optional[float] = None
    ) -> List[Dict[str, Any]]:
        """
        Get complete options chain for underlying asset.
        
        Args:
            underlying_ticker: Underlying stock ticker
            expiry_date: Specific expiry date (YYYY-MM-DD)
            contract_type: 'call' or 'put' filter
            strike_price: Specific strike price filter
            
        Returns:
            list: Options contracts data
        """
        cache_key = f"{underlying_ticker}_{expiry_date}_{contract_type}_{strike_price}"
        
        try:
            # Use Polygon client if available
            if self.polygon_client:
                contracts = list(
                    self.polygon_client.list_snapshot_options_chain(
                        underlying_asset=underlying_ticker
                    )
                )
                
                results = []
                for contract in contracts:
                    contract_data = {
                        'symbol': contract.details.ticker,
                        'underlying': underlying_ticker,
                        'strike': contract.details.strike_price,
                        'expiry': contract.details.expiration_date,
                        'type': contract.details.contract_type,
                        'bid': getattr(contract.day, 'bid_price', 0) if contract.day else 0,
                        'ask': getattr(contract.day, 'ask_price', 0) if contract.day else 0,
                        'last': getattr(contract.day, 'last_price', 0) if contract.day else 0,
                        'volume': getattr(contract.day, 'volume', 0) if contract.day else 0,
                        'open_interest': getattr(contract, 'open_interest', 0),
                        'implied_volatility': getattr(contract, 'implied_volatility', 0),
                    }
                    
                    # Add Greeks if available
                    if hasattr(contract, 'greeks') and contract.greeks:
                        contract_data.update({
                            'delta': contract.greeks.delta,
                            'gamma': contract.greeks.gamma,
                            'theta': contract.greeks.theta,
                            'vega': contract.greeks.vega,
                        })
                    
                    # Apply filters
                    if expiry_date and contract_data['expiry'] != expiry_date:
                        continue
                    if contract_type and contract_data['type'].lower() != contract_type.lower():
                        continue
                    if strike_price and abs(contract_data['strike'] - strike_price) > 0.01:
                        continue
                    
                    results.append(contract_data)
                
                # Cache results
                if self.enable_caching:
                    # Fix: Use put() instead of set() to match ApiCache interface
                    if hasattr(self.cache, 'put'):
                        import asyncio
                        try:
                            loop = asyncio.get_event_loop()
                            loop.run_until_complete(self.cache.put(cache_key, results))
                        except RuntimeError:
                            asyncio.run(self.cache.put(cache_key, results))
                    elif hasattr(self.cache, 'set'):
                        self.cache.set('options_chain', {"key": cache_key}, results)
                
                return results
            else:
                # Fallback to REST API
                response = self._make_request(
                    'options_chain',
                    cache_key=cache_key,
                    underlying_asset=underlying_ticker
                )
                return response.get('results', [])
                
        except Exception as e:
            logger.error(f"Failed to get options chain for {underlying_ticker}: {str(e)}")
            return []
    
    def get_stock_price(self, ticker: str) -> float:
        """Get current stock price."""
        try:
            if self.polygon_client:
                trade = self.polygon_client.get_last_trade(ticker)
                return float(trade.price) if trade and hasattr(trade, 'price') else 0.0
            else:
                response = self._make_request(
                    'stock_last_trade',
                    cache_key=f"price_{ticker}",
                    ticker=ticker
                )
                return float(response.get('results', {}).get('price', 0.0))
        except Exception as e:
            logger.error(f"Failed to get price for {ticker}: {str(e)}")
            return 0.0
    
    def get_historical_data(
        self,
        ticker: str,
        timespan: str = "day",
        multiplier: int = 1,
        from_date: str = None,
        to_date: str = None,
        limit: int = 5000
    ) -> List[Dict[str, Any]]:
        """
        Get historical OHLCV data.
        
        Args:
            ticker: Stock or options ticker
            timespan: minute, hour, day, week, month, quarter, year
            multiplier: Size of timespan multiplier
            from_date: Start date (YYYY-MM-DD)
            to_date: End date (YYYY-MM-DD)
            limit: Maximum number of results
            
        Returns:
            list: Historical OHLCV data
        """
        if not from_date:
            from_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        if not to_date:
            to_date = datetime.now().strftime('%Y-%m-%d')
        
        cache_key = f"{ticker}_{timespan}_{multiplier}_{from_date}_{to_date}"
        
        try:
            if self.polygon_client:
                bars = list(
                    self.polygon_client.list_aggs(
                        ticker=ticker,
                        multiplier=multiplier,
                        timespan=timespan,
                        from_=from_date,
                        to=to_date,
                        limit=limit
                    )
                )
                
                results = []
                for bar in bars:
                    results.append({
                        'timestamp': bar.timestamp,
                        'open': bar.open,
                        'high': bar.high,
                        'low': bar.low,
                        'close': bar.close,
                        'volume': bar.volume,
                        'vwap': getattr(bar, 'vwap', 0),
                        'transactions': getattr(bar, 'transactions', 0)
                    })
                
                # Cache results
                if self.enable_caching:
                    # Fix: Use put() instead of set() to match ApiCache interface
                    if hasattr(self.cache, 'put'):
                        import asyncio
                        try:
                            loop = asyncio.get_event_loop()
                            loop.run_until_complete(self.cache.put(cache_key, results))
                        except RuntimeError:
                            asyncio.run(self.cache.put(cache_key, results))
                    elif hasattr(self.cache, 'set'):
                        self.cache.set('historical_data', {"key": cache_key}, results)
                
                return results
            else:
                # Use REST endpoint
                response = self._make_request(
                    'stock_aggregates',
                    cache_key=cache_key,
                    ticker=ticker,
                    multiplier=multiplier,
                    timespan=timespan,
                    from_=from_date,
                    to=to_date,
                    params={'limit': limit}
                )
                return response.get('results', [])
                
        except Exception as e:
            logger.error(f"Failed to get historical data for {ticker}: {str(e)}")
            return []
    
    def get_api_usage_stats(self) -> Dict[str, Any]:
        """Get API usage and performance statistics."""
        connection_stats = self.connection_manager.get_health_stats()
        
        cache_stats = {}
        if self.enable_caching and hasattr(self.cache, 'get_stats'):
            cache_stats = self.cache.get_stats()
        
        rate_limit_stats = {
            'current_tokens': self.rate_limiter.bucket.tokens,
            'capacity': self.rate_limiter.bucket.capacity,
            'refill_rate': self.rate_limiter.bucket.refill_rate,
            'adaptive_factor': self.rate_limiter.adaptive_factor,
            'error_counts': dict(self.rate_limiter.error_counts)
        }
        
        return {
            'connection': connection_stats,
            'cache': cache_stats,
            'rate_limiting': rate_limit_stats,
            'api_key_valid': bool(self.api_key),
            'polygon_client_available': bool(self.polygon_client)
        }
    
    def optimize_for_ai_training(self) -> Dict[str, Any]:
        """
        Generate optimization recommendations for AI agent training.
        
        Returns:
            dict: Optimization patterns and recommendations
        """
        stats = self.get_api_usage_stats()
        
        recommendations = {
            'rate_limiting': {
                'current_rate': self.rate_limiter.bucket.refill_rate,
                'recommended_rate': min(100, self.rate_limiter.bucket.refill_rate * 1.2),
                'burst_optimization': 'increase_for_batch_operations',
                'pattern': 'adaptive_rate_limiting'
            },
            'caching': {
                'hit_rate': stats.get('cache', {}).get('hit_rate', 0),
                'target_hit_rate': 0.85,
                'optimization': 'aggressive_caching_for_reference_data',
                'ttl_recommendations': {
                    'options_chains': 300,  # 5 minutes
                    'historical_data': 3600,  # 1 hour
                    'reference_data': 86400  # 1 day
                }
            },
            'endpoints': {
                'high_frequency': ['options_chain', 'stock_price', 'market_status'],
                'batch_optimizable': ['historical_data', 'options_contracts_list'],
                'cache_priority': ['reference_data', 'historical_aggregates']
            },
            'ai_training_patterns': {
                'bulk_data_retrieval': 'use_pagination_with_rate_limiting',
                'real_time_monitoring': 'websocket_preferred_over_polling',
                'error_handling': 'exponential_backoff_with_circuit_breaker'
            }
        }
        
        return recommendations
