task_id: A-01
name: Greek / IV Anomaly Detector
version: 1.0.0
description: >
  Compares current Greek & IV metrics to rolling distributions; flags
  statistically significant or regime-defined anomalies with plain-language
  interpretation.

inputs:
  - feature_row_json        # single-row dict produced by build_features
  - hist_stats_parquet      # rolling mean / std / percentiles

outputs:
  files:
    - path: anomalies/{{date}}/{{ticker}}_anomalies.json
      must_exist: true

success_criteria:
  perf_budget: 
    max_runtime_ms: 50
  code_coverage_min: 0.95
  accuracy_min: 0.85

validation:
  statistical_significance: true
  z_score_threshold: 2.5
  percentile_threshold: 0.05

dependencies:
  - pandas>=1.5.0
  - numpy>=1.21.0

anomaly_rules:
  gamma_roc:
    high_threshold: 2.5      # z-score
    low_threshold: -2.5
  vanna_raw:
    extreme_threshold: 3.0   # abs z-score
  charm_raw:
    percentile_threshold: 0.05
  iv_rank:
    high_threshold: 0.9      # absolute value
  iv_roc:
    extreme_threshold: 2.0

interpretations:
  gamma_roc_high: "Dealer gamma expanding rapidly  expect spot dampening."
  gamma_roc_low: "Gamma collapsing  directional moves can stretch."
  vanna_raw_ext: "Large vanna; spot-vol feedback may accelerate moves."
  charm_raw_neg: "Charm drag  deltas will decay; watch intraday roll."
  iv_rank_high: "IV rank > 90th pct  options expensive, consider spreads."
  iv_roc_extreme: "IV momentum extreme  potential volatility regime shift."
