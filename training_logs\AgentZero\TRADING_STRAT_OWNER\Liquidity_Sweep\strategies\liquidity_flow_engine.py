"""
Liquidity Flow Enhancement for Liquidity Sweep Strategy
Replaces R:R ratios with ROI-based liquidity pocket targeting
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional

logger = logging.getLogger(__name__)

class LiquidityFlowEngine:
    """
    Handles liquidity flow analysis and ROI-based targeting
    Maps liquidity pockets and calculates optimal entry/exit points
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('liquidity_flow', {
            'min_roi_threshold': 15.0,           # Minimum 15% ROI required
            'max_distance_from_entry_pct': 0.05, # Max 5% distance for liquidity targets
            'liquidity_strength_weight': 0.4,    # Weight for liquidity strength in scoring
            'distance_weight': 0.3,              # Weight for distance in scoring  
            'volume_weight': 0.3,                # Weight for volume in scoring
            'min_liquidity_pocket_strength': 0.6, # Minimum strength for valid pocket
            'max_liquidity_targets': 3,           # Maximum number of targets
        })
    
    def find_liquidity_pockets(self, price_data: pd.DataFrame, current_price: float, 
                              direction: str, identified_levels: List[Dict]) -> List[Dict[str, Any]]:
        """
        Find liquidity pockets in the direction of the trade
        Returns sorted list of liquidity targets with ROI calculations
        """
        liquidity_pockets = []
        
        # Filter levels by direction
        if direction.upper() == 'LONG':
            # For LONG trades, find resistance levels above current price (targets)
            target_levels = [level for level in identified_levels 
                           if level['type'] == 'resistance' and level['price'] > current_price]
        else:
            # For SHORT trades, find support levels below current price (targets)  
            target_levels = [level for level in identified_levels
                           if level['type'] == 'support' and level['price'] < current_price]
        
        if not target_levels:
            return liquidity_pockets
        
        # Calculate distance and filter by max distance
        max_distance = current_price * self.config['max_distance_from_entry_pct']
        
        for level in target_levels:
            distance = abs(level['price'] - current_price)
            distance_pct = distance / current_price
            
            # Skip if too far away
            if distance > max_distance:
                continue
            
            # Calculate volume concentration at this level
            volume_score = self._calculate_volume_concentration(price_data, level['price'])
            
            # Calculate liquidity pocket strength
            pocket_strength = self._calculate_pocket_strength(level, volume_score)
            
            # Skip weak pockets
            if pocket_strength < self.config['min_liquidity_pocket_strength']:
                continue
            
            # Calculate distance score (closer = better)
            distance_score = 1.0 - (distance_pct / self.config['max_distance_from_entry_pct'])
            
            # Combined score for ranking targets
            combined_score = (
                pocket_strength * self.config['liquidity_strength_weight'] +
                distance_score * self.config['distance_weight'] +
                volume_score * self.config['volume_weight']
            )
            
            liquidity_pockets.append({
                'price': level['price'],
                'type': level['type'],
                'strength': pocket_strength,
                'volume_score': volume_score,
                'distance_pct': distance_pct,
                'distance_score': distance_score,
                'combined_score': combined_score,
                'touches': level.get('touches', 0),
                'pocket_classification': self._classify_liquidity_pocket(level, volume_score)
            })
        
        # Sort by combined score (best targets first)
        liquidity_pockets.sort(key=lambda x: x['combined_score'], reverse=True)
        
        # Limit to max targets
        return liquidity_pockets[:self.config['max_liquidity_targets']]
    
    def calculate_roi_targets(self, entry_price: float, stop_loss: float, 
                            liquidity_pockets: List[Dict], direction: str) -> List[Dict[str, Any]]:
        """
        Calculate ROI for each liquidity pocket and create target structure
        """
        if not liquidity_pockets:
            return []
        
        risk_amount = abs(entry_price - stop_loss)
        if risk_amount == 0:
            return []
        
        roi_targets = []
        
        for i, pocket in enumerate(liquidity_pockets):
            target_price = pocket['price']
            
            # Calculate reward and ROI
            if direction.upper() == 'LONG':
                reward = target_price - entry_price
            else:
                reward = entry_price - target_price
            
            if reward <= 0:
                continue  # Skip negative reward targets
            
            roi_percentage = (reward / risk_amount) * 100
            
            # Skip targets below minimum ROI threshold
            if roi_percentage < self.config['min_roi_threshold']:
                continue
            
            # Calculate position size recommendation based on liquidity
            position_size_factor = min(1.0, pocket['strength'] * pocket['volume_score'])
            
            roi_targets.append({
                'target_number': i + 1,
                'price': round(target_price, 2),
                'roi_percentage': round(roi_percentage, 1),
                'reward_amount': round(reward, 2),
                'liquidity_strength': round(pocket['strength'], 3),
                'volume_score': round(pocket['volume_score'], 3),
                'distance_pct': round(pocket['distance_pct'] * 100, 2),
                'pocket_type': pocket['pocket_classification'],
                'position_size_factor': round(position_size_factor, 2),
                'confidence': round(pocket['combined_score'], 3),
                'touches': pocket['touches']
            })
        
        return roi_targets
    
    def _calculate_volume_concentration(self, price_data: pd.DataFrame, level_price: float) -> float:
        """Calculate volume concentration around a price level"""
        try:
            # Find bars near this level (within 1% tolerance)
            tolerance = level_price * 0.01
            level_bars = price_data[
                (price_data['low'] <= level_price + tolerance) & 
                (price_data['high'] >= level_price - tolerance)
            ]
            
            if len(level_bars) == 0:
                return 0.5  # Default score if no bars found
            
            # Calculate volume metrics
            level_volume = level_bars['volume'].sum()
            avg_volume = price_data['volume'].mean()
            
            if avg_volume == 0:
                return 0.5
            
            # Volume concentration score (normalized)
            volume_ratio = level_volume / (avg_volume * len(level_bars))
            return min(1.0, max(0.1, volume_ratio / 2.0))  # Normalize to 0.1-1.0 range
            
        except Exception as e:
            logger.warning(f"Error calculating volume concentration: {e}")
            return 0.5
    
    def _calculate_pocket_strength(self, level: Dict, volume_score: float) -> float:
        """Calculate overall liquidity pocket strength"""
        # Base strength from level detection
        base_strength = level.get('strength', 0.5)
        
        # Boost from volume concentration
        volume_boost = volume_score * 0.3
        
        # Boost from number of touches (more touches = stronger level)
        touches = level.get('touches', 1)
        touch_boost = min(0.2, touches * 0.05)  # Max 0.2 boost from touches
        
        # Combined strength
        total_strength = base_strength + volume_boost + touch_boost
        return min(1.0, total_strength)
    
    def _classify_liquidity_pocket(self, level: Dict, volume_score: float) -> str:
        """Classify the type of liquidity pocket"""
        strength = level.get('strength', 0.5)
        touches = level.get('touches', 1)
        
        if strength >= 0.8 and volume_score >= 0.7:
            return "Major Liquidity Zone"
        elif strength >= 0.7 and touches >= 3:
            return "Strong Level"
        elif volume_score >= 0.8:
            return "High Volume Zone"
        elif touches >= 4:
            return "Multiple Touch Level"
        else:
            return "Minor Level"

def enhance_strategy_with_liquidity_flow(strategy_instance):
    """
    Enhance the existing strategy with liquidity flow capabilities
    Modifies the signal creation to use ROI-based targeting
    """
    
    # Add liquidity flow engine to strategy
    flow_config = strategy_instance.config.get('liquidity_flow', {})
    strategy_instance.liquidity_flow_engine = LiquidityFlowEngine(strategy_instance.config)
    
    # Store original create_signal method
    original_create_signal = strategy_instance.create_signal
    
    def enhanced_create_signal(ticker: str, direction, strength, entry: float, stop_loss: float, 
                              take_profit: List[float], confidence: float, reason: str, 
                              analysis: Dict, timeframe: str, **kwargs):
        """Enhanced signal creation with liquidity flow targeting"""
        
        try:
            # Get identified levels from analysis
            identified_levels = analysis.get('identified_levels', [])
            price_data = analysis.get('price_data')
            
            if not identified_levels or price_data is None:
                # Fallback to original method if no liquidity data
                return original_create_signal(ticker, direction, strength, entry, stop_loss, 
                                            take_profit, confidence, reason, analysis, timeframe, **kwargs)
            
            # Find liquidity pockets for this trade direction
            direction_str = 'LONG' if direction.value == 'LONG' else 'SHORT'
            liquidity_pockets = strategy_instance.liquidity_flow_engine.find_liquidity_pockets(
                price_data, entry, direction_str, identified_levels
            )
            
            if not liquidity_pockets:
                logger.debug(f"[{ticker}] No suitable liquidity pockets found, using fallback targets")
                # Use original take_profit if no liquidity pockets found
                roi_targets = []
                for i, tp in enumerate(take_profit):
                    risk = abs(entry - stop_loss)
                    reward = abs(tp - entry)
                    roi = (reward / risk * 100) if risk > 0 else 0
                    roi_targets.append({
                        'target_number': i + 1,
                        'price': tp,
                        'roi_percentage': round(roi, 1),
                        'pocket_type': 'Fallback Target',
                        'liquidity_strength': 0.5
                    })
            else:
                # Calculate ROI-based targets from liquidity pockets
                roi_targets = strategy_instance.liquidity_flow_engine.calculate_roi_targets(
                    entry, stop_loss, liquidity_pockets, direction_str
                )
                
                if not roi_targets:
                    logger.debug(f"[{ticker}] No ROI targets meet minimum threshold")
                    return None  # No valid targets found
                
                # Update take_profit with liquidity-based targets
                take_profit = [target['price'] for target in roi_targets]
            
            # Enhanced reason with liquidity flow info
            if roi_targets:
                best_roi = max(roi_targets, key=lambda x: x['roi_percentage'])
                enhanced_reason = (f"{reason} | Liquidity Flow: {len(roi_targets)} targets, "
                                 f"Best ROI: {best_roi['roi_percentage']}% "
                                 f"({best_roi['pocket_type']})")
            else:
                enhanced_reason = reason
            
            # Enhanced analysis with liquidity flow data
            enhanced_analysis = analysis.copy()
            enhanced_analysis.update({
                'liquidity_flow': {
                    'roi_targets': roi_targets,
                    'liquidity_pockets': liquidity_pockets,
                    'flow_direction': direction_str,
                    'total_liquidity_strength': sum(p['strength'] for p in liquidity_pockets),
                    'avg_roi': np.mean([t['roi_percentage'] for t in roi_targets]) if roi_targets else 0
                }
            })
            
            # Create signal with enhanced data
            signal = original_create_signal(ticker, direction, strength, entry, stop_loss, 
                                          take_profit, confidence, enhanced_reason, 
                                          enhanced_analysis, timeframe, **kwargs)
            
            # Add ROI targets to signal object
            if hasattr(signal, '__dict__'):
                signal.roi_targets = roi_targets
                signal.liquidity_pockets = liquidity_pockets
            
            logger.info(f"[{ticker}] Enhanced {direction_str} signal with {len(roi_targets)} ROI targets")
            return signal
            
        except Exception as e:
            logger.error(f"Error in enhanced signal creation for {ticker}: {e}")
            # Fallback to original method
            return original_create_signal(ticker, direction, strength, entry, stop_loss, 
                                        take_profit, confidence, reason, analysis, timeframe, **kwargs)
    
    # Replace the create_signal method
    strategy_instance.create_signal = enhanced_create_signal
    
    logger.info("Strategy enhanced with Liquidity Flow targeting")
    return strategy_instance

def update_risk_management_config(strategy_config: Dict[str, Any]) -> Dict[str, Any]:
    """Update strategy config to use liquidity flow instead of R:R ratios"""
    
    # Remove old R:R based config
    if 'risk_management' in strategy_config:
        old_rm = strategy_config['risk_management']
        strategy_config['risk_management'] = {
            'stop_loss_buffer_pct_beyond_sweep': old_rm.get('stop_loss_buffer_pct_beyond_sweep', 0.002),
            # Remove R:R multiples - will be replaced by liquidity flow
        }
    
    # Add liquidity flow configuration
    strategy_config['liquidity_flow'] = {
        'enabled': True,
        'min_roi_threshold': 15.0,               # Minimum 15% ROI required
        'preferred_roi_threshold': 25.0,         # Preferred 25% ROI
        'max_distance_from_entry_pct': 0.08,     # Max 8% distance for targets
        'liquidity_strength_weight': 0.4,        # Weight for liquidity strength
        'distance_weight': 0.3,                  # Weight for distance
        'volume_weight': 0.3,                    # Weight for volume
        'min_liquidity_pocket_strength': 0.6,    # Minimum pocket strength
        'max_liquidity_targets': 3,              # Maximum targets
        'position_sizing': {
            'scale_by_liquidity_strength': True,  # Scale position by liquidity
            'max_position_per_target': 0.4,      # Max 40% position per target
            'reserve_for_best_target': 0.3       # Reserve 30% for best ROI target
        }
    }
    
    return strategy_config

# Example usage function
def create_liquidity_flow_enhanced_strategy(base_strategy_class, config=None):
    """Factory function to create a liquidity flow enhanced strategy"""
    
    if config:
        config = update_risk_management_config(config)
    
    # Create base strategy instance
    strategy = base_strategy_class(config)
    
    # Enhance with liquidity flow
    enhanced_strategy = enhance_strategy_with_liquidity_flow(strategy)
    
    logger.info("Created Liquidity Flow Enhanced Strategy")
    return enhanced_strategy
