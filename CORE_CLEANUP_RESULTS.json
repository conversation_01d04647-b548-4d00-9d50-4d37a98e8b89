{"timestamp": "2025-06-25T15:47:23.925356", "dry_run": false, "removed_files": 171, "preserved_files": ["agent_zero_advanced_capabilities.py", "agent_zero_integration_hub.py", "core_cleanup_analyzer.py", "enhanced_data_agent_broker_integration.py", "enhanced_greeks_engine.py", "execute_cleanup.py", "main.py", "ultimate_orchestrator.py", "validate_greeks_pipeline.py", "validate_mcp_pipeline_performance.py", "validate_pipeline_performance_fixes.py", "agents\\agent_base.py", "agents\\agent_zero.py", "agents\\anomaly_detector_agent.py", "agents\\auto_broker_adapter.py", "agents\\chart_generator_agent.py", "agents\\data_ingestion_agent.py", "agents\\enhanced_csid_agent.py", "agents\\flow_physics_agent.py", "agents\\fvg_specialist.py", "agents\\greek_enhancement_agent.py", "agents\\iv_dynamics_agent.py", "agents\\liquidity_agent.py", "agents\\liquidity_agent_compliance_report.py", "agents\\LIQUIDITY_AGENT_FINAL_ASSESSMENT.py", "agents\\liquidity_agent_necessity_evaluation.py", "agents\\liquidity_agent_validation.py", "agents\\math_validator_agent.py", "agents\\mean_reversion_specialist.py", "agents\\order_router_agent.py", "agents\\order_router_agent_v2.py", "agents\\output_coordinator_agent.py", "agents\\pivot_point_specialist.py", "agents\\risk_guard_agent.py", "agents\\schwab_data_agent.py", "agents\\signal_convergence_orchestrator.py", "agents\\signal_generator_agent.py", "agents\\signal_quality_agent.py", "agents\\training_mixin.py", "agents\\__init__.py", "agents\\accumulation_distribution_detector\\accumulation_distribution_detector.py", "agents\\accumulation_distribution_detector\\enhanced_accumulation_distribution_agent.py", "agents\\agent_orchestrator\\agent_orchestrator.py", "agents\\agent_orchestrator\\agent_orchestrator_real_data_only.py", "agents\\breakout_validation_specialist\\breakout_validation_specialist.py", "agents\\options_flow_decoder\\options_flow_decoder.py", "agent_templates\\create_agent.py", "analyzers\\flow_physics.py", "analyzers\\gex_analysis.py", "analyzers\\liquidity_analysis.py", "analyzers\\volume_analysis.py", "analyzers\\__init__.py", "api\\ai_training_config.py", "api\\cli_mcp.py", "api\\comprehensive_api_tester.py", "api\\dc_mcp_integration.py", "api\\fastapi_mcp_bridge.py", "api\\mcp_client_compatibility.py", "api\\mcp_dc.py", "api\\mcp_direct_api.py", "api\\mcp_http_gateway.py", "api\\mcp_http_wrapper.py", "api\\mcp_resolution_report.py", "api\\mcp_service_manager.py", "api\\refresh_schwab_token.py", "api\\schwab_mcp_server.py", "api\\start_mcp_server.py", "api\\unified_api_gateway.py", "api\\__init__.py", "api\\modules\\api_cache.py", "api\\modules\\api_client_factory.py", "api\\modules\\api_errors.py", "api\\modules\\api_errors_fixed.py", "api\\modules\\api_health_monitor.py", "api\\modules\\connection_pool_manager.py", "api\\modules\\endpoint_registry.py", "api\\modules\\rate_limiter.py", "api\\modules\\schwab_option_parser.py", "api\\modules\\schwab_production_api.py", "api\\modules\\__init__.py", "api\\modules\\config\\paths.py", "api\\modules\\config\\__init__.py", "api\\modules\\data\\__init__.py", "api\\modules\\data\\loaders\\enhanced_polygon_api.py", "api\\modules\\data\\loaders\\__init__.py", "api\\tests\\__init__.py", "ci\\check_version_drift.py", "ci\\git_secrets_scan.py", "ci\\performance_regression_test.py", "ci\\run_contract_tasks.py", "ci\\security_compliance_check.py", "config\\constants.py", "config\\settings.py", "config\\__init__.py", "dashboards\\fills_dashboard.py", "dashboards\\schwab_monitor.py", "data\\api_gateway.py", "data\\data_handler.py", "data\\factor_spec.py", "data\\__init__.py", "engine\\confluence_engine.py", "engine\\signal_generator.py", "engine\\__init__.py", "flowphysics\\flow_physics_integrator.py", "flowphysics\\__init__.py", "Flow_Physics_Engine\\enhanced_csid_analyzer.py", "Flow_Physics_Engine\\position_manager.py", "Flow_Physics_Engine\\risk_manager.py", "Flow_Physics_Engine\\signal_engine.py", "Flow_Physics_Engine\\testing_framework_template.py", "Flow_Physics_Engine\\__init__.py", "Flow_Physics_Engine\\advanced\\advanced_acceleration_analyzer.py", "Flow_Physics_Engine\\advanced\\advanced_velocity_analyzer.py", "Flow_Physics_Engine\\advanced\\flow_jerk_analyzer.py", "Flow_Physics_Engine\\advanced\\flow_physics_integrator.py", "Flow_Physics_Engine\\advanced\\run_flow_physics_demo.py", "Flow_Physics_Engine\\advanced\\__init__.py", "Flow_Physics_Engine\\api_robustness\\comprehensive_api_tester.py", "Flow_Physics_Engine\\api_robustness\\endpoint_registry.py", "Flow_Physics_Engine\\api_robustness\\enhanced_polygon_api.py", "Flow_Physics_Engine\\api_robustness\\rate_limiter.py", "Flow_Physics_Engine\\api_robustness\\unified_api_gateway.py", "Flow_Physics_Engine\\api_robustness\\__init__.py", "Flow_Physics_Engine\\engine_core\\csid_analyzer.py", "Flow_Physics_Engine\\engine_core\\flow_physics_integrator.py", "greeks\\black_scholes_engine.py", "greeks\\constants.py", "greeks\\greeks_result.py", "greeks\\roc_calculator.py", "greeks\\validation_framework.py", "greeks\\__init__.py", "ml\\ml\\ab_testing_framework.py", "ml\\ml\\anomaly_detection.py", "ml\\ml\\cross_timeframe_interaction.py", "ml\\ml\\extract_model_features.py", "ml\\ml\\feature_compatibility.py", "ml\\ml\\feature_imputer.py", "ml\\ml\\generate_training_data.py", "ml\\ml\\hierarchical_feature_extractor.py", "ml\\ml\\hyperparameter_optimization.py", "ml\\ml\\imputer.py", "ml\\ml\\integrate_ml_components.py", "ml\\ml\\liquidity_features.py", "ml\\ml\\liquidity_persistence.py", "ml\\ml\\liquidity_prediction.py", "ml\\ml\\main_integration.py", "ml\\ml\\market_regime_adaptation.py", "ml\\ml\\market_regime_adapter.py", "ml\\ml\\market_regime_adapter_complete.py", "ml\\ml\\market_regime_detection.py", "ml\\ml\\ml.py", "ml\\ml\\ml_alert_manager.py", "ml\\ml\\ml_alert_pipeline.py", "ml\\ml\\ml_base.py", "ml\\ml\\ml_components.py", "ml\\ml\\ml_config.py", "ml\\ml\\ml_config_manager.py", "ml\\ml\\ml_dashboard.py", "ml\\ml\\ML_FEATURE_ALIGNMENT_SUCCESS_REPORT.py", "ml\\ml\\ml_feature_engineering.py", "ml\\ml\\ml_inference_service.py", "ml\\ml\\ml_integration.py", "ml\\ml\\ml_liquidity_integration.py", "ml\\ml\\ml_logging.py", "ml\\ml\\ml_model.py", "ml\\ml\\ml_model_registry.py", "ml\\ml\\ml_optimization.py", "ml\\ml\\ml_quantization.py", "ml\\ml\\ml_service.py", "ml\\ml\\ml_strategy_optimizer.py", "ml\\ml\\ml_system.py", "ml\\ml\\ml_utils.py", "ml\\ml\\ml_visualization.py", "ml\\ml\\optimize_liquidity_models.py", "ml\\ml\\real_time_prediction.py", "ml\\ml\\reinforcement_learning.py", "ml\\ml\\retrain_models.py", "ml\\ml\\tensorflow.py", "ml\\ml\\test_complete_alignment.py", "ml\\ml\\test_feature_alignment.py", "ml\\ml\\test_final_ml_integration.py", "ml\\ml\\timeframe_aware_models.py", "ml\\ml\\train_liquidity_models.py", "ml\\ml\\user_feedback_integration.py", "ml\\ml\\__init__.py", "ml\\ml\\alerts\\__init__.py", "ml\\ml\\examples\\inference_examples\\basic_inference.py", "ml\\ml\\examples\\inference_examples\\streaming_inference.py", "ml\\ml\\features\\order_book_features.py", "ml\\ml\\features\\__init__.py", "ml\\ml\\real_components\\real_ml_feature_extractor.py", "ml\\ml\\real_components\\real_ml_feature_extractor_fixed.py", "ml\\ml\\real_components\\real_model_loader.py", "ml\\ml\\trading\\edge_detection.py", "ml\\ml\\trading\\enhanced_regime_detector.py", "ml\\ml\\trading\\execution_optimizer.py", "ml\\ml\\trading\\liquidity_integration.py", "ml\\ml\\trading\\position_sizing.py", "ml\\ml\\trading\\trade_model.py", "ml\\ml\\trading\\trade_selection.py", "ml\\ml\\trading\\unified_trading_model.py", "ml\\ml\\trading\\__init__.py", "ml\\ml\\trading\\backtesting\\backtesting.py", "ml\\ml\\trading\\backtesting\\data_pipeline.py", "ml\\ml\\trading\\backtesting\\statistical_analysis.py", "ml\\ml\\trading\\backtesting\\training_pipeline.py", "ml\\ml\\trading\\backtesting\\__init__.py", "SCHWAB_MCP_PRODUCTION\\core\\schwab_mcp_server.py", "SCHWAB_MCP_PRODUCTION\\core\\schwab_production_api.py", "SCHWAB_MCP_PRODUCTION\\scripts\\refresh_token.py", "SCHWAB_MCP_PRODUCTION\\scripts\\START_MCP_SERVER.py", "SCHWAB_MCP_PRODUCTION\\scripts\\update_agents.py", "SCHWAB_MCP_PRODUCTION\\scripts\\validate_system.py", "tasks\\backtest_simulator.py", "tasks\\build_features.py", "tasks\\fetch_history.py", "tasks\\greeks.py", "tasks\\run_backtest_batch.py", "tasks\\update_hist_stats.py", "tasks\\walk_train_validate.py", "tasks\\__init__.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analysis\\__init__.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\base_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\enhanced_gex_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\enhanced_volume_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\funding_rate_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\iv_skew_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\liquidity_sweep_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\liquidity_sweep_detector.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\liquidity_void_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\oi_liquidity_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\options_oi_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\order_book_wall_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\sentiment_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\volatility_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\volume_profile_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\analyzers\\__init__.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\base_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\enhanced_flow_physics_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\flow_momentum_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\gamma_squeeze_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\liquidity_flow_engine.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\liquidity_sweep_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\liquidity_void_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\monitored_enhanced_flow_physics_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\performance_monitor.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\pure_liquidity_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\strategy_factory.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\vanna_charm_squeeze_strategy.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\Liquidity_Sweep\\strategies\\__init__.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\analyzers\\bsm_model.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\analyzers\\options_flow_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\analyzers\\ordinal_greeks_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\enhanced_flow_physics\\advanced_acceleration_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\enhanced_flow_physics\\advanced_velocity_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\enhanced_flow_physics\\flow_jerk_analyzer.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\enhanced_flow_physics\\flow_physics_integrator.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\enhanced_flow_physics\\run_flow_physics_demo.py", "training_logs\\AgentZero\\TRADING_STRAT_OWNER\\ordinal_quantum_trading\\enhanced_flow_physics\\__init__.py", "utils\\base_agent.py", "utils\\greeks_history_cache.py", "utils\\option_chain_entropy.py", "utils\\parallel.py", "utils\\profile_loader.py", "utils\\schwab_chain_parser.py", "utils\\validation.py", "utils\\__init__.py"], "errors": [], "total_size_freed_mb": 1.82, "removal_log": [{"path": "agent_zero_data_maximizer.py", "reason": "Agent Zero test variations", "size": 27255, "action": "removed"}, {"path": "agent_zero_data_maximizer_BACKUP.py", "reason": "Backup file", "size": 36647, "action": "removed"}, {"path": "agent_zero_data_maximizer_FIXED.py", "reason": "Agent Zero test variations", "size": 5716, "action": "removed"}, {"path": "agent_zero_diagnostic.py", "reason": "Diagnostic scripts", "size": 24671, "action": "removed"}, {"path": "agent_zero_final_validation.py", "reason": "Validation scripts", "size": 10403, "action": "removed"}, {"path": "agent_zero_performance_analytics.py", "reason": "Agent Zero performance tests", "size": 19168, "action": "removed"}, {"path": "agent_zero_performance_audit.py", "reason": "Agent Zero performance tests", "size": 22118, "action": "removed"}, {"path": "agent_zero_quick_diag.py", "reason": "Quick test scripts", "size": 13113, "action": "removed"}, {"path": "agent_zero_signal_flow_fix_complete.py", "reason": "Temporary fix scripts", "size": 7994, "action": "removed"}, {"path": "analyze_dependencies.py", "reason": "Dependency analysis scripts", "size": 11486, "action": "removed"}, {"path": "cleanup_training_directories.py", "reason": "Directory cleanup scripts", "size": 12985, "action": "removed"}, {"path": "comprehensive_system_test.py", "reason": "System test suites", "size": 43729, "action": "removed"}, {"path": "comprehensive_test.py", "reason": "Comprehensive test scripts", "size": 31307, "action": "removed"}, {"path": "consolidate_training_logs.py", "reason": "Log consolidation scripts", "size": 8229, "action": "removed"}, {"path": "create_sample_data.py", "reason": "General cleanup candidate", "size": 6838, "action": "removed"}, {"path": "essential_system_test.py", "reason": "System testing scripts", "size": 11459, "action": "removed"}, {"path": "final_validation.py", "reason": "Validation scripts", "size": 18114, "action": "removed"}, {"path": "fix_dependencies.py", "reason": "Temporary fix scripts", "size": 7046, "action": "removed"}, {"path": "fix_greeks_pipeline_urgent.py", "reason": "Temporary fix scripts", "size": 11909, "action": "removed"}, {"path": "live_market_test.py", "reason": "Market testing scripts", "size": 20815, "action": "removed"}, {"path": "live_test_checklist.py", "reason": "Test scripts", "size": 19474, "action": "removed"}, {"path": "main_enhanced_agent_zero_maximized.py", "reason": "General cleanup candidate", "size": 29554, "action": "removed"}, {"path": "main_original_backup.py", "reason": "Backup file", "size": 14014, "action": "removed"}, {"path": "mcp_agent_zero_test.py", "reason": "MCP testing scripts", "size": 7492, "action": "removed"}, {"path": "multi_ticker_batch_test.py", "reason": "Batch testing scripts", "size": 2430, "action": "removed"}, {"path": "production_test_suite.py", "reason": "Test scripts", "size": 13392, "action": "removed"}, {"path": "proper_schwab_api_validation.py", "reason": "Validation scripts", "size": 9352, "action": "removed"}, {"path": "quick_architecture_validation.py", "reason": "Validation scripts", "size": 8716, "action": "removed"}, {"path": "signal_flow_diagnostic.py", "reason": "Diagnostic scripts", "size": 15597, "action": "removed"}, {"path": "signal_flow_fix_validation.py", "reason": "Validation scripts", "size": 14592, "action": "removed"}, {"path": "test_agent_zero_live.py", "reason": "Test scripts", "size": 7246, "action": "removed"}, {"path": "test_core_system.py", "reason": "Test scripts", "size": 2095, "action": "removed"}, {"path": "test_greek_features.py", "reason": "Test scripts", "size": 6629, "action": "removed"}, {"path": "test_ticker_agnostic_system.py", "reason": "Test scripts", "size": 9629, "action": "removed"}, {"path": "test_training_log_access.py", "reason": "Test scripts", "size": 6357, "action": "removed"}, {"path": "ticker_agnostic_system_fix.py", "reason": "System fix scripts", "size": 16814, "action": "removed"}, {"path": "unicode_removal_and_agent_zero_maximization.py", "reason": "Unicode cleanup scripts", "size": 36199, "action": "removed"}, {"path": "api\\mcp_server_validation.py", "reason": "Validation scripts", "size": 11389, "action": "removed"}, {"path": "api\\quick_test.py", "reason": "Quick test scripts", "size": 1817, "action": "removed"}, {"path": "api\\_archive\\mcp_server_production.py", "reason": "Archived file", "size": 77086, "action": "removed"}, {"path": "api\\_archive\\mcp_server_simple.py", "reason": "Archived file", "size": 9421, "action": "removed"}, {"path": "api\\_archive\\migrate_to_schwab_mcp.py", "reason": "Archived file", "size": 12194, "action": "removed"}, {"path": "api\\_archive\\refresh_schwab_token.py", "reason": "Archived file", "size": 3061, "action": "removed"}, {"path": "api\\_archive\\schwab_mcp_server.py", "reason": "Archived file", "size": 21064, "action": "removed"}, {"path": "api\\_archive\\setup_schwab_mcp.py", "reason": "Archived file", "size": 6489, "action": "removed"}, {"path": "api\\_archive\\START_SCHWAB_MCP.py", "reason": "Archived file", "size": 6909, "action": "removed"}, {"path": "api\\_archive\\test_schwab_mcp_comprehensive.py", "reason": "Archived file", "size": 20037, "action": "removed"}, {"path": "archive\\debug_files\\debug_b01_options.py", "reason": "Archived file", "size": 2737, "action": "removed"}, {"path": "archive\\debug_files\\debug_flow_physics_resolved.py", "reason": "Archived file", "size": 1363, "action": "removed"}, {"path": "archive\\debug_files\\debug_mcp.py", "reason": "Archived file", "size": 3722, "action": "removed"}, {"path": "archive\\debug_files\\debug_options.py", "reason": "Archived file", "size": 1302, "action": "removed"}, {"path": "archive\\documentation_archive\\analyze_system_usage.py", "reason": "Archived file", "size": 7674, "action": "removed"}, {"path": "archive\\documentation_archive\\cleanup_test_files.py", "reason": "Archived file", "size": 4402, "action": "removed"}, {"path": "archive\\documentation_archive\\enhanced_accumulation_distribution_agent_zero.py", "reason": "Archived file", "size": 20371, "action": "removed"}, {"path": "archive\\documentation_archive\\final_cleanup.py", "reason": "Archived file", "size": 4711, "action": "removed"}, {"path": "archive\\documentation_archive\\final_system_validation.py", "reason": "Archived file", "size": 9966, "action": "removed"}, {"path": "archive\\documentation_archive\\system_cleanup.py", "reason": "Archived file", "size": 9916, "action": "removed"}, {"path": "archive\\documentation_archive\\ultimate_cleanup.py", "reason": "Archived file", "size": 7187, "action": "removed"}, {"path": "archive\\final_cleanup\\advanced_market_intelligence.py", "reason": "Archived file", "size": 34206, "action": "removed"}, {"path": "archive\\final_cleanup\\agent_zero_integration.py", "reason": "Archived file", "size": 10864, "action": "removed"}, {"path": "archive\\final_cleanup\\bsm_model.py", "reason": "Archived file", "size": 5661, "action": "removed"}, {"path": "archive\\final_cleanup\\dashboard.py", "reason": "Archived file", "size": 10415, "action": "removed"}, {"path": "archive\\final_cleanup\\dynamic_threshold_engine.py", "reason": "Archived file", "size": 16357, "action": "removed"}, {"path": "archive\\final_cleanup\\ml_enhanced_feature_engine.py", "reason": "Archived file", "size": 42110, "action": "removed"}, {"path": "archive\\final_cleanup\\ml_ensemble_engine.py", "reason": "Archived file", "size": 6808, "action": "removed"}, {"path": "archive\\final_cleanup\\orchestrator.py", "reason": "Archived file", "size": 6776, "action": "removed"}, {"path": "archive\\final_cleanup\\trading_system_pipeline_integration.py", "reason": "Archived file", "size": 29318, "action": "removed"}, {"path": "archive\\final_cleanup\\trading_system_schwab_mcp_integration.py", "reason": "Archived file", "size": 25897, "action": "removed"}, {"path": "archive\\misc_scripts\\create_ci_snapshot.py", "reason": "Archived file", "size": 7765, "action": "removed"}, {"path": "archive\\misc_scripts\\demo_live_testing.py", "reason": "Archived file", "size": 6909, "action": "removed"}, {"path": "archive\\misc_scripts\\deployment_checklist.py", "reason": "Archived file", "size": 9051, "action": "removed"}, {"path": "archive\\misc_scripts\\deploy_enhanced_agent.py", "reason": "Archived file", "size": 10505, "action": "removed"}, {"path": "archive\\misc_scripts\\discover_local_mcp.py", "reason": "Archived file", "size": 5973, "action": "removed"}, {"path": "archive\\misc_scripts\\evaluate_special_agent.py", "reason": "Archived file", "size": 15916, "action": "removed"}, {"path": "archive\\misc_scripts\\final_b01_validation.py", "reason": "Archived file", "size": 3379, "action": "removed"}, {"path": "archive\\misc_scripts\\final_validation_test.py", "reason": "Archived file", "size": 10741, "action": "removed"}, {"path": "archive\\misc_scripts\\generate_evaluation_report.py", "reason": "Archived file", "size": 11199, "action": "removed"}, {"path": "archive\\misc_scripts\\iv_roc_demo.py", "reason": "Archived file", "size": 1180, "action": "removed"}, {"path": "archive\\misc_scripts\\launch_live_testing.py", "reason": "Archived file", "size": 6350, "action": "removed"}, {"path": "archive\\misc_scripts\\live_checklist_dashboard.py", "reason": "Archived file", "size": 10060, "action": "removed"}, {"path": "archive\\misc_scripts\\live_testing_dashboard.py", "reason": "Archived file", "size": 10704, "action": "removed"}, {"path": "archive\\misc_scripts\\mcp_system_diagnostics.py", "reason": "Archived file", "size": 13393, "action": "removed"}, {"path": "archive\\misc_scripts\\quick_deploy.py", "reason": "Archived file", "size": 3207, "action": "removed"}, {"path": "archive\\misc_scripts\\quick_mcp_test.py", "reason": "Archived file", "size": 2446, "action": "removed"}, {"path": "archive\\misc_scripts\\run_live_testing_day.py", "reason": "Archived file", "size": 11734, "action": "removed"}, {"path": "archive\\misc_scripts\\security_check.py", "reason": "Archived file", "size": 5563, "action": "removed"}, {"path": "archive\\misc_scripts\\security_sweep.py", "reason": "Archived file", "size": 9633, "action": "removed"}, {"path": "archive\\misc_scripts\\setup_pre_onboarding.py", "reason": "Archived file", "size": 3356, "action": "removed"}, {"path": "archive\\misc_scripts\\updated_evaluation.py", "reason": "Archived file", "size": 24123, "action": "removed"}, {"path": "archive\\misc_scripts\\update_agent_zero_integration.py", "reason": "Archived file", "size": 11399, "action": "removed"}, {"path": "archive\\misc_scripts\\validate_100_percent_integration.py", "reason": "Archived file", "size": 3199, "action": "removed"}, {"path": "archive\\misc_scripts\\validate_agent_zero_backtesting.py", "reason": "Archived file", "size": 9732, "action": "removed"}, {"path": "archive\\misc_scripts\\validate_agent_zero_integration.py", "reason": "Archived file", "size": 3417, "action": "removed"}, {"path": "archive\\misc_scripts\\validate_b01_implementation.py", "reason": "Archived file", "size": 6773, "action": "removed"}, {"path": "archive\\misc_scripts\\validate_b_series.py", "reason": "Archived file", "size": 4340, "action": "removed"}, {"path": "archive\\misc_scripts\\validate_for_next_agent.py", "reason": "Archived file", "size": 3993, "action": "removed"}, {"path": "archive\\misc_scripts\\verify_output_coordinator.py", "reason": "Archived file", "size": 5599, "action": "removed"}, {"path": "archive\\old_tests\\test_agent_army_live.py", "reason": "Archived file", "size": 9232, "action": "removed"}, {"path": "archive\\old_tests\\test_agent_framework.py", "reason": "Archived file", "size": 4714, "action": "removed"}, {"path": "archive\\old_tests\\test_anomaly_detection.py", "reason": "Archived file", "size": 12212, "action": "removed"}, {"path": "archive\\old_tests\\test_b01_integration.py", "reason": "Archived file", "size": 4439, "action": "removed"}, {"path": "archive\\old_tests\\test_b01_ticker_agnostic.py", "reason": "Archived file", "size": 7448, "action": "removed"}, {"path": "archive\\old_tests\\test_bounds_fix.py", "reason": "Archived file", "size": 1499, "action": "removed"}, {"path": "archive\\old_tests\\test_broker_integration.py", "reason": "Archived file", "size": 5463, "action": "removed"}, {"path": "archive\\old_tests\\test_chart_generator.py", "reason": "Archived file", "size": 8997, "action": "removed"}, {"path": "archive\\old_tests\\test_comprehensive_mcp_integration.py", "reason": "Archived file", "size": 11991, "action": "removed"}, {"path": "archive\\old_tests\\test_enhanced_agent.py", "reason": "Archived file", "size": 32587, "action": "removed"}, {"path": "archive\\old_tests\\test_enhanced_greeks_architecture.py", "reason": "Archived file", "size": 9931, "action": "removed"}, {"path": "archive\\old_tests\\test_final_integration.py", "reason": "Archived file", "size": 4883, "action": "removed"}, {"path": "archive\\old_tests\\test_integrated_system.py", "reason": "Archived file", "size": 4824, "action": "removed"}, {"path": "archive\\old_tests\\test_iv_dynamics.py", "reason": "Archived file", "size": 14652, "action": "removed"}, {"path": "archive\\old_tests\\test_live_mcp_integration.py", "reason": "Archived file", "size": 5621, "action": "removed"}, {"path": "archive\\old_tests\\test_math_validator.py", "reason": "Archived file", "size": 12538, "action": "removed"}, {"path": "archive\\old_tests\\test_mcp_agent_integration.py", "reason": "Archived file", "size": 31652, "action": "removed"}, {"path": "archive\\old_tests\\test_mcp_connection.py", "reason": "Archived file", "size": 6633, "action": "removed"}, {"path": "archive\\old_tests\\test_mcp_correct_port.py", "reason": "Archived file", "size": 4369, "action": "removed"}, {"path": "archive\\old_tests\\test_mcp_final.py", "reason": "Archived file", "size": 5413, "action": "removed"}, {"path": "archive\\old_tests\\test_mcp_integration.py", "reason": "Archived file", "size": 7772, "action": "removed"}, {"path": "archive\\old_tests\\test_mcp_wire.py", "reason": "Archived file", "size": 1504, "action": "removed"}, {"path": "archive\\old_tests\\test_multi_orchestrator.py", "reason": "Archived file", "size": 1684, "action": "removed"}, {"path": "archive\\old_tests\\test_original_agent_fix.py", "reason": "Archived file", "size": 1995, "action": "removed"}, {"path": "archive\\old_tests\\test_phase15_enhancements.py", "reason": "Archived file", "size": 9349, "action": "removed"}, {"path": "archive\\old_tests\\test_protocol_compliance.py", "reason": "Archived file", "size": 6843, "action": "removed"}, {"path": "archive\\old_tests\\test_protocol_simple.py", "reason": "Archived file", "size": 3846, "action": "removed"}, {"path": "archive\\old_tests\\test_schwab_options.py", "reason": "Archived file", "size": 19180, "action": "removed"}, {"path": "archive\\old_tests\\test_schwab_production.py", "reason": "Archived file", "size": 5768, "action": "removed"}, {"path": "archive\\old_tests\\test_schwab_validation.py", "reason": "Archived file", "size": 3884, "action": "removed"}, {"path": "archive\\old_tests\\test_signal_generator.py", "reason": "Archived file", "size": 1772, "action": "removed"}, {"path": "archive\\old_tests\\test_signal_quality.py", "reason": "Archived file", "size": 21996, "action": "removed"}, {"path": "archive\\old_tests\\test_simple_integration.py", "reason": "Archived file", "size": 2218, "action": "removed"}, {"path": "archive\\old_tests\\test_system.py", "reason": "Archived file", "size": 1271, "action": "removed"}, {"path": "archive\\old_tests\\test_trading_math.py", "reason": "Archived file", "size": 9230, "action": "removed"}, {"path": "archive\\old_tests\\test_trading_system_agents.py", "reason": "Archived file", "size": 20248, "action": "removed"}, {"path": "archive\\old_tests\\test_trading_system_complete.py", "reason": "Archived file", "size": 19059, "action": "removed"}, {"path": "archive\\old_tests\\test_trading_system_quick.py", "reason": "Archived file", "size": 6083, "action": "removed"}, {"path": "archive\\old_tests\\test_yaml_loader.py", "reason": "Archived file", "size": 830, "action": "removed"}, {"path": "archive\\old_tests\\api\\mcp_test_client.py", "reason": "Archived file", "size": 2999, "action": "removed"}, {"path": "archive\\old_tests\\api\\test_interactive.py", "reason": "Archived file", "size": 3952, "action": "removed"}, {"path": "archive\\old_tests\\api\\test_mcp_comprehensive.py", "reason": "Archived file", "size": 3594, "action": "removed"}, {"path": "archive\\old_tests\\api\\test_real_api.py", "reason": "Archived file", "size": 5220, "action": "removed"}, {"path": "archive\\old_tests\\testing\\temp\\mcp_error_debug.py", "reason": "Archived file", "size": 996, "action": "removed"}, {"path": "archive\\old_tests\\testing\\temp\\quick_mcp_test.py", "reason": "Archived file", "size": 2187, "action": "removed"}, {"path": "archive\\old_tests\\testing\\temp\\test_current_token.py", "reason": "Archived file", "size": 773, "action": "removed"}, {"path": "archive\\old_tests\\testing\\temp\\test_enhanced_direct.py", "reason": "Archived file", "size": 1367, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_auto_broker.py", "reason": "Archived file", "size": 8585, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_backtest_stub.py", "reason": "Archived file", "size": 5741, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_data_ingestion.py", "reason": "Archived file", "size": 7104, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_enhanced_csid_agent.py", "reason": "Archived file", "size": 15365, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_flow_physics_agent.py", "reason": "Archived file", "size": 17446, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_greeks_integration.py", "reason": "Archived file", "size": 2668, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_greek_enhancement_agent.py", "reason": "Archived file", "size": 39891, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_integration_pipeline.py", "reason": "Archived file", "size": 14529, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_order_router.py", "reason": "Archived file", "size": 4346, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_order_router_v2.py", "reason": "Archived file", "size": 4692, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_output_coordinator.py", "reason": "Archived file", "size": 6262, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_risk_guard.py", "reason": "Archived file", "size": 4735, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_schwab_chain_parser.py", "reason": "Archived file", "size": 8405, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_schwab_data_agent.py", "reason": "Archived file", "size": 8834, "action": "removed"}, {"path": "archive\\old_tests\\tests\\test_specialized_agent_army.py", "reason": "Archived file", "size": 18060, "action": "removed"}, {"path": "archive\\old_versions\\agent_zero_backtester.py", "reason": "Archived file", "size": 28766, "action": "removed"}, {"path": "archive\\old_versions\\agent_zero_enhanced_backtester.py", "reason": "Archived file", "size": 18809, "action": "removed"}, {"path": "archive\\old_versions\\enhanced_orchestrator.py", "reason": "Archived file", "size": 8377, "action": "removed"}, {"path": "archive\\old_versions\\enhanced_orchestrator_iv.py", "reason": "Archived file", "size": 11859, "action": "removed"}, {"path": "archive\\old_versions\\fixed_accumulation_distribution_agent.py", "reason": "Archived file", "size": 18296, "action": "removed"}, {"path": "archive\\old_versions\\multi_orchestrator.py", "reason": "Archived file", "size": 4335, "action": "removed"}, {"path": "archive\\old_versions\\orchestrator_example.py", "reason": "Archived file", "size": 4079, "action": "removed"}, {"path": "archive\\old_versions\\production_accumulation_distribution.py", "reason": "Archived file", "size": 9436, "action": "removed"}, {"path": "ci\\test_circuit_breaker.py", "reason": "Test scripts", "size": 12491, "action": "removed"}, {"path": "ci\\test_circuit_breaker_simple.py", "reason": "Test scripts", "size": 6881, "action": "removed"}, {"path": "ci\\test_dashboard_render.py", "reason": "Test scripts", "size": 12977, "action": "removed"}, {"path": "ci\\test_performance_sub10ms.py", "reason": "Test scripts", "size": 8272, "action": "removed"}]}