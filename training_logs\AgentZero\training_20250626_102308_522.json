{"timestamp": "2025-06-26T10:23:08.522797", "signal_data": {"confidence": 0.5, "strength": 0.14, "execution_recommendation": "hold"}, "math_data": {"accuracy_score": 0.9008, "precision": 0.003515}, "decision": {"timestamp": "2025-06-26T10:23:08.522758", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.02, "action": "hold", "confidence": 0.42363500000000004, "reasoning": ["Rule-based decision: composite score 0.424", "Signal confidence: 0.500", "Signal strength: 0.140", "Execution recommendation: hold", "Math accuracy: 0.901", "ML system not available - using rule-based logic"], "composite_score": 0.42363500000000004, "decision_method": "rule_based", "ml_enhanced": false}, "outcome": 0.0, "market_context": {"b_series_analysis": {"features": {}, "confidence": 0.6, "pattern_strength": 0.5}, "flow_analysis": {"momentum": 0.0, "direction": "neutral", "strength": 0.5}, "anomaly_analysis": {"anomaly_detected": false, "anomaly_score": 0.0}, "iv_dynamics_analysis": {"iv_rank": 50.0, "iv_expansion": false, "volatility_regime": "normal"}, "market_regime": {"trend": "sideways", "volatility": "medium"}, "analysis_source": "test_system", "timestamp": "2025-06-26T10:23:08.513424", "ticker": "SPY"}, "agent_version": "1.5_Integrated"}