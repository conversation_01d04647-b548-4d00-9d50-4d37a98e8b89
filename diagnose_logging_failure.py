#!/usr/bin/env python3
"""
Shadow Mode Logging Diagnostic
==============================
Identifies why logging failed and provides surgical fixes
"""

import os
import sys
import json
from pathlib import Path
from datetime import datetime

def diagnose_logging_failure():
    """Surgical diagnosis of logging failure points"""
    
    print("SHADOW MODE LOGGING DIAGNOSTIC")
    print("=" * 50)
    
    # Check 1: Directory permissions and existence
    core_root = Path(__file__).parent
    training_dir = core_root / "training_logs" / "AgentZero"
    
    print("1. DIRECTORY STRUCTURE:")
    print(f"   Core root: {core_root}")
    print(f"   Training dir: {training_dir}")
    print(f"   Exists: {training_dir.exists()}")
    
    if not training_dir.exists():
        print("   CREATING training directory...")
        training_dir.mkdir(parents=True, exist_ok=True)
        print(f"   Created: {training_dir.exists()}")
    
    # Check 2: Write permissions
    print("\n2. WRITE PERMISSIONS TEST:")
    test_file = training_dir / "test_write.json"
    try:
        with open(test_file, 'w') as f:
            json.dump({"test": "write_permission"}, f)
        print("   PASS: Can write to training directory")
        test_file.unlink()  # Clean up
    except Exception as e:
        print(f"   FAIL: Cannot write to directory - {e}")
        return False
    
    # Check 3: Agent Zero initialization
    print("\n3. AGENT ZERO INITIALIZATION:")
    try:
        sys.path.insert(0, str(core_root))
        from agents.agent_zero import AgentZeroAdvisor
        
        agent = AgentZeroAdvisor()
        print(f"   PASS: Agent Zero initialized")
        print(f"   Training dir: {agent.training_dir}")
        print(f"   Directory exists: {agent.training_dir.exists()}")
        
    except Exception as e:
        print(f"   FAIL: Agent Zero initialization error - {e}")
        return False
    
    # Check 4: Manual logging test
    print("\n4. MANUAL LOGGING TEST:")
    try:
        test_signal = {'confidence': 0.8, 'strength': 0.7}
        test_math = {'accuracy_score': 0.9, 'precision': 0.001}
        test_decision = {'action': 'execute', 'confidence': 0.8}
        test_outcome = 0.15
        
        agent.log_training_data(test_signal, test_math, test_decision, test_outcome)
        
        # Check if file was created
        recent_files = list(training_dir.glob("training_*.json"))
        if recent_files:
            latest_file = max(recent_files, key=lambda f: f.stat().st_mtime)
            print(f"   PASS: Log created - {latest_file.name}")
            return True
        else:
            print("   FAIL: No log file created despite no errors")
            return False
            
    except Exception as e:
        print(f"   FAIL: Manual logging failed - {e}")
        return False

def identify_common_failure_points():
    """Identify and fix common logging failure scenarios"""
    
    print("\n" + "=" * 50)
    print("COMMON FAILURE POINT ANALYSIS:")
    print("=" * 50)
    
    # Failure Point 1: log_training_data never called
    print("\n1. CALLING MECHANISM:")
    print("   Root cause: log_training_data() method not being invoked")
    print("   Fix: Ensure shadow mode execution calls logging after decisions")
    
    # Failure Point 2: Directory creation failure
    print("\n2. DIRECTORY CREATION:")
    print("   Root cause: training_logs/AgentZero directory doesn't exist")
    print("   Fix: Create directory structure with proper permissions")
    
    # Failure Point 3: Silent failures in exception handling
    print("\n3. SILENT FAILURES:")
    print("   Root cause: Exception caught but not visible")
    print("   Fix: Enable INFO/DEBUG logging to see actual errors")
    
    # Failure Point 4: Import path issues
    print("\n4. IMPORT FAILURES:")
    print("   Root cause: Agent Zero not properly imported in execution")
    print("   Fix: Verify sys.path includes CORE directory")
    
    print("\n" + "=" * 50)
    print("SURGICAL FIXES REQUIRED:")
    print("=" * 50)
    print("A. Add explicit logging calls in main execution loop")
    print("B. Enable DEBUG logging to catch silent failures") 
    print("C. Add logging validation at script startup")
    print("D. Create logging wrapper to guarantee data capture")

def create_logging_wrapper():
    """Create guaranteed logging wrapper"""
    
    wrapper_content = '''#!/usr/bin/env python3
"""
Guaranteed Logging Wrapper
===========================
Ensures shadow mode data is captured even if main logging fails
"""

import json
import os
from datetime import datetime
from pathlib import Path

class GuaranteedLogger:
    """Failsafe logging that cannot be silently ignored"""
    
    def __init__(self):
        self.core_root = Path(__file__).parent.parent
        self.backup_dir = self.core_root / "training_logs" / "AgentZero" / "backup"
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
    def force_log(self, signal_data, math_data, decision, outcome):
        """Force log data with multiple fallback mechanisms"""
        
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        
        # Primary attempt
        try:
            from agents.agent_zero import AgentZeroAdvisor
            agent = AgentZeroAdvisor()
            agent.log_training_data(signal_data, math_data, decision, outcome)
            print(f"PRIMARY LOGGING: SUCCESS - {timestamp_str}")
            return True
        except Exception as e:
            print(f"PRIMARY LOGGING: FAILED - {e}")
        
        # Backup attempt - direct file write
        try:
            backup_file = self.backup_dir / f"backup_training_{timestamp_str}.json"
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'signal_data': signal_data,
                'math_data': math_data,
                'decision': decision,
                'outcome': outcome,
                'backup_reason': 'primary_logging_failed'
            }
            
            with open(backup_file, 'w') as f:
                json.dump(backup_data, f, indent=2, default=str)
            
            print(f"BACKUP LOGGING: SUCCESS - {backup_file.name}")
            return True
            
        except Exception as e:
            print(f"BACKUP LOGGING: FAILED - {e}")
            
        # Emergency attempt - print to console
        print(f"EMERGENCY LOG: {json.dumps({
            'signal': signal_data, 'math': math_data, 
            'decision': decision, 'outcome': outcome
        }, default=str)}")
        
        return False

# Global instance
_guaranteed_logger = GuaranteedLogger()

def guaranteed_log(signal_data, math_data, decision, outcome):
    """Global function for guaranteed logging"""
    return _guaranteed_logger.force_log(signal_data, math_data, decision, outcome)
'''
    
    wrapper_file = Path(__file__).parent / "guaranteed_logger.py"
    with open(wrapper_file, 'w') as f:
        f.write(wrapper_content)
    
    print(f"\n✅ CREATED: {wrapper_file}")
    print("Usage: from guaranteed_logger import guaranteed_log")

if __name__ == "__main__":
    success = diagnose_logging_failure()
    identify_common_failure_points()
    create_logging_wrapper()
    
    if success:
        print(f"\n✅ DIAGNOSTIC: LOGGING MECHANISM OPERATIONAL")
    else:
        print(f"\n❌ DIAGNOSTIC: LOGGING FAILURES IDENTIFIED - USE SURGICAL FIXES")
