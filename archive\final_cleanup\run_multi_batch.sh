#!/bin/bash
# Multi-ticker CORE execution script for Unix/Linux
# Sets environment and executes batch analysis

echo "========================================"
echo "CORE Multi-Ticker Pipeline Execution"
echo "========================================"

# Set environment variables
export ACCOUNT_EQUITY=25000
export PYTHONPATH=$PWD

# Validate required files exist
if [ ! -f "orchestrator.py" ]; then
    echo "ERROR: orchestrator.py not found"
    exit 1
fi

if [ ! -f "multi_orchestrator.py" ]; then
    echo "ERROR: multi_orchestrator.py not found"
    exit 1
fi

# Execute multi-ticker batch
echo "Executing multi-ticker analysis..."
python multi_orchestrator.py \
    --tickers AAPL,TSLA,NVDA \
    --option_prices 1.50,2.10,3.25 \
    --target_prices 5.00,6.00,10.40 \
    --source mcp-http \
    --account_equity $ACCOUNT_EQUITY

if [ $? -eq 0 ]; then
    echo "SUCCESS: Multi-ticker execution completed"
else
    echo "FAILED: Multi-ticker execution failed with code $?"
fi

echo "========================================"
echo "Execution finished. Check outputs/ directory for results."
echo "========================================"
