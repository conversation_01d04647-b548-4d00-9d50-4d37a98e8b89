#!/usr/bin/env python3
"""
Desktop Commander MCP Server Integration
Allows starting/stopping/testing the MCP server through Desktop Commander
"""

import sys
import os
import subprocess
import json
import time
from pathlib import Path

# MCP Server paths
MCP_DIR = r"D:\script-work\Liquidity_Sweep\api_robustness"
CONFIG_FILE = os.path.join(MCP_DIR, "mcp_installation", "config", "config.json")
SERVER_SCRIPT = os.path.join(MCP_DIR, "mcp_server_production.py")

def start_mcp_server():
    """Start the MCP server."""
    print("Starting Liquidity Sweep MCP Server...")
    print(f"Directory: {MCP_DIR}")
    print(f"Config: {CONFIG_FILE}")
    
    if not os.path.exists(CONFIG_FILE):
        print(f"ERROR: Configuration file not found: {CONFIG_FILE}")
        return 1
    
    if not os.path.exists(SERVER_SCRIPT):
        print(f"ERROR: Server script not found: {SERVER_SCRIPT}")
        return 1
    
    try:
        # Change to MCP directory
        os.chdir(MCP_DIR)
        
        # Start the server
        cmd = ["py", "mcp_server_production.py", "--config", CONFIG_FILE, "--log-level", "INFO"]
        print(f"Running: {' '.join(cmd)}")
        print("Press Ctrl+C to stop the server")
        print("-" * 50)
        
        subprocess.run(cmd, check=True)
        
    except KeyboardInterrupt:
        print("\nMCP Server stopped by user")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"Server error: {e}")
        return 1
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1

def test_mcp_server():
    """Test MCP server functionality."""
    print("Testing MCP Server...")
    
    try:
        os.chdir(MCP_DIR)
        result = subprocess.run(
            ["py", "quick_test.py"],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print("Test Results:")
        print("-" * 30)
        print(result.stdout)
        
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("SUCCESS: All tests passed!")
        else:
            print("ERROR: Some tests failed!")
        
        return result.returncode
        
    except subprocess.TimeoutExpired:
        print("ERROR: Test timed out after 60 seconds")
        return 1
    except Exception as e:
        print(f"Test error: {e}")
        return 1

def check_mcp_status():
    """Check MCP server configuration and status."""
    print("MCP Server Status Check")
    print("=" * 40)
    
    # Check files
    files_to_check = [
        ("Server Script", SERVER_SCRIPT),
        ("Configuration", CONFIG_FILE),
        ("Quick Test", os.path.join(MCP_DIR, "quick_test.py")),
        ("Startup Script", os.path.join(MCP_DIR, "SIMPLE_START.bat"))
    ]
    
    for name, path in files_to_check:
        if os.path.exists(path):
            print(f"SUCCESS: {name}: Found")
        else:
            print(f"ERROR: {name}: Missing - {path}")
    
    # Check configuration
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
            
            api_key = config.get('polygon', {}).get('api_key', '')
            if api_key and len(api_key) > 10:
                print(f"SUCCESS: API Key: Configured ({api_key[:8]}...)")
            else:
                print("ERROR: API Key: Not configured or too short")
                
            tier = config.get('polygon', {}).get('tier', 'unknown')
            print(f"INFO: API Tier: {tier}")
            
        except Exception as e:
            print(f"ERROR: Config Error: {e}")
    
    print("\nMCP Server ready for use!")

def show_help():
    """Show help information."""
    help_text = """
MCP Server Desktop Commander Integration

Available Commands:
  python mcp_dc.py start     - Start the MCP server
  python mcp_dc.py test      - Run functionality tests  
  python mcp_dc.py status    - Check server status
  python mcp_dc.py help      - Show this help

MCP Server Tools Available:
   get_spot_price          - Real-time stock prices
   get_options_chain        - Options data with Greeks
   get_pcr_ratio           - Put/Call ratio analysis
   analyze_liquidity       - Liquidity scoring
   health_check            - System health monitoring
   get_metrics             - Performance statistics

Example MCP Usage:
  Input:  {"jsonrpc": "2.0", "id": 1, "method": "get_spot_price", "params": {"ticker": "AAPL"}}
  Output: {"jsonrpc": "2.0", "id": 1, "result": {"status": "success", "price": 199.20}}

For AI Agents:
  The MCP server uses standard JSON-RPC 2.0 protocol over stdin/stdout.
  Perfect for Claude, GPT, and other AI systems that support MCP.
"""
    print(help_text)

def main():
    """Main entry point."""
    if len(sys.argv) < 2:
        command = "help"
    else:
        command = sys.argv[1].lower()
    
    if command == "start":
        return start_mcp_server()
    elif command == "test":
        return test_mcp_server()
    elif command == "status":
        check_mcp_status()
        return 0
    elif command == "help":
        show_help()
        return 0
    else:
        print(f" Unknown command: {command}")
        print("Available commands: start, test, status, help")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
