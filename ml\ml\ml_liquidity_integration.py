"""
ML Liquidity Integration Module

This module integrates machine learning components with traditional
liquidity analysis, providing a unified interface for enhanced liquidity analysis.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import time
import os
from datetime import datetime

# Internal imports
from ml_logging import get_logger
from liquidity_features import LiquidityFeatureExtractor
from liquidity_prediction import LiquidityPredictionEngine

# Setup logger
logger = get_logger('ml_liquidity_integration')

class MLLiquidityIntegration:
    """Integrates ML with traditional liquidity analysis."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the ML liquidity integration.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.feature_extractor = LiquidityFeatureExtractor(config)
        self.prediction_engine = LiquidityPredictionEngine(config)
        self.initialized = False

        # Performance tracking
        self.performance_data = {}

        logger.info("Initialized MLLiquidityIntegration")

    def initialize(self) -> bool:
        """
        Initialize the integration module.

        Returns:
            True if initialization was successful, False otherwise
        """
        if self.initialized:
            logger.info("ML liquidity integration already initialized")
            return True

        try:
            # Initialize prediction engine
            if not self.prediction_engine.initialize():
                logger.error("Failed to initialize prediction engine")
                return False

            self.initialized = True
            logger.info("ML liquidity integration initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize ML liquidity integration: {str(e)}")
            return False

    def enhance_liquidity_analysis(self,
                                  price_data: pd.DataFrame,
                                  liquidity_levels: Dict[str, List[Dict[str, Any]]],
                                  options_data: Optional[pd.DataFrame] = None,
                                  volume_profile: Optional[Dict[str, Any]] = None,
                                  gex_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Enhance liquidity analysis with ML predictions.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data

        Returns:
            Dictionary with enhanced liquidity analysis
        """
        start_time = time.time()

        # Ensure integration is initialized
        if not self.initialized:
            self.initialize()

        # Create result dictionary
        result = {
            'original_levels': liquidity_levels,
            'enhanced_levels': None,
            'price_reactions': None,
            'anomalies': [],
            'confidence': 0.0,
            'processing_time': 0.0
        }

        try:
            # 1. Enhance level strength
            level_strength_result = self.prediction_engine.predict_level_strength(
                price_data=price_data,
                liquidity_levels=liquidity_levels,
                options_data=options_data,
                volume_profile=volume_profile,
                gex_data=gex_data
            )

            result['enhanced_levels'] = level_strength_result.get('enhanced_levels', liquidity_levels)
            level_confidence = level_strength_result.get('confidence', 0.0)

            # 2. Predict price reactions
            price_reaction_result = self.prediction_engine.predict_price_reaction(
                price_data=price_data,
                liquidity_levels=result['enhanced_levels'],
                options_data=options_data,
                volume_profile=volume_profile,
                gex_data=gex_data
            )

            result['price_reactions'] = price_reaction_result.get('reactions', {})
            reaction_confidence = price_reaction_result.get('confidence', 0.0)

            # 3. Detect anomalies
            anomalies = self._detect_anomalies(
                price_data=price_data,
                liquidity_levels=result['enhanced_levels'],
                price_reactions=result['price_reactions'],
                options_data=options_data,
                volume_profile=volume_profile,
                gex_data=gex_data
            )

            result['anomalies'] = anomalies

            # 4. Calculate overall confidence
            result['confidence'] = (level_confidence + reaction_confidence) / 2

            # 5. Add processing time
            elapsed = time.time() - start_time
            result['processing_time'] = elapsed

            logger.info(f"Enhanced liquidity analysis completed in {elapsed:.2f} seconds")
            return result

        except Exception as e:
            logger.error(f"Error in ML liquidity enhancement: {str(e)}")

            # Return original levels on error
            result['enhanced_levels'] = liquidity_levels
            result['processing_time'] = time.time() - start_time
            return result

    def _detect_anomalies(self,
                         price_data: pd.DataFrame,
                         liquidity_levels: Dict[str, List[Dict[str, Any]]],
                         price_reactions: Dict[str, List[Dict[str, Any]]],
                         options_data: Optional[pd.DataFrame] = None,
                         volume_profile: Optional[Dict[str, Any]] = None,
                         gex_data: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Detect anomalies in liquidity analysis.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            price_reactions: Dictionary with price reaction predictions
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data

        Returns:
            List of detected anomalies
        """
        anomalies = []

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # 1. Check for conflicting signals
        # Example: Strong support level with high break probability
        for level in price_reactions.get('support', []):
            if level.get('strength', 0) > 0.7 and level.get('break_probability', 0) > 0.7:
                anomalies.append({
                    'type': 'conflicting_signal',
                    'subtype': 'support_break',
                    'price': level.get('price', 0),
                    'description': f"Strong support at {level.get('price', 0):.2f} with high break probability",
                    'severity': 'medium'
                })

        for level in price_reactions.get('resistance', []):
            if level.get('strength', 0) > 0.7 and level.get('break_probability', 0) > 0.7:
                anomalies.append({
                    'type': 'conflicting_signal',
                    'subtype': 'resistance_break',
                    'price': level.get('price', 0),
                    'description': f"Strong resistance at {level.get('price', 0):.2f} with high break probability",
                    'severity': 'medium'
                })

        # 2. Check for unusual level clustering
        # Example: Multiple strong levels very close to each other
        support_levels = liquidity_levels.get('support', [])
        resistance_levels = liquidity_levels.get('resistance', [])

        if len(support_levels) >= 2:
            for i in range(len(support_levels) - 1):
                price1 = support_levels[i].get('price', 0)
                price2 = support_levels[i + 1].get('price', 0)

                # If levels are within 0.5% of each other and both are strong
                if (abs(price1 - price2) / current_price < 0.005 and
                    support_levels[i].get('strength', 0) > 0.6 and
                    support_levels[i + 1].get('strength', 0) > 0.6):

                    anomalies.append({
                        'type': 'level_clustering',
                        'subtype': 'support_cluster',
                        'price': (price1 + price2) / 2,
                        'description': f"Unusual clustering of strong support levels near {(price1 + price2) / 2:.2f}",
                        'severity': 'low'
                    })

        # 3. Check for unusual GEX patterns if GEX data is available
        if gex_data and 'gex_at_current' in gex_data:
            gex_value = gex_data.get('gex_at_current', 0)

            # Example: Very high GEX value
            if abs(gex_value) > 1e8:  # Arbitrary threshold
                anomalies.append({
                    'type': 'unusual_gex',
                    'subtype': 'extreme_value',
                    'value': gex_value,
                    'description': f"Extremely {'high positive' if gex_value > 0 else 'high negative'} GEX value",
                    'severity': 'high'
                })

        # 4. Check for unusual options patterns if options data is available
        if options_data is not None and not options_data.empty:
            if 'call_oi' in options_data.columns and 'put_oi' in options_data.columns:
                total_call_oi = options_data['call_oi'].sum()
                total_put_oi = options_data['put_oi'].sum()

                # Example: Extremely skewed put/call ratio
                if total_call_oi > 0 and total_put_oi / total_call_oi > 5:
                    anomalies.append({
                        'type': 'unusual_options',
                        'subtype': 'high_put_call_ratio',
                        'value': total_put_oi / total_call_oi,
                        'description': f"Extremely high put/call ratio: {total_put_oi / total_call_oi:.2f}",
                        'severity': 'medium'
                    })

        return anomalies

    def get_hybrid_liquidity_score(self,
                                  price_data: pd.DataFrame,
                                  liquidity_levels: Dict[str, List[Dict[str, Any]]],
                                  price_reactions: Dict[str, List[Dict[str, Any]]],
                                  anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Calculate a hybrid liquidity score combining traditional and ML analysis.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            price_reactions: Dictionary with price reaction predictions
            anomalies: List of detected anomalies

        Returns:
            Dictionary with hybrid liquidity scores
        """
        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # 1. Calculate support score
        support_levels = liquidity_levels.get('support', [])
        support_score = 0.0

        if support_levels:
            # Find nearest support level
            nearest_support = min(support_levels, key=lambda x: abs(x.get('price', 0) - current_price))
            support_price = nearest_support.get('price', current_price)
            support_strength = nearest_support.get('strength', 0.5)

            # Calculate distance factor (closer = higher score)
            distance = abs(current_price - support_price) / current_price
            distance_factor = max(0, 1 - distance * 20)  # Within 5% = full score

            # Calculate support score
            support_score = support_strength * distance_factor

            # Adjust based on price reactions if available
            support_reactions = price_reactions.get('support', [])
            if support_reactions:
                # Find reaction for this level
                for reaction in support_reactions:
                    if abs(reaction.get('price', 0) - support_price) / current_price < 0.001:
                        # Adjust score based on bounce probability
                        bounce_prob = reaction.get('bounce_probability', 0.5)
                        support_score *= (0.5 + 0.5 * bounce_prob)
                        break

        # 2. Calculate resistance score
        resistance_levels = liquidity_levels.get('resistance', [])
        resistance_score = 0.0

        if resistance_levels:
            # Find nearest resistance level
            nearest_resistance = min(resistance_levels, key=lambda x: abs(x.get('price', 0) - current_price))
            resistance_price = nearest_resistance.get('price', current_price)
            resistance_strength = nearest_resistance.get('strength', 0.5)

            # Calculate distance factor (closer = higher score)
            distance = abs(current_price - resistance_price) / current_price
            distance_factor = max(0, 1 - distance * 20)  # Within 5% = full score

            # Calculate resistance score
            resistance_score = resistance_strength * distance_factor

            # Adjust based on price reactions if available
            resistance_reactions = price_reactions.get('resistance', [])
            if resistance_reactions:
                # Find reaction for this level
                for reaction in resistance_reactions:
                    if abs(reaction.get('price', 0) - resistance_price) / current_price < 0.001:
                        # Adjust score based on rejection probability
                        rejection_prob = reaction.get('rejection_probability', 0.5)
                        resistance_score *= (0.5 + 0.5 * rejection_prob)
                        break

        # 3. Calculate anomaly penalty
        anomaly_penalty = 0.0

        if anomalies:
            # Calculate penalty based on anomaly severity
            for anomaly in anomalies:
                severity = anomaly.get('severity', 'low')

                if severity == 'low':
                    anomaly_penalty += 0.05
                elif severity == 'medium':
                    anomaly_penalty += 0.1
                elif severity == 'high':
                    anomaly_penalty += 0.2

            # Cap penalty at 0.5
            anomaly_penalty = min(0.5, anomaly_penalty)

        # 4. Calculate overall liquidity score
        # Higher score = stronger liquidity structure
        overall_score = max(support_score, resistance_score) * (1 - anomaly_penalty)

        # 5. Calculate directional bias
        # Positive = bullish, Negative = bearish, 0 = neutral
        if support_score > 0 or resistance_score > 0:
            directional_bias = (support_score - resistance_score) / max(support_score, resistance_score)
        else:
            directional_bias = 0.0

        # 6. Calculate risk/reward ratio
        if resistance_score > 0 and support_score > 0:
            # Calculate potential move to resistance and support
            potential_upside = abs(resistance_price - current_price)
            potential_downside = abs(current_price - support_price)

            if potential_downside > 0:
                risk_reward = potential_upside / potential_downside
            else:
                risk_reward = 1.0
        else:
            risk_reward = 1.0

        # 7. Calculate confidence score
        # Higher when levels are stronger and closer
        confidence = overall_score * (1 - anomaly_penalty)

        # Return comprehensive liquidity score
        return {
            'overall_score': float(overall_score),
            'support_score': float(support_score),
            'resistance_score': float(resistance_score),
            'directional_bias': float(directional_bias),
            'risk_reward': float(risk_reward),
            'confidence': float(confidence),
            'anomaly_count': len(anomalies),
            'anomaly_penalty': float(anomaly_penalty)
        }
