#!/usr/bin/env python3
"""
MCP Server Service Manager
Auto-start and background service management
Mathematical precision: Zero-maintenance operation
"""

import os
import sys
import time
import json
import subprocess
import threading
from pathlib import Path
import logging

class MCPServiceManager:
    """Production service manager for MCP server."""
    
    def __init__(self):
        self.server_path = Path(__file__).parent / "mcp_server_production.py"
        self.config_path = Path(__file__).parent / "mcp_installation" / "config" / "config.json"
        self.pid_file = Path(__file__).parent / "mcp_server.pid"
        self.log_file = Path(__file__).parent / "mcp_service.log"
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def is_running(self) -> bool:
        """Check if MCP server is already running."""
        if not self.pid_file.exists():
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Check if process exists (Windows compatible)
            if os.name == 'nt':
                import psutil
                return psutil.pid_exists(pid)
            else:
                os.kill(pid, 0)
                return True
        except:
            return False
    
    def start_server(self, background=True):
        """Start MCP server with auto-restart."""
        if self.is_running():
            self.logger.info("MCP Server already running")
            return True
        
        self.logger.info("Starting Liquidity Sweep MCP Server...")
        
        cmd = [
            sys.executable,
            str(self.server_path),
            "--config", str(self.config_path),
            "--log-level", "INFO"
        ]
        
        if background:
            # Start in background with proper logging
            log_file = self.log_file.parent / "mcp_server_output.log"
            with open(log_file, 'w') as log_handle:
                process = subprocess.Popen(
                    cmd,
                    cwd=str(self.server_path.parent),
                    stdout=log_handle,
                    stderr=subprocess.STDOUT,  # Capture stderr with stdout
                    stdin=subprocess.DEVNULL
                )
            
            # Save PID
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            self.logger.info(f"MCP Server started in background (PID: {process.pid})")
            
            # Monitor and auto-restart in separate thread
            monitor_thread = threading.Thread(
                target=self._monitor_server,
                args=(process,),
                daemon=True
            )
            monitor_thread.start()
            
        else:
            # Start in foreground
            self.logger.info("Starting MCP Server in foreground...")
            subprocess.run(cmd, cwd=str(self.server_path.parent))
        
        return True
    
    def stop_server(self):
        """Stop MCP server."""
        if not self.is_running():
            self.logger.info("MCP Server not running")
            return True
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # Terminate process
            if os.name == 'nt':
                subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                             capture_output=True)
            else:
                os.kill(pid, 15)  # SIGTERM
            
            # Remove PID file
            self.pid_file.unlink()
            self.logger.info("MCP Server stopped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop server: {e}")
            return False
    
    def restart_server(self):
        """Restart MCP server."""
        self.logger.info("Restarting MCP Server...")
        self.stop_server()
        time.sleep(2)
        return self.start_server()
    
    def status(self):
        """Get server status."""
        if self.is_running():
            with open(self.pid_file, 'r') as f:
                pid = f.read().strip()
            self.logger.info(f"MCP Server RUNNING (PID: {pid})")
        else:
            self.logger.info("MCP Server STOPPED")
    
    def _monitor_server(self, process):
        """Monitor server and auto-restart on failure."""
        while True:
            try:
                # Check if process is still running
                if process.poll() is not None:
                    self.logger.warning("MCP Server stopped unexpectedly. Restarting...")
                    time.sleep(5)  # Wait before restart
                    self.start_server(background=True)
                    break
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Monitor error: {e}")
                break
    
    def install_windows_startup(self):
        """Install Windows startup entry."""
        if os.name != 'nt':
            self.logger.error("Windows startup only supported on Windows")
            return False
        
        try:
            import winreg
            
            # Create startup registry entry
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                0,
                winreg.KEY_SET_VALUE
            )
            
            startup_cmd = f'"{sys.executable}" "{__file__}" start --background'
            winreg.SetValueEx(key, "LiquidityMCPServer", 0, winreg.REG_SZ, startup_cmd)
            winreg.CloseKey(key)
            
            self.logger.info("Windows startup entry installed")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to install startup entry: {e}")
            return False

def main():
    """Main service manager entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Liquidity Sweep MCP Service Manager")
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status', 'install-startup'],
                       help='Service action')
    parser.add_argument('--background', action='store_true', default=True,
                       help='Run in background (default: True)')
    parser.add_argument('--foreground', action='store_true',
                       help='Run in foreground')
    
    args = parser.parse_args()
    
    manager = MCPServiceManager()
    
    # Override background setting
    if args.foreground:
        args.background = False
    
    if args.action == 'start':
        manager.start_server(background=args.background)
    elif args.action == 'stop':
        manager.stop_server()
    elif args.action == 'restart':
        manager.restart_server()
    elif args.action == 'status':
        manager.status()
    elif args.action == 'install-startup':
        manager.install_windows_startup()

if __name__ == "__main__":
    main()
