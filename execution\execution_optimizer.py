#!/usr/bin/env python3
"""
Core Execution Optimizer
Smart execution algorithms for minimizing market impact and slippage
"""

import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class ExecutionParameters:
    """Optimized execution parameters"""
    algorithm: str
    time_slices: int
    slice_size: float
    urgency: float
    participation_rate: float
    start_time: datetime
    end_time: datetime

@dataclass 
class ExecutionResult:
    """Result from execution optimization"""
    parameters: ExecutionParameters
    expected_slippage: float
    expected_duration: int  # minutes
    market_impact: float
    confidence: float

class ExecutionOptimizer:
    """
    Mathematical execution optimization for trade orders
    Implements TWAP, VWAP, Implementation Shortfall algorithms
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.max_participation_rate = self.config.get('max_participation_rate', 0.20)  # 20% max
        self.min_time_slices = self.config.get('min_time_slices', 5)
        self.max_time_slices = self.config.get('max_time_slices', 100)
        
    def calculate_twap_execution(self, order_size: float, duration_minutes: int) -> ExecutionParameters:
        """
        Time-Weighted Average Price execution
        Spreads order evenly over time
        """
        time_slices = min(max(duration_minutes // 5, self.min_time_slices), self.max_time_slices)
        slice_size = order_size / time_slices
        
        return ExecutionParameters(
            algorithm="TWAP",
            time_slices=time_slices,
            slice_size=slice_size,
            urgency=0.3,  # Low urgency
            participation_rate=0.10,
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(minutes=duration_minutes)
        )
    
    def calculate_vwap_execution(self, order_size: float, volume_profile: List[float]) -> ExecutionParameters:
        """
        Volume-Weighted Average Price execution
        Aligns execution with historical volume patterns
        """
        if not volume_profile:
            # Fallback to TWAP if no volume data
            return self.calculate_twap_execution(order_size, 60)
        
        total_volume = sum(volume_profile)
        time_slices = len(volume_profile)
        
        # Calculate participation rates based on volume
        max_participation = min(self.max_participation_rate, 0.15)
        avg_participation = max_participation * 0.7
        
        return ExecutionParameters(
            algorithm="VWAP",
            time_slices=time_slices,
            slice_size=order_size / time_slices,  # Will be adjusted per slice
            urgency=0.5,
            participation_rate=avg_participation,
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(minutes=time_slices * 5)
        )
    
    def calculate_implementation_shortfall(self, order_size: float, 
                                         urgency: float, 
                                         volatility: float,
                                         liquidity_score: float) -> ExecutionParameters:
        """
        Implementation Shortfall optimization
        Balances market impact vs timing risk
        """
        # Higher urgency = fewer slices, faster execution
        # Higher volatility = more urgency needed
        # Lower liquidity = need more careful execution
        
        urgency_factor = min(max(urgency, 0.1), 1.0)
        volatility_factor = min(max(volatility, 0.01), 0.1)
        liquidity_factor = min(max(liquidity_score, 0.1), 1.0)
        
        # Calculate optimal execution parameters
        base_duration = 30  # 30 minutes base
        duration_adjustment = (1 - urgency_factor) * 60  # Up to 60 min longer for low urgency
        vol_adjustment = volatility_factor * 100 * -15  # Reduce duration in high vol
        
        optimal_duration = int(base_duration + duration_adjustment + vol_adjustment)
        optimal_duration = max(5, min(optimal_duration, 120))  # 5-120 minute range
        
        # Calculate time slices
        time_slices = max(self.min_time_slices, min(optimal_duration // 3, self.max_time_slices))
        
        # Calculate participation rate
        base_participation = 0.08
        urgency_adjustment = urgency_factor * 0.12  # Up to 12% more for urgent orders
        liquidity_adjustment = (1 - liquidity_factor) * -0.04  # Reduce for illiquid stocks
        
        participation_rate = base_participation + urgency_adjustment + liquidity_adjustment
        participation_rate = max(0.02, min(participation_rate, self.max_participation_rate))
        
        return ExecutionParameters(
            algorithm="Implementation_Shortfall",
            time_slices=time_slices,
            slice_size=order_size / time_slices,
            urgency=urgency_factor,
            participation_rate=participation_rate,
            start_time=datetime.now(),
            end_time=datetime.now() + timedelta(minutes=optimal_duration)
        )
    
    def estimate_execution_cost(self, params: ExecutionParameters, 
                               market_data: Dict[str, Any]) -> ExecutionResult:
        """
        Estimate execution costs and market impact
        """
        spread = market_data.get('bid_ask_spread', 0.01)
        volatility = market_data.get('volatility', 0.02)
        avg_volume = market_data.get('avg_volume', 1000000)
        
        # Calculate market impact
        # Impact increases with participation rate and decreases with liquidity
        participation_impact = params.participation_rate * 0.5  # 50% of participation as impact
        volatility_impact = volatility * 0.3  # Volatility adds uncertainty
        
        market_impact = (participation_impact + volatility_impact) * spread
        
        # Calculate expected slippage
        # Slippage from urgency, market impact, and timing
        urgency_slippage = params.urgency * spread * 0.8
        impact_slippage = market_impact * 0.6
        timing_slippage = (1 / params.time_slices) * spread * 0.2  # More slices = less timing risk
        
        expected_slippage = urgency_slippage + impact_slippage + timing_slippage
        
        # Calculate execution duration
        expected_duration = int((params.end_time - params.start_time).total_seconds() / 60)
        
        # Confidence based on market conditions
        liquidity_factor = min(avg_volume / 500000, 2.0)  # Higher volume = higher confidence
        spread_factor = max(0.3, 1 - (spread * 100))  # Tighter spread = higher confidence
        confidence = min(liquidity_factor * spread_factor * 0.8, 0.95)
        
        return ExecutionResult(
            parameters=params,
            expected_slippage=expected_slippage,
            expected_duration=expected_duration,
            market_impact=market_impact,
            confidence=confidence
        )
    
    def optimize_execution(self, order_size: float,
                          urgency: float,
                          market_data: Dict[str, Any],
                          volume_profile: Optional[List[float]] = None) -> ExecutionResult:
        """
        Select optimal execution algorithm and parameters
        """
        
        # Get market characteristics
        volatility = market_data.get('volatility', 0.02)
        liquidity_score = market_data.get('liquidity_score', 0.7)
        avg_volume = market_data.get('avg_volume', 1000000)
        
        # Determine best algorithm based on conditions
        if urgency > 0.8:
            # High urgency - use aggressive implementation shortfall
            params = self.calculate_implementation_shortfall(
                order_size, urgency, volatility, liquidity_score
            )
        elif volume_profile and len(volume_profile) > 10:
            # Good volume data available - use VWAP
            params = self.calculate_vwap_execution(order_size, volume_profile)
        elif volatility < 0.015 and liquidity_score > 0.8:
            # Low vol, high liquidity - use TWAP
            params = self.calculate_twap_execution(order_size, 60)
        else:
            # Default to implementation shortfall
            params = self.calculate_implementation_shortfall(
                order_size, urgency, volatility, liquidity_score
            )
        
        # Calculate execution metrics
        result = self.estimate_execution_cost(params, market_data)
        
        return result

if __name__ == "__main__":
    # Test the execution optimizer
    optimizer = ExecutionOptimizer()
    
    market_data = {
        'bid_ask_spread': 0.005,  # 0.5% spread
        'volatility': 0.018,      # 1.8% daily volatility  
        'avg_volume': 2000000,    # 2M shares average volume
        'liquidity_score': 0.85   # High liquidity
    }
    
    result = optimizer.optimize_execution(
        order_size=10000,   # 10k shares
        urgency=0.6,        # Medium urgency
        market_data=market_data
    )
    
    print(f"Algorithm: {result.parameters.algorithm}")
    print(f"Time Slices: {result.parameters.time_slices}")
    print(f"Participation Rate: {result.parameters.participation_rate:.1%}")
    print(f"Expected Duration: {result.expected_duration} minutes")
    print(f"Expected Slippage: {result.expected_slippage:.3%}")
    print(f"Market Impact: {result.market_impact:.3%}")
    print(f"Confidence: {result.confidence:.1%}")
