#!/usr/bin/env python3
"""
Greeks History Cache System
Redis + Parquet storage for raw ///V chain snapshots
Enables intraday ROC computation without API re-hits
"""

import pandas as pd
import numpy as np
import json
import time
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict

# Redis is optional - graceful fallback to file-only mode
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

logger = logging.getLogger(__name__)

@dataclass
class GreeksSnapshot:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Single timestamp snapshot of option chain Greeks"""
    timestamp: float
    symbol: str
    underlying_px: float
    snapshot_id: str
    options: List[Dict]  # Raw option data with Greeks
    
    def to_dict(self, ticker: str = None):
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: dict):
        return cls(**data)

class GreeksHistoryCache:
    """
    Greeks history caching system with Redis + Parquet storage
    Mathematical rigor: Zero data loss, optimized for ROC calculations
    """
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 cache_dir: str = "D:/script-work/CORE/data/greeks_cache"):
        """Initialize Greeks cache with Redis and Parquet storage"""
        
        # Redis connection for real-time access
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(redis_url)
                self.redis_client.ping()
                self.redis_available = True
                logger.info("Redis connection established for Greeks cache")
            except Exception as e:
                logger.warning(f"Redis unavailable, using file-only cache: {e}")
                self.redis_client = None
                self.redis_available = False
        else:
            logger.info("Redis not installed, using file-only cache mode")
            self.redis_client = None
            self.redis_available = False
        
        # Parquet storage for persistence
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Configuration
        self.redis_ttl = 86400  # 24 hours in Redis
        self.snapshot_interval = 300  # 5 minutes between snapshots
        self.max_snapshots_per_day = 288  # 24h * 60min / 5min intervals
        
        logger.info(f"Greeks cache initialized: Redis={self.redis_available}, Dir={self.cache_dir}")
    
    def store_snapshot(self, symbol: str, opts_df: pd.DataFrame, underlying_px: float) -> str:
        """
        Store option chain snapshot with Greeks
        
        Args:
            symbol: Underlying symbol
            opts_df: DataFrame with Greeks from parse_chain()
            underlying_px: Current underlying price
            
        Returns:
            snapshot_id for reference
        """
        try:
            timestamp = time.time()
            snapshot_id = f"{symbol}_{int(timestamp)}"
            
            # Convert DataFrame to serializable format
            options_data = []
            for _, row in opts_df.iterrows():
                option_data = {
                    'symbol': row['symbol'],
                    'expiry': row['expiry'],
                    'type': row['type'],
                    'strike': float(row['strike']),
                    'delta': float(row['delta']) if pd.notna(row['delta']) else None,
                    'gamma': float(row['gamma']) if pd.notna(row['gamma']) else None,
                    'theta': float(row['theta']) if pd.notna(row['theta']) else None,
                    'vega': float(row['vega']) if pd.notna(row['vega']) else None,
                    'iv': float(row['iv']) if pd.notna(row['iv']) else None,
                    'bid': float(row['bid']),
                    'ask': float(row['ask']),
                    'mark': float(row['mark'])
                }
                options_data.append(option_data)
            
            # Create snapshot
            snapshot = GreeksSnapshot(
                timestamp=timestamp,
                symbol=symbol,
                underlying_px=underlying_px,
                snapshot_id=snapshot_id,
                options=options_data
            )
            
            # Store in Redis (if available)
            if self.redis_available:
                redis_key = f"greeks:{symbol}:{snapshot_id}"
                self.redis_client.setex(
                    redis_key, 
                    self.redis_ttl, 
                    json.dumps(snapshot.to_dict())
                )
                
                # Update latest snapshot pointer
                self.redis_client.setex(f"greeks:latest:{symbol}", self.redis_ttl, snapshot_id)
            
            # Store in Parquet (always)
            self._store_parquet_snapshot(snapshot)
            
            logger.info(f"Stored Greeks snapshot {snapshot_id} for {symbol} "
                       f"({len(options_data)} options)")
            
            return snapshot_id
            
        except Exception as e:
            logger.error(f"Failed to store Greeks snapshot for {symbol}: {e}")
            raise
    
    def _store_parquet_snapshot(self, snapshot: GreeksSnapshot):
        """Store snapshot in daily Parquet file"""
        try:
            date_str = datetime.fromtimestamp(snapshot.timestamp).strftime('%Y-%m-%d')
            parquet_file = self.cache_dir / f"{snapshot.symbol}_{date_str}.parquet"
            
            # Convert to DataFrame for Parquet storage
            snapshot_df = pd.DataFrame([{
                'timestamp': snapshot.timestamp,
                'snapshot_id': snapshot.snapshot_id,
                'underlying_px': snapshot.underlying_px,
                'options_json': json.dumps(snapshot.options)
            }])
            
            # Append to existing file or create new
            if parquet_file.exists():
                existing_df = pd.read_parquet(parquet_file)
                combined_df = pd.concat([existing_df, snapshot_df], ignore_index=True)
            else:
                combined_df = snapshot_df
            
            # Keep only recent snapshots (prevent file bloat)
            if len(combined_df) > self.max_snapshots_per_day:
                combined_df = combined_df.tail(self.max_snapshots_per_day)
            
            combined_df.to_parquet(parquet_file, index=False)
            
        except Exception as e:
            logger.warning(f"Failed to store Parquet snapshot: {e}")
    
    def get_latest_snapshot(self, symbol: str) -> Optional[GreeksSnapshot]:
        """Get most recent Greeks snapshot for symbol"""
        try:
            # Try Redis first (fastest)
            if self.redis_available:
                latest_id = self.redis_client.get(f"greeks:latest:{symbol}")
                if latest_id:
                    latest_id = latest_id.decode()
                    snapshot_data = self.redis_client.get(f"greeks:{symbol}:{latest_id}")
                    if snapshot_data:
                        data = json.loads(snapshot_data.decode())
                        return GreeksSnapshot.from_dict(data)
            
            # Fall back to Parquet
            return self._get_latest_parquet_snapshot(symbol)
            
        except Exception as e:
            logger.error(f"Failed to get latest snapshot for {symbol}: {e}")
            return None
    
    def _get_latest_parquet_snapshot(self, symbol: str) -> Optional[GreeksSnapshot]:
        """Get latest snapshot from Parquet files"""
        try:
            # Find most recent parquet file
            pattern = f"{symbol}_*.parquet"
            parquet_files = sorted(self.cache_dir.glob(pattern), reverse=True)
            
            if not parquet_files:
                return None
            
            # Load most recent file and get latest snapshot
            df = pd.read_parquet(parquet_files[0])
            if df.empty:
                return None
            
            latest_row = df.iloc[-1]
            options_data = json.loads(latest_row['options_json'])
            
            return GreeksSnapshot(
                timestamp=latest_row['timestamp'],
                symbol=symbol,
                underlying_px=latest_row['underlying_px'],
                snapshot_id=latest_row['snapshot_id'],
                options=options_data
            )
            
        except Exception as e:
            logger.warning(f"Failed to get Parquet snapshot for {symbol}: {e}")
            return None
    
    def compute_greeks_roc(self, symbol: str, minutes_back: int = 30) -> Dict[str, float]:
        """
        Compute Greeks Rate of Change without API re-hits
        
        Args:
            symbol: Underlying symbol
            minutes_back: Minutes to look back for comparison
            
        Returns:
            Dict with delta_roc, gamma_roc, theta_roc, vega_roc, iv_roc
        """
        try:
            current_snapshot = self.get_latest_snapshot(symbol)
            if not current_snapshot:
                return {}
            
            # Get historical snapshot
            target_time = current_snapshot.timestamp - (minutes_back * 60)
            historical_snapshot = self._get_snapshot_near_time(symbol, target_time)
            
            if not historical_snapshot:
                logger.warning(f"No historical snapshot found for {symbol} ROC calculation")
                return {}
            
            # Calculate aggregate Greeks for both snapshots
            current_greeks = self._aggregate_greeks(current_snapshot.options)
            historical_greeks = self._aggregate_greeks(historical_snapshot.options)
            
            # Compute ROC for each Greek
            roc_results = {}
            for greek in ['delta', 'gamma', 'theta', 'vega', 'iv']:
                current_val = current_greeks.get(greek, 0)
                historical_val = historical_greeks.get(greek, 0)
                
                if historical_val != 0:
                    roc = (current_val - historical_val) / abs(historical_val)
                    roc_results[f"{greek}_roc"] = roc
                else:
                    roc_results[f"{greek}_roc"] = 0.0
            
            # Add underlying price ROC
            underlying_roc = 0.0
            if historical_snapshot.underlying_px != 0:
                underlying_roc = ((current_snapshot.underlying_px - historical_snapshot.underlying_px) 
                                / historical_snapshot.underlying_px)
            roc_results['underlying_roc'] = underlying_roc
            
            logger.info(f"Computed Greeks ROC for {symbol} over {minutes_back}min: "
                       f"={roc_results.get('delta_roc', 0):.3f}")
            
            return roc_results
            
        except Exception as e:
            logger.error(f"Failed to compute Greeks ROC for {symbol}: {e}")
            return {}
    
    def _get_snapshot_near_time(self, symbol: str, target_time: float) -> Optional[GreeksSnapshot]:
        """Get snapshot closest to target time"""
        try:
            # Search recent Parquet files
            date_str = datetime.fromtimestamp(target_time).strftime('%Y-%m-%d')
            parquet_file = self.cache_dir / f"{symbol}_{date_str}.parquet"
            
            if not parquet_file.exists():
                # Try previous day
                prev_date = datetime.fromtimestamp(target_time) - timedelta(days=1)
                parquet_file = self.cache_dir / f"{symbol}_{prev_date.strftime('%Y-%m-%d')}.parquet"
            
            if not parquet_file.exists():
                return None
            
            df = pd.read_parquet(parquet_file)
            if df.empty:
                return None
            
            # Find closest timestamp
            time_diffs = abs(df['timestamp'] - target_time)
            closest_idx = time_diffs.idxmin()
            closest_row = df.iloc[closest_idx]
            
            options_data = json.loads(closest_row['options_json'])
            
            return GreeksSnapshot(
                timestamp=closest_row['timestamp'],
                symbol=symbol,
                underlying_px=closest_row['underlying_px'],
                snapshot_id=closest_row['snapshot_id'],
                options=options_data
            )
            
        except Exception as e:
            logger.warning(f"Failed to find snapshot near time for {symbol}: {e}")
            return None
    
    def _aggregate_greeks(self, options_data: List[Dict]) -> Dict[str, float]:
        """Aggregate Greeks across all options in snapshot"""
        aggregated = {
            'delta': 0.0,
            'gamma': 0.0, 
            'theta': 0.0,
            'vega': 0.0,
            'iv': 0.0
        }
        
        valid_count = 0
        
        for option in options_data:
            for greek in aggregated.keys():
                value = option.get(greek)
                if value is not None and not np.isnan(value):
                    if greek == 'iv':
                        aggregated[greek] += value  # Sum for average
                        valid_count += 1
                    else:
                        aggregated[greek] += value  # Portfolio Greeks sum
        
        # Average IV across options
        if valid_count > 0:
            aggregated['iv'] = aggregated['iv'] / valid_count
        
        return aggregated
    
    def cleanup_old_data(self, days_to_keep: int = 7):
        """Clean up old cache data"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            # Clean Parquet files
            for parquet_file in self.cache_dir.glob("*.parquet"):
                try:
                    # Extract date from filename
                    date_part = parquet_file.stem.split('_')[-1]
                    file_date = datetime.strptime(date_part, '%Y-%m-%d')
                    
                    if file_date < cutoff_date:
                        parquet_file.unlink()
                        logger.info(f"Cleaned up old Greeks cache file: {parquet_file}")
                        
                except Exception as e:
                    logger.warning(f"Failed to clean file {parquet_file}: {e}")
            
            logger.info(f"Greeks cache cleanup completed, kept {days_to_keep} days")
            
        except Exception as e:
            logger.error(f"Failed to cleanup Greeks cache: {e}")

# Integration function for LiveDataGatewayAgent
def integrate_greeks_cache(opts_df: pd.DataFrame, symbol: str, underlying_px: float) -> str:
    """
    Integration function to store Greeks snapshot from agent
    Call this after successful _pull_schwab() execution
    """
    try:
        cache = GreeksHistoryCache()
        snapshot_id = cache.store_snapshot(symbol, opts_df, underlying_px)
        return snapshot_id
    except Exception as e:
        logger.error(f"Failed to integrate Greeks cache for {symbol}: {e}")
        return ""

if __name__ == "__main__":
    # Test the cache system
    print("Testing Greeks History Cache...")
    
    # Create test data
    test_data = [{
        'symbol': 'AAPL_TEST_C175',
        'expiry': '2025-07-18',
        'type': 'CALL',
        'strike': 175.0,
        'delta': 0.65,
        'gamma': 0.025,
        'theta': -0.08,
        'vega': 0.18,
        'iv': 0.25,
        'bid': 9.8,
        'ask': 10.0,
        'mark': 9.9
    }]
    
    test_df = pd.DataFrame(test_data)
    cache = GreeksHistoryCache()
    
    # Store snapshot
    snapshot_id = cache.store_snapshot("AAPL", test_df, 175.23)
    print(f"Stored snapshot: {snapshot_id}")
    
    # Retrieve latest
    latest = cache.get_latest_snapshot("AAPL")
    if latest:
        print(f"Retrieved snapshot: {latest.snapshot_id}, {len(latest.options)} options")
    
    print("Greeks cache test completed")
