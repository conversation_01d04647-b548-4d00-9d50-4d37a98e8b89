@echo off
REM Schwab MCP Production Server Launcher
REM Direct execution for production deployment

echo ============================================================
echo SCHWAB MCP PRODUCTION SERVER
echo ============================================================
echo Status: REPLACING POLYGON.IO MCP
echo Mode: Production Ready
echo ============================================================

cd /d "D:\script-work\SCHWAB_MCP_PRODUCTION\scripts"
python START_MCP_SERVER.py

pause
