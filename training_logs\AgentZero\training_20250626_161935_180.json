{"timestamp": "2025-06-26T16:19:35.180376", "signal_data": {"confidence": 0.95, "strength": 0.7, "execution_recommendation": "accumulation"}, "math_data": {"accuracy_score": 0.95, "precision": 0.001}, "decision": {"action": "flow_physics_analysis", "result": {"timestamp": "2025-06-26 16:19:35.128660", "symbol": "TEST_TICKER", "flow_value": 115.0, "flow_velocity": 1.0, "flow_acceleration": 0.1, "flow_jerk": 0.01, "normalized_velocity": 0.5, "normalized_acceleration": 0.5, "normalized_jerk": 0.5, "current_regime": "STEADY_FLOW", "regime_confidence": 0.8, "regime_duration": null, "institutional_activity": true, "institutional_direction": "accumulation", "institutional_strength": 0.7, "momentum_shift_detected": false, "regime_change_detected": false, "extreme_flow_detected": false, "timeframe_alignment": 0.9, "dominant_timeframe": "5m", "quality_score": 0.95, "analysis_metadata": {}}}, "outcome": 0.95, "market_context": {"system": "FLOW_PHYSICS_SPECIALIST", "source_file": "flow_physics_agent.py", "source_agent": "FLOW_PHYSICS_ANALYZER", "intelligence_type": "INSTITUTIONAL_FLOW_ANALYSIS", "ticker": "TEST_TICKER", "data_points": 5, "quality_score": 0.95, "current_regime": "STEADY_FLOW", "institutional_direction": "accumulation", "institutional_activity": true, "output_file": "flow_phys\\2025-06-26\\TEST_TICKER_flowphysics.json"}, "agent_version": "1.5_Integrated"}