#!/usr/bin/env python3
"""
Agent Template Generator - Cookiecutter Alternative
Creates compliant agent structure from template
"""

import os
import sys
from pathlib import Path
from datetime import datetime

def create_agent_from_template(agent_name, agent_type="analyzer"):
    """Generate new agent with compliant structure"""
    
    # Validate agent name
    if not agent_name.replace('_', '').replace('-', '').isalnum():
        raise ValueError("Agent name must be alphanumeric with underscores/hyphens")
    
    agent_dir = Path(f"agents/{agent_name}")
    agent_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate agent class file
    agent_class_content = f'''#!/usr/bin/env python3
"""
{agent_name.title().replace('_', ' ')} Agent
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass

from utils.base_agent import BaseAgent
from utils.validation import validate_input, validate_output

logger = logging.getLogger(__name__)

@dataclass
class {agent_name.title().replace('_', '')}Config:
    """Configuration for {agent_name} agent"""
    timeout_ms: int = 5000
    precision_threshold: float = 0.001
    enable_validation: bool = True

class {agent_name.title().replace('_', '')}Agent(BaseAgent):
    """
    {agent_name.title().replace('_', ' ')} Agent
    
    Implements mathematical precision and statistical rigor
    for {agent_type} operations.
    """
    
    def __init__(self, config: {agent_name.title().replace('_', '')}Config):
        super().__init__(agent_name="{agent_name}")
        self.config = config
        self.metrics = {{}}
    
    @validate_input
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main processing logic with mathematical validation
        
        Args:
            data: Input data dictionary
            
        Returns:
            Processed result with validation metrics
        """
        try:
            # Core processing logic here
            result = self._execute_analysis(data)
            
            # Validate output
            if self.config.enable_validation:
                result = self._validate_result(result)
            
            return result
            
        except Exception as e:
            logger.error(f"{{self.agent_name}} processing failed: {{e}}")
            raise
    
    def _execute_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute core analysis logic"""
        # TODO: Implement agent-specific logic
        return {{
            "status": "processed",
            "timestamp": datetime.now().isoformat(),
            "data": data
        }}
    
    @validate_output
    def _validate_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate mathematical precision and completeness"""
        # TODO: Implement validation logic
        result["validation"] = {{
            "precision_check": True,
            "completeness": 1.0,
            "confidence": 0.95
        }}
        return result
'''
    
    # Write agent class
    with open(agent_dir / f"{agent_name}.py", 'w') as f:
        f.write(agent_class_content)
    
    # Generate contract YAML
    contract_content = f'''task_id: {agent_name.upper()}
name: {agent_name.title().replace('_', ' ')} Agent
version: 1.0.0
description: {agent_type.title()} agent implementing mathematical precision
            and statistical rigor for data processing.

inputs:
  - data: Dict[str, Any]  # Input data structure

outputs:
  data:
    type: Dict[str, Any]
    schema: schemas/{agent_name}_output_v1.json
    
success_criteria:
  perf_budget:
    max_runtime_ms: 5000
  precision_threshold: 0.001
  code_coverage_min: 0.90
  output_completeness: 1.00
  dependency_constraints:
    max_additional_deps: 2
    forbidden_packages:
      - tensorflow
      - torch
      - opencv-python

mathematical_requirements:
  - numerical_stability: true
  - precision_validation: required
  - statistical_rigor: enforced
'''
    
    # Write contract
    with open(f"contracts/{agent_name}.yml", 'w') as f:
        f.write(contract_content)
    
    # Generate test stub
    test_content = f'''#!/usr/bin/env python3
"""
Test suite for {agent_name.title().replace('_', ' ')} Agent
Mathematical validation and precision testing
"""

import pytest
import numpy as np
from agents.{agent_name}.{agent_name} import {agent_name.title().replace('_', '')}Agent, {agent_name.title().replace('_', '')}Config

class Test{agent_name.title().replace('_', '')}Agent:
    """Test mathematical precision and agent compliance"""
    
    @pytest.fixture
    def agent(self):
        config = {agent_name.title().replace('_', '')}Config()
        return {agent_name.title().replace('_', '')}Agent(config)
    
    @pytest.fixture
    def sample_data(self):
        return {{
            "values": np.random.randn(100),
            "timestamp": "2025-06-24T12:00:00Z",
            "metadata": {{"source": "test"}}
        }}
    
    def test_agent_initialization(self, agent):
        """Test agent initializes correctly"""
        assert agent.agent_name == "{agent_name}"
        assert agent.config.timeout_ms == 5000
    
    def test_process_basic(self, agent, sample_data):
        """Test basic processing functionality"""
        result = agent.process(sample_data)
        
        assert "status" in result
        assert "timestamp" in result
        assert "validation" in result
    
    def test_mathematical_precision(self, agent):
        """Test numerical precision requirements"""
        test_data = {{"values": [1.0, 2.0, 3.0]}}
        result = agent.process(test_data)
        
        # Validate precision
        assert result["validation"]["precision_check"] is True
        assert result["validation"]["completeness"] == 1.0
    
    def test_performance_budget(self, agent, sample_data):
        """Test performance stays within budget"""
        import time
        
        start_time = time.time()
        result = agent.process(sample_data)
        execution_time = (time.time() - start_time) * 1000
        
        assert execution_time < agent.config.timeout_ms
    
    def test_output_validation(self, agent, sample_data):
        """Test output meets validation criteria"""
        result = agent.process(sample_data)
        
        # Required fields present
        required_fields = ["status", "timestamp", "validation"]
        for field in required_fields:
            assert field in result
'''
    
    # Write test file
    with open(f"tests/test_{agent_name}.py", 'w') as f:
        f.write(test_content)
    
    # Generate README
    readme_content = f'''# {agent_name.title().replace('_', ' ')} Agent

Mathematical precision {agent_type} implementing statistical rigor.

## Architecture

- **Agent Class**: `agents/{agent_name}/{agent_name}.py`
- **Contract**: `contracts/{agent_name}.yml`
- **Tests**: `tests/test_{agent_name}.py`

## Usage

```python
from agents.{agent_name}.{agent_name} import {agent_name.title().replace('_', '')}Agent, {agent_name.title().replace('_', '')}Config

config = {agent_name.title().replace('_', '')}Config()
agent = {agent_name.title().replace('_', '')}Agent(config)

result = agent.process(data)
```

## Testing

```bash
pytest tests/test_{agent_name}.py -v
```

## Contract Validation

```bash
python ci/run_contract_tasks.py --only {agent_name.upper()}
```

## Performance Requirements

- Max runtime: 5000ms
- Code coverage: >=90%
- Precision threshold: 0.001
- Output completeness: 100%

## Mathematical Standards

All calculations must maintain numerical stability and precision validation.
Statistical rigor enforced through validation framework.
'''
    
    # Write README
    with open(agent_dir / "README.md", 'w') as f:
        f.write(readme_content)
    
    print(f"Agent '{agent_name}' created successfully")
    print(f"  - Agent class: agents/{agent_name}/{agent_name}.py")
    print(f"  - Contract: contracts/{agent_name}.yml")
    print(f"  - Tests: tests/test_{agent_name}.py")
    print(f"  - Documentation: agents/{agent_name}/README.md")
    
    return agent_dir

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python create_agent.py <agent_name> <agent_type>")
        print("Example: python create_agent.py volume_analyzer analyzer")
        sys.exit(1)
    
    agent_name = sys.argv[1]
    agent_type = sys.argv[2]
    
    try:
        create_agent_from_template(agent_name, agent_type)
    except Exception as e:
        print(f"Error creating agent: {e}")
        sys.exit(1)
