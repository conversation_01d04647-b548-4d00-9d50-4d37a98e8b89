"""
ML Imputer Module

This module provides utilities for handling missing values in ML features.
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from sklearn.impute import SimpleImputer, KNNImputer
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor

# Configure logging
logger = logging.getLogger("Liquidity_Reports.ml.imputer")

class MLImputer:
    """
    Handles missing values in ML features.
    
    This class provides methods for imputing missing values in ML features
    using various strategies.
    """
    
    def __init__(self, strategy: str = "auto", **kwargs):
        """
        Initialize the ML imputer.
        
        Args:
            strategy: Imputation strategy ('mean', 'median', 'most_frequent', 'constant', 'knn', 'iterative', 'auto')
            **kwargs: Additional parameters for the imputer
        """
        self.strategy = strategy
        self.kwargs = kwargs
        self.imputers = {}
        self.feature_stats = {}
        
        logger.info(f"Initialized ML imputer with strategy: {strategy}")
    
    def fit(self, X: pd.DataFrame) -> 'MLImputer':
        """
        Fit the imputer to the data.
        
        Args:
            X: DataFrame with features
            
        Returns:
            Self for method chaining
        """
        logger.info(f"Fitting imputer to data with shape: {X.shape}")
        
        # Store feature statistics for reporting
        self._compute_feature_stats(X)
        
        # Create imputers based on strategy
        if self.strategy == "auto":
            # Use different strategies for different types of features
            self._create_auto_imputers(X)
        else:
            # Use the same strategy for all features
            self.imputers["all"] = self._create_imputer(self.strategy)
            self.imputers["all"].fit(X)
        
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Transform the data by imputing missing values.
        
        Args:
            X: DataFrame with features
            
        Returns:
            DataFrame with imputed values
        """
        logger.info(f"Transforming data with shape: {X.shape}")
        
        # Check if imputer is fitted
        if not self.imputers:
            logger.warning("Imputer not fitted, fitting now")
            self.fit(X)
        
        # Create a copy of the input data
        X_imputed = X.copy()
        
        # Apply imputation based on strategy
        if self.strategy == "auto":
            # Apply different imputers to different feature groups
            for group, imputer in self.imputers.items():
                if group == "numeric":
                    # Get numeric columns with missing values
                    numeric_cols = X_imputed.select_dtypes(include=np.number).columns
                    missing_cols = [col for col in numeric_cols if X_imputed[col].isna().any()]
                    
                    if missing_cols:
                        logger.info(f"Imputing {len(missing_cols)} numeric columns")
                        X_imputed[missing_cols] = pd.DataFrame(
                            imputer.transform(X_imputed[missing_cols]),
                            columns=missing_cols,
                            index=X_imputed.index
                        )
                
                elif group == "categorical":
                    # Get categorical columns with missing values
                    cat_cols = X_imputed.select_dtypes(include=["object", "category"]).columns
                    missing_cols = [col for col in cat_cols if X_imputed[col].isna().any()]
                    
                    if missing_cols:
                        logger.info(f"Imputing {len(missing_cols)} categorical columns")
                        X_imputed[missing_cols] = pd.DataFrame(
                            imputer.transform(X_imputed[missing_cols]),
                            columns=missing_cols,
                            index=X_imputed.index
                        )
                
                elif group == "boolean":
                    # Get boolean columns with missing values
                    bool_cols = X_imputed.select_dtypes(include=["bool"]).columns
                    missing_cols = [col for col in bool_cols if X_imputed[col].isna().any()]
                    
                    if missing_cols:
                        logger.info(f"Imputing {len(missing_cols)} boolean columns")
                        X_imputed[missing_cols] = pd.DataFrame(
                            imputer.transform(X_imputed[missing_cols]),
                            columns=missing_cols,
                            index=X_imputed.index
                        )
                
                elif group == "datetime":
                    # Get datetime columns with missing values
                    dt_cols = X_imputed.select_dtypes(include=["datetime"]).columns
                    missing_cols = [col for col in dt_cols if X_imputed[col].isna().any()]
                    
                    if missing_cols:
                        logger.info(f"Imputing {len(missing_cols)} datetime columns")
                        # Convert to numeric for imputation
                        for col in missing_cols:
                            X_imputed[f"{col}_numeric"] = X_imputed[col].astype(np.int64)
                        
                        # Impute numeric values
                        numeric_cols = [f"{col}_numeric" for col in missing_cols]
                        imputed_values = imputer.transform(X_imputed[numeric_cols])
                        
                        # Convert back to datetime
                        for i, col in enumerate(missing_cols):
                            X_imputed[col] = pd.to_datetime(imputed_values[:, i])
                            X_imputed.drop(f"{col}_numeric", axis=1, inplace=True)
        else:
            # Apply the same imputer to all columns with missing values
            missing_cols = [col for col in X_imputed.columns if X_imputed[col].isna().any()]
            
            if missing_cols:
                logger.info(f"Imputing {len(missing_cols)} columns")
                X_imputed[missing_cols] = pd.DataFrame(
                    self.imputers["all"].transform(X_imputed[missing_cols]),
                    columns=missing_cols,
                    index=X_imputed.index
                )
        
        # Log imputation results
        self._log_imputation_results(X, X_imputed)
        
        return X_imputed
    
    def fit_transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Fit the imputer to the data and transform it.
        
        Args:
            X: DataFrame with features
            
        Returns:
            DataFrame with imputed values
        """
        return self.fit(X).transform(X)
    
    def _create_imputer(self, strategy: str):
        """
        Create an imputer based on the specified strategy.
        
        Args:
            strategy: Imputation strategy
            
        Returns:
            Imputer object
        """
        if strategy in ["mean", "median", "most_frequent", "constant"]:
            return SimpleImputer(strategy=strategy, **self.kwargs)
        elif strategy == "knn":
            n_neighbors = self.kwargs.get("n_neighbors", 5)
            return KNNImputer(n_neighbors=n_neighbors)
        elif strategy == "iterative":
            max_iter = self.kwargs.get("max_iter", 10)
            estimator = self.kwargs.get("estimator", RandomForestRegressor(n_estimators=10))
            return IterativeImputer(estimator=estimator, max_iter=max_iter)
        else:
            logger.warning(f"Unknown strategy: {strategy}, using mean")
            return SimpleImputer(strategy="mean")
    
    def _create_auto_imputers(self, X: pd.DataFrame):
        """
        Create imputers for different types of features.
        
        Args:
            X: DataFrame with features
        """
        # Create imputer for numeric features
        self.imputers["numeric"] = self._create_imputer("mean")
        numeric_cols = X.select_dtypes(include=np.number).columns
        if len(numeric_cols) > 0:
            self.imputers["numeric"].fit(X[numeric_cols])
        
        # Create imputer for categorical features
        self.imputers["categorical"] = self._create_imputer("most_frequent")
        cat_cols = X.select_dtypes(include=["object", "category"]).columns
        if len(cat_cols) > 0:
            self.imputers["categorical"].fit(X[cat_cols])
        
        # Create imputer for boolean features
        self.imputers["boolean"] = self._create_imputer("most_frequent")
        bool_cols = X.select_dtypes(include=["bool"]).columns
        if len(bool_cols) > 0:
            self.imputers["boolean"].fit(X[bool_cols])
        
        # Create imputer for datetime features
        self.imputers["datetime"] = self._create_imputer("mean")
        dt_cols = X.select_dtypes(include=["datetime"]).columns
        if len(dt_cols) > 0:
            # Convert to numeric for imputation
            numeric_dt = pd.DataFrame()
            for col in dt_cols:
                numeric_dt[col] = X[col].astype(np.int64)
            self.imputers["datetime"].fit(numeric_dt)
    
    def _compute_feature_stats(self, X: pd.DataFrame):
        """
        Compute statistics for each feature.
        
        Args:
            X: DataFrame with features
        """
        for col in X.columns:
            self.feature_stats[col] = {
                "missing_count": X[col].isna().sum(),
                "missing_percent": X[col].isna().mean() * 100,
                "dtype": str(X[col].dtype)
            }
    
    def _log_imputation_results(self, X_orig: pd.DataFrame, X_imputed: pd.DataFrame):
        """
        Log the results of imputation.
        
        Args:
            X_orig: Original DataFrame
            X_imputed: Imputed DataFrame
        """
        # Count missing values before and after imputation
        missing_before = X_orig.isna().sum().sum()
        missing_after = X_imputed.isna().sum().sum()
        
        logger.info(f"Missing values before imputation: {missing_before}")
        logger.info(f"Missing values after imputation: {missing_after}")
        
        # Log details for each column with missing values
        for col in X_orig.columns:
            missing_before_col = X_orig[col].isna().sum()
            missing_after_col = X_imputed[col].isna().sum()
            
            if missing_before_col > 0:
                logger.info(f"Column '{col}': {missing_before_col} missing values before, "
                           f"{missing_after_col} missing values after imputation")
    
    def get_feature_stats(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for each feature.
        
        Returns:
            Dictionary with feature statistics
        """
        return self.feature_stats
