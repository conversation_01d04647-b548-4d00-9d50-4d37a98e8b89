/* ML Dashboard Styles */

/* Main ML Tab */
.ml-header {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #343a40;
}

.ml-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ml-control-group {
    min-width: 200px;
    flex: 1;
}

.ml-control-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.ml-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: a 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    font-weight: 500;
}

.ml-button:hover {
    background-color: #0069d9;
}

.ml-button:active {
    background-color: #0062cc;
}

.ml-section {
    margin-bottom: 30px;
    background-color: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.ml-section h3 {
    margin-top: 0;
    color: #343a40;
    font-size: 18px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.ml-pattern-list {
    margin-bottom: 15px;
}

.ml-pattern-item {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.15s;
}

.ml-pattern-item:hover {
    background-color: #f8f9fa;
}

.ml-pattern-name {
    font-weight: 600;
    color: #212529;
}

.ml-pattern-confidence {
    font-weight: 600;
    color: #28a745;
}

.ml-pattern-time {
    color: #6c757d;
    font-size: 0.9em;
}

.ml-prediction-summary {
    margin-top: 15px;
}

.ml-pred-item {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.15s;
}

.ml-pred-item:hover {
    background-color: #f8f9fa;
}

.ml-horizon {
    font-weight: 600;
    color: #212529;
}

.ml-pred-details {
    text-align: right;
}

.ml-pred-price {
    font-weight: 600;
    color: #495057;
}

.ml-pred-change {
    margin-top: 5px;
    font-weight: 500;
}

.ml-positive {
    color: #28a745;
}

.ml-negative {
    color: #dc3545;
}

.ml-no-patterns, .ml-no-predictions {
    padding: 20px;
    color: #6c757d;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 6px;
    font-style: italic;
}

/* Alert Panel Styles */
.ml-alert-panel {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 25px;
}

.alert-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

.alert-panel-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #343a40;
}

.alert-header-buttons {
    display: flex;
    gap: 10px;
}

.alert-panel-body {
    padding: 10px 0;
}

.alerts-container {
    max-height: 450px;
    overflow-y: auto;
    padding: 10px 0;
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

.alerts-container::-webkit-scrollbar {
    width: 8px;
}

.alerts-container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.alerts-container::-webkit-scrollbar-thumb {
    background-color: #dee2e6;
    border-radius: 4px;
}

.alert-card {
    margin-bottom: 20px;
    border-width: 0;
    border-left-width: 5px !important;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s, box-shadow 0.2s;
}

.alert-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.alert-card-header {
    padding: 12px 15px;
    display: flex;
    align-items: center;
}

.alert-priority-critical {
    background-color: #f8d7da;
    color: #721c24;
    border-left-color: #dc3545 !important;
}

.alert-priority-high {
    background-color: #fff3cd;
    color: #856404;
    border-left-color: #ffc107 !important;
}

.alert-priority-medium {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left-color: #17a2b8 !important;
}

.alert-priority-low {
    background-color: #e2e3e5;
    color: #383d41;
    border-left-color: #6c757d !important;
}

.alert-card-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.alert-type {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.alert-type::before {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.alert-priority-critical .alert-type::before {
    background-color: #dc3545;
}

.alert-priority-high .alert-type::before {
    background-color: #ffc107;
}

.alert-priority-medium .alert-type::before {
    background-color: #17a2b8;
}

.alert-priority-low .alert-type::before {
    background-color: #6c757d;
}

.alert-confidence {
    font-size: 0.9em;
    font-weight: 500;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.5);
}

.alert-message {
    margin-bottom: 15px;
    font-size: 16px;
    color: #212529;
    line-height: 1.4;
}

.alert-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.9em;
    color: #6c757d;
    margin-bottom: 15px;
}

.alert-time, .alert-source {
    display: flex;
    align-items: center;
}

.alert-time::before {
    content: '';
    margin-right: 4px;
}

.alert-source::before {
    content: '';
    margin-right: 4px;
}

.alert-data {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    font-size: 0.9em;
    color: #495057;
}

.alert-extra-data {
    line-height: 1.6;
}

.alert-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.no-alerts-message {
    text-align: center;
    padding: 30px;
    color: #6c757d;
    font-size: 0.95em;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.no-alerts-message i {
    font-size: 24px;
    color: #28a745;
}
