"""
Service-Oriented Architecture Module

This module provides service-oriented architecture capabilities for ML model deployment,
enabling microservices-based deployment of models for scalability and maintainability.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import json
import logging
import time
import threading
import socket
import queue
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import multiprocessing
from dataclasses import dataclass, field
import uuid

from ml_logging import get_logger
from ml_config_manager import ConfigManager

logger = get_logger(__name__)

@dataclass
class ModelRequest:
    """Container for a model inference request."""
    id: str
    model_id: str
    data: Any
    timestamp: float = field(default_factory=time.time)
    timeout_ms: int = 30000  # 30 seconds default timeout
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ModelResponse:
    """Container for a model inference response."""
    request_id: str
    model_id: str
    results: Any
    error: Optional[str] = None
    inference_time_ms: float = 0.0
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)

class ModelService:
    """
    Model Service
    
    This class provides a service interface for a specific model,
    handling requests, inference, and responses.
    """
    
    def __init__(
        self, 
        model_id: str, 
        model: Any, 
        config: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a model service.
        
        Args:
            model_id: Unique identifier for the model
            model: The model instance
            config: Service configuration
        """
        self.model_id = model_id
        self.model = model
        self.config = config or {}
        
        # Request queue and processing thread
        self.request_queue = queue.Queue()
        self.response_queue = queue.Queue()
        self.processing_thread = None
        self.is_running = False
        
        # Performance metrics
        self.request_count = 0
        self.error_count = 0
        self.total_inference_time = 0.0
        self.last_inference_time = 0.0
        
        logger.info(f"Model service initialized for model {model_id}")
    
    def start(self) -> bool:
        """
        Start the model service.
        
        Returns:
            bool: True if service was started successfully
        """
        if self.is_running:
            logger.info(f"Model service for {self.model_id} already running")
            return True
        
        try:
            self.is_running = True
            self.processing_thread = threading.Thread(
                target=self._process_requests,
                daemon=True
            )
            self.processing_thread.start()
            logger.info(f"Model service for {self.model_id} started")
            return True
        except Exception as e:
            logger.error(f"Error starting model service for {self.model_id}: {str(e)}")
            self.is_running = False
            return False
    
    def stop(self) -> bool:
        """
        Stop the model service.
        
        Returns:
            bool: True if service was stopped successfully
        """
        if not self.is_running:
            return True
        
        try:
            self.is_running = False
            if self.processing_thread and self.processing_thread.is_alive():
                self.processing_thread.join(timeout=5.0)
            logger.info(f"Model service for {self.model_id} stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping model service for {self.model_id}: {str(e)}")
            return False
    
    def submit_request(self, request: ModelRequest) -> bool:
        """
        Submit a request to the model service.
        
        Args:
            request: The model request
            
        Returns:
            bool: True if request was submitted successfully
        """
        if not self.is_running:
            logger.error(f"Model service for {self.model_id} not running")
            return False
        
        try:
            self.request_queue.put(request)
            return True
        except Exception as e:
            logger.error(f"Error submitting request to model service for {self.model_id}: {str(e)}")
            return False
    
    def get_response(self, timeout: Optional[float] = None) -> Optional[ModelResponse]:
        """
        Get a response from the model service.
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            ModelResponse or None if no response available
        """
        try:
            return self.response_queue.get(block=True, timeout=timeout)
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"Error getting response from model service for {self.model_id}: {str(e)}")
            return None
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get performance metrics for the model service.
        
        Returns:
            Dict containing metrics data
        """
        avg_inference_time = 0.0
        if self.request_count > 0:
            avg_inference_time = self.total_inference_time / self.request_count
        
        return {
            "model_id": self.model_id,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "avg_inference_time_ms": avg_inference_time,
            "last_inference_time_ms": self.last_inference_time,
            "error_rate": self.error_count / max(1, self.request_count)
        }
    
    def _process_requests(self) -> None:
        """Background thread for processing model requests."""
        logger.info(f"Request processing thread started for model {self.model_id}")
        
        while self.is_running:
            try:
                # Get next request from queue with timeout
                try:
                    request = self.request_queue.get(block=True, timeout=1.0)
                except queue.Empty:
                    continue
                
                # Process the request
                start_time = time.time()
                error = None
                results = None
                
                try:
                    # Check if request has timed out
                    current_time = time.time()
                    request_age_ms = (current_time - request.timestamp) * 1000
                    if request_age_ms > request.timeout_ms:
                        error = f"Request timed out ({request_age_ms:.1f}ms > {request.timeout_ms}ms)"
                        logger.warning(f"Request {request.id} for model {self.model_id} timed out")
                    else:
                        # Run inference
                        results = self._run_inference(request.data)
                except Exception as e:
                    error = str(e)
                    logger.error(f"Error processing request {request.id} for model {self.model_id}: {error}")
                
                # Calculate inference time
                end_time = time.time()
                inference_time = (end_time - start_time) * 1000  # Convert to ms
                
                # Update metrics
                self.request_count += 1
                self.last_inference_time = inference_time
                self.total_inference_time += inference_time
                if error:
                    self.error_count += 1
                
                # Create response
                response = ModelResponse(
                    request_id=request.id,
                    model_id=self.model_id,
                    results=results,
                    error=error,
                    inference_time_ms=inference_time,
                    metadata=request.metadata.copy()
                )
                
                # Add response to queue
                self.response_queue.put(response)
                
                # Mark task as done
                self.request_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in request processing loop for {self.model_id}: {str(e)}")
                time.sleep(1.0)  # Sleep briefly to prevent rapid logging on error
    
    def _run_inference(self, data: Any) -> Any:
        """
        Run inference on the model.
        
        Args:
            data: Input data for the model
            
        Returns:
            Model inference results
        """
        # This method should be customized based on the specific model type
        # The implementation below is a generic example
        try:
            import torch
            
            # Set model to evaluation mode
            if hasattr(self.model, 'eval'):
                self.model.eval()
            
            # Run inference with no gradients
            with torch.no_grad():
                if isinstance(data, torch.Tensor):
                    results = self.model(data)
                else:
                    # Try to convert to tensor if not already
                    try:
                        data_tensor = torch.tensor(data)
                        results = self.model(data_tensor)
                    except:
                        # Fall back to direct inference
                        results = self.model(data)
            
            # Convert results to Python types for JSON serialization
            if isinstance(results, torch.Tensor):
                return results.cpu().numpy().tolist()
            
            return results
            
        except Exception as e:
            logger.error(f"Error during inference for model {self.model_id}: {str(e)}")
            raise

class ServiceManager:
    """
    Service Manager
    
    This class manages multiple model services, handling service creation,
    request routing, and load balancing.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the service manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.services: Dict[str, ModelService] = {}
        
        # Request handling
        self.request_queue = queue.Queue()
        self.response_map: Dict[str, ModelResponse] = {}
        self.response_listeners: Dict[str, List[Callable]] = {}
        self.dispatch_thread = None
        self.is_running = False
        
        logger.info("Service Manager initialized")
    
    def initialize(self) -> bool:
        """
        Initialize the service manager.
        
        Returns:
            bool: True if initialization was successful
        """
        try:
            # Start the request dispatch thread
            self.is_running = True
            self.dispatch_thread = threading.Thread(
                target=self._dispatch_requests,
                daemon=True
            )
            self.dispatch_thread.start()
            
            logger.info("Service Manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing Service Manager: {str(e)}")
            self.is_running = False
            return False
    
    def register_service(
        self, 
        model_id: str, 
        model: Any, 
        config: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Register a model service.
        
        Args:
            model_id: Unique identifier for the model
            model: The model instance
            config: Service configuration
            
        Returns:
            bool: True if service was registered successfully
        """
        if model_id in self.services:
            logger.warning(f"Service for model {model_id} already registered")
            return True
        
        try:
            # Create and start the service
            service = ModelService(model_id, model, config)
            if not service.start():
                logger.error(f"Failed to start service for model {model_id}")
                return False
            
            # Register the service
            self.services[model_id] = service
            
            logger.info(f"Service for model {model_id} registered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error registering service for model {model_id}: {str(e)}")
            return False
    
    def unregister_service(self, model_id: str) -> bool:
        """
        Unregister a model service.
        
        Args:
            model_id: Unique identifier for the model
            
        Returns:
            bool: True if service was unregistered successfully
        """
        if model_id not in self.services:
            logger.warning(f"Service for model {model_id} not registered")
            return True
        
        try:
            # Stop the service
            service = self.services[model_id]
            if not service.stop():
                logger.error(f"Failed to stop service for model {model_id}")
                return False
            
            # Unregister the service
            del self.services[model_id]
            
            logger.info(f"Service for model {model_id} unregistered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error unregistering service for model {model_id}: {str(e)}")
            return False
    
    def submit_request(
        self, 
        model_id: str, 
        data: Any, 
        metadata: Optional[Dict[str, Any]] = None,
        timeout_ms: int = 30000
    ) -> str:
        """
        Submit a request to a model service.
        
        Args:
            model_id: Target model ID
            data: Input data for the model
            metadata: Additional request metadata
            timeout_ms: Request timeout in milliseconds
            
        Returns:
            str: Request ID for tracking
        """
        if not self.is_running:
            raise RuntimeError("Service Manager not running")
        
        if model_id not in self.services:
            raise ValueError(f"Service for model {model_id} not registered")
        
        # Create request
        request_id = str(uuid.uuid4())
        request = ModelRequest(
            id=request_id,
            model_id=model_id,
            data=data,
            metadata=metadata or {},
            timeout_ms=timeout_ms
        )
        
        # Add to request queue
        self.request_queue.put(request)
        
        return request_id
    
    def get_response(
        self, 
        request_id: str, 
        timeout: Optional[float] = None
    ) -> Optional[ModelResponse]:
        """
        Get a response for a specific request.
        
        Args:
            request_id: Request ID to get response for
            timeout: Timeout in seconds
            
        Returns:
            ModelResponse or None if not available within timeout
        """
        if not self.is_running:
            raise RuntimeError("Service Manager not running")
        
        start_time = time.time()
        while timeout is None or (time.time() - start_time) < timeout:
            # Check if response is already available
            if request_id in self.response_map:
                response = self.response_map[request_id]
                del self.response_map[request_id]
                return response
            
            # Wait briefly before checking again
            time.sleep(0.01)
        
        return None
    
    def register_response_listener(
        self, 
        request_id: str, 
        listener: Callable[[ModelResponse], None]
    ) -> None:
        """
        Register a listener for a response.
        
        Args:
            request_id: Request ID to listen for
            listener: Callback function to call when response is received
        """
        if request_id not in self.response_listeners:
            self.response_listeners[request_id] = []
        
        self.response_listeners[request_id].append(listener)
    
    def submit_request_async(
        self, 
        model_id: str, 
        data: Any, 
        callback: Callable[[ModelResponse], None],
        metadata: Optional[Dict[str, Any]] = None,
        timeout_ms: int = 30000
    ) -> str:
        """
        Submit a request asynchronously with callback.
        
        Args:
            model_id: Target model ID
            data: Input data for the model
            callback: Callback function to call when response is received
            metadata: Additional request metadata
            timeout_ms: Request timeout in milliseconds
            
        Returns:
            str: Request ID for tracking
        """
        request_id = self.submit_request(model_id, data, metadata, timeout_ms)
        self.register_response_listener(request_id, callback)
        return request_id
    
    def get_service_metrics(self, model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get metrics for model services.
        
        Args:
            model_id: Optional model ID to get metrics for a specific service
            
        Returns:
            Dict containing metrics data
        """
        if model_id:
            if model_id in self.services:
                return self.services[model_id].get_metrics()
            else:
                return {"error": f"Service for model {model_id} not registered"}
        else:
            # Return metrics for all services
            metrics = {}
            for mid, service in self.services.items():
                metrics[mid] = service.get_metrics()
            
            # Add overall metrics
            total_requests = sum(metrics[mid]["request_count"] for mid in metrics)
            total_errors = sum(metrics[mid]["error_count"] for mid in metrics)
            avg_inference_time = 0.0
            if total_requests > 0:
                total_time = sum(
                    metrics[mid]["avg_inference_time_ms"] * metrics[mid]["request_count"]
                    for mid in metrics
                )
                avg_inference_time = total_time / total_requests
            
            metrics["overall"] = {
                "services_count": len(self.services),
                "total_requests": total_requests,
                "total_errors": total_errors,
                "avg_inference_time_ms": avg_inference_time,
                "error_rate": total_errors / max(1, total_requests)
            }
            
            return metrics
    
    def _dispatch_requests(self) -> None:
        """Background thread for dispatching requests to services."""
        logger.info("Request dispatch thread started")
        
        while self.is_running:
            try:
                # Get next request from queue with timeout
                try:
                    request = self.request_queue.get(block=True, timeout=1.0)
                except queue.Empty:
                    continue
                
                # Dispatch request to appropriate service
                model_id = request.model_id
                if model_id in self.services:
                    service = self.services[model_id]
                    
                    # Submit request to service
                    if service.submit_request(request):
                        # Start a thread to wait for response
                        threading.Thread(
                            target=self._wait_for_response,
                            args=(service, request.id),
                            daemon=True
                        ).start()
                    else:
                        # Handle submission failure
                        error_response = ModelResponse(
                            request_id=request.id,
                            model_id=model_id,
                            results=None,
                            error="Failed to submit request to service",
                            metadata=request.metadata.copy()
                        )
                        self._handle_response(error_response)
                else:
                    # Handle missing service
                    error_response = ModelResponse(
                        request_id=request.id,
                        model_id=model_id,
                        results=None,
                        error=f"Service for model {model_id} not registered",
                        metadata=request.metadata.copy()
                    )
                    self._handle_response(error_response)
                
                # Mark task as done
                self.request_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in request dispatch loop: {str(e)}")
                time.sleep(1.0)  # Sleep briefly to prevent rapid logging on error
    
    def _wait_for_response(self, service: ModelService, request_id: str) -> None:
        """
        Wait for a response from a service.
        
        Args:
            service: The model service to wait for
            request_id: Request ID to wait for
        """
        try:
            # Wait for response with timeout
            response = service.get_response(timeout=60.0)  # 60 second timeout
            
            if response is not None:
                # Handle the response
                self._handle_response(response)
            else:
                # Handle timeout
                error_response = ModelResponse(
                    request_id=request_id,
                    model_id=service.model_id,
                    results=None,
                    error="Timeout waiting for response"
                )
                self._handle_response(error_response)
                
        except Exception as e:
            logger.error(f"Error waiting for response from service {service.model_id}: {str(e)}")
            
            # Create error response
            error_response = ModelResponse(
                request_id=request_id,
                model_id=service.model_id,
                results=None,
                error=f"Error getting response: {str(e)}"
            )
            self._handle_response(error_response)
    
    def _handle_response(self, response: ModelResponse) -> None:
        """
        Handle a response from a service.
        
        Args:
            response: The model response
        """
        try:
            # Store response in map
            self.response_map[response.request_id] = response
            
            # Notify any registered listeners
            if response.request_id in self.response_listeners:
                listeners = self.response_listeners[response.request_id]
                for listener in listeners:
                    try:
                        listener(response)
                    except Exception as e:
                        logger.error(f"Error in response listener: {str(e)}")
                
                # Remove listeners after notification
                del self.response_listeners[response.request_id]
            
        except Exception as e:
            logger.error(f"Error handling response: {str(e)}")
    
    def shutdown(self) -> None:
        """Safely shut down the service manager."""
        logger.info("Shutting down Service Manager")
        
        # Stop dispatch thread
        self.is_running = False
        if self.dispatch_thread and self.dispatch_thread.is_alive():
            self.dispatch_thread.join(timeout=5.0)
        
        # Unregister all services
        for model_id in list(self.services.keys()):
            self.unregister_service(model_id)
        
        logger.info("Service Manager shut down successfully")


# Global service manager instance
_service_manager = None

def get_service_manager() -> ServiceManager:
    """Get the global service manager instance."""
    global _service_manager
    if _service_manager is None:
        _service_manager = ServiceManager()
    return _service_manager

def initialize_service_manager() -> bool:
    """Initialize the service manager."""
    manager = get_service_manager()
    return manager.initialize()

def shutdown_service_manager() -> None:
    """Safely shut down the service manager."""
    global _service_manager
    if _service_manager is not None:
        _service_manager.shutdown()
        _service_manager = None
