[{"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:30:38.040989", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:30:38.040962", "success": false, "latency_ms": 2758.1171999918297, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:30:38.045989", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:30:38.045975", "success": false, "latency_ms": 0.01720000000204891, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:30:38.046783", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:30:38.046772", "success": false, "latency_ms": 0.009900002623908222, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:35:38.048042", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:35:38.048012", "success": false, "latency_ms": 0.03929999365936965, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:35:38.049039", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:35:38.049013", "success": false, "latency_ms": 0.027400004910305142, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:35:38.050476", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:35:38.050450", "success": false, "latency_ms": 0.02680000034160912, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:40:38.053083", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:40:38.053026", "success": false, "latency_ms": 0.0678999931551516, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:40:38.056016", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:40:38.055978", "success": false, "latency_ms": 0.03220001235604286, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:40:38.058619", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:40:38.058503", "success": false, "latency_ms": 0.04309997893869877, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:40:38.169127", "result": {"timestamp": "2025-06-24T09:40:38.169075", "total_execution_time_ms": 107.46790000121109, "budget_compliance": false, "error": "cannot import name 'get_config' from 'config.settings' (D:\\script-work\\CORE\\config\\settings.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:45:38.170038", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:45:38.170016", "success": false, "latency_ms": 0.03209998249076307, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:45:38.171044", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:45:38.171033", "success": false, "latency_ms": 0.00890000956133008, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:45:38.172044", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:45:38.172034", "success": false, "latency_ms": 0.009399984264746308, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:50:38.173320", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:50:38.173300", "success": false, "latency_ms": 0.026399997295811772, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:50:38.174294", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:50:38.174285", "success": false, "latency_ms": 0.00790000194683671, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:50:38.175078", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:50:38.175066", "success": false, "latency_ms": 0.007300026481971145, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:55:38.176286", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:55:38.176263", "success": false, "latency_ms": 0.03170000854879618, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:55:38.177517", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:55:38.177507", "success": false, "latency_ms": 0.008800008799880743, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:55:38.178595", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:55:38.178583", "success": false, "latency_ms": 0.009800016414374113, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:55:39.947391", "result": {"timestamp": "2025-06-24T09:55:39.947371", "total_execution_time_ms": 1767.7035999950022, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:00:39.949540", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:00:39.949517", "success": false, "latency_ms": 0.03470000228844583, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:00:39.950945", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:00:39.950931", "success": false, "latency_ms": 0.011000025551766157, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:00:39.952091", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:00:39.952081", "success": false, "latency_ms": 0.008300004992634058, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:05:39.953883", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:05:39.953858", "success": false, "latency_ms": 0.03649998689070344, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:05:39.955879", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:05:39.955862", "success": false, "latency_ms": 0.017200014553964138, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:05:39.957514", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:05:39.957498", "success": false, "latency_ms": 0.01300001167692244, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:10:39.959525", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:10:39.959493", "success": false, "latency_ms": 0.03960001049563289, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:10:39.961926", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:10:39.961913", "success": false, "latency_ms": 0.012200005585327744, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:10:39.963477", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:10:39.963462", "success": false, "latency_ms": 0.011800002539530396, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:10:39.965091", "result": {"timestamp": "2025-06-24T10:10:39.965074", "total_execution_time_ms": 0.017799990018829703, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:15:40.003543", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:15:40.003484", "success": false, "latency_ms": 0.0467000063508749, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:15:40.007303", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:15:40.007285", "success": false, "latency_ms": 0.0171000137925148, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:15:40.010620", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:15:40.010600", "success": false, "latency_ms": 0.01689998316578567, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:20:40.016109", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:20:40.016091", "success": false, "latency_ms": 0.024699984351173043, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:20:40.018565", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:20:40.018548", "success": false, "latency_ms": 0.016000005416572094, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:20:40.020227", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:20:40.020214", "success": false, "latency_ms": 0.012800010154023767, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:25:40.023187", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:25:40.023164", "success": false, "latency_ms": 0.03250001464039087, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:25:40.025189", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:25:40.025178", "success": false, "latency_ms": 0.010399991879239678, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:25:40.026962", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:25:40.026952", "success": false, "latency_ms": 0.008400005754083395, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:25:40.028838", "result": {"timestamp": "2025-06-24T10:25:40.028825", "total_execution_time_ms": 0.01729998621158302, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:30:40.031442", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:30:40.031418", "success": false, "latency_ms": 0.0329999893438071, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:30:40.033723", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:30:40.033710", "success": false, "latency_ms": 0.012900010915473104, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:30:40.035759", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:30:40.035747", "success": false, "latency_ms": 0.012200005585327744, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:35:40.038138", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:35:40.038115", "success": false, "latency_ms": 0.03440000000409782, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:35:40.040546", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:35:40.040535", "success": false, "latency_ms": 0.011500000255182385, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:35:40.042697", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:35:40.042686", "success": false, "latency_ms": 0.010500021744519472, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:40:40.045594", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:40:40.045567", "success": false, "latency_ms": 0.03659998765215278, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:40:40.048269", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:40:40.048256", "success": false, "latency_ms": 0.012399978004395962, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:40:40.050724", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:40:40.050704", "success": false, "latency_ms": 0.01650000922381878, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:40:40.053399", "result": {"timestamp": "2025-06-24T10:40:40.053386", "total_execution_time_ms": 0.01780001912266016, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:45:40.056405", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:45:40.056379", "success": false, "latency_ms": 0.035999983083456755, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:45:40.059234", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:45:40.059221", "success": false, "latency_ms": 0.012500007869675756, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:45:40.061395", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:45:40.061384", "success": false, "latency_ms": 0.010100018698722124, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:50:40.064295", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:50:40.064270", "success": false, "latency_ms": 0.03540000761859119, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:50:40.067181", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:50:40.067163", "success": false, "latency_ms": 0.017200014553964138, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:50:40.070329", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:50:40.070301", "success": false, "latency_ms": 0.02030000905506313, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:55:40.075068", "result": {"ticker": "SPY", "timestamp": "2025-06-24T10:55:40.075045", "success": false, "latency_ms": 0.032100011594593525, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:55:40.077673", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T10:55:40.077662", "success": false, "latency_ms": 0.009500014130026102, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T10:55:40.080060", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T10:55:40.080049", "success": false, "latency_ms": 0.010199990356341004, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T10:55:40.082491", "result": {"timestamp": "2025-06-24T10:55:40.082480", "total_execution_time_ms": 0.012999982573091984, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:00:40.085515", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:00:40.085488", "success": false, "latency_ms": 0.03200001083314419, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:00:40.088512", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:00:40.088501", "success": false, "latency_ms": 0.010599993402138352, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:00:40.091109", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:00:40.091099", "success": false, "latency_ms": 0.00890000956133008, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:05:40.094109", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:05:40.094082", "success": false, "latency_ms": 0.03309999010525644, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:05:40.097425", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:05:40.097405", "success": false, "latency_ms": 0.01850002445280552, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:05:40.100323", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:05:40.100310", "success": false, "latency_ms": 0.01069999416358769, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:10:40.103565", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:10:40.103538", "success": false, "latency_ms": 0.03649998689070344, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:10:40.107171", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:10:40.107149", "success": false, "latency_ms": 0.02710000262595713, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:10:40.112549", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:10:40.112533", "success": false, "latency_ms": 0.01920000067912042, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T11:10:40.117143", "result": {"timestamp": "2025-06-24T11:10:40.117122", "total_execution_time_ms": 0.022299995180219412, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:15:40.123229", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:15:40.123192", "success": false, "latency_ms": 0.03309999010525644, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:15:40.126531", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:15:40.126520", "success": false, "latency_ms": 0.009799987310543656, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:15:40.130937", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:15:40.130904", "success": false, "latency_ms": 0.017999991541728377, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:20:40.135948", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:20:40.135925", "success": false, "latency_ms": 0.03300001844763756, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:20:40.139812", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:20:40.139796", "success": false, "latency_ms": 0.016400008462369442, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:20:40.143080", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:20:40.143065", "success": false, "latency_ms": 0.013600016245618463, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:25:40.147543", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:25:40.147521", "success": false, "latency_ms": 0.03399999695830047, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:25:40.151189", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:25:40.151172", "success": false, "latency_ms": 0.015400000847876072, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:25:40.154480", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:25:40.154464", "success": false, "latency_ms": 0.01479999627918005, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T11:25:40.158084", "result": {"timestamp": "2025-06-24T11:25:40.158068", "total_execution_time_ms": 0.018499995348975062, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:30:40.162724", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:30:40.162676", "success": false, "latency_ms": 0.07650000043213367, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:30:40.170614", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:30:40.170591", "success": false, "latency_ms": 0.02099998528137803, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:30:40.176368", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:30:40.176350", "success": false, "latency_ms": 0.018999999156221747, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:35:40.214553", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:35:40.214533", "success": false, "latency_ms": 0.029999995604157448, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:35:40.217860", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:35:40.217842", "success": false, "latency_ms": 0.009000010322779417, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:35:40.220718", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:35:40.220710", "success": false, "latency_ms": 0.008000002708286047, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:40:40.224347", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:40:40.224325", "success": false, "latency_ms": 0.031899980967864394, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:40:40.227890", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:40:40.227871", "success": false, "latency_ms": 0.01209997572004795, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:40:40.231326", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:40:40.231312", "success": false, "latency_ms": 0.014399993233382702, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T11:40:40.234717", "result": {"timestamp": "2025-06-24T11:40:40.234696", "total_execution_time_ms": 0.02710000262595713, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:45:40.238716", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:45:40.238694", "success": false, "latency_ms": 0.03060000017285347, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:45:40.245779", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:45:40.245763", "success": false, "latency_ms": 0.022000021999701858, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:45:40.250441", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:45:40.250424", "success": false, "latency_ms": 0.017400016076862812, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:50:40.254781", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:50:40.254760", "success": false, "latency_ms": 0.0295999925583601, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:50:40.260511", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:50:40.260490", "success": false, "latency_ms": 0.021000014385208488, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:50:40.265250", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:50:40.265234", "success": false, "latency_ms": 0.018099992303177714, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:55:40.271329", "result": {"ticker": "SPY", "timestamp": "2025-06-24T11:55:40.271283", "success": false, "latency_ms": 0.05880001117475331, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:55:40.279732", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T11:55:40.279714", "success": false, "latency_ms": 0.018499995348975062, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T11:55:40.283965", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T11:55:40.283950", "success": false, "latency_ms": 0.03130000550299883, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T11:55:40.288300", "result": {"timestamp": "2025-06-24T11:55:40.288287", "total_execution_time_ms": 0.015399971744045615, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:00:40.296262", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:00:40.296228", "success": false, "latency_ms": 0.04220000118948519, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:00:40.314713", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:00:40.314689", "success": false, "latency_ms": 0.023299973690882325, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:00:40.322763", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:00:40.322740", "success": false, "latency_ms": 0.022599997464567423, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:05:40.331274", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:05:40.331255", "success": false, "latency_ms": 0.02780000795610249, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:05:40.334971", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:05:40.334962", "success": false, "latency_ms": 0.007800001185387373, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:05:40.338584", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:05:40.338575", "success": false, "latency_ms": 0.007099995855242014, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:10:40.375002", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:10:40.374977", "success": false, "latency_ms": 0.05699999746866524, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:10:40.379516", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:10:40.379504", "success": false, "latency_ms": 0.012299977242946625, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:10:40.383184", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:10:40.383175", "success": false, "latency_ms": 0.008800008799880743, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:10:40.386808", "result": {"timestamp": "2025-06-24T12:10:40.386797", "total_execution_time_ms": 0.01300001167692244, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:15:40.423787", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:15:40.423712", "success": false, "latency_ms": 0.1018000184558332, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:15:40.443539", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:15:40.443503", "success": false, "latency_ms": 0.034500000765547156, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:15:40.450554", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:15:40.450456", "success": false, "latency_ms": 0.06600000779144466, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:20:40.457907", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:20:40.457883", "success": false, "latency_ms": 0.034900003811344504, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:20:40.463269", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:20:40.463258", "success": false, "latency_ms": 0.00999998883344233, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:20:40.467192", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:20:40.467183", "success": false, "latency_ms": 0.007800001185387373, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:25:40.471528", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:25:40.471504", "success": false, "latency_ms": 0.03440000000409782, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:25:40.476430", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:25:40.476411", "success": false, "latency_ms": 0.020700012100860476, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:25:40.482438", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:25:40.482417", "success": false, "latency_ms": 0.022799998987466097, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:25:40.489929", "result": {"timestamp": "2025-06-24T12:25:40.489887", "total_execution_time_ms": 0.02519998815841973, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:30:40.531040", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:30:40.531019", "success": false, "latency_ms": 0.04299997817724943, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:30:40.535377", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:30:40.535365", "success": false, "latency_ms": 0.011199997970834374, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:30:40.539863", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:30:40.539851", "success": false, "latency_ms": 0.012600008631125093, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:35:40.544414", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:35:40.544395", "success": false, "latency_ms": 0.02820001100189984, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:35:40.548746", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:35:40.548737", "success": false, "latency_ms": 0.01409999094903469, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:35:40.552708", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:35:40.552700", "success": false, "latency_ms": 0.006700021913275123, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:40:40.557564", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:40:40.557545", "success": false, "latency_ms": 0.030800001695752144, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:40:40.562537", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:40:40.562525", "success": false, "latency_ms": 0.010499992640689015, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:40:40.566985", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:40:40.566974", "success": false, "latency_ms": 0.011500000255182385, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:40:40.604933", "result": {"timestamp": "2025-06-24T12:40:40.604919", "total_execution_time_ms": 0.03320001997053623, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:45:40.611067", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:45:40.611049", "success": false, "latency_ms": 0.020800012862309813, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:45:40.616146", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:45:40.616122", "success": false, "latency_ms": 0.013100012438371778, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:45:40.621217", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:45:40.621201", "success": false, "latency_ms": 0.015500001609325409, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:50:40.626833", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:50:40.626812", "success": false, "latency_ms": 0.028499984182417393, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:50:40.633516", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:50:40.633492", "success": false, "latency_ms": 0.02509998739697039, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:50:40.643193", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:50:40.643171", "success": false, "latency_ms": 0.022799998987466097, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:55:40.654371", "result": {"ticker": "SPY", "timestamp": "2025-06-24T12:55:40.654350", "success": false, "latency_ms": 0.032100011594593525, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:55:40.659603", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T12:55:40.659592", "success": false, "latency_ms": 0.010099989594891667, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T12:55:40.664753", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T12:55:40.664732", "success": false, "latency_ms": 0.03580001066438854, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T12:55:40.670014", "result": {"timestamp": "2025-06-24T12:55:40.670001", "total_execution_time_ms": 0.015400000847876072, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:00:40.675696", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:00:40.675671", "success": false, "latency_ms": 0.033899996196851134, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:00:40.681651", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:00:40.681637", "success": false, "latency_ms": 0.024500011932104826, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:00:40.686697", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:00:40.686683", "success": false, "latency_ms": 0.01479999627918005, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:05:40.692168", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:05:40.692154", "success": false, "latency_ms": 0.015900004655122757, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:05:40.698044", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:05:40.698028", "success": false, "latency_ms": 0.01759998849593103, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:05:40.703791", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:05:40.703776", "success": false, "latency_ms": 0.01500002690590918, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:10:40.710228", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:10:40.710205", "success": false, "latency_ms": 0.031199975637719035, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:10:40.715953", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:10:40.715943", "success": false, "latency_ms": 0.008500006515532732, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:10:40.721705", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:10:40.721694", "success": false, "latency_ms": 0.01179997343569994, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:10:40.726641", "result": {"timestamp": "2025-06-24T13:10:40.726622", "total_execution_time_ms": 0.012900010915473104, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:15:40.732029", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:15:40.732010", "success": false, "latency_ms": 0.030700000934302807, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:15:40.737741", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:15:40.737728", "success": false, "latency_ms": 0.012299977242946625, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:15:40.743053", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:15:40.743013", "success": false, "latency_ms": 0.014999997802078724, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:20:40.753825", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:20:40.753802", "success": false, "latency_ms": 0.03429999924264848, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:20:40.759784", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:20:40.759770", "success": false, "latency_ms": 0.01610000617802143, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:20:40.765663", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:20:40.765643", "success": false, "latency_ms": 0.02139998832717538, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:25:40.771817", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:25:40.771792", "success": false, "latency_ms": 0.03560000914148986, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:25:40.778958", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:25:40.778941", "success": false, "latency_ms": 0.017099984688684344, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:25:40.786452", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:25:40.786412", "success": false, "latency_ms": 0.0261999957729131, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:25:40.794611", "result": {"timestamp": "2025-06-24T13:25:40.794587", "total_execution_time_ms": 0.027000001864507794, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:30:40.804756", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:30:40.804737", "success": false, "latency_ms": 0.02820001100189984, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:30:40.811135", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:30:40.811113", "success": false, "latency_ms": 0.01920000067912042, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:30:40.818946", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:30:40.818924", "success": false, "latency_ms": 0.021799991372972727, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:35:40.829996", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:35:40.829969", "success": false, "latency_ms": 0.03229998401366174, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:35:40.842122", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:35:40.842098", "success": false, "latency_ms": 0.019599974621087313, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:35:40.850832", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:35:40.850812", "success": false, "latency_ms": 0.018999999156221747, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:40:40.858708", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:40:40.858684", "success": false, "latency_ms": 0.03279998782090843, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:40:40.865162", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:40:40.865148", "success": false, "latency_ms": 0.014399993233382702, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:40:40.871495", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:40:40.871480", "success": false, "latency_ms": 0.015500001609325409, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:40:40.877586", "result": {"timestamp": "2025-06-24T13:40:40.877572", "total_execution_time_ms": 0.017000013031065464, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:45:40.884267", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:45:40.884245", "success": false, "latency_ms": 0.0329999893438071, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:45:40.895757", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:45:40.895734", "success": false, "latency_ms": 0.02169999061152339, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:45:40.903598", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:45:40.903580", "success": false, "latency_ms": 0.018000020645558834, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:50:40.911872", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:50:40.911846", "success": false, "latency_ms": 0.04020001506432891, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:50:40.919547", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:50:40.919532", "success": false, "latency_ms": 0.025999994250014424, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:50:40.926709", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:50:40.926692", "success": false, "latency_ms": 0.017700018361210823, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:55:40.935276", "result": {"ticker": "SPY", "timestamp": "2025-06-24T13:55:40.935254", "success": false, "latency_ms": 0.03200001083314419, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:55:40.942317", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T13:55:40.942304", "success": false, "latency_ms": 0.013500015484169126, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T13:55:40.949166", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T13:55:40.949143", "success": false, "latency_ms": 0.013100012438371778, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T13:55:40.956563", "result": {"timestamp": "2025-06-24T13:55:40.956548", "total_execution_time_ms": 0.01729998621158302, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:00:40.964663", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:00:40.964640", "success": false, "latency_ms": 0.03160000778734684, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:00:40.971757", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:00:40.971742", "success": false, "latency_ms": 0.016400008462369442, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:00:40.978894", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:00:40.978869", "success": false, "latency_ms": 0.01580000389367342, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:05:40.985877", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:05:40.985855", "success": false, "latency_ms": 0.03480000304989517, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:05:40.994134", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:05:40.994107", "success": false, "latency_ms": 0.01650000922381878, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:05:41.001844", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:05:41.001829", "success": false, "latency_ms": 0.014299992471933365, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:10:41.009857", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:10:41.009834", "success": false, "latency_ms": 0.03359999391250312, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:10:41.018267", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:10:41.018244", "success": false, "latency_ms": 0.02549999044276774, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:10:41.025266", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:10:41.025235", "success": false, "latency_ms": 0.01680001150816679, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T14:10:41.034384", "result": {"timestamp": "2025-06-24T14:10:41.034366", "total_execution_time_ms": 0.022799998987466097, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:15:41.041713", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:15:41.041693", "success": false, "latency_ms": 0.022099993657320738, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:15:41.049070", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:15:41.049056", "success": false, "latency_ms": 0.014399993233382702, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:15:41.056204", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:15:41.056192", "success": false, "latency_ms": 0.011299998732283711, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:20:41.063979", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:20:41.063957", "success": false, "latency_ms": 0.02980002318508923, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:20:41.071207", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:20:41.071192", "success": false, "latency_ms": 0.013500015484169126, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:20:41.080812", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:20:41.080787", "success": false, "latency_ms": 0.023800006601959467, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:25:41.091229", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:25:41.091218", "success": false, "latency_ms": 0.014299992471933365, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:25:41.097719", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:25:41.097708", "success": false, "latency_ms": 0.009200011845678091, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:25:41.104838", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:25:41.104825", "success": false, "latency_ms": 0.01170000177808106, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T14:25:41.112390", "result": {"timestamp": "2025-06-24T14:25:41.112378", "total_execution_time_ms": 0.013899989426136017, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:30:41.120172", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:30:41.120161", "success": false, "latency_ms": 0.012200005585327744, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:30:41.127217", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:30:41.127206", "success": false, "latency_ms": 0.009499985026195645, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:30:41.134167", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:30:41.134157", "success": false, "latency_ms": 0.009800016414374113, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:53:04.976080", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:53:04.976063", "success": false, "latency_ms": 0.018399994587525725, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:53:04.998044", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:53:04.998025", "success": false, "latency_ms": 0.01930000144056976, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:53:05.034171", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:53:05.034152", "success": false, "latency_ms": 0.0467000063508749, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:58:05.079278", "result": {"ticker": "SPY", "timestamp": "2025-06-24T14:58:05.079245", "success": false, "latency_ms": 0.030800001695752144, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:58:05.087413", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T14:58:05.087400", "success": false, "latency_ms": 0.01409999094903469, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T14:58:05.095330", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T14:58:05.095313", "success": false, "latency_ms": 0.015400000847876072, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T14:58:05.103626", "result": {"timestamp": "2025-06-24T14:58:05.103613", "total_execution_time_ms": 0.01729998621158302, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:03:05.112894", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:03:05.112855", "success": false, "latency_ms": 0.04020001506432891, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:03:05.121679", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:03:05.121661", "success": false, "latency_ms": 0.01810002140700817, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:03:05.130766", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:03:05.130748", "success": false, "latency_ms": 0.0185999961104244, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:08:05.140248", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:08:05.140228", "success": false, "latency_ms": 0.028600014047697186, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:08:05.147428", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:08:05.147409", "success": false, "latency_ms": 0.009800016414374113, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:08:05.154244", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:08:05.154231", "success": false, "latency_ms": 0.011099997209385037, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:13:05.161762", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:13:05.161737", "success": false, "latency_ms": 0.034900003811344504, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:13:05.171167", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:13:05.171148", "success": false, "latency_ms": 0.01930000144056976, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:13:05.181227", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:13:05.181203", "success": false, "latency_ms": 0.023800006601959467, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T15:13:05.191593", "result": {"timestamp": "2025-06-24T15:13:05.191573", "total_execution_time_ms": 0.025300018023699522, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:18:05.212987", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:18:05.212966", "success": false, "latency_ms": 0.03339999238960445, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:18:05.224375", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:18:05.224359", "success": false, "latency_ms": 0.016799982404336333, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:18:05.233435", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:18:05.233419", "success": false, "latency_ms": 0.017099984688684344, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:23:05.242605", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:23:05.242586", "success": false, "latency_ms": 0.022799998987466097, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:23:05.260175", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:23:05.260151", "success": false, "latency_ms": 0.022199994418770075, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:23:05.269945", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:23:05.269926", "success": false, "latency_ms": 0.019500002963468432, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:28:05.282545", "result": {"ticker": "SPY", "timestamp": "2025-06-24T15:28:05.282512", "success": false, "latency_ms": 0.040000013541430235, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:28:05.295389", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T15:28:05.295345", "success": false, "latency_ms": 0.022299995180219412, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T15:28:05.309623", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T15:28:05.309599", "success": false, "latency_ms": 0.022599997464567423, "quality_score": 0.0, "data_source": "failed", "data_points": 0, "error": "cannot import name 'DataIngestionAgent' from 'agents.data_ingestion_agent' (D:\\script-work\\CORE\\agents\\data_ingestion_agent.py)"}}, {"session_id": "20250624_093035", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T15:28:05.321084", "result": {"timestamp": "2025-06-24T15:28:05.321047", "total_execution_time_ms": 0.02929999027401209, "budget_compliance": false, "error": "cannot import name 'run_analysis' from 'orchestrator' (D:\\script-work\\CORE\\orchestrator.py)", "overall_success": false}}]