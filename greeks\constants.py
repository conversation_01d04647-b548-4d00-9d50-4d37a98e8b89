#!/usr/bin/env python3
"""
Mathematical Constants for Greeks Calculations
Precision standards and bounds validation constants
"""

import math

# Mathematical precision standards
GREEKS_PRECISION_TOLERANCE = 1e-12
STATISTICAL_SIGNIFICANCE_LEVEL = 0.95
ROC_CALCULATION_TOLERANCE = 1e-10
ERROR_PROPAGATION_MAX = 1e-10

# Black-Scholes bounds validation
DELTA_BOUNDS = (-1.0, 1.0)
GAMMA_MIN_BOUND = 0.0
VEGA_MIN_BOUND = 0.0
THETA_MAX_BOUND = 0.0  # Theta should be negative for long positions

# Input validation bounds
MIN_SPOT_PRICE = 1e-6
MAX_SPOT_PRICE = 1e6
MIN_STRIKE_PRICE = 1e-6
MAX_STRIKE_PRICE = 1e6
MIN_TIME_TO_EXPIRY = 1e-6  # ~30 seconds
MAX_TIME_TO_EXPIRY = 10.0  # 10 years
MIN_VOLATILITY = 0.001     # 0.1%
MAX_VOLATILITY = 5.0       # 500%
MIN_RISK_FREE_RATE = -0.1  # -10%
MAX_RISK_FREE_RATE = 1.0   # 100%

# Market parameter defaults (configurable, ticker-agnostic)
DEFAULT_RISK_FREE_RATE = 0.045  # 4.5% - US Treasury rate approximation
DEFAULT_DIVIDEND_YIELD = 0.0    # 0% - most stocks don't pay dividends
DEFAULT_VOLATILITY = 0.20       # 20% - market average volatility

# Performance targets
MAX_EXECUTION_TIME_MS = 200
MEMORY_LIMIT_MB = 100
QUALITY_SCORE_THRESHOLD = 0.999

# Statistical thresholds
Z_SCORE_THRESHOLD = 2.0  # 95% confidence level
ANOMALY_DETECTION_SENSITIVITY = 0.85
ROC_SIGNIFICANCE_THRESHOLD = 1.96  # 95% confidence for ROC

# Mathematical constants
SQRT_2PI = math.sqrt(2 * math.pi)
DAYS_PER_YEAR = 365.25

# Greeks calculation constants
NUMERICAL_DERIVATIVE_EPSILON = 1e-8  # For validation only
BSM_CONVERGENCE_TOLERANCE = 1e-14
MAX_ITERATION_COUNT = 100
