"""
Hyperparameter Optimization Module

This module provides advanced hyperparameter optimization capabilities for ML models,
supporting various optimization strategies including grid search, random search,
Bayesian optimization, and evolutionary algorithms.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import sys
import logging
import time
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
from datetime import datetime
import random
import multiprocessing
from functools import partial

# Try to import optional dependencies
try:
    import optuna
    HAS_OPTUNA = True
except ImportError:
    HAS_OPTUNA = False

try:
    from skopt import gp_minimize, forest_minimize
    from skopt.space import Real, Integer, Categorical
    HAS_SKOPT = True
except ImportError:
    HAS_SKOPT = False

# Internal imports
from ml_logging import get_logger

# Setup logger
logger = get_logger('hyperparameter_optimization')

class HyperparameterOptimizer:
    """
    Advanced hyperparameter optimizer for machine learning models.

    This class provides methods for optimizing model hyperparameters using
    various search strategies, including grid search, random search,
    Bayesian optimization, and evolutionary algorithms.
    """

    def __init__(self,
                config: Optional[Dict[str, Any]] = None,
                model_type: str = 'sklearn',
                search_strategy: str = 'bayesian',
                param_space: Optional[Dict[str, Any]] = None,
                n_trials: int = 50,
                cv_folds: int = 5,
                scoring_metric: str = 'neg_mean_squared_error',
                direction: str = 'maximize',
                n_jobs: int = -1,
                random_state: int = 42,
                verbose: bool = True):
        """
        Initialize the hyperparameter optimizer.

        Args:
            config: Optional configuration dictionary
            model_type: Type of model ('sklearn', 'pytorch', 'tensorflow', 'xgboost')
            search_strategy: Search strategy ('grid', 'random', 'bayesian', 'evolutionary')
            param_space: Parameter space definition
            n_trials: Number of trials for optimization
            cv_folds: Number of cross-validation folds
            scoring_metric: Metric to optimize
            direction: Direction of optimization ('maximize' or 'minimize')
            n_jobs: Number of parallel jobs (-1 for all available)
            random_state: Random state for reproducibility
            verbose: Whether to print progress
        """
        self.config = config or {}
        self.model_type = model_type
        self.search_strategy = search_strategy
        self.param_space = param_space or {}
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.scoring_metric = scoring_metric
        self.direction = direction
        self.random_state = random_state
        self.verbose = verbose

        # Set number of jobs
        if n_jobs == -1:
            self.n_jobs = multiprocessing.cpu_count()
        else:
            self.n_jobs = n_jobs

        # Results storage
        self.results = []
        self.best_params = None
        self.best_score = float('-inf') if direction == 'maximize' else float('inf')
        self.best_model = None
        self.optimization_history = []

        # Performance tracking
        self.start_time = None
        self.end_time = None
        self.total_time = None

        # Check if required libraries are available
        if search_strategy == 'bayesian' and not HAS_OPTUNA and not HAS_SKOPT:
            logger.warning("Neither Optuna nor Scikit-Optimize is available. Falling back to random search.")
            self.search_strategy = 'random'

        logger.info(f"Initialized HyperparameterOptimizer with {search_strategy} strategy")

    def optimize(self,
                model_class: Any,
                X_train: Union[pd.DataFrame, np.ndarray],
                y_train: Union[pd.Series, np.ndarray],
                X_val: Optional[Union[pd.DataFrame, np.ndarray]] = None,
                y_val: Optional[Union[pd.Series, np.ndarray]] = None,
                custom_objective: Optional[Callable] = None) -> Dict[str, Any]:
        """
        Optimize hyperparameters for the given model and data.

        Args:
            model_class: Model class to optimize
            X_train: Training features
            y_train: Training targets
            X_val: Validation features (optional)
            y_val: Validation targets (optional)
            custom_objective: Custom objective function (optional)

        Returns:
            Dictionary with optimization results
        """
        self.start_time = time.time()

        # Store data for use in optimization
        self.model_class = model_class
        self.X_train = X_train
        self.y_train = y_train
        self.X_val = X_val
        self.y_val = y_val
        self.custom_objective = custom_objective

        # Run optimization based on selected strategy
        if self.search_strategy == 'grid':
            self._run_grid_search()
        elif self.search_strategy == 'random':
            self._run_random_search()
        elif self.search_strategy == 'bayesian':
            if HAS_OPTUNA:
                self._run_optuna_optimization()
            elif HAS_SKOPT:
                self._run_skopt_optimization()
            else:
                logger.warning("Bayesian optimization libraries not available. Using random search.")
                self._run_random_search()
        elif self.search_strategy == 'evolutionary':
            self._run_evolutionary_search()
        else:
            raise ValueError(f"Unknown search strategy: {self.search_strategy}")

        # Calculate total time
        self.end_time = time.time()
        self.total_time = self.end_time - self.start_time

        # Log results
        logger.info(f"Hyperparameter optimization completed in {self.total_time:.2f} seconds")
        logger.info(f"Best parameters: {self.best_params}")
        logger.info(f"Best score: {self.best_score:.6f}")

        # Return results
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'best_model': self.best_model,
            'results': self.results,
            'optimization_history': self.optimization_history,
            'total_time': self.total_time
        }

    def _evaluate_params(self, params: Dict[str, Any]) -> float:
        """
        Evaluate a set of parameters.

        Args:
            params: Parameters to evaluate

        Returns:
            Score for the parameters
        """
        try:
            # If custom objective is provided, use it
            if self.custom_objective is not None:
                return self.custom_objective(params, self.model_class,
                                           self.X_train, self.y_train,
                                           self.X_val, self.y_val)

            # Otherwise, use default evaluation
            if self.X_val is not None and self.y_val is not None:
                # Use validation data if provided
                return self._evaluate_with_validation(params)
            else:
                # Use cross-validation
                return self._evaluate_with_cv(params)

        except Exception as e:
            logger.error(f"Error evaluating parameters {params}: {str(e)}")
            # Return worst possible score
            return float('-inf') if self.direction == 'maximize' else float('inf')

    def _evaluate_with_validation(self, params: Dict[str, Any]) -> float:
        """
        Evaluate parameters using validation data.

        Args:
            params: Parameters to evaluate

        Returns:
            Validation score
        """
        # Create and train model
        model = self.model_class(**params)
        model.fit(self.X_train, self.y_train)

        # Evaluate on validation data
        if hasattr(model, 'score'):
            score = model.score(self.X_val, self.y_val)
        else:
            # For models without score method, use MSE
            from sklearn.metrics import mean_squared_error
            y_pred = model.predict(self.X_val)
            score = -mean_squared_error(self.y_val, y_pred)

        return score

    def _evaluate_with_cv(self, params: Dict[str, Any]) -> float:
        """
        Evaluate parameters using cross-validation.

        Args:
            params: Parameters to evaluate

        Returns:
            Cross-validation score
        """
        from sklearn.model_selection import cross_val_score

        # Create model
        model = self.model_class(**params)

        # Perform cross-validation
        cv_scores = cross_val_score(
            model, self.X_train, self.y_train,
            cv=self.cv_folds,
            scoring=self.scoring_metric,
            n_jobs=self.n_jobs
        )

        # Return mean score
        return cv_scores.mean()

    def _run_grid_search(self) -> None:
        """
        Run grid search optimization.
        """
        logger.info("Running grid search optimization")

        # Generate all parameter combinations
        param_keys = list(self.param_space.keys())
        param_values = list(self.param_space.values())

        # Generate all combinations
        import itertools
        param_combinations = list(itertools.product(*param_values))

        logger.info(f"Generated {len(param_combinations)} parameter combinations")

        # Evaluate each combination
        for i, combination in enumerate(param_combinations):
            params = {param_keys[j]: combination[j] for j in range(len(param_keys))}

            # Log progress
            if self.verbose and (i+1) % 5 == 0:
                logger.info(f"Evaluating combination {i+1}/{len(param_combinations)}")

            # Evaluate parameters
            score = self._evaluate_params(params)

            # Store result
            self.results.append((params, score))
            self.optimization_history.append({'params': params, 'score': score, 'iteration': i})

            # Update best parameters
            if ((self.direction == 'maximize' and score > self.best_score) or
                (self.direction == 'minimize' and score < self.best_score)):
                self.best_score = score
                self.best_params = params

                # Train best model
                self.best_model = self.model_class(**params)
                self.best_model.fit(self.X_train, self.y_train)

                if self.verbose:
                    logger.info(f"New best score: {score:.6f}, params: {params}")

    def _run_random_search(self) -> None:
        """
        Run random search optimization.
        """
        logger.info("Running random search optimization")

        # Set random seed for reproducibility
        np.random.seed(self.random_state)
        random.seed(self.random_state)

        # Run for specified number of trials
        for i in range(self.n_trials):
            # Sample random parameters
            params = self._sample_params()

            # Log progress
            if self.verbose and (i+1) % 5 == 0:
                logger.info(f"Evaluating trial {i+1}/{self.n_trials}")

            # Evaluate parameters
            score = self._evaluate_params(params)

            # Store result
            self.results.append((params, score))
            self.optimization_history.append({'params': params, 'score': score, 'iteration': i})

            # Update best parameters
            if ((self.direction == 'maximize' and score > self.best_score) or
                (self.direction == 'minimize' and score < self.best_score)):
                self.best_score = score
                self.best_params = params

                # Train best model
                self.best_model = self.model_class(**params)
                self.best_model.fit(self.X_train, self.y_train)

                if self.verbose:
                    logger.info(f"New best score: {score:.6f}, params: {params}")

    def _sample_params(self) -> Dict[str, Any]:
        """
        Sample random parameters from the parameter space.

        Returns:
            Dictionary of sampled parameters
        """
        params = {}

        for param_name, param_values in self.param_space.items():
            if isinstance(param_values, list):
                # Categorical parameter
                params[param_name] = random.choice(param_values)
            elif isinstance(param_values, tuple) and len(param_values) == 3:
                # Numerical parameter (min, max, type)
                min_val, max_val, param_type = param_values

                if param_type == 'int':
                    params[param_name] = random.randint(min_val, max_val)
                elif param_type == 'float':
                    params[param_name] = min_val + random.random() * (max_val - min_val)
                else:
                    raise ValueError(f"Unknown parameter type: {param_type}")
            else:
                raise ValueError(f"Invalid parameter space definition for {param_name}")

        return params

    def _run_optuna_optimization(self) -> None:
        """
        Run Bayesian optimization using Optuna.
        """
        if not HAS_OPTUNA:
            logger.error("Optuna is not available")
            return

        logger.info("Running Bayesian optimization with Optuna")

        # Define objective function for Optuna
        def objective(trial):
            # Sample parameters
            params = {}

            for param_name, param_values in self.param_space.items():
                if isinstance(param_values, list):
                    # Categorical parameter
                    params[param_name] = trial.suggest_categorical(param_name, param_values)
                elif isinstance(param_values, tuple) and len(param_values) == 3:
                    # Numerical parameter (min, max, type)
                    min_val, max_val, param_type = param_values

                    if param_type == 'int':
                        params[param_name] = trial.suggest_int(param_name, min_val, max_val)
                    elif param_type == 'float':
                        params[param_name] = trial.suggest_float(param_name, min_val, max_val)
                    else:
                        raise ValueError(f"Unknown parameter type: {param_type}")
                else:
                    raise ValueError(f"Invalid parameter space definition for {param_name}")

            # Evaluate parameters
            score = self._evaluate_params(params)

            # Store in optimization history
            self.optimization_history.append({
                'params': params,
                'score': score,
                'iteration': len(self.optimization_history)
            })

            return score if self.direction == 'maximize' else -score

        # Create study
        study_direction = 'maximize' if self.direction == 'maximize' else 'minimize'
        study = optuna.create_study(direction=study_direction)

        # Run optimization
        study.optimize(objective, n_trials=self.n_trials, n_jobs=self.n_jobs)

        # Get best parameters
        best_trial = study.best_trial
        self.best_params = best_trial.params
        self.best_score = best_trial.value if self.direction == 'maximize' else -best_trial.value

        # Train best model
        self.best_model = self.model_class(**self.best_params)
        self.best_model.fit(self.X_train, self.y_train)

        # Store all results
        for trial in study.trials:
            params = trial.params
            score = trial.value if self.direction == 'maximize' else -trial.value
            self.results.append((params, score))

        logger.info(f"Optuna optimization completed. Best score: {self.best_score:.6f}")

    def _run_skopt_optimization(self) -> None:
        """
        Run Bayesian optimization using Scikit-Optimize.
        """
        if not HAS_SKOPT:
            logger.error("Scikit-Optimize is not available")
            return

        logger.info("Running Bayesian optimization with Scikit-Optimize")

        # Define search space for skopt
        space = []
        param_names = []

        for param_name, param_values in self.param_space.items():
            param_names.append(param_name)

            if isinstance(param_values, list):
                # Categorical parameter
                space.append(Categorical(param_values, name=param_name))
            elif isinstance(param_values, tuple) and len(param_values) == 3:
                # Numerical parameter (min, max, type)
                min_val, max_val, param_type = param_values

                if param_type == 'int':
                    space.append(Integer(min_val, max_val, name=param_name))
                elif param_type == 'float':
                    space.append(Real(min_val, max_val, name=param_name))
                else:
                    raise ValueError(f"Unknown parameter type: {param_type}")
            else:
                raise ValueError(f"Invalid parameter space definition for {param_name}")

        # Define objective function for skopt
        def objective(params_list):
            # Convert list to dictionary
            params = {param_names[i]: params_list[i] for i in range(len(param_names))}

            # Evaluate parameters
            score = self._evaluate_params(params)

            # Store in optimization history
            self.optimization_history.append({
                'params': params,
                'score': score,
                'iteration': len(self.optimization_history)
            })

            return -score if self.direction == 'maximize' else score

        # Run optimization
        result = gp_minimize(
            objective,
            space,
            n_calls=self.n_trials,
            random_state=self.random_state,
            verbose=self.verbose
        )

        # Get best parameters
        best_params_list = result.x
        self.best_params = {param_names[i]: best_params_list[i] for i in range(len(param_names))}
        self.best_score = -result.fun if self.direction == 'maximize' else result.fun

        # Train best model
        self.best_model = self.model_class(**self.best_params)
        self.best_model.fit(self.X_train, self.y_train)

        # Store all results
        for i, params_list in enumerate(result.x_iters):
            params = {param_names[j]: params_list[j] for j in range(len(param_names))}
            score = -result.func_vals[i] if self.direction == 'maximize' else result.func_vals[i]
            self.results.append((params, score))

        logger.info(f"Scikit-Optimize optimization completed. Best score: {self.best_score:.6f}")

    def _run_evolutionary_search(self) -> None:
        """
        Run evolutionary algorithm for hyperparameter optimization.
        """
        logger.info("Running evolutionary search optimization")

        # Set random seed for reproducibility
        np.random.seed(self.random_state)
        random.seed(self.random_state)

        # Population size and number of generations
        population_size = min(50, self.n_trials // 2)
        n_generations = self.n_trials // population_size

        logger.info(f"Running with population size {population_size} for {n_generations} generations")

        # Generate initial population
        population = []
        scores = []

        for _ in range(population_size):
            params = self._sample_params()
            score = self._evaluate_params(params)

            population.append(params)
            scores.append(score)

            # Store result
            self.results.append((params, score))
            self.optimization_history.append({
                'params': params,
                'score': score,
                'iteration': len(self.optimization_history)
            })

        # Run evolution
        for generation in range(n_generations):
            if self.verbose:
                logger.info(f"Generation {generation+1}/{n_generations}")

            # Select parents (tournament selection)
            parents = self._tournament_selection(population, scores)

            # Create offspring through crossover and mutation
            offspring = []

            for _ in range(population_size):
                # Select two parents
                parent1, parent2 = random.sample(parents, 2)

                # Crossover
                child = self._crossover(parent1, parent2)

                # Mutation
                child = self._mutate(child)

                offspring.append(child)

            # Evaluate offspring
            offspring_scores = []

            for params in offspring:
                score = self._evaluate_params(params)
                offspring_scores.append(score)

                # Store result
                self.results.append((params, score))
                self.optimization_history.append({
                    'params': params,
                    'score': score,
                    'iteration': len(self.optimization_history)
                })

                # Update best parameters
                if ((self.direction == 'maximize' and score > self.best_score) or
                    (self.direction == 'minimize' and score < self.best_score)):
                    self.best_score = score
                    self.best_params = params

                    # Train best model
                    self.best_model = self.model_class(**params)
                    self.best_model.fit(self.X_train, self.y_train)

                    if self.verbose:
                        logger.info(f"New best score: {score:.6f}, params: {params}")

            # Replace population with offspring
            population = offspring
            scores = offspring_scores

    def _tournament_selection(self, population: List[Dict[str, Any]], scores: List[float]) -> List[Dict[str, Any]]:
        """
        Tournament selection for evolutionary algorithm.

        Args:
            population: List of parameter dictionaries
            scores: List of scores

        Returns:
            Selected parents
        """
        tournament_size = 3
        n_parents = len(population) // 2

        parents = []

        for _ in range(n_parents):
            # Select random individuals for tournament
            indices = random.sample(range(len(population)), tournament_size)

            # Find best individual in tournament
            if self.direction == 'maximize':
                best_idx = indices[np.argmax([scores[i] for i in indices])]
            else:
                best_idx = indices[np.argmin([scores[i] for i in indices])]

            parents.append(population[best_idx])

        return parents

    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Crossover operation for evolutionary algorithm.

        Args:
            parent1: First parent parameters
            parent2: Second parent parameters

        Returns:
            Child parameters
        """
        child = {}

        for param_name in parent1.keys():
            # Randomly select from either parent
            if random.random() < 0.5:
                child[param_name] = parent1[param_name]
            else:
                child[param_name] = parent2[param_name]

        return child

    def _mutate(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Mutation operation for evolutionary algorithm.

        Args:
            params: Parameters to mutate

        Returns:
            Mutated parameters
        """
        mutated = params.copy()

        # Mutation probability
        mutation_prob = 0.2

        for param_name, param_value in params.items():
            # Decide whether to mutate this parameter
            if random.random() < mutation_prob:
                param_space = self.param_space[param_name]

                if isinstance(param_space, list):
                    # Categorical parameter
                    mutated[param_name] = random.choice(param_space)
                elif isinstance(param_space, tuple) and len(param_space) == 3:
                    # Numerical parameter (min, max, type)
                    min_val, max_val, param_type = param_space

                    if param_type == 'int':
                        # Add or subtract a small integer
                        delta = random.choice([-2, -1, 1, 2])
                        new_value = param_value + delta
                        mutated[param_name] = max(min_val, min(max_val, new_value))
                    elif param_type == 'float':
                        # Multiply by a factor close to 1
                        factor = random.uniform(0.8, 1.2)
                        new_value = param_value * factor
                        mutated[param_name] = max(min_val, min(max_val, new_value))

        return mutated
