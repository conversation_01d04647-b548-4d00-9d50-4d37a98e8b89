#!/bin/bash
# B-Series Backtest Pipeline Driver - Unix/Linux
# Executes complete backtest pipeline for specified tickers

echo "==============================================="
echo "B-Series Backtest Pipeline"
echo "==============================================="

# Default parameters
TICKERS="AAPL"
SOURCE="polygon"
MODEL="random_forest"
CAPITAL="100000"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --tickers)
            TICKERS="$2"
            shift 2
            ;;
        --source)
            SOURCE="$2"
            shift 2
            ;;
        --model)
            MODEL="$2"
            shift 2
            ;;
        --capital)
            CAPITAL="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

echo "Tickers: $TICKERS"
echo "Source: $SOURCE"
echo "Model: $MODEL"
echo "Capital: $CAPITAL"
echo

# Execute batch pipeline
python -m tasks.run_backtest_batch --tickers $TICKERS --source $SOURCE --model $MODEL --capital $CAPITAL --verbose

if [ $? -eq 0 ]; then
    echo
    echo "==============================================="
    echo "Pipeline completed successfully"
    echo "==============================================="
else
    echo
    echo "==============================================="
    echo "Pipeline failed with errors"
    echo "==============================================="
    exit 1
fi
