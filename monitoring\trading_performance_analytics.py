#!/usr/bin/env python3
"""
Trading Performance Analytics Monitor
P&L tracking, risk metrics, execution quality, and strategy performance measurement
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json
import logging

@dataclass
class TradePerformance:
    """Individual trade performance metrics"""
    trade_id: str
    ticker: str
    strategy: str
    entry_time: datetime
    exit_time: Optional[datetime]
    entry_price: float
    exit_price: Optional[float]
    position_size: float
    direction: str  # 'long', 'short'
    pnl_realized: float
    pnl_unrealized: float
    commission: float
    slippage: float
    hold_time_minutes: int
    roi_percentage: float
    risk_reward_achieved: float

@dataclass
class StrategyMetrics:
    """Strategy performance metrics"""
    strategy_name: str
    total_trades: int
    winning_trades: int
    losing_trades: int
    win_rate: float
    avg_win: float
    avg_loss: float
    max_win: float
    max_loss: float
    profit_factor: float
    sharpe_ratio: float
    max_drawdown: float
    total_pnl: float
    avg_hold_time: float
    success_rate: float

@dataclass
class RiskMetrics:
    """Risk management metrics"""
    timestamp: datetime
    portfolio_value: float
    total_exposure: float
    available_buying_power: float
    margin_used: float
    var_95: float  # Value at Risk 95%
    var_99: float  # Value at Risk 99%
    beta: float
    correlation_spy: float
    concentration_risk: float
    leverage_ratio: float
    max_position_size: float

@dataclass
class ExecutionQuality:
    """Execution quality metrics"""
    timestamp: datetime
    avg_slippage: float
    fill_rate: float
    avg_fill_time: float
    price_improvement: float
    market_impact: float
    execution_shortfall: float
    broker_latency: float

class TradingPerformanceAnalytics:
    """
    Comprehensive trading performance analytics
    Mathematical precision in performance measurement and risk analysis
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.history_days = self.config.get('history_days', 30)
        self.risk_free_rate = self.config.get('risk_free_rate', 0.02)  # 2% annual
        
        # Performance data storage
        self.trade_history = []
        self.strategy_performance = {}
        self.daily_pnl = deque(maxlen=252)  # 1 year of trading days
        self.risk_metrics_history = deque(maxlen=1000)
        self.execution_quality_history = deque(maxlen=1000)
        
        # Real-time tracking
        self.current_positions = {}
        self.daily_stats = {
            'trades_today': 0,
            'pnl_today': 0.0,
            'volume_today': 0.0,
            'commissions_today': 0.0
        }
        
        # Risk monitoring
        self.risk_limits = self.config.get('risk_limits', {
            'max_portfolio_risk': 0.02,  # 2% max portfolio risk
            'max_position_size': 0.10,   # 10% max position size
            'max_drawdown': 0.15,        # 15% max drawdown
            'max_correlation': 0.80,     # 80% max correlation
            'var_limit': 0.05           # 5% VaR limit
        })
        
        self.logger = logging.getLogger(__name__)
    
    def record_trade(self, trade_data: Dict[str, Any]):
        """Record a completed trade for performance tracking"""
        try:
            trade = TradePerformance(
                trade_id=trade_data['trade_id'],
                ticker=trade_data['ticker'],
                strategy=trade_data.get('strategy', 'unknown'),
                entry_time=trade_data['entry_time'],
                exit_time=trade_data.get('exit_time'),
                entry_price=trade_data['entry_price'],
                exit_price=trade_data.get('exit_price'),
                position_size=trade_data['position_size'],
                direction=trade_data['direction'],
                pnl_realized=trade_data.get('pnl_realized', 0.0),
                pnl_unrealized=trade_data.get('pnl_unrealized', 0.0),
                commission=trade_data.get('commission', 0.0),
                slippage=trade_data.get('slippage', 0.0),
                hold_time_minutes=trade_data.get('hold_time_minutes', 0),
                roi_percentage=trade_data.get('roi_percentage', 0.0),
                risk_reward_achieved=trade_data.get('risk_reward_achieved', 0.0)
            )
            
            self.trade_history.append(trade)
            
            # Update daily stats
            self.daily_stats['trades_today'] += 1
            self.daily_stats['pnl_today'] += trade.pnl_realized
            self.daily_stats['volume_today'] += abs(trade.position_size * trade.entry_price)
            self.daily_stats['commissions_today'] += trade.commission
            
            # Update strategy performance
            self._update_strategy_metrics(trade)
            
            self.logger.info(f"Recorded trade: {trade.trade_id} - PnL: ${trade.pnl_realized:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")
    
    def update_position(self, ticker: str, position_data: Dict[str, Any]):
        """Update current position for real-time tracking"""
        self.current_positions[ticker] = {
            'position_size': position_data['position_size'],
            'entry_price': position_data['entry_price'],
            'current_price': position_data['current_price'],
            'unrealized_pnl': position_data.get('unrealized_pnl', 0.0),
            'strategy': position_data.get('strategy', 'unknown'),
            'entry_time': position_data.get('entry_time', datetime.now())
        }
    
    def calculate_portfolio_pnl(self, current_prices: Dict[str, float]) -> Dict[str, float]:
        """Calculate real-time portfolio P&L"""
        total_unrealized = 0.0
        total_realized = sum(trade.pnl_realized for trade in self.trade_history 
                           if trade.entry_time.date() == datetime.now().date())
        
        # Calculate unrealized P&L for current positions
        for ticker, position in self.current_positions.items():
            if ticker in current_prices:
                current_price = current_prices[ticker]
                entry_price = position['entry_price']
                position_size = position['position_size']
                
                if position_size > 0:  # Long position
                    unrealized = (current_price - entry_price) * position_size
                else:  # Short position
                    unrealized = (entry_price - current_price) * abs(position_size)
                
                total_unrealized += unrealized
                position['unrealized_pnl'] = unrealized
        
        return {
            'total_pnl': total_realized + total_unrealized,
            'realized_pnl': total_realized,
            'unrealized_pnl': total_unrealized,
            'daily_pnl': total_realized + total_unrealized
        }
    
    def calculate_risk_metrics(self, portfolio_value: float, 
                              current_prices: Dict[str, float],
                              benchmark_return: float = 0.0) -> RiskMetrics:
        """Calculate comprehensive risk metrics"""
        
        # Portfolio exposure
        total_exposure = sum(abs(pos['position_size'] * current_prices.get(ticker, pos['current_price']))
                           for ticker, pos in self.current_positions.items())
        
        # Calculate returns for VaR
        returns = self._calculate_portfolio_returns(current_prices)
        
        # Value at Risk calculations
        if len(returns) >= 30:
            var_95 = np.percentile(returns, 5) * portfolio_value
            var_99 = np.percentile(returns, 1) * portfolio_value
        else:
            var_95 = 0.0
            var_99 = 0.0
        
        # Beta calculation (simplified)
        beta = self._calculate_portfolio_beta(returns, benchmark_return)
        
        # Concentration risk
        concentration_risk = self._calculate_concentration_risk(current_prices)
        
        # Leverage ratio
        leverage_ratio = total_exposure / portfolio_value if portfolio_value > 0 else 0.0
        
        # Maximum position size
        max_position_size = max((abs(pos['position_size'] * current_prices.get(ticker, pos['current_price'])) / portfolio_value
                               for ticker, pos in self.current_positions.items()), default=0.0)
        
        risk_metrics = RiskMetrics(
            timestamp=datetime.now(),
            portfolio_value=portfolio_value,
            total_exposure=total_exposure,
            available_buying_power=portfolio_value - total_exposure,
            margin_used=max(0, total_exposure - portfolio_value),
            var_95=var_95,
            var_99=var_99,
            beta=beta,
            correlation_spy=0.0,  # Simplified
            concentration_risk=concentration_risk,
            leverage_ratio=leverage_ratio,
            max_position_size=max_position_size
        )
        
        self.risk_metrics_history.append(risk_metrics)
        return risk_metrics
    
    def calculate_execution_quality(self, execution_data: List[Dict]) -> ExecutionQuality:
        """Calculate execution quality metrics"""
        if not execution_data:
            return ExecutionQuality(
                timestamp=datetime.now(),
                avg_slippage=0.0, fill_rate=0.0, avg_fill_time=0.0,
                price_improvement=0.0, market_impact=0.0,
                execution_shortfall=0.0, broker_latency=0.0
            )
        
        # Calculate metrics
        slippages = [trade.get('slippage', 0.0) for trade in execution_data]
        fill_times = [trade.get('fill_time', 0.0) for trade in execution_data]
        fills = [1 if trade.get('filled', False) else 0 for trade in execution_data]
        
        avg_slippage = np.mean(slippages) if slippages else 0.0
        fill_rate = np.mean(fills) if fills else 0.0
        avg_fill_time = np.mean(fill_times) if fill_times else 0.0
        
        # Price improvement (positive slippage)
        price_improvement = np.mean([s for s in slippages if s < 0]) if slippages else 0.0
        
        # Market impact estimation
        market_impact = np.mean([abs(s) for s in slippages]) if slippages else 0.0
        
        execution_quality = ExecutionQuality(
            timestamp=datetime.now(),
            avg_slippage=avg_slippage,
            fill_rate=fill_rate,
            avg_fill_time=avg_fill_time,
            price_improvement=abs(price_improvement),
            market_impact=market_impact,
            execution_shortfall=avg_slippage,
            broker_latency=avg_fill_time
        )
        
        self.execution_quality_history.append(execution_quality)
        return execution_quality
    
    def _update_strategy_metrics(self, trade: TradePerformance):
        """Update strategy-specific performance metrics"""
        strategy = trade.strategy
        
        if strategy not in self.strategy_performance:
            self.strategy_performance[strategy] = {
                'trades': [],
                'total_pnl': 0.0,
                'winning_trades': 0,
                'losing_trades': 0
            }
        
        strat = self.strategy_performance[strategy]
        strat['trades'].append(trade)
        strat['total_pnl'] += trade.pnl_realized
        
        if trade.pnl_realized > 0:
            strat['winning_trades'] += 1
        else:
            strat['losing_trades'] += 1
    
    def calculate_strategy_metrics(self, strategy_name: str) -> Optional[StrategyMetrics]:
        """Calculate comprehensive metrics for a specific strategy"""
        if strategy_name not in self.strategy_performance:
            return None
        
        trades = self.strategy_performance[strategy_name]['trades']
        if not trades:
            return None
        
        # Basic counts
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t.pnl_realized > 0])
        losing_trades = total_trades - winning_trades
        
        # Win rate
        win_rate = winning_trades / total_trades if total_trades > 0 else 0.0
        
        # Average win/loss
        wins = [t.pnl_realized for t in trades if t.pnl_realized > 0]
        losses = [t.pnl_realized for t in trades if t.pnl_realized <= 0]
        
        avg_win = np.mean(wins) if wins else 0.0
        avg_loss = np.mean(losses) if losses else 0.0
        max_win = max(wins) if wins else 0.0
        max_loss = min(losses) if losses else 0.0
        
        # Profit factor
        total_wins = sum(wins) if wins else 0.0
        total_losses = abs(sum(losses)) if losses else 0.0
        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')
        
        # Sharpe ratio
        returns = [t.pnl_realized for t in trades]
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        
        # Maximum drawdown
        max_drawdown = self._calculate_max_drawdown([t.pnl_realized for t in trades])
        
        # Total P&L
        total_pnl = sum(t.pnl_realized for t in trades)
        
        # Average hold time
        hold_times = [t.hold_time_minutes for t in trades if t.hold_time_minutes > 0]
        avg_hold_time = np.mean(hold_times) if hold_times else 0.0
        
        # Success rate (ROI > 0)
        successful_trades = len([t for t in trades if t.roi_percentage > 0])
        success_rate = successful_trades / total_trades if total_trades > 0 else 0.0
        
        return StrategyMetrics(
            strategy_name=strategy_name,
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            win_rate=win_rate,
            avg_win=avg_win,
            avg_loss=avg_loss,
            max_win=max_win,
            max_loss=max_loss,
            profit_factor=profit_factor,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            total_pnl=total_pnl,
            avg_hold_time=avg_hold_time,
            success_rate=success_rate
        )
    
    def _calculate_portfolio_returns(self, current_prices: Dict[str, float]) -> List[float]:
        """Calculate portfolio returns for risk metrics"""
        # Simplified portfolio return calculation
        if len(self.daily_pnl) < 2:
            return []
        
        returns = []
        for i in range(1, len(self.daily_pnl)):
            if self.daily_pnl[i-1] != 0:
                ret = (self.daily_pnl[i] - self.daily_pnl[i-1]) / abs(self.daily_pnl[i-1])
                returns.append(ret)
        
        return returns
    
    def _calculate_portfolio_beta(self, returns: List[float], benchmark_return: float) -> float:
        """Calculate portfolio beta"""
        if len(returns) < 30:
            return 1.0  # Default beta
        
        # Simplified beta calculation
        portfolio_var = np.var(returns)
        market_var = 0.04  # Assumed market variance
        covariance = np.cov(returns, [benchmark_return] * len(returns))[0][1]
        
        return covariance / market_var if market_var != 0 else 1.0
    
    def _calculate_concentration_risk(self, current_prices: Dict[str, float]) -> float:
        """Calculate portfolio concentration risk"""
        if not self.current_positions:
            return 0.0
        
        total_value = sum(abs(pos['position_size'] * current_prices.get(ticker, pos['current_price']))
                         for ticker, pos in self.current_positions.items())
        
        if total_value == 0:
            return 0.0
        
        # Calculate Herfindahl index
        weights = []
        for ticker, pos in self.current_positions.items():
            weight = abs(pos['position_size'] * current_prices.get(ticker, pos['current_price'])) / total_value
            weights.append(weight)
        
        hhi = sum(w**2 for w in weights)
        return hhi
    
    def _calculate_sharpe_ratio(self, returns: List[float]) -> float:
        """Calculate Sharpe ratio"""
        if len(returns) < 2:
            return 0.0
        
        avg_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        # Annualized Sharpe ratio
        daily_rf_rate = (1 + self.risk_free_rate) ** (1/252) - 1
        return (avg_return - daily_rf_rate) / std_return * np.sqrt(252)
    
    def _calculate_max_drawdown(self, returns: List[float]) -> float:
        """Calculate maximum drawdown"""
        if not returns:
            return 0.0
        
        cumulative = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max)
        
        return abs(min(drawdown)) if len(drawdown) > 0 else 0.0
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'daily_stats': self.daily_stats.copy(),
            'total_trades': len(self.trade_history),
            'strategies': {}
        }
        
        # Strategy summaries
        for strategy_name in self.strategy_performance.keys():
            metrics = self.calculate_strategy_metrics(strategy_name)
            if metrics:
                summary['strategies'][strategy_name] = asdict(metrics)
        
        # Risk metrics
        if self.risk_metrics_history:
            latest_risk = self.risk_metrics_history[-1]
            summary['risk_metrics'] = asdict(latest_risk)
        
        # Execution quality
        if self.execution_quality_history:
            latest_execution = self.execution_quality_history[-1]
            summary['execution_quality'] = asdict(latest_execution)
        
        return summary
    
    def check_risk_limits(self) -> List[str]:
        """Check if any risk limits are breached"""
        violations = []
        
        if not self.risk_metrics_history:
            return violations
        
        latest_risk = self.risk_metrics_history[-1]
        
        # Check position size limit
        if latest_risk.max_position_size > self.risk_limits['max_position_size']:
            violations.append(f"Position size limit exceeded: {latest_risk.max_position_size:.1%}")
        
        # Check VaR limit
        if abs(latest_risk.var_95) > self.risk_limits['var_limit'] * latest_risk.portfolio_value:
            violations.append(f"VaR limit exceeded: ${abs(latest_risk.var_95):.2f}")
        
        # Check concentration risk
        if latest_risk.concentration_risk > 0.5:  # 50% concentration threshold
            violations.append(f"High concentration risk: {latest_risk.concentration_risk:.1%}")
        
        return violations

if __name__ == "__main__":
    # Test the performance analytics
    analytics = TradingPerformanceAnalytics()
    
    # Record sample trade
    sample_trade = {
        'trade_id': 'TEST001',
        'ticker': 'AAPL',
        'strategy': 'liquidity_sweep',
        'entry_time': datetime.now() - timedelta(hours=2),
        'exit_time': datetime.now(),
        'entry_price': 150.00,
        'exit_price': 152.50,
        'position_size': 100,
        'direction': 'long',
        'pnl_realized': 250.0,
        'commission': 2.0,
        'slippage': 0.05,
        'hold_time_minutes': 120,
        'roi_percentage': 1.67,
        'risk_reward_achieved': 2.5
    }
    
    analytics.record_trade(sample_trade)
    
    # Get performance summary
    summary = analytics.get_performance_summary()
    print(json.dumps(summary, indent=2, default=str))
