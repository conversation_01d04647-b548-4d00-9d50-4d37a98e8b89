#!/usr/bin/env python3
"""
Schwab Production API Module
Direct interface to Schwab API for real-time data access
Mathematical rigor: 100% error handling, zero-tolerance failure
"""

import json
import logging
import time
import requests
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import os
from pathlib import Path
import urllib.request
import urllib.parse
import base64
import threading
import gzip
import zlib

logger = logging.getLogger(__name__)

class SchwabAPIError(Exception):
    """Custom exception for Schwab API errors"""
    def __init__(self, message: str, error_code: str = None, status_code: int = None):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code

@dataclass
class Quote:
    symbol: str
    price: float
    bid: float
    ask: float
    volume: int
    change: float
    change_percent: float
    timestamp: int

@dataclass
class Account:
    account_number: str
    account_type: str
    buying_power: float
    total_value: float

class TokenManager:
    """Handles automatic token refresh and validation"""
    
    def __init__(self, config_path: str = None, token_path: str = None):
        self.config_path = config_path or self._get_default_config_path()
        self.token_path = token_path or self._get_default_token_path()
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """Get default config path"""
        return "D:/script-work/CORE/SCHWAB_MCP_PRODUCTION/config/config.json"
    
    def _get_default_token_path(self) -> str:
        """Get default token path"""
        return "D:/script-work/CORE/SCHWAB_MCP_PRODUCTION/config/schwab_token.json"
    
    def _load_config(self):
        try:
            with open(self.config_path, 'r') as f:
                config = json.load(f)
            self.app_key = config['app_key']
            self.app_secret = config['app_secret']
            self.callback_url = config['callback_url']
        except Exception as e:
            logger.warning(f"Could not load config: {e}")
            # Set defaults
            self.app_key = os.getenv('SCHWAB_APP_KEY', '')
            self.app_secret = os.getenv('SCHWAB_APP_SECRET', '')
            self.callback_url = os.getenv('SCHWAB_CALLBACK_URL', '')

    def get_valid_token(self) -> str:
        """Get valid access token, refreshing if necessary"""
        try:
            with open(self.token_path, 'r') as f:
                token_data = json.load(f)
            
            current_time = time.time()
            expires_at = token_data.get('expires_at', 0)
            
            # If token expires within 5 minutes, refresh it
            if current_time >= (expires_at - 300):
                logger.info("Token expiring soon, refreshing...")
                self._refresh_token(token_data)
                
                # Reload token data
                with open(self.token_path, 'r') as f:
                    token_data = json.load(f)
            
            return token_data['access_token']
            
        except FileNotFoundError:
            raise SchwabAPIError("Token file not found. Please authenticate first.", "NO_TOKEN")
        except KeyError as e:
            raise SchwabAPIError(f"Invalid token file format: missing {e}", "INVALID_TOKEN")
        except Exception as e:
            raise SchwabAPIError(f"Token validation error: {e}", "TOKEN_ERROR")
    
    def _refresh_token(self, token_data: Dict):
        """Refresh access token using refresh token"""
        try:
            refresh_token = token_data.get('refresh_token')
            if not refresh_token:
                raise SchwabAPIError("No refresh token available", "NO_REFRESH_TOKEN")
            
            # Prepare refresh request
            auth_header = base64.b64encode(f"{self.app_key}:{self.app_secret}".encode()).decode()
            
            headers = {
                'Authorization': f'Basic {auth_header}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = urllib.parse.urlencode({
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }).encode()
            
            request = urllib.request.Request(
                'https://api.schwabapi.com/v1/oauth/token',
                data=data,
                headers=headers,
                method='POST'
            )
            
            with urllib.request.urlopen(request, timeout=30) as response:
                if response.getcode() == 200:
                    new_token_data = json.loads(response.read().decode())
                    
                    # Update token data
                    token_data.update({
                        'access_token': new_token_data['access_token'],
                        'expires_at': time.time() + new_token_data.get('expires_in', 1800)
                    })
                    
                    # Save updated token
                    with open(self.token_path, 'w') as f:
                        json.dump(token_data, f, indent=2)
                        
                else:
                    raise SchwabAPIError("Token refresh failed", "REFRESH_FAILED")
                    
        except Exception as e:
            raise SchwabAPIError(f"Token refresh error: {e}", "REFRESH_ERROR")

class SchwabAPI:
    """Production Schwab API client with full trading capabilities"""
    
    def __init__(self):
        self.token_manager = TokenManager()
        self.session_lock = threading.RLock()
        self.request_count = 0
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms rate limiting
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def _make_request(self, endpoint: str, method: str = 'GET', data: Dict = None, params: Dict = None) -> Dict:
        """Make authenticated API request with rate limiting"""
        with self.session_lock:
            # Rate limiting
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_request_interval:
                time.sleep(self.min_request_interval - time_since_last)
            
            # Get valid token
            access_token = self.token_manager.get_valid_token()
            
            # Build URL
            url = f"https://api.schwabapi.com{endpoint}"
            if params:
                query_string = urllib.parse.urlencode(params)
                url = f"{url}?{query_string}"
                
            # Prepare headers
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Accept': 'application/json',
                'Accept-Encoding': 'gzip, deflate'
            }
            
            # Prepare data
            request_data = None
            if data and method in ['POST', 'PUT', 'PATCH']:
                request_data = json.dumps(data).encode()
                headers['Content-Type'] = 'application/json'
                
            try:
                request = urllib.request.Request(url, data=request_data, headers=headers)
                request.get_method = lambda: method.upper()
                
                with urllib.request.urlopen(request, timeout=30) as response:
                    self.last_request_time = time.time()
                    self.request_count += 1
                    
                    if response.getcode() in [200, 201]:
                        response_data = response.read()
                        
                        # Enhanced response decoding
                        content_encoding = response.getheader('Content-Encoding', '').lower()
                        
                        # Handle compression
                        if content_encoding == 'gzip' or response_data.startswith(b'\x1f\x8b'):
                            response_data = gzip.decompress(response_data)
                        elif content_encoding == 'deflate':
                            response_data = zlib.decompress(response_data)
                        
                        # Decode to text with fallback
                        try:
                            response_text = response_data.decode('utf-8')
                        except UnicodeDecodeError:
                            try:
                                response_text = response_data.decode('latin-1')
                            except UnicodeDecodeError:
                                response_text = response_data.decode('utf-8', errors='ignore')
                        
                        return json.loads(response_text) if response_text else {}
                    else:
                        raise SchwabAPIError(f"HTTP {response.getcode()}", "HTTP_ERROR")
                        
            except urllib.error.HTTPError as e:
                error_body = e.read().decode()
                raise SchwabAPIError(f"API Error: {e.code} - {error_body}", "API_ERROR")
            except Exception as e:
                raise SchwabAPIError(f"Request failed: {e}", "REQUEST_FAILED")
    
    def get_accounts(self) -> List[Account]:
        """Get account information"""
        try:
            response = self._make_request("/trader/v1/accounts")
            accounts = []
            
            for account_data in response:
                accounts.append(Account(
                    account_number=account_data.get('accountNumber', ''),
                    account_type=account_data.get('type', ''),
                    buying_power=float(account_data.get('currentBalances', {}).get('buyingPower', 0)),
                    total_value=float(account_data.get('currentBalances', {}).get('liquidationValue', 0))
                ))
                
            return accounts
            
        except SchwabAPIError:
            raise
        except Exception as e:
            raise SchwabAPIError(f"Failed to get accounts: {e}", "GET_ACCOUNTS_FAILED")
    
    def get_quote(self, symbol: str) -> Quote:
        """Get real-time quote for symbol"""
        try:
            endpoint = f"/marketdata/v1/{symbol.upper()}/quotes"
            response = self._make_request(endpoint)
            
            quote_data = response.get(symbol.upper(), {})

            # Extract quote information from the correct nested structure
            quote_section = quote_data.get('quote', {})

            return Quote(
                symbol=symbol.upper(),
                price=float(quote_section.get('lastPrice', 0)),
                bid=float(quote_section.get('bidPrice', 0)),
                ask=float(quote_section.get('askPrice', 0)),
                volume=int(quote_section.get('totalVolume', 0)),
                change=float(quote_section.get('netChange', 0)),
                change_percent=float(quote_section.get('netPercentChange', 0)),
                timestamp=int(time.time())
            )
            
        except SchwabAPIError:
            raise
        except Exception as e:
            raise SchwabAPIError(f"Failed to get quote for {symbol}: {e}", "GET_QUOTE_FAILED")
    
    def get_multiple_quotes(self, symbols: List[str]) -> Dict[str, Quote]:
        """Get quotes for multiple symbols efficiently"""
        try:
            symbol_string = ','.join([s.upper() for s in symbols])
            endpoint = "/marketdata/v1/quotes"
            params = {'symbols': symbol_string}
            response = self._make_request(endpoint, params=params)
            
            quotes = {}
            for symbol, quote_data in response.items():
                quotes[symbol] = Quote(
                    symbol=symbol,
                    price=float(quote_data.get('last', 0)),
                    bid=float(quote_data.get('bid', 0)),
                    ask=float(quote_data.get('ask', 0)),
                    volume=int(quote_data.get('totalVolume', 0)),
                    change=float(quote_data.get('netChange', 0)),
                    change_percent=float(quote_data.get('netPercentChange', 0)),
                    timestamp=int(time.time())
                )
                
            return quotes
            
        except SchwabAPIError:
            raise
        except Exception as e:
            raise SchwabAPIError(f"Failed to get multiple quotes: {e}", "GET_MULTIPLE_QUOTES_FAILED")
    
    def get_price_history(self, symbol: str, period_type: str = "year", period: int = 1, 
                         frequency_type: str = "daily", frequency: int = 1) -> List[Dict]:
        """Get historical price data"""
        try:
            params = {
                'symbol': symbol.upper(),
                'periodType': period_type,
                'period': period,
                'frequencyType': frequency_type,
                'frequency': frequency
            }
            
            response = self._make_request("/marketdata/v1/pricehistory", params=params)
            return response.get('candles', [])
            
        except SchwabAPIError:
            raise
        except Exception as e:
            raise SchwabAPIError(f"Failed to get price history for {symbol}: {e}", "GET_PRICE_HISTORY_FAILED")
    
    def get_option_chains(self, symbol: str, contract_type: str = "ALL", 
                         strike_count: int = 10, include_quotes: bool = True) -> Dict[str, Any]:
        """
        Get option chains for a symbol
        
        Args:
            symbol: Underlying symbol
            contract_type: 'CALL', 'PUT', or 'ALL'
            strike_count: Number of strikes to return
            include_quotes: Include quote data
            
        Returns:
            Option chain data
        """
        endpoint = f"/marketdata/v1/chains"
        
        params = {
            'symbol': symbol.upper(),
            'contractType': contract_type,
            'strikeCount': strike_count,
            'includeQuotes': include_quotes
        }
        
        try:
            logger.info(f"Fetching option chains for {symbol}")
            response = self._make_request("GET", endpoint, params=params)
            
            logger.info(f"Retrieved option chains for {symbol}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to get option chains for {symbol}: {e}")
            raise
    
    def option_chain(self, symbol: str, strikes: int = 10, days: int = 15) -> Dict[str, Any]:
        """
        Enhanced option chain fetcher with Greeks support
        Drop-in replacement for options data with better performance
        
        Args:
            symbol: Underlying symbol (e.g., 'AAPL')
            strikes: Number of strikes around ATM (default: 10)
            days: Days to expiration filter (default: 15)
            
        Returns:
            Full option chain data with Greeks, IV, and pricing
        """
        # Use standard chains endpoint with enhanced parameters
        return self.get_option_chains(symbol, contract_type="ALL", strike_count=strikes, include_quotes=True)

# Production client wrapper for MCP integration
class SchwabProductionClient:
    """Simplified production client optimized for MCP server integration"""
    
    def __init__(self):
        self.api = SchwabAPI()
        self.logger = logging.getLogger(__name__)
        
    def health_check(self) -> Dict:
        """System health check"""
        try:
            accounts = self.api.get_accounts()
            return {
                "status": "healthy",
                "accounts": len(accounts),
                "token_valid": True,
                "timestamp": time.time()
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_real_time_quote(self, symbol: str) -> Dict:
        """Get real-time quote optimized for MCP response"""
        try:
            quote = self.api.get_quote(symbol)
            return {
                "symbol": quote.symbol,
                "price": quote.price,
                "bid": quote.bid,
                "ask": quote.ask,
                "volume": quote.volume,
                "change": quote.change,
                "change_percent": quote.change_percent,
                "timestamp": quote.timestamp
            }
        except Exception as e:
            self.logger.error(f"Quote error for {symbol}: {e}")
            raise

# Convenience functions for backward compatibility
def get_price_history(*args, **kwargs):
    """Convenience function for price history"""
    api = SchwabAPI()
    return api.get_price_history(*args, **kwargs)

def get_quotes(*args, **kwargs):
    """Convenience function for quotes"""
    api = SchwabAPI()
    return api.get_multiple_quotes(*args, **kwargs)

def get_option_chains(*args, **kwargs):
    """Convenience function for option chains"""
    api = SchwabAPI()
    return api.get_option_chains(*args, **kwargs)

if __name__ == "__main__":
    # Production validation
    client = SchwabProductionClient()
    health = client.health_check()
    print(f"Schwab Production Client: {health['status']}")
