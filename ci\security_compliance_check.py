#!/usr/bin/env python3
"""
Security Compliance Check - Agent Standards Validation
Validates agents follow security patterns from docs/SECURITY_STANDARDS.md
"""

import ast
import os
import sys
from pathlib import Path
import re

class SecurityComplianceChecker:
    """Validates agent security compliance"""
    
    def __init__(self):
        self.violations = []
        self.patterns = {
            'correlation_id': r'X-Correlation-ID',
            'user_agent': r'User-Agent.*CORE-Flow-Detection',
            'env_var_usage': r'os\.getenv\(',
            'hardcoded_secrets': r'(api_key|secret|token|password)\s*=\s*["\'][^"\']*["\']'
        }
    
    def check_file(self, file_path):
        """Check single Python file for security compliance"""
        violations = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for hardcoded secrets
            if re.search(self.patterns['hardcoded_secrets'], content, re.IGNORECASE):
                violations.append(f"Hardcoded secrets detected in {file_path}")
            
            # Check for HTTP requests without proper headers
            if 'requests.' in content and 'post(' in content:
                if not re.search(self.patterns['correlation_id'], content):
                    violations.append(f"Missing X-Correlation-ID header in {file_path}")
                if not re.search(self.patterns['user_agent'], content):
                    violations.append(f"Missing proper User-Agent header in {file_path}")
            
            # Check for environment variable usage
            if any(keyword in content.lower() for keyword in ['api', 'secret', 'token', 'key']):
                if not re.search(self.patterns['env_var_usage'], content):
                    violations.append(f"Potential secret not using environment variables in {file_path}")
        
        except Exception as e:
            violations.append(f"Error reading {file_path}: {e}")
        
        return violations
    
    def check_agents_directory(self):
        """Check all agent files for compliance"""
        agents_dir = Path("agents")
        if not agents_dir.exists():
            return ["Agents directory not found"]
        
        violations = []
        for py_file in agents_dir.rglob("*.py"):
            file_violations = self.check_file(py_file)
            violations.extend(file_violations)
        
        return violations
    
    def check_env_template(self):
        """Validate .env.example exists and contains required keys"""
        env_example = Path(".env.example")
        if not env_example.exists():
            return [".env.example template missing"]
        
        required_keys = ['SCHWAB_API_KEY', 'SCHWAB_API_SECRET']
        violations = []
        
        try:
            with open(env_example, 'r') as f:
                content = f.read()
            
            for key in required_keys:
                if key not in content:
                    violations.append(f"Missing {key} in .env.example")
        
        except Exception as e:
            violations.append(f"Error reading .env.example: {e}")
        
        return violations
    
    def run_full_check(self):
        """Run complete security compliance check"""
        print("Running security compliance check...")
        
        all_violations = []
        
        # Check agents
        agent_violations = self.check_agents_directory()
        all_violations.extend(agent_violations)
        
        # Check environment template
        env_violations = self.check_env_template()
        all_violations.extend(env_violations)
        
        # Report results
        if all_violations:
            print(f"\n[FAIL] Security compliance FAILED ({len(all_violations)} violations):")
            for violation in all_violations:
                print(f"  - {violation}")
            return False
        else:
            print("\n[OK] Security compliance PASSED")
            return True

if __name__ == "__main__":
    checker = SecurityComplianceChecker()
    success = checker.run_full_check()
    sys.exit(0 if success else 1)
