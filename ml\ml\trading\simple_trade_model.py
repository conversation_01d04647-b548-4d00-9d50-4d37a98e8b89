#!/usr/bin/env python3
"""
Simple fallback trade model classes for execution tree
"""

class TradeSignal:
    """Simple trade signal for position sizing"""
    def __init__(self):
        self.position_size = 0.0
        self.confidence = 0.0
        self.direction = 'neutral'

class MarketRegime:
    """Simple market regime for position sizing"""
    def __init__(self):
        self.volatility = 0.0
        self.trend = 'neutral'
