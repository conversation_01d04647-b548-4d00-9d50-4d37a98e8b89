#!/usr/bin/env python3
"""
Dependency Drift Detection - Mathematical Precision Guard
Validates numerical package versions against production lock
"""

import importlib.metadata
import sys
from pathlib import Path

CRITICAL_PACKAGES = {
    'numpy': '1.24.3',
    'scipy': '1.10.1', 
    'pandas': '2.0.3'
}

def check_version_drift():
    """Detect version drift in critical numerical packages"""
    drift_detected = False
    
    for package, expected_version in CRITICAL_PACKAGES.items():
        try:
            installed_version = importlib.metadata.version(package)
            if installed_version != expected_version:
                print(f"DRIFT DETECTED: {package} {installed_version} != {expected_version}")
                drift_detected = True
            else:
                print(f"OK: {package} {installed_version}")
        except importlib.metadata.PackageNotFoundError:
            print(f"MISSING: {package} not installed")
            drift_detected = True
    
    if drift_detected:
        print("\nERROR: Version drift detected - numerical precision at risk")
        print("Run: pip install -r requirements_lock.txt")
        return False
    
    print("\nALL CLEAR: No version drift detected")
    return True

if __name__ == "__main__":
    success = check_version_drift()
    sys.exit(0 if success else 1)
