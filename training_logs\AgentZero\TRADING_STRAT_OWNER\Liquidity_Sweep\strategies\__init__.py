"""
Strategy Framework for Liquidity Sweep System

This package contains all professional trading strategy implementations designed for
institutional-grade liquidity analysis and options flow trading. Each strategy
implements rigorous mathematical models with real-time data integration.

Core Strategy Implementations:
- BaseStrategy: Abstract base class and framework interfaces
- PureLiquidityStrategy: Multi-factor liquidity confluence analysis
- GammaSqueezeStrategy: Greeks-based squeeze detection and trading
- LiquiditySweepStrategy: Support/resistance liquidity sweep patterns
- FlowMomentumStrategy: Institutional flow momentum analysis
- LiquidityVoidStrategy: Low-volume zone momentum trading
- VannaCharmSqueezeStrategy: Higher-order Greeks positioning
- EnhancedFlowPhysicsStrategy: Advanced flow physics modeling

Strategy Factory:
- StrategyFactory: Dynamic strategy instantiation and management

Author: VIRT Trading Team
Version: 2.0.0 (Production Ready)
Dependencies: Real-time market data, BSM model, unified API gateway
"""

# Package metadata
__version__ = "2.0.0"
__author__ = "VIRT Trading Team"
__package_type__ = "strategies"
__dependency_level__ = "production"

# Strategy registry for validation
__strategy_registry__ = {
    'BaseStrategy': 'Abstract base class and framework',
    'PureLiquidityStrategy': 'Multi-factor liquidity confluence analysis',
    'GammaSqueezeStrategy': 'Greeks-based squeeze detection',
    'LiquiditySweepStrategy': 'Liquidity sweep pattern trading',
    'FlowMomentumStrategy': 'Institutional flow momentum',
    'LiquidityVoidStrategy': 'Low-volume zone trading',
    'VannaCharmSqueezeStrategy': 'Higher-order Greeks positioning',
    'EnhancedFlowPhysicsStrategy': 'Advanced flow physics modeling'
}

# Dynamic path resolution for absolute imports
import sys
import os
from pathlib import Path

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

# Import core strategy components using absolute imports
from strategies.base_strategy import BaseStrategy, StrategySignal, SignalDirection, SignalStrength
from strategies.pure_liquidity_strategy import PureLiquidityStrategy
from strategies.gamma_squeeze_strategy import GammaSqueezeStrategy
from strategies.flow_momentum_strategy import FlowMomentumStrategy
from strategies.liquidity_sweep_strategy import LiquiditySweepStrategy
from strategies.liquidity_void_strategy import LiquidityVoidStrategy
from strategies.vanna_charm_squeeze_strategy import VannaCharmSqueezeStrategy
from strategies.enhanced_flow_physics_strategy import EnhancedFlowPhysicsStrategy

# Import factory
from strategies.strategy_factory import StrategyFactory, get_strategy_factory

__all__ = [
    # Base classes and enums
    'BaseStrategy',
    'StrategySignal',
    'SignalDirection',
    'SignalStrength',

    # Strategy implementations (7 total)
    'PureLiquidityStrategy',
    'GammaSqueezeStrategy',
    'FlowMomentumStrategy',
    'LiquiditySweepStrategy',
    'LiquidityVoidStrategy',
    'VannaCharmSqueezeStrategy',
    'EnhancedFlowPhysicsStrategy',

    # Factory
    'StrategyFactory',
    'get_strategy_factory'
]

def get_strategy_info():
    """
    Get comprehensive strategy package information.
    
    Returns:
        dict: Strategy metadata and registry information
    """
    return {
        'version': __version__,
        'author': __author__,
        'package_type': __package_type__,
        'dependency_level': __dependency_level__,
        'strategy_count': len(__strategy_registry__),
        'strategies': __strategy_registry__,
        'available_strategies': list(__strategy_registry__.keys())
    }

def validate_strategy_package():
    """
    Validate strategy package structure and dependencies.
    
    Returns:
        dict: Validation results with status and details
    """
    validation_results = {
        'package_valid': True,
        'strategies_loaded': 0,
        'errors': [],
        'warnings': []
    }
    
    # Check if all strategies can be imported
    for strategy_name in __strategy_registry__.keys():
        if strategy_name == 'BaseStrategy':
            continue  # Skip base class
        try:
            strategy_class = globals().get(strategy_name)
            if strategy_class:
                validation_results['strategies_loaded'] += 1
            else:
                validation_results['errors'].append(f"Strategy {strategy_name} not loaded")
                validation_results['package_valid'] = False
        except Exception as e:
            validation_results['errors'].append(f"Error loading {strategy_name}: {e}")
            validation_results['package_valid'] = False
    
    return validation_results