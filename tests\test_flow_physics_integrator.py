import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch

# Adjust path to import from the CORE project structure
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from Flow_Physics_Engine.engine_core.flow_physics_integrator import FlowPhysicsIntegrator, FlowPhysicsResult
from Flow_Physics_Engine.enhanced_csid_analyzer import EnhancedCSIDResult

# Mock the advanced analyzers since they are not the focus of this integration test
MOCK_ADVANCED_ANALYZERS = {
    'Flow_Physics_Engine.engine_core.flow_physics_integrator.AdvancedVelocityAnalyzer': MagicMock(),
    'Flow_Physics_Engine.engine_core.flow_physics_integrator.AdvancedAccelerationAnalyzer': MagicMock(),
    'Flow_Physics_Engine.engine_core.flow_physics_integrator.FlowJerkAnalyzer': MagicMock(),
}

@pytest.fixture
def integrator():
    """Fixture to create a FlowPhysicsIntegrator instance."""
    return FlowPhysicsIntegrator(config={})

def create_mock_flow_data(num_points=25):
    """Helper to create sample flow data."""
    now = datetime.now()
    timestamps = [now - timedelta(minutes=i) for i in range(num_points)][::-1]
    flows = [1000 + i * 10 + ((-1)**i * 5) for i in range(num_points)]
    
    # The method expects a dictionary for the latest point
    latest_point = {'timestamp': timestamps[-1], 'flow_value': flows[-1]}
    
    return latest_point

def create_mock_price_data(num_points=50):
    """Helper to create sample price data for CSID."""
    now = datetime.now()
    timestamps = [now - timedelta(minutes=i) for i in range(num_points)][::-1]
    return pd.DataFrame({
        'timestamp': timestamps,
        'open': [100 + i * 0.1 for i in range(num_points)],
        'high': [100.5 + i * 0.1 for i in range(num_points)],
        'low': [99.5 + i * 0.1 for i in range(num_points)],
        'close': [100.2 + i * 0.1 for i in range(num_points)],
        'volume': [10000 + i * 100 for i in range(num_points)]
    })

@patch.dict('sys.modules', MOCK_ADVANCED_ANALYZERS)
class TestFlowPhysicsIntegratorCSID:

    def test_analyze_with_csid_integration(self, integrator, mocker):
        """
        Test that analyze_flow_physics correctly integrates a valid CSID result.
        """
        # --- Arrange ---
        symbol = "SPY"
        flow_data = create_mock_flow_data()
        price_data = create_mock_price_data()

        # Mock the EnhancedCSIDAnalyzer instance and its method
        mock_csid_analyzer = MagicMock()
        
        # This is the expected output from the CSID analyzer
        mock_csid_result = EnhancedCSIDResult(
            timestamp=datetime.now(), symbol=symbol, cumulative_volume_delta=12345.6,
            cvd_velocity=15.5, cvd_acceleration=-2.1, cvd_momentum=500.0,
            order_flow_imbalance=0.6, stealth_retail_ratio=2.5, imbalance_strength=0.6,
            flow_persistence=0.8, institutional_stealth_score=0.75, stealth_periods=10,
            retail_fomo_periods=2, institutional_bias='accumulation', smart_money_index=0.82,
            smart_money_direction='stealth_accumulation', accumulation_distribution=0.9,
            stealth_participation=0.6, flow_z_score=2.1, imbalance_significance=0.98,
            trend_consistency=0.88, data_quality_score=0.95, calculation_confidence=0.9,
            error_bounds=(-0.1, 0.1), statistical_significance=0.01
        )
        mock_csid_analyzer.calculate_enhanced_csid.return_value = mock_csid_result
        
        # Patch the integrator's csid_analyzer instance
        integrator.csid_analyzer = mock_csid_analyzer

        # --- Act ---
        result = integrator.analyze_flow_physics(symbol, flow_data, price_data)

        # --- Assert ---
        mock_csid_analyzer.calculate_enhanced_csid.assert_called_once_with(symbol, price_data)

        assert isinstance(result, FlowPhysicsResult)
        assert result.symbol == symbol
        assert result.csid_cumulative_delta == 12345.6
        assert result.csid_velocity == 15.5
        assert result.institutional_bias == 'accumulation'
        assert result.smart_money_flow == 0.82
        assert result.csid_institutional_divergence == 0.75
        assert result.quality_score > 0.5 # Quality score should be boosted by good CSID confidence
        assert result.institutional_direction == 'accumulation' # CSID should influence this
        assert result.csid_divergence_detected is True # abs(0.75) > 0.3

    def test_analyze_without_price_data(self, integrator, mocker):
        """
        Test that analyze_flow_physics handles cases where no price_data is provided,
        and thus CSID analysis is skipped.
        """
        # --- Arrange ---
        symbol = "QQQ"
        flow_data = create_mock_flow_data()
        mock_csid_analyzer = MagicMock()
        integrator.csid_analyzer = mock_csid_analyzer

        # --- Act ---
        result = integrator.analyze_flow_physics(symbol, flow_data, price_data=None)

        # --- Assert ---
        mock_csid_analyzer.calculate_enhanced_csid.assert_not_called()
        assert isinstance(result, FlowPhysicsResult)
        assert result.csid_cumulative_delta == 0.0
        assert result.institutional_bias == 'neutral'
        assert result.csid_divergence_detected is False

    def test_analyze_with_csid_returning_none(self, integrator, mocker):
        """
        Test that the system handles a None result from the CSID analyzer gracefully.
        """
        # --- Arrange ---
        symbol = "TSLA"
        flow_data = create_mock_flow_data()
        price_data = create_mock_price_data()
        mock_csid_analyzer = MagicMock(calculate_enhanced_csid=MagicMock(return_value=None))
        integrator.csid_analyzer = mock_csid_analyzer

        # --- Act ---
        result = integrator.analyze_flow_physics(symbol, flow_data, price_data)

        # --- Assert ---
        mock_csid_analyzer.calculate_enhanced_csid.assert_called_once_with(symbol, price_data)
        assert isinstance(result, FlowPhysicsResult)
        assert result.csid_cumulative_delta == 0.0
        assert result.institutional_bias == 'neutral'
        assert result.csid_divergence_detected is False