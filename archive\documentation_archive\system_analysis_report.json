{"analysis_timestamp": "2025-06-24T18:17:16.061862", "total_files_analyzed": 615, "active_production_files": 15, "files_for_archive": 595, "categories": {"active_production": ["agent_zero_advanced_capabilities.py", "agent_zero_integration_hub.py", "agent_zero_performance_analytics.py", "enhanced_data_agent_broker_integration.py", "live_market_test.py", "main.py", "orchestrator.py", "ultimate_orchestrator.py", "agents\\__init__.py", "analyzers\\__init__.py", "api\\__init__.py", "data\\__init__.py", "engine\\__init__.py", "tasks\\__init__.py", "utils\\__init__.py"], "test_files": ["comprehensive_mcp_test_results_20250622_225017.json", "comprehensive_mcp_test_results_20250622_225132.json", "comprehensive_mcp_test_results_20250622_225240.json", "comprehensive_mcp_test_results_20250622_230306.json", "comprehensive_test.py", "final_validation_test.py", "live_test_checklist.py", "mcp_integration_test_results_20250622_223934.json", "mcp_integration_test_results_20250622_224059.json", "mcp_integration_test_results_20250622_224349.json", "quick_mcp_test.py", "run_backtest_pipeline.bat", "run_backtest_pipeline.sh", "test_agent_army_live.py", "test_agent_framework.py", "test_anomaly_detection.py", "test_b01_integration.py", "test_b01_ticker_agnostic.py", "test_bounds_fix.py", "test_broker_integration.py", "test_chart_generator.py", "test_comprehensive_mcp_integration.py", "test_core_system.py", "test_enhanced_agent.py", "test_enhanced_greeks_architecture.py", "test_final_integration.py", "test_greek_features.py", "test_integrated_system.py", "test_iv_dynamics.py", "test_live_mcp_integration.py", "test_math_validator.py", "test_mcp_agent_integration.py", "test_mcp_connection.py", "test_mcp_correct_port.py", "test_mcp_final.py", "test_mcp_integration.py", "test_mcp_wire.py", "test_multi_orchestrator.py", "test_original_agent_fix.py", "test_phase15_enhancements.py", "test_protocol_compliance.py", "test_protocol_simple.py", "test_results_20250614_110352.json", "test_results_20250614_170359.json", "test_results_20250614_170845.json", "test_schwab_options.py", "test_schwab_production.py", "test_schwab_validation.py", "test_signal_generator.py", "test_signal_quality.py", "test_simple_integration.py", "test_system.py", "test_trading_math.py", "test_trading_system_agents.py", "test_trading_system_complete.py", "test_trading_system_quick.py", "test_yaml_loader.py", "api\\mcp_test_client.py", "api\\mcp_test_input.txt", "api\\quick_test.py", "api\\test_interactive.py", "api\\test_mcp_comprehensive.py", "api\\test_real_api.py", "api\\test_results_schwab_mcp_1750637973.json", "ci\\performance_regression_test.py", "ci\\test_circuit_breaker.py", "ci\\test_circuit_breaker_simple.py", "ci\\test_dashboard_render.py", "ci\\test_performance_sub10ms.py", "docs\\B_SERIES_BACKTEST_FRAMEWORK.md", "live_test_results\\live_test_20250624_091506.json", "live_test_results\\live_test_20250624_092812.json", "live_test_results\\live_test_20250624_093035.json", "live_test_results\\live_test_20250624_093444.json", "live_test_results\\live_test_20250624_093624.json", "live_test_results\\live_test_20250624_095041.json", "live_test_results\\live_test_20250624_095500.json", "live_test_results\\live_test_20250624_102122.json", "live_test_results\\live_test_20250624_102323.json", "live_test_results\\live_test_20250624_105650.json", "live_test_results\\live_test_20250624_110207.json", "live_test_results\\live_test_20250624_113549.json", "live_test_results\\live_test_20250624_114821.json", "live_test_results\\live_test_20250624_115928.json", "tasks\\backtest_simulator.py", "tasks\\run_backtest_batch.py", "tests\\test_auto_broker.py", "tests\\test_backtest_stub.py", "tests\\test_data_ingestion.py", "tests\\test_enhanced_csid_agent.py", "tests\\test_flow_physics_agent.py", "tests\\test_greeks_integration.py", "tests\\test_greek_enhancement_agent.py", "tests\\test_integration_pipeline.py", "tests\\test_order_router.py", "tests\\test_order_router_v2.py", "tests\\test_output_coordinator.py", "tests\\test_risk_guard.py", "tests\\test_schwab_chain_parser.py", "tests\\test_schwab_data_agent.py", "tests\\test_specialized_agent_army.py", "test_data\\2025-06-24\\AAPL_greeks.json", "test_data\\2025-06-24\\AAPL_greeks.parquet", "test_data\\2025-06-24\\FOREIGN_STOCK_greeks.json", "test_data\\2025-06-24\\FOREIGN_STOCK_greeks.parquet", "test_data\\2025-06-24\\SPY_greeks.json", "test_data\\2025-06-24\\SPY_greeks.parquet", "test_data\\2025-06-24\\TSLA_greeks.json", "test_data\\2025-06-24\\TSLA_greeks.parquet", "test_data\\2025-06-24\\XYZ123_greeks.json", "test_data\\2025-06-24\\XYZ123_greeks.parquet", "testing\\temp\\quick_mcp_test.py", "testing\\temp\\test_current_token.py", "testing\\temp\\test_enhanced_direct.py", "ml\\ml\\test_complete_alignment.py", "ml\\ml\\test_feature_alignment.py", "ml\\ml\\test_final_ml_integration.py", "ml\\ml\\test_results\\enhanced_levels.json", "fills\\test_fills\\fill.json", "data\\features\\2025-06-24\\INTEGRATION_TEST_greeks.json", "data\\features\\2025-06-24\\INTEGRATION_TEST_greeks.parquet", "data\\features\\2025-06-24\\TEST_greeks.json", "data\\features\\2025-06-24\\TEST_greeks.parquet", "api\\_archive\\test_schwab_mcp_comprehensive.py"], "debug_files": ["debug_b01_options.py", "debug_flow_physics_resolved.py", "debug_mcp.py", "debug_options.py"], "completed_docs": ["A01_ANOMALY_DETECTION_COMPLETE.md", "AGENT_ZERO_BACKTESTING_IMPLEMENTATION_COMPLETE.md", "AGENT_ZERO_FULL_POWER_COMPLETE.md", "B_SERIES_GREEKS_IMPLEMENTATION_COMPLETE.md", "COMPLETE_AGENT_EVALUATION_REPORT.md", "COMPLETE_SYSTEM_DOCUMENTATION.md", "CONFIGURATION_ENHANCEMENT_COMPLETE.md", "CSID_FACTOR_SYSTEM_COMPLETE.md", "CSID_INTEGRATION_COMPLETE.md", "ENHANCED_AGENT_ZERO_INTEGRATION_COMPLETE.md", "phase2_complete.md", "PRE_ONBOARDING_COMPLETE.md", "SCHWAB_MCP_IMPLEMENTATION_COMPLETE.md", "SCHWAB_MCP_TRADING_SYSTEM_INTEGRATION_COMPLETE.md", "SPECIALIZED_AGENT_ARMY_COMPLETE.md", "SPECIAL_AGENT_EVALUATION_COMPLETE.md", "TRADING_SYSTEM_IMPLEMENTATION_COMPLETE.md"], "old_configs": [], "log_files": ["api\\mcp_server_output.log", "api\\mcp_service.log", "api\\schwab_mcp_server.log", "logs\\schwab_mcp_server.log", "api\\logs\\schwab_mcp_startup.log"], "archive_candidates": [".env", ".env.example", "advanced_market_intelligence.py", "AGENT_DEVELOPMENT_PROGRESS.md", "AGENT_DEVELOPMENT_SUMMARY.md", "AGENT_HANDOFF_GUIDE.md", "AGENT_IMPLEMENTATION_STATUS.md", "AGENT_STANDARDS_IMPLEMENTATION_SUMMARY.md", "AGENT_STANDARDS_TICKER_AGNOSTICISM.md", "AGENT_SYSTEM_ARCHITECTURE.md", "agent_zero_backtester.py", "AGENT_ZERO_BACKTESTING_MISSION_ACCOMPLISHED.md", "agent_zero_enhanced_backtester.py", "agent_zero_integration.py", "AGENT_ZERO_ML_INTEGRATION_CORRECTED.md", "AI Agent Training Pipeline.ini", "AI_AGENT_TRAINING_PIPELINE.md", "analyze_system_usage.py", "B01_INTEGRATION_SUMMARY.md", "B01_TICKER_AGNOSTIC_VALIDATION.md", "Backtesting & ML Implementation Schedule.ini", "Backtesting & ML Training System.ini", "bsm_model.py", "B_SERIES_GREEKS_IMPLEMENTATION_STATUS.md", "B_SERIES_GREEK_ENHANCEMENT_REPORT.md", "B_SERIES_IMPLEMENTATION_REPORT.md", "Code Architecture.ini", "create_ci_snapshot.py", "CSID_OPTIMIZATION_FRAMEWORK.md", "CSID_SWING_TRADING_OPTIMIZATION.md", "CURRENT_ENHANCED_STATUS.md", "CURRENT_STATUS.md", "dashboard.py", "demo_live_testing.py", "deployment_checklist.py", "DEPLOYMENT_READY.md", "DEPLOYMENT_STATUS.md", "deploy_enhanced_agent.py", "Directory Structure.ini", "Directory Structure.md", "discover_local_mcp.py", "dynamic_threshold_engine.py", "enhanced_accumulation_distribution_agent_zero.py", "enhanced_greeks_engine.py", "enhanced_orchestrator.py", "enhanced_orchestrator_iv.py", "ENHANCEMENT_COMPLETION_REPORT.md", "evaluate_special_agent.py", "EVALUATION_FINAL_SUMMARY.md", "Factor Confluence Matrix.ini", "FILES_UPDATED_STATUS_REPORT.md", "final_b01_validation.py", "FINAL_DEPLOYMENT_STATUS.md", "FINAL_HANDOFF_STATUS.md", "fixed_accumulation_distribution_agent.py", "Flow Detection System (AI Agent Training.ini", "Flow Detection System.ini", "generate_evaluation_report.py", "HANDOFF_IMPLEMENTATION_PACKAGE.md", "Information Pipeline.ini", "iv_roc_demo.py", "launch_live_testing.py", "live_checklist_dashboard.py", "live_testing_dashboard.py", "MATHEMATICAL_VALIDATOR_COMPLETION_REPORT.md", "MCP_INTEGRATION.md", "mcp_integration_report_20250622_223934.md", "mcp_integration_report_20250622_224059.md", "mcp_integration_report_20250622_224349.md", "mcp_system_diagnostics.py", "MISSION_ACCOMPLISHED.md", "MISSION_ACCOMPLISHED_AGENT_ZERO_ENHANCED.md", "ml_enhanced_feature_engine.py", "ml_ensemble_engine.py", "multi_orchestrator.py", "orchestrator_example.py", "production_accumulation_distribution.py", "PRODUCTION_ASSESSMENT.md", "pyproject.toml", "quick_deploy.py", "README.md", "REAL_TIME_ENHANCEMENT_STATUS.md", "rotate_api_keys.sh", "run_core_vm.sh", "run_core_win.ps1", "run_live_testing_day.py", "run_multi_batch.bat", "run_multi_batch.sh", "run_production_batch.bat", "run_production_batch.ps1", "SCHWAB_MCP_AGENT_INTEGRATION_CHECKLIST.md", "SCHWAB_MCP_INTEGRATION_FINAL_HANDOFF.md", "SCHWAB_MCP_INTEGRATION_MISSION_ACCOMPLISHED.md", "SCHWAB_NEXT_AGENT_INSTRUCTIONS.md", "security_check.py", "security_sweep.py", "settings.yml", "setup_pre_onboarding.py", "shell_aliases.sh", "Signal Generation Process.ini", "START_SCHWAB_MCP.bat", "STEP3_COMPLETION_REPORT.md", "STEP4_COMPLETION_SUMMARY.md", "SYSTEM_ARCHITECTURE.md", "SYSTEM_STATUS_VALIDATION.md", "TRADING_SYSTEM_ARCHITECTURE.md", "TRADING_SYSTEM_INTEGRATION_UPDATE.md", "trading_system_pipeline_integration.py", "trading_system_schwab_mcp_integration.py", "updated_evaluation.py", "update_agent_zero_integration.py", "validate_100_percent_integration.py", "validate_agent_zero_backtesting.py", "validate_agent_zero_integration.py", "validate_b01_implementation.py", "validate_b_series.py", "validate_for_next_agent.py", "Validation Framework.ini", "verify_output_coordinator.py", "agents\\agent_base.py", "agents\\agent_zero.py", "agents\\anomaly_detector_agent.py", "agents\\auto_broker_adapter.py", "agents\\chart_generator_agent.py", "agents\\data_ingestion_agent.py", "agents\\enhanced_csid_agent.py", "agents\\flow_physics_agent.py", "agents\\fvg_specialist.py", "agents\\greek_enhancement_agent.py", "agents\\iv_dynamics_agent.py", "agents\\math_validator_agent.py", "agents\\mean_reversion_specialist.py", "agents\\order_router_agent.py", "agents\\order_router_agent_v2.py", "agents\\output_coordinator_agent.py", "agents\\pivot_point_specialist.py", "agents\\README.md", "agents\\risk_guard_agent.py", "agents\\schwab_data_agent.py", "agents\\signal_convergence_orchestrator.py", "agents\\signal_generator_agent.py", "agents\\signal_quality_agent.py", "agents\\training_mixin.py", "agent_templates\\create_agent.py", "analyzers\\flow_physics.py", "analyzers\\gex_analysis.py", "analyzers\\liquidity_analysis.py", "analyzers\\volume_analysis.py", "api\\ai_training_config.py", "api\\cli_mcp.py", "api\\comprehensive_api_tester.py", "api\\dc_mcp_integration.py", "api\\desktop_commander_aliases.txt", "api\\DOCUMENTATION.md", "api\\fastapi_mcp_bridge.py", "api\\INTEGRATION_GUIDE.md", "api\\mcp_client_compatibility.py", "api\\mcp_dc.py", "api\\mcp_direct_api.py", "api\\mcp_http_gateway.py", "api\\mcp_http_wrapper.py", "api\\mcp_manifest.json", "api\\mcp_resolution_report.py", "api\\mcp_resolution_report_1749812728.json", "api\\mcp_resolution_summary_1749812728.txt", "api\\mcp_server_validation.py", "api\\mcp_service_manager.py", "api\\PRODUCTION_READY.md", "api\\refresh_schwab_token.py", "api\\schwab_mcp_server.py", "api\\SIMPLE_START.bat", "api\\start_mcp_server.py", "api\\unified_api_gateway.py", "api\\validation_report_1749812698.json", "ci\\check_version_drift.py", "ci\\git_secrets_scan.py", "ci\\run_contract_tasks.py", "ci\\security_compliance_check.py", "config\\constants.py", "config\\settings.py", "config\\__init__.py", "contracts\\A-01_anomaly.yml", "contracts\\accumulation_distribution_detector.yml", "contracts\\agent_orchestrator.yml", "contracts\\B-01_greek_enhancement.yml", "contracts\\B-series.yml", "contracts\\breakout_validation_specialist.yml", "contracts\\C-02_iv_dynamics.yml", "contracts\\C-04.yml", "contracts\\D-01.yml", "contracts\\F-01_enhanced_csid.yml", "contracts\\F-02_flow_physics.yml", "contracts\\options_flow_decoder.yml", "contracts\\R-01-v0.2.0.yml", "contracts\\R-01.yml", "contracts\\R-02.yml", "dashboards\\fills_dashboard.py", "dashboards\\schwab_monitor.py", "data\\api_gateway.py", "data\\data_handler.py", "data\\factor_spec.py", "docs\\multi_orchestrator_README.md", "docs\\SECURITY_STANDARDS.md", "engine\\confluence_engine.py", "engine\\signal_generator.py", "flowphysics\\flow_physics_integrator.py", "flowphysics\\__init__.py", "Flow_Physics_Engine\\enhanced_csid_analyzer.py", "Flow_Physics_Engine\\position_manager.py", "Flow_Physics_Engine\\README.md", "Flow_Physics_Engine\\risk_manager.py", "Flow_Physics_Engine\\signal_engine.py", "Flow_Physics_Engine\\testing_framework_template.py", "Flow_Physics_Engine\\__init__.py", "greeks\\black_scholes_engine.py", "greeks\\constants.py", "greeks\\greeks_result.py", "greeks\\roc_calculator.py", "greeks\\validation_framework.py", "greeks\\__init__.py", "schemas\\tradier_fill_v1.json", "schemas\\unified_analysis_v1.1.json", "schemas\\unified_analysis_v1.json", "tasks\\build_features.py", "tasks\\fetch_history.py", "tasks\\greeks.py", "tasks\\update_hist_stats.py", "tasks\\walk_train_validate.py", "utils\\base_agent.py", "utils\\greeks_history_cache.py", "utils\\option_chain_entropy.py", "utils\\parallel.py", "utils\\profile_loader.py", "utils\\schwab_chain_parser.py", "utils\\validation.py", "tickets\\2025-06-14\\AAPL_order_ticket.txt", "tickets\\2025-06-14\\QQQ_order_ticket.txt", "tickets\\2025-06-14\\TSLA_order_ticket.txt", "tickets\\2025-12-25\\AAPL_order_ticket.txt", "tests\\fixtures\\chart.png", "tests\\fixtures\\enhanced_ua.json", "tests\\fixtures\\math.json", "tests\\fixtures\\sig.json", "testing\\temp\\mcp_error_debug.py", "outputs\\AAPL\\math_validation.json", "outputs\\AAPL\\signal.json", "outputs\\MSFT\\math_validation.json", "outputs\\MSFT\\signal.json", "outputs\\NVDA\\math_validation.json", "outputs\\NVDA\\signal.json", "outputs\\TSLA\\math_validation.json", "outputs\\TSLA\\signal.json", "outputs\\2025-06-14\\AAPL\\execution_plan.md", "outputs\\2025-06-14\\AAPL\\unified_analysis.json", "outputs\\2025-06-14\\NVDA\\execution_plan.md", "outputs\\2025-06-14\\NVDA\\unified_analysis.json", "outputs\\2025-06-14\\QQQ\\execution_plan.md", "outputs\\2025-06-14\\QQQ\\unified_analysis.json", "outputs\\2025-06-14\\SPY\\execution_plan.md", "outputs\\2025-06-14\\SPY\\unified_analysis.json", "outputs\\2025-06-14\\TSLA\\execution_plan.md", "outputs\\2025-06-14\\TSLA\\unified_analysis.json", "output\\coordination\\execution_plan.md", "output\\coordination\\risk_metrics.csv", "output\\coordination\\unified_analysis.json", "ml\\ml\\ab_testing_framework.py", "ml\\ml\\anomaly_detection.py", "ml\\ml\\cross_timeframe_interaction.py", "ml\\ml\\extract_model_features.py", "ml\\ml\\feature_compatibility.py", "ml\\ml\\feature_imputer.py", "ml\\ml\\generate_training_data.py", "ml\\ml\\hierarchical_feature_extractor.py", "ml\\ml\\hyperparameter_optimization.py", "ml\\ml\\imputer.py", "ml\\ml\\integrate_ml_components.py", "ml\\ml\\liquidity_features.py", "ml\\ml\\liquidity_persistence.py", "ml\\ml\\liquidity_prediction.py", "ml\\ml\\main_integration.py", "ml\\ml\\market_regime_adaptation.py", "ml\\ml\\market_regime_adapter.py", "ml\\ml\\market_regime_adapter_complete.py", "ml\\ml\\market_regime_detection.py", "ml\\ml\\ml.py", "ml\\ml\\ml_alert_manager.py", "ml\\ml\\ml_alert_pipeline.py", "ml\\ml\\ml_base.py", "ml\\ml\\ml_components.py", "ml\\ml\\ml_config.py", "ml\\ml\\ml_config_manager.py", "ml\\ml\\ml_dashboard.py", "ml\\ml\\ml_dashboard_styles.css", "ml\\ml\\ML_FEATURE_ALIGNMENT_SUCCESS_REPORT.py", "ml\\ml\\ml_feature_engineering.py", "ml\\ml\\ml_feature_engineering.py.append", "ml\\ml\\ml_inference_service.py", "ml\\ml\\ml_integration.py", "ml\\ml\\ml_liquidity_integration.py", "ml\\ml\\ml_logging.py", "ml\\ml\\ml_model.py", "ml\\ml\\ml_model_registry.py", "ml\\ml\\ml_optimization.py", "ml\\ml\\ml_quantization.py", "ml\\ml\\ml_service.py", "ml\\ml\\ml_strategy_optimizer.py", "ml\\ml\\ml_styles.css", "ml\\ml\\ml_system.py", "ml\\ml\\ml_utils.py", "ml\\ml\\ml_visualization.py", "ml\\ml\\optimize_liquidity_models.py", "ml\\ml\\real_time_prediction.py", "ml\\ml\\reinforcement_learning.py", "ml\\ml\\retrain_models.py", "ml\\ml\\tensorflow.py", "ml\\ml\\timeframe_aware_models.py", "ml\\ml\\train_liquidity_models.py", "ml\\ml\\user_feedback_integration.py", "ml\\ml\\__init__.py", "ml\\ml\\alerts\\__init__.py", "ml\\ml\\features\\order_book_features.py", "ml\\ml\\features\\__init__.py", "ml\\ml\\models\\current_features.txt", "ml\\ml\\models\\level_strength_feature_importance.png", "ml\\ml\\models\\level_strength_model.pkl", "ml\\ml\\models\\pattern_model.pt", "ml\\ml\\models\\price_model.pt", "ml\\ml\\models\\price_reaction_feature_importance.png", "ml\\ml\\models\\price_reaction_model.pkl", "ml\\ml\\real_components\\real_ml_feature_extractor.py", "ml\\ml\\real_components\\real_ml_feature_extractor_fixed.py", "ml\\ml\\real_components\\real_model_loader.py", "ml\\ml\\trading\\edge_detection.py", "ml\\ml\\trading\\enhanced_regime_detector.py", "ml\\ml\\trading\\execution_optimizer.py", "ml\\ml\\trading\\liquidity_integration.py", "ml\\ml\\trading\\position_sizing.py", "ml\\ml\\trading\\trade_model.py", "ml\\ml\\trading\\trade_model.py.tensorflow_bak", "ml\\ml\\trading\\trade_selection.py", "ml\\ml\\trading\\unified_trading_model.py", "ml\\ml\\trading\\__init__.py", "ml\\ml\\trading\\backtesting\\backtesting.py", "ml\\ml\\trading\\backtesting\\data_pipeline.py", "ml\\ml\\trading\\backtesting\\statistical_analysis.py", "ml\\ml\\trading\\backtesting\\training_pipeline.py", "ml\\ml\\trading\\backtesting\\__init__.py", "ml\\ml\\examples\\inference_examples\\basic_inference.py", "ml\\ml\\examples\\inference_examples\\streaming_inference.py", "guarded\\2025-06-14\\AAPL\\execution_plan_ok.md", "guarded\\2025-06-14\\AAPL\\risk_guard_log.json", "guarded\\2025-06-14\\NVDA\\execution_plan_ok.md", "guarded\\2025-06-14\\NVDA\\risk_guard_log.json", "guarded\\2025-06-14\\TSLA\\execution_plan_ok.md", "guarded\\2025-06-14\\TSLA\\risk_guard_log.json", "Flow_Physics_Engine\\advanced\\advanced_acceleration_analyzer.py", "Flow_Physics_Engine\\advanced\\advanced_velocity_analyzer.py", "Flow_Physics_Engine\\advanced\\flow_jerk_analyzer.py", "Flow_Physics_Engine\\advanced\\flow_physics_integrator.py", "Flow_Physics_Engine\\advanced\\run_flow_physics_demo.py", "Flow_Physics_Engine\\advanced\\__init__.py", "Flow_Physics_Engine\\api_robustness\\comprehensive_api_tester.py", "Flow_Physics_Engine\\api_robustness\\endpoint_registry.py", "Flow_Physics_Engine\\api_robustness\\enhanced_polygon_api.py", "Flow_Physics_Engine\\api_robustness\\rate_limiter.py", "Flow_Physics_Engine\\api_robustness\\unified_api_gateway.py", "Flow_Physics_Engine\\api_robustness\\__init__.py", "Flow_Physics_Engine\\engine_core\\csid_analyzer.py", "Flow_Physics_Engine\\engine_core\\flow_physics_integrator.py", "flow_phys\\2025-06-15\\AAPL_csid_analysis.json", "flow_phys\\2025-06-15\\AAPL_flowphysics.json", "data\\greeks_cache\\AAPL_2025-06-24.parquet", "data\\live\\2025-06-22\\AAPL_bars.parquet", "data\\live\\2025-06-22\\AAPL_options.parquet", "data\\live\\2025-06-24\\AAPL_bars.parquet", "data\\live\\2025-06-24\\AAPL_options.parquet", "data\\live\\2025-06-24\\QQQ_bars.parquet", "data\\live\\2025-06-24\\QQQ_options.parquet", "data\\live\\2025-06-24\\SPY_bars.parquet", "data\\live\\2025-06-24\\SPY_options.parquet", "data\\features\\2025-06-24\\AAPL_greeks.json", "data\\features\\2025-06-24\\AAPL_greeks.parquet", "data\\features\\2025-06-24\\FOREIGN_STOCK_greeks.json", "data\\features\\2025-06-24\\FOREIGN_STOCK_greeks.parquet", "data\\features\\2025-06-24\\SPY_greeks.json", "data\\features\\2025-06-24\\SPY_greeks.parquet", "data\\features\\2025-06-24\\TSLA_greeks.json", "data\\features\\2025-06-24\\TSLA_greeks.parquet", "data\\features\\2025-06-24\\XYZ123_greeks.json", "data\\features\\2025-06-24\\XYZ123_greeks.parquet", "custom_data\\2025-06-24\\AAPL_greeks.json", "custom_data\\2025-06-24\\AAPL_greeks.parquet", "custom_data\\2025-06-24\\FOREIGN_STOCK_greeks.json", "custom_data\\2025-06-24\\FOREIGN_STOCK_greeks.parquet", "custom_data\\2025-06-24\\SPY_greeks.json", "custom_data\\2025-06-24\\SPY_greeks.parquet", "custom_data\\2025-06-24\\TSLA_greeks.json", "custom_data\\2025-06-24\\TSLA_greeks.parquet", "custom_data\\2025-06-24\\XYZ123_greeks.json", "custom_data\\2025-06-24\\XYZ123_greeks.parquet", "api\\modules\\api_cache.py", "api\\modules\\api_client_factory.py", "api\\modules\\api_errors.py", "api\\modules\\api_errors_fixed.py", "api\\modules\\api_health_monitor.py", "api\\modules\\api_health_status.json", "api\\modules\\connection_pool_manager.py", "api\\modules\\endpoint_registry.py", "api\\modules\\rate_limiter.py", "api\\modules\\schwab_option_parser.py", "api\\modules\\schwab_production_api.py", "api\\modules\\__init__.py", "api\\tests\\__init__.py", "api\\_archive\\mcp_server_production.py", "api\\_archive\\mcp_server_simple.py", "api\\_archive\\migrate_to_schwab_mcp.py", "api\\_archive\\refresh_schwab_token.py", "api\\_archive\\schwab_mcp_server.py", "api\\_archive\\setup_schwab_mcp.py", "api\\_archive\\START_MCP_SERVER.bat", "api\\_archive\\START_MCP_SERVER.ps1", "api\\_archive\\START_SCHWAB_MCP.py", "api\\modules\\config\\paths.py", "api\\modules\\config\\__init__.py", "api\\modules\\data\\__init__.py", "api\\modules\\data\\loaders\\enhanced_polygon_api.py", "api\\modules\\data\\loaders\\__init__.py", "api\\mcp_installation\\scripts\\start_mcp.bat", "agent_workspace\\chart_gen_001\\chart_1_volume_flow_20250614_114734.png", "agent_workspace\\chart_gen_001\\chart_1_volume_flow_20250614_114802.png", "agent_workspace\\chart_gen_001\\chart_2_liquidity_gex_20250614_114734.png", "agent_workspace\\chart_gen_001\\chart_2_liquidity_gex_20250614_114802.png", "agent_workspace\\chart_gen_001\\chart_3_confluence_20250614_114734.png", "agent_workspace\\chart_gen_001\\chart_3_confluence_20250614_114802.png", "agent_docs\\standards\\mathematical_precision_standards.md", "agent_docs\\tasks\\task_definitions.json", "agent_docs\\workflows\\chart_generation_workflow.md", "agent_docs\\workflows\\convergence.md", "agent_docs\\workflows\\fvg.md", "agent_docs\\workflows\\mathematical_validation_workflow.md", "agent_docs\\workflows\\mean_reversion.md", "agent_docs\\workflows\\output_coordination_workflow.md", "agent_docs\\workflows\\pivot.md", "agent_docs\\workflows\\signal_quality_workflow.md", "agents\\accumulation_distribution_detector\\accumulation_distribution_detector.py", "agents\\accumulation_distribution_detector\\enhanced_accumulation_distribution_agent.py", "agents\\agent_orchestrator\\agent_orchestrator.py", "agents\\agent_orchestrator\\agent_orchestrator_real_data_only.py", "agents\\breakout_validation_specialist\\breakout_validation_specialist.py", "agents\\options_flow_decoder\\options_flow_decoder.py"]}, "recommendations": {"archive_immediately": {"completed_documentation": ["A01_ANOMALY_DETECTION_COMPLETE.md", "AGENT_ZERO_BACKTESTING_IMPLEMENTATION_COMPLETE.md", "AGENT_ZERO_FULL_POWER_COMPLETE.md", "B_SERIES_GREEKS_IMPLEMENTATION_COMPLETE.md", "COMPLETE_AGENT_EVALUATION_REPORT.md", "COMPLETE_SYSTEM_DOCUMENTATION.md", "CONFIGURATION_ENHANCEMENT_COMPLETE.md", "CSID_FACTOR_SYSTEM_COMPLETE.md", "CSID_INTEGRATION_COMPLETE.md", "ENHANCED_AGENT_ZERO_INTEGRATION_COMPLETE.md", "phase2_complete.md", "PRE_ONBOARDING_COMPLETE.md", "SCHWAB_MCP_IMPLEMENTATION_COMPLETE.md", "SCHWAB_MCP_TRADING_SYSTEM_INTEGRATION_COMPLETE.md", "SPECIALIZED_AGENT_ARMY_COMPLETE.md", "SPECIAL_AGENT_EVALUATION_COMPLETE.md", "TRADING_SYSTEM_IMPLEMENTATION_COMPLETE.md"], "debug_files": ["debug_b01_options.py", "debug_flow_physics_resolved.py", "debug_mcp.py", "debug_options.py"], "old_test_files": []}, "review_for_archive": {"test_files": ["comprehensive_mcp_test_results_20250622_225017.json", "comprehensive_mcp_test_results_20250622_225132.json", "comprehensive_mcp_test_results_20250622_225240.json", "comprehensive_mcp_test_results_20250622_230306.json", "comprehensive_test.py", "final_validation_test.py", "live_test_checklist.py", "mcp_integration_test_results_20250622_223934.json", "mcp_integration_test_results_20250622_224059.json", "mcp_integration_test_results_20250622_224349.json", "quick_mcp_test.py", "run_backtest_pipeline.bat", "run_backtest_pipeline.sh", "test_agent_army_live.py", "test_agent_framework.py", "test_anomaly_detection.py", "test_b01_integration.py", "test_b01_ticker_agnostic.py", "test_bounds_fix.py", "test_broker_integration.py", "test_chart_generator.py", "test_comprehensive_mcp_integration.py", "test_core_system.py", "test_enhanced_agent.py", "test_enhanced_greeks_architecture.py", "test_final_integration.py", "test_greek_features.py", "test_integrated_system.py", "test_iv_dynamics.py", "test_live_mcp_integration.py", "test_math_validator.py", "test_mcp_agent_integration.py", "test_mcp_connection.py", "test_mcp_correct_port.py", "test_mcp_final.py", "test_mcp_integration.py", "test_mcp_wire.py", "test_multi_orchestrator.py", "test_original_agent_fix.py", "test_phase15_enhancements.py", "test_protocol_compliance.py", "test_protocol_simple.py", "test_results_20250614_110352.json", "test_results_20250614_170359.json", "test_results_20250614_170845.json", "test_schwab_options.py", "test_schwab_production.py", "test_schwab_validation.py", "test_signal_generator.py", "test_signal_quality.py", "test_simple_integration.py", "test_system.py", "test_trading_math.py", "test_trading_system_agents.py", "test_trading_system_complete.py", "test_trading_system_quick.py", "test_yaml_loader.py", "api\\mcp_test_client.py", "api\\mcp_test_input.txt", "api\\quick_test.py", "api\\test_interactive.py", "api\\test_mcp_comprehensive.py", "api\\test_real_api.py", "api\\test_results_schwab_mcp_1750637973.json", "ci\\performance_regression_test.py", "ci\\test_circuit_breaker.py", "ci\\test_circuit_breaker_simple.py", "ci\\test_dashboard_render.py", "ci\\test_performance_sub10ms.py", "docs\\B_SERIES_BACKTEST_FRAMEWORK.md", "live_test_results\\live_test_20250624_091506.json", "live_test_results\\live_test_20250624_092812.json", "live_test_results\\live_test_20250624_093035.json", "live_test_results\\live_test_20250624_093444.json", "live_test_results\\live_test_20250624_093624.json", "live_test_results\\live_test_20250624_095041.json", "live_test_results\\live_test_20250624_095500.json", "live_test_results\\live_test_20250624_102122.json", "live_test_results\\live_test_20250624_102323.json", "live_test_results\\live_test_20250624_105650.json", "live_test_results\\live_test_20250624_110207.json", "live_test_results\\live_test_20250624_113549.json", "live_test_results\\live_test_20250624_114821.json", "live_test_results\\live_test_20250624_115928.json", "tasks\\backtest_simulator.py", "tasks\\run_backtest_batch.py", "tests\\test_auto_broker.py", "tests\\test_backtest_stub.py", "tests\\test_data_ingestion.py", "tests\\test_enhanced_csid_agent.py", "tests\\test_flow_physics_agent.py", "tests\\test_greeks_integration.py", "tests\\test_greek_enhancement_agent.py", "tests\\test_integration_pipeline.py", "tests\\test_order_router.py", "tests\\test_order_router_v2.py", "tests\\test_output_coordinator.py", "tests\\test_risk_guard.py", "tests\\test_schwab_chain_parser.py", "tests\\test_schwab_data_agent.py", "tests\\test_specialized_agent_army.py", "test_data\\2025-06-24\\AAPL_greeks.json", "test_data\\2025-06-24\\AAPL_greeks.parquet", "test_data\\2025-06-24\\FOREIGN_STOCK_greeks.json", "test_data\\2025-06-24\\FOREIGN_STOCK_greeks.parquet", "test_data\\2025-06-24\\SPY_greeks.json", "test_data\\2025-06-24\\SPY_greeks.parquet", "test_data\\2025-06-24\\TSLA_greeks.json", "test_data\\2025-06-24\\TSLA_greeks.parquet", "test_data\\2025-06-24\\XYZ123_greeks.json", "test_data\\2025-06-24\\XYZ123_greeks.parquet", "testing\\temp\\quick_mcp_test.py", "testing\\temp\\test_current_token.py", "testing\\temp\\test_enhanced_direct.py", "ml\\ml\\test_complete_alignment.py", "ml\\ml\\test_feature_alignment.py", "ml\\ml\\test_final_ml_integration.py", "ml\\ml\\test_results\\enhanced_levels.json", "fills\\test_fills\\fill.json", "data\\features\\2025-06-24\\INTEGRATION_TEST_greeks.json", "data\\features\\2025-06-24\\INTEGRATION_TEST_greeks.parquet", "data\\features\\2025-06-24\\TEST_greeks.json", "data\\features\\2025-06-24\\TEST_greeks.parquet", "api\\_archive\\test_schwab_mcp_comprehensive.py"], "misc_files": [".env", ".env.example", "advanced_market_intelligence.py", "AGENT_DEVELOPMENT_PROGRESS.md", "AGENT_DEVELOPMENT_SUMMARY.md", "AGENT_HANDOFF_GUIDE.md", "AGENT_IMPLEMENTATION_STATUS.md", "AGENT_STANDARDS_IMPLEMENTATION_SUMMARY.md", "AGENT_STANDARDS_TICKER_AGNOSTICISM.md", "AGENT_SYSTEM_ARCHITECTURE.md", "agent_zero_backtester.py", "AGENT_ZERO_BACKTESTING_MISSION_ACCOMPLISHED.md", "agent_zero_enhanced_backtester.py", "agent_zero_integration.py", "AGENT_ZERO_ML_INTEGRATION_CORRECTED.md", "AI Agent Training Pipeline.ini", "AI_AGENT_TRAINING_PIPELINE.md", "analyze_system_usage.py", "B01_INTEGRATION_SUMMARY.md", "B01_TICKER_AGNOSTIC_VALIDATION.md", "Backtesting & ML Implementation Schedule.ini", "Backtesting & ML Training System.ini", "bsm_model.py", "B_SERIES_GREEKS_IMPLEMENTATION_STATUS.md", "B_SERIES_GREEK_ENHANCEMENT_REPORT.md", "B_SERIES_IMPLEMENTATION_REPORT.md", "Code Architecture.ini", "create_ci_snapshot.py", "CSID_OPTIMIZATION_FRAMEWORK.md", "CSID_SWING_TRADING_OPTIMIZATION.md", "CURRENT_ENHANCED_STATUS.md", "CURRENT_STATUS.md", "dashboard.py", "demo_live_testing.py", "deployment_checklist.py", "DEPLOYMENT_READY.md", "DEPLOYMENT_STATUS.md", "deploy_enhanced_agent.py", "Directory Structure.ini", "Directory Structure.md", "discover_local_mcp.py", "dynamic_threshold_engine.py", "enhanced_accumulation_distribution_agent_zero.py", "enhanced_greeks_engine.py", "enhanced_orchestrator.py", "enhanced_orchestrator_iv.py", "ENHANCEMENT_COMPLETION_REPORT.md", "evaluate_special_agent.py", "EVALUATION_FINAL_SUMMARY.md", "Factor Confluence Matrix.ini", "FILES_UPDATED_STATUS_REPORT.md", "final_b01_validation.py", "FINAL_DEPLOYMENT_STATUS.md", "FINAL_HANDOFF_STATUS.md", "fixed_accumulation_distribution_agent.py", "Flow Detection System (AI Agent Training.ini", "Flow Detection System.ini", "generate_evaluation_report.py", "HANDOFF_IMPLEMENTATION_PACKAGE.md", "Information Pipeline.ini", "iv_roc_demo.py", "launch_live_testing.py", "live_checklist_dashboard.py", "live_testing_dashboard.py", "MATHEMATICAL_VALIDATOR_COMPLETION_REPORT.md", "MCP_INTEGRATION.md", "mcp_integration_report_20250622_223934.md", "mcp_integration_report_20250622_224059.md", "mcp_integration_report_20250622_224349.md", "mcp_system_diagnostics.py", "MISSION_ACCOMPLISHED.md", "MISSION_ACCOMPLISHED_AGENT_ZERO_ENHANCED.md", "ml_enhanced_feature_engine.py", "ml_ensemble_engine.py", "multi_orchestrator.py", "orchestrator_example.py", "production_accumulation_distribution.py", "PRODUCTION_ASSESSMENT.md", "pyproject.toml", "quick_deploy.py", "README.md", "REAL_TIME_ENHANCEMENT_STATUS.md", "rotate_api_keys.sh", "run_core_vm.sh", "run_core_win.ps1", "run_live_testing_day.py", "run_multi_batch.bat", "run_multi_batch.sh", "run_production_batch.bat", "run_production_batch.ps1", "SCHWAB_MCP_AGENT_INTEGRATION_CHECKLIST.md", "SCHWAB_MCP_INTEGRATION_FINAL_HANDOFF.md", "SCHWAB_MCP_INTEGRATION_MISSION_ACCOMPLISHED.md", "SCHWAB_NEXT_AGENT_INSTRUCTIONS.md", "security_check.py", "security_sweep.py", "settings.yml", "setup_pre_onboarding.py", "shell_aliases.sh", "Signal Generation Process.ini", "START_SCHWAB_MCP.bat", "STEP3_COMPLETION_REPORT.md", "STEP4_COMPLETION_SUMMARY.md", "SYSTEM_ARCHITECTURE.md", "SYSTEM_STATUS_VALIDATION.md", "TRADING_SYSTEM_ARCHITECTURE.md", "TRADING_SYSTEM_INTEGRATION_UPDATE.md", "trading_system_pipeline_integration.py", "trading_system_schwab_mcp_integration.py", "updated_evaluation.py", "update_agent_zero_integration.py", "validate_100_percent_integration.py", "validate_agent_zero_backtesting.py", "validate_agent_zero_integration.py", "validate_b01_implementation.py", "validate_b_series.py", "validate_for_next_agent.py", "Validation Framework.ini", "verify_output_coordinator.py", "agents\\agent_base.py", "agents\\agent_zero.py", "agents\\anomaly_detector_agent.py", "agents\\auto_broker_adapter.py", "agents\\chart_generator_agent.py", "agents\\data_ingestion_agent.py", "agents\\enhanced_csid_agent.py", "agents\\flow_physics_agent.py", "agents\\fvg_specialist.py", "agents\\greek_enhancement_agent.py", "agents\\iv_dynamics_agent.py", "agents\\math_validator_agent.py", "agents\\mean_reversion_specialist.py", "agents\\order_router_agent.py", "agents\\order_router_agent_v2.py", "agents\\output_coordinator_agent.py", "agents\\pivot_point_specialist.py", "agents\\README.md", "agents\\risk_guard_agent.py", "agents\\schwab_data_agent.py", "agents\\signal_convergence_orchestrator.py", "agents\\signal_generator_agent.py", "agents\\signal_quality_agent.py", "agents\\training_mixin.py", "agent_templates\\create_agent.py", "analyzers\\flow_physics.py", "analyzers\\gex_analysis.py", "analyzers\\liquidity_analysis.py", "analyzers\\volume_analysis.py", "api\\ai_training_config.py", "api\\cli_mcp.py", "api\\comprehensive_api_tester.py", "api\\dc_mcp_integration.py", "api\\desktop_commander_aliases.txt", "api\\DOCUMENTATION.md", "api\\fastapi_mcp_bridge.py", "api\\INTEGRATION_GUIDE.md", "api\\mcp_client_compatibility.py", "api\\mcp_dc.py", "api\\mcp_direct_api.py", "api\\mcp_http_gateway.py", "api\\mcp_http_wrapper.py", "api\\mcp_manifest.json", "api\\mcp_resolution_report.py", "api\\mcp_resolution_report_1749812728.json", "api\\mcp_resolution_summary_1749812728.txt", "api\\mcp_server_validation.py", "api\\mcp_service_manager.py", "api\\PRODUCTION_READY.md", "api\\refresh_schwab_token.py", "api\\schwab_mcp_server.py", "api\\SIMPLE_START.bat", "api\\start_mcp_server.py", "api\\unified_api_gateway.py", "api\\validation_report_1749812698.json", "ci\\check_version_drift.py", "ci\\git_secrets_scan.py", "ci\\run_contract_tasks.py", "ci\\security_compliance_check.py", "config\\constants.py", "config\\settings.py", "config\\__init__.py", "contracts\\A-01_anomaly.yml", "contracts\\accumulation_distribution_detector.yml", "contracts\\agent_orchestrator.yml", "contracts\\B-01_greek_enhancement.yml", "contracts\\B-series.yml", "contracts\\breakout_validation_specialist.yml", "contracts\\C-02_iv_dynamics.yml", "contracts\\C-04.yml", "contracts\\D-01.yml", "contracts\\F-01_enhanced_csid.yml", "contracts\\F-02_flow_physics.yml", "contracts\\options_flow_decoder.yml", "contracts\\R-01-v0.2.0.yml", "contracts\\R-01.yml", "contracts\\R-02.yml", "dashboards\\fills_dashboard.py", "dashboards\\schwab_monitor.py", "data\\api_gateway.py", "data\\data_handler.py", "data\\factor_spec.py", "docs\\multi_orchestrator_README.md", "docs\\SECURITY_STANDARDS.md", "engine\\confluence_engine.py", "engine\\signal_generator.py", "flowphysics\\flow_physics_integrator.py", "flowphysics\\__init__.py", "Flow_Physics_Engine\\enhanced_csid_analyzer.py", "Flow_Physics_Engine\\position_manager.py", "Flow_Physics_Engine\\README.md", "Flow_Physics_Engine\\risk_manager.py", "Flow_Physics_Engine\\signal_engine.py", "Flow_Physics_Engine\\testing_framework_template.py", "Flow_Physics_Engine\\__init__.py", "greeks\\black_scholes_engine.py", "greeks\\constants.py", "greeks\\greeks_result.py", "greeks\\roc_calculator.py", "greeks\\validation_framework.py", "greeks\\__init__.py", "schemas\\tradier_fill_v1.json", "schemas\\unified_analysis_v1.1.json", "schemas\\unified_analysis_v1.json", "tasks\\build_features.py", "tasks\\fetch_history.py", "tasks\\greeks.py", "tasks\\update_hist_stats.py", "tasks\\walk_train_validate.py", "utils\\base_agent.py", "utils\\greeks_history_cache.py", "utils\\option_chain_entropy.py", "utils\\parallel.py", "utils\\profile_loader.py", "utils\\schwab_chain_parser.py", "utils\\validation.py", "tickets\\2025-06-14\\AAPL_order_ticket.txt", "tickets\\2025-06-14\\QQQ_order_ticket.txt", "tickets\\2025-06-14\\TSLA_order_ticket.txt", "tickets\\2025-12-25\\AAPL_order_ticket.txt", "tests\\fixtures\\chart.png", "tests\\fixtures\\enhanced_ua.json", "tests\\fixtures\\math.json", "tests\\fixtures\\sig.json", "testing\\temp\\mcp_error_debug.py", "outputs\\AAPL\\math_validation.json", "outputs\\AAPL\\signal.json", "outputs\\MSFT\\math_validation.json", "outputs\\MSFT\\signal.json", "outputs\\NVDA\\math_validation.json", "outputs\\NVDA\\signal.json", "outputs\\TSLA\\math_validation.json", "outputs\\TSLA\\signal.json", "outputs\\2025-06-14\\AAPL\\execution_plan.md", "outputs\\2025-06-14\\AAPL\\unified_analysis.json", "outputs\\2025-06-14\\NVDA\\execution_plan.md", "outputs\\2025-06-14\\NVDA\\unified_analysis.json", "outputs\\2025-06-14\\QQQ\\execution_plan.md", "outputs\\2025-06-14\\QQQ\\unified_analysis.json", "outputs\\2025-06-14\\SPY\\execution_plan.md", "outputs\\2025-06-14\\SPY\\unified_analysis.json", "outputs\\2025-06-14\\TSLA\\execution_plan.md", "outputs\\2025-06-14\\TSLA\\unified_analysis.json", "output\\coordination\\execution_plan.md", "output\\coordination\\risk_metrics.csv", "output\\coordination\\unified_analysis.json", "ml\\ml\\ab_testing_framework.py", "ml\\ml\\anomaly_detection.py", "ml\\ml\\cross_timeframe_interaction.py", "ml\\ml\\extract_model_features.py", "ml\\ml\\feature_compatibility.py", "ml\\ml\\feature_imputer.py", "ml\\ml\\generate_training_data.py", "ml\\ml\\hierarchical_feature_extractor.py", "ml\\ml\\hyperparameter_optimization.py", "ml\\ml\\imputer.py", "ml\\ml\\integrate_ml_components.py", "ml\\ml\\liquidity_features.py", "ml\\ml\\liquidity_persistence.py", "ml\\ml\\liquidity_prediction.py", "ml\\ml\\main_integration.py", "ml\\ml\\market_regime_adaptation.py", "ml\\ml\\market_regime_adapter.py", "ml\\ml\\market_regime_adapter_complete.py", "ml\\ml\\market_regime_detection.py", "ml\\ml\\ml.py", "ml\\ml\\ml_alert_manager.py", "ml\\ml\\ml_alert_pipeline.py", "ml\\ml\\ml_base.py", "ml\\ml\\ml_components.py", "ml\\ml\\ml_config.py", "ml\\ml\\ml_config_manager.py", "ml\\ml\\ml_dashboard.py", "ml\\ml\\ml_dashboard_styles.css", "ml\\ml\\ML_FEATURE_ALIGNMENT_SUCCESS_REPORT.py", "ml\\ml\\ml_feature_engineering.py", "ml\\ml\\ml_feature_engineering.py.append", "ml\\ml\\ml_inference_service.py", "ml\\ml\\ml_integration.py", "ml\\ml\\ml_liquidity_integration.py", "ml\\ml\\ml_logging.py", "ml\\ml\\ml_model.py", "ml\\ml\\ml_model_registry.py", "ml\\ml\\ml_optimization.py", "ml\\ml\\ml_quantization.py", "ml\\ml\\ml_service.py", "ml\\ml\\ml_strategy_optimizer.py", "ml\\ml\\ml_styles.css", "ml\\ml\\ml_system.py", "ml\\ml\\ml_utils.py", "ml\\ml\\ml_visualization.py", "ml\\ml\\optimize_liquidity_models.py", "ml\\ml\\real_time_prediction.py", "ml\\ml\\reinforcement_learning.py", "ml\\ml\\retrain_models.py", "ml\\ml\\tensorflow.py", "ml\\ml\\timeframe_aware_models.py", "ml\\ml\\train_liquidity_models.py", "ml\\ml\\user_feedback_integration.py", "ml\\ml\\__init__.py", "ml\\ml\\alerts\\__init__.py", "ml\\ml\\features\\order_book_features.py", "ml\\ml\\features\\__init__.py", "ml\\ml\\models\\current_features.txt", "ml\\ml\\models\\level_strength_feature_importance.png", "ml\\ml\\models\\level_strength_model.pkl", "ml\\ml\\models\\pattern_model.pt", "ml\\ml\\models\\price_model.pt", "ml\\ml\\models\\price_reaction_feature_importance.png", "ml\\ml\\models\\price_reaction_model.pkl", "ml\\ml\\real_components\\real_ml_feature_extractor.py", "ml\\ml\\real_components\\real_ml_feature_extractor_fixed.py", "ml\\ml\\real_components\\real_model_loader.py", "ml\\ml\\trading\\edge_detection.py", "ml\\ml\\trading\\enhanced_regime_detector.py", "ml\\ml\\trading\\execution_optimizer.py", "ml\\ml\\trading\\liquidity_integration.py", "ml\\ml\\trading\\position_sizing.py", "ml\\ml\\trading\\trade_model.py", "ml\\ml\\trading\\trade_model.py.tensorflow_bak", "ml\\ml\\trading\\trade_selection.py", "ml\\ml\\trading\\unified_trading_model.py", "ml\\ml\\trading\\__init__.py", "ml\\ml\\trading\\backtesting\\backtesting.py", "ml\\ml\\trading\\backtesting\\data_pipeline.py", "ml\\ml\\trading\\backtesting\\statistical_analysis.py", "ml\\ml\\trading\\backtesting\\training_pipeline.py", "ml\\ml\\trading\\backtesting\\__init__.py", "ml\\ml\\examples\\inference_examples\\basic_inference.py", "ml\\ml\\examples\\inference_examples\\streaming_inference.py", "guarded\\2025-06-14\\AAPL\\execution_plan_ok.md", "guarded\\2025-06-14\\AAPL\\risk_guard_log.json", "guarded\\2025-06-14\\NVDA\\execution_plan_ok.md", "guarded\\2025-06-14\\NVDA\\risk_guard_log.json", "guarded\\2025-06-14\\TSLA\\execution_plan_ok.md", "guarded\\2025-06-14\\TSLA\\risk_guard_log.json", "Flow_Physics_Engine\\advanced\\advanced_acceleration_analyzer.py", "Flow_Physics_Engine\\advanced\\advanced_velocity_analyzer.py", "Flow_Physics_Engine\\advanced\\flow_jerk_analyzer.py", "Flow_Physics_Engine\\advanced\\flow_physics_integrator.py", "Flow_Physics_Engine\\advanced\\run_flow_physics_demo.py", "Flow_Physics_Engine\\advanced\\__init__.py", "Flow_Physics_Engine\\api_robustness\\comprehensive_api_tester.py", "Flow_Physics_Engine\\api_robustness\\endpoint_registry.py", "Flow_Physics_Engine\\api_robustness\\enhanced_polygon_api.py", "Flow_Physics_Engine\\api_robustness\\rate_limiter.py", "Flow_Physics_Engine\\api_robustness\\unified_api_gateway.py", "Flow_Physics_Engine\\api_robustness\\__init__.py", "Flow_Physics_Engine\\engine_core\\csid_analyzer.py", "Flow_Physics_Engine\\engine_core\\flow_physics_integrator.py", "flow_phys\\2025-06-15\\AAPL_csid_analysis.json", "flow_phys\\2025-06-15\\AAPL_flowphysics.json", "data\\greeks_cache\\AAPL_2025-06-24.parquet", "data\\live\\2025-06-22\\AAPL_bars.parquet", "data\\live\\2025-06-22\\AAPL_options.parquet", "data\\live\\2025-06-24\\AAPL_bars.parquet", "data\\live\\2025-06-24\\AAPL_options.parquet", "data\\live\\2025-06-24\\QQQ_bars.parquet", "data\\live\\2025-06-24\\QQQ_options.parquet", "data\\live\\2025-06-24\\SPY_bars.parquet", "data\\live\\2025-06-24\\SPY_options.parquet", "data\\features\\2025-06-24\\AAPL_greeks.json", "data\\features\\2025-06-24\\AAPL_greeks.parquet", "data\\features\\2025-06-24\\FOREIGN_STOCK_greeks.json", "data\\features\\2025-06-24\\FOREIGN_STOCK_greeks.parquet", "data\\features\\2025-06-24\\SPY_greeks.json", "data\\features\\2025-06-24\\SPY_greeks.parquet", "data\\features\\2025-06-24\\TSLA_greeks.json", "data\\features\\2025-06-24\\TSLA_greeks.parquet", "data\\features\\2025-06-24\\XYZ123_greeks.json", "data\\features\\2025-06-24\\XYZ123_greeks.parquet", "custom_data\\2025-06-24\\AAPL_greeks.json", "custom_data\\2025-06-24\\AAPL_greeks.parquet", "custom_data\\2025-06-24\\FOREIGN_STOCK_greeks.json", "custom_data\\2025-06-24\\FOREIGN_STOCK_greeks.parquet", "custom_data\\2025-06-24\\SPY_greeks.json", "custom_data\\2025-06-24\\SPY_greeks.parquet", "custom_data\\2025-06-24\\TSLA_greeks.json", "custom_data\\2025-06-24\\TSLA_greeks.parquet", "custom_data\\2025-06-24\\XYZ123_greeks.json", "custom_data\\2025-06-24\\XYZ123_greeks.parquet", "api\\modules\\api_cache.py", "api\\modules\\api_client_factory.py", "api\\modules\\api_errors.py", "api\\modules\\api_errors_fixed.py", "api\\modules\\api_health_monitor.py", "api\\modules\\api_health_status.json", "api\\modules\\connection_pool_manager.py", "api\\modules\\endpoint_registry.py", "api\\modules\\rate_limiter.py", "api\\modules\\schwab_option_parser.py", "api\\modules\\schwab_production_api.py", "api\\modules\\__init__.py", "api\\tests\\__init__.py", "api\\_archive\\mcp_server_production.py", "api\\_archive\\mcp_server_simple.py", "api\\_archive\\migrate_to_schwab_mcp.py", "api\\_archive\\refresh_schwab_token.py", "api\\_archive\\schwab_mcp_server.py", "api\\_archive\\setup_schwab_mcp.py", "api\\_archive\\START_MCP_SERVER.bat", "api\\_archive\\START_MCP_SERVER.ps1", "api\\_archive\\START_SCHWAB_MCP.py", "api\\modules\\config\\paths.py", "api\\modules\\config\\__init__.py", "api\\modules\\data\\__init__.py", "api\\modules\\data\\loaders\\enhanced_polygon_api.py", "api\\modules\\data\\loaders\\__init__.py", "api\\mcp_installation\\scripts\\start_mcp.bat", "agent_workspace\\chart_gen_001\\chart_1_volume_flow_20250614_114734.png", "agent_workspace\\chart_gen_001\\chart_1_volume_flow_20250614_114802.png", "agent_workspace\\chart_gen_001\\chart_2_liquidity_gex_20250614_114734.png", "agent_workspace\\chart_gen_001\\chart_2_liquidity_gex_20250614_114802.png", "agent_workspace\\chart_gen_001\\chart_3_confluence_20250614_114734.png", "agent_workspace\\chart_gen_001\\chart_3_confluence_20250614_114802.png", "agent_docs\\standards\\mathematical_precision_standards.md", "agent_docs\\tasks\\task_definitions.json", "agent_docs\\workflows\\chart_generation_workflow.md", "agent_docs\\workflows\\convergence.md", "agent_docs\\workflows\\fvg.md", "agent_docs\\workflows\\mathematical_validation_workflow.md", "agent_docs\\workflows\\mean_reversion.md", "agent_docs\\workflows\\output_coordination_workflow.md", "agent_docs\\workflows\\pivot.md", "agent_docs\\workflows\\signal_quality_workflow.md", "agents\\accumulation_distribution_detector\\accumulation_distribution_detector.py", "agents\\accumulation_distribution_detector\\enhanced_accumulation_distribution_agent.py", "agents\\agent_orchestrator\\agent_orchestrator.py", "agents\\agent_orchestrator\\agent_orchestrator_real_data_only.py", "agents\\breakout_validation_specialist\\breakout_validation_specialist.py", "agents\\options_flow_decoder\\options_flow_decoder.py"]}, "keep_active": ["agent_zero_advanced_capabilities.py", "agent_zero_integration_hub.py", "agent_zero_performance_analytics.py", "enhanced_data_agent_broker_integration.py", "live_market_test.py", "main.py", "orchestrator.py", "ultimate_orchestrator.py", "agents\\__init__.py", "analyzers\\__init__.py", "api\\__init__.py", "data\\__init__.py", "engine\\__init__.py", "tasks\\__init__.py", "utils\\__init__.py"]}}