#!/usr/bin/env python3
"""
COMPLETE SYSTEM TEST - DIRECTIONAL SIGNAL
Test Agent Zero with Enhanced Options for directional trades
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from agent_zero_complete_system import AgentZeroWithEnhancedOptions

def test_directional_signal():
    """Test with stronger signal that should trigger options analysis"""
    print("=" * 70)
    print("TESTING DIRECTIONAL SIGNAL WITH COMPLETE OPTIONS ANALYSIS")
    print("=" * 70)
    
    # Initialize enhanced Agent Zero
    enhanced_agent_zero = AgentZeroWithEnhancedOptions()
    print("[OK] Enhanced Agent Zero initialized")
    
    # Create stronger bullish signal
    signal_data = {
        'confidence': 0.85,      # Higher confidence
        'strength': 0.70,        # Strong signal
        'execution_recommendation': 'execute'
    }
    
    math_data = {
        'accuracy_score': 0.98,
        'precision': 0.0005
    }
    
    # Better market context (higher liquidity)
    market_context = {
        'current_price': 455.0,
        'liquidity_score': 0.85,    # Higher liquidity
        'volume_analysis': {'relative_volume': 45000000},
        'flow_analysis': {'momentum': 0.6, 'strength': 0.8},
        'iv_dynamics_analysis': {'iv_rank': 35},  # Cheaper IV
        'b_series_analysis': {'confidence': 0.9, 'pattern_strength': 0.85},
        'implied_volatility': 0.22,  # Lower IV
        'iv_rank': 35.0,
        'price_trend': 'bullish',
        'volatility_regime': 'normal_vol'
    }
    
    # Get complete trading decision
    complete_decision = enhanced_agent_zero.get_complete_trading_decision(
        signal_data=signal_data,
        math_data=math_data,
        market_context=market_context,
        ticker='SPY',
        fetch_options_data=False  # Use mock data
    )
    
    print(f"[OK] Decision Type: {complete_decision['decision_type']}")
    
    # Display comprehensive results
    final_rec = complete_decision['final_recommendation']
    print(f"\n{'='*50}")
    print(f"COMPLETE TRADING RECOMMENDATION")
    print(f"{'='*50}")
    print(f"Action: {final_rec['action']}")
    print(f"Confidence: {final_rec['confidence']:.1%}")
    
    if complete_decision['decision_type'] == 'enhanced_options':
        print(f"\nOPTIONS SPECIFICATION:")
        print(f"  Strike: ${final_rec['strike']}")
        print(f"  Option Type: {final_rec['option_type']}")
        print(f"  Position Size: {final_rec['position_size']:.1f} contracts")
        print(f"  Expiration: {final_rec['expiration_date']}")
        
        print(f"\nGREEK-BASED RISK METRICS:")
        print(f"  Delta: {final_rec['estimated_delta']:.3f}")
        print(f"  Daily Theta: {final_rec['daily_theta_cost']:.3f}")
        print(f"  IV Sensitivity: {final_rec['iv_sensitivity']:.4f}")
        print(f"  Gamma Risk: {final_rec['gamma_risk']:.4f}")
        
        print(f"\nTRADE CHARACTERISTICS:")
        print(f"  Classification: {final_rec['trade_classification']}")
        print(f"  Leverage: {final_rec['leverage_factor']:.1f}x")
        print(f"  Probability: {final_rec['probability_estimate']:.0%}")
        print(f"  Breakeven Move: {final_rec['breakeven_move_pct']:.1f}%")
        
        print(f"\nEXIT STRATEGY:")
        print(f"  Max Hold Days: {final_rec['max_hold_days']}")
        print(f"  Profit Targets: {len(final_rec['profit_targets'])}")
        print(f"  Stop Losses: {len(final_rec['stop_losses'])}")
        print(f"  Primary Exit: {final_rec['primary_exit_rule']}")
        
        print(f"\nRISK ASSESSMENT:")
        print(f"  Overall Risk: {final_rec['overall_risk_level']}")
        print(f"  Execution Quality: {final_rec['execution_quality']}")
        print(f"  Greek Engine Quality: {final_rec['greek_engine_quality']:.3f}")
        
        print(f"\nPRIMARY RISKS:")
        for risk in final_rec.get('primary_risks', []):
            print(f"  - {risk}")
    
    print(f"\nREASONING SUMMARY:")
    reasoning = final_rec.get('reasoning', 'No reasoning provided')
    if len(reasoning) > 200:
        print(f"  {reasoning[:200]}...")
    else:
        print(f"  {reasoning}")
    
    print(f"\n[SUCCESS] Complete directional signal test completed")
    return complete_decision

if __name__ == "__main__":
    result = test_directional_signal()
