{"timestamp": "2025-06-26T09:56:34.248994", "signal_data": {"confidence": 0.654, "strength": 0.42, "execution_recommendation": "execute"}, "math_data": {"accuracy_score": 0.9289, "precision": 0.0054}, "decision": {"timestamp": "2025-06-26T09:56:34.248956", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.03, "action": "hold", "confidence": 0.645935, "reasoning": ["Rule-based decision: composite score 0.646", "Signal confidence: 0.654", "Signal strength: 0.420", "Execution recommendation: execute", "Math accuracy: 0.929", "ML system not available - using rule-based logic"], "composite_score": 0.645935, "decision_method": "rule_based", "ml_enhanced": false}, "outcome": 0.0, "market_context": {"b_series_analysis": {"features": {}, "confidence": 0.85, "pattern_strength": 0.72}, "flow_analysis": {"momentum": 0.5, "direction": "bullish", "strength": 0.8}, "anomaly_analysis": {"anomaly_detected": false, "anomaly_score": 0.1}, "iv_dynamics_analysis": {"iv_rank": 35.0, "iv_expansion": false, "volatility_regime": "low"}, "market_regime": {"trend": "uptrend", "volatility": "low"}, "analysis_source": "test_system", "timestamp": "2025-06-26T10:00:00", "ticker": "SPY"}, "agent_version": "1.5_Integrated"}