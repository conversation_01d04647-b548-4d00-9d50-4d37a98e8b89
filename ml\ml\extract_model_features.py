"""
Extract Exact Feature Names from ML Models

This script extracts the exact 61 feature names that the trained models expect.
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import logging

# Add the parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_model_feature_names():
    """Extract exact feature names from trained models."""
    
    models_dir = os.path.join(os.path.dirname(__file__), 'models')
    
    # Load level_strength_model.pkl
    model_path = os.path.join(models_dir, 'level_strength_model.pkl')
    try:
        model = joblib.load(model_path)
        
        feature_names = None
        if hasattr(model, 'feature_names_in_'):
            feature_names = list(model.feature_names_in_)
        
        logger.info(f"Extracted {len(feature_names) if feature_names else 0} feature names from level_strength_model.pkl")
        
        if feature_names:
            print("EXACT FEATURE NAMES FROM TRAINED MODEL:")
            print("=" * 50)
            for i, feature in enumerate(feature_names):
                print(f"{i+1:2d}: {feature}")
            
            # Save to a new file
            correct_features_file = os.path.join(models_dir, 'correct_features_61.txt')
            with open(correct_features_file, 'w') as f:
                for feature in feature_names:
                    f.write(f"{feature}\n")
            
            logger.info(f"Saved correct features to: {correct_features_file}")
            
            return feature_names
        else:
            logger.error("Could not extract feature names from model")
            return None
            
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return None

def main():
    logger.info("Extracting exact feature names from trained models")
    feature_names = extract_model_feature_names()
    
    if feature_names:
        print(f"\nSUCCESS: Extracted {len(feature_names)} feature names")
        print("These are the EXACT features the ML models expect.")
    else:
        print("FAILED: Could not extract feature names")

if __name__ == "__main__":
    main()
