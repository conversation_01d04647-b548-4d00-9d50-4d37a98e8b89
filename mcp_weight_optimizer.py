#!/usr/bin/env python3
"""
Empirical Weight Optimization for Agent Zero - MCP Integration
Uses actual Schwab MCP server endpoints with real data generation
"""

import json
import requests
import numpy as np
from datetime import datetime
import statistics
from pathlib import Path

class MCPWeightOptimizer:
    """
    Empirical testing framework using actual MCP server endpoints
    """
    
    def __init__(self, mcp_url="http://localhost:8005"):
        self.mcp_url = mcp_url
        self.test_results = []
        
        # Test universe - mix of different market characteristics
        self.test_tickers = [
            'SPY',   # High liquidity ETF
            'QQQ',   # High liquidity tech ETF  
            'IWM',   # Small cap ETF
            'AAPL',  # Large cap tech
            'TSLA',  # High volatility
            'AMD',   # Semiconductor
            'XLF',   # Financial sector
            'NVDA',  # AI/Tech high vol
            'AMZN',  # E-commerce large cap
            'MSFT'   # Stable large cap
        ]
        
        print(f"MCP Weight Optimizer initialized")
        print(f"MCP Server: {self.mcp_url}")
        print(f"Test Universe: {len(self.test_tickers)} symbols")
    
    def test_mcp_connection(self):
        """Test MCP server connectivity"""
        try:
            response = requests.get(f"{self.mcp_url}/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"MCP Server Status: CONNECTED")
                print(f"  Uptime: {health_data.get('uptime_seconds', 0):.1f} seconds")
                print(f"  Requests: {health_data.get('request_count', 0)}")
                return True
            else:
                print(f"MCP Server Status: ERROR ({response.status_code})")
                return False
        except Exception as e:
            print(f"MCP Server Status: FAILED - {e}")
            return False
    
    def fetch_mcp_data(self, ticker):
        """Fetch quote and options data from MCP server"""
        try:
            # Get quote data
            quote_response = requests.get(f"{self.mcp_url}/quotes/{ticker}", timeout=10)
            if quote_response.status_code != 200:
                print(f"  Failed to get quote for {ticker}")
                return None
            
            quote_data = quote_response.json()
            
            # Get options data
            options_response = requests.get(f"{self.mcp_url}/options/{ticker}", timeout=10)
            if options_response.status_code != 200:
                print(f"  Failed to get options for {ticker}")
                return None
            
            options_data = options_response.json()
            
            # Combine data
            market_data = {
                'ticker': ticker,
                'quote': quote_data,
                'options': options_data,
                'timestamp': datetime.now().isoformat()
            }
            
            return market_data
            
        except Exception as e:
            print(f"  Error fetching MCP data for {ticker}: {e}")
            return None
    
    def calculate_liquidity_metrics(self, market_data):
        """Calculate liquidity metrics from MCP data"""
        try:
            quote = market_data['quote']
            options = market_data['options']
            
            # Volume liquidity
            volume = quote.get('volume', 0)
            avg_volume = quote.get('avg_volume', volume)
            relative_volume = volume / avg_volume if avg_volume > 0 else 1.0
            volume_liquidity = min(relative_volume / 2.0, 1.0)
            
            # Spread liquidity (from bid-ask)
            bid = quote.get('bid', 0)
            ask = quote.get('ask', 0)
            price = quote.get('price', (bid + ask) / 2 if bid and ask else 100)
            
            if bid > 0 and ask > 0:
                spread_pct = (ask - bid) / price
                spread_liquidity = max(1.0 - (spread_pct * 100), 0.0)  # Lower spread = higher liquidity
            else:
                spread_liquidity = 0.5
            
            # Options liquidity
            chains = options.get('chains', {})
            total_open_interest = 0
            total_volume = 0
            
            for expiry, strikes in chains.items():
                for strike_data in strikes:
                    if 'calls' in strike_data:
                        call_data = strike_data['calls']
                        total_open_interest += call_data.get('open_interest', 0)
                        total_volume += call_data.get('volume', 0)
                    if 'puts' in strike_data:
                        put_data = strike_data['puts']
                        total_open_interest += put_data.get('open_interest', 0)
                        total_volume += put_data.get('volume', 0)
            
            # Options liquidity scoring
            oi_score = min(total_open_interest / 50000, 1.0)  # Normalize by 50k OI
            options_volume_score = min(total_volume / 1000, 1.0)  # Normalize by 1k volume
            options_liquidity = (oi_score + options_volume_score) / 2
            
            # IV metrics
            iv_rank = options.get('iv_rank', 50.0)
            if 25 <= iv_rank <= 75:
                iv_liquidity = 1.0
            elif 15 <= iv_rank <= 85:
                iv_liquidity = 0.8
            else:
                iv_liquidity = 0.6
            
            # Composite liquidity score
            liquidity_score = (
                volume_liquidity * 0.30 +
                spread_liquidity * 0.25 +
                options_liquidity * 0.25 +
                iv_liquidity * 0.20
            )
            
            liquidity_metrics = {
                'composite_score': min(max(liquidity_score, 0.0), 1.0),
                'volume_liquidity': volume_liquidity,
                'spread_liquidity': spread_liquidity,
                'options_liquidity': options_liquidity,
                'iv_liquidity': iv_liquidity,
                'relative_volume': relative_volume,
                'spread_pct': spread_pct if 'spread_pct' in locals() else 0.01,
                'total_open_interest': total_open_interest,
                'options_volume': total_volume
            }
            
            return liquidity_metrics
            
        except Exception as e:
            print(f"Error calculating liquidity metrics: {e}")
            return {'composite_score': 0.5}
    
    def calculate_signal_metrics(self, market_data):
        """Extract signal metrics from MCP data"""
        try:
            quote = market_data['quote']
            options = market_data['options']
            
            # Price momentum (using price vs moving average proxy)
            price = quote.get('price', 100)
            day_change = quote.get('change', 0)
            day_change_pct = quote.get('change_percent', 0)
            
            # Signal confidence based on price action
            abs_change_pct = abs(day_change_pct)
            if abs_change_pct > 3.0:
                signal_confidence = 0.9  # Strong move
            elif abs_change_pct > 1.5:
                signal_confidence = 0.7  # Moderate move
            elif abs_change_pct > 0.5:
                signal_confidence = 0.6  # Weak move
            else:
                signal_confidence = 0.4  # No clear signal
            
            # Signal strength based on volume confirmation
            volume = quote.get('volume', 0)
            avg_volume = quote.get('avg_volume', volume)
            relative_volume = volume / avg_volume if avg_volume > 0 else 1.0
            
            volume_confirmation = min(relative_volume / 2.0, 1.0)
            signal_strength = signal_confidence * volume_confirmation
            
            # Execution recommendation based on options flow
            iv_rank = options.get('iv_rank', 50.0)
            
            if signal_confidence > 0.7 and volume_confirmation > 0.8:
                execution_rec = 'execute'
            elif signal_confidence > 0.5 and volume_confirmation > 0.5:
                execution_rec = 'hold'
            else:
                execution_rec = 'avoid'
            
            signal_metrics = {
                'confidence': signal_confidence,
                'strength': signal_strength,
                'execution_recommendation': execution_rec,
                'price_momentum': day_change_pct,
                'volume_confirmation': volume_confirmation
            }
            
            return signal_metrics
            
        except Exception as e:
            print(f"Error calculating signal metrics: {e}")
            return {
                'confidence': 0.5,
                'strength': 0.5,
                'execution_recommendation': 'hold'
            }
    
    def calculate_math_metrics(self, market_data, liquidity_metrics):
        """Calculate mathematical accuracy and precision metrics"""
        try:
            # Math accuracy correlates with data quality and liquidity
            liquidity_score = liquidity_metrics.get('composite_score', 0.5)
            spread_quality = 1.0 - liquidity_metrics.get('spread_pct', 0.01)
            
            # Higher liquidity = more accurate mathematical models
            math_accuracy = 0.7 + (liquidity_score * 0.25) + (spread_quality * 0.05)
            math_accuracy = min(max(math_accuracy, 0.5), 0.99)
            
            # Precision inversely related to volatility
            quote = market_data['quote']
            volatility = abs(quote.get('change_percent', 1.0)) / 100.0
            math_precision = 0.001 + (volatility * 0.003)
            math_precision = min(max(math_precision, 0.0001), 0.01)
            
            return {
                'accuracy_score': math_accuracy,
                'precision': math_precision
            }
            
        except Exception as e:
            print(f"Error calculating math metrics: {e}")
            return {
                'accuracy_score': 0.8,
                'precision': 0.001
            }
    
    def generate_weight_configurations(self):
        """Generate weight configurations to test"""
        return [
            {
                'name': 'liquidity_heavy',
                'liquidity_score': 0.45,
                'signal_confidence': 0.20,
                'signal_strength': 0.15,
                'execution_recommendation': 0.12,
                'math_accuracy': 0.05,
                'math_precision': 0.03
            },
            {
                'name': 'balanced_approach',
                'liquidity_score': 0.30,
                'signal_confidence': 0.25,
                'signal_strength': 0.20,
                'execution_recommendation': 0.15,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            {
                'name': 'signal_focused',
                'liquidity_score': 0.25,
                'signal_confidence': 0.30,
                'signal_strength': 0.25,
                'execution_recommendation': 0.12,
                'math_accuracy': 0.05,
                'math_precision': 0.03
            },
            {
                'name': 'execution_priority',
                'liquidity_score': 0.35,
                'signal_confidence': 0.15,
                'signal_strength': 0.15,
                'execution_recommendation': 0.25,
                'math_accuracy': 0.06,
                'math_precision': 0.04
            },
            {
                'name': 'conservative_liquidity',
                'liquidity_score': 0.50,
                'signal_confidence': 0.18,
                'signal_strength': 0.12,
                'execution_recommendation': 0.12,
                'math_accuracy': 0.05,
                'math_precision': 0.03
            }
        ]
    
    def test_weight_configuration(self, weights, market_dataset):
        """Test a weight configuration against market data"""
        results = {
            'configuration': weights['name'],
            'weights': weights,
            'decisions': [],
            'performance_metrics': {}
        }
        
        decision_outcomes = []
        liquidity_scores = []
        confidence_scores = []
        
        for ticker, market_data in market_dataset.items():
            if market_data is None:
                continue
            
            try:
                # Calculate all metrics
                liquidity_metrics = self.calculate_liquidity_metrics(market_data)
                signal_metrics = self.calculate_signal_metrics(market_data)
                math_metrics = self.calculate_math_metrics(market_data, liquidity_metrics)
                
                # Apply weight configuration
                liquidity_score = liquidity_metrics['composite_score']
                signal_confidence = signal_metrics['confidence']
                signal_strength = signal_metrics['strength']
                exec_recommendation = signal_metrics['execution_recommendation']
                math_accuracy = math_metrics['accuracy_score']
                math_precision = math_metrics['precision']
                
                # Calculate weighted composite score
                exec_score = {'execute': 1.0, 'hold': 0.5, 'avoid': 0.0}.get(exec_recommendation, 0.5)
                
                composite_score = (
                    liquidity_score * weights['liquidity_score'] +
                    signal_confidence * weights['signal_confidence'] +
                    signal_strength * weights['signal_strength'] +
                    exec_score * weights['execution_recommendation'] +
                    math_accuracy * weights['math_accuracy'] +
                    min(math_precision, 0.1) * 10 * weights['math_precision']
                )
                
                # Decision logic with liquidity gates
                if liquidity_score < 0.25:
                    decision = 'avoid'
                    final_confidence = composite_score * 0.5
                elif composite_score >= 0.75:
                    decision = 'execute'
                    final_confidence = composite_score
                elif composite_score >= 0.25:
                    decision = 'hold'
                    final_confidence = composite_score
                else:
                    decision = 'avoid'
                    final_confidence = composite_score
                
                # Record decision
                decision_record = {
                    'ticker': ticker,
                    'liquidity_score': liquidity_score,
                    'signal_confidence': signal_confidence,
                    'signal_strength': signal_strength,
                    'composite_score': composite_score,
                    'decision': decision,
                    'final_confidence': final_confidence,
                    'liquidity_metrics': liquidity_metrics,
                    'signal_metrics': signal_metrics
                }
                
                results['decisions'].append(decision_record)
                decision_outcomes.append(composite_score)
                liquidity_scores.append(liquidity_score)
                confidence_scores.append(final_confidence)
                
            except Exception as e:
                print(f"    Error processing {ticker}: {e}")
                continue
        
        # Calculate performance metrics
        if decision_outcomes:
            metrics = {
                'total_decisions': len(decision_outcomes),
                'avg_composite_score': statistics.mean(decision_outcomes),
                'composite_variance': statistics.variance(decision_outcomes) if len(decision_outcomes) > 1 else 0,
                'avg_liquidity_score': statistics.mean(liquidity_scores),
                'liquidity_variance': statistics.variance(liquidity_scores) if len(liquidity_scores) > 1 else 0,
                'avg_confidence': statistics.mean(confidence_scores),
                'confidence_variance': statistics.variance(confidence_scores) if len(confidence_scores) > 1 else 0,
                'execute_decisions': sum(1 for d in results['decisions'] if d['decision'] == 'execute'),
                'hold_decisions': sum(1 for d in results['decisions'] if d['decision'] == 'hold'),
                'avoid_decisions': sum(1 for d in results['decisions'] if d['decision'] == 'avoid'),
                'liquidity_utilization': sum(1 for d in results['decisions'] if d['liquidity_score'] > 0.5) / len(results['decisions'])
            }
            
            results['performance_metrics'] = metrics
        
        return results
    
    def run_optimization(self):
        """Run the complete empirical optimization"""
        print("\n" + "=" * 60)
        print("EMPIRICAL WEIGHT OPTIMIZATION - MCP DATA DRIVEN")
        print("=" * 60)
        
        # Test MCP connection
        if not self.test_mcp_connection():
            print("ERROR: Cannot connect to MCP server. Exiting.")
            return None
        
        # Fetch market data
        print(f"\nFetching market data for {len(self.test_tickers)} symbols...")
        market_dataset = {}
        
        for ticker in self.test_tickers:
            print(f"  Fetching {ticker}...", end="")
            market_data = self.fetch_mcp_data(ticker)
            market_dataset[ticker] = market_data
            
            if market_data:
                print(" SUCCESS")
            else:
                print(" FAILED")
        
        valid_count = sum(1 for data in market_dataset.values() if data is not None)
        print(f"\nValid data: {valid_count}/{len(self.test_tickers)} symbols")
        
        if valid_count == 0:
            print("ERROR: No valid market data. Check MCP server.")
            return None
        
        # Test weight configurations
        configurations = self.generate_weight_configurations()
        print(f"\nTesting {len(configurations)} weight configurations...")
        
        all_results = []
        
        for i, config in enumerate(configurations):
            print(f"\n  Testing {i+1}/{len(configurations)}: {config['name']}")
            result = self.test_weight_configuration(config, market_dataset)
            all_results.append(result)
            
            # Print metrics
            metrics = result['performance_metrics']
            if metrics:
                print(f"    Decisions: {metrics['total_decisions']}")
                print(f"    Avg Liquidity: {metrics['avg_liquidity_score']:.3f}")
                print(f"    Avg Composite: {metrics['avg_composite_score']:.3f}")
                print(f"    Execute/Hold/Avoid: {metrics['execute_decisions']}/{metrics['hold_decisions']}/{metrics['avoid_decisions']}")
        
        # Analyze results
        return self.analyze_results(all_results)
    
    def analyze_results(self, all_results):
        """Analyze and rank results"""
        print("\n" + "=" * 60)
        print("OPTIMIZATION RESULTS ANALYSIS")
        print("=" * 60)
        
        # Score each configuration
        scored_results = []
        
        for result in all_results:
            metrics = result['performance_metrics']
            if not metrics:
                continue
            
            # Performance scoring
            score = (
                metrics['avg_liquidity_score'] * 0.3 +       # 30% liquidity utilization
                metrics['liquidity_variance'] * 0.2 +        # 20% liquidity discrimination
                metrics['avg_composite_score'] * 0.25 +      # 25% decision quality
                metrics['confidence_variance'] * 0.15 +      # 15% confidence range
                (metrics['execute_decisions'] / metrics['total_decisions']) * 0.1  # 10% execution rate
            )
            
            scored_results.append({
                'config': result['configuration'],
                'weights': result['weights'],
                'score': score,
                'metrics': metrics,
                'decisions': result['decisions']
            })
        
        # Rank by score
        scored_results.sort(key=lambda x: x['score'], reverse=True)
        
        # Print rankings
        print(f"\nRANKINGS (Higher score = better performance):")
        print("-" * 70)
        
        for i, result in enumerate(scored_results):
            config = result['config']
            score = result['score']
            metrics = result['metrics']
            weights = result['weights']
            
            print(f"\n{i+1}. {config.upper()}")
            print(f"   Performance Score: {score:.4f}")
            print(f"   Liquidity Weight: {weights['liquidity_score']:.2f}")
            print(f"   Avg Liquidity Score: {metrics['avg_liquidity_score']:.3f}")
            print(f"   Decision Quality: {metrics['avg_composite_score']:.3f}")
            print(f"   Decision Mix: {metrics['execute_decisions']}E/{metrics['hold_decisions']}H/{metrics['avoid_decisions']}A")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"mcp_weight_optimization_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(scored_results, f, indent=2, default=str)
        
        print(f"\n📊 Results saved to: {filename}")
        
        # Recommendation
        if scored_results:
            best = scored_results[0]
            print(f"\n🏆 OPTIMAL CONFIGURATION: {best['config']}")
            print("Recommended weights:")
            for key, value in best['weights'].items():
                if key != 'name':
                    print(f"  {key}: {value:.3f}")
        
        return scored_results

def main():
    """Run MCP-based empirical optimization"""
    optimizer = MCPWeightOptimizer()
    
    try:
        results = optimizer.run_optimization()
        
        if results:
            print(f"\n🎯 Empirical optimization completed!")
            print(f"   Tested {len(results)} configurations with real MCP data")
            print(f"   Best performing weights identified")
            return True
        else:
            print(f"\n❌ Optimization failed")
            return False
            
    except Exception as e:
        print(f"❌ Optimization error: {e}")
        return False

if __name__ == "__main__":
    main()
