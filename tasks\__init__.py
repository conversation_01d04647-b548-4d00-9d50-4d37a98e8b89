"""
B-Series Backtest Tasks Module

Historical Back-test & ML Walk-Forward Implementation
"""

from .fetch_history import HistoricalDataFetcher
from .build_features import FeatureBuilder
from .walk_train_validate import WalkForwardValidator
from .backtest_simulator import BacktestSimulator

__all__ = [
    'HistoricalDataFetcher',
    'FeatureBuilder', 
    'WalkForwardValidator',
    'BacktestSimulator'
]

__version__ = '1.0.0'
