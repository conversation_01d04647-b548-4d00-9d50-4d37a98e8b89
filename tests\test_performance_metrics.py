import unittest
import sys
from pathlib import Path
from datetime import datetime, timed<PERSON><PERSON>
from typing import List
import pandas as pd
import numpy as np
import tempfile
import shutil
import json

# Add the parent directory to the sys.path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from backtesting.performance_metrics import (
    PerformanceAnalyzer, RiskMetrics, ReturnMetrics, TradeMetrics, CostMetrics,
    PerformanceReport, ReportFormat, BenchmarkType
)


class MockTrade:
    """Mock trade object for testing."""
    def __init__(self, side: str, quantity: float, price: float, commission: float = 0, slippage: float = 0):
        self.side = side
        self.quantity = quantity
        self.price = price
        self.commission = commission
        self.slippage = slippage


class TestRiskMetrics(unittest.TestCase):
    """Test cases for RiskMetrics dataclass."""

    def test_risk_metrics_creation(self):
        """Test RiskMetrics creation and to_dict method."""
        metrics = RiskMetrics(
            volatility=0.15,
            sharpe_ratio=1.2,
            sortino_ratio=1.5,
            calmar_ratio=2.0,
            max_drawdown=-0.08,
            max_drawdown_duration=30,
            var_95=-0.02,
            cvar_95=-0.035,
            beta=1.1,
            alpha=0.02,
            correlation=0.8
        )
        
        self.assertEqual(metrics.volatility, 0.15)
        self.assertEqual(metrics.sharpe_ratio, 1.2)
        self.assertEqual(metrics.max_drawdown, -0.08)
        
        # Test to_dict
        metrics_dict = metrics.to_dict()
        self.assertIn('volatility', metrics_dict)
        self.assertEqual(metrics_dict['sharpe_ratio'], 1.2)


class TestReturnMetrics(unittest.TestCase):
    """Test cases for ReturnMetrics dataclass."""

    def test_return_metrics_creation(self):
        """Test ReturnMetrics creation and to_dict method."""
        metrics = ReturnMetrics(
            total_return=0.25,
            annualized_return=0.12,
            monthly_returns=[0.02, 0.01, -0.01, 0.03],
            yearly_returns=[0.12],
            best_month=0.03,
            worst_month=-0.01,
            best_year=0.12,
            worst_year=0.12,
            positive_months=3,
            negative_months=1,
            win_rate_monthly=0.75
        )
        
        self.assertEqual(metrics.total_return, 0.25)
        self.assertEqual(metrics.win_rate_monthly, 0.75)
        self.assertEqual(len(metrics.monthly_returns), 4)
        
        # Test to_dict
        metrics_dict = metrics.to_dict()
        self.assertIn('total_return', metrics_dict)
        self.assertEqual(metrics_dict['positive_months'], 3)


class TestTradeMetrics(unittest.TestCase):
    """Test cases for TradeMetrics dataclass."""

    def test_trade_metrics_creation(self):
        """Test TradeMetrics creation and to_dict method."""
        metrics = TradeMetrics(
            total_trades=100,
            winning_trades=60,
            losing_trades=40,
            win_rate=0.6,
            avg_win=150.0,
            avg_loss=-75.0,
            largest_win=500.0,
            largest_loss=-200.0,
            profit_factor=1.2,
            expectancy=45.0,
            avg_trade_duration=2.5,
            max_consecutive_wins=5,
            max_consecutive_losses=3
        )
        
        self.assertEqual(metrics.total_trades, 100)
        self.assertEqual(metrics.win_rate, 0.6)
        self.assertEqual(metrics.profit_factor, 1.2)
        
        # Test to_dict
        metrics_dict = metrics.to_dict()
        self.assertIn('total_trades', metrics_dict)
        self.assertEqual(metrics_dict['winning_trades'], 60)


class TestCostMetrics(unittest.TestCase):
    """Test cases for CostMetrics dataclass."""

    def test_cost_metrics_creation(self):
        """Test CostMetrics creation and to_dict method."""
        metrics = CostMetrics(
            total_commission=500.0,
            total_slippage=200.0,
            total_costs=700.0,
            cost_as_percent_of_pnl=2.5,
            avg_commission_per_trade=5.0,
            avg_slippage_per_trade=2.0
        )
        
        self.assertEqual(metrics.total_commission, 500.0)
        self.assertEqual(metrics.total_costs, 700.0)
        
        # Test to_dict
        metrics_dict = metrics.to_dict()
        self.assertIn('total_commission', metrics_dict)
        self.assertEqual(metrics_dict['total_costs'], 700.0)


class TestPerformanceReport(unittest.TestCase):
    """Test cases for PerformanceReport dataclass."""

    def setUp(self):
        """Set up test fixtures."""
        self.risk_metrics = RiskMetrics(0.15, 1.2, 1.5, 2.0, -0.08, 30, -0.02, -0.035)
        self.return_metrics = ReturnMetrics(0.25, 0.12, [], [], 0.03, -0.01, 0.12, 0.12, 3, 1, 0.75)
        self.trade_metrics = TradeMetrics(100, 60, 40, 0.6, 150.0, -75.0, 500.0, -200.0, 1.2, 45.0, 2.5, 5, 3)
        self.cost_metrics = CostMetrics(500.0, 200.0, 700.0, 2.5, 5.0, 2.0)

    def test_performance_report_creation(self):
        """Test PerformanceReport creation."""
        report = PerformanceReport(
            strategy_name="Test Strategy",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            initial_capital=100000.0,
            final_capital=125000.0,
            benchmark_name="SPY",
            risk_metrics=self.risk_metrics,
            return_metrics=self.return_metrics,
            trade_metrics=self.trade_metrics,
            cost_metrics=self.cost_metrics
        )
        
        self.assertEqual(report.strategy_name, "Test Strategy")
        self.assertEqual(report.initial_capital, 100000.0)
        self.assertEqual(report.final_capital, 125000.0)
        self.assertEqual(report.benchmark_name, "SPY")

    def test_performance_report_to_dict(self):
        """Test PerformanceReport to_dict method."""
        report = PerformanceReport(
            strategy_name="Test Strategy",
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 12, 31),
            initial_capital=100000.0,
            final_capital=125000.0,
            benchmark_name=None,
            risk_metrics=self.risk_metrics,
            return_metrics=self.return_metrics,
            trade_metrics=self.trade_metrics,
            cost_metrics=self.cost_metrics
        )
        
        report_dict = report.to_dict()
        
        self.assertIn('strategy_name', report_dict)
        self.assertIn('start_date', report_dict)
        self.assertIn('risk_metrics', report_dict)
        self.assertIn('return_metrics', report_dict)
        self.assertIn('trade_metrics', report_dict)
        self.assertIn('cost_metrics', report_dict)
        
        self.assertEqual(report_dict['strategy_name'], "Test Strategy")
        self.assertEqual(report_dict['initial_capital'], 100000.0)


class TestPerformanceAnalyzer(unittest.TestCase):
    """Test cases for PerformanceAnalyzer class."""

    def setUp(self):
        """Set up test fixtures."""
        self.analyzer = PerformanceAnalyzer(risk_free_rate=0.02)
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir)

    def create_sample_portfolio_values(self, days: int = 252, initial_value: float = 100000.0) -> pd.Series:
        """Create sample portfolio values for testing."""
        dates = pd.date_range('2023-01-01', periods=days, freq='D')
        
        # Create realistic returns with some volatility
        daily_returns = np.random.normal(0.0005, 0.015, days)  # ~13% annual return, 24% vol
        
        # Add some trend and mean reversion
        cumulative_returns = np.cumsum(daily_returns)
        values = initial_value * np.exp(cumulative_returns)
        
        return pd.Series(values, index=dates)

    def create_sample_trades(self, num_trades: int = 50) -> List[MockTrade]:
        """Create sample trades for testing."""
        trades = []
        
        for i in range(num_trades):
            side = 'buy' if i % 2 == 0 else 'sell'
            quantity = np.random.uniform(50, 200)
            price = np.random.uniform(100, 200)
            commission = quantity * price * 0.001  # 0.1% commission
            slippage = np.random.uniform(0.01, 0.05)
            
            trades.append(MockTrade(side, quantity, price, commission, slippage))
        
        return trades

    def test_initialization(self):
        """Test PerformanceAnalyzer initialization."""
        self.assertEqual(self.analyzer.risk_free_rate, 0.02)

    def test_analyze_performance_basic(self):
        """Test basic performance analysis."""
        portfolio_values = self.create_sample_portfolio_values(100)
        trades = self.create_sample_trades(20)
        
        report = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            trades=trades,
            initial_capital=100000.0,
            strategy_name="Test Strategy"
        )
        
        self.assertIsInstance(report, PerformanceReport)
        self.assertEqual(report.strategy_name, "Test Strategy")
        self.assertEqual(report.initial_capital, 100000.0)
        self.assertIsInstance(report.risk_metrics, RiskMetrics)
        self.assertIsInstance(report.return_metrics, ReturnMetrics)
        self.assertIsInstance(report.trade_metrics, TradeMetrics)
        self.assertIsInstance(report.cost_metrics, CostMetrics)

    def test_analyze_performance_empty_portfolio(self):
        """Test performance analysis with empty portfolio."""
        empty_portfolio = pd.Series(dtype=float)
        
        with self.assertRaises(ValueError):
            self.analyzer.analyze_performance(
                portfolio_values=empty_portfolio,
                trades=[],
                initial_capital=100000.0
            )

    def test_calculate_risk_metrics(self):
        """Test risk metrics calculation."""
        # Create returns with known characteristics
        dates = pd.date_range('2023-01-01', periods=252, freq='D')
        returns = pd.Series(np.random.normal(0.001, 0.02, 252), index=dates)
        
        # Create drawdowns
        cumulative_returns = (1 + returns).cumprod()
        peak_values = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - peak_values) / peak_values
        
        risk_metrics = self.analyzer._calculate_risk_metrics(returns, drawdowns)
        
        self.assertIsInstance(risk_metrics, RiskMetrics)
        self.assertGreater(risk_metrics.volatility, 0)
        self.assertIsInstance(risk_metrics.sharpe_ratio, float)
        self.assertLessEqual(risk_metrics.max_drawdown, 0)
        self.assertGreaterEqual(risk_metrics.max_drawdown_duration, 0)

    def test_calculate_risk_metrics_with_benchmark(self):
        """Test risk metrics calculation with benchmark."""
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        returns = pd.Series(np.random.normal(0.001, 0.02, 100), index=dates)
        benchmark_returns = pd.Series(np.random.normal(0.0008, 0.015, 100), index=dates)
        
        cumulative_returns = (1 + returns).cumprod()
        peak_values = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - peak_values) / peak_values
        
        risk_metrics = self.analyzer._calculate_risk_metrics(returns, drawdowns, benchmark_returns)
        
        self.assertIsNotNone(risk_metrics.beta)
        self.assertIsNotNone(risk_metrics.alpha)
        self.assertIsNotNone(risk_metrics.correlation)

    def test_calculate_max_drawdown_duration(self):
        """Test maximum drawdown duration calculation."""
        # Create drawdown series with known patterns
        # Pattern: [0, -0.01, -0.02, -0.01, 0, 0, -0.03, -0.04, -0.02, 0]
        # Drawdown periods: positions 1-3 (3 periods), positions 6-8 (3 periods)
        drawdowns = pd.Series([0, -0.01, -0.02, -0.01, 0, 0, -0.03, -0.04, -0.02, 0])
        
        max_duration = self.analyzer._calculate_max_drawdown_duration(drawdowns)
        
        # Should find the longest drawdown period (3 periods)
        self.assertEqual(max_duration, 3)

    def test_calculate_return_metrics(self):
        """Test return metrics calculation."""
        portfolio_values = self.create_sample_portfolio_values(365)  # Full year
        returns = portfolio_values.pct_change().dropna()
        
        return_metrics = self.analyzer._calculate_return_metrics(portfolio_values, returns, 100000.0)
        
        self.assertIsInstance(return_metrics, ReturnMetrics)
        self.assertIsInstance(return_metrics.total_return, float)
        self.assertIsInstance(return_metrics.annualized_return, float)
        self.assertIsInstance(return_metrics.monthly_returns, list)
        self.assertGreaterEqual(return_metrics.win_rate_monthly, 0)
        self.assertLessEqual(return_metrics.win_rate_monthly, 1)

    def test_calculate_trade_metrics_empty(self):
        """Test trade metrics calculation with empty trades."""
        trade_metrics = self.analyzer._calculate_trade_metrics([])
        
        self.assertEqual(trade_metrics.total_trades, 0)
        self.assertEqual(trade_metrics.winning_trades, 0)
        self.assertEqual(trade_metrics.win_rate, 0)

    def test_calculate_trade_metrics_with_trades(self):
        """Test trade metrics calculation with actual trades."""
        trades = [
            MockTrade('sell', 100, 110),  # Profit: +11000
            MockTrade('buy', 100, 105),   # Cost: -10500
            MockTrade('sell', 50, 120),   # Profit: +6000
            MockTrade('buy', 50, 115),    # Cost: -5750
        ]
        
        trade_metrics = self.analyzer._calculate_trade_metrics(trades)
        
        self.assertEqual(trade_metrics.total_trades, 4)
        self.assertGreater(trade_metrics.winning_trades, 0)
        self.assertGreater(trade_metrics.losing_trades, 0)
        self.assertGreater(trade_metrics.win_rate, 0)
        self.assertLess(trade_metrics.win_rate, 1)

    def test_calculate_cost_metrics(self):
        """Test cost metrics calculation."""
        trades = [
            MockTrade('buy', 100, 100, commission=10, slippage=0.1),
            MockTrade('sell', 100, 110, commission=11, slippage=0.1)
        ]
        
        cost_metrics = self.analyzer._calculate_cost_metrics(trades, 1000.0)
        
        self.assertEqual(cost_metrics.total_commission, 21.0)
        self.assertEqual(cost_metrics.total_slippage, 20.0)  # 0.1 * 100 * 2
        self.assertEqual(cost_metrics.total_costs, 41.0)
        self.assertGreater(cost_metrics.cost_as_percent_of_pnl, 0)

    def test_calculate_monthly_performance(self):
        """Test monthly performance calculation."""
        portfolio_values = self.create_sample_portfolio_values(365)
        returns = portfolio_values.pct_change().dropna()
        
        monthly_performance = self.analyzer._calculate_monthly_performance(portfolio_values, returns)
        
        self.assertIsInstance(monthly_performance, pd.DataFrame)
        if not monthly_performance.empty:
            self.assertIn('year', monthly_performance.columns)
            self.assertIn('month', monthly_performance.columns)
            self.assertIn('return', monthly_performance.columns)

    def test_calculate_yearly_performance(self):
        """Test yearly performance calculation."""
        portfolio_values = self.create_sample_portfolio_values(365)
        returns = portfolio_values.pct_change().dropna()
        
        yearly_performance = self.analyzer._calculate_yearly_performance(portfolio_values, returns)
        
        self.assertIsInstance(yearly_performance, pd.DataFrame)
        # May be empty if less than a full year of data

    def test_calculate_rolling_metrics(self):
        """Test rolling metrics calculation."""
        returns = pd.Series(np.random.normal(0.001, 0.02, 300), 
                          index=pd.date_range('2023-01-01', periods=300, freq='D'))
        
        rolling_metrics = self.analyzer._calculate_rolling_metrics(returns, window=60)
        
        self.assertIsInstance(rolling_metrics, pd.DataFrame)
        if not rolling_metrics.empty:
            self.assertIn('rolling_return', rolling_metrics.columns)
            self.assertIn('rolling_volatility', rolling_metrics.columns)
            self.assertIn('rolling_sharpe', rolling_metrics.columns)

    def test_generate_json_report(self):
        """Test JSON report generation."""
        portfolio_values = self.create_sample_portfolio_values(100)
        trades = self.create_sample_trades(10)
        
        report = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            trades=trades,
            initial_capital=100000.0,
            strategy_name="Test Strategy"
        )
        
        output_path = Path(self.temp_dir) / "test_report"
        generated_path = self.analyzer.generate_report(report, str(output_path), ReportFormat.JSON)
        
        self.assertTrue(Path(generated_path).exists())
        self.assertTrue(generated_path.endswith('.json'))
        
        # Verify JSON content
        with open(generated_path, 'r') as f:
            report_data = json.load(f)
        
        self.assertIn('strategy_name', report_data)
        self.assertEqual(report_data['strategy_name'], "Test Strategy")

    def test_generate_html_report(self):
        """Test HTML report generation."""
        portfolio_values = self.create_sample_portfolio_values(100)
        trades = self.create_sample_trades(10)
        
        report = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            trades=trades,
            initial_capital=100000.0,
            strategy_name="Test Strategy"
        )
        
        output_path = Path(self.temp_dir) / "test_report"
        generated_path = self.analyzer.generate_report(report, str(output_path), ReportFormat.HTML)
        
        self.assertTrue(Path(generated_path).exists())
        self.assertTrue(generated_path.endswith('.html'))
        
        # Verify HTML content contains expected elements
        with open(generated_path, 'r') as f:
            html_content = f.read()
        
        self.assertIn('Performance Report', html_content)
        self.assertIn('Test Strategy', html_content)
        self.assertIn('Return Metrics', html_content)

    def test_generate_csv_report(self):
        """Test CSV report generation."""
        portfolio_values = self.create_sample_portfolio_values(100)
        trades = self.create_sample_trades(10)
        
        report = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            trades=trades,
            initial_capital=100000.0,
            strategy_name="Test Strategy"
        )
        
        output_path = Path(self.temp_dir) / "test_report"
        generated_path = self.analyzer.generate_report(report, str(output_path), ReportFormat.CSV)
        
        self.assertTrue(Path(generated_path).exists())
        self.assertTrue(generated_path.endswith('.csv'))
        
        # Verify CSV content
        csv_data = pd.read_csv(generated_path)
        self.assertIn('Metric', csv_data.columns)
        self.assertIn('Value', csv_data.columns)
        self.assertGreater(len(csv_data), 0)

    def test_generate_report_invalid_format(self):
        """Test report generation with invalid format."""
        portfolio_values = self.create_sample_portfolio_values(50)
        
        report = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            trades=[],
            initial_capital=100000.0
        )
        
        with self.assertRaises(ValueError):
            self.analyzer.generate_report(report, "test", ReportFormat.PDF)

    def test_compare_strategies(self):
        """Test strategy comparison functionality."""
        # Create two reports
        portfolio1 = self.create_sample_portfolio_values(100)
        portfolio2 = self.create_sample_portfolio_values(100)
        
        report1 = self.analyzer.analyze_performance(
            portfolio_values=portfolio1,
            trades=self.create_sample_trades(10),
            initial_capital=100000.0,
            strategy_name="Strategy A"
        )
        
        report2 = self.analyzer.analyze_performance(
            portfolio_values=portfolio2,
            trades=self.create_sample_trades(15),
            initial_capital=100000.0,
            strategy_name="Strategy B"
        )
        
        comparison = self.analyzer.compare_strategies([report1, report2])
        
        self.assertIsInstance(comparison, pd.DataFrame)
        self.assertEqual(len(comparison), 2)
        self.assertIn('Strategy', comparison.columns)
        self.assertIn('Total Return', comparison.columns)
        self.assertIn('Sharpe Ratio', comparison.columns)
        
        self.assertIn('Strategy A', comparison['Strategy'].values)
        self.assertIn('Strategy B', comparison['Strategy'].values)


class TestPerformanceIntegration(unittest.TestCase):
    """Integration tests for performance metrics system."""

    def setUp(self):
        """Set up integration test fixtures."""
        self.analyzer = PerformanceAnalyzer(risk_free_rate=0.02)
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up integration test fixtures."""
        shutil.rmtree(self.temp_dir)

    def test_complete_performance_workflow(self):
        """Test complete performance analysis workflow."""
        # 1. Create realistic portfolio data
        dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
        initial_value = 100000.0
        
        # Simulate realistic portfolio with trend and volatility
        daily_returns = np.random.normal(0.0005, 0.02, len(dates))
        cumulative_returns = np.cumsum(daily_returns)
        portfolio_values = pd.Series(
            initial_value * np.exp(cumulative_returns),
            index=dates
        )
        
        # 2. Create realistic trades
        trades = []
        for i in range(50):
            side = 'buy' if i % 2 == 0 else 'sell'
            quantity = np.random.uniform(50, 200)
            price = np.random.uniform(100, 200)
            commission = quantity * price * 0.001
            slippage = np.random.uniform(0.01, 0.05)
            
            trades.append(MockTrade(side, quantity, price, commission, slippage))
        
        # 3. Perform analysis
        report = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            trades=trades,
            initial_capital=initial_value,
            strategy_name="Integration Test Strategy"
        )
        
        # 4. Verify report completeness
        self.assertIsInstance(report, PerformanceReport)
        self.assertEqual(report.strategy_name, "Integration Test Strategy")
        self.assertGreater(len(report.portfolio_values), 300)  # ~365 days
        self.assertGreater(len(report.returns), 300)
        
        # 5. Verify metrics are reasonable
        self.assertIsInstance(report.risk_metrics.volatility, float)
        self.assertGreater(report.risk_metrics.volatility, 0)
        self.assertLessEqual(report.risk_metrics.max_drawdown, 0)
        self.assertGreaterEqual(report.trade_metrics.total_trades, 50)
        
        # 6. Generate all report formats
        for format_type in [ReportFormat.JSON, ReportFormat.HTML, ReportFormat.CSV]:
            output_path = Path(self.temp_dir) / f"integration_test_{format_type.value}"
            generated_path = self.analyzer.generate_report(report, str(output_path), format_type)
            self.assertTrue(Path(generated_path).exists())
        
        # 7. Test strategy comparison
        report2 = self.analyzer.analyze_performance(
            portfolio_values=portfolio_values * 1.1,  # Slightly better performance
            trades=trades[:30],  # Fewer trades
            initial_capital=initial_value,
            strategy_name="Strategy B"
        )
        
        comparison = self.analyzer.compare_strategies([report, report2])
        self.assertEqual(len(comparison), 2)
        self.assertIn('Integration Test Strategy', comparison['Strategy'].values)
        self.assertIn('Strategy B', comparison['Strategy'].values)


if __name__ == '__main__':
    unittest.main()