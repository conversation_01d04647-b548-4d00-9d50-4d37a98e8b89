# Git Safety and Monitoring Setup

##  **Git Pre-commit Hook Setup**

The pre-commit hook is installed but needs to be made executable:

```bash
# Make pre-commit hook executable
chmod +x .git/hooks/pre-commit

# Test the hook
git add .env.example
git commit -m "test commit"
# Should pass since vm_iso profile not active

# Test protection (if vm_iso is active)
python utils/profile_loader.py --apply vm_iso
git config --local core.profile vm_iso
git add some_file.py
git commit -m "test commit"
# Should be blocked with protection message
```

##  **Streamlit Dashboard Setup**

Launch the profile-aware dashboard:

```bash
# Install Streamlit if not already installed
pip install streamlit

# Launch dashboard
streamlit run dashboard.py

# Dashboard will auto-detect current profile and show:
# - Environment status (vm_iso/win_quick)
# - Recent fills and trading activity
# - Risk management metrics
# - System health checks
```

##  **Health Check Monitoring Setup**

1. **Sign up at healthchecks.io**:
   - Create account at https://healthchecks.io/
   - Create a new check with 30-minute interval
   - Copy the ping URL

2. **Configure environment**:
   ```bash
   # Add to your environment
   export HEALTHCHECK_PING_URL="https://hc-ping.com/your-uuid-here"
   
   # Or add to .env file
   echo "HEALTHCHECK_PING_URL=https://hc-ping.com/your-uuid-here" >> .env
   ```

3. **Test health check**:
   ```bash
   # Test success ping
   curl https://hc-ping.com/your-uuid-here
   
   # Test failure ping  
   curl https://hc-ping.com/your-uuid-here/fail
   ```

##  **Security Features**

### **Pre-commit Protection**
- Blocks commits when `vm_iso` profile is active
- Scans for common token patterns in staged files
- Provides clear instructions for safe committing

### **Environment Variable Template**
- `.env.example` documents all required variables
- Safe to commit (contains no real credentials)
- Clear examples for both VM and Windows profiles

### **Profile Tracking**
- `CORE_PROFILE` environment variable tracks active profile
- Git config stores profile preference locally
- Dashboard auto-detects current environment

##  **Production Benefits**

### **Prevents "Oops" Moments**
- Can't accidentally commit production tokens
- Clear documentation of all required environment variables
- Visual dashboard shows which environment is active

### **Monitoring & Alerting**
- Email alerts if VM stops running
- Health check history and uptime tracking
- Dashboard shows system health at a glance

### **Team Collaboration**
- New team members see all required env vars immediately
- Git hooks protect against credential leaks
- Dashboard works for any profile configuration

##  **Quick Setup Checklist**

```bash
# 1. Make git hook executable
chmod +x .git/hooks/pre-commit

# 2. Copy environment template
cp .env.example .env
# Edit .env with your actual values

# 3. Set up health checks
# Sign up at healthchecks.io and get ping URL
export HEALTHCHECK_PING_URL="https://hc-ping.com/your-uuid"

# 4. Test dashboard
streamlit run dashboard.py

# 5. Test git protection
python utils/profile_loader.py --apply vm_iso
git config --local core.profile vm_iso
# Try to commit - should be blocked

# 6. Switch back to safe profile
python utils/profile_loader.py --apply win_quick
git config --local core.profile win_quick
# Now commits should work
```

---

**All safety systems operational!** 
