#!/usr/bin/env python3
"""
Test Network Connectivity for Schwab API
Diagnose and fix network connectivity issues
"""

import socket
import urllib.request
import urllib.parse
import ssl
import json
import sys
import os

def test_dns_resolution():
    """Test DNS resolution for Schwab API"""
    print("\n🔍 Testing DNS Resolution")
    print("=" * 40)
    
    try:
        # Test DNS resolution
        host = "api.schwabapi.com"
        ip = socket.gethostbyname(host)
        print(f"✓ DNS Resolution: {host} -> {ip}")
        return True
    except socket.gaierror as e:
        print(f"✗ DNS Resolution Failed: {e}")
        return False

def test_basic_connectivity():
    """Test basic network connectivity"""
    print("\n🔍 Testing Basic Connectivity")
    print("=" * 40)
    
    try:
        # Test socket connection
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(("api.schwabapi.com", 443))
        sock.close()
        
        if result == 0:
            print("✓ Socket Connection: SUCCESS")
            return True
        else:
            print(f"✗ Socket Connection Failed: Error {result}")
            return False
    except Exception as e:
        print(f"✗ Socket Connection Failed: {e}")
        return False

def test_ssl_connection():
    """Test SSL/TLS connection"""
    print("\n🔍 Testing SSL Connection")
    print("=" * 40)
    
    try:
        # Create SSL context
        context = ssl.create_default_context()
        
        # Test SSL connection
        with socket.create_connection(("api.schwabapi.com", 443), timeout=10) as sock:
            with context.wrap_socket(sock, server_hostname="api.schwabapi.com") as ssock:
                print(f"✓ SSL Connection: SUCCESS")
                print(f"  SSL Version: {ssock.version()}")
                print(f"  Cipher: {ssock.cipher()}")
                return True
    except Exception as e:
        print(f"✗ SSL Connection Failed: {e}")
        return False

def test_http_request():
    """Test basic HTTP request"""
    print("\n🔍 Testing HTTP Request")
    print("=" * 40)
    
    try:
        # Test basic HTTP request (without authentication)
        url = "https://api.schwabapi.com/marketdata/v1/AAPL/quotes"
        
        # Create request with proper headers
        headers = {
            'Accept': 'application/json',
            'User-Agent': 'Python/3.x'
        }
        
        request = urllib.request.Request(url, headers=headers)
        
        try:
            with urllib.request.urlopen(request, timeout=30) as response:
                print(f"✓ HTTP Request: {response.getcode()}")
                print(f"  Response Headers: {dict(response.headers)}")
                return True
        except urllib.error.HTTPError as e:
            if e.code == 401:
                print("✓ HTTP Request: Reached API (401 Unauthorized - expected without token)")
                return True
            else:
                print(f"✗ HTTP Request Failed: {e.code} {e.reason}")
                return False
                
    except Exception as e:
        print(f"✗ HTTP Request Failed: {e}")
        return False

def test_with_token():
    """Test with actual Schwab token"""
    print("\n🔍 Testing With Schwab Token")
    print("=" * 40)
    
    try:
        # Load token
        token_path = os.path.join("SCHWAB_MCP_PRODUCTION", "config", "schwab_token.json")
        
        if not os.path.exists(token_path):
            print("✗ Token file not found")
            return False
            
        with open(token_path, 'r') as f:
            token_data = json.load(f)
            
        access_token = token_data.get('access_token')
        if not access_token:
            print("✗ No access token in file")
            return False
            
        # Test authenticated request
        url = "https://api.schwabapi.com/marketdata/v1/AAPL/quotes"
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Accept': 'application/json'
        }
        
        request = urllib.request.Request(url, headers=headers)
        
        with urllib.request.urlopen(request, timeout=30) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode())
                print("✓ Authenticated Request: SUCCESS")
                print(f"  Quote data received for: {list(data.keys())}")
                return True
            else:
                print(f"✗ Authenticated Request Failed: {response.getcode()}")
                return False
                
    except Exception as e:
        print(f"✗ Authenticated Request Failed: {e}")
        return False

def test_options_endpoint():
    """Test options endpoint specifically"""
    print("\n🔍 Testing Options Endpoint")
    print("=" * 40)
    
    try:
        # Load token
        token_path = os.path.join("SCHWAB_MCP_PRODUCTION", "config", "schwab_token.json")
        
        with open(token_path, 'r') as f:
            token_data = json.load(f)
            
        access_token = token_data.get('access_token')
        
        # Test options chain endpoint
        params = {
            'symbol': 'AAPL',
            'contractType': 'ALL',
            'strikeCount': 5,
            'includeQuotes': 'true'
        }
        
        query_string = urllib.parse.urlencode(params)
        url = f"https://api.schwabapi.com/marketdata/v1/chains?{query_string}"
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Accept': 'application/json'
        }
        
        request = urllib.request.Request(url, headers=headers)
        
        with urllib.request.urlopen(request, timeout=30) as response:
            if response.getcode() == 200:
                data = json.loads(response.read().decode())
                print("✓ Options Endpoint: SUCCESS")
                print(f"  Symbol: {data.get('symbol', 'N/A')}")
                print(f"  Call expirations: {len(data.get('callExpDateMap', {}))}")
                print(f"  Put expirations: {len(data.get('putExpDateMap', {}))}")
                return True
            else:
                print(f"✗ Options Endpoint Failed: {response.getcode()}")
                return False
                
    except Exception as e:
        print(f"✗ Options Endpoint Failed: {e}")
        return False

def main():
    """Run all connectivity tests"""
    print("\n🎯 SCHWAB API CONNECTIVITY DIAGNOSIS")
    print("=" * 50)
    
    tests = [
        ("DNS Resolution", test_dns_resolution),
        ("Basic Connectivity", test_basic_connectivity),
        ("SSL Connection", test_ssl_connection),
        ("HTTP Request", test_http_request),
        ("Authenticated Request", test_with_token),
        ("Options Endpoint", test_options_endpoint)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"✗ {test_name} crashed: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)
    
    print(f"CONNECTIVITY TEST RESULTS: {passed}/{total}")
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASS" if results[i] else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print("\n✅ ALL CONNECTIVITY TESTS PASSED")
        print("✅ Network connectivity is working properly!")
    else:
        print(f"\n❌ CONNECTIVITY ISSUES DETECTED")
        print("❌ Network connectivity needs attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
