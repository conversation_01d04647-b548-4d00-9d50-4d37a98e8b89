# Desktop Commander MCP Aliases
# Add these to your Desktop Commander configuration

# MCP Server Commands
alias mcp-status="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py status"
alias mcp-test="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py test"  
alias mcp-start="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py start"
alias mcp-help="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py help"

# Quick shortcuts
alias mcp="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py"
alias liquidity-mcp="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_dc.py start"

# Direct server start (alternative)
alias start-mcp="py D:\script-work\Liquidity_Sweep\api_robustness\mcp_server_production.py --config D:\script-work\Liquidity_Sweep\api_robustness\mcp_installation\config\config.json"
