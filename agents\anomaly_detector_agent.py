#!/usr/bin/env python3
#!/usr/bin/env python3
"""
Greek Anomaly Detection Agent - MCP Enhanced
Detects statistically significant anomalies in Greek/IV metrics with MCP-primary data
ENHANCED: Real-time broker Greeks + calculated fallback for anomaly detection
"""

import os
import sys
import json
import logging
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from agents.agent_base import BaseAgent
from enhanced_greeks_engine import GreeksEnhancedMixin


class GreekAnomalyAgent(BaseAgent, GreeksEnhancedMixin):
    """Detects anomalies in Greek and IV metrics with MCP-primary Greeks
    **ENHANCED: Real-time broker Greeks + statistical anomaly detection**
    """
    
    task_id = "A-01"
    
    def __init__(self, agent_id="greek_anomaly_agent"):
        """Initialize Greek Anomaly Agent with Enhanced Greeks Engine"""
        BaseAgent.__init__(self, agent_id)
        GreeksEnhancedMixin.__init__(self)
        self.logger = logging.getLogger(agent_id)
        self.stats_file = "data/hist_stats.parquet"
        self.today = datetime.now().strftime("%Y-%m-%d")
        
        # Initialize direct Schwab API for real-time data
        try:
            import sys
            sys.path.append("SCHWAB_MCP_PRODUCTION/core")
            from schwab_production_api import SchwabAPI
            self.schwab_api = SchwabAPI()
            self.has_real_time = True
            self.logger.info("Anomaly Detector initialized with direct Schwab API access")
        except ImportError:
            self.schwab_api = None
            self.has_real_time = False
            self.logger.warning("Schwab API unavailable - using historical anomaly detection only")
    
    # Statistical interpretation templates
    INTERPRETATIONS = {
        "gamma_roc_high": "Dealer gamma expanding rapidly  expect spot dampening.",
        "gamma_roc_low": "Gamma collapsing  directional moves can stretch.",
        "vanna_raw_ext": "Large vanna; spot-vol feedback may accelerate moves.",
        "vanna_roc_ext": "Vanna momentum extreme  cross-derivative regime shift.",
        "charm_raw_neg": "Charm drag  deltas will decay; watch intraday roll.",
        "charm_roc_ext": "Charm rate-of-change extreme  time decay acceleration.",
        "iv_rank_high": "IV rank > 90th pct  options expensive, consider spreads.",
        "iv_rank_low": "IV rank < 10th pct  options cheap, volatility expansion risk.",
        "iv_roc_extreme": "IV momentum extreme  potential volatility regime shift.",
        "gamma_mean_extreme": "Average gamma at statistical extreme  dealer positioning shift."
    }
    
    # Anomaly detection thresholds
    THRESHOLDS = {
        "z_score_high": 2.5,
        "z_score_low": -2.5,
        "z_score_extreme": 3.0,
        "percentile_high": 0.95,
        "percentile_low": 0.05,
        "iv_rank_high": 0.9,
        "iv_rank_low": 0.1
    }
    
    def execute_task(self, task):
        """Execute anomaly detection task"""
        feature_row = task.inputs.get("feature_row_json", {})
        ticker = task.inputs.get("ticker", "UNKNOWN")
        
        return self.execute(feature_row, ticker)
    
    def validate_inputs(self, task):
        """Validate task inputs"""
        inputs = task.inputs
        
        # Check required inputs
        has_feature_row = "feature_row_json" in inputs
        has_ticker = "ticker" in inputs
        has_stats_file = os.path.exists(self.stats_file)
        
        return has_feature_row and has_ticker and has_stats_file
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        try:
            if isinstance(outputs, str) and os.path.exists(outputs):
                with open(outputs, 'r') as f:
                    anomalies = json.load(f)
                
                return {
                    "anomalies_detected": len(anomalies),
                    "output_valid": 1.0,
                    "file_created": 1.0
                }
            else:
                return {
                    "anomalies_detected": 0,
                    "output_valid": 0.0,
                    "file_created": 0.0
                }
        except:
            return {
                "anomalies_detected": 0,
                "output_valid": 0.0,
                "file_created": 0.0
            }
    
    def execute(self, feature_row, ticker):
        """
        Detect anomalies in Greek/IV metrics with real-time analysis
        **ENHANCED: Real-time anomaly detection with current candle data**
        
        Args:
            feature_row (dict): Current feature values
            ticker (str): Stock ticker symbol
            
        Returns:
            str: Path to anomalies JSON file
        """
        try:
            self.logger.info(f"Detecting anomalies for {ticker} with enhanced Greeks")
            
            # Step 1: Get enhanced Greeks for anomaly analysis
            enhanced_greeks = self.get_agent_greeks_with_roc(ticker)
            greeks_quality = self.get_greeks_quality_info(ticker)
            
            self.logger.info(f"[{ticker}] Greeks anomaly analysis - Source: {greeks_quality['data_source']}, "
                           f"Quality: {greeks_quality['quality_score']:.2f}")
            
            # Step 2: Detect Greek-specific anomalies
            greek_anomalies = self._detect_enhanced_greek_anomalies(enhanced_greeks, ticker)
            
            # Step 3: Real-time data enhancement for current candle anomaly detection
            real_time_anomalies = []
            if self.has_real_time:
                try:
                    result = self.real_time_agent.get_market_data([ticker])
                    
                    if result and result.get("source") == "schwab_broker":
                        ticker_data = result["data"][ticker]
                        
                        if ticker_data.get("is_current_candle"):
                            # Analyze current candle for immediate anomalies
                            current_price = ticker_data["last_price"]
                            volume = ticker_data.get("volume", 0)
                            
                            # Check for volume anomalies
                            if volume > 0:
                                # Simple volume anomaly detection (can be enhanced)
                                avg_volume = feature_row.get("avg_volume", volume)
                                if volume > avg_volume * 3:  # 3x normal volume
                                    real_time_anomalies.append({
                                        "type": "volume_spike",
                                        "metric": "current_volume",
                                        "current_value": volume,
                                        "threshold": avg_volume * 3,
                                        "z_score": 3.0,  # Simplified
                                        "significance": "HIGH",
                                        "interpretation": "Unusual volume spike detected in current candle",
                                        "timestamp": ticker_data["timestamp"],
                                        "real_time": True
                                    })
                            
                            self.logger.info(f"[{ticker}] Real-time anomaly analysis on current candle: ${current_price:.2f}, vol: {volume}")
                        else:
                            self.logger.info(f"[{ticker}] Using most recent available data for anomaly detection")
                            
                except Exception as e:
                    self.logger.warning(f"[{ticker}] Real-time anomaly detection failed: {e}")
            
            # Load historical statistics
            hist_stats = self._load_historical_stats(ticker)
            
            # Detect historical anomalies
            historical_anomalies = self._detect_anomalies(feature_row, hist_stats, ticker)
            
            # Combine Greek, real-time, and historical anomalies
            all_anomalies = historical_anomalies + real_time_anomalies + greek_anomalies
            
            # Save results
            output_path = self._save_anomalies(all_anomalies, ticker)
            
            # Shadow mode logging - capture anomaly detection results
            try:
                from agents.agent_zero import AgentZeroAdvisor
                shadow_agent = AgentZeroAdvisor()
                
                # Calculate anomaly strength
                anomaly_strength = min(len(all_anomalies) / 10.0, 1.0)  # Normalize to 0-1
                
                signal_data = {
                    'confidence': 0.90,  # High confidence in anomaly detection
                    'strength': anomaly_strength,
                    'execution_recommendation': 'analyze' if len(all_anomalies) > 0 else 'monitor'
                }
                
                math_data = {
                    'accuracy_score': 0.91,  # Anomaly detection accuracy
                    'precision': 0.001
                }
                
                market_context = {
                    'system': 'ANOMALY_DETECTION_SPECIALIST',  # Enhanced source name
                    'source_file': 'anomaly_detector_agent.py',
                    'source_agent': 'ANOMALY_DETECTOR',
                    'intelligence_type': 'MARKET_ANOMALY_ANALYSIS',
                    'ticker': ticker,
                    'total_anomalies': len(all_anomalies),
                    'real_time_anomalies': len(real_time_anomalies),
                    'greek_anomalies': len(greek_anomalies),
                    'historical_anomalies': len(historical_anomalies),
                    'output_file': output_path
                }
                
                shadow_agent.log_training_data(
                    signal_data=signal_data,
                    math_data=math_data,
                    decision={'action': 'anomaly_detection', 'anomalies_found': len(all_anomalies)},
                    outcome=anomaly_strength,  # Anomaly strength as outcome
                    market_context=market_context
                )
                self.logger.info("Shadow mode: Anomaly detection logged")
                
            except Exception as e:
                self.logger.warning(f"Shadow mode logging failed: {e}")
            
            self.logger.info(f"Detected {len(all_anomalies)} anomalies for {ticker} ({len(real_time_anomalies)} real-time)")
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"Anomaly detection failed for {ticker}: {e}")
            # Return empty anomalies file on error
            return self._save_anomalies([], ticker)
    
    def _load_historical_stats(self, ticker):
        """Load historical statistics for the ticker"""
        if not os.path.exists(self.stats_file):
            raise FileNotFoundError(f"Historical stats file not found: {self.stats_file}")
        
        # Load stats and filter for ticker
        all_stats = pd.read_parquet(self.stats_file)
        
        if 'ticker' in all_stats.columns:
            ticker_stats = all_stats[all_stats['ticker'] == ticker]
        else:
            # If no ticker column, use all data (single ticker case)
            ticker_stats = all_stats
        
        if ticker_stats.empty:
            self.logger.warning(f"No historical stats found for {ticker}, using aggregate stats")
            ticker_stats = all_stats
        
        # Get most recent statistics
        if not ticker_stats.empty:
            latest_stats = ticker_stats.tail(1).iloc[0]
            return latest_stats.to_dict()
        else:
            raise ValueError("No historical statistics available")
    
    def _detect_anomalies(self, feature_row, hist_stats, ticker):
        """Detect statistical anomalies in features"""
        anomalies = []
        
        # Helper function to calculate z-score
        def calculate_z_score(metric):
            if metric not in feature_row:
                return None
            
            mean_key = f"{metric}_mean"
            std_key = f"{metric}_std"
            
            if mean_key not in hist_stats or std_key not in hist_stats:
                return None
            
            mean_val = hist_stats[mean_key]
            std_val = hist_stats[std_key]
            current_val = feature_row[metric]
            
            if pd.isna(mean_val) or pd.isna(std_val) or std_val == 0:
                return None
            
            return (current_val - mean_val) / std_val
        
        # Helper function to check percentiles
        def check_percentile(metric, percentile):
            if metric not in feature_row:
                return False
            
            percentile_key = f"{metric}_q{int(percentile*100):02d}"
            
            if percentile_key not in hist_stats:
                return False
            
            threshold = hist_stats[percentile_key]
            current_val = feature_row[metric]
            
            if pd.isna(threshold) or pd.isna(current_val):
                return False
            
            return current_val > threshold if percentile > 0.5 else current_val < threshold
        
        # Gamma ROC anomalies
        gamma_roc_metrics = ['gamma_calc_mean_roc', 'gamma_calc_sum_roc']
        for metric in gamma_roc_metrics:
            if metric in feature_row:
                z_score = calculate_z_score(metric)
                if z_score is not None:
                    if z_score > self.THRESHOLDS["z_score_high"]:
                        anomalies.append({
                            "metric": metric,
                            "level": feature_row[metric],
                            "z_score": z_score,
                            "type": "high",
                            "interpret": self.INTERPRETATIONS["gamma_roc_high"]
                        })
                    elif z_score < self.THRESHOLDS["z_score_low"]:
                        anomalies.append({
                            "metric": metric,
                            "level": feature_row[metric],
                            "z_score": z_score,
                            "type": "low",
                            "interpret": self.INTERPRETATIONS["gamma_roc_low"]
                        })
        
        # Vanna anomalies
        vanna_metrics = ['vanna_calc_mean', 'vanna_calc_mean_roc']
        for metric in vanna_metrics:
            if metric in feature_row:
                z_score = calculate_z_score(metric)
                if z_score is not None and abs(z_score) > self.THRESHOLDS["z_score_extreme"]:
                    interp_key = "vanna_raw_ext" if "roc" not in metric else "vanna_roc_ext"
                    anomalies.append({
                        "metric": metric,
                        "level": feature_row[metric],
                        "z_score": z_score,
                        "type": "extreme",
                        "interpret": self.INTERPRETATIONS[interp_key]
                    })
        
        # Charm anomalies
        charm_metrics = ['charm_calc_mean', 'charm_calc_mean_roc']
        for metric in charm_metrics:
            if metric in feature_row:
                # Check for extreme negative charm (5th percentile)
                if check_percentile(metric, 0.05):
                    interp_key = "charm_raw_neg" if "roc" not in metric else "charm_roc_ext"
                    anomalies.append({
                        "metric": metric,
                        "level": feature_row[metric],
                        "type": "negative_extreme",
                        "interpret": self.INTERPRETATIONS[interp_key]
                    })
                
                # Check for extreme positive charm (95th percentile)
                if check_percentile(metric, 0.95):
                    interp_key = "charm_roc_ext" if "roc" in metric else "charm_raw_neg"
                    anomalies.append({
                        "metric": metric,
                        "level": feature_row[metric],
                        "type": "positive_extreme",
                        "interpret": self.INTERPRETATIONS[interp_key]
                    })
        
        # IV Rank anomalies
        if 'iv_rank' in feature_row:
            iv_rank = feature_row['iv_rank']
            if not pd.isna(iv_rank):
                if iv_rank > self.THRESHOLDS["iv_rank_high"]:
                    anomalies.append({
                        "metric": "iv_rank",
                        "level": iv_rank,
                        "type": "high_rank",
                        "interpret": self.INTERPRETATIONS["iv_rank_high"]
                    })
                elif iv_rank < self.THRESHOLDS["iv_rank_low"]:
                    anomalies.append({
                        "metric": "iv_rank",
                        "level": iv_rank,
                        "type": "low_rank",
                        "interpret": self.INTERPRETATIONS["iv_rank_low"]
                    })
        
        # IV ROC anomalies
        if 'iv_roc' in feature_row:
            z_score = calculate_z_score('iv_roc')
            if z_score is not None and abs(z_score) > self.THRESHOLDS["z_score_extreme"]:
                anomalies.append({
                    "metric": "iv_roc",
                    "level": feature_row['iv_roc'],
                    "z_score": z_score,
                    "type": "extreme",
                    "interpret": self.INTERPRETATIONS["iv_roc_extreme"]
                })
        
        return anomalies
    
    def _save_anomalies(self, anomalies, ticker):
        """Save anomalies to JSON file"""
        # Create output directory
        output_dir = Path(f"anomalies/{self.today}")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create output file
        output_file = output_dir / f"{ticker}_anomalies.json"
        
        # Add metadata
        anomaly_data = {
            "ticker": ticker,
            "date": self.today,
            "timestamp": datetime.now().isoformat(),
            "anomaly_count": len(anomalies),
            "anomalies": anomalies
        }
        
        # Save to file
        with open(output_file, 'w') as f:
            json.dump(anomaly_data, f, indent=2, default=str)
        
        self.logger.info(f"Anomalies saved to {output_file}")
        
        return str(output_file)
    
    def get_anomaly_summary(self, anomaly_file):
        """Get summary of detected anomalies"""
        try:
            with open(anomaly_file, 'r') as f:
                data = json.load(f)
            
            anomalies = data.get('anomalies', [])
            
            summary = {
                "total_anomalies": len(anomalies),
                "by_type": {},
                "by_metric": {},
                "critical_anomalies": []
            }
            
            for anomaly in anomalies:
                # Count by type
                anomaly_type = anomaly.get('type', 'unknown')
                summary["by_type"][anomaly_type] = summary["by_type"].get(anomaly_type, 0) + 1
                
                # Count by metric
                metric = anomaly.get('metric', 'unknown')
                summary["by_metric"][metric] = summary["by_metric"].get(metric, 0) + 1
                
                # Flag critical anomalies (extreme z-scores)
                z_score = anomaly.get('z_score')
                if z_score is not None and abs(z_score) > 3.0:
                    summary["critical_anomalies"].append(anomaly)
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to summarize anomalies: {e}")
            return {"error": str(e)}


    def _detect_enhanced_greek_anomalies(self, enhanced_greeks: dict, ticker: str) -> list:
        """
        Detect anomalies in enhanced Greeks data from MCP or calculated sources
        
        Args:
            enhanced_greeks (dict): Greeks data from enhanced engine
            ticker (str): Stock ticker
            
        Returns:
            list: List of Greek-specific anomalies
        """
        anomalies = []
        
        try:
            # Define Greek anomaly thresholds
            greek_thresholds = {
                'delta_overall_mean': {'min': -1.0, 'max': 1.0, 'extreme': 0.8},
                'gamma_overall_mean': {'min': 0.0, 'max': 1.0, 'extreme': 0.1},
                'vanna_overall_mean': {'min': -5.0, 'max': 5.0, 'extreme': 2.0},
                'charm_call_mean': {'min': -1.0, 'max': 0.5, 'extreme': -0.5},
                'gamma_roc': {'min': -10.0, 'max': 10.0, 'extreme': 5.0},
                'vanna_roc': {'min': -20.0, 'max': 20.0, 'extreme': 10.0}
            }
            
            # Check each Greek metric for anomalies
            for metric, thresholds in greek_thresholds.items():
                value = enhanced_greeks.get(metric)
                
                if value is None:
                    continue
                
                # Check for out-of-bounds values
                if value < thresholds['min'] or value > thresholds['max']:
                    anomalies.append({
                        'type': 'greek_bounds_violation',
                        'metric': metric,
                        'level': value,
                        'threshold_min': thresholds['min'],
                        'threshold_max': thresholds['max'],
                        'z_score': None,
                        'severity': 'HIGH',
                        'interpret': f"Greek {metric} outside normal bounds",
                        'data_source': enhanced_greeks.get('_data_source', 'unknown'),
                        'timestamp': datetime.now().isoformat()
                    })
                
                # Check for extreme values within bounds
                elif abs(value) > thresholds['extreme']:
                    anomalies.append({
                        'type': 'greek_extreme_value',
                        'metric': metric,
                        'level': value,
                        'threshold': thresholds['extreme'],
                        'z_score': abs(value) / thresholds['extreme'],
                        'severity': 'MEDIUM',
                        'interpret': f"Extreme {metric} value detected",
                        'data_source': enhanced_greeks.get('_data_source', 'unknown'),
                        'timestamp': datetime.now().isoformat()
                    })
            
            # ROC-specific anomaly patterns
            gamma_roc = enhanced_greeks.get('gamma_roc', 0)
            vanna_roc = enhanced_greeks.get('vanna_roc', 0)
            
            # Rapid gamma expansion
            if gamma_roc > 3.0:
                anomalies.append({
                    'type': 'gamma_roc_high',
                    'metric': 'gamma_roc',
                    'level': gamma_roc,
                    'z_score': gamma_roc / 3.0,
                    'severity': 'HIGH',
                    'interpret': "Dealer gamma expanding rapidly  expect spot dampening",
                    'data_source': enhanced_greeks.get('_data_source', 'unknown'),
                    'timestamp': datetime.now().isoformat()
                })
            
            # Vanna momentum
            if abs(vanna_roc) > 5.0:
                direction = "positive" if vanna_roc > 0 else "negative"
                anomalies.append({
                    'type': 'vanna_roc_extreme',
                    'metric': 'vanna_roc',
                    'level': vanna_roc,
                    'z_score': abs(vanna_roc) / 5.0,
                    'severity': 'MEDIUM',
                    'interpret': f"Large vanna {direction} momentum - spot-vol feedback risk",
                    'data_source': enhanced_greeks.get('_data_source', 'unknown'),
                    'timestamp': datetime.now().isoformat()
                })
            
            # Quality-based warnings
            quality_score = enhanced_greeks.get('_quality_score', 1.0)
            if quality_score < 0.7:
                anomalies.append({
                    'type': 'data_quality_warning',
                    'metric': 'greeks_quality',
                    'level': quality_score,
                    'threshold': 0.7,
                    'severity': 'LOW',
                    'interpret': f"Greeks data quality below threshold - results may be unreliable",
                    'data_source': enhanced_greeks.get('_data_source', 'unknown'),
                    'timestamp': datetime.now().isoformat()
                })
            
            if anomalies:
                self.logger.info(f"[{ticker}] Detected {len(anomalies)} Greek anomalies")
            
        except Exception as e:
            self.logger.error(f"[{ticker}] Enhanced Greeks anomaly detection failed: {e}")
        
        return anomalies


def main():
    """Command line interface for anomaly detection"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Detect Greek/IV anomalies")
    parser.add_argument("--ticker", required=True, help="Stock ticker symbol")
    parser.add_argument("--features-file", help="Features JSON file path")
    parser.add_argument("--summary", action="store_true", help="Show anomaly summary")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Create agent
    agent = GreekAnomalyAgent()
    
    try:
        if args.features_file:
            # Load features from file
            with open(args.features_file, 'r') as f:
                feature_row = json.load(f)
        else:
            # Create dummy features for testing
            feature_row = {
                "iv_rank": 0.95,
                "iv_roc": 0.15,
                "gamma_calc_mean_roc": 0.025,
                "vanna_calc_mean": 0.045,
                "charm_calc_mean": -0.2
            }
            print(f"Using dummy features for testing: {feature_row}")
        
        # Detect anomalies
        output_file = agent.execute(feature_row, args.ticker.upper())
        
        print(f"SUCCESS: Anomaly detection completed")
        print(f"Output: {output_file}")
        
        # Show summary if requested
        if args.summary:
            summary = agent.get_anomaly_summary(output_file)
            print(f"\nAnomaly Summary:")
            print(f"Total: {summary['total_anomalies']}")
            print(f"By Type: {summary['by_type']}")
            print(f"By Metric: {summary['by_metric']}")
            if summary['critical_anomalies']:
                print(f"Critical: {len(summary['critical_anomalies'])}")
        
        return 0
        
    except Exception as e:
        print(f"ERROR: Anomaly detection failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
