"""
A/B Testing Framework for ML Models

This module provides a framework for comparing the performance of different
ML models or model versions in the liquidity analysis system.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import logging
import time
from datetime import datetime
import matplotlib.pyplot as plt
import uuid
from dataclasses import dataclass, field

# Internal imports
from ml_logging import get_logger
from ml_model_registry import ModelRegistry

# Setup logger
logger = get_logger('ab_testing_framework')


@dataclass
class TestResult:
    """Class for storing test results."""
    model_id: str
    model_version: str
    metrics: Dict[str, float]
    timestamp: datetime = field(default_factory=datetime.now)
    test_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ABTest:
    """Class for storing A/B test configuration and results."""
    test_id: str
    model_a_id: str
    model_a_version: str
    model_b_id: str
    model_b_version: str
    test_name: str
    description: str
    metrics: List[str]
    created_at: datetime = field(default_factory=datetime.now)
    status: str = "created"
    results_a: Optional[TestResult] = None
    results_b: Optional[TestResult] = None
    winner: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class ABTestingFramework:
    """
    Framework for A/B testing ML models.

    This class provides methods for creating, running, and analyzing A/B tests
    between different ML models or model versions.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the A/B testing framework.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Set default configuration
        self.storage_path = self.config.get('storage_path', 'ml/test_results')
        self.default_metrics = self.config.get('default_metrics', [
            'accuracy', 'precision', 'recall', 'f1_score', 'mse', 'mae'
        ])

        # Create storage directory if it doesn't exist
        os.makedirs(self.storage_path, exist_ok=True)

        # Initialize model registry
        self.model_registry = ModelRegistry(self.config.get('model_registry_config'))

        # Initialize test registry
        self.tests = {}
        self._load_tests()

        logger.info("A/B testing framework initialized")

    def _load_tests(self):
        """Load existing tests from storage."""
        try:
            test_file = os.path.join(self.storage_path, 'ab_tests.json')
            if os.path.exists(test_file):
                with open(test_file, 'r') as f:
                    tests_data = json.load(f)

                for test_id, test_data in tests_data.items():
                    # Convert string timestamps to datetime
                    test_data['created_at'] = datetime.fromisoformat(test_data['created_at'])

                    # Convert results to TestResult objects
                    if test_data.get('results_a'):
                        results_a = test_data['results_a']
                        results_a['timestamp'] = datetime.fromisoformat(results_a['timestamp'])
                        test_data['results_a'] = TestResult(**results_a)

                    if test_data.get('results_b'):
                        results_b = test_data['results_b']
                        results_b['timestamp'] = datetime.fromisoformat(results_b['timestamp'])
                        test_data['results_b'] = TestResult(**results_b)

                    # Create ABTest object
                    self.tests[test_id] = ABTest(**test_data)

                logger.info(f"Loaded {len(self.tests)} A/B tests from storage")

        except Exception as e:
            logger.error(f"Error loading A/B tests: {str(e)}")

    def _save_tests(self):
        """Save tests to storage."""
        try:
            test_file = os.path.join(self.storage_path, 'ab_tests.json')

            # Convert tests to serializable format
            tests_data = {}
            for test_id, test in self.tests.items():
                test_dict = test.__dict__.copy()

                # Convert datetime to string
                test_dict['created_at'] = test_dict['created_at'].isoformat()

                # Convert results to dictionaries
                if test_dict.get('results_a'):
                    results_a = test_dict['results_a'].__dict__.copy()
                    results_a['timestamp'] = results_a['timestamp'].isoformat()
                    test_dict['results_a'] = results_a

                if test_dict.get('results_b'):
                    results_b = test_dict['results_b'].__dict__.copy()
                    results_b['timestamp'] = results_b['timestamp'].isoformat()
                    test_dict['results_b'] = results_b

                tests_data[test_id] = test_dict

            with open(test_file, 'w') as f:
                json.dump(tests_data, f, indent=2)

            logger.info(f"Saved {len(self.tests)} A/B tests to storage")

        except Exception as e:
            logger.error(f"Error saving A/B tests: {str(e)}")

    def create_test(self,
                   model_a_id: str,
                   model_a_version: str,
                   model_b_id: str,
                   model_b_version: str,
                   test_name: str,
                   description: str = "",
                   metrics: Optional[List[str]] = None) -> str:
        """
        Create a new A/B test.

        Args:
            model_a_id: ID of model A
            model_a_version: Version of model A
            model_b_id: ID of model B
            model_b_version: Version of model B
            test_name: Name of the test
            description: Description of the test
            metrics: List of metrics to evaluate

        Returns:
            ID of the created test
        """
        # Generate test ID
        test_id = str(uuid.uuid4())

        # Use default metrics if none provided
        if metrics is None:
            metrics = self.default_metrics

        # Create test
        test = ABTest(
            test_id=test_id,
            model_a_id=model_a_id,
            model_a_version=model_a_version,
            model_b_id=model_b_id,
            model_b_version=model_b_version,
            test_name=test_name,
            description=description,
            metrics=metrics
        )

        # Add to registry
        self.tests[test_id] = test

        # Save tests
        self._save_tests()

        logger.info(f"Created A/B test '{test_name}' with ID {test_id}")
        return test_id

    def run_test(self,
                test_id: str,
                evaluation_function: Callable[[str, str, List[str]], Dict[str, float]],
                data: Optional[Any] = None) -> Dict[str, Any]:
        """
        Run an A/B test.

        Args:
            test_id: ID of the test to run
            evaluation_function: Function to evaluate models
            data: Optional data to use for evaluation

        Returns:
            Dictionary with test results
        """
        if test_id not in self.tests:
            logger.error(f"Test with ID {test_id} not found")
            return {'success': False, 'error': 'Test not found'}

        test = self.tests[test_id]

        # Update test status
        test.status = "running"
        self._save_tests()

        start_time = time.time()

        try:
            # Evaluate model A
            logger.info(f"Evaluating model A: {test.model_a_id} (version {test.model_a_version})")
            metrics_a = evaluation_function(test.model_a_id, test.model_a_version, test.metrics, data)

            # Create test result for model A
            test.results_a = TestResult(
                model_id=test.model_a_id,
                model_version=test.model_a_version,
                metrics=metrics_a
            )

            # Evaluate model B
            logger.info(f"Evaluating model B: {test.model_b_id} (version {test.model_b_version})")
            metrics_b = evaluation_function(test.model_b_id, test.model_b_version, test.metrics, data)

            # Create test result for model B
            test.results_b = TestResult(
                model_id=test.model_b_id,
                model_version=test.model_b_version,
                metrics=metrics_b
            )

            # Determine winner
            winner = self._determine_winner(test)
            test.winner = winner

            # Update test status
            test.status = "completed"

            # Save tests
            self._save_tests()

            elapsed = time.time() - start_time
            logger.info(f"Completed A/B test '{test.test_name}' in {elapsed:.2f} seconds. Winner: {winner}")

            return {
                'success': True,
                'test_id': test_id,
                'results_a': test.results_a.__dict__,
                'results_b': test.results_b.__dict__,
                'winner': winner,
                'elapsed_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error running A/B test: {str(e)}")

            # Update test status
            test.status = "failed"
            self._save_tests()

            return {'success': False, 'error': str(e)}

    def _determine_winner(self, test: ABTest) -> str:
        """
        Determine the winner of an A/B test.

        Args:
            test: ABTest object

        Returns:
            ID of the winning model or 'tie'
        """
        if not test.results_a or not test.results_b:
            return "unknown"

        # Count wins for each model
        wins_a = 0
        wins_b = 0

        for metric in test.metrics:
            # Skip metrics that don't exist in both results
            if metric not in test.results_a.metrics or metric not in test.results_b.metrics:
                continue

            value_a = test.results_a.metrics[metric]
            value_b = test.results_b.metrics[metric]

            # For error metrics (lower is better)
            if metric in ['mse', 'mae', 'rmse', 'error_rate']:
                if value_a < value_b:
                    wins_a += 1
                elif value_b < value_a:
                    wins_b += 1
            # For other metrics (higher is better)
            else:
                if value_a > value_b:
                    wins_a += 1
                elif value_b > value_a:
                    wins_b += 1

        # Determine winner
        if wins_a > wins_b:
            return f"{test.model_a_id}:{test.model_a_version}"
        elif wins_b > wins_a:
            return f"{test.model_b_id}:{test.model_b_version}"
        else:
            return "tie"

    def get_test(self, test_id: str) -> Optional[ABTest]:
        """
        Get an A/B test by ID.

        Args:
            test_id: ID of the test

        Returns:
            ABTest object or None if not found
        """
        return self.tests.get(test_id)

    def get_all_tests(self) -> Dict[str, ABTest]:
        """
        Get all A/B tests.

        Returns:
            Dictionary of test ID to ABTest object
        """
        return self.tests

    def delete_test(self, test_id: str) -> bool:
        """
        Delete an A/B test.

        Args:
            test_id: ID of the test to delete

        Returns:
            True if successful, False otherwise
        """
        if test_id not in self.tests:
            logger.error(f"Test with ID {test_id} not found")
            return False

        # Remove from registry
        del self.tests[test_id]

        # Save tests
        self._save_tests()

        logger.info(f"Deleted A/B test with ID {test_id}")
        return True

    def generate_report(self, test_id: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate a report for an A/B test.

        Args:
            test_id: ID of the test
            output_path: Optional path to save the report

        Returns:
            Dictionary with report data
        """
        if test_id not in self.tests:
            logger.error(f"Test with ID {test_id} not found")
            return {'success': False, 'error': 'Test not found'}

        test = self.tests[test_id]

        if test.status != "completed":
            logger.warning(f"Test with ID {test_id} is not completed (status: {test.status})")

        if not test.results_a or not test.results_b:
            logger.error(f"Test with ID {test_id} has no results")
            return {'success': False, 'error': 'No results'}

        try:
            # Create report data
            report = {
                'test_id': test_id,
                'test_name': test.test_name,
                'description': test.description,
                'created_at': test.created_at.isoformat(),
                'status': test.status,
                'model_a': {
                    'id': test.model_a_id,
                    'version': test.model_a_version,
                    'metrics': test.results_a.metrics
                },
                'model_b': {
                    'id': test.model_b_id,
                    'version': test.model_b_version,
                    'metrics': test.results_b.metrics
                },
                'winner': test.winner,
                'metrics': test.metrics
            }

            # Generate visualizations
            if output_path:
                self._generate_visualizations(test, output_path)

            return {
                'success': True,
                'report': report
            }

        except Exception as e:
            logger.error(f"Error generating report: {str(e)}")
            return {'success': False, 'error': str(e)}

    def _generate_visualizations(self, test: ABTest, output_path: str):
        """
        Generate visualizations for an A/B test.

        Args:
            test: ABTest object
            output_path: Path to save visualizations
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_path, exist_ok=True)

            # Create metrics comparison chart
            plt.figure(figsize=(12, 8))

            # Get metrics that exist in both results
            common_metrics = [m for m in test.metrics
                             if m in test.results_a.metrics and m in test.results_b.metrics]

            # Create bar chart
            x = np.arange(len(common_metrics))
            width = 0.35

            # Get values for each model
            values_a = [test.results_a.metrics[m] for m in common_metrics]
            values_b = [test.results_b.metrics[m] for m in common_metrics]

            # Create bars
            plt.bar(x - width/2, values_a, width, label=f"Model A: {test.model_a_id} (v{test.model_a_version})")
            plt.bar(x + width/2, values_b, width, label=f"Model B: {test.model_b_id} (v{test.model_b_version})")

            # Add labels and title
            plt.xlabel('Metrics')
            plt.ylabel('Values')
            plt.title(f'A/B Test Results: {test.test_name}')
            plt.xticks(x, common_metrics, rotation=45)
            plt.legend()

            # Add winner annotation
            if test.winner == f"{test.model_a_id}:{test.model_a_version}":
                winner_text = f"Winner: Model A ({test.model_a_id})"
            elif test.winner == f"{test.model_b_id}:{test.model_b_version}":
                winner_text = f"Winner: Model B ({test.model_b_id})"
            else:
                winner_text = "Result: Tie"

            plt.annotate(winner_text, xy=(0.5, 0.95), xycoords='figure fraction',
                        ha='center', va='center', fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", fc="yellow", alpha=0.3))

            plt.tight_layout()

            # Save figure
            output_file = os.path.join(output_path, f"ab_test_{test.test_id}.png")
            plt.savefig(output_file)
            plt.close()

            logger.info(f"Generated visualization for A/B test {test.test_id} at {output_file}")

        except Exception as e:
            logger.error(f"Error generating visualizations: {str(e)}")

    def compare_models(self,
                      model_a_id: str,
                      model_a_version: str,
                      model_b_id: str,
                      model_b_version: str,
                      evaluation_function: Callable[[str, str, List[str]], Dict[str, float]],
                      test_name: str = "Quick Comparison",
                      metrics: Optional[List[str]] = None,
                      data: Optional[Any] = None) -> Dict[str, Any]:
        """
        Quickly compare two models without creating a persistent test.

        Args:
            model_a_id: ID of model A
            model_a_version: Version of model A
            model_b_id: ID of model B
            model_b_version: Version of model B
            evaluation_function: Function to evaluate models
            test_name: Name for the comparison
            metrics: List of metrics to evaluate
            data: Optional data to use for evaluation

        Returns:
            Dictionary with comparison results
        """
        # Use default metrics if none provided
        if metrics is None:
            metrics = self.default_metrics

        start_time = time.time()

        try:
            # Evaluate model A
            logger.info(f"Evaluating model A: {model_a_id} (version {model_a_version})")
            metrics_a = evaluation_function(model_a_id, model_a_version, metrics, data)

            # Evaluate model B
            logger.info(f"Evaluating model B: {model_b_id} (version {model_b_version})")
            metrics_b = evaluation_function(model_b_id, model_b_version, metrics, data)

            # Create test results
            results_a = TestResult(
                model_id=model_a_id,
                model_version=model_a_version,
                metrics=metrics_a
            )

            results_b = TestResult(
                model_id=model_b_id,
                model_version=model_b_version,
                metrics=metrics_b
            )

            # Create temporary test for determining winner
            temp_test = ABTest(
                test_id="temp",
                model_a_id=model_a_id,
                model_a_version=model_a_version,
                model_b_id=model_b_id,
                model_b_version=model_b_version,
                test_name=test_name,
                description="Temporary test for quick comparison",
                metrics=metrics,
                results_a=results_a,
                results_b=results_b
            )

            # Determine winner
            winner = self._determine_winner(temp_test)

            elapsed = time.time() - start_time
            logger.info(f"Completed quick comparison in {elapsed:.2f} seconds. Winner: {winner}")

            return {
                'success': True,
                'test_name': test_name,
                'results_a': results_a.__dict__,
                'results_b': results_b.__dict__,
                'winner': winner,
                'elapsed_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error in quick comparison: {str(e)}")
            return {'success': False, 'error': str(e)}
