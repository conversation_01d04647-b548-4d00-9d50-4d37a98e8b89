#!/usr/bin/env python3
"""
CORE Paper Trading Fills Dashboard

Real-time monitoring of paper trading fills with slippage analysis.
Refreshes every 30 seconds with live data from fills directory.
"""

import streamlit as st
import pandas as pd
import glob
import json
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import os

# Page configuration
st.set_page_config(
    page_title="Paper-fills monitor", 
    layout="wide",
    page_icon=""
)

st.title(" CORE Paper Trading Monitor")
st.markdown("Real-time fills tracking with slippage analysis")

# Sidebar configuration
FILLS_DIR = st.sidebar.text_input("Fills directory", value="fills")
refresh_rate = st.sidebar.selectbox("Refresh rate", [10, 30, 60], index=1)

@st.cache_data(ttl=refresh_rate)
def load_fills_data():
    """Load and process fills data with caching"""
    rows = []
    
    if not os.path.exists(FILLS_DIR):
        return pd.DataFrame()
    
    # Search for fill.json files recursively
    pattern = f"{FILLS_DIR}/**/fill.json"
    fill_files = glob.glob(pattern, recursive=True)
    
    for filepath in fill_files:
        try:
            with open(filepath, 'r') as f:
                j = json.load(f)
                
            # Extract transaction data
            txn = j.get("transaction", {})
            
            rows.append({
                "timestamp": txn.get("date", "Unknown"),
                "ticker": txn.get("symbol", "Unknown"),
                "side": txn.get("side", "Unknown"),
                "quantity": int(txn.get("quantity", 0)),
                "fill_price": float(txn.get("price", 0.0)),
                "limit_price": float(j.get("price_limit", txn.get("price", 0.0))),
                "option_type": j.get("option_type", "Unknown"),
                "strike": j.get("strike", 0.0),
                "expiration": j.get("expiration", "Unknown"),
                "file_path": filepath
            })
            
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            st.sidebar.warning(f"Error reading {filepath}: {e}")
            continue
    
    if not rows:
        return pd.DataFrame()
        
    df = pd.DataFrame(rows)
    
    # Calculate slippage in cents
    df["slippage_cents"] = (df.fill_price - df.limit_price) * 100
    
    # Calculate dollar impact
    df["dollar_impact"] = df.slippage_cents * df.quantity / 100
    
    # Sort by timestamp descending
    df = df.sort_values("timestamp", ascending=False)
    
    return df

# Load data
df = load_fills_data()

# Main dashboard
if df.empty:
    st.warning(f"No fill data found in directory: {FILLS_DIR}")
    st.info("Ensure paper trading fills are being generated and saved to the specified directory.")
else:
    # Summary metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Fills", len(df))
    
    with col2:
        avg_slippage = df.slippage_cents.mean()
        st.metric("Avg Slippage", f"{avg_slippage:.2f}")
    
    with col3:
        total_pnl = df.dollar_impact.sum()
        st.metric("Total Slippage P&L", f"${total_pnl:.2f}")
    
    with col4:
        max_slippage = df.slippage_cents.max()
        st.metric("Max Slippage", f"{max_slippage:.2f}")
    
    # Recent fills table
    st.subheader("Recent Fills")
    display_cols = [
        "timestamp", "ticker", "side", "quantity", 
        "fill_price", "limit_price", "slippage_cents", "dollar_impact"
    ]
    
    # Format the dataframe for display
    display_df = df[display_cols].copy()
    display_df["fill_price"] = display_df["fill_price"].apply(lambda x: f"${x:.2f}")
    display_df["limit_price"] = display_df["limit_price"].apply(lambda x: f"${x:.2f}")
    display_df["slippage_cents"] = display_df["slippage_cents"].apply(lambda x: f"{x:.2f}")
    display_df["dollar_impact"] = display_df["dollar_impact"].apply(lambda x: f"${x:.2f}")
    
    st.dataframe(display_df, use_container_width=True)
    
    # Charts section
    col1, col2 = st.columns(2)
    
    with col1:
        # Slippage distribution histogram
        if len(df) > 1:
            fig_hist = px.histogram(
                df, 
                x="slippage_cents", 
                nbins=min(30, len(df)),
                title="Slippage Distribution ()",
                labels={"slippage_cents": "Slippage ()", "count": "Number of Fills"}
            )
            fig_hist.update_layout(showlegend=False)
            st.plotly_chart(fig_hist, use_container_width=True)
    
    with col2:
        # Slippage over time
        if len(df) > 1:
            fig_time = px.scatter(
                df,
                x="timestamp",
                y="slippage_cents",
                color="ticker",
                size="quantity",
                title="Slippage Over Time",
                labels={"slippage_cents": "Slippage ()", "timestamp": "Time"}
            )
            st.plotly_chart(fig_time, use_container_width=True)
    
    # Performance by ticker
    if len(df) > 1:
        st.subheader("Performance by Ticker")
        
        ticker_stats = df.groupby("ticker").agg({
            "slippage_cents": ["mean", "std", "count"],
            "dollar_impact": "sum"
        }).round(3)
        
        ticker_stats.columns = ["Avg Slippage ()", "Std Slippage ()", "Fill Count", "Total P&L ($)"]
        st.dataframe(ticker_stats, use_container_width=True)

# Sidebar info
st.sidebar.markdown("---")
st.sidebar.info(f"""
**Dashboard Info:**
- Refresh Rate: {refresh_rate}s
- Last Updated: {datetime.now().strftime('%H:%M:%S')}
- Files Found: {len(df) if not df.empty else 0}
""")

# Auto-refresh
if not df.empty:
    st.rerun()
