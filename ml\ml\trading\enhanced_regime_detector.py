"""
Enhanced Market Regime Detector

This module implements an advanced market regime detection system that can identify
more granular market conditions than the basic detector.
"""

import numpy as np
import pandas as pd
from enum import Enum
import logging

# Configure logging
logger = logging.getLogger(__name__)

class EnhancedMarketRegime(Enum):
    """Enhanced market regime enumeration with more granular states."""
    BULL = "bull"               # Clear uptrend with normal volatility
    BULL_STRONG = "bull_strong" # Strong uptrend with accelerating momentum
    BULL_QUIET = "bull_quiet"   # Uptrend with below-average volatility
    BULL_VOLATILE = "bull_volatile"  # Uptrend with above-average volatility
    BEAR = "bear"               # Clear downtrend with normal volatility
    BEAR_STRONG = "bear_strong" # Strong downtrend with accelerating momentum
    BEAR_QUIET = "bear_quiet"   # Downtrend with below-average volatility
    BEAR_VOLATILE = "bear_volatile"  # Downtrend with above-average volatility
    SIDEWAYS = "sideways"       # Range-bound with normal volatility
    SIDEWAYS_TIGHT = "sideways_tight"  # Range-bound with low volatility
    SIDEWAYS_VOLATILE = "sideways_volatile"  # Range-bound with high volatility
    VOLATILE = "volatile"       # High volatility without clear direction
    REVERSAL = "reversal"       # Potential trend reversal in progress
    TRANSITION = "transition"   # Transition between regimes
    UNKNOWN = "unknown"         # Not enough data to determine regime


def detect_enhanced_regime(market_data: pd.DataFrame) -> EnhancedMarketRegime:
    """
    Detect the current market regime using multiple indicators.
    
    Args:
        market_data: DataFrame with market data (must include OHLCV data)
            
    Returns:
        Detected market regime
    """
    try:
        # Check if we have enough data
        if len(market_data) < 20:
            logger.warning("Not enough data for reliable regime detection")
            return EnhancedMarketRegime.UNKNOWN
        
        # Extract required data
        if 'close' not in market_data.columns:
            logger.warning("Market data missing 'close' column. Cannot detect regime.")
            return EnhancedMarketRegime.UNKNOWN
        
        closes = market_data['close'].values
        volumes = market_data['volume'].values if 'volume' in market_data.columns else None
        
        # Calculate daily returns
        returns = np.diff(closes) / closes[:-1]
        
        # Calculate various metrics
        volatility = np.std(returns[-20:]) * np.sqrt(252)  # Annualized volatility
        mean_return = np.mean(returns[-20:]) * 252  # Annualized return
        
        # Calculate moving averages
        sma20 = np.mean(closes[-20:])
        sma50 = np.mean(closes[-50:]) if len(closes) >= 50 else sma20
        sma200 = np.mean(closes[-200:]) if len(closes) >= 200 else sma50
        
        # Calculate volume metrics if volume data is available
        volume_trend = 0
        if volumes is not None and len(volumes) >= 20:
            avg_volume = np.mean(volumes[-20:])
            recent_volume = np.mean(volumes[-5:])
            volume_trend = recent_volume / avg_volume - 1
        
        # Calculate trend strength (simplified ADX-like calculation)
        trend_strength = 0
        if len(market_data) >= 15 and all(col in market_data.columns for col in ['high', 'low']):
            highs = market_data['high'].values
            lows = market_data['low'].values
            
            # Calculate Directional Movement
            dm_plus = np.zeros(len(highs)-1)
            dm_minus = np.zeros(len(highs)-1)
            
            for i in range(1, len(highs)):
                dm_plus[i-1] = max(0, highs[i] - highs[i-1])
                dm_minus[i-1] = max(0, lows[i-1] - lows[i])
            
            # Smooth Directional Movement with 14-period Wilder's smoothing
            period = 14
            if len(dm_plus) >= period:
                smooth_dm_plus = np.mean(dm_plus[-period:])
                smooth_dm_minus = np.mean(dm_minus[-period:])
                
                # Calculate True Range
                tr = np.zeros(len(highs)-1)
                for i in range(1, len(highs)):
                    tr[i-1] = max(
                        highs[i] - lows[i],
                        abs(highs[i] - closes[i-1]),
                        abs(lows[i] - closes[i-1])
                    )
                
                atr = np.mean(tr[-period:])
                
                # Calculate Directional Indicators
                if atr > 0:
                    di_plus = 100 * smooth_dm_plus / atr
                    di_minus = 100 * smooth_dm_minus / atr
                    
                    # Calculate Directional Index
                    if (di_plus + di_minus) > 0:
                        dx = 100 * abs(di_plus - di_minus) / (di_plus + di_minus)
                        trend_strength = dx / 100  # Normalize to 0-1 range
        
        # Determine if market is in a momentum phase
        momentum = 0
        if len(closes) >= 50:
            # Calculate rate of change over different periods
            roc10 = (closes[-1] / closes[-10]) - 1
            roc20 = (closes[-1] / closes[-20]) - 1
            roc50 = (closes[-1] / closes[-50]) - 1
            
            # Check for accelerating momentum
            if abs(roc10) > abs(roc20) > abs(roc50) and np.sign(roc10) == np.sign(roc20) == np.sign(roc50):
                momentum = np.sign(roc10) * min(1.0, abs(roc10 / 0.1))  # Normalize, 10% ROC is considered strong
        
        # Volatility categorization relative to historical levels
        if len(returns) >= 100:
            hist_vol = np.std(returns) * np.sqrt(252)
            relative_vol = volatility / hist_vol
            
            if relative_vol < 0.7:
                vol_category = "low"
            elif relative_vol < 1.3:
                vol_category = "normal"
            elif relative_vol < 2.0:
                vol_category = "high"
            else:
                vol_category = "extreme"
        else:
            # Without enough history, use absolute levels
            if volatility < 0.15:  # 15% annualized
                vol_category = "low"
            elif volatility < 0.25:
                vol_category = "normal"
            elif volatility < 0.40:
                vol_category = "high"
            else:
                vol_category = "extreme"
        
        # Check for divergence (potential reversal signal)
        divergence = False
        if len(closes) >= 20 and volumes is not None and len(volumes) >= 20:
            # Price making higher highs but volume decreasing
            if closes[-1] > max(closes[-20:-1]) and np.mean(volumes[-5:]) < np.mean(volumes[-20:-5]):
                divergence = True
            # Price making lower lows but volume decreasing
            elif closes[-1] < min(closes[-20:-1]) and np.mean(volumes[-5:]) < np.mean(volumes[-20:-5]):
                divergence = True
        
        # Check for transition between regimes
        transition = False
        if len(closes) >= 20:
            # Check if recent price action differs from slightly older price action
            recent_return = np.mean(returns[-5:])
            older_return = np.mean(returns[-20:-5])
            
            if np.sign(recent_return) != np.sign(older_return) and abs(recent_return) > abs(older_return):
                transition = True
        
        # Regime classification logic
        # Trend determination
        if mean_return > 0.15:  # 15% annualized return threshold for bullish
            if closes[-1] > sma20 > sma50:
                trend = "bull"
                if momentum > 0.5:
                    trend = "bull_strong"
            elif divergence or transition:
                trend = "transition"
            else:
                trend = "sideways"
        elif mean_return < -0.15:  # -15% annualized return threshold for bearish
            if closes[-1] < sma20 < sma50:
                trend = "bear"
                if momentum < -0.5:
                    trend = "bear_strong"
            elif divergence or transition:
                trend = "transition"
            else:
                trend = "sideways"
        else:
            if abs(closes[-1] - sma20) / sma20 < 0.02 and abs(sma20 - sma50) / sma50 < 0.03:
                trend = "sideways"
            elif closes[-1] > sma20 > sma50:
                trend = "bull"
            elif closes[-1] < sma20 < sma50:
                trend = "bear"
            else:
                trend = "sideways"
        
        # Combine trend and volatility
        if trend == "bull":
            if vol_category == "low":
                regime = EnhancedMarketRegime.BULL_QUIET
            elif vol_category == "normal":
                regime = EnhancedMarketRegime.BULL
            elif vol_category == "high" or vol_category == "extreme":
                regime = EnhancedMarketRegime.BULL_VOLATILE
        elif trend == "bull_strong":
            regime = EnhancedMarketRegime.BULL_STRONG
        elif trend == "bear":
            if vol_category == "low":
                regime = EnhancedMarketRegime.BEAR_QUIET
            elif vol_category == "normal":
                regime = EnhancedMarketRegime.BEAR
            elif vol_category == "high" or vol_category == "extreme":
                regime = EnhancedMarketRegime.BEAR_VOLATILE
        elif trend == "bear_strong":
            regime = EnhancedMarketRegime.BEAR_STRONG
        elif trend == "sideways":
            if vol_category == "low":
                regime = EnhancedMarketRegime.SIDEWAYS_TIGHT
            elif vol_category == "normal":
                regime = EnhancedMarketRegime.SIDEWAYS
            elif vol_category == "high" or vol_category == "extreme":
                regime = EnhancedMarketRegime.SIDEWAYS_VOLATILE
        elif trend == "transition":
            regime = EnhancedMarketRegime.TRANSITION
        else:
            if vol_category == "high" or vol_category == "extreme":
                regime = EnhancedMarketRegime.VOLATILE
            else:
                regime = EnhancedMarketRegime.UNKNOWN
        
        # Override with reversal if divergence is detected
        if divergence and (trend == "bull" or trend == "bear"):
            regime = EnhancedMarketRegime.REVERSAL
        
        logger.debug(f"Detected regime: {regime.value}")
        return regime
        
    except Exception as e:
        logger.error(f"Error in regime detection: {str(e)}")
        return EnhancedMarketRegime.UNKNOWN


def map_enhanced_to_basic_regime(enhanced_regime: EnhancedMarketRegime):
    """
    Map an enhanced regime to the basic MarketRegime enum used in the original model.
    
    Args:
        enhanced_regime: Enhanced market regime
        
    Returns:
        Equivalent basic market regime
    """
    # from src.ml.trading.trade_model import MarketRegime  # External dependency
    try:
        from .trade_model import MarketRegime
    except ImportError:
        # Fallback definition
        class MarketRegime:
            BULL = "BULL"
            BEAR = "BEAR"
            NEUTRAL = "NEUTRAL"
    
    mapping = {
        EnhancedMarketRegime.BULL: MarketRegime.BULL,
        EnhancedMarketRegime.BULL_STRONG: MarketRegime.BULL,
        EnhancedMarketRegime.BULL_QUIET: MarketRegime.BULL,
        EnhancedMarketRegime.BULL_VOLATILE: MarketRegime.BULL,
        EnhancedMarketRegime.BEAR: MarketRegime.BEAR,
        EnhancedMarketRegime.BEAR_STRONG: MarketRegime.BEAR,
        EnhancedMarketRegime.BEAR_QUIET: MarketRegime.BEAR,
        EnhancedMarketRegime.BEAR_VOLATILE: MarketRegime.BEAR,
        EnhancedMarketRegime.SIDEWAYS: MarketRegime.SIDEWAYS,
        EnhancedMarketRegime.SIDEWAYS_TIGHT: MarketRegime.SIDEWAYS,
        EnhancedMarketRegime.SIDEWAYS_VOLATILE: MarketRegime.VOLATILE,
        EnhancedMarketRegime.VOLATILE: MarketRegime.VOLATILE,
        EnhancedMarketRegime.REVERSAL: MarketRegime.REVERSAL,
        EnhancedMarketRegime.TRANSITION: MarketRegime.TRANSITION,
        EnhancedMarketRegime.UNKNOWN: MarketRegime.UNKNOWN
    }
    
    return mapping.get(enhanced_regime, MarketRegime.UNKNOWN)
