# MCP Data Integration Guide

## Overview
The LiveDataGatewayAgent provides dual-mode data ingestion with MCP as the primary source and Polygon as fallback for real-time market data and options chains.

## Configuration

### MCP Setup (Primary)
```bash
export MCP_HOST="https://your.mcp.host/api/v1"
export MCP_TOKEN="your_bearer_token"  # Optional
export DATA_SOURCE="mcp"
```

### Polygon Setup (Fallback)
```bash
export POLYGON_API_KEY="your_polygon_api_key"
```

## Usage Examples

### Command Line
```bash
# MCP source (default)
py -m agents.data_ingestion_agent --tickers AAPL MSFT --source mcp

# Polygon fallback
py -m agents.data_ingestion_agent --tickers AAPL MSFT --source polygon --api-key YOUR_KEY
```

### Programmatic
```python
from agents.data_ingestion_agent import LiveDataGatewayAgent

agent = LiveDataGatewayAgent()

# Auto-select source from environment
source = os.getenv("DATA_SOURCE", "mcp")
result = agent.execute(["AAPL", "MSFT"], source=source)
```

## MCP API Endpoints Expected

### Bars Endpoint
```
GET {MCP_HOST}/bars?tk={ticker}&tf={timeframe}
Response: {"data": [{"timestamp": ..., "open": ..., "high": ..., "low": ..., "close": ..., "volume": ...}]}
```

### Options Endpoint  
```
GET {MCP_HOST}/options?tk={ticker}
Response: {"data": [{"strike": ..., "expiry": ..., "type": ..., "bid": ..., "ask": ..., "volume": ...}]}
```

## Output Format
- **Bars**: `data/live/{date}/{ticker}_bars.parquet`
- **Options**: `data/live/{date}/{ticker}_options.parquet`
- **Metadata**: Success/failure status, record counts, file paths

## Integration Points
1. **Agent Orchestration**: Use `DATA_SOURCE` env var for easy switching
2. **Fallback Logic**: Automatic Polygon fallback if MCP fails
3. **Training Data**: All requests logged for Agent Zero learning
4. **Quality Gates**: Minimum data thresholds (10+ bars, 50+ options)

## Testing
```bash
# Run tests (MCP integration test skipped if no MCP_HOST)
py -m pytest tests/test_data_ingestion.py -v

# Test both sources
py orchestrator_example.py --test-sources
```

## Performance Targets
- **Runtime**: <15 seconds for multi-ticker ingestion
- **Data Quality**: 10+ bars, 50+ options minimum
- **Reliability**: MCP primary with Polygon fallback
