#!/usr/bin/env python3
"""
Test Ticker-Agnostic MCP Server
Verify that the MCP server works with any ticker symbol
"""

import requests
import json
import time

def test_ticker_agnostic_functionality():
    """Test MCP server with various ticker symbols"""
    print("TESTING TICKER-AGNOSTIC MCP SERVER")
    print("=" * 50)
    
    base_url = "http://localhost:8005"
    
    # Test various ticker types
    test_tickers = [
        # ETFs
        "SPY", "QQQ", "IWM", "VTI",
        # Large caps
        "AAPL", "MSFT", "GOOGL", "TSLA", 
        # Mid/Small caps
        "AMD", "PLTR", "ABCD", "XYZT",
        # Random symbols
        "TEST", "FAKE", "DEMO"
    ]
    
    success_count = 0
    total_tests = len(test_tickers)
    
    print(f"\nTesting {total_tests} different ticker symbols...")
    print("-" * 50)
    
    for ticker in test_tickers:
        try:
            # Test quotes endpoint
            response = requests.get(f"{base_url}/quotes/{ticker}", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                symbol = data.get('symbol')
                price = data.get('last_price')
                bid = data.get('bid')
                ask = data.get('ask')
                volume = data.get('volume')
                
                # Validate data structure
                if all([symbol, price, bid, ask, volume is not None]):
                    print(f"PASS - {ticker}: ${price:.2f} (${bid:.2f}/${ask:.2f}) Vol: {volume:,}")
                    success_count += 1
                else:
                    print(f"FAIL - {ticker}: Missing required fields")
            else:
                print(f"FAIL - {ticker}: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"FAIL - {ticker}: {e}")
            
        time.sleep(0.1)  # Brief pause between requests
    
    print("-" * 50)
    print(f"\nRESULTS: {success_count}/{total_tests} tickers successful")
    print(f"Success Rate: {success_count/total_tests*100:.1f}%")
    
    # Test options endpoint with a few symbols
    print("\nTesting options endpoint...")
    options_test_tickers = ["SPY", "AAPL", "RANDOM"]
    
    for ticker in options_test_tickers:
        try:
            response = requests.get(f"{base_url}/options/{ticker}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                calls = data.get('calls', [])
                puts = data.get('puts', [])
                print(f"PASS - {ticker} options: {len(calls)} calls, {len(puts)} puts")
            else:
                print(f"FAIL - {ticker} options: HTTP {response.status_code}")
        except Exception as e:
            print(f"FAIL - {ticker} options: {e}")
    
    # Test consistency (same ticker should return same base data)
    print("\nTesting data consistency...")
    ticker = "CONSISTENCY_TEST"
    
    responses = []
    for i in range(3):
        try:
            response = requests.get(f"{base_url}/quotes/{ticker}", timeout=5)
            if response.status_code == 200:
                data = response.json()
                responses.append(data.get('last_price'))
        except:
            pass
        time.sleep(0.1)
    
    if len(responses) >= 2:
        # Prices should be very similar (same base price with small intraday variation)
        price_range = max(responses) - min(responses)
        if price_range < 5.0:  # Should be within $5 range
            print(f"PASS - Data consistency: Price range ${price_range:.2f}")
        else:
            print(f"WARN - Data consistency: Large price range ${price_range:.2f}")
    
    print("\n" + "=" * 50)
    if success_count == total_tests:
        print("SUCCESS - MCP Server is fully ticker-agnostic!")
        return True
    else:
        print(f"PARTIAL - {total_tests - success_count} tickers failed")
        return False

if __name__ == "__main__":
    test_ticker_agnostic_functionality()
