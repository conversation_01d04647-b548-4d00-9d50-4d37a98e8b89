"""Flow Jerk Analyzer

Analyzes flow jerk (third derivative) to detect sudden acceleration changes
that indicate institutional commitment shifts and regime changes.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from scipy import signal
from scipy.stats import kurtosis, skew
import warnings

from . import FLOW_PHYSICS_CONSTANTS, FLOW_REGIMES

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore', category=RuntimeWarning)


@dataclass
class JerkEvent:
    """Represents a significant jerk event."""
    timestamp: datetime
    jerk_value: float
    event_type: str  # 'commitment_shift', 'regime_change', 'shock', 'stabilization'
    severity: str  # 'low', 'medium', 'high', 'extreme'
    duration: Optional[timedelta]
    preceded_by: Optional[str]  # What pattern preceded this event
    likely_outcome: str  # Predicted outcome
    confidence: float
    

@dataclass
class JerkProfile:
    """Comprehensive jerk analysis profile."""
    timestamp: datetime
    raw_jerk: float
    smoothed_jerk: float
    normalized_jerk: float
    
    # Event detection
    jerk_event: Optional[JerkEvent]
    event_probability: float
    
    # Regime change analysis
    regime_change_detected: bool
    regime_change_type: Optional[str]  # 'bullish_to_bearish', 'bearish_to_bullish', etc.
    regime_change_confidence: float
    
    # Institutional commitment
    commitment_shift_detected: bool
    commitment_direction: str  # 'increasing', 'decreasing', 'neutral'
    commitment_urgency: float  # 0-1 scale
    
    # Early warning indicators
    instability_score: float  # Market instability metric
    breakout_probability: float
    reversal_probability: float
    
    # Statistical properties
    jerk_percentile: float
    jerk_zscore: float
    jerk_kurtosis: float  # Measure of tail risk
    
    # Pattern analysis
    jerk_pattern: Optional[str]  # 'spike', 'oscillation', 'decay', 'buildup'
    pattern_phase: str  # 'early', 'middle', 'late'
    
    # Risk metrics
    volatility_surge_risk: float
    liquidity_shock_risk: float
    cascade_risk: float  # Risk of triggering cascade effects
    

class FlowJerkAnalyzer:
    """Analyzer for flow jerk with regime change and commitment shift detection."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the flow jerk analyzer.
        
        Args:
            config: Optional configuration overrides
        """
        self.config = config or {}
        self.constants = FLOW_PHYSICS_CONSTANTS
        self.regimes = FLOW_REGIMES
        
        # Analysis parameters
        self.lookback_window = self.config.get('lookback_window', 30)
        self.event_threshold = self.config.get('event_threshold', 2.0)  # Standard deviations
        self.smoothing_window = self.config.get('smoothing_window', 3)
        
        # Thresholds
        self.min_jerk_threshold = self.constants['MIN_JERK_THRESHOLD']
        self.regime_jerk_threshold = self.constants['REGIME_CHANGE_JERK_THRESHOLD']
        self.extreme_jerk_threshold = self.constants['EXTREME_JERK_THRESHOLD']
        
        # Pattern detection parameters
        self.pattern_window = self.config.get('pattern_window', 10)
        self.commitment_window = self.config.get('commitment_window', 15)
        
        # Historical storage
        self._jerk_history = {}
        self._event_history = {}
        self._regime_transitions = {}
        
        logger.info("Flow Jerk Analyzer initialized")
        
    def analyze(self,
                symbol: str,
                acceleration_data: pd.DataFrame,
                current_acceleration: float,
                velocity: float,
                flow: float,
                timestamp: Optional[datetime] = None) -> JerkProfile:
        """Analyze flow jerk for regime changes and commitment shifts.
        
        Args:
            symbol: Symbol being analyzed
            acceleration_data: Historical acceleration data
            current_acceleration: Current acceleration value
            velocity: Current velocity
            flow: Current flow value
            timestamp: Current timestamp
            
        Returns:
            JerkProfile with comprehensive jerk analysis
        """
        timestamp = timestamp or datetime.now()
        
        try:
            # Calculate raw jerk
            raw_jerk = self._calculate_raw_jerk(
                acceleration_data, current_acceleration
            )
            
            # Apply smoothing
            smoothed_jerk = self._smooth_jerk(symbol, raw_jerk)
            
            # Normalize jerk
            normalized_jerk = self._normalize_jerk(smoothed_jerk)
            
            # Detect jerk events
            event_analysis = self._detect_jerk_event(
                symbol, smoothed_jerk, current_acceleration, velocity
            )
            
            # Analyze regime changes
            regime_analysis = self._analyze_regime_change(
                symbol, smoothed_jerk, velocity, flow
            )
            
            # Detect commitment shifts
            commitment_analysis = self._detect_commitment_shift(
                symbol, smoothed_jerk, current_acceleration
            )
            
            # Calculate early warning indicators
            warnings = self._calculate_early_warnings(
                symbol, smoothed_jerk, velocity
            )
            
            # Calculate statistical properties
            stats = self._calculate_jerk_statistics(symbol, smoothed_jerk)
            
            # Detect patterns
            pattern_analysis = self._detect_jerk_patterns(symbol, smoothed_jerk)
            
            # Calculate risk metrics
            risks = self._calculate_risk_metrics(
                symbol, smoothed_jerk, velocity, flow
            )
            
            # Create jerk profile
            profile = JerkProfile(
                timestamp=timestamp,
                raw_jerk=raw_jerk,
                smoothed_jerk=smoothed_jerk,
                normalized_jerk=normalized_jerk,
                jerk_event=event_analysis['event'],
                event_probability=event_analysis['probability'],
                regime_change_detected=regime_analysis['detected'],
                regime_change_type=regime_analysis['type'],
                regime_change_confidence=regime_analysis['confidence'],
                commitment_shift_detected=commitment_analysis['detected'],
                commitment_direction=commitment_analysis['direction'],
                commitment_urgency=commitment_analysis['urgency'],
                instability_score=warnings['instability'],
                breakout_probability=warnings['breakout_prob'],
                reversal_probability=warnings['reversal_prob'],
                jerk_percentile=stats['percentile'],
                jerk_zscore=stats['zscore'],
                jerk_kurtosis=stats['kurtosis'],
                jerk_pattern=pattern_analysis['pattern'],
                pattern_phase=pattern_analysis['phase'],
                volatility_surge_risk=risks['volatility_risk'],
                liquidity_shock_risk=risks['liquidity_risk'],
                cascade_risk=risks['cascade_risk']
            )
            
            # Store in history
            self._update_history(symbol, profile)
            
            return profile
            
        except Exception as e:
            logger.error(f"Error in jerk analysis: {e}")
            return self._create_default_profile(timestamp)
            
    def _calculate_raw_jerk(self,
                          acceleration_data: pd.DataFrame,
                          current_acceleration: float) -> float:
        """Calculate raw jerk from acceleration data."""
        if acceleration_data.empty or len(acceleration_data) < 2:
            return 0.0
            
        # Get recent acceleration points
        recent_data = acceleration_data.tail(4).copy()
        
        # Add current point
        current_point = pd.DataFrame({
            'timestamp': [datetime.now()],
            'acceleration': [current_acceleration]
        })
        recent_data = pd.concat([recent_data, current_point], ignore_index=True)
        
        # Convert timestamps to seconds
        recent_data['time_seconds'] = recent_data['timestamp'].apply(
            lambda x: x.timestamp() if isinstance(x, datetime) else x
        )
        
        # Calculate jerk using numpy gradient
        accelerations = recent_data['acceleration'].values
        times = recent_data['time_seconds'].values
        
        if len(accelerations) >= 2:
            # Use gradient for smoother derivative
            jerks = np.gradient(accelerations, times)
            return float(jerks[-1])
        else:
            return 0.0
            
    def _smooth_jerk(self, symbol: str, raw_jerk: float) -> float:
        """Apply smoothing to jerk values."""
        if symbol not in self._jerk_history:
            return raw_jerk
            
        history = self._jerk_history[symbol]
        if len(history) < self.smoothing_window:
            return raw_jerk
            
        # Get recent jerk values
        recent_jerks = [h.raw_jerk for h in history[-self.smoothing_window:]]
        recent_jerks.append(raw_jerk)
        
        # Apply median filter to reduce noise
        if len(recent_jerks) >= 3:
            smoothed = signal.medfilt(recent_jerks, kernel_size=3)[-1]
        else:
            smoothed = np.mean(recent_jerks)
            
        return smoothed
        
    def _normalize_jerk(self, jerk: float) -> float:
        """Normalize jerk to standard scale."""
        if abs(jerk) < self.min_jerk_threshold:
            return 0.0
            
        normalized = jerk / self.extreme_jerk_threshold
        return np.clip(normalized, -1.0, 1.0)
        
    def _detect_jerk_event(self,
                         symbol: str,
                         jerk: float,
                         acceleration: float,
                         velocity: float) -> Dict[str, Any]:
        """Detect significant jerk events."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < 5:
            return {'event': None, 'probability': 0.0}
            
        history = self._jerk_history[symbol]
        historical_jerks = [h.smoothed_jerk for h in history]
        
        # Calculate jerk statistics
        mean_jerk = np.mean(historical_jerks)
        std_jerk = np.std(historical_jerks)
        
        if std_jerk == 0:
            return {'event': None, 'probability': 0.0}
            
        # Check if current jerk is significant
        z_score = abs((jerk - mean_jerk) / std_jerk)
        
        if z_score < self.event_threshold:
            return {'event': None, 'probability': 0.0}
            
        # Determine event type
        event_type = self._classify_jerk_event(
            jerk, acceleration, velocity, z_score
        )
        
        # Determine severity
        if abs(jerk) > self.extreme_jerk_threshold:
            severity = 'extreme'
        elif abs(jerk) > self.regime_jerk_threshold:
            severity = 'high'
        elif abs(jerk) > self.min_jerk_threshold * 2:
            severity = 'medium'
        else:
            severity = 'low'
            
        # Analyze what preceded this event
        preceded_by = self._analyze_preceding_pattern(symbol)
        
        # Predict likely outcome
        likely_outcome = self._predict_jerk_outcome(
            event_type, severity, velocity
        )
        
        # Create event
        event = JerkEvent(
            timestamp=datetime.now(),
            jerk_value=jerk,
            event_type=event_type,
            severity=severity,
            duration=None,  # Will be updated when event ends
            preceded_by=preceded_by,
            likely_outcome=likely_outcome,
            confidence=min(1.0, z_score / 4)  # Confidence based on significance
        )
        
        # Store event
        self._store_event(symbol, event)
        
        return {
            'event': event,
            'probability': min(1.0, z_score / 3)
        }
        
    def _classify_jerk_event(self,
                           jerk: float,
                           acceleration: float,
                           velocity: float,
                           z_score: float) -> str:
        """Classify the type of jerk event."""
        # Commitment shift: Large jerk with aligned acceleration
        if z_score > 3 and np.sign(jerk) == np.sign(acceleration):
            return 'commitment_shift'
            
        # Regime change: Extreme jerk
        elif abs(jerk) > self.regime_jerk_threshold:
            return 'regime_change'
            
        # Shock: Sudden jerk opposite to velocity
        elif np.sign(jerk) != np.sign(velocity) and z_score > 2.5:
            return 'shock'
            
        # Stabilization: Jerk opposing acceleration (damping)
        elif np.sign(jerk) != np.sign(acceleration):
            return 'stabilization'
            
        else:
            return 'unknown'
            
    def _analyze_preceding_pattern(self, symbol: str) -> Optional[str]:
        """Analyze what pattern preceded the current jerk event."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < 10:
            return None
            
        history = self._jerk_history[symbol]
        recent_patterns = [h.jerk_pattern for h in history[-10:-1] if h.jerk_pattern]
        
        if not recent_patterns:
            return None
            
        # Find most common recent pattern
        from collections import Counter
        pattern_counts = Counter(recent_patterns)
        return pattern_counts.most_common(1)[0][0]
        
    def _predict_jerk_outcome(self,
                            event_type: str,
                            severity: str,
                            velocity: float) -> str:
        """Predict likely outcome of jerk event."""
        if event_type == 'commitment_shift':
            if severity in ['high', 'extreme']:
                return 'sustained_trend'
            else:
                return 'temporary_move'
                
        elif event_type == 'regime_change':
            return 'trend_reversal'
            
        elif event_type == 'shock':
            if abs(velocity) > 0:
                return 'momentum_break'
            else:
                return 'volatility_spike'
                
        elif event_type == 'stabilization':
            return 'consolidation'
            
        else:
            return 'uncertain'
            
    def _store_event(self, symbol: str, event: JerkEvent):
        """Store jerk event in history."""
        if symbol not in self._event_history:
            self._event_history[symbol] = []
            
        self._event_history[symbol].append(event)
        
        # Limit history
        if len(self._event_history[symbol]) > 50:
            self._event_history[symbol] = self._event_history[symbol][-50:]
            
    def _analyze_regime_change(self,
                             symbol: str,
                             jerk: float,
                             velocity: float,
                             flow: float) -> Dict[str, Any]:
        """Analyze if jerk indicates regime change."""
        if abs(jerk) < self.regime_jerk_threshold:
            return {
                'detected': False,
                'type': None,
                'confidence': 0.0
            }
            
        # Determine current regime from velocity and flow
        current_regime = self._determine_current_regime(velocity, flow)
        
        # Check regime history
        if symbol in self._regime_transitions:
            last_regime = self._regime_transitions[symbol]['last_regime']
            
            if current_regime != last_regime:
                # Regime change detected
                change_type = f"{last_regime}_to_{current_regime}"
                
                # Calculate confidence based on jerk magnitude
                confidence = min(1.0, abs(jerk) / self.extreme_jerk_threshold)
                
                # Update regime history
                self._regime_transitions[symbol] = {
                    'last_regime': current_regime,
                    'change_time': datetime.now(),
                    'jerk_magnitude': abs(jerk)
                }
                
                return {
                    'detected': True,
                    'type': change_type,
                    'confidence': confidence
                }
        else:
            # Initialize regime tracking
            self._regime_transitions[symbol] = {
                'last_regime': current_regime,
                'change_time': datetime.now(),
                'jerk_magnitude': abs(jerk)
            }
            
        return {
            'detected': False,
            'type': None,
            'confidence': 0.0
        }
        
    def _determine_current_regime(self, velocity: float, flow: float) -> str:
        """Determine current market regime."""
        if velocity > 0 and flow > 0:
            return 'bullish'
        elif velocity < 0 and flow < 0:
            return 'bearish'
        else:
            return 'neutral'
            
    def _detect_commitment_shift(self,
                               symbol: str,
                               jerk: float,
                               acceleration: float) -> Dict[str, Any]:
        """Detect institutional commitment shifts."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < self.commitment_window:
            return {
                'detected': False,
                'direction': 'neutral',
                'urgency': 0.0
            }
            
        history = self._jerk_history[symbol]
        recent_jerks = [h.smoothed_jerk for h in history[-self.commitment_window:]]
        
        # Check for sustained high jerk in one direction
        positive_jerks = sum(1 for j in recent_jerks if j > self.min_jerk_threshold)
        negative_jerks = sum(1 for j in recent_jerks if j < -self.min_jerk_threshold)
        
        total_significant = positive_jerks + negative_jerks
        
        if total_significant < self.commitment_window * 0.3:
            return {
                'detected': False,
                'direction': 'neutral',
                'urgency': 0.0
            }
            
        # Determine direction
        if positive_jerks > negative_jerks * 2:
            direction = 'increasing'
        elif negative_jerks > positive_jerks * 2:
            direction = 'decreasing'
        else:
            direction = 'neutral'
            
        # Calculate urgency
        avg_jerk_magnitude = np.mean([abs(j) for j in recent_jerks])
        urgency = min(1.0, avg_jerk_magnitude / self.extreme_jerk_threshold)
        
        detected = direction != 'neutral' and urgency > 0.3
        
        return {
            'detected': detected,
            'direction': direction,
            'urgency': urgency
        }
        
    def _calculate_early_warnings(self,
                                symbol: str,
                                jerk: float,
                                velocity: float) -> Dict[str, Any]:
        """Calculate early warning indicators."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < 10:
            return {
                'instability': 0.5,
                'breakout_prob': 0.3,
                'reversal_prob': 0.3
            }
            
        history = self._jerk_history[symbol]
        recent_jerks = [h.smoothed_jerk for h in history[-20:]]
        
        # Calculate instability score
        instability = self._calculate_instability(recent_jerks)
        
        # Calculate breakout probability
        breakout_prob = self._calculate_breakout_probability(
            recent_jerks, jerk, velocity
        )
        
        # Calculate reversal probability
        reversal_prob = self._calculate_reversal_probability_from_jerk(
            recent_jerks, jerk, velocity
        )
        
        return {
            'instability': instability,
            'breakout_prob': breakout_prob,
            'reversal_prob': reversal_prob
        }
        
    def _calculate_instability(self, jerks: List[float]) -> float:
        """Calculate market instability from jerk patterns."""
        if len(jerks) < 3:
            return 0.5
            
        # High variance in jerk indicates instability
        jerk_variance = np.var(jerks)
        
        # Normalize by threshold
        normalized_variance = jerk_variance / (self.regime_jerk_threshold ** 2)
        
        # Convert to 0-1 scale
        instability = min(1.0, normalized_variance)
        
        return instability
        
    def _calculate_breakout_probability(self,
                                      jerks: List[float],
                                      current_jerk: float,
                                      velocity: float) -> float:
        """Calculate probability of breakout."""
        # Factors that increase breakout probability:
        # 1. Increasing jerk magnitude
        # 2. Jerk aligned with velocity
        # 3. Low recent volatility followed by spike
        
        prob = 0.0
        
        # Factor 1: Jerk magnitude trend
        if len(jerks) >= 5:
            recent_magnitudes = [abs(j) for j in jerks[-5:]]
            if all(recent_magnitudes[i] <= recent_magnitudes[i+1] 
                   for i in range(len(recent_magnitudes)-1)):
                prob += 0.3
                
        # Factor 2: Alignment
        if np.sign(current_jerk) == np.sign(velocity) and abs(current_jerk) > self.min_jerk_threshold:
            prob += 0.3
            
        # Factor 3: Volatility compression
        if len(jerks) >= 10:
            early_vol = np.std(jerks[:-5])
            recent_vol = np.std(jerks[-5:])
            if recent_vol > early_vol * 2:
                prob += 0.4
                
        return min(1.0, prob)
        
    def _calculate_reversal_probability_from_jerk(self,
                                                jerks: List[float],
                                                current_jerk: float,
                                                velocity: float) -> float:
        """Calculate reversal probability from jerk patterns."""
        # Factors that increase reversal probability:
        # 1. Extreme jerk opposite to velocity
        # 2. Series of opposing jerks
        # 3. Diverging jerk pattern
        
        prob = 0.0
        
        # Factor 1: Opposition
        if np.sign(current_jerk) != np.sign(velocity) and abs(current_jerk) > self.regime_jerk_threshold:
            prob += 0.4
            
        # Factor 2: Series of opposing jerks
        if len(jerks) >= 5:
            opposing_count = sum(1 for j in jerks[-5:] 
                               if np.sign(j) != np.sign(velocity))
            if opposing_count >= 3:
                prob += 0.3
                
        # Factor 3: Diverging pattern
        if len(jerks) >= 3:
            # Check if jerk magnitude is increasing while opposing velocity
            if all(np.sign(j) != np.sign(velocity) for j in jerks[-3:]):
                if all(abs(jerks[i]) < abs(jerks[i+1]) for i in range(-3, -1)):
                    prob += 0.3
                    
        return min(1.0, prob)
        
    def _calculate_jerk_statistics(self,
                                 symbol: str,
                                 jerk: float) -> Dict[str, Any]:
        """Calculate statistical properties of jerk."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < 5:
            return {
                'percentile': 50.0,
                'zscore': 0.0,
                'kurtosis': 3.0  # Normal distribution kurtosis
            }
            
        history = self._jerk_history[symbol]
        historical_jerks = [h.smoothed_jerk for h in history]
        
        # Calculate percentile
        sorted_jerks = sorted(historical_jerks)
        pos = np.searchsorted(sorted_jerks, jerk)
        percentile = (pos / len(sorted_jerks)) * 100
        
        # Calculate z-score
        mean = np.mean(historical_jerks)
        std = np.std(historical_jerks)
        zscore = (jerk - mean) / std if std > 0 else 0.0
        
        # Calculate kurtosis (tail risk)
        if len(historical_jerks) >= 10:
            kurt = kurtosis(historical_jerks)
        else:
            kurt = 3.0  # Normal distribution
            
        return {
            'percentile': percentile,
            'zscore': zscore,
            'kurtosis': kurt
        }
        
    def _detect_jerk_patterns(self,
                            symbol: str,
                            jerk: float) -> Dict[str, Any]:
        """Detect patterns in jerk behavior."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < self.pattern_window:
            return {
                'pattern': None,
                'phase': 'unknown'
            }
            
        history = self._jerk_history[symbol]
        recent_jerks = [h.smoothed_jerk for h in history[-self.pattern_window:]]
        recent_jerks.append(jerk)
        
        # Detect patterns
        if self._is_spike_pattern(recent_jerks):
            pattern = 'spike'
            phase = self._determine_spike_phase(recent_jerks)
        elif self._is_oscillation_pattern(recent_jerks):
            pattern = 'oscillation'
            phase = self._determine_oscillation_phase(recent_jerks)
        elif self._is_decay_pattern(recent_jerks):
            pattern = 'decay'
            phase = 'late'  # Decay is typically late phase
        elif self._is_buildup_pattern(recent_jerks):
            pattern = 'buildup'
            phase = 'early'  # Buildup is typically early phase
        else:
            pattern = None
            phase = 'unknown'
            
        return {
            'pattern': pattern,
            'phase': phase
        }
        
    def _is_spike_pattern(self, jerks: List[float]) -> bool:
        """Check for spike pattern in jerk."""
        if len(jerks) < 3:
            return False
            
        # Find maximum absolute jerk
        max_idx = np.argmax([abs(j) for j in jerks])
        
        # Check if it's significantly larger than neighbors
        if 0 < max_idx < len(jerks) - 1:
            max_val = abs(jerks[max_idx])
            before = abs(jerks[max_idx - 1])
            after = abs(jerks[max_idx + 1])
            
            return max_val > (before + after) * 2
            
        return False
        
    def _is_oscillation_pattern(self, jerks: List[float]) -> bool:
        """Check for oscillation pattern."""
        if len(jerks) < 4:
            return False
            
        # Count sign changes
        sign_changes = sum(1 for i in range(1, len(jerks)) 
                          if np.sign(jerks[i]) != np.sign(jerks[i-1]) and 
                          jerks[i] != 0 and jerks[i-1] != 0)
        
        return sign_changes >= len(jerks) * 0.4
        
    def _is_decay_pattern(self, jerks: List[float]) -> bool:
        """Check for decay pattern (decreasing magnitude)."""
        if len(jerks) < 4:
            return False
            
        magnitudes = [abs(j) for j in jerks]
        
        # Check if generally decreasing
        decreasing_count = sum(1 for i in range(1, len(magnitudes)) 
                              if magnitudes[i] < magnitudes[i-1])
        
        return decreasing_count >= len(magnitudes) * 0.6
        
    def _is_buildup_pattern(self, jerks: List[float]) -> bool:
        """Check for buildup pattern (increasing magnitude)."""
        if len(jerks) < 4:
            return False
            
        magnitudes = [abs(j) for j in jerks]
        
        # Check if generally increasing
        increasing_count = sum(1 for i in range(1, len(magnitudes)) 
                              if magnitudes[i] > magnitudes[i-1])
        
        return increasing_count >= len(magnitudes) * 0.6
        
    def _determine_spike_phase(self, jerks: List[float]) -> str:
        """Determine phase of spike pattern."""
        max_idx = np.argmax([abs(j) for j in jerks])
        position_ratio = max_idx / len(jerks)
        
        if position_ratio < 0.3:
            return 'early'
        elif position_ratio < 0.7:
            return 'middle'
        else:
            return 'late'
            
    def _determine_oscillation_phase(self, jerks: List[float]) -> str:
        """Determine phase of oscillation pattern."""
        # Check if amplitude is changing
        first_half = jerks[:len(jerks)//2]
        second_half = jerks[len(jerks)//2:]
        
        first_amp = np.mean([abs(j) for j in first_half])
        second_amp = np.mean([abs(j) for j in second_half])
        
        if second_amp > first_amp * 1.2:
            return 'early'  # Amplifying
        elif second_amp < first_amp * 0.8:
            return 'late'  # Damping
        else:
            return 'middle'  # Stable
            
    def _calculate_risk_metrics(self,
                              symbol: str,
                              jerk: float,
                              velocity: float,
                              flow: float) -> Dict[str, Any]:
        """Calculate risk metrics based on jerk analysis."""
        # Volatility surge risk
        volatility_risk = self._calculate_volatility_surge_risk(symbol, jerk)
        
        # Liquidity shock risk
        liquidity_risk = self._calculate_liquidity_shock_risk(
            symbol, jerk, flow
        )
        
        # Cascade risk
        cascade_risk = self._calculate_cascade_risk(
            symbol, jerk, velocity
        )
        
        return {
            'volatility_risk': volatility_risk,
            'liquidity_risk': liquidity_risk,
            'cascade_risk': cascade_risk
        }
        
    def _calculate_volatility_surge_risk(self, symbol: str, jerk: float) -> float:
        """Calculate risk of volatility surge."""
        if symbol not in self._jerk_history or len(self._jerk_history[symbol]) < 10:
            return 0.3
            
        history = self._jerk_history[symbol]
        
        # High jerk magnitude increases volatility risk
        magnitude_risk = min(1.0, abs(jerk) / self.extreme_jerk_threshold)
        
        # Recent instability increases risk
        recent_jerks = [h.smoothed_jerk for h in history[-10:]]
        instability_risk = self._calculate_instability(recent_jerks)
        
        # Combined risk
        return (magnitude_risk + instability_risk) / 2
        
    def _calculate_liquidity_shock_risk(self,
                                      symbol: str,
                                      jerk: float,
                                      flow: float) -> float:
        """Calculate risk of liquidity shock."""
        # Extreme jerk with low flow indicates liquidity risk
        if abs(flow) < 0.1:  # Low flow threshold
            if abs(jerk) > self.regime_jerk_threshold:
                return 0.8
            elif abs(jerk) > self.min_jerk_threshold:
                return 0.5
                
        # Normal conditions
        return min(0.3, abs(jerk) / self.extreme_jerk_threshold)
        
    def _calculate_cascade_risk(self,
                              symbol: str,
                              jerk: float,
                              velocity: float) -> float:
        """Calculate risk of cascade effects."""
        # Extreme jerk aligned with velocity can trigger cascades
        if np.sign(jerk) == np.sign(velocity) and abs(jerk) > self.regime_jerk_threshold:
            alignment_risk = 0.6
        else:
            alignment_risk = 0.2
            
        # Check for recent events that could amplify cascade
        if symbol in self._event_history and self._event_history[symbol]:
            recent_events = [e for e in self._event_history[symbol][-5:] 
                           if e.severity in ['high', 'extreme']]
            if recent_events:
                event_risk = 0.4
            else:
                event_risk = 0.1
        else:
            event_risk = 0.1
            
        return min(1.0, alignment_risk + event_risk)
        
    def _update_history(self, symbol: str, profile: JerkProfile):
        """Update jerk history for symbol."""
        if symbol not in self._jerk_history:
            self._jerk_history[symbol] = []
            
        self._jerk_history[symbol].append(profile)
        
        # Limit history size
        max_history = self.lookback_window * 2
        if len(self._jerk_history[symbol]) > max_history:
            self._jerk_history[symbol] = self._jerk_history[symbol][-max_history:]
            
    def _create_default_profile(self, timestamp: datetime) -> JerkProfile:
        """Create default profile when analysis fails."""
        return JerkProfile(
            timestamp=timestamp,
            raw_jerk=0.0,
            smoothed_jerk=0.0,
            normalized_jerk=0.0,
            jerk_event=None,
            event_probability=0.0,
            regime_change_detected=False,
            regime_change_type=None,
            regime_change_confidence=0.0,
            commitment_shift_detected=False,
            commitment_direction='neutral',
            commitment_urgency=0.0,
            instability_score=0.5,
            breakout_probability=0.3,
            reversal_probability=0.3,
            jerk_percentile=50.0,
            jerk_zscore=0.0,
            jerk_kurtosis=3.0,
            jerk_pattern=None,
            pattern_phase='unknown',
            volatility_surge_risk=0.3,
            liquidity_shock_risk=0.3,
            cascade_risk=0.3
        )
        
    def get_jerk_summary(self, symbol: str) -> Dict[str, Any]:
        """Get summary of jerk analysis for symbol."""
        if symbol not in self._jerk_history:
            return {'status': 'no_data'}
            
        history = self._jerk_history[symbol]
        if not history:
            return {'status': 'no_data'}
            
        recent = history[-1]
        jerks = [h.smoothed_jerk for h in history]
        
        # Get recent events
        recent_events = []
        if symbol in self._event_history:
            recent_events = self._event_history[symbol][-5:]
            
        return {
            'status': 'active',
            'current_jerk': recent.smoothed_jerk,
            'normalized_jerk': recent.normalized_jerk,
            'regime_change_detected': recent.regime_change_detected,
            'commitment_shift_detected': recent.commitment_shift_detected,
            'instability_score': recent.instability_score,
            'pattern': recent.jerk_pattern,
            'recent_events': len(recent_events),
            'avg_jerk': np.mean(jerks),
            'max_jerk': max(jerks),
            'min_jerk': min(jerks),
            'jerk_volatility': np.std(jerks),
            'risk_levels': {
                'volatility': recent.volatility_surge_risk,
                'liquidity': recent.liquidity_shock_risk,
                'cascade': recent.cascade_risk
            }
        }