#!/usr/bin/env python3
"""
CORE Flow Detection System - Enhanced Settings Configuration

All system settings with CSID optimization and swing trading parameters.
Enhanced configuration for institutional intelligence framework.
"""

from .constants import *

# SYSTEM CONFIGURATION - ENHANCED WITH CSID CAPABILITIES 
SYSTEM_CONFIG = {
    'version': '2.0.0',  # Enhanced with CSID optimization
    'name': 'CORE Mathematical Trading Intelligence System',
    'debug_mode': False,
    'parallel_processing': True,
    'max_workers': 4,
    'cache_enabled': False,
    'logging_level': 'INFO',
    
    # CSID SYSTEM ENHANCEMENT 
    'csid_optimization_enabled': True,
    'flow_physics_engine_enabled': True,
    'factor_confluence_system_enabled': True,
    'swing_trading_optimization_enabled': True,
    'institutional_intelligence_enabled': True,
    'pattern_recognition_enhanced': True,
    'mathematical_validation_strict': True,
    'unicode_compatibility_enforced': True,
    'type_safety_validation_enabled': True
}

# API CONFIGURATION - ENHANCED FOR REAL-TIME DATA 
API_CONFIG = {
    'polygon_api_key': None,  # Set via environment variable
    'base_url': 'https://api.polygon.io',
    'timeout_seconds': 30,
    'max_retries': 3,
    'retry_delay_seconds': 1,
    'rate_limit_delay_ms': 100,
    
    # ENHANCED DATA REQUIREMENTS 
    'order_book_data_enabled': True,
    'options_chain_data_enabled': True,
    'real_time_flow_data_enabled': True,
    'historical_data_depth_days': 252,  # 1 year for training
    'sub_second_latency_required': True,
    'institutional_flow_detection_enabled': True
}

# FLOW PHYSICS CONFIGURATION 
FLOW_PHYSICS_CONFIG = {
    'synthetic_data_enabled': True,  # For testing without real data
    'fallback_analysis_enabled': True,
    'flow_physics_integrator_enabled': False,  # Advanced modules optional
    'velocity_calculation_enabled': True,
    'acceleration_analysis_enabled': True,
    'jerk_detection_enabled': True,
    'institutional_bias_calculation_enabled': True,
    'regime_classification_enabled': True,
    'csid_pattern_recognition_enabled': True,
    'signal_decay_monitoring_enabled': True
}

# CSID OPTIMIZATION CONFIGURATION 
CSID_CONFIG = {
    'pattern_reliability_optimization': True,
    'signal_decay_analysis': True,
    'greeks_correlation_analysis': True,
    'flow_regime_alpha_optimization': True,
    'factor_confluence_system': True,
    'swing_trading_focus': True,
    'options_optimization': True,
    'risk_management_enhanced': True,
    'performance_monitoring': True,
    'backtesting_framework_enabled': True
}

# SWING TRADING CONFIGURATION 
SWING_TRADING_CONFIG = {
    'order_book_analysis_enabled': True,
    'bid_ask_imbalance_detection': True,
    'liquidity_sweep_detection': True,
    'depth_spike_analysis': True,
    'post_event_momentum_capture': True,
    'institutional_accumulation_detection': True,
    'atm_call_optimization': True,
    'theta_risk_management': True,
    'vix_regime_adaptation': True,
    'factor_confluence_decisions': True
}

# FACTOR CONFLUENCE CONFIGURATION 
FACTOR_CONFLUENCE_CONFIG = {
    'binary_decision_logic': True,
    'weighted_systems_disabled': True,
    'signal_strength_factor': True,
    'regime_alignment_factor': True,
    'greeks_alignment_factor': True,
    'timing_optimal_factor': True,
    'signal_uniqueness_factor': True,
    'confluence_statements_enabled': True,
    'decision_framework_operational': True
}

# ML TRAINING CONFIGURATION 
ML_TRAINING_CONFIG = {
    'training_data_collection_enabled': True,
    'synthetic_pattern_generation': True,
    'institutional_pattern_labeling': True,
    'flow_regime_classification_training': True,
    'factor_confluence_optimization': True,
    'swing_trading_pattern_recognition': True,
    'greeks_correlation_learning': True,
    'signal_decay_prediction_training': True,
    'cross_validation_enabled': True,
    'walk_forward_analysis_enabled': True
}

# PERFORMANCE MONITORING CONFIGURATION 
PERFORMANCE_CONFIG = {
    'execution_time_monitoring': True,
    'mathematical_accuracy_validation': True,
    'error_rate_tracking': True,
    'type_safety_monitoring': True,
    'unicode_compatibility_testing': True,
    'statistical_significance_validation': True,
    'correlation_accuracy_monitoring': True,
    'factor_confluence_performance_tracking': True,
    'swing_trading_metrics_collection': True,
    'institutional_detection_accuracy_monitoring': True
}

# RISK MANAGEMENT CONFIGURATION 
RISK_MANAGEMENT_CONFIG = {
    'position_sizing_enabled': True,
    'decay_adjusted_sizing': True,
    'theta_risk_monitoring': True,
    'volatility_regime_adaptation': True,
    'correlation_risk_management': True,
    'factor_confluence_risk_assessment': True,
    'institutional_flow_risk_analysis': True,
    'swing_trading_risk_controls': True,
    'mathematical_precision_enforcement': True,
    'error_free_execution_required': True
}

# VALIDATION CONFIGURATION 
VALIDATION_CONFIG = {
    'mathematical_validation_enabled': True,
    'statistical_significance_testing': True,
    'correlation_validation_enabled': True,
    'factor_confluence_validation': True,
    'swing_trading_parameter_validation': True,
    'flow_physics_validation': True,
    'type_safety_validation': True,
    'unicode_compatibility_validation': True,
    'error_handling_validation': True,
    'performance_benchmarking_enabled': True
}

# OUTPUT CONFIGURATION 
OUTPUT_CONFIG = {
    'flow_physics_output_enabled': True,
    'csid_analysis_output_enabled': True,
    'factor_confluence_reports_enabled': True,
    'swing_trading_signals_enabled': True,
    'institutional_intelligence_reports_enabled': True,
    'pattern_recognition_outputs_enabled': True,
    'performance_metrics_reporting_enabled': True,
    'training_data_export_enabled': True,
    'json_structured_output': True,
    'csv_export_enabled': True
}

# INTEGRATION CONFIGURATION 
INTEGRATION_CONFIG = {
    'ultimate_orchestrator_enabled': True,
    'multi_agent_coordination': True,
    'b_series_integration_ready': True,
    'a01_anomaly_detection_integration': True,
    'c02_iv_dynamics_integration': True,
    'f02_flow_physics_operational': True,
    'ml_model_integration_ready': True,
    'real_data_integration_prepared': True,
    'production_deployment_ready': True,
    'ai_training_pipeline_operational': True
}

# CONSOLIDATED CONFIGURATION DICTIONARIES 
ANALYZER_CONFIG = FLOW_PHYSICS_CONFIG
CONFLUENCE_CONFIG = FACTOR_CONFLUENCE_CONFIG
SIGNAL_CONFIG = SWING_TRADING_CONFIG
DATA_CONFIG = API_CONFIG
ML_CONFIG = ML_TRAINING_CONFIG
TESTING_CONFIG = VALIDATION_CONFIG

def get_config(section: str = None):
    """Get configuration section or all configurations"""
    configs = {
        'system': SYSTEM_CONFIG,
        'api': API_CONFIG,
        'flow_physics': FLOW_PHYSICS_CONFIG,
        'csid': CSID_CONFIG,
        'swing_trading': SWING_TRADING_CONFIG,
        'factor_confluence': FACTOR_CONFLUENCE_CONFIG,
        'ml_training': ML_TRAINING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'risk_management': RISK_MANAGEMENT_CONFIG,
        'validation': VALIDATION_CONFIG,
        'output': OUTPUT_CONFIG,
        'integration': INTEGRATION_CONFIG,
        'analyzer': ANALYZER_CONFIG,
        'confluence': CONFLUENCE_CONFIG,
        'signal': SIGNAL_CONFIG,
        'data': DATA_CONFIG,
        'ml': ML_CONFIG,
        'testing': TESTING_CONFIG
    }
    
    if section:
        return configs.get(section.lower(), {})
    return configs

def get_analyzer_config(analyzer_type: str = 'flow_physics'):
    """Get specific analyzer configuration"""
    analyzer_configs = {
        'flow_physics': FLOW_PHYSICS_CONFIG,
        'volume': SYSTEM_CONFIG,
        'liquidity': SYSTEM_CONFIG,
        'gex': SYSTEM_CONFIG
    }
    return analyzer_configs.get(analyzer_type, SYSTEM_CONFIG)

def update_config(section: str, updates: dict):
    """Update configuration section with new values"""
    config_map = {
        'system': SYSTEM_CONFIG,
        'api': API_CONFIG,
        'flow_physics': FLOW_PHYSICS_CONFIG,
        'csid': CSID_CONFIG,
        'swing_trading': SWING_TRADING_CONFIG,
        'factor_confluence': FACTOR_CONFLUENCE_CONFIG,
        'ml_training': ML_TRAINING_CONFIG,
        'performance': PERFORMANCE_CONFIG,
        'risk_management': RISK_MANAGEMENT_CONFIG,
        'validation': VALIDATION_CONFIG,
        'output': OUTPUT_CONFIG,
        'integration': INTEGRATION_CONFIG
    }
    
    if section.lower() in config_map:
        config_map[section.lower()].update(updates)
        return True
    return False
