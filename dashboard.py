#!/usr/bin/env python3
"""
Dashboard Launcher
Quick launcher for operational dashboards and monitoring
"""

import sys
import os
import webbrowser
from pathlib import Path

def launch_visual_monitor():
    """Launch visual monitoring interface"""
    print("=== CORE VISUAL MONITORING ===")
    print()
    
    # Check for recent visual reports
    visual_dir = Path("visual_output")
    if visual_dir.exists():
        html_files = list(visual_dir.glob("*.html"))
        if html_files:
            latest_report = max(html_files, key=lambda f: f.stat().st_mtime)
            print(f"Latest Visual Report: {latest_report.name}")
            
            # Open in browser
            try:
                webbrowser.open(f"file://{latest_report.absolute()}")
                print(f"Opening in browser: {latest_report}")
            except Exception as e:
                print(f"Could not open browser: {e}")
                print(f"Manual open: file://{latest_report.absolute()}")
        else:
            print("No visual reports found. Generate charts first:")
            print("  py trade.py AAPL --charts")
    else:
        print("Visual output directory not found. Generate charts first:")
        print("  py trade.py AAPL --charts")

def launch_schwab_monitor():
    """Launch Schwab quality monitor"""
    print("=== SCHWAB QUALITY MONITOR ===")
    print()
    
    try:
        from dashboards.schwab_monitor import SchwabQualityMonitor
        monitor = SchwabQualityMonitor()
        
        # Quick quality check
        tickers = ['SPY', 'QQQ', 'AAPL']
        print("Schwab Feed Quality Check:")
        for ticker in tickers:
            try:
                quality = monitor.calculate_quality_score(ticker)
                print(f"  {ticker}: Quality score available")
            except Exception as e:
                print(f"  {ticker}: Quality check failed - {e}")
        
        print("\nFor full dashboard, install streamlit and run:")
        print("  streamlit run dashboards/schwab_monitor.py")
        
    except ImportError as e:
        print(f"Schwab monitor not available: {e}")

def show_dashboard_menu():
    """Show dashboard options menu"""
    print("CORE DASHBOARD LAUNCHER")
    print("=" * 30)
    print()
    print("Available Dashboards:")
    print("  1. Visual Trading Reports")
    print("  2. Schwab Quality Monitor") 
    print("  3. System Status")
    print("  0. Exit")
    print()
    
    choice = input("Select dashboard (0-3): ").strip()
    
    if choice == "1":
        launch_visual_monitor()
    elif choice == "2":
        launch_schwab_monitor()
    elif choice == "3":
        show_system_status()
    elif choice == "0":
        print("Exiting dashboard launcher.")
    else:
        print("Invalid choice. Please select 0-3.")

def show_system_status():
    """Show basic system status"""
    print("=== SYSTEM STATUS ===")
    print()
    
    # Check core files
    core_files = [
        'trade.py',
        'ultimate_orchestrator.py', 
        'trading_visualizer.py',
        'enhanced_data_agent_broker_integration.py'
    ]
    
    print("Core System Files:")
    for file in core_files:
        if Path(file).exists():
            print(f"  OK {file}")
        else:
            print(f"  MISSING {file}")
    
    print()
    
    # Check visual output
    visual_dir = Path("visual_output")
    if visual_dir.exists():
        charts = list(visual_dir.glob("*.png"))
        reports = list(visual_dir.glob("*.html"))
        print(f"Visual Output: {len(charts)} charts, {len(reports)} reports")
    else:
        print("Visual Output: No output directory")
    
    print()
    print("Trading Commands:")
    print("  py trade.py AAPL          - Quick analysis")
    print("  py trade.py AAPL --charts - Analysis with visuals")
    print("  py trade.py --batch QQQ,AAPL,TSLA - Batch analysis")

def main():
    """Main dashboard launcher"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "visual":
            launch_visual_monitor()
        elif command == "schwab":
            launch_schwab_monitor()
        elif command == "status":
            show_system_status()
        else:
            print(f"Unknown command: {command}")
            print("Usage: py dashboard.py [visual|schwab|status]")
    else:
        show_dashboard_menu()

if __name__ == "__main__":
    main()
