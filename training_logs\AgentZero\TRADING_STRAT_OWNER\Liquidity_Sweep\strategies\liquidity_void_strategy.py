
"""
Liquidity Void Strategy (Revamped - v2)

Identifies and trades price movements through areas with minimal liquidity (LVN zones).
Uses direct Volume Profile calculation and simplified Flow Velocity for momentum.
Stop-Loss/Take-Profit are percentage-based.

Strategy Logic:
1. Calculate Volume Profile from historical data to find LVNs (voids) and HVNs (targets).
2. Monitor if current price is approaching a significant LVN (void).
3. Calculate Flow Velocity (price * volume trend) to confirm momentum into/through the void.
4. If momentum confirmed and price is at void edge, enter trade.
5. Position size can be scaled by velocity strength.
6. Target the next significant HVN.

PRODUCTION READY based on UnifiedAPIGateway (v13-fixed).
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import sys
import os
from pathlib import Path

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

# --- Standard Import for BaseStrategy ---
from strategies.base_strategy import BaseStrategy, StrategySignal, SignalDirection, SignalStrength

# --- API Gateway Import ---
try:
    from api_robustness.unified_api_gateway import get_api_gateway
    UNIFIED_GATEWAY_AVAILABLE_FOR_LVS = True
except ImportError:
    UNIFIED_GATEWAY_AVAILABLE_FOR_LVS = False
    logging.warning("UnifiedAPIGateway not available for LiquidityVoidStrategy")
    get_api_gateway = None
# --- End API Gateway Import ---
# --- End API Gateway Import ---

logger = logging.getLogger(__name__)

class LiquidityVoidStrategy(BaseStrategy):
    """
    Liquidity Void Strategy - Trade through areas with minimal liquidity.
    """

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for liquidity void strategy."""
        return {
            'enabled': True,
            'min_confidence': 0.65, # Adjusted from 0.70
            'max_signals_per_ticker': 1, # Focus on the best void opportunity

            'volume_profile_bins': 50, # Bins for VP calculation
            'analysis_lookback_bars': 100, # Bars for VP and primary analysis
            'primary_timeframe_polygon': 'minute', # Polygon timespan: minute, hour, day, week, month, quarter, year
            'primary_timeframe_multiplier': 15,    # e.g., 15 with 'minute' -> 15min bars

            'void_detection': {
                'min_void_size_pct_of_range': 0.02, # Min void size as 2% of lookback price range
                'max_void_distance_pct_from_price': 0.005, # Void edge within 0.5% of current price
                'lvn_volume_threshold_vs_mean': 0.3,    # Volume in LVN <= 30% of mean VP bin volume
            },

            'momentum_confirmation': {
                'min_flow_velocity_normalized': 0.3, # Min normalized flow velocity (0 to 1)
                'velocity_lookback_bars': 10,      # Bars for velocity calculation
                'confirmation_bars': 3,            # Bars for recent price/vol agreement
                'min_volume_increase_factor': 1.1, # Recent volume must be 1.1x avg
                'min_price_move_pct_confirm': 0.002, # 0.2% price move in momentum direction
            },

            'position_sizing': { # Scales the base risk amount or notional value
                'base_multiplier': 1.0,
                'velocity_scaling_enabled': True,
                'velocity_scale_map': { # Normalized_velocity_threshold: position_size_multiplier
                    0.3: 0.75, # Weak velocity, smaller size
                    0.5: 1.0,  # Moderate velocity, base size
                    0.7: 1.25, # Strong velocity, larger size
                }
            },

            'risk_management': { # Percentage-based
                'stop_loss_from_void_edge_pct': 0.003, # SL 0.3% beyond the other side of the void (or entry if inside)
                'target_hvn_buffer_pct': 0.002,     # Take profit 0.2% before HVN
                'min_rr_ratio': 1.5,
            },

            'exit_conditions': { # Not fully implemented in signal creation, for future enhancement
                'take_profit_at_first_hvn': True, # Take full profit at first HVN target
                'velocity_decay_exit_enabled': False, # Exit if velocity drops significantly
                'velocity_decay_threshold': 0.4,    # e.g., if norm_velocity drops below 0.4 of its peak during trade
            }
        }

    def __init__(self, config: Optional[Dict[str, Any]] = None, api_gateway_instance=None):
        super().__init__(config)

        if api_gateway_instance:
            self.api_gateway = api_gateway_instance
            logger.info(f"[{self.name}] Initialized with provided API Gateway.")
        elif UNIFIED_GATEWAY_AVAILABLE_FOR_LVS and get_api_gateway:
            try:
                self.api_gateway = get_api_gateway()
                logger.info(f"[{self.name}] Initialized its own API Gateway.")
            except Exception as e_init_gw:
                logger.critical(f"FATAL [{self.name}]: API Gateway init failed: {e_init_gw}", exc_info=True)
                raise RuntimeError(f"{self.name} requires API Gateway.")
        else:
            raise RuntimeError(f"{self.name} requires Unified API Gateway (module not found).")
        logger.info(f"{self.name} initialized successfully.")


    def analyze(self,
               ticker: str,
               data: Dict[str, Any], # Expects 'current_price', and optionally 'price_data'
               analysis_results: Dict[str, Any]) -> List[StrategySignal]:
        # analysis_results parameter is part of BaseStrategy interface but not used in this implementation

        if not self.is_enabled: return []

        is_valid, error_msg = self.validate_data(data)
        if not is_valid:
            logger.warning(f"[{ticker}] Invalid data for LVS: {error_msg}")
            return []

        current_price = data.get('current_price')
        if current_price is None or pd.isna(current_price) or current_price <= 0:
            logger.warning(f"[{ticker}] LVS: Invalid current_price. Aborting.")
            return []

        # Get historical price data for analysis (VP, velocity)
        price_data_df = self._get_historical_price_data(ticker, data)
        if price_data_df is None or price_data_df.empty or \
           len(price_data_df) < self.config['analysis_lookback_bars']:
            logger.warning(f"[{ticker}] LVS: Insufficient historical price data ({len(price_data_df) if price_data_df is not None else 0} bars).")
            return []

        logger.debug(f"[{ticker}] LVS Analysis Start. Price: ${current_price:.2f}. Data: {len(price_data_df)} bars.")
        signals: List[StrategySignal] = []

        try:
            # 1. Identify liquidity voids using volume profile
            #    HVNs are also identified here to be used as targets.
            voids, hvn_levels = self._identify_liquidity_voids_and_hvns(ticker, price_data_df)
            if not voids:
                logger.debug(f"[{ticker}] LVS: No liquidity voids found.")
                return []

            # 2. Check for price approaching or inside void zones
            active_void_scenarios = self._filter_active_void_scenarios(current_price, voids, hvn_levels)
            if not active_void_scenarios:
                logger.debug(f"[{ticker}] LVS: Price not actively interacting with any identified voids.")
                return []

            # 3. Analyze momentum for each active void scenario
            for void_scenario in active_void_scenarios:
                # `void_scenario` includes: `void_data`, `direction_to_trade`, `entry_price`, `target_hvn`

                # Calculate flow velocity using price_data_df
                flow_velocity_norm, flow_velocity_raw = self._calculate_flow_velocity(price_data_df)

                if flow_velocity_norm < self.config['momentum_confirmation']['min_flow_velocity_normalized']:
                    logger.debug(f"[{ticker}] LVS: Flow velocity {flow_velocity_norm:.2f} (norm) too low for void {void_scenario['void_data']['start_price']:.2f}-{void_scenario['void_data']['end_price']:.2f}.")
                    continue

                # Confirm momentum aligns with the direction to trade the void
                # e.g., if void is above and we need to trade LONG, velocity should be positive.
                required_velocity_sign = 1 if void_scenario['direction_to_trade'] == SignalDirection.LONG else -1
                if np.sign(flow_velocity_raw) != required_velocity_sign and flow_velocity_raw !=0:
                    logger.debug(f"[{ticker}] LVS: Flow velocity direction ({np.sign(flow_velocity_raw)}) "
                                 f"misaligned with trade direction ({required_velocity_sign}) for void.")
                    continue

                momentum_confirmed_by_recent_action = self._confirm_recent_momentum(price_data_df, required_velocity_sign)
                if not momentum_confirmed_by_recent_action:
                    logger.debug(f"[{ticker}] LVS: Recent price/volume action does not confirm momentum for void.")
                    continue

                # 4. Calculate position sizing based on velocity
                pos_size_multiplier = self._calculate_position_size_multiplier(flow_velocity_norm)

                # 5. Create the signal
                signal = self._create_void_trade_signal(
                    ticker, current_price, void_scenario,
                    flow_velocity_norm, pos_size_multiplier
                )
                if signal:
                    signals.append(signal)

            logger.info(f"[{ticker}] LVS: Generated {len(signals)} signals.")
            return self.filter_signals(signals)

        except Exception as e:
            logger.error(f"[{ticker}] Error in LVS analyze: {e}", exc_info=True)
            return []

    def _get_historical_price_data(self, ticker: str, data: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """
        Gets historical price data. Prefers data passed in `data['price_data'][timeframe_key]`,
        then tries to fetch from API gateway.
        """
        # Construct the timeframe key used by this strategy's config
        # Example: if primary_timeframe_polygon='minute' and primary_timeframe_multiplier=15, key is '15m'
        # For simplicity, we'll assume a direct mapping or that the gateway can handle Polygon's timespan/multiplier.

        # Try to get from `data` if already fetched by a master strategy
        # Assume price_data in `data` is already the correct timeframe if present
        if 'price_data' in data and isinstance(data['price_data'], pd.DataFrame) and not data['price_data'].empty:
            df = data['price_data']
            if len(df) >= self.config['analysis_lookback_bars']:
                 logger.debug(f"[{ticker}] Using provided price_data ({len(df)} bars) for LVS.")
                 # Ensure it has necessary columns
                 if all(col in df.columns for col in ['open', 'high', 'low', 'close', 'volume']):
                    if not isinstance(df.index, pd.DatetimeIndex) and 'timestamp' in df.columns:
                        df = df.set_index(pd.to_datetime(df['timestamp'])).sort_index()
                    elif not isinstance(df.index, pd.DatetimeIndex):
                        logger.warning(f"[{ticker}] Provided price_data has no DatetimeIndex or 'timestamp' column.")
                        return None
                    return df.copy() # Return a copy

        # Fallback: fetch from API
        if not self.api_gateway:
            logger.warning(f"[{ticker}] No API gateway to fetch historical price data for LVS.")
            return None
        try:
            poly_timespan = self.config['primary_timeframe_polygon']
            poly_multiplier = self.config['primary_timeframe_multiplier']
            lookback_bars = self.config['analysis_lookback_bars']

            # Calculate from_date based on bars and timespan/multiplier with proper calendar math
            now = datetime.now(datetime.timezone.utc)
            if poly_timespan == 'minute':
                # Account for market hours: [PARTIAL]6.5 hours trading day, 5 days per week
                trading_minutes_per_day = 6.5 * 60  # 390 minutes
                calendar_days_needed = (poly_multiplier * lookback_bars) / trading_minutes_per_day
                # Account for weekends: multiply by 7/5
                calendar_days_with_weekends = calendar_days_needed * (7/5)
                delta_days = timedelta(days=calendar_days_with_weekends * 1.2)  # 20% buffer
            elif poly_timespan == 'hour':
                trading_hours_per_day = 6.5
                calendar_days_needed = (poly_multiplier * lookback_bars) / trading_hours_per_day
                calendar_days_with_weekends = calendar_days_needed * (7/5)
                delta_days = timedelta(days=calendar_days_with_weekends * 1.2)
            elif poly_timespan == 'day':
                # 5 trading days per week
                calendar_days_needed = poly_multiplier * lookback_bars * (7/5)
                delta_days = timedelta(days=calendar_days_needed * 1.2)
            else: # week, month etc.
                # More precise estimation for longer timeframes
                if poly_timespan == 'week':
                    delta_days = timedelta(weeks=poly_multiplier * lookback_bars * 1.2)
                elif poly_timespan == 'month':
                    delta_days = timedelta(days=poly_multiplier * lookback_bars * 30 * 1.2)
                else:
                    delta_days = timedelta(days=poly_multiplier * lookback_bars * 7 * 1.2)

            from_date_str = (now - delta_days).strftime('%Y-%m-%d')
            to_date_str = now.strftime('%Y-%m-%d')

            logger.debug(f"[{ticker}] Fetching LVS price data: {poly_multiplier}{poly_timespan} from {from_date_str} to {to_date_str}")

            price_df = self.api_gateway.get_price_data(
                ticker=ticker,
                timespan=poly_timespan,
                multiplier=poly_multiplier,
                from_date=from_date_str,
                to_date=to_date_str
            )
            if price_df is not None and not price_df.empty:
                # Ensure it has a datetime index and required columns
                if not isinstance(price_df.index, pd.DatetimeIndex) and 'timestamp' in price_df.columns:
                    price_df = price_df.set_index(pd.to_datetime(price_df['timestamp'])).sort_index()
                elif not isinstance(price_df.index, pd.DatetimeIndex):
                    logger.error(f"[{ticker}] Fetched price_df has no usable DatetimeIndex.")
                    return None

                if not all(col in price_df.columns for col in ['open','high','low','close','volume']):
                    logger.error(f"[{ticker}] Fetched price_df missing OHLCV columns.")
                    return None

                # Return the most recent 'analysis_lookback_bars'
                return price_df.tail(lookback_bars).copy()
            return None

        except Exception as e:
            logger.error(f"[{ticker}] Failed to fetch/process price data for LVS: {e}", exc_info=True)
            return None

    def _identify_liquidity_voids_and_hvns(self, ticker: str, price_data_df: pd.DataFrame) -> Tuple[List[Dict[str, Any]], List[float]]:
        """
        Identifies liquidity voids (LVNs) and High Volume Nodes (HVNs).
        Returns: Tuple (list_of_void_dicts, list_of_hvn_price_levels)
        """
        voids: List[Dict[str, Any]] = []
        hvn_price_levels: List[float] = []
        cfg_vp_bins = self.config['volume_profile_bins']
        cfg_void = self.config['void_detection']

        try:
            # 1. Calculate Volume Profile (integrated from HardLiquidityStrategy)
            price_min_range, price_max_range = price_data_df['low'].min(), price_data_df['high'].max()
            if pd.isna(price_min_range) or pd.isna(price_max_range) or price_min_range == price_max_range:
                return voids, hvn_price_levels

            price_bins = np.linspace(price_min_range, price_max_range, cfg_vp_bins + 1)
            volume_at_price = np.zeros(cfg_vp_bins)
            bin_centers = (price_bins[:-1] + price_bins[1:]) / 2

            for _, bar in price_data_df.iterrows():
                bar_low, bar_high, bar_vol = bar['low'], bar['high'], bar['volume']
                if bar_vol == 0: continue

                low_idx = np.searchsorted(price_bins, bar_low, side='right') - 1
                high_idx = np.searchsorted(price_bins, bar_high, side='left')
                low_idx, high_idx = np.clip(low_idx, 0, cfg_vp_bins - 1), np.clip(high_idx, 0, cfg_vp_bins - 1)
                if low_idx > high_idx: continue

                relevant_indices = range(low_idx, high_idx + 1)
                num_relevant = len(relevant_indices)
                if num_relevant > 0:
                    vol_per_segment = bar_vol / num_relevant
                    for i_bin in relevant_indices:
                        volume_at_price[i_bin] += vol_per_segment

            if np.sum(volume_at_price) == 0: return voids, hvn_price_levels

            mean_vol_per_bin = np.mean(volume_at_price[volume_at_price > 0])

            # Identify HVNs
            hvn_cutoff = mean_vol_per_bin * 1.5 # Example: volume > 1.5x mean
            for i, vol in enumerate(volume_at_price):
                if vol >= hvn_cutoff:
                    hvn_price_levels.append(bin_centers[i])
            hvn_price_levels = sorted(list(set(hvn_price_levels))) # Unique sorted HVNs

            # Identify LVNs for Voids
            lvn_volume_max = mean_vol_per_bin * cfg_void['lvn_volume_threshold_vs_mean']

            current_void_start_idx = -1
            for i, vol_in_bin in enumerate(volume_at_price):
                if vol_in_bin <= lvn_volume_max: # This bin is part of an LVN
                    if current_void_start_idx == -1:
                        current_void_start_idx = i
                else: # This bin is NOT part of an LVN
                    if current_void_start_idx != -1: # A void just ended
                        void_end_idx = i - 1
                        # Check if this void is significant
                        if void_end_idx >= current_void_start_idx :
                            void_start_price = bin_centers[current_void_start_idx]
                            void_end_price = bin_centers[void_end_idx]
                            # Ensure start is lower than end
                            if void_start_price > void_end_price:
                                void_start_price, void_end_price = void_end_price, void_start_price

                            void_size_actual = void_end_price - void_start_price
                            min_void_size_needed = (price_max_range - price_min_range) * cfg_void['min_void_size_pct_of_range']

                            if void_size_actual >= min_void_size_needed:
                                void_volumes = volume_at_price[current_void_start_idx : void_end_idx + 1]
                                voids.append({
                                    'start_price': void_start_price,
                                    'end_price': void_end_price,
                                    'size': void_size_actual,
                                    'avg_volume_in_void': np.mean(void_volumes) if len(void_volumes) > 0 else 0,
                                    'num_bins_in_void': (void_end_idx - current_void_start_idx + 1)
                                })
                        current_void_start_idx = -1 # Reset for next void

            # Check for a void that ends at the last bin
            if current_void_start_idx != -1:
                void_end_idx = cfg_vp_bins - 1
                if void_end_idx >= current_void_start_idx:
                    void_start_price = bin_centers[current_void_start_idx]
                    void_end_price = bin_centers[void_end_idx]
                    if void_start_price > void_end_price: void_start_price, void_end_price = void_end_price, void_start_price
                    void_size_actual = void_end_price - void_start_price
                    min_void_size_needed = (price_max_range - price_min_range) * cfg_void['min_void_size_pct_of_range']
                    if void_size_actual >= min_void_size_needed:
                        void_volumes = volume_at_price[current_void_start_idx : void_end_idx + 1]
                        voids.append({
                            'start_price': void_start_price, 'end_price': void_end_price, 'size': void_size_actual,
                            'avg_volume_in_void': np.mean(void_volumes) if len(void_volumes) > 0 else 0,
                            'num_bins_in_void': (void_end_idx - current_void_start_idx + 1)
                        })
            logger.debug(f"[{ticker}] Identified {len(voids)} potential voids and {len(hvn_price_levels)} HVN levels.")

        except Exception as e:
            logger.error(f"[{ticker}] Error identifying liquidity voids/HVNs: {e}", exc_info=True)
        return voids, hvn_price_levels


    def _filter_active_void_scenarios(self, current_price: float, voids: List[Dict[str, Any]], hvn_levels: List[float]) -> List[Dict[str, Any]]:
        """Filters voids that price is actively interacting with and determines potential trade."""
        active_scenarios = []
        cfg_void = self.config['void_detection']
        max_dist_abs = current_price * cfg_void['max_void_distance_pct_from_price']

        for void in voids:
            void_start, void_end = void['start_price'], void['end_price']
            scenario = None

            # Price is approaching void from below
            if current_price < void_start and (void_start - current_price) <= max_dist_abs:
                # Target HVNs above the void
                target_hvns_above = sorted([hvn for hvn in hvn_levels if hvn > void_end])
                if target_hvns_above:
                    scenario = {
                        'void_data': void, 'direction_to_trade': SignalDirection.LONG,
                        'entry_price': void_start, # Enter at void bottom edge
                        'target_hvn': target_hvns_above[0], # First HVN above
                        'stop_loss_ref_price': void_start # SL based on entry at void edge
                    }
            # Price is approaching void from above
            elif current_price > void_end and (current_price - void_end) <= max_dist_abs:
                # Target HVNs below the void
                target_hvns_below = sorted([hvn for hvn in hvn_levels if hvn < void_start], reverse=True)
                if target_hvns_below:
                    scenario = {
                        'void_data': void, 'direction_to_trade': SignalDirection.SHORT,
                        'entry_price': void_end, # Enter at void top edge
                        'target_hvn': target_hvns_below[0], # First HVN below
                        'stop_loss_ref_price': void_end
                    }
            # Price is INSIDE the void
            elif void_start <= current_price <= void_end:
                # Determine likely direction based on proximity to edges and available targets
                dist_to_top_edge = void_end - current_price
                dist_to_bottom_edge = current_price - void_start

                target_hvns_above = sorted([hvn for hvn in hvn_levels if hvn > void_end])
                target_hvns_below = sorted([hvn for hvn in hvn_levels if hvn < void_start], reverse=True)

                # Tentatively favor direction with closer edge and available target
                # Momentum will ultimately confirm this
                if dist_to_bottom_edge < dist_to_top_edge and target_hvns_above: # Closer to bottom, potential long
                     scenario = {
                        'void_data': void, 'direction_to_trade': SignalDirection.LONG,
                        'entry_price': current_price, # Enter at current price inside void
                        'target_hvn': target_hvns_above[0],
                        'stop_loss_ref_price': void_start # SL beyond the other side of void
                    }
                elif dist_to_top_edge < dist_to_bottom_edge and target_hvns_below: # Closer to top, potential short
                     scenario = {
                        'void_data': void, 'direction_to_trade': SignalDirection.SHORT,
                        'entry_price': current_price,
                        'target_hvn': target_hvns_below[0],
                        'stop_loss_ref_price': void_end # SL beyond the other side of void
                    }
                # If equidistant or one direction has no target, might need more rules or rely purely on momentum
                elif target_hvns_above : # Default to long if above targets exist
                     scenario = {
                        'void_data': void, 'direction_to_trade': SignalDirection.LONG,
                        'entry_price': current_price, 'target_hvn': target_hvns_above[0],
                        'stop_loss_ref_price': void_start
                    }
                elif target_hvns_below : # Default to short if below targets exist
                     scenario = {
                        'void_data': void, 'direction_to_trade': SignalDirection.SHORT,
                        'entry_price': current_price, 'target_hvn': target_hvns_below[0],
                        'stop_loss_ref_price': void_end
                    }


            if scenario:
                active_scenarios.append(scenario)

        logger.debug(f"Filtered to {len(active_scenarios)} active void scenarios.")
        return active_scenarios

    def _calculate_flow_velocity(self, price_data_df: pd.DataFrame) -> Tuple[float, float]:
        """
        Calculates a simplified flow velocity and its normalized value.
        Returns: (normalized_velocity, raw_velocity)
        Normalized velocity is a value between -1 and 1.
        Raw velocity is the actual rate of change of flow_value.
        """
        cfg_mom = self.config['momentum_confirmation']
        lookback = cfg_mom['velocity_lookback_bars']
        if len(price_data_df) < lookback:
            return 0.0, 0.0

        recent_data = price_data_df.tail(lookback).copy()
        if not all(c in recent_data for c in ['close', 'volume', 'timestamp']): # Check for timestamp if not index
             if isinstance(recent_data.index, pd.DatetimeIndex):
                 recent_data['timestamp'] = recent_data.index.astype(np.int64) // 10**9 # Seconds
             else:
                logger.warning("Flow velocity calc: Missing timestamp for derivative.")
                return 0.0,0.0


        recent_data['flow_value'] = recent_data['close'] * recent_data['volume']

        # Calculate rate of change of flow_value (raw velocity)
        # Using numpy.gradient for smoother derivative over the lookback period
        flow_values = recent_data['flow_value'].values
        timestamps_sec = recent_data['timestamp'].values # Assuming already in seconds or convertible

        if len(flow_values) < 2: return 0.0, 0.0

        try:
            # Ensure timestamps are numeric seconds for gradient
            if not pd.api.types.is_numeric_dtype(timestamps_sec):
                 if isinstance(timestamps_sec[0], (datetime, pd.Timestamp)):
                    timestamps_sec = np.array([ts.timestamp() for ts in timestamps_sec])
                 else: # Try to convert if strings etc.
                    timestamps_sec = pd.to_datetime(timestamps_sec).astype(np.int64) // 10**9


            velocities = np.gradient(flow_values, timestamps_sec)
            raw_velocity = velocities[-1] # Most recent velocity
        except Exception as e_grad:
            logger.error(f"Error in np.gradient for velocity: {e_grad}. Falling back to simple diff.")
            # Fallback simple diff if gradient fails
            if len(flow_values) >=2 and (timestamps_sec[-1] - timestamps_sec[-2]) > 0 :
                raw_velocity = (flow_values[-1] - flow_values[-2]) / (timestamps_sec[-1] - timestamps_sec[-2])
            else:
                raw_velocity = 0.0


        # Normalize raw_velocity: compare it to a typical range of velocities for this stock
        # A simple normalization: divide by average *absolute* flow value over the period
        # This makes it somewhat adaptive to different stocks' typical flow magnitudes.
        avg_abs_flow = np.mean(np.abs(flow_values))
        if avg_abs_flow > 0:
            # Normalized velocity essentially becomes % change of avg flow per second
            # This needs a scaling factor to bring it to a sensible -1 to 1 range for typical % changes
            # Example: if typical flow is $1M, and velocity is $10k/sec, norm_vel_unscaled = 0.01
            # We need to define what constitutes "strong" normalized velocity.
            # Let's say 10% of average flow per time unit is "extreme" (norm_vel = 1.0)
            # The time unit is implicitly seconds here if timestamps are in seconds.
            # This needs careful thought or use a fixed threshold based on typical market observations.
            # For now, let's use a simpler normalization based on observed range of velocities
            if len(velocities) > 1 :
                max_abs_observed_velocity = np.percentile(np.abs(velocities), 95) # 95th percentile of observed velocities
                if max_abs_observed_velocity > 0:
                    normalized_velocity = raw_velocity / max_abs_observed_velocity
                    normalized_velocity = np.clip(normalized_velocity, -1.0, 1.0) # Clip to [-1, 1]
                else: normalized_velocity = 0.0
            else: normalized_velocity = 0.0
        else:
            normalized_velocity = 0.0

        return normalized_velocity, raw_velocity

    def _confirm_recent_momentum(self, price_data_df: pd.DataFrame, required_price_direction_sign: int) -> bool:
        """Confirms momentum based on recent price action and volume."""
        cfg_mom = self.config['momentum_confirmation']
        num_bars = cfg_mom['confirmation_bars']
        if len(price_data_df) < num_bars + 1: # Need one more for avg_vol calculation base
            return False

        recent_bars_df = price_data_df.tail(num_bars)

        # Price Confirmation
        price_change = recent_bars_df['close'].iloc[-1] - recent_bars_df['close'].iloc[0]
        price_start = recent_bars_df['close'].iloc[0]
        price_move_pct = (price_change / price_start) if price_start > 0 else 0

        price_agrees_direction = np.sign(price_change) == required_price_direction_sign
        price_move_significant = abs(price_move_pct) >= cfg_mom['min_price_move_pct_confirm']

        # Volume Confirmation
        lookback_for_avg_vol = max(20, num_bars + 5) # Lookback for baseline average volume
        if len(price_data_df) < lookback_for_avg_vol : return False # Not enough data for avg vol

        avg_volume_baseline = price_data_df['volume'].iloc[-lookback_for_avg_vol:-num_bars].mean()
        recent_avg_volume = recent_bars_df['volume'].mean()

        volume_supports = (recent_avg_volume >= avg_volume_baseline * cfg_mom['min_volume_increase_factor']) \
                          if avg_volume_baseline > 0 else True # If no baseline vol, assume true

        if price_agrees_direction and price_move_significant and volume_supports:
            logger.debug(f"Momentum confirmed: Price move {price_move_pct*100:.2f}%, Volume recent_avg/baseline_avg = {recent_avg_volume/(avg_volume_baseline if avg_volume_baseline > 0 else 1):.2f}")
            return True

        logger.debug(f"Momentum NOT confirmed: PriceOk={price_agrees_direction and price_move_significant} (Move:{price_move_pct*100:.2f}%), VolOk={volume_supports}")
        return False


    def _calculate_position_size_multiplier(self, normalized_flow_velocity: float) -> float:
        """Calculates position size multiplier based on normalized flow velocity."""
        cfg_ps = self.config['position_sizing']
        if not cfg_ps.get('velocity_scaling_enabled', False):
            return cfg_ps['base_multiplier']

        abs_norm_vel = abs(normalized_flow_velocity)
        scale_map = cfg_ps['velocity_scale_map']

        # Iterate from strongest to weakest velocity defined in map
        for vel_thresh_str, size_mult in sorted(scale_map.items(), key=lambda item: float(item[0]), reverse=True):
            vel_thresh = float(vel_thresh_str)
            if abs_norm_vel >= vel_thresh:
                return cfg_ps['base_multiplier'] * size_mult

        # If below the lowest threshold in map, use a default minimum or the multiplier for the lowest threshold
        min_map_thresh = min(float(k) for k in scale_map.keys())
        return cfg_ps['base_multiplier'] * scale_map.get(str(min_map_thresh), 0.5) # Default to 0.5 if map is weird


    def _create_void_trade_signal(self,
                                ticker: str,
                                current_price_at_analysis: float, # Price when analysis was run
                                void_scenario: Dict[str, Any],
                                flow_velocity_norm: float,
                                pos_size_multiplier: float
                                ) -> Optional[StrategySignal]:
        """Creates a StrategySignal object for a liquidity void trade."""

        direction_enum = void_scenario['direction_to_trade']
        entry_price = void_scenario['entry_price'] # This is the void edge or current price if inside
        target_hvn_price = void_scenario['target_hvn']
        void_data = void_scenario['void_data']
        sl_ref_price = void_scenario['stop_loss_ref_price'] # Price to base SL % from (other side of void)

        cfg_risk = self.config['risk_management']

        # Stop Loss: X% beyond the OTHER side of the void from entry, or from entry if inside void and SL ref is that edge
        sl_pct_from_ref = cfg_risk['stop_loss_from_void_edge_pct']
        if direction_enum == SignalDirection.LONG:
            # Entry is at void_start or current_price (if inside, closer to void_start).
            # SL reference is void_start if entering at void_start, or other side (void_start) if already inside.
            stop_loss = sl_ref_price * (1 - sl_pct_from_ref)
        else: # SHORT
            # Entry is at void_end or current_price (if inside, closer to void_end).
            # SL reference is void_end.
            stop_loss = sl_ref_price * (1 + sl_pct_from_ref)

        # Ensure SL is actually creating risk relative to intended entry_price at void edge
        # If current_price is used as entry (inside void), SL is beyond the opposite edge.
        # If entry_price is void edge, SL is beyond that edge.
        # The logic above should set SL beyond the "safe" side of the void.
        # Now, ensure it's valid relative to the actual current_price (potential fill price)
        if direction_enum == SignalDirection.LONG and current_price_at_analysis <= stop_loss:
            logger.warning(f"[{ticker}] Long SL ${stop_loss:.2f} is >= current price ${current_price_at_analysis:.2f}. Adjusting.")
            stop_loss = current_price_at_analysis * (1 - self.config['risk_management']['stop_loss_from_void_edge_pct'] * 0.5) # Fallback tighter SL
        elif direction_enum == SignalDirection.SHORT and current_price_at_analysis >= stop_loss:
            logger.warning(f"[{ticker}] Short SL ${stop_loss:.2f} is <= current price ${current_price_at_analysis:.2f}. Adjusting.")
            stop_loss = current_price_at_analysis * (1 + self.config['risk_management']['stop_loss_from_void_edge_pct'] * 0.5)

        risk_per_share = abs(entry_price - stop_loss) # Risk from planned entry at void edge
        if risk_per_share == 0 : # Should not happen if sl_pct is > 0
            logger.warning(f"[{ticker}] Zero risk calculated for void trade. Setting default risk.")
            risk_per_share = entry_price * 0.01


        # Take Profit: At target HVN, with a buffer
        tp_buffer_abs = target_hvn_price * cfg_risk['target_hvn_buffer_pct']
        take_profit = target_hvn_price - tp_buffer_abs if direction_enum == SignalDirection.LONG else target_hvn_price + tp_buffer_abs

        # Validate R:R
        potential_reward = abs(take_profit - entry_price)
        if risk_per_share > 0 and (potential_reward / risk_per_share) < cfg_risk['min_rr_ratio']:
            logger.info(f"[{ticker}] Void trade scenario skipped. R:R {potential_reward/risk_per_share:.2f} < min {cfg_risk['min_rr_ratio']:.2f}. "
                        f"Entry ${entry_price:.2f}, SL ${stop_loss:.2f}, TP ${take_profit:.2f}, TargetHVN ${target_hvn_price:.2f}")
            return None

        # Confidence calculation
        # Higher for larger voids, stronger velocity, closer proximity of price to void entry
        void_size_pct_of_price = (void_data['size'] / current_price_at_analysis) * 100
        dist_to_entry_pct = abs(current_price_at_analysis - entry_price) / current_price_at_analysis if entry_price != current_price_at_analysis else 0

        confidence = self.config['min_confidence'] # Start with base
        confidence += min(0.15, flow_velocity_norm * 0.2) # Velocity contribution
        confidence += min(0.10, void_size_pct_of_price * 0.02)    # Void size contribution (e.g., 5% void = 0.1)
        if dist_to_entry_pct < 0.001 : confidence += 0.05 # Bonus if very close to entry edge
        confidence = min(0.95, confidence) # Cap

        if confidence < self.config['min_confidence']:
            logger.debug(f"[{ticker}] Void trade scenario confidence {confidence:.2f} < min {self.config['min_confidence']:.2f}.")
            return None

        strength_enum = SignalStrength.LOW
        if confidence >= 0.80: strength_enum = SignalStrength.HIGH
        elif confidence >= 0.70: strength_enum = SignalStrength.MEDIUM

        reason = (f"LVS {direction_enum.value}: Enter void ({void_data['start_price']:.2f}-{void_data['end_price']:.2f}), "
                  f"Target HVN ${target_hvn_price:.2f}. NormVel: {flow_velocity_norm:.2f}.")

        analysis_payload = {
            'component': 'LiquidityVoid',
            'void_details': void_data,
            'target_hvn': target_hvn_price,
            'entry_trigger_price': entry_price, # Price at which to consider entry (void edge)
            'normalized_flow_velocity': flow_velocity_norm,
            'position_size_multiplier': pos_size_multiplier,
            'stop_loss_ref_price': sl_ref_price,
            'calculated_risk_per_share': risk_per_share
        }

        # Note: The actual entry for the signal is `current_price_at_analysis` if already inside void,
        # or `entry_price` (void edge) if approaching. BaseStrategy `create_signal` uses its `entry` arg.
        # For this strategy, if price is approaching, `entry_price` (void edge) is the condition.
        # If already inside, `current_price_at_analysis` is the entry.
        # The `BaseStrategy.create_signal` uses `entry` as the signal's entry price.
        # Let's assume the signal is generated *when current price allows entry*.
        # If approaching, signal entry is the void edge. If inside, signal entry is current price.
        void_start = void_data['start_price']
        void_end = void_data['end_price']
        signal_entry_price = current_price_at_analysis if void_start <= current_price_at_analysis <= void_end else entry_price

        return self.create_signal(
            ticker=ticker, direction=direction_enum, strength=strength_enum,
            entry=signal_entry_price, # This is where the signal becomes active
            stop_loss=round(stop_loss, 2),
            take_profit=[round(take_profit, 2)], # Single TP for now
            confidence=confidence, reason=reason, analysis=analysis_payload
        )

    def validate_data(self, data: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate required data for liquidity void analysis."""
        if 'current_price' not in data or pd.isna(data['current_price']) or data['current_price'] <=0:
            return False, "Missing or invalid 'current_price'."
        # Historical data check is done in _get_historical_price_data
        return True, ""

# --- End of LiquidityVoidStrategy Class ---

# Helper methods from HardLiquidityStrategy that are needed by LVS if not already in BaseStrategy
# For Volume Profile, Market Regime.
# For now, LVS implements its own _calculate_volume_profile and _determine_market_regime (simplified)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - [%(levelname)s] - %(name)s - %(message)s')

    if not UNIFIED_GATEWAY_AVAILABLE_FOR_LVS or get_api_gateway is None:
        logger.critical("Standalone LVS test cannot run: Unified API Gateway not available.")
        exit()

    lvs_strategy = LiquidityVoidStrategy()
    logger.info(f"Standalone LVS Test: Strategy '{lvs_strategy.name}' initialized. Min Confidence: {lvs_strategy.config['min_confidence']}")

    test_tickers = ['AAPL', 'MSFT', 'NVDA']
    gateway_for_test = get_api_gateway()

    for ticker_symbol in test_tickers:
        logger.info(f"\n{'='*25} TESTING LVS FOR {ticker_symbol} {'='*25}")
        current_spot = gateway_for_test.get_spot_price(ticker_symbol)
        if current_spot is None or pd.isna(current_spot) or current_spot <= 0:
            logger.warning(f"Could not get current spot price for {ticker_symbol}. Skipping.")
            continue

        # Data for LVS: current_price. Historical data will be fetched by _get_historical_price_data
        data_for_lvs = {'current_price': current_spot}

        # To pre-fetch data and pass it (optional, _get_historical_price_data will fetch if not provided)
        # This demonstrates passing pre-fetched data if available from a master strategy or orchestrator
        poly_timespan_main = lvs_strategy.config['primary_timeframe_polygon']
        poly_multiplier_main = lvs_strategy.config['primary_timeframe_multiplier']
        lookback_bars_main = lvs_strategy.config['analysis_lookback_bars']
        now_main = datetime.now(datetime.timezone.utc)
        if poly_timespan_main == 'minute': delta_days_main = timedelta(minutes=poly_multiplier_main * lookback_bars_main * 1.8) # More buffer
        elif poly_timespan_main == 'hour': delta_days_main = timedelta(hours=poly_multiplier_main * lookback_bars_main * 1.8)
        else: delta_days_main = timedelta(days=poly_multiplier_main * lookback_bars_main * 1.8)
        from_date_main_str = (now_main - delta_days_main).strftime('%Y-%m-%d')
        to_date_main_str = now_main.strftime('%Y-%m-%d')

        fetched_price_data = gateway_for_test.get_price_data(
            ticker=ticker_symbol,
            timespan=poly_timespan_main,
            multiplier=poly_multiplier_main,
            from_date=from_date_main_str,
            to_date=to_date_main_str
        )
        if fetched_price_data is not None and not fetched_price_data.empty:
            if not isinstance(fetched_price_data.index, pd.DatetimeIndex) and 'timestamp' in fetched_price_data.columns:
                fetched_price_data = fetched_price_data.set_index(pd.to_datetime(fetched_price_data['timestamp'])).sort_index()
            if isinstance(fetched_price_data.index, pd.DatetimeIndex) and \
               all(col in fetched_price_data.columns for col in ['open','high','low','close','volume']):
                data_for_lvs['price_data'] = fetched_price_data.tail(lookback_bars_main) # Pass only needed lookback
            else:
                logger.warning(f"[{ticker_symbol}] Fetched data for main() test is malformed, LVS will try to fetch internally.")
        else:
            logger.warning(f"[{ticker_symbol}] Could not pre-fetch data for main() test, LVS will try to fetch internally.")


        generated_signals = lvs_strategy.analyze(ticker_symbol, data_for_lvs, {})

        if generated_signals:
            logger.info(f"Found {len(generated_signals)} signals for {ticker_symbol}:")
            for idx, sig in enumerate(generated_signals):
                logger.info(f"  Signal {idx+1}: {sig.direction.value} {sig.strength.value} (Conf: {sig.confidence:.2f}) @ Entry: ${sig.entry:.2f}")
                logger.info(f"     SL: ${sig.stop_loss:.2f}, TP: {[f'${tp:.2f}' for tp in sig.take_profit]}")
                logger.info(f"     Reason: {sig.reason}")
                if sig.analysis:
                     logger.info(f"     Analysis Component: {sig.analysis.get('component','N/A')}")
                     if 'void_details' in sig.analysis:
                        logger.info(f"       Void: ${sig.analysis['void_details']['start_price']:.2f}-${sig.analysis['void_details']['end_price']:.2f} "
                                    f"Target HVN: ${sig.analysis.get('target_hvn',0):.2f}")
        else:
            logger.info(f"No signals generated by LVS for {ticker_symbol} with current data/config.")

    logger.info(f"\n{'-'*20} LVS Standalone Test Complete {'-'*20}\n")

