@echo off
REM Windows batch script for CORE multi-ticker execution
REM Sets environment and runs the multi-orchestrator

echo ========================================
echo CORE Multi-Ticker Production Execution
echo ========================================

REM Set environment variable
set ACCOUNT_EQUITY=25000

REM Execute multi-ticker analysis
echo Running multi-ticker analysis...
py multi_orchestrator.py --tickers AAPL,TSLA,NVDA --option_prices 1.50,2.10,3.25 --target_prices 5.00,6.00,10.40 --source mcp-http

if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Multi-ticker execution completed
) else (
    echo FAILED: Multi-ticker execution failed with error %ERRORLEVEL%
)

echo ========================================
echo Check outputs/ directory for results
echo ========================================
pause
