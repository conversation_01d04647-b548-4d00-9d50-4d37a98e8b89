#!/usr/bin/env python3
"""
Training Mixin - Shared training data capture functionality

Provides standardized training data logging for Agent Zero learning.
Used across all agents for consistent data collection.
"""

import json
import os
from datetime import datetime
from typing import Dict, Any, Optional
import logging

class TrainingMixin:
    """
    Mixin class for training data capture and logging.
    
    Provides standardized methods for Agent Zero training data collection.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def log_training(self, 
                    agent_name: str, 
                    input_data: Dict[str, Any], 
                    output_data: Dict[str, Any], 
                    execution_time: float) -> None:
        """
        Log training data for Agent Zero learning.
        
        Args:
            agent_name: Name of the agent generating the data
            input_data: Input data used for decision making
            output_data: Output/decision data generated
            execution_time: Time taken for execution (seconds)
        """
        
        # Use absolute path to ensure consistent training log location
        from pathlib import Path
        core_root = Path(__file__).parent.parent
        log_dir = core_root / "training_logs" / agent_name
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Build training record
        training_record = {
            "agent": agent_name,
            "timestamp": datetime.now().isoformat(),
            "input_data": input_data,
            "output_data": output_data,
            "execution_time": execution_time,
            "environment": {
                "agent_zero_mode": os.getenv("AGENT_ZERO_MODE", "off"),
                "data_source": os.getenv("DATA_SOURCE", "unknown"),
                "account_equity": os.getenv("ACCOUNT_EQUITY", "25000")
            }
        }
        
        # Generate unique filename
        timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        log_file = f"{log_dir}/training_{timestamp_str}.json"
        
        try:
            with open(log_file, 'w') as f:
                json.dump(training_record, f, indent=2, default=str)
                
            self.logger.debug(f"Training data logged: {log_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to log training data: {e}")
    
    def get_training_stats(self, agent_name: str) -> Dict[str, Any]:
        """
        Get training data statistics for an agent.
        
        Args:
            agent_name: Name of the agent
            
        Returns:
            Dict with training data statistics
        """
        log_dir = f"training_logs/{agent_name}"
        
        if not os.path.exists(log_dir):
            return {"total_records": 0, "directory_exists": False}
        
        try:
            files = [f for f in os.listdir(log_dir) if f.startswith("training_") and f.endswith(".json")]
            
            return {
                "total_records": len(files),
                "directory_exists": True,
                "latest_file": max(files) if files else None,
                "log_directory": log_dir
            }
            
        except Exception as e:
            self.logger.error(f"Error getting training stats: {e}")
            return {"total_records": 0, "error": str(e)}
