"""
Enhanced Flow Physics Strategy - Performance Monitoring Module

Tracks the performance of each component (Flow Physics, Pure Liquidity, Enhanced Greeks)
to enable adaptive weight adjustment and continuous improvement.
"""

import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd
import numpy as np
from dataclasses import dataclass, field
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


@dataclass
class SignalPerformance:
    """Performance data for a single signal."""
    signal_id: str
    timestamp: datetime
    ticker: str
    direction: str  # 'LONG' or 'SHORT'
    confidence: float
    component: str  # 'flow_physics', 'pure_liquidity', 'enhanced_greeks', 'confluence'
    entry_price: float
    current_price: Optional[float] = None
    exit_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # Performance metrics
    unrealized_pnl: Optional[float] = None
    realized_pnl: Optional[float] = None
    max_favorable: Optional[float] = None
    max_adverse: Optional[float] = None
    days_open: Optional[int] = None
    is_closed: bool = False
    close_reason: Optional[str] = None  # 'stop_loss', 'take_profit', 'manual', 'expired'
    
    # Component attribution
    contributing_components: List[str] = field(default_factory=list)
    component_weights: Dict[str, float] = field(default_factory=dict)


@dataclass 
class ComponentMetrics:
    """Performance metrics for a strategy component."""
    name: str
    total_signals: int = 0
    successful_signals: int = 0
    failed_signals: int = 0
    success_rate: float = 0.0
    avg_confidence: float = 0.0
    avg_pnl: float = 0.0
    total_pnl: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0
    recent_performance: float = 0.0  # Last 30 days
    
    # Time-based performance
    performance_by_hour: Dict[int, float] = field(default_factory=dict)
    performance_by_day: Dict[int, float] = field(default_factory=dict)
    performance_by_volatility: Dict[str, float] = field(default_factory=dict)


class PerformanceMonitor:
    """
    Monitors and tracks performance of Enhanced Flow Physics Strategy components.
    
    Features:
    - Signal tracking and attribution
    - Component performance analysis
    - Adaptive weight recommendations
    - Performance reporting and visualization
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize performance monitor."""
        self.config = config or {}
        
        # Performance data storage
        self.signals: Dict[str, SignalPerformance] = {}
        self.component_metrics: Dict[str, ComponentMetrics] = {
            'flow_physics': ComponentMetrics('flow_physics'),
            'pure_liquidity': ComponentMetrics('pure_liquidity'), 
            'enhanced_greeks': ComponentMetrics('enhanced_greeks'),
            'confluence': ComponentMetrics('confluence')
        }
        
        # Rolling performance windows
        self.performance_window = self.config.get('performance_window_days', 30)
        self.recent_signals = deque(maxlen=1000)  # Keep last 1000 signals
        
        # Storage configuration
        self.storage_dir = self.config.get('storage_dir', 'performance_data')
        self.auto_save_interval = self.config.get('auto_save_interval_minutes', 60)
        
        # Performance thresholds
        self.min_signals_for_stats = self.config.get('min_signals_for_stats', 10)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.6)
        
        # Ensure storage directory exists
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # Load existing data
        self._load_performance_data()
        
        logger.info(f"Performance monitor initialized with {len(self.signals)} tracked signals")
    
    def record_signal(self, 
                     signal_id: str,
                     ticker: str, 
                     direction: str,
                     confidence: float,
                     component: str,
                     entry_price: float,
                     stop_loss: Optional[float] = None,
                     take_profit: Optional[float] = None,
                     contributing_components: Optional[List[str]] = None,
                     component_weights: Optional[Dict[str, float]] = None) -> None:
        """Record a new signal for performance tracking."""
        
        signal = SignalPerformance(
            signal_id=signal_id,
            timestamp=datetime.now(),
            ticker=ticker,
            direction=direction,
            confidence=confidence,
            component=component,
            entry_price=entry_price,
            current_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            contributing_components=contributing_components or [component],
            component_weights=component_weights or {component: 1.0}
        )
        
        self.signals[signal_id] = signal
        self.recent_signals.append(signal_id)
        
        # Update component signal counts
        for comp in signal.contributing_components:
            if comp in self.component_metrics:
                self.component_metrics[comp].total_signals += 1
        
        logger.info(f"Recorded signal {signal_id} for {ticker} ({component})")
    
    def update_signal_price(self, signal_id: str, current_price: float) -> None:
        """Update current price for an open signal."""
        if signal_id not in self.signals:
            logger.warning(f"Signal {signal_id} not found for price update")
            return
            
        signal = self.signals[signal_id]
        if signal.is_closed:
            return
            
        signal.current_price = current_price
        
        # Calculate unrealized P&L
        if signal.direction == 'LONG':
            signal.unrealized_pnl = (current_price - signal.entry_price) / signal.entry_price
        else:
            signal.unrealized_pnl = (signal.entry_price - current_price) / signal.entry_price
        
        # Track max favorable and adverse moves
        if signal.max_favorable is None or signal.unrealized_pnl > signal.max_favorable:
            signal.max_favorable = signal.unrealized_pnl
            
        if signal.max_adverse is None or signal.unrealized_pnl < signal.max_adverse:
            signal.max_adverse = signal.unrealized_pnl
        
        # Update days open
        signal.days_open = (datetime.now() - signal.timestamp).days
        
        # Check for automatic exit conditions
        self._check_exit_conditions(signal)
    
    def close_signal(self, 
                    signal_id: str, 
                    exit_price: float, 
                    close_reason: str = 'manual') -> None:
        """Close a signal and record final performance."""
        if signal_id not in self.signals:
            logger.warning(f"Signal {signal_id} not found for closing")
            return
            
        signal = self.signals[signal_id]
        if signal.is_closed:
            logger.warning(f"Signal {signal_id} already closed")
            return
        
        signal.exit_price = exit_price
        signal.current_price = exit_price
        signal.close_reason = close_reason
        signal.is_closed = True
        
        # Calculate realized P&L
        if signal.direction == 'LONG':
            signal.realized_pnl = (exit_price - signal.entry_price) / signal.entry_price
        else:
            signal.realized_pnl = (signal.entry_price - exit_price) / signal.entry_price
        
        signal.unrealized_pnl = signal.realized_pnl
        
        # Update component performance metrics
        self._update_component_metrics(signal)
        
        logger.info(f"Closed signal {signal_id}: {signal.realized_pnl:.2%} P&L ({close_reason})")
    
    def _check_exit_conditions(self, signal: SignalPerformance) -> None:
        """Check if signal should be automatically closed."""
        if signal.is_closed or signal.current_price is None:
            return
            
        current_price = signal.current_price
        
        # Check stop loss
        if signal.stop_loss is not None:
            if (signal.direction == 'LONG' and current_price <= signal.stop_loss) or \
               (signal.direction == 'SHORT' and current_price >= signal.stop_loss):
                self.close_signal(signal.signal_id, current_price, 'stop_loss')
                return
        
        # Check take profit  
        if signal.take_profit is not None:
            if (signal.direction == 'LONG' and current_price >= signal.take_profit) or \
               (signal.direction == 'SHORT' and current_price <= signal.take_profit):
                self.close_signal(signal.signal_id, current_price, 'take_profit')
                return
        
        # Check time-based expiry (e.g., 30 days)
        max_days_open = self.config.get('max_signal_days', 30)
        if signal.days_open and signal.days_open >= max_days_open:
            self.close_signal(signal.signal_id, current_price, 'expired')
    
    def _update_component_metrics(self, signal: SignalPerformance) -> None:
        """Update performance metrics for components involved in the signal."""
        if not signal.is_closed or signal.realized_pnl is None:
            return
            
        # Update metrics for each contributing component
        for component in signal.contributing_components:
            if component not in self.component_metrics:
                continue
                
            metrics = self.component_metrics[component]
            weight = signal.component_weights.get(component, 1.0)
            weighted_pnl = signal.realized_pnl * weight
            
            # Update basic counts
            if signal.realized_pnl > 0:
                metrics.successful_signals += 1
            else:
                metrics.failed_signals += 1
            
            # Update P&L metrics
            metrics.total_pnl += weighted_pnl
            metrics.avg_pnl = metrics.total_pnl / max(metrics.total_signals, 1)
            
            # Update win rate and win/loss averages
            closed_signals = [s for s in self.signals.values() 
                            if s.is_closed and component in s.contributing_components]
            
            if closed_signals:
                wins = [s.realized_pnl for s in closed_signals if s.realized_pnl > 0]
                losses = [s.realized_pnl for s in closed_signals if s.realized_pnl <= 0]
                
                metrics.win_rate = len(wins) / len(closed_signals)
                metrics.avg_win = np.mean(wins) if wins else 0.0
                metrics.avg_loss = np.mean(losses) if losses else 0.0
                
                # Calculate profit factor
                total_wins = sum(wins) if wins else 0
                total_losses = abs(sum(losses)) if losses else 1e-10
                metrics.profit_factor = total_wins / total_losses
                
                # Calculate Sharpe ratio (simplified)
                pnls = [s.realized_pnl for s in closed_signals]
                if len(pnls) > 1:
                    metrics.sharpe_ratio = np.mean(pnls) / np.std(pnls) if np.std(pnls) > 0 else 0
    
    def get_component_performance(self, component: str) -> ComponentMetrics:
        """Get performance metrics for a specific component."""
        if component not in self.component_metrics:
            return ComponentMetrics(component)
        
        # Refresh metrics
        self._calculate_recent_performance(component)
        return self.component_metrics[component]
    
    def _calculate_recent_performance(self, component: str) -> None:
        """Calculate recent performance for a component."""
        cutoff_date = datetime.now() - timedelta(days=self.performance_window)
        
        recent_signals = [
            s for s in self.signals.values() 
            if s.timestamp >= cutoff_date and s.is_closed and component in s.contributing_components
        ]
        
        if recent_signals:
            recent_pnls = [s.realized_pnl for s in recent_signals]
            self.component_metrics[component].recent_performance = np.mean(recent_pnls)
        else:
            self.component_metrics[component].recent_performance = 0.0
    
    def get_adaptive_weights(self) -> Dict[str, float]:
        """Calculate adaptive weights based on recent performance."""
        base_weights = {
            'flow_physics': 0.45,
            'pure_liquidity': 0.30, 
            'enhanced_greeks': 0.25
        }
        
        # Get recent performance for each component
        performances = {}
        for component in base_weights.keys():
            metrics = self.get_component_performance(component)
            
            # Use recent performance with minimum signal requirement
            if metrics.total_signals >= self.min_signals_for_stats:
                performances[component] = metrics.recent_performance
            else:
                performances[component] = 0.0  # No data, use base weight
        
        # Calculate performance-based adjustments
        adaptive_weights = base_weights.copy()
        
        if any(p != 0 for p in performances.values()):
            # Calculate relative performance
            total_performance = sum(performances.values())
            
            if total_performance > 0:
                # Boost weights for outperforming components
                adjustment_factor = self.config.get('weight_adjustment_factor', 0.2)
                
                for component, performance in performances.items():
                    if performance > 0:
                        # Positive performance gets a boost
                        boost = min(adjustment_factor, performance * adjustment_factor)
                        adaptive_weights[component] += boost
                    elif performance < 0:
                        # Negative performance gets a reduction
                        reduction = min(adjustment_factor, abs(performance) * adjustment_factor)
                        adaptive_weights[component] = max(0.1, adaptive_weights[component] - reduction)
        
        # Normalize weights to sum to 1.0
        total_weight = sum(adaptive_weights.values())
        if total_weight > 0:
            adaptive_weights = {k: v/total_weight for k, v in adaptive_weights.items()}
        else:
            adaptive_weights = base_weights
        
        return adaptive_weights
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_signals': len(self.signals),
                'open_signals': len([s for s in self.signals.values() if not s.is_closed]),
                'closed_signals': len([s for s in self.signals.values() if s.is_closed]),
                'performance_window_days': self.performance_window
            },
            'component_performance': {},
            'adaptive_weights': self.get_adaptive_weights(),
            'top_performers': {},
            'recommendations': []
        }
        
        # Component performance details
        for component, metrics in self.component_metrics.items():
            self._calculate_recent_performance(component)
            
            report['component_performance'][component] = {
                'total_signals': metrics.total_signals,
                'success_rate': metrics.win_rate,
                'total_pnl': metrics.total_pnl,
                'avg_pnl': metrics.avg_pnl,
                'recent_performance': metrics.recent_performance,
                'sharpe_ratio': metrics.sharpe_ratio,
                'profit_factor': metrics.profit_factor,
                'avg_win': metrics.avg_win,
                'avg_loss': metrics.avg_loss
            }
        
        # Find top performers
        if any(m.total_signals >= self.min_signals_for_stats for m in self.component_metrics.values()):
            best_recent = max(
                [(k, v.recent_performance) for k, v in self.component_metrics.items() 
                 if v.total_signals >= self.min_signals_for_stats],
                key=lambda x: x[1],
                default=('none', 0)
            )
            report['top_performers']['best_recent'] = best_recent[0]
            
            best_overall = max(
                [(k, v.total_pnl) for k, v in self.component_metrics.items()
                 if v.total_signals >= self.min_signals_for_stats],
                key=lambda x: x[1],
                default=('none', 0)
            )
            report['top_performers']['best_overall'] = best_overall[0]
        
        # Generate recommendations
        recommendations = self._generate_recommendations()
        report['recommendations'] = recommendations
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate performance-based recommendations."""
        recommendations = []
        
        for component, metrics in self.component_metrics.items():
            if metrics.total_signals < self.min_signals_for_stats:
                continue
                
            # Low win rate recommendation
            if metrics.win_rate < 0.4:
                recommendations.append(
                    f"Consider reducing {component} weight - low win rate ({metrics.win_rate:.1%})"
                )
            
            # High Sharpe ratio recommendation  
            if metrics.sharpe_ratio > 1.5:
                recommendations.append(
                    f"Consider increasing {component} weight - high Sharpe ratio ({metrics.sharpe_ratio:.2f})"
                )
            
            # Poor recent performance
            if metrics.recent_performance < -0.05:  # -5% recent performance
                recommendations.append(
                    f"{component} underperforming recently ({metrics.recent_performance:.1%}) - investigate"
                )
        
        # Confluence recommendations
        confluence_signals = [s for s in self.signals.values() if s.component == 'confluence']
        if confluence_signals:
            confluence_pnl = np.mean([s.realized_pnl for s in confluence_signals if s.is_closed])
            individual_pnl = np.mean([s.realized_pnl for s in self.signals.values() 
                                    if s.is_closed and s.component != 'confluence'])
            
            if confluence_pnl > individual_pnl * 1.2:
                recommendations.append(
                    "Confluence signals significantly outperforming - consider stricter confluence requirements"
                )
        
        return recommendations
    
    def save_performance_data(self) -> None:
        """Save performance data to disk."""
        try:
            # Save signals data
            signals_file = os.path.join(self.storage_dir, 'signals.json')
            signals_data = {}
            
            for signal_id, signal in self.signals.items():
                signals_data[signal_id] = {
                    'signal_id': signal.signal_id,
                    'timestamp': signal.timestamp.isoformat(),
                    'ticker': signal.ticker,
                    'direction': signal.direction,
                    'confidence': signal.confidence,
                    'component': signal.component,
                    'entry_price': signal.entry_price,
                    'current_price': signal.current_price,
                    'exit_price': signal.exit_price,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit,
                    'unrealized_pnl': signal.unrealized_pnl,
                    'realized_pnl': signal.realized_pnl,
                    'max_favorable': signal.max_favorable,
                    'max_adverse': signal.max_adverse,
                    'days_open': signal.days_open,
                    'is_closed': signal.is_closed,
                    'close_reason': signal.close_reason,
                    'contributing_components': signal.contributing_components,
                    'component_weights': signal.component_weights
                }
            
            with open(signals_file, 'w') as f:
                json.dump(signals_data, f, indent=2)
            
            # Save performance report
            report = self.generate_performance_report()
            report_file = os.path.join(self.storage_dir, 'performance_report.json')
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"Saved performance data: {len(self.signals)} signals")
            
        except Exception as e:
            logger.error(f"Failed to save performance data: {e}")
    
    def _load_performance_data(self) -> None:
        """Load existing performance data from disk."""
        try:
            signals_file = os.path.join(self.storage_dir, 'signals.json')
            if not os.path.exists(signals_file):
                return
                
            with open(signals_file, 'r') as f:
                signals_data = json.load(f)
            
            for signal_id, data in signals_data.items():
                signal = SignalPerformance(
                    signal_id=data['signal_id'],
                    timestamp=datetime.fromisoformat(data['timestamp']),
                    ticker=data['ticker'],
                    direction=data['direction'],
                    confidence=data['confidence'],
                    component=data['component'],
                    entry_price=data['entry_price'],
                    current_price=data.get('current_price'),
                    exit_price=data.get('exit_price'),
                    stop_loss=data.get('stop_loss'),
                    take_profit=data.get('take_profit'),
                    unrealized_pnl=data.get('unrealized_pnl'),
                    realized_pnl=data.get('realized_pnl'),
                    max_favorable=data.get('max_favorable'),
                    max_adverse=data.get('max_adverse'),
                    days_open=data.get('days_open'),
                    is_closed=data.get('is_closed', False),
                    close_reason=data.get('close_reason'),
                    contributing_components=data.get('contributing_components', []),
                    component_weights=data.get('component_weights', {})
                )
                
                self.signals[signal_id] = signal
                
                if not signal.is_closed:
                    self.recent_signals.append(signal_id)
            
            # Rebuild component metrics
            for signal in self.signals.values():
                if signal.is_closed:
                    self._update_component_metrics(signal)
            
            logger.info(f"Loaded {len(self.signals)} signals from storage")
            
        except Exception as e:
            logger.error(f"Failed to load performance data: {e}")


def create_performance_monitor(config: Optional[Dict[str, Any]] = None) -> PerformanceMonitor:
    """Factory function to create a performance monitor."""
    default_config = {
        'performance_window_days': 30,
        'storage_dir': 'performance_data',
        'auto_save_interval_minutes': 60,
        'min_signals_for_stats': 10,
        'confidence_threshold': 0.6,
        'max_signal_days': 30,
        'weight_adjustment_factor': 0.2
    }
    
    if config:
        default_config.update(config)
    
    return PerformanceMonitor(default_config)
