@echo off
REM B-Series Backtest Pipeline Driver - Windows
REM Executes complete backtest pipeline for specified tickers

echo ===============================================
echo B-Series Backtest Pipeline
echo ===============================================

REM Default parameters
set TICKERS=AAPL
set SOURCE=polygon
set MODEL=random_forest
set CAPITAL=100000

REM Parse command line arguments
:parse
if "%1"=="" goto execute
if "%1"=="--tickers" (
    set TICKERS=%2
    shift
    shift
    goto parse
)
if "%1"=="--source" (
    set SOURCE=%2
    shift
    shift
    goto parse
)
if "%1"=="--model" (
    set MODEL=%2
    shift
    shift
    goto parse
)
if "%1"=="--capital" (
    set CAPITAL=%2
    shift
    shift
    goto parse
)
shift
goto parse

:execute
echo Tickers: %TICKERS%
echo Source: %SOURCE%
echo Model: %MODEL%
echo Capital: %CAPITAL%
echo.

REM Execute batch pipeline
py -m tasks.run_backtest_batch --tickers %TICKERS% --source %SOURCE% --model %MODEL% --capital %CAPITAL% --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ===============================================
    echo Pipeline completed successfully
    echo ===============================================
) else (
    echo.
    echo ===============================================
    echo Pipeline failed with errors
    echo ===============================================
    exit /b 1
)
