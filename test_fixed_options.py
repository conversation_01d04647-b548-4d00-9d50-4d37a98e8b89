#!/usr/bin/env python3
"""
Test Fixed Options Data Retrieval
Test the corrected Schwab API options functionality
"""

import sys
import os
import logging

# Add SCHWAB_MCP_PRODUCTION to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'SCHWAB_MCP_PRODUCTION'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_fixed_options_data():
    """Test the fixed Schwab options data retrieval"""
    print("\n🔍 Testing FIXED Schwab Options Data")
    print("=" * 50)
    
    try:
        from core.schwab_production_api import SchwabAPI
        
        schwab_api = SchwabAPI()
        logger.info("✓ Schwab API initialized")
        
        # Test options chain retrieval with the fix
        logger.info("Testing FIXED options chain for AAPL...")
        options_chain = schwab_api.get_option_chains("AAPL", strike_count=5)
        
        logger.info(f"Options chain response type: {type(options_chain)}")
        
        if options_chain and isinstance(options_chain, dict):
            # Check structure
            symbol = options_chain.get('symbol', 'N/A')
            underlying_price = options_chain.get('underlyingPrice', 'N/A')
            
            calls = options_chain.get('callExpDateMap', {})
            puts = options_chain.get('putExpDateMap', {})
            
            logger.info(f"✅ OPTIONS DATA SUCCESSFULLY RETRIEVED")
            logger.info(f"  Symbol: {symbol}")
            logger.info(f"  Underlying Price: ${underlying_price}")
            logger.info(f"  Call Expirations: {len(calls)}")
            logger.info(f"  Put Expirations: {len(puts)}")
            
            # Analyze first expiration
            if calls:
                first_exp = list(calls.keys())[0]
                strikes = list(calls[first_exp].keys())
                logger.info(f"  First Expiration: {first_exp}")
                logger.info(f"  Available Strikes: {len(strikes)}")
                
                # Show sample option with Greeks
                if strikes:
                    sample_strike = strikes[len(strikes)//2]  # Middle strike
                    sample_option = calls[first_exp][sample_strike][0]
                    
                    logger.info(f"\n  📊 SAMPLE OPTION DATA:")
                    logger.info(f"    Strike: ${sample_option.get('strikePrice', 'N/A')}")
                    logger.info(f"    Bid: ${sample_option.get('bid', 'N/A')}")
                    logger.info(f"    Ask: ${sample_option.get('ask', 'N/A')}")
                    logger.info(f"    Last: ${sample_option.get('last', 'N/A')}")
                    logger.info(f"    Volume: {sample_option.get('totalVolume', 'N/A')}")
                    logger.info(f"    Open Interest: {sample_option.get('openInterest', 'N/A')}")
                    logger.info(f"    IV: {sample_option.get('volatility', 'N/A')}")
                    
                    # Greeks data
                    logger.info(f"\n  🔢 GREEKS DATA:")
                    logger.info(f"    Delta: {sample_option.get('delta', 'N/A')}")
                    logger.info(f"    Gamma: {sample_option.get('gamma', 'N/A')}")
                    logger.info(f"    Theta: {sample_option.get('theta', 'N/A')}")
                    logger.info(f"    Vega: {sample_option.get('vega', 'N/A')}")
                    logger.info(f"    Rho: {sample_option.get('rho', 'N/A')}")
                    
                    # Check if Greeks are actually populated
                    greeks_available = []
                    for greek in ['delta', 'gamma', 'theta', 'vega', 'rho']:
                        value = sample_option.get(greek)
                        if value is not None and value != 0:
                            greeks_available.append(greek)
                    
                    logger.info(f"    Available Greeks: {greeks_available}")
            
            return True
        else:
            logger.error("✗ No options data returned or wrong format")
            return False
            
    except Exception as e:
        logger.error(f"✗ Fixed options test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_symbols():
    """Test options data for multiple symbols"""
    print("\n🔍 Testing Multiple Symbols")
    print("=" * 50)
    
    symbols = ['AAPL', 'MSFT', 'SPY']
    results = {}
    
    try:
        from core.schwab_production_api import SchwabAPI
        schwab_api = SchwabAPI()
        
        for symbol in symbols:
            try:
                logger.info(f"Testing {symbol}...")
                options_data = schwab_api.get_option_chains(symbol, strike_count=3)
                
                if options_data:
                    calls = len(options_data.get('callExpDateMap', {}))
                    puts = len(options_data.get('putExpDateMap', {}))
                    results[symbol] = {'calls': calls, 'puts': puts, 'success': True}
                    logger.info(f"  ✓ {symbol}: {calls} call exps, {puts} put exps")
                else:
                    results[symbol] = {'success': False}
                    logger.error(f"  ✗ {symbol}: No data")
                    
            except Exception as e:
                results[symbol] = {'success': False, 'error': str(e)}
                logger.error(f"  ✗ {symbol}: {e}")
        
        # Summary
        successful = sum(1 for r in results.values() if r.get('success'))
        logger.info(f"\n✅ MULTI-SYMBOL TEST: {successful}/{len(symbols)} successful")
        
        return successful == len(symbols)
        
    except Exception as e:
        logger.error(f"✗ Multi-symbol test failed: {e}")
        return False

def main():
    """Run fixed options tests"""
    print("\n🎯 FIXED OPTIONS DATA TEST")
    print("=" * 60)
    
    results = []
    
    # Test 1: Fixed Options Data
    results.append(test_fixed_options_data())
    
    # Test 2: Multiple Symbols
    results.append(test_multiple_symbols())
    
    # Summary
    print("\n" + "=" * 60)
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("✅ Options data with Greeks is FULLY WORKING!")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("❌ Options functionality still has issues")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
