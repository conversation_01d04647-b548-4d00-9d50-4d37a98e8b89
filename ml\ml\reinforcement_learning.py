"""
Reinforcement Learning Module for Parameter Tuning

This module provides functionality for using reinforcement learning
to optimize parameters in the liquidity analysis system.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import logging
import time
from datetime import datetime
import threading
import json
import os
import random
from dataclasses import dataclass, field

# Try to import RL libraries
try:
    import gym
    from gym import spaces
    HAS_GYM = True
except ImportError:
    HAS_GYM = False
    
try:
    from stable_baselines3 import PPO, A2C, SAC
    from stable_baselines3.common.callbacks import BaseCallback
    HAS_SB3 = True
except ImportError:
    HAS_SB3 = False

# Internal imports
from ml_logging import get_logger

# Setup logger
logger = get_logger('reinforcement_learning')


@dataclass
class ParameterSpace:
    """Class for defining parameter space."""
    min_value: float
    max_value: float
    step_size: Optional[float] = None
    discrete: bool = False
    log_scale: bool = False
    values: Optional[List[Any]] = None


@dataclass
class TrainingEpisode:
    """Class for storing training episode data."""
    episode_id: str
    parameters: Dict[str, Any]
    reward: float
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


class LiquidityEnvironment:
    """
    Reinforcement learning environment for liquidity analysis.
    
    This class provides a gym-like environment for optimizing
    parameters in the liquidity analysis system.
    """
    
    def __init__(self, 
                parameter_space: Dict[str, ParameterSpace],
                reward_function: Callable[[Dict[str, Any]], float],
                config: Optional[Dict[str, Any]] = None):
        """
        Initialize the environment.
        
        Args:
            parameter_space: Dictionary mapping parameter names to ParameterSpace objects
            reward_function: Function that takes parameters and returns a reward
            config: Optional configuration dictionary
        """
        self.parameter_space = parameter_space
        self.reward_function = reward_function
        self.config = config or {}
        
        # Set default configuration
        self.max_steps = self.config.get('max_steps', 100)
        self.history_size = self.config.get('history_size', 10)
        
        # Initialize state
        self.current_parameters = {}
        self.current_step = 0
        self.episode_reward = 0.0
        self.history = []
        
        # Initialize action and observation spaces if gym is available
        if HAS_GYM:
            self._setup_spaces()
        
        logger.info("Liquidity environment initialized")
    
    def _setup_spaces(self):
        """Set up action and observation spaces."""
        # Create action space
        self.action_space = spaces.Dict({
            param_name: spaces.Box(
                low=np.array([space.min_value]),
                high=np.array([space.max_value]),
                dtype=np.float32
            ) if not space.discrete else spaces.Discrete(
                len(space.values) if space.values else 
                int((space.max_value - space.min_value) / space.step_size) + 1
            )
            for param_name, space in self.parameter_space.items()
        })
        
        # Create observation space
        # Observation includes current parameters and recent rewards
        obs_dict = {
            f"param_{param_name}": spaces.Box(
                low=np.array([space.min_value]),
                high=np.array([space.max_value]),
                dtype=np.float32
            )
            for param_name, space in self.parameter_space.items()
        }
        
        # Add history of rewards
        obs_dict["reward_history"] = spaces.Box(
            low=np.array([-np.inf] * self.history_size),
            high=np.array([np.inf] * self.history_size),
            dtype=np.float32
        )
        
        self.observation_space = spaces.Dict(obs_dict)
    
    def reset(self) -> Dict[str, Any]:
        """
        Reset the environment.
        
        Returns:
            Initial observation
        """
        # Reset state
        self.current_step = 0
        self.episode_reward = 0.0
        
        # Initialize parameters randomly
        self.current_parameters = {}
        for param_name, space in self.parameter_space.items():
            if space.discrete:
                if space.values:
                    self.current_parameters[param_name] = random.choice(space.values)
                else:
                    self.current_parameters[param_name] = space.min_value + space.step_size * random.randint(
                        0, int((space.max_value - space.min_value) / space.step_size)
                    )
            else:
                if space.log_scale:
                    log_min = np.log(max(space.min_value, 1e-10))
                    log_max = np.log(space.max_value)
                    log_value = log_min + random.random() * (log_max - log_min)
                    self.current_parameters[param_name] = np.exp(log_value)
                else:
                    self.current_parameters[param_name] = space.min_value + random.random() * (
                        space.max_value - space.min_value
                    )
        
        # Initialize history
        self.history = [0.0] * self.history_size
        
        # Return observation
        return self._get_observation()
    
    def step(self, action: Dict[str, Any]) -> Tuple[Dict[str, Any], float, bool, Dict[str, Any]]:
        """
        Take a step in the environment.
        
        Args:
            action: Dictionary mapping parameter names to values
            
        Returns:
            Tuple of (observation, reward, done, info)
        """
        # Update parameters based on action
        for param_name, value in action.items():
            if param_name in self.parameter_space:
                space = self.parameter_space[param_name]
                
                if space.discrete:
                    if space.values:
                        # For discrete spaces with explicit values
                        index = int(value) % len(space.values)
                        self.current_parameters[param_name] = space.values[index]
                    else:
                        # For discrete spaces with step size
                        steps = int(value) % (int((space.max_value - space.min_value) / space.step_size) + 1)
                        self.current_parameters[param_name] = space.min_value + steps * space.step_size
                else:
                    # For continuous spaces
                    if space.log_scale:
                        log_min = np.log(max(space.min_value, 1e-10))
                        log_max = np.log(space.max_value)
                        log_value = log_min + (log_max - log_min) * value
                        self.current_parameters[param_name] = np.exp(log_value)
                    else:
                        self.current_parameters[param_name] = space.min_value + value * (
                            space.max_value - space.min_value
                        )
        
        # Calculate reward
        reward = self.reward_function(self.current_parameters)
        
        # Update state
        self.current_step += 1
        self.episode_reward += reward
        
        # Update history
        self.history.pop(0)
        self.history.append(reward)
        
        # Check if done
        done = self.current_step >= self.max_steps
        
        # Create info dictionary
        info = {
            'parameters': self.current_parameters,
            'step': self.current_step,
            'episode_reward': self.episode_reward
        }
        
        # Return observation, reward, done, info
        return self._get_observation(), reward, done, info
    
    def _get_observation(self) -> Dict[str, Any]:
        """
        Get current observation.
        
        Returns:
            Observation dictionary
        """
        # Create observation with current parameters
        observation = {
            f"param_{param_name}": np.array([value], dtype=np.float32)
            for param_name, value in self.current_parameters.items()
        }
        
        # Add reward history
        observation["reward_history"] = np.array(self.history, dtype=np.float32)
        
        return observation


class ParameterOptimizer:
    """
    Parameter optimizer using reinforcement learning.
    
    This class provides methods for optimizing parameters
    in the liquidity analysis system using reinforcement learning.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the parameter optimizer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Set default configuration
        self.storage_path = self.config.get('storage_path', 'ml/rl_models')
        self.algorithm = self.config.get('algorithm', 'ppo')
        self.training_steps = self.config.get('training_steps', 10000)
        self.evaluation_episodes = self.config.get('evaluation_episodes', 10)
        
        # Check if RL libraries are available
        if not HAS_GYM or not HAS_SB3:
            logger.warning("Gym or Stable Baselines 3 not available, RL functionality will be limited")
        
        # Create storage directory if it doesn't exist
        os.makedirs(self.storage_path, exist_ok=True)
        
        logger.info("Parameter optimizer initialized")
    
    def optimize(self,
                parameter_space: Dict[str, ParameterSpace],
                reward_function: Callable[[Dict[str, Any]], float],
                model_name: str = "liquidity_optimizer") -> Dict[str, Any]:
        """
        Optimize parameters using reinforcement learning.
        
        Args:
            parameter_space: Dictionary mapping parameter names to ParameterSpace objects
            reward_function: Function that takes parameters and returns a reward
            model_name: Name for the optimizer model
            
        Returns:
            Dictionary with optimization results
        """
        if not HAS_GYM or not HAS_SB3:
            logger.error("Gym or Stable Baselines 3 not available, cannot optimize parameters")
            return {'success': False, 'error': 'RL libraries not available'}
        
        start_time = time.time()
        
        try:
            # Create environment
            env = LiquidityEnvironment(
                parameter_space=parameter_space,
                reward_function=reward_function,
                config=self.config.get('environment_config')
            )
            
            # Create model
            if self.algorithm.lower() == 'ppo':
                model = PPO('MultiInputPolicy', env, verbose=1)
            elif self.algorithm.lower() == 'a2c':
                model = A2C('MultiInputPolicy', env, verbose=1)
            elif self.algorithm.lower() == 'sac':
                model = SAC('MultiInputPolicy', env, verbose=1)
            else:
                logger.error(f"Unknown algorithm: {self.algorithm}")
                return {'success': False, 'error': f"Unknown algorithm: {self.algorithm}"}
            
            # Train model
            logger.info(f"Training {self.algorithm} model for {self.training_steps} steps")
            model.learn(total_timesteps=self.training_steps)
            
            # Save model
            model_path = os.path.join(self.storage_path, f"{model_name}.zip")
            model.save(model_path)
            logger.info(f"Saved model to {model_path}")
            
            # Evaluate model
            best_parameters = None
            best_reward = float('-inf')
            
            for _ in range(self.evaluation_episodes):
                obs = env.reset()
                done = False
                episode_reward = 0.0
                
                while not done:
                    action, _ = model.predict(obs, deterministic=True)
                    obs, reward, done, info = env.step(action)
                    episode_reward += reward
                
                if episode_reward > best_reward:
                    best_reward = episode_reward
                    best_parameters = info['parameters']
            
            elapsed = time.time() - start_time
            logger.info(f"Optimization completed in {elapsed:.2f} seconds")
            
            return {
                'success': True,
                'best_parameters': best_parameters,
                'best_reward': best_reward,
                'model_path': model_path,
                'elapsed_time': elapsed
            }
            
        except Exception as e:
            logger.error(f"Error optimizing parameters: {str(e)}")
            return {'success': False, 'error': str(e)}
