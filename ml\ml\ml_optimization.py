"""
ML Optimization and Scaling Module

This module provides optimization and scaling capabilities for the ML system,
focusing on computational performance, distributed training, model quantization,
and real-time processing.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import time
import logging
import threading
import multiprocessing
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import json
import numpy as np

from ml_logging import get_logger
from ml_config_manager import ConfigManager

logger = get_logger(__name__)

class MLOptimizer:
    """
    Machine Learning Optimizer
    
    This class provides optimization capabilities for ML models,
    focusing on computational performance, quantization, and scaling.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the ML optimizer.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.worker_pool = None
        self.distributed_enabled = False
        self.quantization_enabled = False
        self.max_workers = self.config_manager.get("max_workers", multiprocessing.cpu_count())
        
        logger.info(f"ML Optimizer initialized with max_workers={self.max_workers}")
    
    def initialize(self) -> bool:
        """
        Initialize optimization services.
        
        Returns:
            bool: True if initialization was successful
        """
        try:
            # Create worker pool if parallel processing is enabled
            if self.config_manager.get("enable_parallel_processing", False):
                self.worker_pool = multiprocessing.Pool(processes=self.max_workers)
                logger.info(f"Worker pool initialized with {self.max_workers} workers")
            
            # Enable distributed training if configured
            self.distributed_enabled = self.config_manager.get("enable_distributed", False)
            if self.distributed_enabled:
                self._setup_distributed_environment()
            
            # Enable quantization if configured
            self.quantization_enabled = self.config_manager.get("enable_quantization", False)
            
            logger.info("ML Optimizer initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing ML optimizer: {str(e)}")
            return False
    
    def _setup_distributed_environment(self) -> None:
        """Set up the distributed training environment."""
        try:
            import torch.distributed as dist
            
            # Initialize process group based on configuration
            backend = self.config_manager.get("distributed_backend", "gloo")
            
            # If we're using a specific distributed setup, initialize it
            if self.config_manager.get("is_distributed_node", False):
                world_size = self.config_manager.get("world_size", 1)
                rank = self.config_manager.get("rank", 0)
                
                if world_size > 1:
                    dist.init_process_group(
                        backend=backend,
                        init_method=self.config_manager.get("dist_url", "tcp://localhost:23456"),
                        world_size=world_size,
                        rank=rank
                    )
                    logger.info(f"Distributed environment initialized (rank={rank}, world_size={world_size})")
            
        except ImportError:
            logger.warning("PyTorch distributed not available, distributed training disabled")
            self.distributed_enabled = False
        except Exception as e:
            logger.error(f"Error setting up distributed environment: {str(e)}")
            self.distributed_enabled = False
    
    def optimize_model(self, model: Any, optimization_level: int = 1) -> Any:
        """
        Optimize a PyTorch model for inference.
        
        Args:
            model: The PyTorch model to optimize
            optimization_level: Optimization aggressiveness (1-3)
            
        Returns:
            Optimized model
        """
        try:
            import torch
            
            # Apply fusion optimizations (LSTM, etc.)
            if hasattr(torch, 'jit') and optimization_level >= 1:
                try:
                    # Try script mode first
                    model = torch.jit.script(model)
                    logger.info("Model optimized with TorchScript")
                except Exception as e:
                    logger.warning(f"TorchScript optimization failed: {str(e)}")
                    
                    # Fall back to tracing if scripting fails
                    if optimization_level >= 2:
                        try:
                            # We need example inputs for tracing
                            example_inputs = self._get_example_inputs(model)
                            if example_inputs is not None:
                                model = torch.jit.trace(model, example_inputs)
                                logger.info("Model optimized with TorchScript tracing")
                        except Exception as e:
                            logger.warning(f"TorchScript tracing failed: {str(e)}")
            
            # Apply quantization if enabled and supported
            if self.quantization_enabled and optimization_level >= 2:
                model = self._quantize_model(model)
            
            return model
            
        except ImportError:
            logger.warning("PyTorch not available, model optimization skipped")
            return model
        except Exception as e:
            logger.error(f"Error during model optimization: {str(e)}")
            return model
    
    def _get_example_inputs(self, model: Any) -> Optional[Any]:
        """
        Get example inputs for model tracing.
        
        Args:
            model: The model to generate inputs for
            
        Returns:
            Example inputs for the model, or None if not possible
        """
        # This needs to be customized based on the model architecture
        # Here is a simplified example for a typical model
        try:
            import torch
            
            # Try to infer input shape from model
            input_shape = None
            if hasattr(model, 'input_shape'):
                input_shape = model.input_shape
            elif hasattr(model, 'input_dim'):
                input_shape = (1, model.input_dim)
            
            if input_shape:
                example_input = torch.randn(*input_shape)
                return example_input
            
            return None
            
        except Exception as e:
            logger.warning(f"Failed to generate example inputs: {str(e)}")
            return None
    
    def _quantize_model(self, model: Any) -> Any:
        """
        Apply quantization to the model to reduce memory footprint and improve inference speed.
        
        Args:
            model: The model to quantize
            
        Returns:
            Quantized model
        """
        try:
            import torch
            
            # Check if quantization is supported for this model
            if not hasattr(torch, 'quantization'):
                logger.warning("Quantization not supported in this PyTorch version")
                return model
            
            # Configure quantization parameters based on config
            quantization_mode = self.config_manager.get("quantization_mode", "dynamic")
            
            if quantization_mode == "dynamic":
                # Dynamic quantization (quantizes weights, activations calculated in fp32)
                try:
                    quantized_model = torch.quantization.quantize_dynamic(
                        model, 
                        {torch.nn.Linear, torch.nn.LSTM, torch.nn.GRU}, 
                        dtype=torch.qint8
                    )
                    logger.info("Model successfully quantized using dynamic quantization")
                    return quantized_model
                except Exception as e:
                    logger.warning(f"Dynamic quantization failed: {str(e)}")
            
            elif quantization_mode == "static":
                # Static quantization (requires calibration)
                logger.warning("Static quantization requires calibration, not implemented yet")
            
            elif quantization_mode == "qat":
                # Quantization-aware training (must be applied during training)
                logger.warning("Quantization-aware training must be applied during model training")
            
            # Return original model if quantization failed
            return model
            
        except ImportError:
            logger.warning("PyTorch quantization not available")
            return model
        except Exception as e:
            logger.error(f"Error during model quantization: {str(e)}")
            return model
    
    def parallel_apply(self, func: Callable, items: List[Any]) -> List[Any]:
        """
        Apply a function to a list of items in parallel.
        
        Args:
            func: Function to apply
            items: List of items to process
            
        Returns:
            List of results
        """
        if self.worker_pool is not None:
            try:
                results = self.worker_pool.map(func, items)
                return list(results)
            except Exception as e:
                logger.error(f"Error in parallel processing: {str(e)}")
                # Fall back to sequential processing
                return [func(item) for item in items]
        else:
            # Sequential processing if parallel disabled
            return [func(item) for item in items]
    
    def optimize_batch_size(
        self, 
        model: Any, 
        data_loader: Any, 
        target_latency_ms: float = 100.0
    ) -> int:
        """
        Find optimal batch size for inference based on latency constraints.
        
        Args:
            model: The model to optimize for
            data_loader: DataLoader with sample data
            target_latency_ms: Target latency in milliseconds
            
        Returns:
            Optimal batch size
        """
        try:
            import torch
            
            # Try different batch sizes
            batch_sizes = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512]
            latencies = []
            
            # Set model to evaluation mode
            model.eval()
            
            # Get sample data for testing
            sample_data = next(iter(data_loader))[0]
            
            for batch_size in batch_sizes:
                # Skip larger batch sizes if we've already exceeded target latency
                if latencies and latencies[-1] > target_latency_ms:
                    break
                
                # Create a batch of appropriate size
                if batch_size == 1:
                    batch = sample_data[0:1]
                else:
                    # Repeat the data to create a larger batch
                    repeats = (batch_size + len(sample_data) - 1) // len(sample_data)
                    batch = torch.cat([sample_data] * repeats, dim=0)[:batch_size]
                
                # Measure inference time
                start_time = time.time()
                with torch.no_grad():
                    # Run inference multiple times for more stable measurement
                    for _ in range(10):
                        _ = model(batch)
                
                end_time = time.time()
                avg_latency = ((end_time - start_time) / 10) * 1000  # ms
                latencies.append(avg_latency)
                
                logger.debug(f"Batch size {batch_size}: latency {avg_latency:.2f} ms")
            
            # Find optimal batch size
            optimal_batch_size = 1
            for i, (batch_size, latency) in enumerate(zip(batch_sizes, latencies)):
                if latency <= target_latency_ms:
                    optimal_batch_size = batch_size
                else:
                    break
            
            logger.info(f"Optimal batch size: {optimal_batch_size} (latency: {latencies[batch_sizes.index(optimal_batch_size)]:.2f} ms)")
            return optimal_batch_size
            
        except Exception as e:
            logger.error(f"Error optimizing batch size: {str(e)}")
            return 32  # Default batch size
    
    def enable_real_time_processing(self, model: Any) -> Any:
        """
        Optimize a model for real-time processing.
        
        Args:
            model: The model to optimize
            
        Returns:
            Optimized model
        """
        try:
            # Maximum optimization level for real-time
            optimized_model = self.optimize_model(model, optimization_level=3)
            
            # Additional real-time specific optimizations
            if hasattr(optimized_model, 'eval'):
                optimized_model.eval()
            
            return optimized_model
        except Exception as e:
            logger.error(f"Error optimizing for real-time: {str(e)}")
            return model
    
    def shutdown(self) -> None:
        """Safely shut down all optimizer resources."""
        logger.info("Shutting down ML optimizer")
        
        # Close worker pool if it exists
        if self.worker_pool is not None:
            self.worker_pool.close()
            self.worker_pool.join()
            self.worker_pool = None
        
        # Clean up distributed environment if enabled
        if self.distributed_enabled:
            try:
                import torch.distributed as dist
                if dist.is_initialized():
                    dist.destroy_process_group()
            except:
                pass
        
        logger.info("ML optimizer shut down successfully")


# Global optimizer instance
_optimizer = None

def get_ml_optimizer() -> MLOptimizer:
    """Get the global ML optimizer instance."""
    global _optimizer
    if _optimizer is None:
        _optimizer = MLOptimizer()
    return _optimizer

def initialize_optimizer() -> bool:
    """Initialize the ML optimizer."""
    optimizer = get_ml_optimizer()
    return optimizer.initialize()

def shutdown_optimizer() -> None:
    """Safely shut down the optimizer."""
    global _optimizer
    if _optimizer is not None:
        _optimizer.shutdown()
        _optimizer = None
