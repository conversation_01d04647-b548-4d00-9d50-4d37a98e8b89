#!/usr/bin/env python3
"""
Schwab MCP Server - CORRECTED Production Implementation
Proper MCP protocol with historical data exposure
Mathematical rigor: 100% agent accessibility
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import signal
import os
from datetime import datetime, timedelta

# MCP Protocol Implementation
try:
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent
except ImportError:
    print("MCP library not found. Installing...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "mcp"])
    from mcp.server import Server
    from mcp.server.stdio import stdio_server
    from mcp.types import Tool, TextContent

# Get absolute paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_DIR = os.path.join(BASE_DIR, "logs")
CONFIG_DIR = os.path.join(BASE_DIR, "config")

os.makedirs(LOG_DIR, exist_ok=True)
os.makedirs(CONFIG_DIR, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, 'schwab_mcp_server.log')),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import production API client
sys.path.append(os.path.join(BASE_DIR, "core"))
from schwab_production_api import SchwabProductionClient, SchwabAPIError

# Initialize MCP Server
app = Server("schwab-market-data")
client = SchwabProductionClient()

@app.list_tools()
async def list_tools() -> List[Tool]:
    """List all available MCP tools for agents"""
    return [
        Tool(
            name="get_price_history",
            description="Get historical OHLCV price data for any symbol with full mathematical precision",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string", 
                        "description": "Stock ticker symbol (e.g., SPY, QQQ, AAPL)"
                    },
                    "period_type": {
                        "type": "string",
                        "enum": ["day", "month", "year", "ytd"],
                        "default": "year",
                        "description": "Period type for historical data"
                    },
                    "period": {
                        "type": "integer",
                        "default": 1,
                        "description": "Number of periods (1-20)"
                    },
                    "frequency_type": {
                        "type": "string", 
                        "enum": ["minute", "daily", "weekly", "monthly"],
                        "default": "daily",
                        "description": "Frequency type for data points"
                    },
                    "frequency": {
                        "type": "integer",
                        "default": 1,
                        "description": "Frequency interval (1, 5, 15, 30)"
                    }
                },
                "required": ["symbol"]
            }
        ),
        Tool(
            name="get_real_time_quote",
            description="Get real-time quote with bid/ask/last price and volume",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Stock ticker symbol"
                    }
                },
                "required": ["symbol"]
            }
        ),
        Tool(
            name="get_multiple_quotes", 
            description="Get real-time quotes for multiple symbols efficiently",
            inputSchema={
                "type": "object",
                "properties": {
                    "symbols": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "List of stock ticker symbols"
                    }
                },
                "required": ["symbols"]
            }
        ),
        Tool(
            name="get_option_chain",
            description="Get options chain data for symbol (if available)",
            inputSchema={
                "type": "object", 
                "properties": {
                    "symbol": {
                        "type": "string",
                        "description": "Underlying stock ticker symbol"
                    },
                    "contract_type": {
                        "type": "string",
                        "enum": ["CALL", "PUT", "ALL"],
                        "default": "ALL",
                        "description": "Option contract type"
                    }
                },
                "required": ["symbol"]
            }
        ),
        Tool(
            name="health_check",
            description="Get system health and performance metrics",
            inputSchema={
                "type": "object",
                "properties": {}
            }
        )
    ]

@app.call_tool()
async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Execute MCP tool calls with full error handling"""
    logger.info(f"Tool called: {name} with args: {arguments}")
    
    try:
        if name == "get_price_history":
            symbol = arguments["symbol"].upper()
            period_type = arguments.get("period_type", "year")
            period = arguments.get("period", 1)
            frequency_type = arguments.get("frequency_type", "daily") 
            frequency = arguments.get("frequency", 1)
            
            # Get historical data from Schwab API
            candles = client.api.get_price_history(
                symbol=symbol,
                period_type=period_type,
                period=period,
                frequency_type=frequency_type,
                frequency=frequency
            )
            
            result = {
                "symbol": symbol,
                "period_type": period_type,
                "period": period,
                "frequency_type": frequency_type,
                "frequency": frequency,
                "candles": candles,
                "count": len(candles),
                "timestamp": time.time()
            }
            
            return [TextContent(
                type="text",
                text=f"Historical data for {symbol}: {len(candles)} candles retrieved\n" +
                     f"Data structure: OHLCV with datetime\n" +
                     f"Sample: {candles[0] if candles else 'No data'}\n" +
                     f"Full data: {json.dumps(result, indent=2)}"
            )]
            
        elif name == "get_real_time_quote":
            symbol = arguments["symbol"].upper()
            quote_data = client.get_real_time_quote(symbol)
            
            return [TextContent(
                type="text", 
                text=f"Real-time quote for {symbol}:\n" +
                     f"Price: ${quote_data['price']}\n" +
                     f"Bid: ${quote_data['bid']} | Ask: ${quote_data['ask']}\n" +
                     f"Volume: {quote_data['volume']:,}\n" +
                     f"Change: {quote_data['change']} ({quote_data['change_percent']:.2f}%)\n" +
                     f"Full data: {json.dumps(quote_data, indent=2)}"
            )]
            
        elif name == "get_multiple_quotes":
            symbols = [s.upper() for s in arguments["symbols"]]
            quotes = client.api.get_multiple_quotes(symbols)
            
            result_text = f"Multiple quotes for {len(symbols)} symbols:\n"
            for symbol, quote in quotes.items():
                result_text += f"{symbol}: ${quote.price} (Vol: {quote.volume:,})\n"
            
            result_text += f"\nFull data: {json.dumps({k: v.__dict__ for k, v in quotes.items()}, indent=2)}"
            
            return [TextContent(type="text", text=result_text)]
            
        elif name == "get_option_chain":
            symbol = arguments["symbol"].upper()
            contract_type = arguments.get("contract_type", "ALL")
            
            # Note: Options chain implementation depends on Schwab API availability
            return [TextContent(
                type="text",
                text=f"Options chain for {symbol} ({contract_type}):\n" +
                     f"Note: Options implementation requires Schwab API options endpoint\n" +
                     f"Current status: Checking API availability..."
            )]
            
        elif name == "health_check":
            health_data = client.health_check()
            
            return [TextContent(
                type="text",
                text=f"System Health Check:\n" +
                     f"Status: {health_data['status']}\n" +
                     f"Accounts Connected: {health_data.get('accounts', 0)}\n" +
                     f"Token Valid: {health_data.get('token_valid', False)}\n" +
                     f"Timestamp: {health_data['timestamp']}\n" +
                     f"Full data: {json.dumps(health_data, indent=2)}"
            )]
            
        else:
            return [TextContent(
                type="text",
                text=f"Error: Unknown tool '{name}'"
            )]
            
    except Exception as e:
        logger.error(f"Error executing tool {name}: {e}")
        return [TextContent(
            type="text",
            text=f"Error executing {name}: {str(e)}\n" +
                 f"Arguments were: {arguments}"
        )]

async def main():
    """Main MCP server entry point with proper protocol"""
    logger.info("="*60)
    logger.info("SCHWAB MCP SERVER - CORRECTED IMPLEMENTATION")
    logger.info("="*60)
    logger.info("MCP Protocol: JSON-RPC 2.0 with stdio transport")
    logger.info("Historical Data: FULLY EXPOSED TO AGENTS")
    logger.info("Tools Available: get_price_history, get_real_time_quote, get_multiple_quotes")
    logger.info("="*60)
    
    # Run MCP server with stdio transport
    async with stdio_server() as (read_stream, write_stream):
        await app.run(
            read_stream,
            write_stream, 
            app.create_initialization_options()
        )

if __name__ == "__main__":
    asyncio.run(main())
