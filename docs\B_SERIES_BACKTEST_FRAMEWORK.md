# B-Series Backtest Framework

## Overview

The B-Series Backtest Framework implements a complete historical back-testing and machine learning walk-forward validation system for algorithmic trading strategies. This modular framework follows engineering excellence principles with statistical rigor and mathematical validation.

## Architecture

The framework consists of four core stages executed sequentially:

- **B-01**: Historical Data Fetcher - Retrieves historical market data
- **B-02**: Feature Builder - Constructs technical and mathematical features
- **B-03**: Walk-Forward Validator - Trains and validates ML models using time-series cross-validation
- **B-04**: Backtest Simulator - Simulates trading strategy with realistic execution

## Performance Criteria

All backtest results must meet the following quantitative thresholds:

- **Sharpe Ratio**:  1.0
- **Maximum Drawdown**:  15%
- **Minimum Trades**:  10
- **Accuracy**:  55%
- **Runtime**:  30 minutes

## Quick Start

### Single Ticker Execution

```bash
# Windows
run_backtest_pipeline.bat --tickers ${TICKER} --source polygon

# Unix/Linux
./run_backtest_pipeline.sh --tickers ${TICKER} --source polygon
```

### Multiple Tickers

```bash
py -m tasks.run_backtest_batch --tickers ${TICKER} TSLA NVDA --source polygon --verbose
```

### Individual Stage Execution

```bash
# B-01: Fetch historical data
py -m tasks.fetch_history --ticker ${TICKER} --source polygon

# B-02: Build features
py -m tasks.build_features --ticker ${TICKER}

# B-03: Train and validate model
py -m tasks.walk_train_validate --ticker ${TICKER} --model random_forest

# B-04: Run backtest simulation
py -m tasks.backtest_simulator --ticker ${TICKER} --threshold 0.6
```

## Configuration

### Contract Specification (contracts/B-series.yml)

The B-series contract defines:
- Stage dependencies and timeouts
- Success criteria and validation rules
- Required dependencies and versions

### Runtime Parameters

Key configurable parameters:

- `--source`: Data source (polygon, mcp)
- `--model`: ML model type (random_forest, logistic)
- `--threshold`: Signal threshold (default: 0.6)
- `--commission`: Commission rate (default: 0.001)
- `--capital`: Initial capital (default: 100000)

## Feature Engineering

The Feature Builder (B-02) constructs 25+ technical indicators:

### Price Features
- Simple Moving Averages (10, 20, 50 periods)
- Exponential Moving Averages (12, 26 periods)
- Price momentum (1, 5, 10, 20 period returns)
- Price position within daily range

### Technical Indicators
- MACD (Moving Average Convergence Divergence)
- RSI (Relative Strength Index, 14 periods)
- Bollinger Bands with position indicator
- Average True Range (ATR)

### Volume Analysis
- Volume moving averages and ratios
- Price-volume trend analysis

### Volatility Metrics
- Historical volatility (10, 20 periods)
- True Range calculations

## Machine Learning

### Walk-Forward Validation (B-03)

Time-series cross-validation methodology:
- **Training Window**: 252 trading days (1 year)
- **Testing Window**: 21 trading days (1 month)
- **Non-overlapping windows** to prevent data leakage
- **Forward-looking target**: 5-period ahead returns

### Model Options

1. **Random Forest Classifier**
   - 100 estimators
   - Max depth: 10
   - Minimum samples: 20/10 (split/leaf)

2. **Logistic Regression**
   - L2 regularization
   - Maximum iterations: 1000

### Feature Scaling

StandardScaler normalization applied to all features to ensure:
- Zero mean and unit variance
- Consistent model performance across different feature scales

## Backtest Simulation

### Trading Logic (B-04)

- **Signal Generation**: Binary classification (buy/hold)
- **Position Sizing**: 95% of available capital
- **Commission**: Configurable rate per trade
- **Execution**: Market orders at close prices

### Performance Metrics

Comprehensive performance analysis including:
- Total return and Sharpe ratio
- Maximum drawdown analysis
- Win rate and trade statistics
- Risk-adjusted metrics

## Output Artifacts

### Model Files
- `models/backtest_model.pkl`: Trained model with scaler and features

### Reports
- `reports/backtest_{ticker}_{date}.md`: Detailed performance report

### Data Files
- `data/history/{ticker}_bars.parquet`: Historical OHLCV data
- `data/features/{ticker}_features.parquet`: Engineered features

## Testing and Validation

### Continuous Integration

Mock tests ensure CI pipeline integrity:
- All stages return successful status
- Performance metrics exceed thresholds
- Contract compliance validation

### Test Execution

```bash
pytest tests/test_backtest_stub.py -v
```

## Mathematical Foundation

### Sharpe Ratio Calculation

```
Sharpe = (Mean_Return - Risk_Free_Rate) / Standard_Deviation * sqrt(252)
```

### Maximum Drawdown

```
Drawdown = (Portfolio_Value - Running_Maximum) / Running_Maximum
Max_Drawdown = min(Drawdown_Series)
```

### Walk-Forward Efficiency

Non-overlapping windows prevent look-ahead bias while maximizing data utilization for robust statistical inference.

## Error Handling

Comprehensive error handling at each stage:
- Data validation and quality checks
- Model convergence verification
- File system error recovery
- Timeout and retry mechanisms

## Integration with Agent Zero

Trained models can be exported for Agent Zero meta-policy training:

```python
# Copy model for Agent Zero integration
cp models/backtest_model.pkl models/az_meta_policy.pt
```

## Performance Optimization

- **Modular Design**: Independent stage execution
- **Efficient Data Formats**: Parquet for fast I/O
- **Vectorized Calculations**: NumPy/Pandas operations
- **Memory Management**: Chunked processing for large datasets

## Compliance and Risk

- **Data Freshness**: 24-hour maximum age requirement
- **Model Validation**: Statistical significance testing
- **Performance Bounds**: Automatic threshold enforcement
- **Audit Trail**: Complete execution logging

## Future Enhancements

Planned improvements:
- Multi-timeframe analysis
- Alternative data integration
- Advanced ensemble methods
- Real-time model updates
