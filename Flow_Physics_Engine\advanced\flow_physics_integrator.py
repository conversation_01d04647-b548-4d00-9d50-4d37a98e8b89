"""Flow Physics Integrator

Integrates all flow physics components (velocity, acceleration, jerk) to provide
comprehensive flow analysis and institutional detection.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass

from . import FLOW_PHYSICS_CONSTANTS, FLOW_REGIMES, get_flow_physics_config

logger = logging.getLogger(__name__)


@dataclass
class FlowPhysicsResult:
    """Complete flow physics analysis result."""
    timestamp: datetime
    symbol: str
    
    # Raw values
    flow_value: float
    flow_velocity: float
    flow_acceleration: float
    flow_jerk: float
    
    # Normalized values (0-1 scale)
    normalized_velocity: float
    normalized_acceleration: float
    normalized_jerk: float
    
    # Regime detection
    current_regime: str
    regime_confidence: float
    regime_duration: Optional[timedelta]
    
    # Institutional detection
    institutional_activity: bool
    institutional_direction: str  # 'accumulation', 'distribution', 'neutral'
    institutional_strength: float
    
    # Alerts
    momentum_shift_detected: bool
    regime_change_detected: bool
    extreme_flow_detected: bool
    
    # Multi-timeframe consensus
    timeframe_alignment: float  # 0-1, how aligned are different timeframes
    dominant_timeframe: str
    
    # Metadata
    quality_score: float
    analysis_metadata: Dict[str, Any]
    

class FlowPhysicsIntegrator:
    """Integrates all flow physics components for comprehensive analysis."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the flow physics integrator.
        
        Args:
            config: Optional configuration overrides
        """
        self.config = get_flow_physics_config(config)
        self.constants = self.config['constants']
        self.regimes = self.config['regimes']
        
        # Component references (will be initialized on first use)
        self._velocity_analyzer = None
        self._acceleration_analyzer = None
        self._jerk_analyzer = None
        
        # State tracking
        self._regime_history = {}
        self._flow_history = {}
        self._last_analysis = {}
        
        logger.info("Flow Physics Integrator initialized")
        
    def analyze(self, 
                symbol: str,
                flow_data: Dict[str, Any],
                lookback_data: Optional[pd.DataFrame] = None) -> FlowPhysicsResult:
        """Perform comprehensive flow physics analysis.
        
        Args:
            symbol: Symbol being analyzed
            flow_data: Current flow data point
            lookback_data: Historical flow data for derivative calculations
            
        Returns:
            FlowPhysicsResult with complete analysis
        """
        try:
            timestamp = flow_data.get('timestamp', datetime.now())
            flow_value = float(flow_data.get('flow_value', 0))
            
            # Get or create history for this symbol
            if symbol not in self._flow_history:
                self._flow_history[symbol] = []
                
            # Add current data point to history
            self._flow_history[symbol].append({
                'timestamp': timestamp,
                'flow_value': flow_value
            })
            
            # Limit history size
            max_history = self.constants['DEFAULT_LOOKBACK_PERIODS'] * 2
            if len(self._flow_history[symbol]) > max_history:
                self._flow_history[symbol] = self._flow_history[symbol][-max_history:]
                
            # Calculate derivatives
            derivatives = self._calculate_all_derivatives(symbol)
            
            # Detect regime
            regime_info = self._detect_regime(derivatives)
            
            # Detect institutional activity
            institutional_info = self._detect_institutional_activity(
                derivatives, regime_info
            )
            
            # Check for alerts
            alerts = self._check_alerts(derivatives, regime_info)
            
            # Multi-timeframe analysis if provided
            mtf_analysis = self._analyze_multi_timeframe(
                symbol, flow_data, lookback_data
            ) if lookback_data is not None else {
                'alignment': 0.5,
                'dominant_timeframe': 'current'
            }
            
            # Calculate quality score
            quality_score = self._calculate_quality_score(
                len(self._flow_history[symbol]),
                derivatives
            )
            
            # Create result
            result = FlowPhysicsResult(
                timestamp=timestamp,
                symbol=symbol,
                flow_value=flow_value,
                flow_velocity=derivatives['velocity'],
                flow_acceleration=derivatives['acceleration'],
                flow_jerk=derivatives['jerk'],
                normalized_velocity=derivatives['norm_velocity'],
                normalized_acceleration=derivatives['norm_acceleration'],
                normalized_jerk=derivatives['norm_jerk'],
                current_regime=regime_info['regime'],
                regime_confidence=regime_info['confidence'],
                regime_duration=regime_info['duration'],
                institutional_activity=institutional_info['detected'],
                institutional_direction=institutional_info['direction'],
                institutional_strength=institutional_info['strength'],
                momentum_shift_detected=alerts['momentum_shift'],
                regime_change_detected=alerts['regime_change'],
                extreme_flow_detected=alerts['extreme_flow'],
                timeframe_alignment=mtf_analysis['alignment'],
                dominant_timeframe=mtf_analysis['dominant_timeframe'],
                quality_score=quality_score,
                analysis_metadata={
                    'history_size': len(self._flow_history[symbol]),
                    'derivatives': derivatives,
                    'regime_info': regime_info,
                    'institutional_info': institutional_info,
                    'alerts': alerts
                }
            )
            
            # Store last analysis
            self._last_analysis[symbol] = result
            
            return result
            
        except Exception as e:
            logger.error(f"Error in flow physics analysis: {e}")
            # Return default result on error
            return self._create_default_result(symbol, timestamp, flow_value)
            
    def _calculate_all_derivatives(self, symbol: str) -> Dict[str, float]:
        """Calculate velocity, acceleration, and jerk from flow history."""
        history = self._flow_history[symbol]
        
        if len(history) < 2:
            return {
                'velocity': 0.0,
                'acceleration': 0.0,
                'jerk': 0.0,
                'norm_velocity': 0.0,
                'norm_acceleration': 0.0,
                'norm_jerk': 0.0
            }
            
        # Extract flow values and timestamps
        flow_values = [h['flow_value'] for h in history]
        timestamps = [h['timestamp'].timestamp() for h in history]
        
        # Calculate derivatives
        velocity = self._calculate_derivative(flow_values, timestamps, order=1)
        acceleration = self._calculate_derivative(flow_values, timestamps, order=2)
        jerk = self._calculate_derivative(flow_values, timestamps, order=3)
        
        # Normalize values
        norm_velocity = self._normalize_value(
            velocity, self.constants['EXTREME_VELOCITY_THRESHOLD']
        )
        norm_acceleration = self._normalize_value(
            acceleration, self.constants['EXTREME_ACCELERATION_THRESHOLD']
        )
        norm_jerk = self._normalize_value(
            jerk, self.constants['EXTREME_JERK_THRESHOLD']
        )
        
        return {
            'velocity': velocity,
            'acceleration': acceleration,
            'jerk': jerk,
            'norm_velocity': norm_velocity,
            'norm_acceleration': norm_acceleration,
            'norm_jerk': norm_jerk
        }
        
    def _calculate_derivative(self, values: List[float], 
                            timestamps: List[float],
                            order: int) -> float:
        """Calculate the nth derivative of the most recent values."""
        if len(values) < order + 1:
            return 0.0
            
        # Use last few points for derivative calculation
        n_points = min(order + 2, len(values))
        recent_values = values[-n_points:]
        recent_times = timestamps[-n_points:]
        
        result = np.array(recent_values, dtype=float)
        time_array = np.array(recent_times, dtype=float)
        
        for _ in range(order):
            if len(result) < 2:
                return 0.0
                
            # Calculate differences
            value_diff = np.diff(result)
            time_diff = np.diff(time_array[:len(result)])
            
            # Avoid division by zero
            time_diff[time_diff == 0] = 1e-6
            
            # Calculate derivative
            result = value_diff / time_diff
            time_array = time_array[1:len(result)+1]
            
        # Return the most recent derivative value
        return float(result[-1]) if len(result) > 0 else 0.0
        
    def _normalize_value(self, value: float, threshold: float) -> float:
        """Normalize a value to 0-1 scale using threshold."""
        if threshold == 0:
            return 0.0
        normalized = abs(value) / threshold
        return min(1.0, normalized)
        
    def _detect_regime(self, derivatives: Dict[str, float]) -> Dict[str, Any]:
        """Detect the current flow regime based on derivatives."""
        velocity = derivatives['velocity']
        acceleration = derivatives['acceleration']
        jerk = derivatives['jerk']
        
        # Check for extreme jerk (regime change)
        if abs(jerk) > self.constants['REGIME_CHANGE_JERK_THRESHOLD']:
            regime = 'REGIME_CHANGE'
            confidence = min(1.0, abs(jerk) / self.constants['EXTREME_JERK_THRESHOLD'])
            
        # Check for momentum shift
        elif (velocity > 0 and acceleration < 0) or (velocity < 0 and acceleration > 0):
            regime = 'MOMENTUM_SHIFT'
            confidence = 0.7
            
        # Check for accumulation
        elif velocity > self.constants['MIN_VELOCITY_THRESHOLD'] and acceleration > 0:
            regime = 'ACCUMULATION'
            confidence = min(1.0, (abs(velocity) + abs(acceleration)) / 2)
            
        # Check for distribution
        elif velocity < -self.constants['MIN_VELOCITY_THRESHOLD'] and acceleration < 0:
            regime = 'DISTRIBUTION'
            confidence = min(1.0, (abs(velocity) + abs(acceleration)) / 2)
            
        # Default to steady flow
        else:
            regime = 'STEADY_FLOW'
            confidence = min(1.0, (abs(velocity) + abs(acceleration)) / 2) if velocity != 0 or acceleration != 0 else 0.3
            
        # Calculate regime duration
        symbol = list(self._flow_history.keys())[0] if self._flow_history else 'UNKNOWN'
        duration = self._get_regime_duration(symbol, regime)
        
        return {
            'regime': regime,
            'confidence': confidence,
            'duration': duration,
            'description': self.regimes.get(regime, {}).get('description', '')
        }
        
    def _get_regime_duration(self, symbol: str, current_regime: str) -> Optional[timedelta]:
        """Get how long the current regime has been active."""
        if symbol not in self._regime_history:
            self._regime_history[symbol] = {
                'regime': current_regime,
                'start_time': datetime.now()
            }
            return timedelta(seconds=0)
            
        if self._regime_history[symbol]['regime'] != current_regime:
            # Regime changed
            self._regime_history[symbol] = {
                'regime': current_regime,
                'start_time': datetime.now()
            }
            return timedelta(seconds=0)
            
        # Calculate duration
        start_time = self._regime_history[symbol]['start_time']
        return datetime.now() - start_time
        
    def _detect_institutional_activity(self, 
                                     derivatives: Dict[str, float],
                                     regime_info: Dict[str, Any]) -> Dict[str, Any]:
        """Detect institutional activity patterns."""
        velocity = derivatives['velocity']
        acceleration = derivatives['acceleration']
        norm_velocity = derivatives['norm_velocity']
        norm_acceleration = derivatives['norm_acceleration']
        
        # Check velocity threshold for institutional activity
        institutional_velocity = abs(velocity) > self.constants['INSTITUTIONAL_VELOCITY_THRESHOLD']
        
        # Check acceleration pattern
        institutional_acceleration = abs(acceleration) > self.constants['INSTITUTIONAL_ACCELERATION_THRESHOLD']
        
        # Institutional activity detected if both velocity and acceleration are significant
        detected = institutional_velocity or institutional_acceleration
        
        # Determine direction
        if not detected:
            direction = 'neutral'
            strength = 0.0
        elif regime_info['regime'] == 'ACCUMULATION' or velocity > 0:
            direction = 'accumulation'
            strength = (norm_velocity + norm_acceleration) / 2
        elif regime_info['regime'] == 'DISTRIBUTION' or velocity < 0:
            direction = 'distribution'
            strength = (norm_velocity + norm_acceleration) / 2
        else:
            direction = 'neutral'
            strength = max(norm_velocity, norm_acceleration)
            
        return {
            'detected': detected,
            'direction': direction,
            'strength': min(1.0, strength),
            'velocity_institutional': institutional_velocity,
            'acceleration_institutional': institutional_acceleration
        }
        
    def _check_alerts(self, 
                     derivatives: Dict[str, float],
                     regime_info: Dict[str, Any]) -> Dict[str, bool]:
        """Check for various alert conditions."""
        return {
            'momentum_shift': regime_info['regime'] == 'MOMENTUM_SHIFT',
            'regime_change': regime_info['regime'] == 'REGIME_CHANGE',
            'extreme_flow': (
                derivatives['norm_velocity'] > 0.8 or
                derivatives['norm_acceleration'] > 0.8 or
                derivatives['norm_jerk'] > 0.8
            )
        }
        
    def _analyze_multi_timeframe(self, 
                               symbol: str,
                               current_data: Dict[str, Any],
                               historical_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze flow physics across multiple timeframes."""
        # This is a simplified implementation
        # In practice, you would analyze each timeframe separately
        return {
            'alignment': 0.7,  # Default medium alignment
            'dominant_timeframe': '5m'
        }
        
    def _calculate_quality_score(self, 
                               history_size: int,
                               derivatives: Dict[str, float]) -> float:
        """Calculate quality score for the analysis."""
        # Base score on data availability
        if history_size < 3:
            return 0.3
        elif history_size < 10:
            return 0.5
        elif history_size < 20:
            return 0.7
        else:
            # Check for data consistency
            if any(np.isnan(v) or np.isinf(v) for v in derivatives.values()):
                return 0.6
            return 0.9
            
    def _create_default_result(self, 
                             symbol: str,
                             timestamp: datetime,
                             flow_value: float) -> FlowPhysicsResult:
        """Create a default result when analysis fails."""
        return FlowPhysicsResult(
            timestamp=timestamp,
            symbol=symbol,
            flow_value=flow_value,
            flow_velocity=0.0,
            flow_acceleration=0.0,
            flow_jerk=0.0,
            normalized_velocity=0.0,
            normalized_acceleration=0.0,
            normalized_jerk=0.0,
            current_regime='UNKNOWN',
            regime_confidence=0.0,
            regime_duration=None,
            institutional_activity=False,
            institutional_direction='neutral',
            institutional_strength=0.0,
            momentum_shift_detected=False,
            regime_change_detected=False,
            extreme_flow_detected=False,
            timeframe_alignment=0.0,
            dominant_timeframe='unknown',
            quality_score=0.0,
            analysis_metadata={}
        )
        
    def get_regime_summary(self, symbol: str) -> Dict[str, Any]:
        """Get a summary of regime history for a symbol."""
        if symbol not in self._regime_history:
            return {'current_regime': 'UNKNOWN', 'history': []}
            
        return {
            'current_regime': self._regime_history[symbol]['regime'],
            'regime_start': self._regime_history[symbol]['start_time'],
            'duration': datetime.now() - self._regime_history[symbol]['start_time']
        }
        
    def clear_history(self, symbol: Optional[str] = None):
        """Clear historical data for a symbol or all symbols."""
        if symbol:
            if symbol in self._flow_history:
                del self._flow_history[symbol]
            if symbol in self._regime_history:
                del self._regime_history[symbol]
            if symbol in self._last_analysis:
                del self._last_analysis[symbol]
        else:
            self._flow_history.clear()
            self._regime_history.clear()
            self._last_analysis.clear()