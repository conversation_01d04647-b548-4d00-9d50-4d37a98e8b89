@echo off
REM Liquidity Sweep MCP Server Startup Script

echo Starting Liquidity Sweep MCP Server...

set MCP_DIR=D:\script-work\Liquidity_Sweep\api_robustness
set CONFIG_FILE=%MCP_DIR%\mcp_installation\config\config.json

cd /d "%MCP_DIR%"

REM Check if configuration exists
if not exist "%CONFIG_FILE%" (
    echo ERROR: Configuration file not found: %CONFIG_FILE%
    echo Please update the configuration with your API credentials.
    pause
    exit /b 1
)

REM Start MCP server
echo Starting MCP Server...
py mcp_server_production.py --config "%CONFIG_FILE%" --log-level INFO

pause
