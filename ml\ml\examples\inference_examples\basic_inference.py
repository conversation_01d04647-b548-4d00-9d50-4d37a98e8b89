"""
Basic Inference Service Example

This script demonstrates basic usage of the ML inference service.
"""

import sys
import os
import time
from datetime import datetime

# Add parent directory to path to import ml modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# Import inference service
from ml.ml_inference_service import (
    InferenceRequest,
    InferenceResult,
    create_inference_service
)


def handle_result(result: InferenceResult) -> None:
    """
    Handle inference result callback.
    
    Args:
        result: Result of inference
    """
    print("\n=== Inference Result ===")
    print(f"Request ID: {result.request.request_id}")
    print(f"Model: {result.request.model_name}")
    print(f"Symbol: {result.request.symbol}")
    print(f"Processing time: {result.processing_time:.3f} seconds")
    
    if result.is_error():
        print(f"Error: {result.error}")
    else:
        print("Predictions:")
        for key, value in result.predictions.items():
            print(f"  {key}: {value}")
        
        print("Metadata:")
        for key, value in result.metadata.items():
            print(f"  {key}: {value}")


def main():
    """Main function."""
    print("Starting ML Inference Service Example")
    
    # Create inference service
    service = create_inference_service()
    
    # Start service
    print("Starting inference service...")
    service.start()
    
    # Create inference requests
    print("\nSubmitting pattern recognition request...")
    pattern_request = InferenceRequest(
        model_name="pattern_recognition_model",
        symbol="AAPL",
        timeframe="1h"
    )
    
    pattern_id = service.submit_request(pattern_request, handle_result)
    
    print("\nSubmitting price prediction request...")
    price_request = InferenceRequest(
        model_name="price_prediction_model",
        symbol="AAPL",
        timeframe="1h",
        additional_params={
            "horizon": 5,
            "confidence_interval": 0.95
        }
    )
    
    price_id = service.submit_request(price_request, handle_result)
    
    print("\nSubmitting regime detection request...")
    regime_request = InferenceRequest(
        model_name="regime_detection_model",
        symbol="AAPL",
        timeframe="1d"
    )
    
    regime_id = service.submit_request(regime_request, handle_result)
    
    # Wait for processing
    print("\nWaiting for results...")
    time.sleep(2)
    
    # Check service stats
    stats = service.get_service_stats()
    
    print("\n=== Service Statistics ===")
    print(f"Total requests: {stats['total_requests']}")
    print(f"Successful requests: {stats['successful_requests']}")
    print(f"Error requests: {stats['error_requests']}")
    print(f"Average processing time: {stats['avg_processing_time']:.3f} seconds")
    if 'uptime_formatted' in stats:
        print(f"Uptime: {stats['uptime_formatted']}")
    
    # Stop service
    print("\nStopping inference service...")
    service.stop()
    
    print("\nExample complete!")


if __name__ == "__main__":
    main()
