#!/usr/bin/env python3
"""
Monitoring System Validation Test
Tests all nervous system components and integration
"""

import sys
import time
import json
from datetime import datetime, timedelta
from typing import Dict, Any

def test_system_health_monitor():
    """Test system health monitoring"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[SYSTEM HEALTH MONITOR]")
    
    try:
        from monitoring.system_health_monitor import SystemHealthMonitor
        monitor = SystemHealthMonitor()
        
        # Test registration
        monitor.register_agent("test_agent")
        monitor.register_api("test_api", "https://api.example.com")
        
        # Test heartbeat update
        monitor.update_agent_heartbeat("test_agent", 0.1, True)
        monitor.update_api_status("test_api", 0.2, True, 100)
        
        # Test health summary
        summary = monitor.get_system_health_summary()
        
        if isinstance(summary, dict) and 'timestamp' in summary:
            results['passed'] += 1
            print("+ SystemHealthMonitor: SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("SystemHealthMonitor: Invalid summary structure")
            print("- SystemHealthMonitor: Invalid summary structure")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"SystemHealthMonitor: {str(e)}")
        print(f"- SystemHealthMonitor: FAILED - {str(e)}")
    
    return results

def test_trading_performance_analytics():
    """Test trading performance analytics"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[TRADING PERFORMANCE ANALYTICS]")
    
    try:
        from monitoring.trading_performance_analytics import TradingPerformanceAnalytics
        analytics = TradingPerformanceAnalytics()
        
        # Test trade recording
        sample_trade = {
            'trade_id': 'TEST001',
            'ticker': 'AAPL',
            'strategy': 'test_strategy',
            'entry_time': datetime.now() - timedelta(hours=1),
            'exit_time': datetime.now(),
            'entry_price': 150.00,
            'exit_price': 152.50,
            'position_size': 100,
            'direction': 'long',
            'pnl_realized': 250.0,
            'commission': 2.0,
            'slippage': 0.05,
            'hold_time_minutes': 60,
            'roi_percentage': 1.67,
            'risk_reward_achieved': 2.5
        }
        
        analytics.record_trade(sample_trade)
        
        # Test position update
        analytics.update_position('AAPL', {
            'position_size': 100,
            'entry_price': 150.00,
            'current_price': 151.00,
            'unrealized_pnl': 100.0,
            'strategy': 'test_strategy'
        })
        
        # Test performance summary
        summary = analytics.get_performance_summary()
        
        if (isinstance(summary, dict) and 
            'total_trades' in summary and 
            summary['total_trades'] > 0):
            results['passed'] += 1
            print("+ TradingPerformanceAnalytics: SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("TradingPerformanceAnalytics: Invalid summary")
            print("- TradingPerformanceAnalytics: Invalid summary")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"TradingPerformanceAnalytics: {str(e)}")
        print(f"- TradingPerformanceAnalytics: FAILED - {str(e)}")
    
    return results

def test_alert_notification_system():
    """Test alert and notification system"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[ALERT NOTIFICATION SYSTEM]")
    
    try:
        from monitoring.alert_notification_system import (
            AlertNotificationSystem, AlertSeverity, AlertCategory, NotificationChannel
        )
        
        alert_system = AlertNotificationSystem()
        
        # Add notification channel
        log_channel = NotificationChannel(
            channel_type='log',
            config={},
            enabled=True
        )
        alert_system.add_notification_channel('test_log', log_channel)
        
        # Create test alert
        alert_id = alert_system.create_alert(
            AlertSeverity.WARNING,
            AlertCategory.PERFORMANCE_DEGRADATION,
            "Test Alert",
            "This is a test alert message",
            "test_component",
            {"test_data": 123}
        )
        
        # Test alert acknowledgment
        alert_system.acknowledge_alert(alert_id, "test_user")
        
        # Test alert summary
        summary = alert_system.get_alert_summary()
        
        if (isinstance(summary, dict) and 
            'active_alerts' in summary and
            isinstance(alert_id, str)):
            results['passed'] += 1
            print("+ AlertNotificationSystem: SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("AlertNotificationSystem: Invalid operation")
            print("- AlertNotificationSystem: Invalid operation")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"AlertNotificationSystem: {str(e)}")
        print(f"- AlertNotificationSystem: FAILED - {str(e)}")
    
    return results

def test_operational_dashboards():
    """Test operational dashboards"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[OPERATIONAL DASHBOARDS]")
    
    try:
        from monitoring.operational_dashboards import OperationalDashboards
        dashboards = OperationalDashboards()
        
        # Test dashboard list
        dashboard_list = dashboards.get_dashboard_list()
        
        # Test getting dashboard data
        if dashboard_list:
            dashboard_id = dashboard_list[0]['dashboard_id']
            dashboard_data = dashboards.get_dashboard_data(dashboard_id)
            
            if (isinstance(dashboard_data, dict) and 
                'dashboard_info' in dashboard_data and
                'widget_data' in dashboard_data):
                results['passed'] += 1
                print("+ OperationalDashboards: SUCCESS")
            else:
                results['failed'] += 1
                results['errors'].append("OperationalDashboards: Invalid dashboard data")
                print("- OperationalDashboards: Invalid dashboard data")
        else:
            results['failed'] += 1
            results['errors'].append("OperationalDashboards: No dashboards available")
            print("- OperationalDashboards: No dashboards available")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"OperationalDashboards: {str(e)}")
        print(f"- OperationalDashboards: FAILED - {str(e)}")
    
    return results

def test_nervous_system_orchestrator():
    """Test nervous system orchestrator integration"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[NERVOUS SYSTEM ORCHESTRATOR]")
    
    try:
        from monitoring.nervous_system_orchestrator import NervousSystemOrchestrator
        
        orchestrator = NervousSystemOrchestrator()
        
        # Test component registration
        orchestrator.register_agent("test_agent")
        orchestrator.register_api("test_api", "https://api.example.com")
        
        # Test system status
        status = orchestrator.get_system_status()
        
        # Test health check
        health_check = orchestrator.run_health_check()
        
        if (isinstance(status, dict) and 
            'overall_status' in status and
            isinstance(health_check, dict) and
            'components' in health_check):
            results['passed'] += 1
            print("+ NervousSystemOrchestrator: SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("NervousSystemOrchestrator: Invalid status structure")
            print("- NervousSystemOrchestrator: Invalid status structure")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"NervousSystemOrchestrator: {str(e)}")
        print(f"- NervousSystemOrchestrator: FAILED - {str(e)}")
    
    return results

def test_monitoring_integration():
    """Test full monitoring system integration"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[MONITORING INTEGRATION TEST]")
    
    try:
        from monitoring.nervous_system_orchestrator import NervousSystemOrchestrator
        
        # Create orchestrator with test config
        config = {
            'update_interval': 1,  # Fast updates for testing
            'alerts': {
                'alert_thresholds': {
                    'cpu_critical': 95.0,  # High threshold for testing
                    'memory_critical': 95.0
                }
            }
        }
        
        orchestrator = NervousSystemOrchestrator(config)
        
        # Register components
        orchestrator.register_agent("integration_test_agent")
        orchestrator.register_api("integration_test_api", "https://api.test.com")
        
        # Start monitoring
        orchestrator.start_monitoring()
        
        # Wait for a few monitoring cycles
        time.sleep(3)
        
        # Update components
        orchestrator.update_agent_heartbeat("integration_test_agent", 0.1, True)
        orchestrator.update_api_status("integration_test_api", 0.2, True, 100)
        
        # Record sample trade
        sample_trade = {
            'trade_id': 'INTEGRATION_TEST',
            'ticker': 'TEST',
            'strategy': 'integration_test',
            'entry_time': datetime.now() - timedelta(minutes=30),
            'exit_time': datetime.now(),
            'entry_price': 100.00,
            'exit_price': 102.00,
            'position_size': 50,
            'direction': 'long',
            'pnl_realized': 100.0,
            'commission': 1.0,
            'slippage': 0.02,
            'hold_time_minutes': 30,
            'roi_percentage': 2.0,
            'risk_reward_achieved': 2.0
        }
        
        orchestrator.record_trade(sample_trade)
        
        # Wait for processing
        time.sleep(2)
        
        # Get comprehensive status
        final_status = orchestrator.get_system_status()
        
        # Stop monitoring
        orchestrator.stop_monitoring()
        
        # Validate integration results
        if (isinstance(final_status, dict) and
            'system_health' in final_status and
            'trading_performance' in final_status and
            'alerts' in final_status and
            final_status['trading_performance']['total_trades'] > 0):
            results['passed'] += 1
            print("+ MonitoringIntegration: SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("MonitoringIntegration: Integration validation failed")
            print("- MonitoringIntegration: Integration validation failed")
            
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"MonitoringIntegration: {str(e)}")
        print(f"- MonitoringIntegration: FAILED - {str(e)}")
    
    return results

def combine_results(*result_sets):
    """Combine multiple test result sets"""
    combined = {'passed': 0, 'failed': 0, 'errors': []}
    
    for result_set in result_sets:
        combined['passed'] += result_set['passed']
        combined['failed'] += result_set['failed']
        combined['errors'].extend(result_set['errors'])
    
    return combined

def main():
    """Run complete monitoring system validation"""
    print("CORE MONITORING SYSTEM (NERVOUS SYSTEM) VALIDATION")
    print("=" * 60)
    
    # Run all test suites
    health_results = test_system_health_monitor()
    analytics_results = test_trading_performance_analytics()
    alert_results = test_alert_notification_system()
    dashboard_results = test_operational_dashboards()
    orchestrator_results = test_nervous_system_orchestrator()
    integration_results = test_monitoring_integration()
    
    # Combine all results
    total_results = combine_results(
        health_results, analytics_results, alert_results,
        dashboard_results, orchestrator_results, integration_results
    )
    
    # Summary
    print("\n" + "=" * 60)
    print("MONITORING SYSTEM VALIDATION SUMMARY")
    print("=" * 60)
    print(f"PASSED: {total_results['passed']}")
    print(f"FAILED: {total_results['failed']}")
    
    if total_results['errors']:
        print("\nERRORS:")
        for error in total_results['errors']:
            print(f"  {error}")
    
    success_rate = (total_results['passed'] / (total_results['passed'] + total_results['failed'])) * 100
    print(f"\nSUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("\n+ NERVOUS SYSTEM: FULLY OPERATIONAL")
    elif success_rate >= 85:
        print("\n~ NERVOUS SYSTEM: OPERATIONAL (minor issues)")
    elif success_rate >= 70:
        print("\n~ NERVOUS SYSTEM: FUNCTIONAL (needs attention)")
    else:
        print("\n- NERVOUS SYSTEM: CRITICAL ISSUES")
    
    print(f"\nMONITORING SYSTEM STATUS: {'READY' if success_rate >= 90 else 'REQUIRES FIXES'}")
    
    return success_rate >= 90

if __name__ == "__main__":
    main()
