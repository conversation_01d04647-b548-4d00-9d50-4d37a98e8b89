task_id: D-01
name: Live Data Gateway Agent
version: 1.0.0
description: >
  Dual-mode data ingestion supporting MCP (primary) and Polygon (fallback)
  for real-time market data and options chains.
inputs:
  - ticker_list            # list[str]
  - source: "mcp"          # "mcp" or "polygon"  (default: mcp)
  - api_key                # str  (if polygon)
  - bar_tf: "1"            # timeframe in minutes
outputs:
  files:
    - path: data/live/{{date}}/{{ticker}}_bars.parquet
      must_exist: true
    - path: data/live/{{date}}/{{ticker}}_options.parquet
      must_exist: true
  metadata:
    - bars_count: integer
    - options_count: integer
    - source_used: string
success_criteria:
  perf_budget:
    max_runtime_ms: 15000
  data_quality:
    min_bars: 10
    min_options: 50
  availability:
    mcp_primary: true
    polygon_fallback: true
