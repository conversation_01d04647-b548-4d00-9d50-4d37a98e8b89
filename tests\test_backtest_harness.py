import unittest
import sys
from pathlib import Path
from datetime import datetime, timed<PERSON>ta
from typing import Dict
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

# Add the parent directory to the sys.path to allow imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from backtesting.run_backtest import (
    BacktestHarness, BacktestConfig, BacktestResults, Trade, Order, 
    OrderType, OrderStatus, RiskLevel, AllocationStrategy
)


class TestBacktestHarness(unittest.TestCase):
    """Test cases for BacktestHarness."""

    def setUp(self):
        """Set up test fixtures."""
        self.config = BacktestConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 1, 31),
            initial_capital=100000.0,
            commission_rate=0.001,
            slippage_bps=2.0
        )
        self.harness = BacktestHarness(self.config)

    def test_initialization(self):
        """Test BacktestHarness initialization."""
        self.assertEqual(self.harness.config, self.config)
        self.assertEqual(self.harness.portfolio_value, 100000.0)
        self.assertEqual(self.harness.cash, 100000.0)
        self.assertEqual(self.harness.peak_value, 100000.0)
        self.assertEqual(self.harness.current_drawdown, 0.0)
        self.assertEqual(len(self.harness.orders), 0)
        self.assertEqual(len(self.harness.trades), 0)

    def test_update_portfolio_value(self):
        """Test portfolio value update."""
        # Add a position
        # Mock position_manager as it's not part of the BacktestHarness core logic
        self.harness.position_manager = Mock()
        self.harness.position_manager.add_position.return_value = None

        self.harness.position_manager.add_position("AAPL", 100, 150.0)
        self.harness.cash = 85000.0  # 100000 - (100 * 150)
        
        # Create mock market data
        market_data = pd.DataFrame({
            'symbol': ['AAPL'],
            'close': [160.0],
            'timestamp': [datetime.now()]
        })
        
        self.harness._update_positions(market_data)
        self.harness._update_portfolio_value(market_data)
        
        # Portfolio value should be cash + position value
        expected_value = 85000.0 + (100 * 160.0)  # 101000
        self.assertEqual(self.harness.portfolio_value, expected_value)

    def test_calculate_market_impact(self):
        """Test market impact calculation."""
        symbol_data = pd.DataFrame({
            'volume': [10000, 12000, 8000]
        })
        
        # Small order relative to volume
        impact1 = self.harness._calculate_market_impact(100, symbol_data)
        
        # Large order relative to volume
        impact2 = self.harness._calculate_market_impact(5000, symbol_data)
        
        # Large order should have higher impact
        self.assertGreater(impact2, impact1)
        
        # Test with no volume data
        empty_data = pd.DataFrame({'price': [100, 101, 102]})
        impact3 = self.harness._calculate_market_impact(100, empty_data)
        self.assertEqual(impact3, 1.0)  # Default value

    def test_check_risk_limits(self):
        """Test risk limit checking."""
        # Normal situation - no breach
        self.harness.current_drawdown = 0.05  # 5% drawdown
        self.assertFalse(self.harness._check_risk_limits())
        
        # Breach max drawdown
        self.harness.current_drawdown = 0.20  # 20% drawdown (exceeds 15% limit)
        self.assertTrue(self.harness._check_risk_limits())

    def test_get_current_position_weights(self):
        """Test current position weight calculation."""
        # Add positions
        # Mock position_manager
        self.harness.position_manager = Mock()
        self.harness.position_manager.add_position.return_value = None
        self.harness.position_manager.positions = {"AAPL": {'quantity': 100, 'avg_price': 150.0}, "GOOGL": {'quantity': 10, 'avg_price': 2500.0}}

        self.harness.portfolio_value = 100000.0
        
        weights = self.harness._get_current_position_weights()
        
        self.assertAlmostEqual(weights["AAPL"], 0.15, places=3)  # 15,000/100,000
        self.assertAlmostEqual(weights["GOOGL"], 0.25, places=3)  # 25,000/100,000

    def test_get_current_position_weights_zero_portfolio(self):
        """Test position weights with zero portfolio value."""
        self.harness.portfolio_value = 0.0
        weights = self.harness._get_current_position_weights()
        self.assertEqual(len(weights), 0)

    def test_trade_pnl_calculation(self):
        """Test trade P&L calculation."""
        buy_trade = Trade(
            timestamp=datetime.now(),
            symbol="AAPL",
            side="buy",
            quantity=100,
            price=150.0,
            commission=15.0,
            slippage=0.05,
            order_id="order_1"
        )
        
        sell_trade = Trade(
            timestamp=datetime.now(),
            symbol="AAPL",
            side="sell",
            quantity=100,
            price=160.0,
            commission=16.0,
            slippage=0.05,
            order_id="order_2"
        )
        
        # Buy trade should have negative P&L (cost)
        buy_pnl = self.harness._trade_pnl(buy_trade)
        self.assertEqual(buy_pnl, -15000.0)  # -100 * 150
        
        # Sell trade should have positive P&L (income)
        sell_pnl = self.harness._trade_pnl(sell_trade)
        self.assertEqual(sell_pnl, 16000.0)  # 100 * 160

    def test_record_state(self):
        """Test state recording functionality."""
        self.harness.current_time = datetime(2023, 1, 1, 10, 0)
        self.harness.portfolio_value = 105000.0
        self.harness.cash = 90000.0
        self.harness.current_drawdown = 0.02
        
        # Add a position
        # Mock position_manager
        self.harness.position_manager = Mock()
        self.harness.position_manager.add_position.return_value = None
        self.harness.position_manager.positions = {"AAPL": {'quantity': 100, 'avg_price': 150.0}}

        initial_history_length = len(self.harness.portfolio_history)
        initial_position_length = len(self.harness.position_history)
        
        self.harness._record_state()
        
        # Check that history was recorded
        self.assertEqual(len(self.harness.portfolio_history), initial_history_length + 1)
        self.assertEqual(len(self.harness.position_history), initial_position_length + 1)
        
        # Check portfolio history content
        timestamp, value = self.harness.portfolio_history[-1]
        self.assertEqual(timestamp, self.harness.current_time)
        self.assertEqual(value, 105000.0)
        
        # Check position history content
        position_state = self.harness.position_history[-1]
        self.assertEqual(position_state['timestamp'], self.harness.current_time)
        self.assertEqual(position_state['portfolio_value'], 105000.0)
        self.assertEqual(position_state['cash'], 90000.0)
        self.assertIn('AAPL', position_state['positions'])

    def test_simulate_order_fill_buy_order(self):
        """Test order fill simulation for buy orders."""
        
        order = Order(
            order_id="test_order",
            timestamp=datetime.now(),
            symbol="AAPL",
            side="buy",
            quantity=100,
            order_type=OrderType.MARKET
        )
        
        symbol_data = pd.DataFrame({
            'close': [150.0],
            'volume': [10000]
        })
        
        initial_cash = self.harness.cash
        
        result = self.harness._simulate_order_fill(order, symbol_data)
        
        self.assertTrue(result)
        self.assertEqual(order.status, OrderStatus.FILLED)
        self.assertEqual(order.filled_quantity, 100)
        self.assertGreater(order.avg_fill_price, 150.0)  # Should include slippage
        self.assertLess(self.harness.cash, initial_cash)  # Cash should decrease

    def test_simulate_order_fill_insufficient_cash(self):
        """Test order fill with insufficient cash."""
        self.harness.cash = 1000.0  # Very low cash
        
        order = Order(
            order_id="test_order",
            timestamp=datetime.now(),
            symbol="AAPL",
            side="buy",
            quantity=100,
            order_type=OrderType.MARKET
        )
        
        symbol_data = pd.DataFrame({
            'close': [150.0],
            'volume': [10000]
        })
        
        result = self.harness._simulate_order_fill(order, symbol_data)
        
        self.assertFalse(result)
        self.assertEqual(order.status, OrderStatus.REJECTED)

    def test_simulate_order_fill_sell_order(self):
        """Test order fill simulation for sell orders."""
        # First add a position to sell
        # Mock position_manager
        self.harness.position_manager = Mock()
        self.harness.position_manager.add_position.return_value = None
        self.harness.position_manager.positions = {"AAPL": {'quantity': 100, 'avg_price': 140.0}}

        order = Order(
            order_id="test_order",
            timestamp=datetime.now(),
            symbol="AAPL",
            side="sell",
            quantity=100,
            order_type=OrderType.MARKET
        )
        
        symbol_data = pd.DataFrame({
            'close': [150.0],
            'volume': [10000]
        })
        
        initial_cash = self.harness.cash
        
        result = self.harness._simulate_order_fill(order, symbol_data)
        
        self.assertTrue(result)
        self.assertEqual(order.status, OrderStatus.FILLED)
        self.assertGreater(self.harness.cash, initial_cash)  # Cash should increase

    def create_sample_data(self, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """Helper method to create sample market data."""
        dates = pd.date_range(start_date, end_date, freq='1D')
        data = []
        
        for i, date in enumerate(dates):
            for symbol in ['AAPL', 'GOOGL']:
                base_price = 150.0 if symbol == 'AAPL' else 2500.0
                price = base_price * (1 + 0.01 * np.sin(i * 0.1))  # Synthetic price movement
                
                data.append({
                    'timestamp': date,
                    'symbol': symbol,
                    'open': price * 0.99,
                    'high': price * 1.02,
                    'low': price * 0.98,
                    'close': price,
                    'volume': 10000 + np.random.randint(-2000, 2000)
                })
        
        return pd.DataFrame(data)

    def create_sample_signal_generator(self):
        """Helper method to create a sample signal generator."""
        def signal_generator(data: pd.DataFrame, timestamp: datetime) -> Dict[str, Dict]:
            # Simple signal: buy when price is below 150 for AAPL
            signals = {}
            
            current_data = data[data['timestamp'] == timestamp]
            for _, row in current_data.iterrows():
                if row['symbol'] == 'AAPL':
                    signal_strength = 0.8 if row['close'] < 150 else 0.3
                    signals['AAPL'] = {
                        'signal_strength': signal_strength,
                        'volatility': 0.2,
                        'price': row['close']
                    }
            
            return signals
        
        return signal_generator

    def test_iterate_market_data(self):
        """Test market data iteration."""
        sample_data = self.create_sample_data(
            datetime(2023, 1, 1), 
            datetime(2023, 1, 3)
        )
        
        iterations = list(self.harness._iterate_market_data(sample_data))
        
        # Should have data for each day
        self.assertGreater(len(iterations), 0)
        
        # Each iteration should return timestamp and DataFrame
        for timestamp, market_slice in iterations:
            self.assertIsInstance(timestamp, datetime)
            self.assertIsInstance(market_slice, pd.DataFrame)
            self.assertFalse(market_slice.empty)

    @patch('backtesting.run_backtest.logger')
    def test_run_backtest_empty_data(self, mock_logger):
        """Test backtest with empty data."""
        def empty_data_loader(start_date, end_date):
            return pd.DataFrame()
        
        signal_generator = self.create_sample_signal_generator()
        
        with self.assertRaises(ValueError) as context:
            self.harness.run_backtest(empty_data_loader, signal_generator, ['AAPL'])
        
        self.assertIn("No historical data available", str(context.exception))


class TestBacktestIntegration(unittest.TestCase):
    """Integration tests for the complete backtesting workflow."""

    def setUp(self):
        """Set up integration test fixtures."""
        self.config = BacktestConfig(
            start_date=datetime(2023, 1, 1),
            end_date=datetime(2023, 1, 10),
            initial_capital=100000.0,
            commission_rate=0.001,
            slippage_bps=2.0
        )
        self.harness = BacktestHarness(self.config)

    def create_realistic_data_loader(self):
        """Create a realistic data loader for testing."""
        def data_loader(start_date: datetime, end_date: datetime) -> pd.DataFrame:
            dates = pd.date_range(start_date, end_date, freq='1D')
            data = []
            
            for i, date in enumerate(dates):
                base_price = 150.0
                # Add some realistic price movement
                price = base_price * (1 + 0.02 * np.sin(i * 0.2) + 0.001 * np.random.randn())
                
                data.append({
                    'timestamp': date,
                    'symbol': 'AAPL',
                    'open': price * (1 + 0.001 * np.random.randn()),
                    'high': price * (1 + abs(0.01 * np.random.randn())),
                    'low': price * (1 - abs(0.01 * np.random.randn())),
                    'close': price,
                    'volume': 10000 + np.random.randint(-2000, 2000)
                })
            
            return pd.DataFrame(data)
        
        return data_loader

    def create_realistic_signal_generator(self):
        """Create a realistic signal generator for testing."""
        def signal_generator(data: pd.DataFrame, timestamp: datetime) -> Dict[str, Dict]:
            # Get recent data for signal calculation
            recent_data = data[data['timestamp'] <= timestamp].tail(5)
            
            if len(recent_data) < 2:
                return {}
            
            # Simple momentum signal
            recent_prices = recent_data['close'].values
            momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            
            signal_strength = max(0.1, min(0.9, 0.5 + momentum * 10))
            volatility = np.std(recent_prices) / np.mean(recent_prices)
            
            return {
                'AAPL': {
                    'signal_strength': signal_strength,
                    'volatility': volatility,
                    'price': recent_prices[-1]
                }
            }
        
        return signal_generator

    def test_complete_backtest_workflow(self):
        """Test complete backtesting workflow."""
        data_loader = self.create_realistic_data_loader()
        signal_generator = self.create_realistic_signal_generator()
        
        # This should complete without errors
        results = self.harness.run_backtest(data_loader, signal_generator, ['AAPL'])
        
        # Verify results structure
        self.assertIsInstance(results, BacktestResults)
        self.assertEqual(results.start_date, self.config.start_date)
        self.assertEqual(results.end_date, self.config.end_date)
        self.assertIsInstance(results.total_return, float)
        self.assertIsInstance(results.sharpe_ratio, float)
        self.assertGreaterEqual(results.total_trades, 0)
        self.assertGreaterEqual(results.final_portfolio_value, 0)


if __name__ == '__main__':
    unittest.main()