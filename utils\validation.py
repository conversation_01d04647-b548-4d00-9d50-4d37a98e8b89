#!/usr/bin/env python3
"""
Validation utilities for specialized agents
"""

from functools import wraps
from typing import Dict, Any, Callable
import logging

logger = logging.getLogger(__name__)

def validate_input(func: Callable) -> Callable:
    """Decorator to validate input data"""
    @wraps(func)
    def wrapper(self, data: Dict[str, Any]) -> Dict[str, Any]:
        if not isinstance(data, dict):
            raise ValueError("Input data must be a dictionary")
        
        return func(self, data)
    
    return wrapper

def validate_output(func: Callable) -> Callable:
    """Decorator to validate output data"""
    @wraps(func)
    def wrapper(*args, **kwargs) -> Dict[str, Any]:
        result = func(*args, **kwargs)
        
        if not isinstance(result, dict):
            raise ValueError("Output must be a dictionary")
        
        return result
    
    return wrapper
