#!/usr/bin/env python3
"""
CORE Flow Detection System - Volume Analyzer

Stripped, optimized volume profile analyzer focusing on:
- Point of Control (POC) identification
- Value Area High/Low (VAH/VAL) calculation  
- High Volume Nodes detection
- Flow concentration mapping

Clean implementation with essential volume profile mathematics only.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Import from CORE data layer
from data.factor_spec import FactorData, DirectionBias, TimeFrame, create_factor
from config.constants import VOLUME_ANALYSIS

class VolumeAnalyzer:
    """
    Core volume profile analyzer - essential volume mathematics.
    Clean, focused implementation for flow concentration detection.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Core parameters from constants
        self.num_bins = VOLUME_ANALYSIS['POC_CALCULATION_BINS']
        self.value_area_pct = VOLUME_ANALYSIS['VALUE_AREA_PERCENTAGE']
        self.hvn_threshold = VOLUME_ANALYSIS['HIGH_VOLUME_NODE_THRESHOLD']
        self.min_volume = VOLUME_ANALYSIS['MIN_VOLUME_FOR_ANALYSIS']
        self.volume_quality_threshold = VOLUME_ANALYSIS['VOLUME_QUALITY_THRESHOLD']
        
        # Results cache
        self._profile_cache = {}
    
    def analyze_factors(self, data_package: Dict[str, Any]) -> List[FactorData]:
        """
        Main analyzer interface - generate volume profile factors.
        
        Args:
            data_package: {ticker, current_price, mtf_data, timestamp}
            
        Returns:
            List[FactorData]: Volume profile factors
        """
        ticker = data_package.get('ticker', 'UNKNOWN')
        current_price = data_package.get('current_price', 0.0)
        mtf_data = data_package.get('mtf_data', {})
        timestamp = data_package.get('timestamp', datetime.now())
        
        factors = []
        
        try:
            # Get primary timeframe data
            price_data = self._get_primary_timeframe_data(mtf_data)
            if price_data is None or price_data.empty:
                return factors
            
            # Calculate volume profile
            profile_result = self._calculate_volume_profile(ticker, price_data, current_price)
            
            # Generate factors from profile
            factors.extend(self._create_volume_factors(
                ticker, profile_result, timestamp, current_price
            ))
            
            return factors
            
        except Exception as e:
            print(f"Volume analysis error for {ticker}: {e}")
            return factors
    
    def _get_primary_timeframe_data(self, mtf_data: Dict[str, pd.DataFrame]) -> Optional[pd.DataFrame]:
        """Get best available timeframe data."""
        # Prefer 1h for volume profile, fallback to available
        preferred_order = ['1h', '15m', '30m', '4h', '5m', '1m']
        
        for tf in preferred_order:
            if tf in mtf_data and not mtf_data[tf].empty:
                return mtf_data[tf]
        
        # Return any available data
        for tf_data in mtf_data.values():
            if not tf_data.empty:
                return tf_data
        
        return None
    
    def _calculate_volume_profile(self, ticker: str, price_data: pd.DataFrame, 
                                current_price: float) -> Dict[str, Any]:
        """Calculate volume profile with POC, VAH, VAL, and HVNs."""
        
        try:
            # Validate data
            if not self._validate_volume_data(price_data):
                return self._empty_profile()
            
            # Create price bins
            price_min = price_data['low'].min()
            price_max = price_data['high'].max()
            
            if price_max <= price_min:
                return self._empty_profile()
            
            # Create uniform price levels
            price_levels = np.linspace(price_min, price_max, self.num_bins)
            volume_at_price = np.zeros(len(price_levels))
            
            # Distribute volume across price levels
            for _, bar in price_data.iterrows():
                bar_range = bar['high'] - bar['low']
                if bar_range <= 0:
                    continue
                
                # Find price levels within this bar's range
                in_range = (price_levels >= bar['low']) & (price_levels <= bar['high'])
                levels_in_range = np.sum(in_range)
                
                if levels_in_range > 0:
                    # Distribute volume proportionally
                    volume_per_level = bar['volume'] / levels_in_range
                    volume_at_price[in_range] += volume_per_level
            
            # Calculate profile metrics
            poc_idx, poc_price, poc_volume = self._find_poc(price_levels, volume_at_price)
            vah, val = self._calculate_value_area(price_levels, volume_at_price)
            hvn_levels = self._find_high_volume_nodes(price_levels, volume_at_price)
            
            # Calculate flow concentration
            concentration_score = self._calculate_flow_concentration(
                price_levels, volume_at_price, current_price
            )
            
            # Calculate volume quality
            volume_quality = self._calculate_volume_quality(price_data)
            
            profile_result = {
                'poc_price': poc_price,
                'poc_volume': poc_volume,
                'vah': vah,
                'val': val,
                'hvn_levels': hvn_levels,
                'price_levels': price_levels,
                'volume_at_price': volume_at_price,
                'concentration_score': concentration_score,
                'volume_quality': volume_quality,
                'total_volume': price_data['volume'].sum(),
                'value_area_volume': self._calculate_value_area_volume(price_levels, volume_at_price, vah, val)
            }
            
            # Cache result
            self._profile_cache[ticker] = profile_result
            
            return profile_result
            
        except Exception as e:
            print(f"Volume profile calculation error: {e}")
            return self._empty_profile()
    
    def _validate_volume_data(self, price_data: pd.DataFrame) -> bool:
        """Validate volume data quality."""
        try:
            # Check required columns
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in price_data.columns for col in required_cols):
                return False
            
            # Check minimum data
            if len(price_data) < 10:
                return False
            
            # Check volume threshold
            total_volume = price_data['volume'].sum()
            if total_volume < self.min_volume:
                return False
            
            # Check for excessive zero volumes
            zero_volume_pct = (price_data['volume'] == 0).sum() / len(price_data)
            if zero_volume_pct > 0.3:  # More than 30% zero volume
                return False
            
            return True
            
        except Exception:
            return False
    
    def _find_poc(self, price_levels: np.ndarray, volume_at_price: np.ndarray) -> Tuple[int, float, float]:
        """Find Point of Control (highest volume price level)."""
        if len(volume_at_price) == 0:
            return 0, 0.0, 0.0
        
        poc_idx = np.argmax(volume_at_price)
        poc_price = price_levels[poc_idx]
        poc_volume = volume_at_price[poc_idx]
        
        return poc_idx, poc_price, poc_volume
    
    def _calculate_value_area(self, price_levels: np.ndarray, volume_at_price: np.ndarray) -> Tuple[float, float]:
        """Calculate Value Area High (VAH) and Value Area Low (VAL)."""
        if len(volume_at_price) == 0:
            return 0.0, 0.0
        
        total_volume = np.sum(volume_at_price)
        target_volume = total_volume * self.value_area_pct
        
        # Start from POC and expand
        poc_idx = np.argmax(volume_at_price)
        
        # Initialize value area
        va_volume = volume_at_price[poc_idx]
        va_low_idx = poc_idx
        va_high_idx = poc_idx
        
        # Expand value area to include target volume
        while va_volume < target_volume and (va_low_idx > 0 or va_high_idx < len(price_levels) - 1):
            
            # Determine which direction to expand
            expand_up = False
            expand_down = False
            
            if va_high_idx < len(price_levels) - 1 and va_low_idx > 0:
                # Can expand both ways - choose direction with more volume
                volume_above = volume_at_price[va_high_idx + 1]
                volume_below = volume_at_price[va_low_idx - 1]
                expand_up = volume_above >= volume_below
                expand_down = not expand_up
            elif va_high_idx < len(price_levels) - 1:
                expand_up = True
            elif va_low_idx > 0:
                expand_down = True
            else:
                break
            
            # Expand in chosen direction
            if expand_up:
                va_high_idx += 1
                va_volume += volume_at_price[va_high_idx]
            elif expand_down:
                va_low_idx -= 1
                va_volume += volume_at_price[va_low_idx]
        
        vah = price_levels[va_high_idx]
        val = price_levels[va_low_idx]
        
        return vah, val
    
    def _find_high_volume_nodes(self, price_levels: np.ndarray, volume_at_price: np.ndarray) -> List[float]:
        """Find High Volume Nodes (significant volume concentrations)."""
        if len(volume_at_price) == 0:
            return []
        
        # Calculate volume statistics
        mean_volume = np.mean(volume_at_price)
        std_volume = np.std(volume_at_price)
        
        # Threshold for HVN detection
        hvn_threshold = mean_volume + (self.hvn_threshold * std_volume)
        
        # Find indices above threshold
        hvn_indices = np.where(volume_at_price >= hvn_threshold)[0]
        
        # Convert to price levels
        hvn_levels = [price_levels[idx] for idx in hvn_indices]
        
        # Merge nearby levels (within 1% of price)
        if hvn_levels:
            merged_levels = []
            current_group = [hvn_levels[0]]
            
            for level in hvn_levels[1:]:
                if abs(level - current_group[-1]) / current_group[-1] < 0.01:  # Within 1%
                    current_group.append(level)
                else:
                    # Add average of current group
                    merged_levels.append(np.mean(current_group))
                    current_group = [level]
            
            # Add final group
            merged_levels.append(np.mean(current_group))
            
            return merged_levels
        
        return hvn_levels
    
    def _calculate_flow_concentration(self, price_levels: np.ndarray, volume_at_price: np.ndarray, 
                                    current_price: float) -> float:
        """Calculate flow concentration score around current price."""
        if len(volume_at_price) == 0:
            return 0.0
        
        try:
            # Find price level closest to current price
            current_idx = np.argmin(np.abs(price_levels - current_price))
            
            # Define concentration window (2% around current price)
            price_range = 0.02 * current_price
            window_indices = np.where(
                np.abs(price_levels - current_price) <= price_range
            )[0]
            
            if len(window_indices) == 0:
                return 0.0
            
            # Calculate concentration
            window_volume = np.sum(volume_at_price[window_indices])
            total_volume = np.sum(volume_at_price)
            
            if total_volume == 0:
                return 0.0
            
            concentration_ratio = window_volume / total_volume
            
            # Normalize to 0-1 scale
            concentration_score = min(1.0, concentration_ratio * 5)  # Scale factor
            
            return concentration_score
            
        except Exception:
            return 0.0
    
    def _calculate_volume_quality(self, price_data: pd.DataFrame) -> float:
        """Calculate volume data quality score."""
        try:
            # Check volume consistency
            volumes = price_data['volume'].values
            
            # Remove zeros for quality calculation
            non_zero_volumes = volumes[volumes > 0]
            if len(non_zero_volumes) == 0:
                return 0.0
            
            # Calculate coefficient of variation
            mean_vol = np.mean(non_zero_volumes)
            std_vol = np.std(non_zero_volumes)
            cv = std_vol / mean_vol if mean_vol > 0 else 0
            
            # Quality inversely related to variability
            quality_score = max(0.0, 1.0 - (cv / 3.0))  # Normalize CV
            
            # Adjust for data completeness
            completeness = len(non_zero_volumes) / len(volumes)
            quality_score *= completeness
            
            return min(1.0, quality_score)
            
        except Exception:
            return 0.5
    
    def _calculate_value_area_volume(self, price_levels: np.ndarray, volume_at_price: np.ndarray,
                                   vah: float, val: float) -> float:
        """Calculate total volume within value area."""
        try:
            va_indices = np.where((price_levels >= val) & (price_levels <= vah))[0]
            return np.sum(volume_at_price[va_indices])
        except Exception:
            return 0.0
    
    def _empty_profile(self) -> Dict[str, Any]:
        """Return empty profile result."""
        return {
            'poc_price': 0.0,
            'poc_volume': 0.0,
            'vah': 0.0,
            'val': 0.0,
            'hvn_levels': [],
            'price_levels': np.array([]),
            'volume_at_price': np.array([]),
            'concentration_score': 0.0,
            'volume_quality': 0.0,
            'total_volume': 0.0,
            'value_area_volume': 0.0
        }
    
    def _create_volume_factors(self, ticker: str, profile_result: Dict[str, Any], 
                             timestamp: datetime, current_price: float) -> List[FactorData]:
        """Create FactorData objects from volume profile analysis."""
        factors = []
        
        try:
            # POC factor - if current price near POC
            poc_price = profile_result['poc_price']
            if poc_price > 0:
                price_diff_pct = abs(current_price - poc_price) / current_price
                
                if price_diff_pct < 0.02:  # Within 2% of POC
                    direction = DirectionBias.NEUTRAL  # POC is typically a pivot point
                    strength = min(1.0, profile_result['concentration_score'] * 1.5)
                    
                    factor = create_factor(
                        analyzer_name="VolumeAnalysis",
                        factor_name="POC_Proximity",
                        direction=direction,
                        strength=strength,
                        timeframe=TimeFrame.HOUR_1,
                        timestamp=timestamp,
                        confidence_score=profile_result['volume_quality'],
                        raw_value=poc_price,
                        metadata={
                            'poc_price': poc_price,
                            'poc_volume': profile_result['poc_volume'],
                            'price_diff_pct': price_diff_pct
                        }
                    )
                    factors.append(factor)
            
            # Value Area factor
            vah = profile_result['vah']
            val = profile_result['val']
            
            if vah > 0 and val > 0:
                in_value_area = val <= current_price <= vah
                
                if in_value_area:
                    # Inside value area - typically consolidation
                    direction = DirectionBias.NEUTRAL
                    strength = 0.7
                else:
                    # Outside value area - potential directional move
                    direction = DirectionBias.BULLISH if current_price > vah else DirectionBias.BEARISH
                    strength = 0.8
                
                factor = create_factor(
                    analyzer_name="VolumeAnalysis",
                    factor_name="Value_Area_Position",
                    direction=direction,
                    strength=strength,
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=profile_result['volume_quality'],
                    raw_value=current_price,
                    metadata={
                        'vah': vah,
                        'val': val,
                        'in_value_area': in_value_area,
                        'value_area_volume': profile_result['value_area_volume']
                    }
                )
                factors.append(factor)
            
            # High Volume Node factor
            hvn_levels = profile_result['hvn_levels']
            if hvn_levels:
                # Check if current price is near any HVN
                for hvn_price in hvn_levels:
                    price_diff_pct = abs(current_price - hvn_price) / current_price
                    
                    if price_diff_pct < 0.015:  # Within 1.5% of HVN
                        direction = DirectionBias.NEUTRAL  # HVNs are support/resistance
                        strength = min(1.0, profile_result['concentration_score'] * 1.2)
                        
                        factor = create_factor(
                            analyzer_name="VolumeAnalysis",
                            factor_name="HVN_Proximity",
                            direction=direction,
                            strength=strength,
                            timeframe=TimeFrame.HOUR_1,
                            timestamp=timestamp,
                            confidence_score=profile_result['volume_quality'],
                            raw_value=hvn_price,
                            metadata={
                                'hvn_price': hvn_price,
                                'price_diff_pct': price_diff_pct,
                                'total_hvn_count': len(hvn_levels)
                            }
                        )
                        factors.append(factor)
            
            # Flow Concentration factor
            concentration_score = profile_result['concentration_score']
            if concentration_score > 0.5:
                direction = DirectionBias.NEUTRAL  # High concentration suggests consolidation
                strength = concentration_score
                
                factor = create_factor(
                    analyzer_name="VolumeAnalysis",
                    factor_name="Flow_Concentration",
                    direction=direction,
                    strength=strength,
                    timeframe=TimeFrame.HOUR_1,
                    timestamp=timestamp,
                    confidence_score=profile_result['volume_quality'],
                    raw_value=concentration_score,
                    metadata={
                        'concentration_score': concentration_score,
                        'total_volume': profile_result['total_volume']
                    }
                )
                factors.append(factor)
            
        except Exception as e:
            print(f"Error creating volume factors: {e}")
        
        return factors
