"""
Order Book Features Module

This module provides advanced order book analysis and feature extraction
for liquidity analysis, focusing on order book imbalance, depth, and dynamics.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
from datetime import datetime

# Setup logger
logger = logging.getLogger('order_book_features')
logger.setLevel(logging.INFO)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

class OrderBookFeatureExtractor:
    """
    Extracts features from order book data for liquidity analysis.

    This class provides methods for calculating order book imbalance,
    depth, resilience, and other microstructure features.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the order book feature extractor.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Default configuration
        self.depth_levels = self.config.get('depth_levels', 10)
        self.price_grouping = self.config.get('price_grouping', 0.01)
        self.volume_percentiles = self.config.get('volume_percentiles', [25, 50, 75, 90, 95])
        self.time_windows = self.config.get('time_windows', [1, 5, 15, 30, 60])  # minutes

        logger.info("Initialized OrderBookFeatureExtractor")

    def extract_features(self,
                        order_book_data: pd.DataFrame,
                        price_data: Optional[pd.DataFrame] = None,
                        current_price: Optional[float] = None) -> pd.DataFrame:
        """
        Extract features from order book data.

        Args:
            order_book_data: DataFrame with order book data
                Expected columns: timestamp, price, bid_size, ask_size
            price_data: Optional DataFrame with OHLCV price data
            current_price: Optional current price (if not provided, will use last price from price_data)

        Returns:
            DataFrame with order book features
        """
        if order_book_data.empty:
            logger.error("Empty order book data provided")
            return pd.DataFrame()

        # Ensure required columns exist
        required_columns = ['timestamp', 'price', 'bid_size', 'ask_size']
        missing_columns = [col for col in required_columns if col not in order_book_data.columns]
        if missing_columns:
            logger.error(f"Missing required columns in order book data: {missing_columns}")
            return pd.DataFrame()

        # Get current price if not provided
        if current_price is None and price_data is not None and not price_data.empty:
            current_price = price_data['close'].iloc[-1]
        elif current_price is None:
            # Try to estimate from order book
            mid_prices = (order_book_data['bid_price'] + order_book_data['ask_price']) / 2
            current_price = mid_prices.iloc[-1] if 'bid_price' in order_book_data.columns and 'ask_price' in order_book_data.columns else order_book_data['price'].iloc[-1]

        logger.info(f"Extracting order book features (current price: {current_price})")

        # Initialize result DataFrame
        result = pd.DataFrame(index=[0])  # Single row for current state

        # Extract basic order book features
        basic_features = self._extract_basic_features(order_book_data, current_price)
        result = pd.concat([result, pd.DataFrame([basic_features])], axis=1)

        # Extract imbalance features
        imbalance_features = self._extract_imbalance_features(order_book_data, current_price)
        result = pd.concat([result, pd.DataFrame([imbalance_features])], axis=1)

        # Extract depth features
        depth_features = self._extract_depth_features(order_book_data, current_price)
        result = pd.concat([result, pd.DataFrame([depth_features])], axis=1)

        # Extract resilience features if time series data is available
        if 'timestamp' in order_book_data.columns and len(order_book_data['timestamp'].unique()) > 1:
            resilience_features = self._extract_resilience_features(order_book_data, current_price)
            result = pd.concat([result, pd.DataFrame([resilience_features])], axis=1)

        # Extract price impact features
        impact_features = self._extract_price_impact_features(order_book_data, current_price)
        result = pd.concat([result, pd.DataFrame([impact_features])], axis=1)

        logger.info(f"Extracted {result.shape[1]} order book features")
        return result

    def _extract_basic_features(self,
                              order_book_data: pd.DataFrame,
                              current_price: float) -> Dict[str, float]:
        """
        Extract basic features from order book data.

        Args:
            order_book_data: DataFrame with order book data
            current_price: Current price

        Returns:
            Dictionary with basic features
        """
        features = {}

        try:
            # Get the latest snapshot if multiple timestamps
            if 'timestamp' in order_book_data.columns and len(order_book_data['timestamp'].unique()) > 1:
                latest_time = order_book_data['timestamp'].max()
                snapshot = order_book_data[order_book_data['timestamp'] == latest_time]
            else:
                snapshot = order_book_data

            # Calculate bid-ask spread
            if 'bid_price' in snapshot.columns and 'ask_price' in snapshot.columns:
                # Direct calculation if bid/ask prices are available
                best_bid = snapshot['bid_price'].max()
                best_ask = snapshot['ask_price'].min()
                spread = best_ask - best_bid
                spread_bps = (spread / current_price) * 10000  # in basis points

                features['bid_ask_spread'] = spread
                features['bid_ask_spread_bps'] = spread_bps
                features['best_bid'] = best_bid
                features['best_ask'] = best_ask
                features['mid_price'] = (best_bid + best_ask) / 2
            else:
                # Estimate from price and sizes
                prices = snapshot['price'].values
                bid_sizes = snapshot['bid_size'].values
                ask_sizes = snapshot['ask_size'].values

                # Find best bid and ask
                bid_prices = prices[bid_sizes > 0]
                ask_prices = prices[ask_sizes > 0]

                if len(bid_prices) > 0 and len(ask_prices) > 0:
                    best_bid = max(bid_prices)
                    best_ask = min(ask_prices)
                    spread = best_ask - best_bid
                    spread_bps = (spread / current_price) * 10000  # in basis points

                    features['bid_ask_spread'] = spread
                    features['bid_ask_spread_bps'] = spread_bps
                    features['best_bid'] = best_bid
                    features['best_ask'] = best_ask
                    features['mid_price'] = (best_bid + best_ask) / 2

            # Calculate total bid and ask sizes
            total_bid_size = snapshot['bid_size'].sum()
            total_ask_size = snapshot['ask_size'].sum()

            features['total_bid_size'] = total_bid_size
            features['total_ask_size'] = total_ask_size
            features['total_order_book_size'] = total_bid_size + total_ask_size

            # Calculate number of bid and ask levels
            bid_levels = (snapshot['bid_size'] > 0).sum()
            ask_levels = (snapshot['ask_size'] > 0).sum()

            features['bid_levels'] = bid_levels
            features['ask_levels'] = ask_levels
            features['total_levels'] = bid_levels + ask_levels

            return features

        except Exception as e:
            logger.error(f"Error extracting basic order book features: {str(e)}")
            return features

    def _extract_imbalance_features(self,
                                  order_book_data: pd.DataFrame,
                                  current_price: float) -> Dict[str, float]:
        """
        Extract order book imbalance features.

        Args:
            order_book_data: DataFrame with order book data
            current_price: Current price

        Returns:
            Dictionary with imbalance features
        """
        features = {}

        try:
            # Get the latest snapshot if multiple timestamps
            if 'timestamp' in order_book_data.columns and len(order_book_data['timestamp'].unique()) > 1:
                latest_time = order_book_data['timestamp'].max()
                snapshot = order_book_data[order_book_data['timestamp'] == latest_time]
            else:
                snapshot = order_book_data

            # Calculate total bid and ask sizes
            total_bid_size = snapshot['bid_size'].sum()
            total_ask_size = snapshot['ask_size'].sum()
            total_size = total_bid_size + total_ask_size

            # Calculate basic imbalance ratio (-1 to 1, negative means more asks, positive means more bids)
            if total_size > 0:
                imbalance_ratio = (total_bid_size - total_ask_size) / total_size
                features['order_book_imbalance'] = imbalance_ratio

                # Normalized imbalance (0 to 1, where 1 is complete imbalance)
                features['order_book_imbalance_normalized'] = abs(imbalance_ratio)

            # Calculate imbalance at different price levels from current price
            for level in [0.1, 0.5, 1.0, 2.0, 5.0]:  # percentage from current price
                lower_bound = current_price * (1 - level/100)
                upper_bound = current_price * (1 + level/100)

                # Filter orders within this price range
                range_orders = snapshot[(snapshot['price'] >= lower_bound) & (snapshot['price'] <= upper_bound)]

                if not range_orders.empty:
                    range_bid_size = range_orders['bid_size'].sum()
                    range_ask_size = range_orders['ask_size'].sum()
                    range_total_size = range_bid_size + range_ask_size

                    if range_total_size > 0:
                        range_imbalance = (range_bid_size - range_ask_size) / range_total_size
                        features[f'imbalance_{level}pct_range'] = range_imbalance

            # Calculate weighted imbalance (weighted by distance from current price)
            bid_weighted_sum = 0
            ask_weighted_sum = 0

            for _, row in snapshot.iterrows():
                price = row['price']
                price_distance = abs(price - current_price) / current_price
                weight = 1 / (1 + price_distance)  # Closer prices get higher weight

                if row['bid_size'] > 0:
                    bid_weighted_sum += row['bid_size'] * weight

                if row['ask_size'] > 0:
                    ask_weighted_sum += row['ask_size'] * weight

            total_weighted = bid_weighted_sum + ask_weighted_sum
            if total_weighted > 0:
                weighted_imbalance = (bid_weighted_sum - ask_weighted_sum) / total_weighted
                features['weighted_imbalance'] = weighted_imbalance

            return features

        except Exception as e:
            logger.error(f"Error extracting order book imbalance features: {str(e)}")
            return features

    def _extract_depth_features(self,
                              order_book_data: pd.DataFrame,
                              current_price: float) -> Dict[str, float]:
        """
        Extract order book depth features.

        Args:
            order_book_data: DataFrame with order book data
            current_price: Current price

        Returns:
            Dictionary with depth features
        """
        features = {}

        try:
            # Get the latest snapshot if multiple timestamps
            if 'timestamp' in order_book_data.columns and len(order_book_data['timestamp'].unique()) > 1:
                latest_time = order_book_data['timestamp'].max()
                snapshot = order_book_data[order_book_data['timestamp'] == latest_time]
            else:
                snapshot = order_book_data

            # Calculate depth at different price levels
            for level in [0.1, 0.25, 0.5, 1.0, 2.0, 5.0]:  # percentage from current price
                lower_bound = current_price * (1 - level/100)
                upper_bound = current_price * (1 + level/100)

                # Filter orders within this price range
                range_orders = snapshot[(snapshot['price'] >= lower_bound) & (snapshot['price'] <= upper_bound)]

                if not range_orders.empty:
                    bid_depth = range_orders['bid_size'].sum()
                    ask_depth = range_orders['ask_size'].sum()
                    total_depth = bid_depth + ask_depth

                    features[f'bid_depth_{level}pct'] = bid_depth
                    features[f'ask_depth_{level}pct'] = ask_depth
                    features[f'total_depth_{level}pct'] = total_depth

                    # Depth ratio (bid/ask)
                    if ask_depth > 0:
                        features[f'depth_ratio_{level}pct'] = bid_depth / ask_depth

            # Calculate cumulative depth profile
            price_levels = sorted(snapshot['price'].unique())
            mid_price_idx = 0

            # Find index closest to current price
            for i, price in enumerate(price_levels):
                if price >= current_price:
                    mid_price_idx = i
                    break

            # Calculate cumulative depth above and below current price
            cum_bid_depth = []
            cum_ask_depth = []

            # Cumulative bid depth (going down from current price)
            cum_depth = 0
            for i in range(mid_price_idx - 1, -1, -1):
                price = price_levels[i]
                level_orders = snapshot[snapshot['price'] == price]
                level_depth = level_orders['bid_size'].sum()
                cum_depth += level_depth
                cum_bid_depth.append((price, cum_depth))

            # Cumulative ask depth (going up from current price)
            cum_depth = 0
            for i in range(mid_price_idx, len(price_levels)):
                price = price_levels[i]
                level_orders = snapshot[snapshot['price'] == price]
                level_depth = level_orders['ask_size'].sum()
                cum_depth += level_depth
                cum_ask_depth.append((price, cum_depth))

            # Extract features from cumulative depth
            if cum_bid_depth:
                # Depth required to move price by X%
                for pct in [0.1, 0.25, 0.5, 1.0]:
                    target_price = current_price * (1 - pct/100)
                    for price, depth in cum_bid_depth:
                        if price <= target_price:
                            features[f'depth_to_down_{pct}pct'] = depth
                            break

            if cum_ask_depth:
                # Depth required to move price by X%
                for pct in [0.1, 0.25, 0.5, 1.0]:
                    target_price = current_price * (1 + pct/100)
                    for price, depth in cum_ask_depth:
                        if price >= target_price:
                            features[f'depth_to_up_{pct}pct'] = depth
                            break

            # Calculate depth asymmetry
            for pct in [0.1, 0.25, 0.5, 1.0]:
                if f'depth_to_down_{pct}pct' in features and f'depth_to_up_{pct}pct' in features:
                    down_depth = features[f'depth_to_down_{pct}pct']
                    up_depth = features[f'depth_to_up_{pct}pct']
                    total_depth = down_depth + up_depth

                    if total_depth > 0:
                        # Positive means more depth on bid side (harder to go down)
                        # Negative means more depth on ask side (harder to go up)
                        asymmetry = (down_depth - up_depth) / total_depth
                        features[f'depth_asymmetry_{pct}pct'] = asymmetry

            # Calculate order book shape features
            if len(price_levels) > 5:
                # Fit linear regression to bid and ask sides
                from sklearn.linear_model import LinearRegression

                # Bid side
                if len(cum_bid_depth) > 2:
                    bid_prices = np.array([p[0] for p in cum_bid_depth]).reshape(-1, 1)
                    bid_depths = np.array([p[1] for p in cum_bid_depth])
                    bid_model = LinearRegression().fit(bid_prices, bid_depths)
                    features['bid_depth_slope'] = bid_model.coef_[0]

                # Ask side
                if len(cum_ask_depth) > 2:
                    ask_prices = np.array([p[0] for p in cum_ask_depth]).reshape(-1, 1)
                    ask_depths = np.array([p[1] for p in cum_ask_depth])
                    ask_model = LinearRegression().fit(ask_prices, ask_depths)
                    features['ask_depth_slope'] = ask_model.coef_[0]

            return features

        except Exception as e:
            logger.error(f"Error extracting order book depth features: {str(e)}")
            return features

    def _extract_resilience_features(self,
                                   order_book_data: pd.DataFrame,
                                   current_price: float) -> Dict[str, float]:
        """
        Extract order book resilience features.

        Args:
            order_book_data: DataFrame with order book data
            current_price: Current price

        Returns:
            Dictionary with resilience features
        """
        features = {}

        try:
            # Ensure we have timestamp data
            if 'timestamp' not in order_book_data.columns:
                logger.warning("No timestamp data available for resilience features")
                return features

            # Convert timestamp to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(order_book_data['timestamp']):
                order_book_data['timestamp'] = pd.to_datetime(order_book_data['timestamp'])

            # Sort by timestamp
            order_book_data = order_book_data.sort_values('timestamp')

            # Calculate time windows
            latest_time = order_book_data['timestamp'].max()

            # Calculate features for different time windows
            for minutes in self.time_windows:
                window_start = latest_time - pd.Timedelta(minutes=minutes)
                window_data = order_book_data[order_book_data['timestamp'] >= window_start]

                if len(window_data) < 2:
                    continue

                # Calculate order flow imbalance over time
                timestamps = window_data['timestamp'].unique()

                # Initialize arrays for tracking metrics over time
                bid_sizes = []
                ask_sizes = []
                imbalances = []

                for ts in timestamps:
                    ts_data = window_data[window_data['timestamp'] == ts]

                    # Calculate imbalance at this timestamp
                    ts_bid_size = ts_data['bid_size'].sum()
                    ts_ask_size = ts_data['ask_size'].sum()
                    ts_total_size = ts_bid_size + ts_ask_size

                    bid_sizes.append(ts_bid_size)
                    ask_sizes.append(ts_ask_size)

                    if ts_total_size > 0:
                        ts_imbalance = (ts_bid_size - ts_ask_size) / ts_total_size
                        imbalances.append(ts_imbalance)

                # Calculate resilience metrics
                if len(imbalances) > 1:
                    # Imbalance volatility (standard deviation)
                    features[f'imbalance_volatility_{minutes}m'] = np.std(imbalances)

                    # Imbalance trend (linear regression slope)
                    x = np.arange(len(imbalances)).reshape(-1, 1)
                    y = np.array(imbalances)

                    from sklearn.linear_model import LinearRegression
                    model = LinearRegression().fit(x, y)
                    features[f'imbalance_trend_{minutes}m'] = model.coef_[0]

                    # Imbalance mean reversion (autocorrelation)
                    if len(imbalances) > 2:
                        from statsmodels.tsa.stattools import acf
                        try:
                            acf_result = acf(imbalances, nlags=1)
                            features[f'imbalance_autocorr_{minutes}m'] = acf_result[1]
                        except:
                            pass

                # Calculate order replenishment rate
                if len(bid_sizes) > 1 and len(ask_sizes) > 1:
                    # Calculate changes in sizes
                    bid_changes = np.diff(bid_sizes)
                    ask_changes = np.diff(ask_sizes)

                    # Positive changes represent order replenishment
                    bid_replenishments = bid_changes[bid_changes > 0].sum() if any(bid_changes > 0) else 0
                    ask_replenishments = ask_changes[ask_changes > 0].sum() if any(ask_changes > 0) else 0

                    # Negative changes represent order consumption
                    bid_consumptions = -bid_changes[bid_changes < 0].sum() if any(bid_changes < 0) else 0
                    ask_consumptions = -ask_changes[ask_changes < 0].sum() if any(ask_changes < 0) else 0

                    # Calculate replenishment rates
                    if bid_consumptions > 0:
                        features[f'bid_replenishment_rate_{minutes}m'] = bid_replenishments / bid_consumptions

                    if ask_consumptions > 0:
                        features[f'ask_replenishment_rate_{minutes}m'] = ask_replenishments / ask_consumptions

                    # Calculate overall replenishment rate
                    total_consumption = bid_consumptions + ask_consumptions
                    total_replenishment = bid_replenishments + ask_replenishments

                    if total_consumption > 0:
                        features[f'total_replenishment_rate_{minutes}m'] = total_replenishment / total_consumption

            return features

        except Exception as e:
            logger.error(f"Error extracting order book resilience features: {str(e)}")
            return features

    def _extract_price_impact_features(self,
                                     order_book_data: pd.DataFrame,
                                     current_price: float) -> Dict[str, float]:
        """
        Extract price impact features from order book data.

        Args:
            order_book_data: DataFrame with order book data
            current_price: Current price

        Returns:
            Dictionary with price impact features
        """
        features = {}

        try:
            # Get the latest snapshot if multiple timestamps
            if 'timestamp' in order_book_data.columns and len(order_book_data['timestamp'].unique()) > 1:
                latest_time = order_book_data['timestamp'].max()
                snapshot = order_book_data[order_book_data['timestamp'] == latest_time]
            else:
                snapshot = order_book_data

            # Sort by price
            snapshot = snapshot.sort_values('price')

            # Separate bid and ask sides
            bids = snapshot[snapshot['bid_size'] > 0].sort_values('price', ascending=False)
            asks = snapshot[snapshot['ask_size'] > 0].sort_values('price')

            # Calculate price impact for different order sizes
            for size_percentile in [10, 25, 50, 75, 90]:
                # Calculate size threshold based on percentile of total book size
                size_threshold = np.percentile(
                    np.concatenate([bids['bid_size'].values, asks['ask_size'].values]),
                    size_percentile
                )

                # Calculate price impact for buying this size
                buy_impact = self._calculate_price_impact(asks, size_threshold, current_price, direction='buy')
                if buy_impact is not None:
                    features[f'buy_impact_{size_percentile}pct'] = buy_impact

                # Calculate price impact for selling this size
                sell_impact = self._calculate_price_impact(bids, size_threshold, current_price, direction='sell')
                if sell_impact is not None:
                    features[f'sell_impact_{size_percentile}pct'] = sell_impact

                # Calculate impact asymmetry
                if buy_impact is not None and sell_impact is not None:
                    # Positive means selling has more impact (easier to push price down)
                    # Negative means buying has more impact (easier to push price up)
                    impact_asymmetry = sell_impact - buy_impact
                    features[f'impact_asymmetry_{size_percentile}pct'] = impact_asymmetry

            # Calculate Kyle's lambda (price impact per unit of order size)
            # Higher lambda means lower liquidity
            for direction in ['buy', 'sell']:
                for size_percentile in [25, 50, 75]:
                    impact_key = f'buy_impact_{size_percentile}pct' if direction == 'buy' else f'sell_impact_{size_percentile}pct'

                    if impact_key in features:
                        impact = features[impact_key]
                        size = np.percentile(
                            asks['ask_size'].values if direction == 'buy' else bids['bid_size'].values,
                            size_percentile
                        )

                        if size > 0:
                            lambda_value = impact / size
                            features[f'kyle_lambda_{direction}_{size_percentile}pct'] = lambda_value

            return features

        except Exception as e:
            logger.error(f"Error extracting price impact features: {str(e)}")
            return features

    def _calculate_price_impact(self,
                              orders: pd.DataFrame,
                              size: float,
                              current_price: float,
                              direction: str) -> Optional[float]:
        """
        Calculate price impact of executing an order of given size.

        Args:
            orders: DataFrame with order book data (sorted by price)
            size: Order size to execute
            current_price: Current price
            direction: 'buy' or 'sell'

        Returns:
            Price impact as a percentage of current price
        """
        try:
            remaining_size = size
            impact_price = None

            if direction == 'buy':
                # For buy orders, we consume ask liquidity from lowest to highest price
                for _, row in orders.iterrows():
                    if row['ask_size'] >= remaining_size:
                        # This level has enough liquidity
                        impact_price = row['price']
                        break
                    else:
                        # Consume this level and continue
                        remaining_size -= row['ask_size']

                # If we couldn't fill the entire order
                if impact_price is None and not orders.empty:
                    impact_price = orders['price'].max()  # Highest available ask price

            else:  # sell
                # For sell orders, we consume bid liquidity from highest to lowest price
                for _, row in orders.iterrows():
                    if row['bid_size'] >= remaining_size:
                        # This level has enough liquidity
                        impact_price = row['price']
                        break
                    else:
                        # Consume this level and continue
                        remaining_size -= row['bid_size']

                # If we couldn't fill the entire order
                if impact_price is None and not orders.empty:
                    impact_price = orders['price'].min()  # Lowest available bid price

            # Calculate impact as percentage of current price
            if impact_price is not None:
                if direction == 'buy':
                    impact = (impact_price - current_price) / current_price
                else:  # sell
                    impact = (current_price - impact_price) / current_price

                return impact

            return None

        except Exception as e:
            logger.error(f"Error calculating price impact: {str(e)}")
            return None
