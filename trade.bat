@echo off
REM CORE Trading System - Clean Command Interface with Visual Capabilities
REM Commands: Single ticker, Batch, Visual charts

echo CORE Trading System
echo ===================

if "%1"=="" (
    echo USAGE:
    echo   Single ticker: trade AAPL
    echo   With charts:   trade AAPL --charts
    echo   Batch:         trade --batch QQQ,AAPL,TSLA
    echo   Batch + charts: trade --batch QQQ,AAPL,TSLA --charts
    goto :end
)

REM Execute Python trading system
py trade.py %*

:end
