#!/usr/bin/env python3
"""
Agent Zero Core Decision Processor
Main decision-making engine with mathematical precision
"""

import numpy as np
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

@dataclass
class DecisionInput:
    """Structured input for Agent Zero decisions"""
    signal_confidence: float
    signal_strength: float
    execution_recommendation: str
    math_accuracy: float
    math_precision: float
    market_context: Dict[str, Any]
    timestamp: datetime

@dataclass
class DecisionOutput:
    """Structured output from Agent Zero decisions"""
    action: str  # 'execute', 'hold', 'avoid'
    confidence: float
    composite_score: float
    reasoning: List[str]
    execution_time_ms: float
    decision_method: str
    timestamp: datetime

class AgentZeroCore:
    """
    Core Agent Zero decision processor
    Mathematical decision engine with weighted scoring
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Mathematical weights for decision scoring
        self.weights = {
            'signal_confidence': 0.30,
            'signal_strength': 0.25,
            'execution_recommendation': 0.20,
            'math_accuracy': 0.15,
            'math_precision': 0.10
        }
        
        # Decision thresholds with hysteresis
        self.thresholds = {
            'execute_threshold': 0.75,
            'hold_threshold': 0.25,
            'confidence_floor': 0.0,
            'confidence_ceiling': 1.0
        }
        
        # Performance tracking
        self.decision_count = 0
        self.total_execution_time = 0.0
    
    def process_decision(self, decision_input: DecisionInput) -> DecisionOutput:
        """
        Process trading decision with mathematical precision
        """
        start_time = datetime.now()
        
        # Validate inputs
        self._validate_inputs(decision_input)
        
        # Calculate composite score
        composite_score = self._calculate_composite_score(decision_input)
        
        # Determine action based on thresholds
        action = self._determine_action(composite_score)
        
        # Calculate confidence
        confidence = self._calculate_confidence(composite_score, decision_input)
        
        # Generate reasoning
        reasoning = self._generate_reasoning(decision_input, composite_score, action)
        
        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        
        # Update performance tracking
        self.decision_count += 1
        self.total_execution_time += execution_time
        
        return DecisionOutput(
            action=action,
            confidence=confidence,
            composite_score=composite_score,
            reasoning=reasoning,
            execution_time_ms=execution_time,
            decision_method="core_weighted_scoring",
            timestamp=datetime.now()
        )
    
    def _validate_inputs(self, inputs: DecisionInput):
        """Validate input parameters"""
        assert 0.0 <= inputs.signal_confidence <= 1.0, "Signal confidence out of bounds"
        assert 0.0 <= inputs.signal_strength <= 1.0, "Signal strength out of bounds"
        assert inputs.execution_recommendation in ['execute', 'hold', 'avoid'], "Invalid execution recommendation"
        assert 0.0 <= inputs.math_accuracy <= 1.0, "Math accuracy out of bounds"
        assert inputs.math_precision >= 0.0, "Math precision must be non-negative"
    
    def _calculate_composite_score(self, inputs: DecisionInput) -> float:
        """Calculate weighted composite score"""
        
        # Convert execution recommendation to numeric score
        exec_scores = {'execute': 1.0, 'hold': 0.5, 'avoid': 0.0}
        exec_score = exec_scores.get(inputs.execution_recommendation, 0.5)
        
        # Normalize precision score (higher precision = better)
        precision_score = min(inputs.math_precision * 1000, 1.0)  # Scale to [0,1]
        
        # Weighted composite calculation
        composite_score = (
            inputs.signal_confidence * self.weights['signal_confidence'] +
            inputs.signal_strength * self.weights['signal_strength'] +
            exec_score * self.weights['execution_recommendation'] +
            inputs.math_accuracy * self.weights['math_accuracy'] +
            precision_score * self.weights['math_precision']
        )
        
        return np.clip(composite_score, 0.0, 1.0)
    
    def _determine_action(self, composite_score: float) -> str:
        """Determine action based on composite score and thresholds"""
        if composite_score >= self.thresholds['execute_threshold']:
            return 'execute'
        elif composite_score >= self.thresholds['hold_threshold']:
            return 'hold'
        else:
            return 'avoid'
    
    def _calculate_confidence(self, composite_score: float, inputs: DecisionInput) -> float:
        """Calculate decision confidence"""
        base_confidence = composite_score
        
        # Boost confidence for high accuracy
        if inputs.math_accuracy > 0.9:
            base_confidence *= 1.1
        
        # Reduce confidence for low precision
        if inputs.math_precision > 0.01:
            base_confidence *= 0.95
        
        return np.clip(base_confidence, 
                      self.thresholds['confidence_floor'], 
                      self.thresholds['confidence_ceiling'])
    
    def _generate_reasoning(self, inputs: DecisionInput, score: float, action: str) -> List[str]:
        """Generate decision reasoning"""
        reasoning = [
            f"Composite score: {score:.3f}",
            f"Signal confidence: {inputs.signal_confidence:.3f}",
            f"Signal strength: {inputs.signal_strength:.3f}",
            f"Execution recommendation: {inputs.execution_recommendation}",
            f"Math accuracy: {inputs.math_accuracy:.3f}",
            f"Math precision: {inputs.math_precision:.6f}",
            f"Action determined: {action}"
        ]
        
        # Add threshold-specific reasoning
        if action == 'execute':
            reasoning.append(f"Score {score:.3f} exceeds execute threshold {self.thresholds['execute_threshold']}")
        elif action == 'hold':
            reasoning.append(f"Score {score:.3f} between hold ({self.thresholds['hold_threshold']}) and execute ({self.thresholds['execute_threshold']}) thresholds")
        else:
            reasoning.append(f"Score {score:.3f} below hold threshold {self.thresholds['hold_threshold']}")
        
        return reasoning
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get performance statistics"""
        avg_execution_time = (self.total_execution_time / self.decision_count 
                             if self.decision_count > 0 else 0.0)
        
        return {
            'total_decisions': self.decision_count,
            'total_execution_time_ms': self.total_execution_time,
            'average_execution_time_ms': avg_execution_time,
            'decisions_per_second': (1000.0 / avg_execution_time) if avg_execution_time > 0 else 0.0
        }

if __name__ == "__main__":
    # Test core processor
    core = AgentZeroCore()
    
    test_input = DecisionInput(
        signal_confidence=0.85,
        signal_strength=0.75,
        execution_recommendation='execute',
        math_accuracy=0.95,
        math_precision=0.001,
        market_context={'volatility': 'normal'},
        timestamp=datetime.now()
    )
    
    decision = core.process_decision(test_input)
    print(f"Decision: {decision.action}")
    print(f"Confidence: {decision.confidence:.3f}")
    print(f"Execution time: {decision.execution_time_ms:.3f}ms")
