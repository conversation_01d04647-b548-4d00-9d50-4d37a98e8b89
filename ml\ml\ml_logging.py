"""
Logging and Error Handling Framework for Machine Learning Components

This module provides a centralized logging and error handling system for all ML
components in the Liquidity Strategy Dashboard, including customized loggers,
error tracking, and performance monitoring.
"""

import os
import sys
import logging
import traceback
import time
import json
import datetime
from typing import Dict, Any, Optional, List, Callable, Union
import functools

def configure_logging(log_level='INFO', log_file=None):
    """Configure logging settings for the ML system.
    
    Args:
        log_level (str): Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file (str, optional): Path to log file
    
    Returns:
        logging.Logger: Configured logger
    """
    logger = logging.getLogger("ml_system")
    logger.setLevel(getattr(logging, log_level))
    
    # Create handlers
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, log_level))
        logger.addHandler(file_handler)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level))
    logger.addHandler(console_handler)
    
    return logger

# Define log levels
LOG_LEVELS = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL
}

# Custom log formatter
class MLLogFormatter(logging.Formatter):
    """
    Custom formatter for ML component logs.
    """
    
    def __init__(self, include_timestamp: bool = True):
        """
        Initialize the formatter.
        
        Args:
            include_timestamp: Whether to include timestamps in logs
        """
        if include_timestamp:
            fmt = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        else:
            fmt = '%(name)s - %(levelname)s - %(message)s'
        
        super().__init__(fmt)

# ML Exception classes
class MLException(Exception):
    """
    Base exception class for ML components.
    """
    
    def __init__(self, message: str, component: Optional[str] = None, **kwargs):
        """
        Initialize the exception.
        
        Args:
            message: Exception message
            component: Optional component name where the exception occurred
            **kwargs: Additional exception metadata
        """
        self.message = message
        self.component = component
        self.timestamp = datetime.datetime.now()
        self.metadata = kwargs
        
        # Format the message with component info if available
        if component:
            full_message = f"[{component}] {message}"
        else:
            full_message = message
        
        super().__init__(full_message)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the exception to a dictionary.
        
        Returns:
            Dictionary representation of the exception
        """
        result = {
            'message': self.message,
            'timestamp': self.timestamp.isoformat()
        }
        
        if self.component:
            result['component'] = self.component
        
        if self.metadata:
            result['metadata'] = self.metadata
        
        return result

class ModelNotFoundError(MLException):
    """Exception raised when a model is not found."""
    pass

class ModelNotTrainedError(MLException):
    """Exception raised when trying to use an untrained model."""
    pass

class InvalidDataError(MLException):
    """Exception raised when input data is invalid."""
    pass

class FeatureEngineeringError(MLException):
    """Exception raised during feature engineering."""
    pass

class InferenceError(MLException):
    """Exception raised during model inference."""
    pass

class ConfigurationError(MLException):
    """Exception raised for configuration issues."""
    pass

class AlertingError(MLException):
    """Exception raised during alert generation."""
    pass

# Error handler class
class MLErrorHandler:
    """
    Handles errors and exceptions for ML components.
    """
    
    def __init__(self, logger: logging.Logger):
        """
        Initialize the error handler.
        
        Args:
            logger: Logger instance to use for error logging
        """
        self.logger = logger
        self.last_errors = []
        self.error_counts = {}
        self.max_stored_errors = 100
    
    def handle_error(self, error: Exception, log_level: str = 'ERROR', 
                   reraise: bool = False, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Handle an error.
        
        Args:
            error: The exception to handle
            log_level: Log level to use
            reraise: Whether to reraise the exception after handling
            **kwargs: Additional error metadata
            
        Returns:
            Dictionary with error information if not reraised
            
        Raises:
            The original exception if reraise is True
        """
        # Convert to ML exception if it's not already
        if not isinstance(error, MLException):
            component = kwargs.get('component')
            message = str(error)
            error = MLException(message, component, **kwargs)
        
        # Get traceback
        tb = traceback.format_exc()
        
        # Log the error
        log_method = getattr(self.logger, log_level.lower())
        log_method(f"Error: {error}")
        self.logger.debug(f"Traceback: {tb}")
        
        # Store error info
        error_info = {
            'error_type': error.__class__.__name__,
            'message': str(error),
            'timestamp': datetime.datetime.now().isoformat(),
            'traceback': tb
        }
        
        # Add any additional metadata
        error_info.update(kwargs)
        
        # Store in last errors
        self.last_errors.append(error_info)
        if len(self.last_errors) > self.max_stored_errors:
            self.last_errors.pop(0)
        
        # Update error count
        error_type = error.__class__.__name__
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1
        
        # Reraise if requested
        if reraise:
            raise error
        
        return error_info
    
    def get_last_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get recent errors.
        
        Args:
            limit: Maximum number of errors to return
            
        Returns:
            List of error dictionaries
        """
        return self.last_errors[-limit:]
    
    def get_error_counts(self) -> Dict[str, int]:
        """
        Get counts of each error type.
        
        Returns:
            Dictionary mapping error types to counts
        """
        return self.error_counts
    
    def clear_errors(self) -> None:
        """Clear stored errors."""
        self.last_errors = []
        self.error_counts = {}

# Performance monitoring
class MLPerformanceMonitor:
    """
    Monitors performance of ML components.
    """
    
    def __init__(self, logger: logging.Logger):
        """
        Initialize the performance monitor.
        
        Args:
            logger: Logger instance to use for performance logging
        """
        self.logger = logger
        self.timings = {}
        self.counts = {}
        self.current_timers = {}
    
    def start_timer(self, operation: str) -> str:
        """
        Start a timer for an operation.
        
        Args:
            operation: Name of the operation
            
        Returns:
            Timer ID
        """
        timer_id = f"{operation}_{time.time()}"
        self.current_timers[timer_id] = {
            'operation': operation,
            'start_time': time.time()
        }
        return timer_id
    
    def stop_timer(self, timer_id: str) -> Dict[str, Any]:
        """
        Stop a timer and record the elapsed time.
        
        Args:
            timer_id: ID of the timer to stop
            
        Returns:
            Dictionary with timing information
        """
        if timer_id not in self.current_timers:
            self.logger.warning(f"Timer {timer_id} not found")
            return {}
        
        timer_info = self.current_timers.pop(timer_id)
        operation = timer_info['operation']
        start_time = timer_info['start_time']
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        # Record timing
        if operation not in self.timings:
            self.timings[operation] = []
        
        self.timings[operation].append(elapsed_time)
        
        # Update count
        self.counts[operation] = self.counts.get(operation, 0) + 1
        
        # Log timing
        self.logger.debug(f"Operation '{operation}' took {elapsed_time:.4f} seconds")
        
        # Return timing info
        timing_info = {
            'operation': operation,
            'start_time': start_time,
            'end_time': end_time,
            'elapsed_time': elapsed_time
        }
        
        return timing_info
    
    def get_stats(self, operation: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance statistics.
        
        Args:
            operation: Optional operation name to filter stats
            
        Returns:
            Dictionary with performance statistics
        """
        import numpy as np
        
        if operation:
            # Get stats for specific operation
            if operation not in self.timings:
                return {}
            
            times = self.timings[operation]
            count = self.counts[operation]
            
            return {
                'operation': operation,
                'count': count,
                'mean_time': np.mean(times),
                'median_time': np.median(times),
                'min_time': np.min(times),
                'max_time': np.max(times),
                'std_time': np.std(times),
                'total_time': np.sum(times)
            }
        else:
            # Get stats for all operations
            stats = {}
            
            for op in self.timings:
                times = self.timings[op]
                count = self.counts[op]
                
                stats[op] = {
                    'count': count,
                    'mean_time': np.mean(times),
                    'median_time': np.median(times),
                    'min_time': np.min(times),
                    'max_time': np.max(times),
                    'std_time': np.std(times),
                    'total_time': np.sum(times)
                }
            
            return stats
    
    def clear_stats(self, operation: Optional[str] = None) -> None:
        """
        Clear performance statistics.
        
        Args:
            operation: Optional operation name to clear. If None, clears all.
        """
        if operation:
            # Clear specific operation
            if operation in self.timings:
                self.timings.pop(operation)
                self.counts.pop(operation, None)
        else:
            # Clear all
            self.timings = {}
            self.counts = {}

# Logger factory
class MLLoggerFactory:
    """
    Factory for creating loggers for ML components.
    """
    
    def __init__(self, log_dir: Optional[str] = None, 
               default_level: str = 'INFO',
               console_output: bool = True):
        """
        Initialize the logger factory.
        
        Args:
            log_dir: Directory for log files. If None, does not log to files.
            default_level: Default log level
            console_output: Whether to output logs to console
        """
        self.log_dir = log_dir
        self.default_level = default_level
        self.console_output = console_output
        self.loggers = {}
        
        # Create log directory if specified
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
    
    def get_logger(self, name: str, level: Optional[str] = None) -> logging.Logger:
        """
        Get or create a logger for a component.
        
        Args:
            name: Logger name
            level: Optional log level
            
        Returns:
            Logger instance
        """
        # Return existing logger if already created
        if name in self.loggers:
            return self.loggers[name]
        
        # Create new logger
        logger = logging.getLogger(name)
        
        # Set level
        logger.setLevel(LOG_LEVELS.get(level or self.default_level, logging.INFO))
        
        # Reset handlers
        logger.handlers = []
        
        # Add console handler if enabled
        if self.console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(MLLogFormatter())
            logger.addHandler(console_handler)
        
        # Add file handler if log directory is specified
        if self.log_dir:
            log_file = os.path.join(self.log_dir, f"{name}.log")
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(MLLogFormatter())
            logger.addHandler(file_handler)
        
        # Store logger
        self.loggers[name] = logger
        
        return logger

# Decorators for error handling and performance monitoring
def handle_errors(logger_name: str, log_level: str = 'ERROR', 
                reraise: bool = False, **kwargs):
    """
    Decorator for error handling.
    
    Args:
        logger_name: Name of the logger to use
        log_level: Log level for errors
        reraise: Whether to reraise exceptions
        **kwargs: Additional error metadata
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **func_kwargs):
            # Get logger
            logger_factory = get_logger_factory()
            logger = logger_factory.get_logger(logger_name)
            error_handler = MLErrorHandler(logger)
            
            try:
                return func(*args, **func_kwargs)
            except Exception as e:
                # Add function info to metadata
                error_metadata = {
                    'function': func.__name__,
                    'args': [str(arg) for arg in args],
                    'kwargs': {k: str(v) for k, v in func_kwargs.items()}
                }
                error_metadata.update(kwargs)
                
                # Handle error
                error_handler.handle_error(
                    e, log_level=log_level, reraise=reraise, **error_metadata
                )
                
                # Return None if not reraising
                return None
        
        return wrapper
    
    return decorator

def measure_performance(logger_name: str, operation: Optional[str] = None):
    """
    Decorator for performance measurement.
    
    Args:
        logger_name: Name of the logger to use
        operation: Optional operation name. If None, uses function name.
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Get logger
            logger_factory = get_logger_factory()
            logger = logger_factory.get_logger(logger_name)
            perf_monitor = MLPerformanceMonitor(logger)
            
            # Start timer
            op_name = operation or func.__name__
            timer_id = perf_monitor.start_timer(op_name)
            
            try:
                # Run function
                result = func(*args, **kwargs)
                return result
            finally:
                # Stop timer
                perf_monitor.stop_timer(timer_id)
        
        return wrapper
    
    return decorator

# Global instances
_logger_factory = None

def get_logger_factory(log_dir: Optional[str] = None, 
                     default_level: str = 'INFO',
                     console_output: bool = True) -> MLLoggerFactory:
    """
    Get the global logger factory instance.
    
    Args:
        log_dir: Directory for log files
        default_level: Default log level
        console_output: Whether to output logs to console
        
    Returns:
        MLLoggerFactory instance
    """
    global _logger_factory
    if _logger_factory is None:
        _logger_factory = MLLoggerFactory(log_dir, default_level, console_output)
    return _logger_factory

def get_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """
    Get a logger for a component.
    
    Args:
        name: Logger name
        level: Optional log level
        
    Returns:
        Logger instance
    """
    return get_logger_factory().get_logger(name, level)

def set_log_dir(log_dir: str) -> None:
    """
    Set the log directory for the global logger factory.
    
    Args:
        log_dir: Directory for log files
    """
    global _logger_factory
    if _logger_factory is None:
        _logger_factory = MLLoggerFactory(log_dir)
    else:
        _logger_factory.log_dir = log_dir
        
        # Create directory if it doesn't exist
        os.makedirs(log_dir, exist_ok=True)
