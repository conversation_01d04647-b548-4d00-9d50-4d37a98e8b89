"""
Analyzers Package for Liquidity Sweep System

This package contains all market analysis components for institutional-grade
trading strategies. Each analyzer implements rigorous mathematical models
for specific market microstructure analysis tasks.

Core Analyzer Categories:
- Base Components: BaseAnalyzer, FactorData, DirectionBias, TimeFrame
- Volume Analysis: Enhanced volume profiling and distribution analysis
- Options Analysis: Greeks, flow, open interest, and skew analysis
- Liquidity Analysis: Order book walls, sweeps, and void detection
- Flow Physics: Advanced momentum, velocity, and acceleration analysis
- Multi-Timeframe: Cross-timeframe confluence and analysis
- Price Action: Support/resistance, patterns, and trap detection

Mathematical Foundation:
- Statistical analysis with scipy integration
- Vectorized calculations for performance
- Real-time data processing capabilities
- Institutional-grade precision requirements

Author: VIRT Trading Team
Version: 2.0.0 (Production Ready)
Dependencies: NumPy, SciPy, Pandas, real-time market data
"""

import sys
import os

# Add current directory to sys.path for relative imports
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import and make base analyzer available
try:
    from .base_analyzer import BaseAnalyzer, FactorData, DirectionBias, TimeFrame
except ImportError:
    from base_analyzer import BaseAnalyzer, FactorData, DirectionBias, TimeFrame

__all__ = ['BaseAnalyzer', 'FactorData', 'DirectionBias', 'TimeFrame']

# Package metadata
__version__ = "2.0.0"
__author__ = "VIRT Trading Team"
__package_type__ = "analyzers"
__mathematical_foundation__ = "statistical_analysis"

# Analyzer registry for validation and discovery
__analyzer_registry__ = {
    # Base Components
    'BaseAnalyzer': 'Abstract base class for all analyzers',
    'FactorData': 'Standardized factor data structure',
    'DirectionBias': 'Directional bias enumeration',
    'TimeFrame': 'Time frame specifications',
    
    # Volume Analysis
    'EnhancedVolumeAnalyzer': 'Advanced volume profiling and analysis',
    'VolumeProfileAnalyzer': 'Volume profile construction and analysis',
    
    # Options Analysis
    'GammaSqueezeDetector': 'Gamma squeeze and gamma wall detection',
    'EnhancedGEXAnalyzer': 'Enhanced Gamma Exposure (GEX) analysis',
    'IVSkewAnalyzer': 'Implied volatility skew analysis',
    'OptionsOIAnalyzer': 'Options open interest analysis',
    'OILiquidityAnalyzer': 'Open interest liquidity analysis',
    'OptionsFlowAnalyzer': 'Options flow and institutional detection',
    'GreekAnalyzer': 'Options Greeks analysis and flow',
    'DeltaSqueezeDetector': 'Delta hedging squeeze detection',
    
    # Liquidity Analysis
    'LiquidityAnalyzer': 'Core liquidity pool analysis',
    'LiquiditySweepAnalyzer': 'Liquidity sweep pattern detection',
    'LiquiditySweepDetector': 'Advanced sweep detection algorithms',
    'LiquidityVoidAnalyzer': 'Liquidity void and gap analysis',
    'OrderBookWallAnalyzer': 'Order book wall detection and analysis',
    
    # Flow Physics
    'FlowVelocityAnalyzer': 'Flow velocity and momentum analysis',
    'FlowAccelerationAnalyzer': 'Flow acceleration pattern analysis',
    'FlowJerkAnalyzer': 'Flow jerk and regime change analysis',
    'FlowPhysicsIntegrator': 'Comprehensive flow physics integration',
    'FlowDivergenceAnalyzer': 'Flow divergence pattern detection',
    
    # Multi-Timeframe
    'MultiTimeframeAnalyzer': 'Cross-timeframe analysis and confluence',
    'TimeframeIntegrator': 'Timeframe integration and weighting',
    'ConfluenceDetector': 'Multi-timeframe confluence detection',
    'LevelHierarchy': 'Hierarchical level organization',
    'RealTimeProcessor': 'Real-time multi-timeframe processing',
    
    # Price Action
    'PriceActionAnalyzer': 'Price action pattern analysis',
    'TrapPatternDetector': 'Trap pattern detection and classification',
    'EnhancedTrapPatternDetector': 'Advanced trap pattern algorithms',
    'FractalAnalyzer': 'Fractal pattern analysis',
    
    # Advanced Analysis
    'ConsensusAnalyzer': 'Multi-analyzer consensus building',
    'RiskAnalyzer': 'Risk assessment and metrics',
    'MomentumAnalyzer': 'Momentum indicator analysis',
    'MarketAnalyzerUnified': 'Unified market analysis',
    'AdaptiveTimeframeWeighter': 'Adaptive timeframe weighting',
    'AdvancedConfluenceDetector': 'Advanced confluence algorithms',
    'HierarchicalLiquidityAnalyzer': 'Hierarchical liquidity analysis',
    'LiquidityDivergenceDetector': 'Liquidity divergence detection',
    'UnifiedLiquidityScoringSystem': 'Unified liquidity scoring'
}

# Enhanced volume analyzer availability check
try:
    from .enhanced_volume_analyzer import EnhancedVolumeAnalyzer
    _enhanced_volume_available = True
except ImportError:
    _enhanced_volume_available = False

# Enhanced flow physics availability check
try:
    from .enhanced_flow_physics import FlowPhysicsIntegrator as EnhancedFlowPhysicsIntegrator
    _enhanced_flow_physics_available = True
except ImportError:
    _enhanced_flow_physics_available = False

# Multi-timeframe components availability check
try:
    from .multi_timeframe import TimeframeIntegrator, ConfluenceDetector
    _multi_timeframe_available = True
except ImportError:
    _multi_timeframe_available = False


def get_analyzer_info():
    """
    Get comprehensive analyzer package information.
    
    Returns:
        dict: Analyzer metadata and registry information
    """
    return {
        'version': __version__,
        'author': __author__,
        'package_type': __package_type__,
        'mathematical_foundation': __mathematical_foundation__,
        'analyzer_count': len(__analyzer_registry__),
        'analyzers': __analyzer_registry__,
        'available_analyzers': list(__analyzer_registry__.keys()),
        'enhanced_volume_available': _enhanced_volume_available
    }

def list_available_analyzers():
    """
    List all available analyzers by category.
    
    Returns:
        dict: Analyzers organized by category
    """
    categories = {
        'Base Components': [],
        'Volume Analysis': [],
        'Options Analysis': [],
        'Liquidity Analysis': [],
        'Flow Physics': [],
        'Multi-Timeframe': [],
        'Price Action': []
    }
    
    for analyzer_name, description in __analyzer_registry__.items():
        if 'base' in analyzer_name.lower() or analyzer_name in ['FactorData', 'DirectionBias', 'TimeFrame']:
            categories['Base Components'].append((analyzer_name, description))
        elif 'volume' in analyzer_name.lower():
            categories['Volume Analysis'].append((analyzer_name, description))
        elif any(term in analyzer_name.lower() for term in ['options', 'greek', 'gamma', 'iv', 'oi']):
            categories['Options Analysis'].append((analyzer_name, description))
        elif any(term in analyzer_name.lower() for term in ['liquidity', 'order', 'wall', 'sweep', 'void']):
            categories['Liquidity Analysis'].append((analyzer_name, description))
        elif 'flow' in analyzer_name.lower():
            categories['Flow Physics'].append((analyzer_name, description))
        elif 'timeframe' in analyzer_name.lower():
            categories['Multi-Timeframe'].append((analyzer_name, description))
        elif any(term in analyzer_name.lower() for term in ['price', 'trap', 'pattern']):
            categories['Price Action'].append((analyzer_name, description))
    
    return categories

def validate_analyzer_package():
    """
    Validate analyzer package structure and critical components.
    
    Returns:
        dict: Validation results with status and component availability
    """
    validation_results = {
        'package_valid': True,
        'base_components_loaded': 0,
        'critical_analyzers_available': 0,
        'errors': [],
        'warnings': [],
        'component_status': {}
    }
    
    # Check base components
    base_components = ['BaseAnalyzer', 'FactorData', 'DirectionBias', 'TimeFrame']
    for component in base_components:
        try:
            component_class = globals().get(component)
            if component_class:
                validation_results['base_components_loaded'] += 1
                validation_results['component_status'][component] = 'loaded'
            else:
                validation_results['errors'].append(f"Base component {component} not loaded")
                validation_results['component_status'][component] = 'missing'
        except Exception as e:
            validation_results['errors'].append(f"Error checking {component}: {str(e)}")
            validation_results['component_status'][component] = 'error'
    
    # Check enhanced volume analyzer
    if _enhanced_volume_available:
        validation_results['critical_analyzers_available'] += 1
        validation_results['component_status']['EnhancedVolumeAnalyzer'] = 'loaded'
    else:
        validation_results['warnings'].append("EnhancedVolumeAnalyzer not available")
        validation_results['component_status']['EnhancedVolumeAnalyzer'] = 'missing'
    
    return validation_results
