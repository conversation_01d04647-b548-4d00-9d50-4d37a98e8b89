"""
Timeframe-Aware Prediction Models

This module implements prediction models that incorporate multiple timeframes,
ensemble methods that combine predictions across timeframes, and adaptive
weighting based on timeframe performance.
"""

import os
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
import joblib
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler

# Import ML components
from ml.ml_logging import get_logger

logger = get_logger(__name__)

class TimeframeAwareModel:
    """
    Prediction model that incorporates multiple timeframes.

    This class implements models that explicitly account for data from multiple
    timeframes, with adaptive weighting based on timeframe performance.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the timeframe-aware prediction model.

        Args:
            config: Configuration dictionary with parameters
        """
        # Default configuration
        default_config = {
            # Timeframe parameters
            'timeframe_hierarchy': ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],  # Ordered from lowest to highest
            'base_timeframe': '1h',  # Timeframe for primary predictions

            # Model parameters
            'model_type': 'ensemble',  # 'ensemble', 'hierarchical', or 'integrated'
            'base_algorithm': 'random_forest',  # 'random_forest', 'gradient_boosting', 'linear', 'ridge'
            'prediction_target': 'price',  # 'price', 'direction', 'volatility', 'liquidity'
            'prediction_horizon': 10,  # Number of periods ahead to predict

            # Feature parameters
            'use_price_features': True,
            'use_volume_features': True,
            'use_liquidity_features': True,
            'use_persistence_features': True,
            'use_interaction_features': True,

            # Ensemble parameters
            'ensemble_method': 'weighted',  # 'weighted', 'stacked', or 'boosted'
            'enable_adaptive_weights': True,
            'weight_update_frequency': 10,  # Update weights every N predictions

            # Training parameters
            'train_test_split': 0.8,  # Fraction of data to use for training
            'n_splits': 5,  # Number of splits for time series cross-validation
            'max_samples': 1000,  # Maximum number of samples to use for training

            # Paths
            'model_save_path': 'models/timeframe_aware',
            'enable_model_saving': True
        }

        # Initialize configuration
        self.config = default_config.copy()
        if config:
            self.config.update(config)

        # Initialize models
        self.models = {}
        self.ensemble_model = None
        self.scalers = {}
        self.feature_importances = {}
        self.timeframe_weights = {tf: 1.0 for tf in self.config['timeframe_hierarchy']}
        self.performance_metrics = {}

        # Initialize training history
        self.training_history = {
            'train_errors': [],
            'val_errors': [],
            'timeframe_weights': [],
            'feature_importances': []
        }

        logger.info("Initialized timeframe-aware prediction model")

    def train(self,
             feature_data: Dict[str, pd.DataFrame],
             target_data: Dict[str, pd.DataFrame],
             validation_split: float = 0.2) -> Dict[str, Any]:
        """
        Train the timeframe-aware prediction model.

        Args:
            feature_data: Dictionary mapping timeframes to feature DataFrames
            target_data: Dictionary mapping timeframes to target DataFrames
            validation_split: Fraction of data to use for validation

        Returns:
            Dictionary with training results
        """
        if not feature_data or not target_data:
            logger.error("No feature or target data provided")
            return {'success': False, 'error': 'No data provided'}

        logger.info("Training timeframe-aware prediction model")
        start_time = time.time()

        try:
            # Step 1: Prepare data for each timeframe
            prepared_data = self._prepare_training_data(feature_data, target_data, validation_split)

            # Step 2: Train models based on configuration
            if self.config['model_type'] == 'ensemble':
                training_results = self._train_ensemble_model(prepared_data)
            elif self.config['model_type'] == 'hierarchical':
                training_results = self._train_hierarchical_model(prepared_data)
            elif self.config['model_type'] == 'integrated':
                training_results = self._train_integrated_model(prepared_data)
            else:
                logger.error(f"Unknown model type: {self.config['model_type']}")
                return {'success': False, 'error': f"Unknown model type: {self.config['model_type']}"}

            # Step 3: Calculate and store feature importances
            self._calculate_feature_importances()

            # Step 4: Initialize timeframe weights
            self._initialize_timeframe_weights(training_results)

            # Step 5: Save models if enabled
            if self.config['enable_model_saving']:
                self._save_models()

            # Calculate and log timing
            elapsed = time.time() - start_time
            logger.info(f"Completed model training in {elapsed:.2f} seconds")

            # Prepare results
            results = {
                'success': True,
                'elapsed_time': elapsed,
                'training_results': training_results,
                'feature_importances': self.feature_importances,
                'timeframe_weights': self.timeframe_weights
            }

            return results

        except Exception as e:
            logger.error(f"Error training timeframe-aware model: {e}")
            return {'success': False, 'error': str(e)}

    def predict(self,
               feature_data: Dict[str, pd.DataFrame],
               update_weights: bool = True) -> Dict[str, Any]:
        """
        Make predictions using the timeframe-aware model.

        Args:
            feature_data: Dictionary mapping timeframes to feature DataFrames
            update_weights: Whether to update timeframe weights based on performance

        Returns:
            Dictionary with prediction results
        """
        if not feature_data:
            logger.error("No feature data provided")
            return {'success': False, 'error': 'No data provided'}

        if not self.models:
            logger.error("Models not trained")
            return {'success': False, 'error': 'Models not trained'}

        logger.info("Making predictions with timeframe-aware model")
        start_time = time.time()

        try:
            # Step 1: Prepare data for prediction
            prepared_data = self._prepare_prediction_data(feature_data)

            # Step 2: Make predictions based on model type
            if self.config['model_type'] == 'ensemble':
                prediction_results = self._predict_with_ensemble(prepared_data)
            elif self.config['model_type'] == 'hierarchical':
                prediction_results = self._predict_with_hierarchical(prepared_data)
            elif self.config['model_type'] == 'integrated':
                prediction_results = self._predict_with_integrated(prepared_data)
            else:
                logger.error(f"Unknown model type: {self.config['model_type']}")
                return {'success': False, 'error': f"Unknown model type: {self.config['model_type']}"}

            # Step 3: Update timeframe weights if enabled and requested
            if self.config['enable_adaptive_weights'] and update_weights:
                self._update_timeframe_weights(prediction_results)

            # Calculate and log timing
            elapsed = time.time() - start_time
            logger.info(f"Completed predictions in {elapsed:.2f} seconds")

            # Prepare results
            results = {
                'success': True,
                'elapsed_time': elapsed,
                'predictions': prediction_results,
                'timeframe_weights': self.timeframe_weights
            }

            return results

        except Exception as e:
            logger.error(f"Error making predictions with timeframe-aware model: {e}")
            return {'success': False, 'error': str(e)}

    def _prepare_training_data(self,
                              feature_data: Dict[str, pd.DataFrame],
                              target_data: Dict[str, pd.DataFrame],
                              validation_split: float) -> Dict[str, Dict[str, Any]]:
        """
        Prepare data for training.

        Args:
            feature_data: Dictionary mapping timeframes to feature DataFrames
            target_data: Dictionary mapping timeframes to target DataFrames
            validation_split: Fraction of data to use for validation

        Returns:
            Dictionary with prepared training data
        """
        prepared_data = {}

        # Process each timeframe
        for tf in feature_data:
            if tf not in target_data:
                logger.warning(f"No target data for timeframe {tf}, skipping")
                continue

            # Get features and target for this timeframe
            X = feature_data[tf]
            y = target_data[tf]

            # Skip if empty
            if X.empty or y.empty:
                logger.warning(f"Empty data for timeframe {tf}, skipping")
                continue

            # Align data
            aligned_data = pd.concat([X, y], axis=1, join='inner')
            if aligned_data.empty:
                logger.warning(f"No aligned data for timeframe {tf}, skipping")
                continue

            # Split features and target
            X_aligned = aligned_data[X.columns]
            y_aligned = aligned_data[y.columns]

            # Scale features
            scaler = StandardScaler()
            X_scaled = pd.DataFrame(
                scaler.fit_transform(X_aligned),
                index=X_aligned.index,
                columns=X_aligned.columns
            )

            # Store scaler
            self.scalers[tf] = scaler

            # Split into train and validation sets
            split_idx = int(len(X_scaled) * (1 - validation_split))

            # Ensure we don't use too many samples
            if self.config['max_samples'] > 0 and split_idx > self.config['max_samples']:
                start_idx = split_idx - self.config['max_samples']
                X_train = X_scaled.iloc[start_idx:split_idx]
                y_train = y_aligned.iloc[start_idx:split_idx]
            else:
                X_train = X_scaled.iloc[:split_idx]
                y_train = y_aligned.iloc[:split_idx]

            X_val = X_scaled.iloc[split_idx:]
            y_val = y_aligned.iloc[split_idx:]

            # Store prepared data
            prepared_data[tf] = {
                'X_train': X_train,
                'y_train': y_train,
                'X_val': X_val,
                'y_val': y_val,
                'feature_columns': X.columns,
                'target_columns': y.columns
            }

        return prepared_data

    def _prepare_prediction_data(self, feature_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Prepare data for prediction.

        Args:
            feature_data: Dictionary mapping timeframes to feature DataFrames

        Returns:
            Dictionary with prepared prediction data
        """
        prepared_data = {}

        # Process each timeframe
        for tf in feature_data:
            # Skip if no model or scaler for this timeframe
            if tf not in self.models or tf not in self.scalers:
                logger.warning(f"No model or scaler for timeframe {tf}, skipping")
                continue

            # Get features for this timeframe
            X = feature_data[tf]

            # Skip if empty
            if X.empty:
                logger.warning(f"Empty data for timeframe {tf}, skipping")
                continue

            # Scale features using stored scaler
            scaler = self.scalers[tf]
            X_scaled = pd.DataFrame(
                scaler.transform(X),
                index=X.index,
                columns=X.columns
            )

            # Store prepared data
            prepared_data[tf] = X_scaled

        return prepared_data

    def _create_model(self):
        """
        Create a model based on configuration.

        Returns:
            Initialized model
        """
        if self.config['base_algorithm'] == 'random_forest':
            return RandomForestRegressor(
                n_estimators=100,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42
            )
        elif self.config['base_algorithm'] == 'gradient_boosting':
            return GradientBoostingRegressor(
                n_estimators=100,
                learning_rate=0.1,
                max_depth=3,
                random_state=42
            )
        elif self.config['base_algorithm'] == 'linear':
            return LinearRegression()
        elif self.config['base_algorithm'] == 'ridge':
            return Ridge(alpha=1.0)
        else:
            logger.warning(f"Unknown algorithm: {self.config['base_algorithm']}, using random forest")
            return RandomForestRegressor(
                n_estimators=100,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42
            )

    def _train_ensemble_model(self, prepared_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Train ensemble of models for different timeframes.

        Args:
            prepared_data: Dictionary with prepared training data

        Returns:
            Dictionary with training results
        """
        training_results = {
            'models': {},
            'metrics': {},
            'ensemble': {}
        }

        # Train a model for each timeframe
        for tf, data in prepared_data.items():
            # Get training data
            X_train = data['X_train']
            y_train = data['y_train']
            X_val = data['X_val']
            y_val = data['y_val']

            # Skip if not enough data
            if len(X_train) < 10 or len(X_val) < 5:
                logger.warning(f"Not enough data for timeframe {tf}, skipping")
                continue

            # Create model based on configuration
            model = self._create_model()

            # Train model
            model.fit(X_train, y_train)

            # Store model
            self.models[tf] = model

            # Evaluate model
            train_pred = model.predict(X_train)
            val_pred = model.predict(X_val)

            train_mse = mean_squared_error(y_train, train_pred)
            val_mse = mean_squared_error(y_val, val_pred)
            train_mae = mean_absolute_error(y_train, train_pred)
            val_mae = mean_absolute_error(y_val, val_pred)
            train_r2 = r2_score(y_train, train_pred)
            val_r2 = r2_score(y_val, val_pred)

            # Store metrics
            metrics = {
                'train_mse': train_mse,
                'val_mse': val_mse,
                'train_mae': train_mae,
                'val_mae': val_mae,
                'train_r2': train_r2,
                'val_r2': val_r2
            }

            self.performance_metrics[tf] = metrics
            training_results['metrics'][tf] = metrics
            training_results['models'][tf] = {
                'type': self.config['base_algorithm'],
                'features': list(X_train.columns)
            }

        # Train ensemble model if using stacked ensemble
        if self.config['ensemble_method'] == 'stacked' and len(self.models) > 1:
            # Prepare data for stacked model
            stacked_X_train = []
            stacked_y_train = None
            stacked_X_val = []
            stacked_y_val = None

            # Get predictions from each model for stacking
            for tf, data in prepared_data.items():
                if tf not in self.models:
                    continue

                # Get model predictions
                model = self.models[tf]
                train_pred = model.predict(data['X_train']).reshape(-1, 1)
                val_pred = model.predict(data['X_val']).reshape(-1, 1)

                # Add to stacked data
                stacked_X_train.append(train_pred)
                stacked_X_val.append(val_pred)

                # Set target data (same for all timeframes)
                if stacked_y_train is None:
                    stacked_y_train = data['y_train']
                    stacked_y_val = data['y_val']

            # Combine predictions
            stacked_X_train = np.hstack(stacked_X_train)
            stacked_X_val = np.hstack(stacked_X_val)

            # Create and train stacked model
            stacked_model = Ridge(alpha=1.0)
            stacked_model.fit(stacked_X_train, stacked_y_train)

            # Store stacked model
            self.ensemble_model = stacked_model

            # Evaluate stacked model
            stacked_train_pred = stacked_model.predict(stacked_X_train)
            stacked_val_pred = stacked_model.predict(stacked_X_val)

            stacked_train_mse = mean_squared_error(stacked_y_train, stacked_train_pred)
            stacked_val_mse = mean_squared_error(stacked_y_val, stacked_val_pred)
            stacked_train_mae = mean_absolute_error(stacked_y_train, stacked_train_pred)
            stacked_val_mae = mean_absolute_error(stacked_y_val, stacked_val_pred)
            stacked_train_r2 = r2_score(stacked_y_train, stacked_train_pred)
            stacked_val_r2 = r2_score(stacked_y_val, stacked_val_pred)

            # Store ensemble metrics
            ensemble_metrics = {
                'train_mse': stacked_train_mse,
                'val_mse': stacked_val_mse,
                'train_mae': stacked_train_mae,
                'val_mae': stacked_val_mae,
                'train_r2': stacked_train_r2,
                'val_r2': stacked_val_r2
            }

            training_results['ensemble'] = {
                'type': 'stacked',
                'metrics': ensemble_metrics,
                'coefficients': stacked_model.coef_.tolist()
            }

        return training_results

    def _predict_with_ensemble(self, prepared_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Make predictions using ensemble of models.

        Args:
            prepared_data: Dictionary with prepared prediction data

        Returns:
            Dictionary with prediction results
        """
        prediction_results = {
            'predictions': {},
            'ensemble_prediction': None,
            'timeframe_weights': self.timeframe_weights.copy()
        }

        # Make predictions with each model
        individual_predictions = {}

        for tf, X in prepared_data.items():
            if tf not in self.models:
                continue

            # Get model
            model = self.models[tf]

            # Make prediction
            pred = model.predict(X)

            # Store prediction
            individual_predictions[tf] = pred
            prediction_results['predictions'][tf] = pred.tolist()

        # Combine predictions based on ensemble method
        if self.config['ensemble_method'] == 'weighted' and individual_predictions:
            # Get weighted average of predictions
            weighted_preds = []
            weights = []

            for tf, pred in individual_predictions.items():
                weighted_preds.append(pred * self.timeframe_weights[tf])
                weights.append(self.timeframe_weights[tf])

            # Calculate weighted average
            if weights:
                ensemble_pred = sum(weighted_preds) / sum(weights)
                prediction_results['ensemble_prediction'] = ensemble_pred.tolist()

        elif self.config['ensemble_method'] == 'stacked' and self.ensemble_model is not None:
            # Prepare data for stacked model
            stacked_X = []

            for tf in self.models:
                if tf not in prepared_data:
                    continue

                # Get model predictions
                model = self.models[tf]
                pred = model.predict(prepared_data[tf]).reshape(-1, 1)

                # Add to stacked data
                stacked_X.append(pred)

            # Combine predictions
            if stacked_X:
                stacked_X = np.hstack(stacked_X)

                # Make prediction with stacked model
                ensemble_pred = self.ensemble_model.predict(stacked_X)
                prediction_results['ensemble_prediction'] = ensemble_pred.tolist()

        return prediction_results

    def _predict_with_hierarchical(self, prepared_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Make predictions using hierarchical model.

        Args:
            prepared_data: Dictionary with prepared prediction data

        Returns:
            Dictionary with prediction results
        """
        prediction_results = {
            'predictions': {},
            'hierarchical_prediction': None,
            'timeframe_weights': self.timeframe_weights.copy()
        }

        # Get ordered timeframes based on hierarchy
        ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in prepared_data]

        if len(ordered_timeframes) < 2:
            logger.warning("Not enough timeframes for hierarchical prediction")
            return prediction_results

        # Make predictions from highest to lowest timeframe
        for i in range(len(ordered_timeframes) - 1, -1, -1):
            tf = ordered_timeframes[i]

            if tf not in self.models:
                continue

            # Get model and data for this timeframe
            model = self.models[tf]
            X = prepared_data[tf]

            # For lower timeframes, incorporate predictions from higher timeframes
            if i < len(ordered_timeframes) - 1:
                # Get predictions from higher timeframes
                higher_predictions = []

                for j in range(i + 1, len(ordered_timeframes)):
                    higher_tf = ordered_timeframes[j]

                    if higher_tf not in prediction_results['predictions']:
                        continue

                    # Get predictions from higher timeframe
                    higher_pred = np.array(prediction_results['predictions'][higher_tf]).reshape(-1, 1)

                    # Add to predictions
                    higher_predictions.append(higher_pred)

                # If we have predictions from higher timeframes, incorporate them
                if higher_predictions:
                    # Combine predictions
                    higher_pred = np.hstack(higher_predictions)

                    # Add as features
                    X_with_higher = np.hstack([X, higher_pred])

                    # Update column names
                    higher_cols = [f"higher_tf_{j}" for j in range(higher_pred.shape[1])]
                    X_cols = list(X.columns) + higher_cols

                    # Convert back to DataFrame
                    X = pd.DataFrame(X_with_higher, index=X.index, columns=X_cols)

            # Make prediction
            pred = model.predict(X)

            # Store prediction
            prediction_results['predictions'][tf] = pred.tolist()

        # Use prediction from base timeframe as hierarchical prediction
        base_tf = self.config['base_timeframe']
        if base_tf in prediction_results['predictions']:
            prediction_results['hierarchical_prediction'] = prediction_results['predictions'][base_tf]

        return prediction_results

    def _predict_with_integrated(self, prepared_data: Dict[str, pd.DataFrame]) -> Dict[str, Any]:
        """
        Make predictions using integrated model.

        Args:
            prepared_data: Dictionary with prepared prediction data

        Returns:
            Dictionary with prediction results
        """
        prediction_results = {
            'prediction': None,
            'timeframe_weights': self.timeframe_weights.copy()
        }

        # Get base timeframe
        base_tf = self.config['base_timeframe']

        if base_tf not in prepared_data or base_tf not in self.models:
            logger.error(f"Base timeframe {base_tf} not available for prediction")
            return prediction_results

        # Get model and data for base timeframe
        model = self.models[base_tf]
        X = prepared_data[base_tf]

        # Incorporate features from other timeframes
        integrated_features = {}

        for tf, tf_X in prepared_data.items():
            if tf == base_tf:
                continue

            # Skip if empty
            if tf_X.empty:
                continue

            # Add prefix to column names to avoid conflicts
            tf_X.columns = [f"{tf}_{col}" for col in tf_X.columns]

            # Store for integration
            integrated_features[tf] = tf_X

        # If we have features from other timeframes, integrate them
        if integrated_features:
            # Align all data
            aligned_X = X

            for tf, tf_X in integrated_features.items():
                # Combine with base features
                aligned_X = pd.concat([aligned_X, tf_X], axis=1)

            # Update prediction data
            X = aligned_X

        try:
            # Make prediction
            pred = model.predict(X)

            # Store prediction
            prediction_results['prediction'] = pred.tolist()
        except Exception as e:
            logger.error(f"Error making prediction with integrated model: {e}")
            return prediction_results

        return prediction_results

    def _calculate_feature_importances(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate feature importances for each model.

        Returns:
            Dictionary mapping timeframes to feature importance dictionaries
        """
        feature_importances = {}

        for tf, model in self.models.items():
            # Skip if model doesn't support feature importances
            if not hasattr(model, 'feature_importances_'):
                continue

            # Get feature importances
            importances = model.feature_importances_

            # Get feature names
            if tf in self.scalers:
                feature_names = self.scalers[tf].feature_names_in_
            else:
                feature_names = [f"feature_{i}" for i in range(len(importances))]

            # Create dictionary mapping feature names to importances
            tf_importances = dict(zip(feature_names, importances))

            # Store feature importances
            feature_importances[tf] = tf_importances

        self.feature_importances = feature_importances
        return feature_importances

    def _initialize_timeframe_weights(self, training_results: Dict[str, Any]) -> None:
        """
        Initialize timeframe weights based on validation performance.

        Args:
            training_results: Dictionary with training results
        """
        # Reset weights
        self.timeframe_weights = {tf: 1.0 for tf in self.config['timeframe_hierarchy']}

        # Update weights based on validation performance
        if 'metrics' in training_results:
            for tf, metrics in training_results['metrics'].items():
                # Skip if no validation metrics
                if 'val_mse' not in metrics:
                    continue

                # Calculate weight based on inverse MSE
                val_mse = metrics['val_mse']
                if val_mse > 0:
                    self.timeframe_weights[tf] = 1.0 / val_mse

        # Normalize weights
        total_weight = sum(self.timeframe_weights.values())
        if total_weight > 0:
            for tf in self.timeframe_weights:
                self.timeframe_weights[tf] /= total_weight

    def _update_timeframe_weights(self, prediction_results: Dict[str, Any]) -> None:
        """
        Update timeframe weights based on recent performance.

        Args:
            prediction_results: Dictionary with prediction results
        """
        # Skip if adaptive weights not enabled
        if not self.config['enable_adaptive_weights']:
            return

        # Skip if no performance metrics
        if not self.performance_metrics:
            return

        # Update weights based on validation performance
        for tf, metrics in self.performance_metrics.items():
            # Skip if no validation metrics
            if 'val_mse' not in metrics:
                continue

            # Calculate weight based on inverse MSE
            val_mse = metrics['val_mse']
            if val_mse > 0:
                self.timeframe_weights[tf] = 1.0 / val_mse

        # Normalize weights
        total_weight = sum(self.timeframe_weights.values())
        if total_weight > 0:
            for tf in self.timeframe_weights:
                self.timeframe_weights[tf] /= total_weight

    def _save_models(self) -> None:
        """
        Save models to disk.
        """
        # Skip if model saving not enabled
        if not self.config['enable_model_saving']:
            return

        # Create directory if it doesn't exist
        os.makedirs(self.config['model_save_path'], exist_ok=True)

        # Save each model
        for tf, model in self.models.items():
            model_path = os.path.join(self.config['model_save_path'], f"{tf}_model.joblib")
            joblib.dump(model, model_path)
            logger.info(f"Saved model for timeframe {tf} to {model_path}")

        # Save ensemble model if it exists
        if self.ensemble_model is not None:
            ensemble_path = os.path.join(self.config['model_save_path'], "ensemble_model.joblib")
            joblib.dump(self.ensemble_model, ensemble_path)
            logger.info(f"Saved ensemble model to {ensemble_path}")

        # Save scalers
        for tf, scaler in self.scalers.items():
            scaler_path = os.path.join(self.config['model_save_path'], f"{tf}_scaler.joblib")
            joblib.dump(scaler, scaler_path)
            logger.info(f"Saved scaler for timeframe {tf} to {scaler_path}")

        # Save configuration
        config_path = os.path.join(self.config['model_save_path'], "config.joblib")
        joblib.dump(self.config, config_path)
        logger.info(f"Saved configuration to {config_path}")

    def load_models(self, model_path: Optional[str] = None) -> bool:
        """
        Load models from disk.

        Args:
            model_path: Optional path to load models from

        Returns:
            True if models were loaded successfully, False otherwise
        """
        # Use configured path if none provided
        if model_path is None:
            model_path = self.config['model_save_path']

        try:
            # Load configuration
            config_path = os.path.join(model_path, "config.joblib")
            if os.path.exists(config_path):
                self.config = joblib.load(config_path)
                logger.info(f"Loaded configuration from {config_path}")

            # Load models
            for tf in self.config['timeframe_hierarchy']:
                model_path_tf = os.path.join(model_path, f"{tf}_model.joblib")
                if os.path.exists(model_path_tf):
                    self.models[tf] = joblib.load(model_path_tf)
                    logger.info(f"Loaded model for timeframe {tf} from {model_path_tf}")

            # Load ensemble model
            ensemble_path = os.path.join(model_path, "ensemble_model.joblib")
            if os.path.exists(ensemble_path):
                self.ensemble_model = joblib.load(ensemble_path)
                logger.info(f"Loaded ensemble model from {ensemble_path}")

            # Load scalers
            for tf in self.config['timeframe_hierarchy']:
                scaler_path = os.path.join(model_path, f"{tf}_scaler.joblib")
                if os.path.exists(scaler_path):
                    self.scalers[tf] = joblib.load(scaler_path)
                    logger.info(f"Loaded scaler for timeframe {tf} from {scaler_path}")

            return True

        except Exception as e:
            logger.error(f"Error loading models: {e}")
            return False

    def get_feature_importances(self, timeframe: Optional[str] = None, top_n: int = 10) -> Dict[str, Any]:
        """
        Get feature importances for a specific timeframe or all timeframes.

        Args:
            timeframe: Optional timeframe to get feature importances for
            top_n: Number of top features to return

        Returns:
            Dictionary with feature importances
        """
        if not self.feature_importances:
            self._calculate_feature_importances()

        if timeframe is not None:
            if timeframe in self.feature_importances:
                # Get importances for specific timeframe
                importances = self.feature_importances[timeframe]

                # Sort by importance
                sorted_importances = sorted(importances.items(), key=lambda x: x[1], reverse=True)

                # Return top N
                return {
                    'timeframe': timeframe,
                    'importances': dict(sorted_importances[:top_n])
                }
            else:
                return {'timeframe': timeframe, 'importances': {}}
        else:
            # Get importances for all timeframes
            result = {}

            for tf, importances in self.feature_importances.items():
                # Sort by importance
                sorted_importances = sorted(importances.items(), key=lambda x: x[1], reverse=True)

                # Add to result
                result[tf] = dict(sorted_importances[:top_n])

            return {'all_timeframes': result}

    def get_timeframe_weights(self) -> Dict[str, float]:
        """
        Get current timeframe weights.

        Returns:
            Dictionary mapping timeframes to weights
        """
        return self.timeframe_weights.copy()

    def set_timeframe_weights(self, weights: Dict[str, float]) -> None:
        """
        Set timeframe weights manually.

        Args:
            weights: Dictionary mapping timeframes to weights
        """
        # Update weights
        for tf, weight in weights.items():
            if tf in self.timeframe_weights:
                self.timeframe_weights[tf] = weight

        # Normalize weights
        total_weight = sum(self.timeframe_weights.values())
        if total_weight > 0:
            for tf in self.timeframe_weights:
                self.timeframe_weights[tf] /= total_weight
    def _prepare_prediction_data(self, feature_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Prepare data for prediction.

        Args:
            feature_data: Dictionary mapping timeframes to feature DataFrames

        Returns:
            Dictionary with prepared prediction data
        """
        prepared_data = {}

        # Process each timeframe
        for tf in feature_data:
            # Skip if no model or scaler for this timeframe
            if tf not in self.models or tf not in self.scalers:
                logger.warning(f"No model or scaler for timeframe {tf}, skipping")
                continue

            # Get features for this timeframe
            X = feature_data[tf]

            # Skip if empty
            if X.empty:
                logger.warning(f"Empty data for timeframe {tf}, skipping")
                continue

            # Scale features using stored scaler
            scaler = self.scalers[tf]
            X_scaled = pd.DataFrame(
                scaler.transform(X),
                index=X.index,
                columns=X.columns
            )

            # Store prepared data
            prepared_data[tf] = X_scaled

        return prepared_data

    def _train_ensemble_model(self, prepared_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Train ensemble of models for different timeframes.

        Args:
            prepared_data: Dictionary with prepared training data

        Returns:
            Dictionary with training results
        """
        training_results = {
            'models': {},
            'metrics': {},
            'ensemble': {}
        }

        # Train a model for each timeframe
        for tf, data in prepared_data.items():
            # Get training data
            X_train = data['X_train']
            y_train = data['y_train']
            X_val = data['X_val']
            y_val = data['y_val']

            # Skip if not enough data
            if len(X_train) < 10 or len(X_val) < 5:
                logger.warning(f"Not enough data for timeframe {tf}, skipping")
                continue

            # Create model based on configuration
            model = self._create_model()

            # Train model
            model.fit(X_train, y_train)

            # Store model
            self.models[tf] = model

            # Evaluate model
            train_pred = model.predict(X_train)
            val_pred = model.predict(X_val)

            train_mse = mean_squared_error(y_train, train_pred)
            val_mse = mean_squared_error(y_val, val_pred)
            train_mae = mean_absolute_error(y_train, train_pred)
            val_mae = mean_absolute_error(y_val, val_pred)
            train_r2 = r2_score(y_train, train_pred)
            val_r2 = r2_score(y_val, val_pred)

            # Store metrics
            metrics = {
                'train_mse': train_mse,
                'val_mse': val_mse,
                'train_mae': train_mae,
                'val_mae': val_mae,
                'train_r2': train_r2,
                'val_r2': val_r2
            }

            self.performance_metrics[tf] = metrics
            training_results['metrics'][tf] = metrics
            training_results['models'][tf] = {
                'type': self.config['base_algorithm'],
                'features': list(X_train.columns)
            }

        # Train ensemble model if using stacked ensemble
        if self.config['ensemble_method'] == 'stacked' and len(self.models) > 1:
            # Prepare data for stacked model
            stacked_X_train = []
            stacked_y_train = None
            stacked_X_val = []
            stacked_y_val = None

            # Get predictions from each model for stacking
            for tf, data in prepared_data.items():
                if tf not in self.models:
                    continue

                # Get model predictions
                model = self.models[tf]
                train_pred = model.predict(data['X_train']).reshape(-1, 1)
                val_pred = model.predict(data['X_val']).reshape(-1, 1)

                # Add to stacked data
                stacked_X_train.append(train_pred)
                stacked_X_val.append(val_pred)

                # Set target data (same for all timeframes)
                if stacked_y_train is None:
                    stacked_y_train = data['y_train']
                    stacked_y_val = data['y_val']

            # Combine predictions
            stacked_X_train = np.hstack(stacked_X_train)
            stacked_X_val = np.hstack(stacked_X_val)

            # Create and train stacked model
            stacked_model = Ridge(alpha=1.0)
            stacked_model.fit(stacked_X_train, stacked_y_train)

            # Store stacked model
            self.ensemble_model = stacked_model

            # Evaluate stacked model
            stacked_train_pred = stacked_model.predict(stacked_X_train)
            stacked_val_pred = stacked_model.predict(stacked_X_val)

            stacked_train_mse = mean_squared_error(stacked_y_train, stacked_train_pred)
            stacked_val_mse = mean_squared_error(stacked_y_val, stacked_val_pred)
            stacked_train_mae = mean_absolute_error(stacked_y_train, stacked_train_pred)
            stacked_val_mae = mean_absolute_error(stacked_y_val, stacked_val_pred)
            stacked_train_r2 = r2_score(stacked_y_train, stacked_train_pred)
            stacked_val_r2 = r2_score(stacked_y_val, stacked_val_pred)

            # Store ensemble metrics
            ensemble_metrics = {
                'train_mse': stacked_train_mse,
                'val_mse': stacked_val_mse,
                'train_mae': stacked_train_mae,
                'val_mae': stacked_val_mae,
                'train_r2': stacked_train_r2,
                'val_r2': stacked_val_r2
            }

            training_results['ensemble'] = {
                'type': 'stacked',
                'metrics': ensemble_metrics,
                'coefficients': stacked_model.coef_.tolist()
            }

        return training_results

    def _train_hierarchical_model(self, prepared_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Train hierarchical model that incorporates features from multiple timeframes.

        Args:
            prepared_data: Dictionary with prepared training data

        Returns:
            Dictionary with training results
        """
        training_results = {
            'models': {},
            'metrics': {},
            'hierarchy': {}
        }

        # Get ordered timeframes based on hierarchy
        ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in prepared_data]

        if len(ordered_timeframes) < 2:
            logger.warning("Not enough timeframes for hierarchical model")
            return training_results

        # Train models from highest to lowest timeframe
        for i in range(len(ordered_timeframes) - 1, -1, -1):
            tf = ordered_timeframes[i]

            # Get training data for this timeframe
            data = prepared_data[tf]
            X_train = data['X_train']
            y_train = data['y_train']
            X_val = data['X_val']
            y_val = data['y_val']

            # Skip if not enough data
            if len(X_train) < 10 or len(X_val) < 5:
                logger.warning(f"Not enough data for timeframe {tf}, skipping")
                continue

            # For lower timeframes, incorporate predictions from higher timeframes
            if i < len(ordered_timeframes) - 1:
                # Get predictions from higher timeframes
                higher_predictions_train = []
                higher_predictions_val = []

                for j in range(i + 1, len(ordered_timeframes)):
                    higher_tf = ordered_timeframes[j]

                    if higher_tf not in self.models:
                        continue

                    # Get predictions from higher timeframe model
                    higher_model = self.models[higher_tf]
                    higher_data = prepared_data[higher_tf]

                    # Align indices
                    common_train_idx = X_train.index.intersection(higher_data['X_train'].index)
                    common_val_idx = X_val.index.intersection(higher_data['X_val'].index)

                    if len(common_train_idx) > 0 and len(common_val_idx) > 0:
                        # Get aligned data
                        higher_X_train = higher_data['X_train'].loc[common_train_idx]
                        higher_X_val = higher_data['X_val'].loc[common_val_idx]

                        # Get predictions
                        higher_train_pred = higher_model.predict(higher_X_train).reshape(-1, 1)
                        higher_val_pred = higher_model.predict(higher_X_val).reshape(-1, 1)

                        # Add to predictions
                        higher_predictions_train.append(higher_train_pred)
                        higher_predictions_val.append(higher_val_pred)

                # If we have predictions from higher timeframes, incorporate them
                if higher_predictions_train and higher_predictions_val:
                    # Combine predictions
                    higher_train_pred = np.hstack(higher_predictions_train)
                    higher_val_pred = np.hstack(higher_predictions_val)

                    # Add as features
                    X_train_with_higher = np.hstack([X_train, higher_train_pred])
                    X_val_with_higher = np.hstack([X_val, higher_val_pred])

                    # Update column names
                    higher_cols = [f"higher_tf_{j}" for j in range(higher_train_pred.shape[1])]
                    X_train_cols = list(X_train.columns) + higher_cols

                    # Convert back to DataFrame
                    X_train = pd.DataFrame(X_train_with_higher, index=X_train.index, columns=X_train_cols)
                    X_val = pd.DataFrame(X_val_with_higher, index=X_val.index, columns=X_train_cols)

            # Create model based on configuration
            model = self._create_model()

            # Train model
            model.fit(X_train, y_train)

            # Store model
            self.models[tf] = model

            # Evaluate model
            train_pred = model.predict(X_train)
            val_pred = model.predict(X_val)

            train_mse = mean_squared_error(y_train, train_pred)
            val_mse = mean_squared_error(y_val, val_pred)
            train_mae = mean_absolute_error(y_train, train_pred)
            val_mae = mean_absolute_error(y_val, val_pred)
            train_r2 = r2_score(y_train, train_pred)
            val_r2 = r2_score(y_val, val_pred)

            # Store metrics
            metrics = {
                'train_mse': train_mse,
                'val_mse': val_mse,
                'train_mae': train_mae,
                'val_mae': val_mae,
                'train_r2': train_r2,
                'val_r2': val_r2
            }

            self.performance_metrics[tf] = metrics
            training_results['metrics'][tf] = metrics
            training_results['models'][tf] = {
                'type': self.config['base_algorithm'],
                'features': list(X_train.columns)
            }

            # Store hierarchy information
            if i < len(ordered_timeframes) - 1:
                training_results['hierarchy'][tf] = {
                    'higher_timeframes': ordered_timeframes[i+1:],
                    'num_higher_features': len(higher_cols) if 'higher_cols' in locals() else 0
                }

        return training_results

    def _train_integrated_model(self, prepared_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Train integrated model that combines features from all timeframes.

        Args:
            prepared_data: Dictionary with prepared training data

        Returns:
            Dictionary with training results
        """
        training_results = {
            'model': {},
            'metrics': {},
            'integration': {}
        }

        # Get base timeframe
        base_tf = self.config['base_timeframe']

        if base_tf not in prepared_data:
            logger.error(f"Base timeframe {base_tf} not in prepared data")
            return training_results

        # Get training data for base timeframe
        base_data = prepared_data[base_tf]
        X_train = base_data['X_train']
        y_train = base_data['y_train']
        if isinstance(y_train, pd.DataFrame):
            y_train = y_train.values.ravel()
        X_val = base_data['X_val']
        y_val = base_data['y_val']
        if isinstance(y_val, pd.DataFrame):
            y_val = y_val.values.ravel()

        # Skip if not enough data
        if len(X_train) < 10 or len(X_val) < 5:
            logger.warning(f"Not enough data for base timeframe {base_tf}")
            return training_results

        # Incorporate features from other timeframes
        integrated_features = {}

        for tf, data in prepared_data.items():
            if tf == base_tf:
                continue

            # Get features for this timeframe
            tf_X_train = data['X_train']
            tf_X_val = data['X_val']

            # Skip if not enough data
            if tf_X_train.empty or tf_X_val.empty:
                continue

            # Align indices with base timeframe
            common_train_idx = X_train.index.intersection(tf_X_train.index)
            common_val_idx = X_val.index.intersection(tf_X_val.index)

            if len(common_train_idx) > 0 and len(common_val_idx) > 0:
                # Get aligned data
                aligned_tf_X_train = tf_X_train.loc[common_train_idx]
                aligned_tf_X_val = tf_X_val.loc[common_val_idx]

                # Add prefix to column names to avoid conflicts
                aligned_tf_X_train.columns = [f"{tf}_{col}" for col in aligned_tf_X_train.columns]
                aligned_tf_X_val.columns = [f"{tf}_{col}" for col in aligned_tf_X_val.columns]

                # Store for integration
                integrated_features[tf] = {
                    'X_train': aligned_tf_X_train,
                    'X_val': aligned_tf_X_val
                }

        # If we have features from other timeframes, integrate them
        if integrated_features:
            # Align base data with common indices
            common_train_idx = X_train.index
            common_val_idx = X_val.index

            for tf, tf_data in integrated_features.items():
                common_train_idx = common_train_idx.intersection(tf_data['X_train'].index)
                common_val_idx = common_val_idx.intersection(tf_data['X_val'].index)

            # Skip if not enough common data
            if len(common_train_idx) < 10 or len(common_val_idx) < 5:
                logger.warning("Not enough common data across timeframes")
                return training_results

            # Align all data
            aligned_X_train = X_train.loc[common_train_idx]
            aligned_y_train = y_train.loc[common_train_idx]
            aligned_X_val = X_val.loc[common_val_idx]
            aligned_y_val = y_val.loc[common_val_idx]

            # Combine features from all timeframes
            for tf, tf_data in integrated_features.items():
                aligned_tf_X_train = tf_data['X_train'].loc[common_train_idx]
                aligned_tf_X_val = tf_data['X_val'].loc[common_val_idx]

                # Combine with base features
                aligned_X_train = pd.concat([aligned_X_train, aligned_tf_X_train], axis=1)
                aligned_X_val = pd.concat([aligned_X_val, aligned_tf_X_val], axis=1)

            # Update training data
            X_train = aligned_X_train
            y_train = aligned_y_train
            X_val = aligned_X_val
            y_val = aligned_y_val

        # Create model based on configuration
        model = self._create_model()

        # Train model
        model.fit(X_train, y_train)

        # Store model
        self.models[base_tf] = model

        # Evaluate model
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)

        train_mse = mean_squared_error(y_train, train_pred)
        val_mse = mean_squared_error(y_val, val_pred)
        train_mae = mean_absolute_error(y_train, train_pred)
        val_mae = mean_absolute_error(y_val, val_pred)
        train_r2 = r2_score(y_train, train_pred)
        val_r2 = r2_score(y_val, val_pred)

        # Store metrics
        metrics = {
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_r2': train_r2,
            'val_r2': val_r2
        }

        self.performance_metrics[base_tf] = metrics
        training_results['metrics'] = metrics
        training_results['model'] = {
            'type': self.config['base_algorithm'],
            'features': list(X_train.columns)
        }

        # Store integration information
        training_results['integration'] = {
            'base_timeframe': base_tf,
            'integrated_timeframes': list(integrated_features.keys()),
            'num_features': X_train.shape[1],
            'num_samples': X_train.shape[0]
        }

        return training_results
