[{"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:12.692042", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:28:12.692013", "success": true, "latency_ms": 150, "quality_score": 0.95, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:12.692646", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:28:12.692632", "success": true, "latency_ms": 150, "quality_score": 0.95, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:12.693170", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:28:12.693151", "success": true, "latency_ms": 150, "quality_score": 0.95, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:28:12.693638", "result": {"timestamp": "2025-06-24T09:28:12.693622", "total_execution_time_ms": 35000, "budget_compliance": true, "budget_limit_ms": 45000, "ticker_results": {"SPY": {"success": true, "execution_time": 11000, "output_files": ["SPY_analysis.json"], "quality_metrics": {"overall": 0.92}}, "QQQ": {"success": true, "execution_time": 11000, "output_files": ["QQQ_analysis.json"], "quality_metrics": {"overall": 0.92}}, "AAPL": {"success": true, "execution_time": 11000, "output_files": ["AAPL_analysis.json"], "quality_metrics": {"overall": 0.92}}}, "overall_success": true}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:17.694753", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:28:17.694731", "success": true, "latency_ms": 170, "quality_score": 0.9299999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:17.695431", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:28:17.695417", "success": true, "latency_ms": 170, "quality_score": 0.9299999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:17.696026", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:28:17.696014", "success": true, "latency_ms": 170, "quality_score": 0.9299999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:22.697117", "result": {"ticker": "SPY", "timestamp": "2025-06-24T09:28:22.697059", "success": true, "latency_ms": 190, "quality_score": 0.9099999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:22.698367", "result": {"ticker": "QQQ", "timestamp": "2025-06-24T09:28:22.698351", "success": true, "latency_ms": 190, "quality_score": 0.9099999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "ticker_ingestion", "timestamp": "2025-06-24T09:28:22.699121", "result": {"ticker": "AAPL", "timestamp": "2025-06-24T09:28:22.699108", "success": true, "latency_ms": 190, "quality_score": 0.9099999999999999, "data_source": "mock_polygon", "data_points": 8, "error": null}}, {"session_id": "20250624_092812", "test_type": "orchestrator_cycle", "timestamp": "2025-06-24T09:28:22.699885", "result": {"timestamp": "2025-06-24T09:28:22.699868", "total_execution_time_ms": 39000, "budget_compliance": true, "budget_limit_ms": 45000, "ticker_results": {"SPY": {"success": true, "execution_time": 12000, "output_files": ["SPY_analysis.json"], "quality_metrics": {"overall": 0.92}}, "QQQ": {"success": true, "execution_time": 12000, "output_files": ["QQQ_analysis.json"], "quality_metrics": {"overall": 0.92}}, "AAPL": {"success": true, "execution_time": 12000, "output_files": ["AAPL_analysis.json"], "quality_metrics": {"overall": 0.92}}}, "overall_success": true}}]