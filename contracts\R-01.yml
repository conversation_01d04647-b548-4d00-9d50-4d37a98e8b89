task_id: R-01
name: Auto Broker Adapter (Tradier Sandbox)
version: 2.0.0
description: >
  Converts risk-guarded execution plan into a Tradier paper order and returns
  a fill JSON.

agent:
  type: AutoBrokerAdapterAgent
  priority: HIGH
  phase: 5

inputs:
  required:
    - execution_plan_md     # Risk-guarded execution plan file
    - unified_analysis_json # Analysis with ticker and trade details
  optional:
    - slippage_tolerance   # Maximum acceptable slippage in cents

outputs:
  files:
    - path: fills/{{date}}/{{ticker}}_fill.json
      must_exist: true
      schema: schemas/tradier_fill_v1.json
  metrics:
    - slippage_cents: Maximum 5 cents acceptable
    - execution_time: Sub-second order placement
    - fill_confirmation: Required order confirmation

success_criteria:
  perf_budget:
    max_runtime_ms: 1000
  slippage_ok:
    max_cents: 5
  code_coverage_min: 0.95
  fill_validation:
    required_fields: ["status", "price", "quantity", "symbol"]
    acceptable_statuses: ["filled", "ok", "pending"]

error_handling:
  api_timeout: 8000ms
  slippage_exceeded: Reject order if >5 cents
  invalid_plan: Validate plan format before submission
  network_error: Retry once with exponential backoff

integration:
  orchestrator: After RiskGuardAgent execution
  dependencies: ["output_coordination", "risk_guard"]
  environment:
    - TRADIER_TOKEN: Bearer token for sandbox API
    - TRADIER_URL: https://sandbox.tradier.com/v1

test_scenarios:
  - name: successful_fill
    input: Valid execution plan with AAPL call option
    expected: Fill JSON with acceptable slippage
  - name: slippage_rejection
    input: Plan with high slippage scenario
    expected: ValueError with slippage details
  - name: invalid_plan
    input: Malformed execution plan
    expected: Parsing error with clear message
  - name: api_timeout
    input: Valid plan with network timeout
    expected: Timeout error with retry logic
