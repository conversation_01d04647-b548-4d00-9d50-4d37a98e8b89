# agents/signal_generator_agent.py
from datetime import datetime as dt
from agents.agent_base import BaseAgent

class SignalGeneratorAgent(BaseAgent):
    """
    Signal Generator Agent - Creates properly formatted signals with direction
    **ENHANCED: Real-time Schwab MCP integration for live bid/ask spreads**
    """
    
    def __init__(self, agent_id="signal_generator_agent"):
        super().__init__(agent_id)
        
        # Initialize real-time data agent for enhanced signal quality
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.real_time_agent = EnhancedDataAgent()
            self.has_real_time = True
            self.logger.info("Signal Generator initialized with real-time current candle access")
        except ImportError:
            self.real_time_agent = None
            self.has_real_time = False
            self.logger.warning("Real-time agent unavailable - using standard signal generation")
    
    def execute(self, ticker, consensus_score):
        """
        Returns a fully-formed signal dict that passes unified_analysis_v1.1 schema.
        **ENHANCED: Uses real-time bid/ask spreads for signal quality improvement**
        """
        
        # Get real-time market data for enhanced signal generation
        signal_quality_factor = 1.0
        bid_ask_spread = None
        
        if self.has_real_time:
            try:
                result = self.real_time_agent.get_market_data([ticker])
                
                if result and result.get("source") == "schwab_broker":
                    ticker_data = result["data"][ticker]
                    
                    if ticker_data.get("is_current_candle"):
                        # Use real-time current candle for signal enhancement
                        live_bid = ticker_data.get("bid")
                        live_ask = ticker_data.get("ask")
                        
                        if live_bid and live_ask:
                            bid_ask_spread = (live_ask - live_bid) / live_ask
                            # Tighter spreads indicate better signal quality
                            signal_quality_factor = max(0.5, 1.0 - (bid_ask_spread * 10))
                            
                            self.logger.info(f"[{ticker}] Enhanced with real-time spread: {bid_ask_spread:.4f}, quality: {signal_quality_factor:.2f}")
                        else:
                            self.logger.info(f"[{ticker}] Real-time data available, no bid/ask")
                    else:
                        self.logger.info(f"[{ticker}] Using most recent available data")
                        
            except Exception as e:
                self.logger.warning(f"[{ticker}] Real-time enhancement failed: {e}")
        
        # -- existing score logic with enhancement -----------------
        direction = "CALL" if consensus_score >= 0 else "PUT"
        # Apply signal quality enhancement from real-time data
        enhanced_confidence = abs(consensus_score) * signal_quality_factor
        confidence = min(100.0, enhanced_confidence)  # Cap at 100
        # --------------------------------------------------------
        
        result = {
            "ticker": ticker,
            "direction": direction,
            "confidence": round(confidence, 2),
            "timestamp": dt.utcnow().isoformat(timespec="seconds") + "Z"
        }
        
        # Add real-time metadata if available
        if bid_ask_spread is not None:
            result["real_time_enhancement"] = {
                "bid_ask_spread": round(bid_ask_spread, 6),
                "signal_quality_factor": round(signal_quality_factor, 4),
                "enhanced_confidence": round(enhanced_confidence, 2)
            }
        
        return result
    
    def execute_task(self, task):
        """Execute task interface"""
        ticker = task.inputs.get("ticker")
        consensus_score = task.inputs.get("consensus_score", 0.0)
        return self.execute(ticker, consensus_score)
    
    def validate_inputs(self, task):
        """Validate task inputs"""
        return "ticker" in task.inputs
    
    def validate_outputs(self, outputs):
        """Validate outputs meet quality standards"""
        required_fields = ["ticker", "direction", "confidence", "timestamp"]
        if all(field in outputs for field in required_fields):
            return {"signal_completeness": 1.0}
        return {"signal_completeness": 0.0}
