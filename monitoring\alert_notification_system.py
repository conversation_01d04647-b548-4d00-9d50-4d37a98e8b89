#!/usr/bin/env python3
"""
Alert and Notification System
Handles critical system failures, risk threshold breaches, execution anomalies, and performance warnings
"""

import smtplib
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from enum import Enum
import threading
from collections import deque

class AlertSeverity(Enum):
    """Alert severity levels"""
    INFO = "INFO"
    WARNING = "WARNING" 
    CRITICAL = "CRITICAL"
    EMERGENCY = "EMERGENCY"

class AlertCategory(Enum):
    """Alert categories"""
    SYSTEM_FAILURE = "SYSTEM_FAILURE"
    RISK_BREACH = "RISK_BREACH"
    EXECUTION_ANOMALY = "EXECUTION_ANOMALY"
    PERFORMANCE_DEGRADATION = "PERFORMANCE_DEGRADATION"
    API_CONNECTIVITY = "API_CONNECTIVITY"
    DATA_QUALITY = "DATA_QUALITY"

@dataclass
class Alert:
    """Alert data structure"""
    alert_id: str
    timestamp: datetime
    severity: AlertSeverity
    category: AlertCategory
    title: str
    message: str
    source_component: str
    metadata: Dict[str, Any]
    acknowledged: bool = False
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class NotificationChannel:
    """Notification channel configuration"""
    channel_type: str  # 'email', 'sms', 'webhook', 'slack'
    config: Dict[str, Any]
    enabled: bool = True
    severity_filter: List[AlertSeverity] = None

class AlertNotificationSystem:
    """
    Comprehensive alert and notification system
    Mathematical precision in threshold monitoring and escalation
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Alert configuration
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'cpu_warning': 70.0,
            'cpu_critical': 85.0,
            'memory_warning': 80.0,
            'memory_critical': 90.0,
            'response_time_critical': 5.0,
            'error_rate_critical': 0.05,
            'pnl_loss_warning': 1000.0,
            'pnl_loss_critical': 5000.0,
            'position_size_critical': 0.12,
            'var_critical': 0.05
        })
        
        # Alert storage and tracking
        self.active_alerts = {}
        self.alert_history = deque(maxlen=10000)
        self.notification_channels = {}
        
        self.logger = logging.getLogger(__name__)
    
    def add_notification_channel(self, name: str, channel: NotificationChannel):
        """Add a notification channel"""
        self.notification_channels[name] = channel
        self.logger.info(f"Added notification channel: {name}")
    
    def create_alert(self, 
                    severity: AlertSeverity,
                    category: AlertCategory,
                    title: str,
                    message: str,
                    source_component: str,
                    metadata: Optional[Dict] = None) -> str:
        """Create and process a new alert"""
        
        alert_id = self._generate_alert_id()
        
        alert = Alert(
            alert_id=alert_id,
            timestamp=datetime.now(),
            severity=severity,
            category=category,
            title=title,
            message=message,
            source_component=source_component,
            metadata=metadata or {}
        )
        
        # Store alert
        self.active_alerts[alert_id] = alert
        self.alert_history.append(alert)
        
        # Log alert
        self.logger.log(
            self._get_log_level(severity),
            f"ALERT [{severity.value}] {category.value}: {title} - {message}"
        )
        
        return alert_id
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "system"):
        """Acknowledge an alert"""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].acknowledged = True
            self.logger.info(f"Alert acknowledged: {alert_id} by {acknowledged_by}")
    
    def resolve_alert(self, alert_id: str, resolved_by: str = "system"):
        """Resolve an alert"""
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolution_time = datetime.now()
            del self.active_alerts[alert_id]
            self.logger.info(f"Alert resolved: {alert_id} by {resolved_by}")
    
    def check_system_health_alerts(self, health_metrics: Dict[str, Any]):
        """Check system health metrics against thresholds"""
        
        # CPU usage alerts
        cpu_usage = health_metrics.get('cpu_usage', 0.0)
        if cpu_usage >= self.alert_thresholds['cpu_critical']:
            self.create_alert(
                AlertSeverity.CRITICAL,
                AlertCategory.SYSTEM_FAILURE,
                "Critical CPU Usage",
                f"CPU usage at {cpu_usage:.1f}%",
                "system_monitor",
                {"cpu_usage": cpu_usage}
            )
        
        # Memory usage alerts
        memory_usage = health_metrics.get('memory_usage', 0.0)
        if memory_usage >= self.alert_thresholds['memory_critical']:
            self.create_alert(
                AlertSeverity.CRITICAL,
                AlertCategory.SYSTEM_FAILURE,
                "Critical Memory Usage",
                f"Memory usage at {memory_usage:.1f}%",
                "system_monitor",
                {"memory_usage": memory_usage}
            )
    
    def check_risk_alerts(self, risk_metrics: Dict[str, Any]):
        """Check risk metrics against thresholds"""
        
        # VaR alerts
        var_95 = abs(risk_metrics.get('var_95', 0.0))
        portfolio_value = risk_metrics.get('portfolio_value', 1.0)
        var_percentage = var_95 / portfolio_value if portfolio_value > 0 else 0.0
        
        if var_percentage >= self.alert_thresholds['var_critical']:
            self.create_alert(
                AlertSeverity.CRITICAL,
                AlertCategory.RISK_BREACH,
                "Critical VaR Breach",
                f"VaR at {var_percentage:.1%} of portfolio",
                "risk_monitor",
                {"var_95": var_95, "var_percentage": var_percentage}
            )
    
    def check_execution_alerts(self, execution_metrics: Dict[str, Any]):
        """Check execution quality metrics"""
        
        # Slippage alerts
        avg_slippage = execution_metrics.get('avg_slippage', 0.0)
        if abs(avg_slippage) >= 0.005:  # 0.5% critical slippage
            self.create_alert(
                AlertSeverity.CRITICAL,
                AlertCategory.EXECUTION_ANOMALY,
                "High Slippage Detected",
                f"Average slippage {avg_slippage:.3%}",
                "execution_monitor",
                {"avg_slippage": avg_slippage}
            )
    
    def check_performance_alerts(self, performance_metrics: Dict[str, Any]):
        """Check trading performance metrics"""
        
        # P&L alerts
        daily_pnl = performance_metrics.get('daily_pnl', 0.0)
        if daily_pnl <= -self.alert_thresholds['pnl_loss_critical']:
            self.create_alert(
                AlertSeverity.CRITICAL,
                AlertCategory.RISK_BREACH,
                "Critical Daily Loss",
                f"Daily P&L: ${daily_pnl:.2f}",
                "performance_monitor",
                {"daily_pnl": daily_pnl}
            )
    
    def _generate_alert_id(self) -> str:
        """Generate unique alert ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"ALERT_{timestamp}_{len(self.alert_history):04d}"
    
    def _get_log_level(self, severity: AlertSeverity) -> int:
        """Get logging level for alert severity"""
        level_map = {
            AlertSeverity.INFO: logging.INFO,
            AlertSeverity.WARNING: logging.WARNING,
            AlertSeverity.CRITICAL: logging.ERROR,
            AlertSeverity.EMERGENCY: logging.CRITICAL
        }
        return level_map.get(severity, logging.INFO)
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """Get comprehensive alert summary"""
        return {
            'timestamp': datetime.now().isoformat(),
            'active_alerts': len(self.active_alerts),
            'total_alerts_today': len([a for a in self.alert_history 
                                     if a.timestamp.date() == datetime.now().date()]),
            'unacknowledged_alerts': [
                asdict(alert) for alert in self.active_alerts.values()
                if not alert.acknowledged
            ]
        }

if __name__ == "__main__":
    # Test the alert system
    alert_system = AlertNotificationSystem()
    
    # Add a log notification channel
    log_channel = NotificationChannel(
        channel_type='log',
        config={},
        severity_filter=[AlertSeverity.WARNING, AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]
    )
    alert_system.add_notification_channel('log', log_channel)
    
    # Create test alerts
    alert_system.create_alert(
        AlertSeverity.WARNING,
        AlertCategory.PERFORMANCE_DEGRADATION,
        "High CPU Usage",
        "CPU usage at 75% for 5 minutes",
        "system_monitor",
        {"cpu_usage": 75.0}
    )
    
    alert_system.create_alert(
        AlertSeverity.CRITICAL,
        AlertCategory.RISK_BREACH,
        "Position Size Limit Exceeded",
        "Position size at 15% of portfolio",
        "risk_monitor",
        {"position_size": 0.15}
    )
    
    # Get summary
    summary = alert_system.get_alert_summary()
    print(json.dumps(summary, indent=2, default=str))
