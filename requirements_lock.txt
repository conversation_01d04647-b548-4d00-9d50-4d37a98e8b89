# CORE Flow Detection System - Production Lock File
# Mathematical precision requires exact version control
# DO NOT MODIFY WITHOUT VALIDATION - numerical drift risk

# Critical numerical packages - FROZEN
numpy==1.24.3
scipy==1.10.1
pandas==2.0.3

# Core dependencies - STABLE
requests==2.31.0
matplotlib==3.7.2

# ML/Analytics - VALIDATED
scikit-learn==1.3.0
joblib==1.3.2

# Development - LOCKED
pytest==7.4.0
pytest-cov==4.1.0

# Code quality - ENFORCED
ruff==0.1.8
pylint==2.17.5
pre-commit==3.4.0

# Hash verification for critical packages
# numpy==1.24.3 --hash=sha256:xxxxx
# scipy==1.10.1 --hash=sha256:xxxxx
# pandas==2.0.3 --hash=sha256:xxxxx
