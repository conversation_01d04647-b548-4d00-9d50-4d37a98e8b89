"""
Backtesting framework for simulating and evaluating trading strategies.

This module provides classes and functions to backtest trading strategies with
realistic market simulation, including slippage, market impact, and execution modeling.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging
import os
import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
import torch

from .execution_optimizer import ExecutionStrategy
from ..ml_model_registry import ModelRegistry

# Configure logging
logger = logging.getLogger(__name__)


class OrderType(Enum):
    """Order types for trading."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"


class OrderSide(Enum):
    """Order sides for trading."""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """Order statuses for tracking."""
    PENDING = "pending"
    ACTIVE = "active"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class Order:
    """
    Order representation for backtesting.
    
    This class represents a trading order with execution parameters and status tracking.
    """
    
    def __init__(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        limit_price: Optional[float] = None,
        trailing_pct: Optional[float] = None,
        time_in_force: str = "GTC",
        execution_strategy: Optional[ExecutionStrategy] = None,
        execution_params: Optional[Dict[str, Any]] = None,
        parent_order_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize an order.
        
        Args:
            symbol: Trading symbol
            order_type: Type of order
            side: Order side (buy or sell)
            quantity: Order quantity
            price: Order price
            stop_price: Stop price for stop orders
            limit_price: Limit price for limit and stop-limit orders
            trailing_pct: Trailing percentage for trailing stop orders
            time_in_force: Time in force (GTC, IOC, FOK, etc.)
            execution_strategy: Execution strategy
            execution_params: Execution parameters
            parent_order_id: ID of parent order (for child orders)
            metadata: Additional metadata
        """
        self.order_id = f"order_{datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        self.symbol = symbol
        self.order_type = order_type
        self.side = side
        self.quantity = quantity
        self.price = price
        self.stop_price = stop_price
        self.limit_price = limit_price
        self.trailing_pct = trailing_pct
        self.time_in_force = time_in_force
        self.execution_strategy = execution_strategy
        self.execution_params = execution_params or {}
        self.parent_order_id = parent_order_id
        self.metadata = metadata or {}
        
        # Execution tracking
        self.status = OrderStatus.PENDING
        self.filled_quantity = 0.0
        self.average_fill_price = 0.0
        self.fills = []
        self.created_at = datetime.datetime.now()
        self.updated_at = self.created_at
        self.filled_at = None
        self.cancelled_at = None
        self.expired_at = None
        self.rejected_at = None
        
        # Position tracking
        self.position_id = None
        self.trade_id = None
        
        # Child orders (for complex execution strategies)
        self.child_orders = []
    
    def update_status(self, status: OrderStatus) -> None:
        """
        Update order status.
        
        Args:
            status: New status
        """
        self.status = status
        self.updated_at = datetime.datetime.now()
        
        if status == OrderStatus.FILLED:
            self.filled_at = self.updated_at
        elif status == OrderStatus.CANCELLED:
            self.cancelled_at = self.updated_at
        elif status == OrderStatus.EXPIRED:
            self.expired_at = self.updated_at
        elif status == OrderStatus.REJECTED:
            self.rejected_at = self.updated_at
    
    def add_fill(self, quantity: float, price: float, timestamp: Optional[datetime.datetime] = None) -> None:
        """
        Add a fill to the order.
        
        Args:
            quantity: Filled quantity
            price: Fill price
            timestamp: Fill timestamp
        """
        if timestamp is None:
            timestamp = datetime.datetime.now()
        
        # Create fill record
        fill = {
            "quantity": quantity,
            "price": price,
            "timestamp": timestamp
        }
        
        # Add fill to fills list
        self.fills.append(fill)
        
        # Update filled quantity and average price
        self.filled_quantity += quantity
        
        # Calculate new average fill price (weighted average)
        total_value = sum(f["quantity"] * f["price"] for f in self.fills)
        self.average_fill_price = total_value / self.filled_quantity if self.filled_quantity > 0 else 0.0
        
        # Update status
        if self.filled_quantity >= self.quantity:
            self.update_status(OrderStatus.FILLED)
        elif self.filled_quantity > 0:
            self.update_status(OrderStatus.PARTIALLY_FILLED)
        
        # Update timestamp
        self.updated_at = timestamp
    
    def cancel(self) -> bool:
        """
        Cancel the order.
        
        Returns:
            True if cancelled, False otherwise
        """
        if self.status in [OrderStatus.PENDING, OrderStatus.ACTIVE, OrderStatus.PARTIALLY_FILLED]:
            self.update_status(OrderStatus.CANCELLED)
            return True
        
        return False
    
    def reject(self, reason: str) -> None:
        """
        Reject the order.
        
        Args:
            reason: Rejection reason
        """
        self.update_status(OrderStatus.REJECTED)
        self.metadata["rejection_reason"] = reason
    
    def expire(self) -> None:
        """Expire the order."""
        self.update_status(OrderStatus.EXPIRED)
    
    def get_unfilled_quantity(self) -> float:
        """
        Get unfilled quantity.
        
        Returns:
            Unfilled quantity
        """
        return self.quantity - self.filled_quantity
    
    def is_active(self) -> bool:
        """
        Check if the order is active.
        
        Returns:
            True if active, False otherwise
        """
        return self.status in [OrderStatus.PENDING, OrderStatus.ACTIVE, OrderStatus.PARTIALLY_FILLED]
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert order to dictionary.
        
        Returns:
            Order as dictionary
        """
        return {
            "order_id": self.order_id,
            "symbol": self.symbol,
            "order_type": self.order_type.value,
            "side": self.side.value,
            "quantity": self.quantity,
            "price": self.price,
            "stop_price": self.stop_price,
            "limit_price": self.limit_price,
            "trailing_pct": self.trailing_pct,
            "time_in_force": self.time_in_force,
            "execution_strategy": self.execution_strategy.value if self.execution_strategy else None,
            "status": self.status.value,
            "filled_quantity": self.filled_quantity,
            "average_fill_price": self.average_fill_price,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "filled_at": self.filled_at.isoformat() if self.filled_at else None,
            "cancelled_at": self.cancelled_at.isoformat() if self.cancelled_at else None,
            "expired_at": self.expired_at.isoformat() if self.expired_at else None,
            "rejected_at": self.rejected_at.isoformat() if self.rejected_at else None,
            "position_id": self.position_id,
            "trade_id": self.trade_id,
            "parent_order_id": self.parent_order_id,
            "metadata": self.metadata
        }


class Position:
    """
    Position representation for backtesting.
    
    This class represents a trading position with entry, exit, and performance tracking.
    """
    
    def __init__(
        self,
        symbol: str,
        side: OrderSide,
        entry_order: Order,
        trade_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a position.
        
        Args:
            symbol: Trading symbol
            side: Position side (buy or sell)
            entry_order: Entry order
            trade_id: Trade ID
            metadata: Additional metadata
        """
        self.position_id = f"position_{datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        self.symbol = symbol
        self.side = side
        self.entry_order = entry_order
        self.trade_id = trade_id
        self.metadata = metadata or {}
        
        # Link position to entry order
        self.entry_order.position_id = self.position_id
        self.entry_order.trade_id = self.trade_id
        
        # Position tracking
        self.quantity = entry_order.quantity
        self.entry_price = entry_order.average_fill_price
        self.entry_cost = self.entry_price * self.quantity
        self.entry_time = entry_order.filled_at
        
        # Exit tracking
        self.exit_orders = []
        self.exit_price = None
        self.exit_value = None
        self.exit_time = None
        self.is_closed = False
        
        # Performance tracking
        self.profit_loss = None
        self.profit_loss_pct = None
        self.duration = None
    
    def add_exit_order(self, exit_order: Order) -> None:
        """
        Add an exit order.
        
        Args:
            exit_order: Exit order
        """
        # Link exit order to position
        exit_order.position_id = self.position_id
        exit_order.trade_id = self.trade_id
        
        # Add to exit orders
        self.exit_orders.append(exit_order)
    
    def update_from_fills(self) -> None:
        """Update position from fills."""
        # Check if entry order has fills
        if not self.entry_order.fills:
            return
        
        # Update entry price and cost
        self.entry_price = self.entry_order.average_fill_price
        self.entry_cost = self.entry_price * self.quantity
        self.entry_time = self.entry_order.filled_at
        
        # Check if exit orders have fills
        filled_exit_quantity = 0.0
        total_exit_value = 0.0
        latest_exit_time = None
        
        for exit_order in self.exit_orders:
            for fill in exit_order.fills:
                filled_exit_quantity += fill["quantity"]
                total_exit_value += fill["quantity"] * fill["price"]
                
                if latest_exit_time is None or fill["timestamp"] > latest_exit_time:
                    latest_exit_time = fill["timestamp"]
        
        # Update exit price and value
        if filled_exit_quantity > 0:
            self.exit_price = total_exit_value / filled_exit_quantity
            self.exit_value = total_exit_value
            self.exit_time = latest_exit_time
            
            # Check if position is closed
            if filled_exit_quantity >= self.quantity:
                self.is_closed = True
                
                # Calculate profit/loss
                if self.side == OrderSide.BUY:
                    self.profit_loss = self.exit_value - self.entry_cost
                else:
                    self.profit_loss = self.entry_cost - self.exit_value
                
                # Calculate percentage profit/loss
                self.profit_loss_pct = self.profit_loss / self.entry_cost
                
                # Calculate duration
                if self.entry_time and self.exit_time:
                    self.duration = (self.exit_time - self.entry_time).total_seconds()
    
    def get_current_value(self, current_price: float) -> float:
        """
        Get current position value.
        
        Args:
            current_price: Current price
            
        Returns:
            Current position value
        """
        if self.is_closed:
            return self.exit_value
        
        return self.quantity * current_price
    
    def get_unrealized_pl(self, current_price: float) -> Tuple[float, float]:
        """
        Get unrealized profit/loss.
        
        Args:
            current_price: Current price
            
        Returns:
            Tuple of (profit_loss, profit_loss_pct)
        """
        if self.is_closed:
            return self.profit_loss, self.profit_loss_pct
        
        current_value = self.get_current_value(current_price)
        
        if self.side == OrderSide.BUY:
            profit_loss = current_value - self.entry_cost
        else:
            profit_loss = self.entry_cost - current_value
        
        profit_loss_pct = profit_loss / self.entry_cost
        
        return profit_loss, profit_loss_pct
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert position to dictionary.
        
        Returns:
            Position as dictionary
        """
        return {
            "position_id": self.position_id,
            "symbol": self.symbol,
            "side": self.side.value,
            "quantity": self.quantity,
            "entry_price": self.entry_price,
            "entry_cost": self.entry_cost,
            "entry_time": self.entry_time.isoformat() if self.entry_time else None,
            "exit_price": self.exit_price,
            "exit_value": self.exit_value,
            "exit_time": self.exit_time.isoformat() if self.exit_time else None,
            "is_closed": self.is_closed,
            "profit_loss": self.profit_loss,
            "profit_loss_pct": self.profit_loss_pct,
            "duration": self.duration,
            "trade_id": self.trade_id,
            "entry_order_id": self.entry_order.order_id,
            "exit_order_ids": [order.order_id for order in self.exit_orders],
            "metadata": self.metadata
        }


class Trade:
    """
    Trade representation for backtesting.
    
    This class represents a complete trade with entry and exit, including multiple
    positions if scaling in/out.
    """
    
    def __init__(
        self,
        symbol: str,
        side: OrderSide,
        signal_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize a trade.
        
        Args:
            symbol: Trading symbol
            side: Trade side (buy or sell)
            signal_id: Signal ID that triggered the trade
            metadata: Additional metadata
        """
        self.trade_id = f"trade_{datetime.datetime.now().strftime('%Y%m%d%H%M%S%f')}"
        self.symbol = symbol
        self.side = side
        self.signal_id = signal_id
        self.metadata = metadata or {}
        
        # Positions in this trade
        self.positions = []
        
        # Trade tracking
        self.is_open = True
        self.open_time = datetime.datetime.now()
        self.close_time = None
        
        # Performance tracking
        self.entry_cost = 0.0
        self.exit_value = 0.0
        self.profit_loss = 0.0
        self.profit_loss_pct = 0.0
        self.duration = None
    
    def add_position(self, position: Position) -> None:
        """
        Add a position to the trade.
        
        Args:
            position: Position to add
        """
        # Link position to trade
        position.trade_id = self.trade_id
        
        # Add to positions
        self.positions.append(position)
        
        # Update trade tracking
        self._update_trade_metrics()
    
    def _update_trade_metrics(self) -> None:
        """Update trade metrics from positions."""
        # Reset metrics
        self.entry_cost = 0.0
        self.exit_value = 0.0
        self.profit_loss = 0.0
        
        # Calculate from positions
        all_closed = True
        earliest_entry = None
        latest_exit = None
        
        for position in self.positions:
            # Update entry cost
            self.entry_cost += position.entry_cost
            
            # Track earliest entry
            if position.entry_time:
                if earliest_entry is None or position.entry_time < earliest_entry:
                    earliest_entry = position.entry_time
            
            # Check if position is closed
            if position.is_closed:
                # Update exit value
                self.exit_value += position.exit_value
                
                # Track latest exit
                if position.exit_time:
                    if latest_exit is None or position.exit_time > latest_exit:
                        latest_exit = position.exit_time
            else:
                all_closed = False
        
        # Calculate profit/loss if all positions are closed
        if all_closed and self.positions:
            self.is_open = False
            self.close_time = latest_exit
            
            # Calculate profit/loss
            if self.side == OrderSide.BUY:
                self.profit_loss = self.exit_value - self.entry_cost
            else:
                self.profit_loss = self.entry_cost - self.exit_value
            
            # Calculate percentage profit/loss
            self.profit_loss_pct = self.profit_loss / self.entry_cost if self.entry_cost > 0 else 0.0
            
            # Calculate duration
            if earliest_entry and latest_exit:
                self.duration = (latest_exit - earliest_entry).total_seconds()
    
    def get_unrealized_pl(self, current_price: float) -> Tuple[float, float]:
        """
        Get unrealized profit/loss.
        
        Args:
            current_price: Current price
            
        Returns:
            Tuple of (profit_loss, profit_loss_pct)
        """
        if not self.is_open:
            return self.profit_loss, self.profit_loss_pct
        
        # Calculate unrealized P/L from positions
        total_pl = 0.0
        total_cost = 0.0
        
        for position in self.positions:
            if position.is_closed:
                # Add closed P/L
                total_pl += position.profit_loss
                total_cost += position.entry_cost
            else:
                # Add unrealized P/L
                position_pl, _ = position.get_unrealized_pl(current_price)
                total_pl += position_pl
                total_cost += position.entry_cost
        
        # Calculate percentage P/L
        if total_cost > 0:
            total_pl_pct = total_pl / total_cost
        else:
            total_pl_pct = 0.0
        
        return total_pl, total_pl_pct
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert trade to dictionary.
        
        Returns:
            Trade as dictionary
        """
        return {
            "trade_id": self.trade_id,
            "symbol": self.symbol,
            "side": self.side.value,
            "signal_id": self.signal_id,
            "is_open": self.is_open,
            "open_time": self.open_time.isoformat(),
            "close_time": self.close_time.isoformat() if self.close_time else None,
            "entry_cost": self.entry_cost,
            "exit_value": self.exit_value,
            "profit_loss": self.profit_loss,
            "profit_loss_pct": self.profit_loss_pct,
            "duration": self.duration,
            "position_ids": [position.position_id for position in self.positions],
            "metadata": self.metadata
        }


class MarketSimulator:
    """
    Market simulator for realistic backtesting.
    
    This class simulates market behavior for backtesting trading strategies, including
    price simulation, order execution, slippage, and market impact modeling.
    """
    
    def __init__(
        self,
        data: pd.DataFrame,
        config: Optional[Dict[str, Any]] = None,
        slippage_model: Optional[Any] = None,
        impact_model: Optional[Any] = None
    ):
        """
        Initialize the market simulator.
        
        Args:
            data: Market data
            config: Configuration dictionary
            slippage_model: Slippage prediction model
            impact_model: Market impact prediction model
        """
        self.data = data.copy()
        self.config = config or {}
        
        # Set default configuration
        self.config.setdefault('spread_pct', 0.0001)
        self.config.setdefault('base_slippage_pct', 0.0001)
        self.config.setdefault('base_impact_pct', 0.0001)
        self.config.setdefault('use_ml_models', True)
        self.config.setdefault('slippage_model_name', 'slippage_predictor_v1')
        self.config.setdefault('impact_model_name', 'market_impact_v1')
        
        # Initialize ML models
        self.slippage_model = slippage_model
        self.impact_model = impact_model
        
        if self.config['use_ml_models'] and (not slippage_model or not impact_model):
            self._load_ml_models()
        
        # Current state
        self.current_index = 0
        self.current_timestamp = None
        self.current_bars = {}
        
        # Track filled orders
        self.filled_orders = []
    
    def _load_ml_models(self) -> None:
        """Load ML models for slippage and market impact prediction."""
        model_registry = ModelRegistry()
        
        try:
            # Load slippage model
            if not self.slippage_model:
                self.slippage_model = model_registry.load_model(
                    self.config['slippage_model_name']
                )
            
            # Load market impact model
            if not self.impact_model:
                self.impact_model = model_registry.load_model(
                    self.config['impact_model_name']
                )
            
            logger.info("Loaded ML models for market simulation")
        except Exception as e:
            logger.warning("Failed to load ML models: %s. Using fallback methods.", str(e))
            self.config['use_ml_models'] = False
    
    def reset(self) -> None:
        """Reset the simulator to the start."""
        self.current_index = 0
        self.current_timestamp = None
        self.current_bars = {}
        self.filled_orders = []
    
    def step(self) -> bool:
        """
        Step the simulator forward.
        
        Returns:
            True if successful, False if end of data
        """
        if self.current_index >= len(self.data):
            return False
        
        # Update current state
        self.current_timestamp = self.data.index[self.current_index]
        
        # Update current bars
        if 'symbol' in self.data.columns:
            # Multi-symbol data
            symbols = self.data['symbol'].unique()
            
            for symbol in symbols:
                symbol_data = self.data[self.data['symbol'] == symbol]
                
                if self.current_index < len(symbol_data):
                    self.current_bars[symbol] = symbol_data.iloc[self.current_index]
        else:
            # Single symbol data
            self.current_bars['default'] = self.data.iloc[self.current_index]
        
        # Step to next index
        self.current_index += 1
        
        return True
    
    def get_current_price(self, symbol: str, side: OrderSide) -> float:
        """
        Get current price for symbol and side.
        
        Args:
            symbol: Symbol
            side: Order side
            
        Returns:
            Current price
        """
        # Get current bar
        if symbol in self.current_bars:
            bar = self.current_bars[symbol]
        else:
            bar = self.current_bars.get('default')
            
        if bar is None:
            raise ValueError(f"No data for symbol: {symbol}")
        
        # Get price with spread
        spread = bar['close'] * self.config['spread_pct']
        
        if side == OrderSide.BUY:
            # Buy at ask
            return bar['close'] + spread / 2
        else:
            # Sell at bid
            return bar['close'] - spread / 2
    
    def calculate_slippage(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        price: float,
        order_type: OrderType
    ) -> float:
        """
        Calculate slippage for an order.
        
        Args:
            symbol: Symbol
            side: Order side
            quantity: Order quantity
            price: Order price
            order_type: Order type
            
        Returns:
            Slippage percentage
        """
        # Get current bar
        if symbol in self.current_bars:
            bar = self.current_bars[symbol]
        else:
            bar = self.current_bars.get('default')
            
        if bar is None:
            raise ValueError(f"No data for symbol: {symbol}")
        
        # Use ML model if available
        if self.config['use_ml_models'] and self.slippage_model is not None:
            try:
                # Extract features
                features = self._extract_features(symbol, side, quantity, price, order_type)
                
                # Normalize features
                features_tensor = torch.tensor(features, dtype=torch.float32).unsqueeze(0)
                
                # Predict slippage
                with torch.no_grad():
                    slippage = self.slippage_model(features_tensor).item()
                
                return max(0.0, slippage)
            except Exception as e:
                logger.warning(f"Failed to predict slippage: {str(e)}. Using fallback method.")
        
        # Fallback slippage calculation
        base_slippage = self.config['base_slippage_pct']
        
        # Adjust for volatility
        volatility_factor = 1.0
        if 'high' in bar and 'low' in bar and 'close' in bar:
            hl_range = (bar['high'] - bar['low']) / bar['close']
            volatility_factor = 1.0 + hl_range * 10.0
        
        # Adjust for order type
        order_type_factor = 1.0
        if order_type == OrderType.MARKET:
            order_type_factor = 2.0
        elif order_type == OrderType.LIMIT:
            order_type_factor = 0.5
        
        # Adjust for quantity (size impact)
        quantity_factor = 1.0
        if 'volume' in bar and bar['volume'] > 0:
            quantity_factor = 1.0 + (quantity / bar['volume']) * 10.0
        
        return base_slippage * volatility_factor * order_type_factor * quantity_factor
    
    def calculate_market_impact(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        price: float
    ) -> float:
        """
        Calculate market impact for an order.
        
        Args:
            symbol: Symbol
            side: Order side
            quantity: Order quantity
            price: Order price
            
        Returns:
            Market impact percentage
        """
        # Get current bar
        if symbol in self.current_bars:
            bar = self.current_bars[symbol]
        else:
            bar = self.current_bars.get('default')
            
        if bar is None:
            raise ValueError(f"No data for symbol: {symbol}")
        
        # Use ML model if available
        if self.config['use_ml_models'] and self.impact_model is not None:
            try:
                # Extract features
                features = self._extract_features(symbol, side, quantity, price, None)
                
                # Normalize features
                features_tensor = torch.tensor(features, dtype=torch.float32).unsqueeze(0)
                
                # Predict market impact
                with torch.no_grad():
                    impact = self.impact_model(features_tensor).item()
                
                return max(0.0, impact)
            except Exception as e:
                logger.warning(f"Failed to predict market impact: {str(e)}. Using fallback method.")
        
        # Fallback impact calculation
        base_impact = self.config['base_impact_pct']
        
        # Adjust for volatility
        volatility_factor = 1.0
        if 'high' in bar and 'low' in bar and 'close' in bar:
            hl_range = (bar['high'] - bar['low']) / bar['close']
            volatility_factor = 1.0 + hl_range * 10.0
        
        # Adjust for quantity (size impact)
        quantity_factor = 1.0
        if 'volume' in bar and bar['volume'] > 0:
            quantity_factor = 1.0 + (quantity / bar['volume']) * 20.0
        
        return base_impact * volatility_factor * quantity_factor
    
    def _extract_features(
        self,
        symbol: str,
        side: OrderSide,
        quantity: float,
        price: float,
        order_type: Optional[OrderType]
    ) -> np.ndarray:
        """
        Extract features for ML models.
        
        Args:
            symbol: Symbol
            side: Order side
            quantity: Order quantity
            price: Order price
            order_type: Order type
            
        Returns:
            Feature array
        """
        # Get current bar
        if symbol in self.current_bars:
            bar = self.current_bars[symbol]
        else:
            bar = self.current_bars.get('default')
            
        if bar is None:
            raise ValueError(f"No data for symbol: {symbol}")
        
        # Basic market features
        close = bar['close']
        high = bar.get('high', close)
        low = bar.get('low', close)
        volume = bar.get('volume', 0.0)
        
        # Calculate price range
        price_range = (high - low) / close
        
        # Calculate relative quantity
        relative_quantity = quantity / volume if volume > 0 else 0.0
        
        # Order type and side features
        order_type_market = 1.0 if order_type == OrderType.MARKET else 0.0
        order_type_limit = 1.0 if order_type == OrderType.LIMIT else 0.0
        side_buy = 1.0 if side == OrderSide.BUY else 0.0
        
        # Combine features
        features = np.array([
            price_range,
            relative_quantity,
            order_type_market,
            order_type_limit,
            side_buy
        ])
        
        return features
    
    def execute_order(self, order: Order) -> Order:
        """
        Execute an order.
        
        Args:
            order: Order to execute
            
        Returns:
            Executed order
        """
        # Skip if already filled or cancelled
        if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED]:
            return order
        
        # Get current price
        current_price = self.get_current_price(order.symbol, order.side)
        
        # Check if order should be executed
        should_execute = False
        execution_price = None
        
        if order.order_type == OrderType.MARKET:
            # Market orders execute immediately
            should_execute = True
            execution_price = current_price
            
        elif order.order_type == OrderType.LIMIT:
            # Limit orders execute if price is favorable
            if order.side == OrderSide.BUY and current_price <= order.limit_price:
                should_execute = True
                execution_price = min(current_price, order.limit_price)
            elif order.side == OrderSide.SELL and current_price >= order.limit_price:
                should_execute = True
                execution_price = max(current_price, order.limit_price)
                
        elif order.order_type == OrderType.STOP:
            # Stop orders execute if price crosses stop level
            if order.side == OrderSide.BUY and current_price >= order.stop_price:
                should_execute = True
                execution_price = current_price
            elif order.side == OrderSide.SELL and current_price <= order.stop_price:
                should_execute = True
                execution_price = current_price
                
        elif order.order_type == OrderType.STOP_LIMIT:
            # Stop-limit orders activate when price crosses stop level,
            # then execute like a limit order
            if order.side == OrderSide.BUY and current_price >= order.stop_price:
                # Activate as limit order
                order.order_type = OrderType.LIMIT
                order.price = order.limit_price
                order.update_status(OrderStatus.ACTIVE)
            elif order.side == OrderSide.SELL and current_price <= order.stop_price:
                # Activate as limit order
                order.order_type = OrderType.LIMIT
                order.price = order.limit_price
                order.update_status(OrderStatus.ACTIVE)
                
        elif order.order_type == OrderType.TRAILING_STOP:
            # Trailing stop orders have a moving stop level based on best price
            # Implementation would track best price since order placement
            # and adjust stop level accordingly
            # For simplicity, treat like regular stop order in this example
            if order.stop_price is not None:
                if order.side == OrderSide.BUY and current_price >= order.stop_price:
                    should_execute = True
                    execution_price = current_price
                elif order.side == OrderSide.SELL and current_price <= order.stop_price:
                    should_execute = True
                    execution_price = current_price
        
        # Execute order if conditions are met
        if should_execute and execution_price is not None:
            # Calculate slippage
            slippage_pct = self.calculate_slippage(
                order.symbol, order.side, order.quantity, execution_price, order.order_type
            )
            
            # Apply slippage to execution price
            if order.side == OrderSide.BUY:
                execution_price *= (1.0 + slippage_pct)
            else:
                execution_price *= (1.0 - slippage_pct)
            
            # Calculate market impact (for future prices)
            impact_pct = self.calculate_market_impact(
                order.symbol, order.side, order.quantity, execution_price
            )
            
            # Apply market impact to future prices
            # In a real simulator, this would affect prices for subsequent orders
            # For now, just record the impact in the order metadata
            order.metadata["market_impact_pct"] = impact_pct
            
            # Record the fill
            order.add_fill(
                quantity=order.quantity,
                price=execution_price,
                timestamp=self.current_timestamp
            )
            
            # Update order status
            order.update_status(OrderStatus.FILLED)
            
            # Add to filled orders
            self.filled_orders.append(order)
        
        return order


class StrategyBacktester:
    """
    Strategy backtester for trading strategies.
    
    This class runs backtests for trading strategies with realistic execution simulation
    and performance analysis.
    """
    
    def __init__(
        self,
        data: pd.DataFrame,
        config: Optional[Dict[str, Any]] = None,
        market_simulator: Optional[MarketSimulator] = None
    ):
        """
        Initialize the strategy backtester.
        
        Args:
            data: Market data
            config: Configuration dictionary
            market_simulator: Market simulator
        """
        self.data = data.copy()
        self.config = config or {}
        
        # Set default configuration
        self.config.setdefault('initial_capital', 100000.0)
        self.config.setdefault('commission_pct', 0.001)
        self.config.setdefault('max_position_pct', 0.02)
        
        # Initialize market simulator
        if market_simulator is None:
            self.market_simulator = MarketSimulator(data, self.config)
        else:
            self.market_simulator = market_simulator
        
        # Reset simulator
        self.market_simulator.reset()
        
        # Portfolio tracking
        self.initial_capital = self.config['initial_capital']
        self.cash = self.initial_capital
        self.positions = []
        self.trades = []
        self.orders = []
        self.active_orders = []
        
        # Performance tracking
        self.equity_curve = []
        self.trade_history = []
        self.performance_metrics = {}
    
    def reset(self) -> None:
        """Reset the backtester."""
        # Reset market simulator
        self.market_simulator.reset()
        
        # Reset portfolio
        self.cash = self.initial_capital
        self.positions = []
        self.trades = []
        self.orders = []
        self.active_orders = []
        
        # Reset performance tracking
        self.equity_curve = []
        self.trade_history = []
        self.performance_metrics = {}
    
    def get_current_equity(self) -> float:
        """
        Get current equity value.
        
        Returns:
            Current equity value
        """
        # Start with cash
        equity = self.cash
        
        # Add value of open positions
        for position in self.positions:
            if not position.is_closed:
                # Get current price for symbol
                current_price = self.market_simulator.get_current_price(
                    position.symbol, position.side
                )
                
                # Add position value
                equity += position.get_current_value(current_price)
        
        return equity
    
    def place_order(
        self,
        symbol: str,
        order_type: OrderType,
        side: OrderSide,
        quantity: float,
        price: Optional[float] = None,
        stop_price: Optional[float] = None,
        limit_price: Optional[float] = None,
        trailing_pct: Optional[float] = None,
        time_in_force: str = "GTC",
        execution_strategy: Optional[ExecutionStrategy] = None,
        execution_params: Optional[Dict[str, Any]] = None,
        signal_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Order:
        """
        Place an order.
        
        Args:
            symbol: Symbol
            order_type: Order type
            side: Order side
            quantity: Order quantity
            price: Order price
            stop_price: Stop price
            limit_price: Limit price
            trailing_pct: Trailing percentage
            time_in_force: Time in force
            execution_strategy: Execution strategy
            execution_params: Execution parameters
            signal_id: Signal ID
            metadata: Order metadata
            
        Returns:
            Placed order
        """
        # Create order
        order = Order(
            symbol=symbol,
            order_type=order_type,
            side=side,
            quantity=quantity,
            price=price,
            stop_price=stop_price,
            limit_price=limit_price,
            trailing_pct=trailing_pct,
            time_in_force=time_in_force,
            execution_strategy=execution_strategy,
            execution_params=execution_params,
            metadata=metadata or {}
        )
        
        # Add signal ID to metadata
        if signal_id:
            order.metadata["signal_id"] = signal_id
        
        # Add to orders
        self.orders.append(order)
        self.active_orders.append(order)
        
        return order
    
    def cancel_order(self, order: Order) -> bool:
        """
        Cancel an order.
        
        Args:
            order: Order to cancel
            
        Returns:
            True if cancelled, False otherwise
        """
        # Cancel order
        cancelled = order.cancel()
        
        # Remove from active orders if cancelled
        if cancelled and order in self.active_orders:
            self.active_orders.remove(order)
        
        return cancelled
    
    def process_fills(self) -> None:
        """Process fills and update positions."""
        # Process filled orders
        for order in self.market_simulator.filled_orders:
            # Skip if already processed
            if order not in self.active_orders:
                continue
            
            # Remove from active orders
            self.active_orders.remove(order)
            
            # Update cash
            order_value = order.average_fill_price * order.quantity
            commission = order_value * self.config['commission_pct']
            
            if order.side == OrderSide.BUY:
                self.cash -= (order_value + commission)
            else:
                self.cash += (order_value - commission)
            
            # Check if order is for entry or exit
            if order.position_id is None:
                # Entry order - create new position
                position = Position(
                    symbol=order.symbol,
                    side=order.side,
                    entry_order=order,
                    metadata={"signal_id": order.metadata.get("signal_id")}
                )
                
                # Add to positions
                self.positions.append(position)
                
                # Check if part of an existing trade
                trade = None
                
                if order.metadata.get("signal_id"):
                    # Find trade with same signal ID
                    for t in self.trades:
                        if (t.signal_id == order.metadata.get("signal_id") and
                            t.symbol == order.symbol and
                            t.side == order.side and
                            t.is_open):
                            trade = t
                            break
                
                if trade:
                    # Add position to existing trade
                    trade.add_position(position)
                else:
                    # Create new trade
                    trade = Trade(
                        symbol=order.symbol,
                        side=order.side,
                        signal_id=order.metadata.get("signal_id"),
                        metadata=order.metadata
                    )
                    
                    # Add position to trade
                    trade.add_position(position)
                    
                    # Add to trades
                    self.trades.append(trade)
            else:
                # Exit order - find position
                for position in self.positions:
                    if position.position_id == order.position_id:
                        # Add exit order to position
                        position.add_exit_order(order)
                        
                        # Update position
                        position.update_from_fills()
                        
                        # Update trade
                        for trade in self.trades:
                            if trade.trade_id == position.trade_id:
                                trade._update_trade_metrics()
                                break
                        
                        break
        
        # Clear filled orders
        self.market_simulator.filled_orders = []
    
    def update_active_orders(self) -> None:
        """Update active orders."""
        # Process active orders
        for order in list(self.active_orders):
            # Execute order
            executed_order = self.market_simulator.execute_order(order)
            
            # Remove if filled or cancelled
            if not executed_order.is_active():
                if order in self.active_orders:
                    self.active_orders.remove(order)
    
    def update_equity_curve(self) -> None:
        """Update equity curve."""
        # Get current equity
        equity = self.get_current_equity()
        
        # Add to equity curve
        self.equity_curve.append({
            "timestamp": self.market_simulator.current_timestamp,
            "equity": equity
        })
    
    def update_trade_history(self) -> None:
        """Update trade history."""
        # Process closed trades
        for trade in self.trades:
            if not trade.is_open and trade not in self.trade_history:
                # Add to trade history
                self.trade_history.append(trade)
    
    def run_backtest(self, strategy_fn: Callable) -> Dict[str, Any]:
        """
        Run a backtest.
        
        Args:
            strategy_fn: Strategy function that takes (backtester, timestamp, bar)
                        and returns signals or orders
            
        Returns:
            Backtest results
        """
        # Reset backtester
        self.reset()
        
        # Run simulation
        while self.market_simulator.step():
            # Get current timestamp and bar
            timestamp = self.market_simulator.current_timestamp
            bar = next(iter(self.market_simulator.current_bars.values()))
            
            # Update active orders
            self.update_active_orders()
            
            # Process fills
            self.process_fills()
            
            # Run strategy
            strategy_fn(self, timestamp, bar)
            
            # Update active orders again (for strategy orders)
            self.update_active_orders()
            
            # Process fills again
            self.process_fills()
            
            # Update equity curve
            self.update_equity_curve()
            
            # Update trade history
            self.update_trade_history()
        
        # Calculate performance metrics
        self.calculate_performance_metrics()
        
        # Return results
        return self.get_backtest_results()
    
    def calculate_performance_metrics(self) -> None:
        """Calculate performance metrics."""
        # Check if we have equity data
        if not self.equity_curve:
            return
        
        # Extract equity values
        equity_values = [point["equity"] for point in self.equity_curve]
        timestamps = [point["timestamp"] for point in self.equity_curve]
        
        # Calculate basic metrics
        start_equity = equity_values[0]
        end_equity = equity_values[-1]
        max_equity = max(equity_values)
        
        # Calculate returns
        total_return = (end_equity / start_equity) - 1.0
        
        # Calculate drawdown
        drawdowns = []
        peak = equity_values[0]
        
        for equity in equity_values:
            if equity > peak:
                peak = equity
            
            drawdown = (peak - equity) / peak
            drawdowns.append(drawdown)
        
        max_drawdown = max(drawdowns) if drawdowns else 0.0
        
        # Calculate trade metrics
        num_trades = len(self.trade_history)
        winning_trades = [t for t in self.trade_history if t.profit_loss > 0]
        losing_trades = [t for t in self.trade_history if t.profit_loss <= 0]
        
        num_winning = len(winning_trades)
        num_losing = len(losing_trades)
        
        win_rate = num_winning / num_trades if num_trades > 0 else 0.0
        
        avg_profit = np.mean([t.profit_loss for t in winning_trades]) if winning_trades else 0.0
        avg_loss = np.mean([t.profit_loss for t in losing_trades]) if losing_trades else 0.0
        
        profit_factor = abs(sum([t.profit_loss for t in winning_trades]) / 
                           sum([t.profit_loss for t in losing_trades])) if losing_trades and sum([t.profit_loss for t in losing_trades]) != 0 else float('inf')
        
        # Calculate time-based metrics
        if timestamps and len(timestamps) > 1:
            start_time = timestamps[0]
            end_time = timestamps[-1]
            
            # Duration in days
            duration_days = (end_time - start_time).total_seconds() / (60 * 60 * 24)
            
            # Annualized return
            if duration_days > 0:
                annualized_return = ((1 + total_return) ** (365 / duration_days)) - 1
            else:
                annualized_return = 0.0
        else:
            duration_days = 0.0
            annualized_return = 0.0
        
        # Store metrics
        self.performance_metrics = {
            "start_equity": start_equity,
            "end_equity": end_equity,
            "max_equity": max_equity,
            "total_return": total_return,
            "annualized_return": annualized_return,
            "max_drawdown": max_drawdown,
            "num_trades": num_trades,
            "num_winning": num_winning,
            "num_losing": num_losing,
            "win_rate": win_rate,
            "avg_profit": avg_profit,
            "avg_loss": avg_loss,
            "profit_factor": profit_factor,
            "duration_days": duration_days
        }
    
    def get_backtest_results(self) -> Dict[str, Any]:
        """
        Get backtest results.
        
        Returns:
            Backtest results
        """
        # Convert objects to dictionaries
        equity_curve_dict = self.equity_curve
        trade_history_dict = [trade.to_dict() for trade in self.trade_history]
        
        # Prepare results
        results = {
            "config": self.config,
            "performance_metrics": self.performance_metrics,
            "equity_curve": equity_curve_dict,
            "trade_history": trade_history_dict
        }
        
        return results
