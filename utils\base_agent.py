#!/usr/bin/env python3
"""
Base Agent Class for Specialized Agent Army
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class BaseAgent(ABC):
    """Base class for all specialized agents"""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.creation_time = datetime.now()
        
    @abstractmethod
    def process(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process input data and return results"""
        pass
    
    def get_agent_info(self) -> Dict[str, str]:
        """Get basic agent information"""
        return {
            'agent_name': self.agent_name,
            'creation_time': self.creation_time.isoformat(),
            'class_name': self.__class__.__name__
        }
