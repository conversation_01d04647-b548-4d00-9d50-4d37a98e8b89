{"name": "liquidity_sweep_mcp", "version": "1.0.0", "description": "Production MCP server for Liquidity Sweep API system with mathematical precision", "main": "mcp_server_production.py", "dependencies": {"polygon": ">=3.0.0", "pandas": ">=2.0.0", "numpy": ">=1.24.0", "asyncio": ">=3.4.3", "aiohttp": ">=3.8.0"}, "config": {"polygon": {"api_key": "${POLYGON_API_KEY}", "tier": "starter", "cache_ttl": 300, "rate_limit": 300}, "mcp": {"timeout": 30.0, "max_payload_size": 1048576, "enable_metrics": true, "enable_health_checks": true}}, "tools": {"get_spot_price": {"description": "Get current spot price for any ticker with validation", "parameters": {"ticker": {"type": "string", "required": true}}}, "get_options_chain": {"description": "Get comprehensive options chain with Greeks and metadata", "parameters": {"ticker": {"type": "string", "required": true}, "expiry": {"type": "string", "required": false}}}, "get_pcr_ratio": {"description": "Calculate Put/Call ratio with mathematical precision", "parameters": {"ticker": {"type": "string", "required": true}, "expiry": {"type": "string", "required": false}}}, "get_market_depth": {"description": "Get market depth with bid/ask analysis", "parameters": {"ticker": {"type": "string", "required": true}, "levels": {"type": "integer", "default": 5, "max": 10}}}, "get_price_data": {"description": "Get historical price data with technical analysis", "parameters": {"ticker": {"type": "string", "required": true}, "timespan": {"type": "string", "default": "day"}, "multiplier": {"type": "integer", "default": 1}, "from_date": {"type": "string", "required": false}, "to_date": {"type": "string", "required": false}}}, "analyze_liquidity": {"description": "Comprehensive liquidity analysis with scoring algorithm", "parameters": {"ticker": {"type": "string", "required": true}}}, "health_check": {"description": "Comprehensive system health monitoring", "parameters": {}}, "get_metrics": {"description": "Performance metrics and statistics", "parameters": {}}, "run_diagnostics": {"description": "Full system diagnostics with API validation", "parameters": {}}, "get_training_config": {"description": "AI agent training configuration", "parameters": {}}, "benchmark_performance": {"description": "Performance benchmarking for AI training validation", "parameters": {"iterations": {"type": "integer", "default": 10, "max": 100}, "endpoints": {"type": "array", "default": ["get_spot_price", "health_check"]}}}}, "mathematical_guarantees": {"response_time_p95": "< 2000ms", "error_rate_max": "< 5%", "cache_hit_ratio_min": "> 85%", "api_utilization_target": "80% of subscription limit", "data_integrity": "SHA256 checksum validation", "rate_limiting": "Mathematical token bucket with adaptive backoff"}, "ai_agent_optimization": {"intelligent_caching": "Performance-based TTL with hit ratio optimization", "adaptive_rate_limiting": "Mathematical algorithms prevent API errors", "comprehensive_error_handling": "Circuit breaker with graceful degradation", "statistical_monitoring": "Real-time performance metrics and optimization", "training_configuration": "Complete patterns and strategies for AI agents"}}