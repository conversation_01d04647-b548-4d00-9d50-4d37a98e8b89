#!/usr/bin/env python3
"""
CORE Agent Base Framework

Foundation class that all specialized agents inherit from.
Provides standardized interface, communication, and training data capture.
"""

import json
import time
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging

class TaskPriority(Enum):
    """Task priority levels"""
    CRITICAL = 1
    HIGH = 2
    NORMAL = 3

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"  
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

@dataclass
class AgentTask:
    """Standardized task structure for agents"""
    task_id: str
    task_type: str
    agent_type: str
    priority: TaskPriority
    inputs: Dict[str, Any]
    workflow_file: str
    quality_standards: str
    performance_targets: Dict[str, Any]
    dependencies: List[str]
    training_data_tags: List[str]
    timestamp: datetime
    correlation_id: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary for serialization"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'agent_type': self.agent_type,
            'priority': self.priority.value,
            'inputs': self.inputs,
            'workflow_file': self.workflow_file,
            'quality_standards': self.quality_standards,
            'performance_targets': self.performance_targets,
            'dependencies': self.dependencies,
            'training_data_tags': self.training_data_tags,
            'timestamp': self.timestamp.isoformat(),
            'correlation_id': self.correlation_id
        }

@dataclass 
class AgentResult:
    """Standardized result structure from agents"""
    task_id: str
    agent_id: str
    status: TaskStatus
    outputs: Dict[str, Any]
    execution_time: float
    quality_metrics: Dict[str, float]
    error_details: Optional[str] = None
    training_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary"""
        return {
            'task_id': self.task_id,
            'agent_id': self.agent_id,
            'status': self.status.value,
            'outputs': self.outputs,
            'execution_time': self.execution_time,
            'quality_metrics': self.quality_metrics,
            'error_details': self.error_details,
            'training_data': self.training_data,
            'timestamp': datetime.now().isoformat()
        }

class BaseAgent(ABC):
    """
    Base class for all CORE system agents.
    
    Provides:
    - Standardized task interface
    - Training data capture
    - Quality validation
    - Error handling
    - Performance monitoring
    """
    
    def __init__(self, agent_id: str, config: Optional[Dict] = None):
        self.agent_id = agent_id
        self.config = config or {}
        self.logger = logging.getLogger(f"agent.{agent_id}")
        
        # Performance tracking
        self.tasks_completed = 0
        self.tasks_failed = 0
        self.total_execution_time = 0.0
        self.quality_scores = []
        
        # Training data collection
        self.training_data_buffer = []
        self.decision_points = []
        
        # Load agent-specific task definitions
        self.task_definitions = self._load_task_definitions()
        
        # Initialize agent
        self._initialize_agent()
        
    def _load_task_definitions(self) -> Dict[str, Any]:
        """Load task definitions from agent_docs/tasks/"""
        import os
        try:
            # Try relative path first, then absolute
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            task_file = os.path.join(base_dir, 'agent_docs', 'tasks', 'task_definitions.json')
            
            if not os.path.exists(task_file):
                # Fallback to relative path
                task_file = 'agent_docs/tasks/task_definitions.json'
                
            with open(task_file, 'r') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load task definitions: {e}")
            return {}
    
    def _initialize_agent(self):
        """Initialize agent-specific components"""
        self.logger.info(f"Initializing agent: {self.agent_id}")
        
        # Create agent workspace
        import os
        os.makedirs(f"agent_workspace/{self.agent_id}", exist_ok=True)
        os.makedirs(f"agent_workspace/{self.agent_id}/logs", exist_ok=True)
        os.makedirs(f"agent_workspace/{self.agent_id}/temp", exist_ok=True)
        
        # Load quality standards
        self._load_quality_standards()
        
        self.logger.info(f"Agent {self.agent_id} initialized successfully")
    
    def _load_quality_standards(self):
        """Load quality standards for this agent"""
        import os
        try:
            # Try relative path first, then absolute
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            standards_file = os.path.join(base_dir, 'agent_docs', 'standards', 'mathematical_precision_standards.md')
            
            if not os.path.exists(standards_file):
                # Fallback to relative path
                standards_file = 'agent_docs/standards/mathematical_precision_standards.md'
                
            with open(standards_file, 'r') as f:
                self.quality_standards = f.read()
        except Exception as e:
            self.logger.warning(f"Could not load quality standards: {e}")
            self.quality_standards = ""
    
    @abstractmethod
    def execute_task(self, task: AgentTask) -> AgentResult:
        """
        Execute assigned task following workflow specifications.
        
        Args:
            task: AgentTask with complete specifications
            
        Returns:
            AgentResult: Task execution results with quality metrics
        """
        pass
    
    @abstractmethod
    def validate_inputs(self, task: AgentTask) -> bool:
        """
        Validate task inputs meet agent requirements.
        
        Args:
            task: AgentTask to validate
            
        Returns:
            bool: True if inputs valid, False otherwise
        """
        pass
    
    @abstractmethod
    def validate_outputs(self, outputs: Dict[str, Any]) -> Dict[str, float]:
        """
        Validate outputs meet quality standards.
        
        Args:
            outputs: Task execution outputs
            
        Returns:
            Dict[str, float]: Quality metrics for outputs
        """
        pass
    
    def process_task(self, task: AgentTask) -> AgentResult:
        """
        Main task processing pipeline with full error handling and training data capture.
        
        Args:
            task: AgentTask to process
            
        Returns:
            AgentResult: Complete task execution results
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"Processing task {task.task_id} (type: {task.task_type})")
            
            # Step 1: Validate inputs
            if not self.validate_inputs(task):
                return self._create_failed_result(
                    task, "Input validation failed", start_time
                )
            
            # Step 2: Check dependencies
            if not self._check_dependencies(task):
                return self._create_failed_result(
                    task, "Dependencies not satisfied", start_time
                )
            
            # Step 3: Load workflow
            workflow_steps = self._load_workflow(task.workflow_file)
            if not workflow_steps:
                return self._create_failed_result(
                    task, f"Could not load workflow: {task.workflow_file}", start_time
                )
            
            # Step 4: Execute task with training data capture
            training_data = self._start_training_capture(task)
            
            try:
                result = self.execute_task(task)
                
                # Step 5: Validate outputs
                quality_metrics = self.validate_outputs(result.outputs)
                result.quality_metrics = quality_metrics
                
                # Step 6: Complete training data capture
                training_data.update(self._complete_training_capture(task, result))
                result.training_data = training_data
                
                # Step 7: Update performance metrics
                self._update_performance_metrics(result)
                
                self.logger.info(f"Task {task.task_id} completed successfully")
                return result
                
            except Exception as e:
                self.logger.error(f"Task execution failed: {e}")
                return self._create_failed_result(task, str(e), start_time)
                
        except Exception as e:
            self.logger.error(f"Task processing failed: {e}")
            return self._create_failed_result(task, f"Processing error: {e}", start_time)
    
    def _check_dependencies(self, task: AgentTask) -> bool:
        """Check if task dependencies are satisfied"""
        # For now, assume dependencies are satisfied
        # In full implementation, check agent_workspace for dependency completion
        return True
    
    def _load_workflow(self, workflow_file: str) -> Optional[Dict[str, Any]]:
        """Load workflow steps from agent_docs/workflows/"""
        try:
            workflow_path = f"agent_docs/workflows/{workflow_file}"
            with open(workflow_path, 'r') as f:
                # For now, return workflow loaded indicator
                # In full implementation, parse markdown workflow into steps
                return {"workflow_loaded": True, "file": workflow_file}
        except Exception as e:
            self.logger.error(f"Could not load workflow {workflow_file}: {e}")
            return None
    
    def _start_training_capture(self, task: AgentTask) -> Dict[str, Any]:
        """Start capturing training data for Agent Zero"""
        return {
            'task_start_time': datetime.now().isoformat(),
            'task_type': task.task_type,
            'agent_id': self.agent_id,
            'decision_points': [],
            'performance_data': {},
            'quality_checkpoints': []
        }
    
    def _complete_training_capture(self, task: AgentTask, result: AgentResult) -> Dict[str, Any]:
        """Complete training data capture"""
        return {
            'task_completion_time': datetime.now().isoformat(),
            'execution_success': result.status == TaskStatus.COMPLETED,
            'quality_achieved': result.quality_metrics,
            'performance_metrics': {
                'execution_time': result.execution_time,
                'quality_score': sum(result.quality_metrics.values()) / len(result.quality_metrics) if result.quality_metrics else 0.0
            },
            'lessons_learned': self._extract_lessons_learned(task, result)
        }
    
    def _extract_lessons_learned(self, task: AgentTask, result: AgentResult) -> Dict[str, Any]:
        """Extract lessons learned for Agent Zero training"""
        return {
            'efficiency_factors': self._identify_efficiency_factors(result),
            'quality_factors': self._identify_quality_factors(result),
            'optimization_opportunities': self._identify_optimizations(task, result),
            'error_patterns': self._identify_error_patterns(result) if result.error_details else None
        }
    
    def _identify_efficiency_factors(self, result: AgentResult) -> List[str]:
        """Identify what made execution efficient"""
        factors = []
        if result.execution_time < 5.0:  # Fast execution
            factors.append("fast_execution_achieved")
        if result.quality_metrics and all(q > 0.99 for q in result.quality_metrics.values()):
            factors.append("high_quality_maintained")
        return factors
    
    def _identify_quality_factors(self, result: AgentResult) -> List[str]:
        """Identify what contributed to quality"""
        factors = []
        if result.quality_metrics:
            if result.quality_metrics.get('mathematical_precision', 0) > 0.999:
                factors.append("mathematical_precision_excellent")
            if result.quality_metrics.get('output_accuracy', 0) > 0.99:
                factors.append("output_accuracy_high")
        return factors
    
    def _identify_optimizations(self, task: AgentTask, result: AgentResult) -> List[str]:
        """Identify optimization opportunities"""
        optimizations = []
        
        # Parse max execution time (handle string format like "30_seconds")
        max_time = task.performance_targets.get('max_execution_time', 10)
        if isinstance(max_time, str):
            if 'second' in max_time.lower():
                max_time = float(max_time.lower().replace('_seconds', '').replace('_second', ''))
            else:
                max_time = 10  # Default fallback
        
        if result.execution_time > max_time:
            optimizations.append("execution_time_optimization_needed")
        if result.quality_metrics and any(q < 0.95 for q in result.quality_metrics.values()):
            optimizations.append("quality_improvement_needed")
        return optimizations
    
    def _identify_error_patterns(self, result: AgentResult) -> Dict[str, Any]:
        """Identify error patterns for learning"""
        if not result.error_details:
            return {}
        
        return {
            'error_type': self._classify_error(result.error_details),
            'recovery_strategy': self._suggest_recovery_strategy(result.error_details),
            'prevention_method': self._suggest_prevention_method(result.error_details)
        }
    
    def _classify_error(self, error_details: str) -> str:
        """Classify error type for pattern recognition"""
        if "validation" in error_details.lower():
            return "validation_error"
        elif "timeout" in error_details.lower():
            return "timeout_error"
        elif "calculation" in error_details.lower():
            return "calculation_error"
        else:
            return "unknown_error"
    
    def _suggest_recovery_strategy(self, error_details: str) -> str:
        """Suggest recovery strategy based on error"""
        error_type = self._classify_error(error_details)
        strategies = {
            'validation_error': 'retry_with_enhanced_validation',
            'timeout_error': 'simplify_calculation_and_retry',
            'calculation_error': 'use_higher_precision_arithmetic',
            'unknown_error': 'log_and_escalate'
        }
        return strategies.get(error_type, 'log_and_escalate')
    
    def _suggest_prevention_method(self, error_details: str) -> str:
        """Suggest prevention method for future"""
        error_type = self._classify_error(error_details)
        methods = {
            'validation_error': 'enhanced_input_validation',
            'timeout_error': 'performance_optimization',
            'calculation_error': 'mathematical_precision_improvement',
            'unknown_error': 'comprehensive_error_handling'
        }
        return methods.get(error_type, 'comprehensive_error_handling')
    
    def _update_performance_metrics(self, result: AgentResult):
        """Update agent performance tracking"""
        if result.status == TaskStatus.COMPLETED:
            self.tasks_completed += 1
            self.total_execution_time += result.execution_time
            if result.quality_metrics:
                avg_quality = sum(result.quality_metrics.values()) / len(result.quality_metrics)
                self.quality_scores.append(avg_quality)
        else:
            self.tasks_failed += 1
    
    def _create_failed_result(self, task: AgentTask, error_message: str, start_time: float) -> AgentResult:
        """Create failed result with error details"""
        return AgentResult(
            task_id=task.task_id,
            agent_id=self.agent_id,
            status=TaskStatus.FAILED,
            outputs={},
            execution_time=time.time() - start_time,
            quality_metrics={},
            error_details=error_message,
            training_data=self._create_failure_training_data(task, error_message)
        )
    
    def _create_failure_training_data(self, task: AgentTask, error_message: str) -> Dict[str, Any]:
        """Create training data from failures for Agent Zero learning"""
        return {
            'failure_type': 'task_execution_failure',
            'task_type': task.task_type,
            'error_message': error_message,
            'recovery_suggestions': self._suggest_recovery_strategy(error_message),
            'prevention_methods': self._suggest_prevention_method(error_message),
            'timestamp': datetime.now().isoformat()
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get agent performance summary"""
        total_tasks = self.tasks_completed + self.tasks_failed
        avg_execution_time = self.total_execution_time / self.tasks_completed if self.tasks_completed > 0 else 0
        avg_quality = sum(self.quality_scores) / len(self.quality_scores) if self.quality_scores else 0
        
        return {
            'agent_id': self.agent_id,
            'total_tasks': total_tasks,
            'tasks_completed': self.tasks_completed,
            'tasks_failed': self.tasks_failed,
            'success_rate': self.tasks_completed / total_tasks if total_tasks > 0 else 0,
            'average_execution_time': avg_execution_time,
            'average_quality_score': avg_quality,
            'total_training_data_points': len(self.training_data_buffer)
        }
    
    def save_training_data(self):
        """Save collected training data for Agent Zero"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # Use absolute path to ensure consistent training log location
            from pathlib import Path
            core_root = Path(__file__).parent.parent
            training_dir = core_root / "training_logs" / "agent_training" / self.agent_id
            training_dir.mkdir(parents=True, exist_ok=True)
            
            filename = training_dir / f"{self.agent_id}_{timestamp}.json"
            
            training_data = {
                'agent_id': self.agent_id,
                'collection_period': timestamp,
                'performance_summary': self.get_performance_summary(),
                'training_examples': self.training_data_buffer,
                'decision_points': self.decision_points
            }
            
            with open(filename, 'w') as f:
                json.dump(training_data, f, indent=2, default=str)
                
            self.logger.info(f"Training data saved to {filename}")
            
        except Exception as e:
            self.logger.error(f"Failed to save training data: {e}")
    
    def record_decision_point(self, decision_type: str, context: Dict[str, Any], 
                            choice_made: str, rationale: str):
        """Record decision points for Agent Zero learning"""
        decision_point = {
            'timestamp': datetime.now().isoformat(),
            'decision_type': decision_type,
            'context': context,
            'choice_made': choice_made,
            'rationale': rationale,
            'agent_id': self.agent_id
        }
        
        self.decision_points.append(decision_point)
        self.logger.debug(f"Decision recorded: {decision_type} -> {choice_made}")

    def __del__(self):
        """Cleanup and save training data when agent is destroyed"""
        if hasattr(self, 'training_data_buffer') and self.training_data_buffer:
            self.save_training_data()
