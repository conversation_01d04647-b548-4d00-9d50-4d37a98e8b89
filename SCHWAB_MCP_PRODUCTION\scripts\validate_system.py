#!/usr/bin/env python3
"""
Schwab MCP Production Validation
Comprehensive system validation for production deployment
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
import asyncio
from typing import Dict

# Add core directory to path
script_dir = Path(__file__).parent.parent
sys.path.append(str(script_dir / "core"))

from schwab_production_api import SchwabProductionClient
from schwab_mcp_server import SchwabMCPServer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionValidator:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Production validation framework"""
    
    def __init__(self):
        self.client = SchwabProductionClient()
        self.results = {}
        
    def validate_configuration(self) -> bool:
        """Validate configuration files"""
        logger.info("Validating configuration files...")
        
        config_path = script_dir / "config" / "config.json"
        token_path = script_dir / "config" / "schwab_token.json"
        
        try:
            # Check config file
            if not config_path.exists():
                logger.error("Configuration file missing")
                return False
                
            with open(config_path, 'r') as f:
                config = json.load(f)
                
            required_keys = ['app_key', 'app_secret', 'callback_url']
            for key in required_keys:
                if key not in config:
                    logger.error(f"Missing configuration key: {key}")
                    return False
            
            # Check token file
            if not token_path.exists():
                logger.error("Token file missing")
                return False
                
            with open(token_path, 'r') as f:
                token_data = json.load(f)
                
            required_token_keys = ['access_token', 'refresh_token', 'expires_at']
            for key in required_token_keys:
                if key not in token_data:
                    logger.error(f"Missing token key: {key}")
                    return False
            
            logger.info("Configuration validation: PASSED")
            return True
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def validate_api_connection(self) -> bool:
        """Validate API connection and authentication"""
        logger.info("Validating API connection...")
        
        try:
            health = self.client.health_check()
            
            if health["status"] == "healthy":
                logger.info(f"API connection: HEALTHY ({health.get('accounts', 0)} accounts)")
                return True
            else:
                logger.error(f"API connection failed: {health.get('error', 'Unknown error')}")
                return False
                
        except Exception as e:
            logger.error(f"API connection validation failed: {e}")
            return False
    
    def validate_real_time_data(self) -> bool:
        """Validate real-time data access"""
        logger.info("Validating real-time data access...")
        
        try:
            # Test with SPY (highly liquid ETF)
            quote = self.client.get_real_time_quote("SPY")
            
            if quote["price"] > 0:
                logger.info(f"Real-time data: ACTIVE (SPY: ${quote['price']:.2f})")
                return True
            else:
                logger.error("Real-time data validation failed - no price data")
                return False
                
        except Exception as e:
            logger.error(f"Real-time data validation failed: {e}")
            return False
    
    async def validate_mcp_server(self) -> bool:
        """Validate MCP server functionality"""
        logger.info("Validating MCP server...")
        
        try:
            server = SchwabMCPServer()
            
            # Test health check
            health_result = await server.health_check()
            if not health_result["success"]:
                logger.error("MCP server health check failed")
                return False
            
            # Test spot price endpoint
            price_result = await server.get_spot_price("AAPL")
            if not price_result["success"]:
                logger.error("MCP server spot price test failed")
                return False
            
            logger.info("MCP server validation: PASSED")
            return True
            
        except Exception as e:
            logger.error(f"MCP server validation failed: {e}")
            return False
    
    async def run_full_validation(self) -> Dict:
        """Run complete production validation suite"""
        logger.info("="*60)
        logger.info("SCHWAB MCP PRODUCTION VALIDATION")
        logger.info("="*60)
        
        validation_results = {
            "configuration": self.validate_configuration(),
            "api_connection": self.validate_api_connection(),
            "real_time_data": self.validate_real_time_data(),
            "mcp_server": await self.validate_mcp_server()
        }
        
        # Calculate overall status
        all_passed = all(validation_results.values())
        passed_count = sum(validation_results.values())
        total_count = len(validation_results)
        
        logger.info("="*60)
        logger.info("VALIDATION RESULTS")
        logger.info("="*60)
        
        for test_name, result in validation_results.items():
            status = "PASSED" if result else "FAILED"
            logger.info(f"{test_name.replace('_', ' ').title()}: {status}")
        
        logger.info("="*60)
        logger.info(f"Overall Status: {passed_count}/{total_count} tests passed")
        
        if all_passed:
            logger.info("PRODUCTION READY: All validations passed")
            logger.info("System ready for AI agent integration")
        else:
            logger.warning("PRODUCTION NOT READY: Some validations failed")
            logger.info("Address failed tests before deployment")
        
        logger.info("="*60)
        
        return {
            "overall_status": "READY" if all_passed else "NOT_READY",
            "tests_passed": passed_count,
            "tests_total": total_count,
            "validation_results": validation_results,
            "timestamp": time.time()
        }

async def main():
    """Main validation entry point"""
    validator = ProductionValidator()
    results = await validator.run_full_validation()
    
    # Save validation results
    results_path = script_dir / "logs" / f"validation_results_{int(time.time())}.json"
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Validation results saved to: {results_path}")
    
    return results["overall_status"] == "READY"

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
