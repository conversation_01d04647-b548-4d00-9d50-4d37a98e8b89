#!/usr/bin/env python3
"""
Monitoring Orchestrator - The Nervous System Core
Coordinates all monitoring components for complete system awareness
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import json

from .system_health_monitor import SystemHealthMonitor
from .trading_performance_analytics import TradingPerformanceAnalytics
from .alert_notification_system import AlertNotificationSystem, AlertSeverity, AlertCategory, NotificationChannel
from .operational_dashboards import OperationalDashboards

class NervousSystemOrchestrator:
    """
    Comprehensive monitoring orchestrator
    Integrates system health, performance analytics, alerts, and dashboards
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Initialize monitoring components
        self.system_health = SystemHealthMonitor(self.config.get('system_health', {}))
        self.performance_analytics = TradingPerformanceAnalytics(self.config.get('performance', {}))
        self.alert_system = AlertNotificationSystem(self.config.get('alerts', {}))
        self.dashboards = OperationalDashboards(self.config.get('dashboards', {}))
        
        # Orchestration settings
        self.monitoring_active = False
        self.orchestrator_thread = None
        self.update_interval = self.config.get('update_interval', 10)  # seconds
        
        # Component integration
        self._setup_component_integration()
        
        # Performance tracking
        self.orchestrator_stats = {
            'start_time': None,
            'total_updates': 0,
            'last_update': None,
            'avg_update_time': 0.0
        }
        
        self.logger = logging.getLogger(__name__)
    
    def _setup_component_integration(self):
        """Setup integration between monitoring components"""
        
        # Register data sources for dashboards
        self.dashboards.register_data_source('system_health', self.system_health)
        self.dashboards.register_data_source('trading_performance', self.performance_analytics)
        self.dashboards.register_data_source('risk_metrics', self.performance_analytics)
        self.dashboards.register_data_source('execution_quality', self.performance_analytics)
        self.dashboards.register_data_source('alert_system', self.alert_system)
        
        # Setup default notification channels
        self._setup_default_notifications()
    
    def _setup_default_notifications(self):
        """Setup default notification channels"""
        
        # Log notification channel (always available)
        log_channel = NotificationChannel(
            channel_type='log',
            config={},
            enabled=True,
            severity_filter=[AlertSeverity.WARNING, AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]
        )
        self.alert_system.add_notification_channel('log', log_channel)
        
        # Email notifications if configured
        email_config = self.config.get('email_notifications')
        if email_config:
            email_channel = NotificationChannel(
                channel_type='email',
                config=email_config,
                enabled=True,
                severity_filter=[AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]
            )
            self.alert_system.add_notification_channel('email', email_channel)
        
        # Webhook notifications if configured
        webhook_config = self.config.get('webhook_notifications')
        if webhook_config:
            webhook_channel = NotificationChannel(
                channel_type='webhook',
                config=webhook_config,
                enabled=True,
                severity_filter=[AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]
            )
            self.alert_system.add_notification_channel('webhook', webhook_channel)
    
    def start_monitoring(self):
        """Start the complete monitoring system"""
        if self.monitoring_active:
            self.logger.warning("Monitoring already active")
            return
        
        self.monitoring_active = True
        self.orchestrator_stats['start_time'] = datetime.now()
        
        # Start individual components
        self.system_health.start_monitoring()
        
        # Start orchestrator thread
        self.orchestrator_thread = threading.Thread(target=self._orchestration_loop, daemon=True)
        self.orchestrator_thread.start()
        
        self.logger.info("Nervous System monitoring started")
    
    def stop_monitoring(self):
        """Stop the complete monitoring system"""
        self.monitoring_active = False
        
        # Stop individual components
        self.system_health.stop_monitoring()
        
        # Wait for orchestrator thread
        if self.orchestrator_thread:
            self.orchestrator_thread.join(timeout=10)
        
        self.logger.info("Nervous System monitoring stopped")
    
    def _orchestration_loop(self):
        """Main orchestration loop"""
        while self.monitoring_active:
            try:
                update_start = time.time()
                
                # Get system health data
                health_summary = self.system_health.get_system_health_summary()
                
                # Get performance data
                performance_summary = self.performance_analytics.get_performance_summary()
                
                # Check alert conditions
                self._check_all_alert_conditions(health_summary, performance_summary)
                
                # Update performance tracking
                self.orchestrator_stats['total_updates'] += 1
                self.orchestrator_stats['last_update'] = datetime.now()
                
                update_time = time.time() - update_start
                self.orchestrator_stats['avg_update_time'] = (
                    (self.orchestrator_stats['avg_update_time'] * (self.orchestrator_stats['total_updates'] - 1) + update_time) /
                    self.orchestrator_stats['total_updates']
                )
                
            except Exception as e:
                self.logger.error(f"Error in orchestration loop: {e}")
            
            time.sleep(self.update_interval)
    
    def _check_all_alert_conditions(self, health_summary: Dict, performance_summary: Dict):
        """Check all alert conditions across components"""
        
        # System health alerts
        if 'system_metrics' in health_summary:
            self.alert_system.check_system_health_alerts(health_summary['system_metrics'])
        
        # Risk alerts
        if 'risk_metrics' in performance_summary:
            self.alert_system.check_risk_alerts(performance_summary['risk_metrics'])
        
        # Execution quality alerts
        if 'execution_quality' in performance_summary:
            self.alert_system.check_execution_alerts(performance_summary['execution_quality'])
        
        # Performance alerts
        self.alert_system.check_performance_alerts(performance_summary)
        
        # Agent health alerts
        if 'agent_health' in health_summary:
            for agent_id, agent_data in health_summary['agent_health'].items():
                if agent_data['status'] in ['critical', 'offline']:
                    self.alert_system.create_alert(
                        AlertSeverity.CRITICAL if agent_data['status'] == 'critical' else AlertSeverity.WARNING,
                        AlertCategory.SYSTEM_FAILURE,
                        f"Agent {agent_data['status'].title()}",
                        f"Agent {agent_id} is {agent_data['status']}",
                        "agent_monitor",
                        {"agent_id": agent_id, "agent_status": agent_data}
                    )
        
        # API connectivity alerts
        if 'api_health' in health_summary:
            for api_name, api_data in health_summary['api_health'].items():
                if api_data['status'] in ['failed', 'timeout']:
                    self.alert_system.create_alert(
                        AlertSeverity.CRITICAL,
                        AlertCategory.API_CONNECTIVITY,
                        f"API Connection {api_data['status'].title()}",
                        f"API {api_name} connection {api_data['status']}",
                        "api_monitor",
                        {"api_name": api_name, "api_status": api_data}
                    )
    
    def register_agent(self, agent_id: str):
        """Register an agent for monitoring"""
        self.system_health.register_agent(agent_id)
        self.logger.info(f"Registered agent for nervous system monitoring: {agent_id}")
    
    def register_api(self, api_name: str, endpoint: str):
        """Register an API for monitoring"""
        self.system_health.register_api(api_name, endpoint)
        self.logger.info(f"Registered API for nervous system monitoring: {api_name}")
    
    def update_agent_heartbeat(self, agent_id: str, response_time: float = 0.0, success: bool = True):
        """Update agent heartbeat"""
        self.system_health.update_agent_heartbeat(agent_id, response_time, success)
    
    def update_api_status(self, api_name: str, response_time: float, success: bool, 
                         rate_limit_remaining: int = None):
        """Update API status"""
        self.system_health.update_api_status(api_name, response_time, success, rate_limit_remaining)
    
    def record_trade(self, trade_data: Dict[str, Any]):
        """Record a trade for performance tracking"""
        self.performance_analytics.record_trade(trade_data)
    
    def update_position(self, ticker: str, position_data: Dict[str, Any]):
        """Update position for real-time tracking"""
        self.performance_analytics.update_position(ticker, position_data)
    
    def calculate_portfolio_pnl(self, current_prices: Dict[str, float]) -> Dict[str, float]:
        """Calculate real-time portfolio P&L"""
        return self.performance_analytics.calculate_portfolio_pnl(current_prices)
    
    def get_dashboard_data(self, dashboard_id: str) -> Dict[str, Any]:
        """Get dashboard data"""
        return self.dashboards.get_dashboard_data(dashboard_id)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        
        # Get data from all components
        health_summary = self.system_health.get_system_health_summary()
        performance_summary = self.performance_analytics.get_performance_summary()
        alert_summary = self.alert_system.get_alert_summary()
        
        # Calculate overall system status
        overall_status = self._calculate_overall_system_status(health_summary, alert_summary)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'overall_status': overall_status,
            'nervous_system_stats': self.orchestrator_stats.copy(),
            'system_health': health_summary,
            'trading_performance': performance_summary,
            'alerts': alert_summary,
            'available_dashboards': self.dashboards.get_dashboard_list()
        }
    
    def _calculate_overall_system_status(self, health_summary: Dict, alert_summary: Dict) -> str:
        """Calculate overall system status"""
        
        # Check for critical alerts
        active_critical = len([a for a in alert_summary.get('unacknowledged_alerts', [])
                              if a.get('severity') in ['CRITICAL', 'EMERGENCY']])
        
        if active_critical > 0:
            return 'critical'
        
        # Check system health
        system_status = health_summary.get('overall_status', 'unknown')
        if system_status == 'critical':
            return 'critical'
        elif system_status in ['degraded', 'warning']:
            return 'warning'
        
        # Check for warning alerts
        active_warnings = len([a for a in alert_summary.get('unacknowledged_alerts', [])
                              if a.get('severity') == 'WARNING'])
        
        if active_warnings > 5:  # Too many warnings
            return 'warning'
        
        return 'healthy'
    
    def create_manual_alert(self, severity: str, category: str, title: str, 
                           message: str, metadata: Optional[Dict] = None):
        """Create a manual alert"""
        severity_enum = AlertSeverity(severity.upper())
        category_enum = AlertCategory(category.upper())
        
        return self.alert_system.create_alert(
            severity_enum, category_enum, title, message,
            "manual", metadata
        )
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str = "operator"):
        """Acknowledge an alert"""
        self.alert_system.acknowledge_alert(alert_id, acknowledged_by)
    
    def resolve_alert(self, alert_id: str, resolved_by: str = "operator"):
        """Resolve an alert"""
        self.alert_system.resolve_alert(alert_id, resolved_by)
    
    def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check"""
        health_check = {
            'timestamp': datetime.now().isoformat(),
            'nervous_system_active': self.monitoring_active,
            'components': {}
        }
        
        # Check system health monitor
        try:
            health_summary = self.system_health.get_system_health_summary()
            health_check['components']['system_health'] = {
                'status': 'healthy',
                'agent_count': len(health_summary.get('agent_health', {})),
                'api_count': len(health_summary.get('api_health', {}))
            }
        except Exception as e:
            health_check['components']['system_health'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Check performance analytics
        try:
            perf_summary = self.performance_analytics.get_performance_summary()
            health_check['components']['performance_analytics'] = {
                'status': 'healthy',
                'total_trades': perf_summary.get('total_trades', 0),
                'strategy_count': len(perf_summary.get('strategies', {}))
            }
        except Exception as e:
            health_check['components']['performance_analytics'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Check alert system
        try:
            alert_summary = self.alert_system.get_alert_summary()
            health_check['components']['alert_system'] = {
                'status': 'healthy',
                'active_alerts': alert_summary.get('active_alerts', 0),
                'total_alerts_today': alert_summary.get('total_alerts_today', 0)
            }
        except Exception as e:
            health_check['components']['alert_system'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Check dashboards
        try:
            dashboard_list = self.dashboards.get_dashboard_list()
            health_check['components']['dashboards'] = {
                'status': 'healthy',
                'dashboard_count': len(dashboard_list)
            }
        except Exception as e:
            health_check['components']['dashboards'] = {
                'status': 'error',
                'error': str(e)
            }
        
        return health_check
    
    def log_monitoring_decision(self, decision_type: str, decision_data: Dict[str, Any]):
        """Log monitoring decisions for shadow mode learning"""
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            # Convert monitoring data to signal format
            signal_data = {
                'confidence': decision_data.get('confidence', 0.85),
                'strength': decision_data.get('alert_severity', 0.5),
                'execution_recommendation': decision_type.lower()
            }
            
            math_data = {
                'accuracy_score': 0.92,  # Monitoring system accuracy
                'precision': 0.001
            }
            
            market_context = {
                'system': 'nervous_system_orchestrator',
                'decision_type': decision_type,
                'monitoring_data': decision_data,
                'timestamp': datetime.now().isoformat()
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': decision_type, 'data': decision_data},
                outcome=0.0,
                market_context=market_context
            )
            self.logger.info(f"Shadow mode: Monitoring decision logged - {decision_type}")
            
        except Exception as e:
            self.logger.warning(f"Shadow mode logging failed: {e}")

if __name__ == "__main__":
    # Test the nervous system orchestrator
    config = {
        'system_health': {
            'monitoring_interval': 5
        },
        'alerts': {
            'alert_thresholds': {
                'cpu_critical': 80.0,
                'memory_critical': 85.0
            }
        }
    }
    
    nervous_system = NervousSystemOrchestrator(config)
    
    # Register test components
    nervous_system.register_agent("test_agent")
    nervous_system.register_api("test_api", "https://api.example.com")
    
    # Start monitoring
    nervous_system.start_monitoring()
    
    # Run for a short time
    time.sleep(15)
    
    # Get system status
    status = nervous_system.get_system_status()
    print(json.dumps(status, indent=2, default=str))
    
    # Run health check
    health_check = nervous_system.run_health_check()
    print("\nHealth Check:")
    print(json.dumps(health_check, indent=2, default=str))
    
    # Stop monitoring
    nervous_system.stop_monitoring()
