"""
MCP Client Compatibility Layer
Provides direct polygon client interface compatibility while routing through MCP
"""

from __future__ import annotations
import logging
from typing import Any, Dict, List, Optional, Union, Iterator
from datetime import datetime
from api_robustness.unified_api_gateway import get_api_gateway

logger = logging.getLogger(__name__)

class MCPPolygonClient:
    """
    MCP-backed polygon client that provides the same interface as polygon.RESTClient
    but routes all requests through the unified MCP gateway.
    """
    
    def __init__(self, api_key: str = None):
        """Initialize MCP client with optional API key."""
        self.api_key = api_key
        self._gateway = get_api_gateway()
        logger.info("MCP Polygon Client initialized")
    
    # ============================================================================
    # Market Status
    # ============================================================================
    def get_market_status(self):
        """Get market status."""
        return self._gateway.get_market_status()
    
    # ============================================================================
    # Price Data / Aggregates
    # ============================================================================
    def list_aggs(self, ticker: str, multiplier: int = 1, timespan: str = "day",
                  from_: str = None, to: str = None, **kwargs) -> Iterator[Any]:
        """List aggregates (OHLCV) data."""
        result = self._gateway.list_aggs(
            ticker=ticker, 
            multiplier=multiplier, 
            timespan=timespan,
            from_=from_, 
            to=to, 
            **kwargs
        )
        return iter(result)
    
    def get_aggs(self, ticker: str, multiplier: int = 1, timespan: str = "day",
                 from_: str = None, to: str = None, **kwargs) -> List[Any]:
        """Get aggregates as list."""
        return list(self.list_aggs(ticker, multiplier, timespan, from_, to, **kwargs))
    
    # ============================================================================
    # Quotes and Trades
    # ============================================================================
    def get_last_trade(self, ticker: str):
        """Get last trade for ticker."""
        return self._gateway.get_last_trade(ticker)
    
    def get_last_quote(self, ticker: str):
        """Get last quote for ticker."""
        return self._gateway.get_last_quote(ticker)
    
    def list_trades(self, ticker: str, **kwargs) -> Iterator[Any]:
        """List trades for ticker."""
        result = self._gateway.list_trades(ticker, **kwargs)
        return iter(result)
    
    # ============================================================================
    # Options
    # ============================================================================
    def list_snapshot_options_chain(self, underlying_asset: str, **kwargs) -> Iterator[Any]:
        """List options chain snapshot."""
        result = self._gateway.list_snapshot_options_chain(underlying_asset, **kwargs)
        return iter(result)
    
    def get_snapshot_option_contract(self, option_symbol: str):
        """Get option contract snapshot."""
        return self._gateway.get_snapshot_option_contract(option_symbol)
    
    # ============================================================================
    # Snapshots
    # ============================================================================
    def get_snapshot_ticker(self, ticker: str):
        """Get ticker snapshot."""
        return self._gateway.get_snapshot_ticker(ticker)


class MCPSnapshotClient(MCPPolygonClient):
    """
    MCP-backed snapshot client that provides SnapshotClient interface.
    """
    
    def __init__(self, api_key: str = None):
        super().__init__(api_key)
        logger.info("MCP Snapshot Client initialized")


def create_polygon_client(api_key: str = None) -> MCPPolygonClient:
    """Factory function to create MCP-backed polygon client."""
    return MCPPolygonClient(api_key)


def create_snapshot_client(api_key: str = None) -> MCPSnapshotClient:
    """Factory function to create MCP-backed snapshot client."""
    return MCPSnapshotClient(api_key)


# Legacy support classes for backward compatibility
class RESTClient(MCPPolygonClient):
    """Legacy RESTClient compatibility class."""
    pass


class SnapshotClient(MCPSnapshotClient):
    """Legacy SnapshotClient compatibility class."""
    pass
