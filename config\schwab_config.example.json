{"_comment": "Example Schwab configuration - copy to schwab_config.json and set environment variables", "app_key": "ENV:SCHWAB_APP_KEY", "app_secret": "ENV:SCHWAB_APP_SECRET", "callback_url": "https://127.0.0.1:8182", "token_path": "D:/python projects/schwab_token.json", "server_config": {"host": "localhost", "port": 8005, "rate_limit_requests_per_second": 10, "max_concurrent_requests": 5, "request_timeout_seconds": 30}, "api_config": {"base_url": "https://api.schwabapi.com", "default_timeframe": "1", "max_bars_per_request": 1000, "max_options_per_request": 5000}, "logging": {"level": "INFO", "file": "logs/schwab_mcp.log", "max_size_mb": 100, "backup_count": 5}, "setup_instructions": {"1": "Set environment variable: SCHWAB_APP_KEY=your_app_key_here", "2": "Set environment variable: SCHWAB_APP_SECRET=your_app_secret_here", "3": "Ensure schwab_token.json exists at specified token_path", "4": "Both app_key and app_secret are obtained from Schwab Developer Portal"}}