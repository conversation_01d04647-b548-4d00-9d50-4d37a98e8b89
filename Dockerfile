FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy entire CORE system
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV AGENT_ZERO_MODE=shadow

# Create necessary directories
RUN mkdir -p /app/training_logs/AgentZero
RUN mkdir -p /app/logs

# Expose port for health checks
EXPOSE 8000

# Run Agent Zero with CORE system
CMD ["python3", "main.py"]