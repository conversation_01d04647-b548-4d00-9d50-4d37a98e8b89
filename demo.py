#!/usr/bin/env python3
"""
Demo Mode for Visual Trading System
Shows visual capabilities with synthetic data while MCP debugging occurs
"""

import sys
import json
from datetime import datetime
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def create_demo_analysis_data(ticker: str) -> dict:
    """Create realistic demo analysis data for visual testing"""
    return {
        'ticker': ticker,
        'timestamp': datetime.now().isoformat(),
        'execution_time_seconds': 2.3,
        'system_status': 'SUCCESS',
        'analysis': {
            'b_series': {
                'status': 'SUCCESS',
                'feature_count': 47,
                'greek_features': ['gamma_roc', 'vanna_roc', 'charm_roc']
            },
            'a01_anomalies': {
                'status': 'SUCCESS',
                'anomalies_detected': 3,
                'anomaly_scores': [0.85, 0.72, 0.91]
            },
            'c02_iv_dynamics': {
                'status': 'SUCCESS', 
                'iv_regime': 'elevated',
                'iv_percentile': 73.2
            },
            'f02_flow_physics': {
                'status': 'SUCCESS',
                'flow_score': 0.78,
                'flow_direction': 'bullish',
                'momentum': 0.83
            },
            'specialized_army': {
                'enhanced_accumulation_distribution': {
                    'signal': {
                        'direction': 'bullish',
                        'strength': 0.85,
                        'confidence': 0.92
                    },
                    'institutional_positioning': 'accumulation',
                    'money_flow_multiplier': 1.34
                },
                'breakout_validation': {
                    'breakout_validity': 78.5,
                    'volume_confirmation': True,
                    'momentum_sustainability': 'strong'
                },
                'options_flow': {
                    'directional_bias': 'BULLISH',
                    'gamma_positioning': 'positive',
                    'institutional_hedging': 'minimal'
                }
            }
        },
        'agent_zero_decision': {
            'final_decision': 'BULLISH',
            'confidence': 87.3,
            'strength': 'STRONG',
            'execution_recommendation': 'BUY',
            'position_sizing': 'Standard',
            'risk_level': 'Moderate',
            'reasoning': [
                'Strong institutional accumulation detected (92% confidence)',
                'Breakout validation confirmed with volume support',
                'Bullish options flow with positive gamma positioning',
                'Flow physics showing positive momentum (0.83)',
                'Mathematical ensemble score: 87.3%'
            ],
            'ensemble_score': 87.3,
            'component_signals': {
                'accumulation_distribution': 92,
                'breakout_validation': 78,
                'options_flow': 85,
                'flow_physics': 83,
                'anomaly_detection': 71,
                'iv_dynamics': 73
            }
        },
        'performance_metrics': {
            'total_agents': 27,
            'pipeline_steps': 6,
            'data_quality': '>95%',
            'mathematical_precision': 'IEEE 754'
        }
    }

def main():
    """Run demo mode with visual charts"""
    if len(sys.argv) < 2:
        print("Demo Mode Usage:")
        print("  py demo.py SPY          # Demo analysis")
        print("  py demo.py SPY --charts # Demo with visual charts")
        return 1
    
    ticker = sys.argv[1].upper()
    generate_charts = '--charts' in sys.argv
    
    print(f"DEMO MODE: CORE TRADING ANALYSIS: {ticker}")
    print("=" * 60)
    print("NOTE: Using synthetic data for visual demonstration")
    print("MCP connection will be resolved separately")
    print("=" * 60)
    
    # Create demo analysis data
    analysis_data = create_demo_analysis_data(ticker)
    
    print("Demo intelligence pipeline complete...")
    
    # Generate visual charts if requested
    if generate_charts:
        print("\nGenerating visual validation charts...")
        try:
            from trading_visualizer import TradingVisualizer
            visualizer = TradingVisualizer()
            chart_files = visualizer.generate_trade_validation_charts(ticker, analysis_data)
            
            if chart_files:
                analysis_data['visual_charts'] = chart_files
                print(f"  Generated {len(chart_files)} validation charts")
                
                # Create HTML summary report
                report_file = visualizer.create_summary_report(ticker, analysis_data, chart_files)
                if report_file:
                    analysis_data['visual_report'] = report_file
                    print(f"  Visual report: {report_file}")
                    
                    # Auto-open in browser for demonstration
                    import webbrowser
                    try:
                        webbrowser.open(f"file://{Path(report_file).absolute()}")
                        print(f"  Opening visual report in browser...")
                    except Exception as e:
                        print(f"  Manual open: file://{Path(report_file).absolute()}")
            else:
                print("  No charts generated")
                
        except Exception as e:
            print(f"  Chart generation failed: {e}")
            analysis_data['visual_error'] = str(e)
    
    # Print executive summary
    print(f"\nDEMO EXECUTIVE SUMMARY")
    print("-" * 25)
    print(f"Ticker: {analysis_data['ticker']}")
    print(f"Status: {analysis_data['system_status']}")
    print(f"Execution Time: {analysis_data['execution_time_seconds']}s")
    
    agent_zero = analysis_data.get('agent_zero_decision', {})
    if agent_zero:
        print(f"Agent Zero Decision: {agent_zero.get('final_decision', 'N/A')}")
        print(f"Confidence: {agent_zero.get('confidence', 0):.1f}%")
        print(f"Strength: {agent_zero.get('strength', 'N/A')}")
        print(f"Recommendation: {agent_zero.get('execution_recommendation', 'N/A')}")
        
        # Shadow mode logging - capture demo decisions for learning
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            signal_data = {
                'confidence': agent_zero.get('confidence', 87.3) / 100,
                'strength': 0.85,  # Demo strong signal
                'execution_recommendation': agent_zero.get('execution_recommendation', 'BUY').lower()
            }
            
            math_data = {
                'accuracy_score': 0.92,  # Demo high accuracy
                'precision': 0.001
            }
            
            market_context = {
                'system': 'demo_mode',
                'ticker': ticker,
                'demo_data': True,
                'ensemble_score': agent_zero.get('ensemble_score', 87.3)
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision=agent_zero,
                outcome=0.0,  # Demo outcome
                market_context=market_context
            )
            print("Shadow mode: Demo decision logged for learning")
            
        except Exception as shadow_error:
            print(f"Shadow mode logging failed: {shadow_error}")
    
    # Print visual info if charts were generated
    if analysis_data.get('visual_charts'):
        print(f"Visual Charts: {len(analysis_data['visual_charts'])} generated")
        print(f"Charts saved in: visual_output/")
    if analysis_data.get('visual_report'):
        print(f"Visual Report: {Path(analysis_data['visual_report']).name}")
    
    # Save demo results
    output_file = f"DEMO_{ticker}_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(analysis_data, f, indent=2, default=str)
    
    print(f"\nDemo results saved to: {output_file}")
    
    print(f"\n" + "=" * 60)
    print("DEMO COMPLETE - Visual capabilities demonstrated")
    print("Next: Debug MCP connection for live data integration")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
