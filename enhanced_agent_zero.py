#!/usr/bin/env python3
"""
Enhanced Agent Zero - Liquidity-Aware Options Trading Advisor
Implements balanced 30/20/20 weight structure with directional actions
"""

import json
import os
import sys
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class EnhancedAgentZeroAdvisor:
    """
    Enhanced Agent Zero - Balanced Liquidity-Aware Trading Advisor
    
    Implements:
    - 30% liquidity weighting (leading but not dominant)
    - 20% signal confidence + 20% signal strength (institutional flow)
    - Directional actions: buy_calls, buy_puts, hold, avoid
    - Liquidity gates for risk management
    """
    
    def __init__(self):
        # Training directory
        core_root = Path(__file__).parent.parent
        self.training_dir = core_root / "training_logs" / "EnhancedAgentZero"
        self.training_dir.mkdir(parents=True, exist_ok=True)
        
        # Balanced weight structure - empirically informed
        self.weights = {
            'liquidity_score': 0.30,           # Leading factor - money flow enabler
            'signal_confidence': 0.20,         # Market direction certainty
            'signal_strength': 0.20,           # Institutional flow conviction
            'execution_recommendation': 0.15,  # Timing component
            'math_accuracy': 0.10,             # Enhanced mathematical validation
            'math_precision': 0.05             # Precision factor
        }
        
        # Decision thresholds
        self.thresholds = {
            'execute': 0.75,    # High confidence threshold
            'hold': 0.25,       # Neutral threshold
            'avoid': 0.25       # Below this = avoid
        }
        
        # Liquidity gates
        self.liquidity_gates = {
            'critical_low': 0.20,   # Force avoid below this
            'low': 0.40,            # Elevated thresholds
            'good': 0.60            # Normal execution
        }
        
        logger.info(f"Enhanced Agent Zero initialized - Balanced weights: {self.weights}")
    
    def calculate_liquidity_score(self, market_context: Dict[str, Any]) -> float:
        """
        Calculate comprehensive liquidity score from market context
        
        Components:
        - Volume liquidity (relative volume vs average)
        - Spread liquidity (bid-ask spread efficiency)  
        - Options liquidity (open interest and volume)
        - Flow liquidity (money flow strength)
        
        Returns:
            float: Liquidity score [0.0, 1.0]
        """
        try:
            # Extract market data components
            volume_analysis = market_context.get('volume_analysis', {})
            flow_analysis = market_context.get('flow_analysis', {})
            iv_dynamics = market_context.get('iv_dynamics_analysis', {})
            
            # Component 1: Volume liquidity
            relative_volume = volume_analysis.get('relative_volume', 1.0)
            volume_liquidity = min(relative_volume / 2.0, 1.0)  # 2x normal = max
            
            # Component 2: Flow liquidity (institutional money flow)
            flow_strength = flow_analysis.get('strength', 0.5)
            flow_momentum = abs(flow_analysis.get('momentum', 0.0))
            flow_liquidity = min((flow_strength + min(flow_momentum / 2.0, 1.0)) / 2, 1.0)
            
            # Component 3: Options liquidity environment
            iv_rank = iv_dynamics.get('iv_rank', 50.0)
            if 25 <= iv_rank <= 75:
                iv_liquidity = 1.0      # Optimal options trading zone
            elif 15 <= iv_rank <= 85:
                iv_liquidity = 0.8      # Acceptable zone
            else:
                iv_liquidity = 0.5      # Suboptimal extremes
            
            # Component 4: Market structure liquidity
            volatility_regime = iv_dynamics.get('volatility_regime', 'normal')
            structure_multipliers = {
                'low': 0.7,       # Low vol = less options activity
                'normal': 1.0,    # Normal = optimal
                'high': 1.1,      # High vol = more activity
                'extreme': 0.8    # Extreme = stressed conditions
            }
            structure_liquidity = structure_multipliers.get(volatility_regime, 1.0)
            
            # Weighted composite liquidity score
            liquidity_score = (
                volume_liquidity * 0.30 +     # Volume availability
                flow_liquidity * 0.30 +       # Institutional flow
                iv_liquidity * 0.25 +         # Options environment
                structure_liquidity * 0.15    # Market structure
            )
            
            return min(max(liquidity_score, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Liquidity calculation failed: {e}")
            return 0.5  # Conservative fallback
    
    def _determine_market_direction(self, market_context: Dict[str, Any], liquidity_score: float) -> str:
        """
        Determine market direction for directional options trades
        
        Args:
            market_context: Market analysis data
            liquidity_score: Current liquidity score
            
        Returns:
            str: 'bullish', 'bearish', or 'neutral'
        """
        try:
            # Only determine direction in good liquidity conditions
            if liquidity_score < self.liquidity_gates['low']:
                return 'neutral'  # Don't trade directionally in poor liquidity
            
            # Flow analysis
            flow_data = market_context.get('flow_analysis', {})
            momentum = flow_data.get('momentum', 0.0)
            direction = flow_data.get('direction', 'neutral')
            strength = flow_data.get('strength', 0.5)
            
            # B-series confirmation
            b_series = market_context.get('b_series_analysis', {})
            pattern_strength = b_series.get('pattern_strength', 0.5)
            confidence = b_series.get('confidence', 0.5)
            
            # Directional score calculation
            direction_score = 0.0
            
            if direction == 'bullish' and momentum > 0:
                direction_score = momentum * strength * pattern_strength
            elif direction == 'bearish' and momentum < 0:
                direction_score = abs(momentum) * strength * pattern_strength
            else:
                direction_score = 0.0
            
            # Liquidity-adjusted thresholds
            threshold = 0.3 * (liquidity_score ** 0.5)  # Higher liquidity = lower threshold
            
            if direction_score > threshold:
                return direction if direction in ['bullish', 'bearish'] else 'neutral'
            else:
                return 'neutral'
                
        except Exception as e:
            logger.error(f"Direction determination failed: {e}")
            return 'neutral'
    
    def _calculate_dynamic_thresholds(self, liquidity_score: float) -> Dict[str, float]:
        """
        Calculate dynamic decision thresholds based on liquidity quality
        
        Higher liquidity = lower thresholds (easier to execute)
        Lower liquidity = higher thresholds (harder to execute)
        """
        if liquidity_score >= self.liquidity_gates['good']:
            # Good liquidity - normal thresholds
            return {
                'execute': 0.75,
                'hold': 0.25
            }
        elif liquidity_score >= self.liquidity_gates['low']:
            # Moderate liquidity - elevated thresholds
            adjustment = (self.liquidity_gates['good'] - liquidity_score) * 0.3
            return {
                'execute': 0.75 + adjustment,
                'hold': 0.25 + adjustment
            }
        else:
            # Poor liquidity - very high thresholds
            return {
                'execute': 0.90,
                'hold': 0.50
            }
    
    def predict(self, signal_data: Dict[str, Any], math_data: Dict[str, Any], 
                market_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate enhanced trading decision with liquidity awareness and directional actions
        
        Returns buy_calls, buy_puts, hold, or avoid decisions
        """
        prediction_start = datetime.now()
        
        # Base decision structure
        decision = {
            'timestamp': prediction_start.isoformat(),
            'agent_version': 'Enhanced_1.0_Balanced',
            'weight_structure': self.weights,
            'execution_time_ms': 0,
            'action': 'hold',  # Conservative default
            'confidence': 0.0,
            'reasoning': []
        }
        
        try:
            # Calculate liquidity score
            liquidity_score = self.calculate_liquidity_score(market_context or {})
            
            # Extract signal components
            signal_confidence = signal_data.get('confidence', 0.5)
            signal_strength = signal_data.get('strength', 0.5)  # Institutional flow conviction
            execution_rec = signal_data.get('execution_recommendation', 'hold')
            math_accuracy = math_data.get('accuracy_score', 0.5)
            math_precision = math_data.get('precision', 0.001)
            
            # Convert execution recommendation to score
            exec_scores = {'execute': 1.0, 'delay': 0.5, 'hold': 0.5, 'avoid': 0.0}
            exec_score = exec_scores.get(execution_rec, 0.5)
            
            # Enhanced weighted composite score
            composite_score = (
                liquidity_score * self.weights['liquidity_score'] +
                signal_confidence * self.weights['signal_confidence'] +
                signal_strength * self.weights['signal_strength'] +
                exec_score * self.weights['execution_recommendation'] +
                math_accuracy * self.weights['math_accuracy'] +
                min(math_precision, 0.1) * 10 * self.weights['math_precision']
            )
            
            # Liquidity gates - override poor liquidity conditions
            if liquidity_score < self.liquidity_gates['critical_low']:
                action = 'avoid'
                final_confidence = composite_score * 0.3  # Severely penalize
                reasoning = [
                    f"Critical liquidity gate triggered: {liquidity_score:.3f} < {self.liquidity_gates['critical_low']}",
                    "Insufficient liquidity for safe execution"
                ]
            else:
                # Calculate dynamic thresholds
                thresholds = self._calculate_dynamic_thresholds(liquidity_score)
                
                # Determine market direction
                market_direction = self._determine_market_direction(market_context or {}, liquidity_score)
                
                # Enhanced decision logic with directional actions
                if composite_score >= thresholds['execute']:
                    if market_direction == 'bullish':
                        action = 'buy_calls'
                    elif market_direction == 'bearish':
                        action = 'buy_puts'
                    else:
                        action = 'hold'  # High confidence but unclear direction
                    final_confidence = composite_score
                elif composite_score >= thresholds['hold']:
                    action = 'hold'
                    final_confidence = composite_score
                else:
                    action = 'avoid'
                    final_confidence = composite_score
                
                # Build reasoning
                reasoning = [
                    f"Balanced decision: L={liquidity_score:.3f}, SC={signal_confidence:.3f}, SS={signal_strength:.3f}",
                    f"Composite score: {composite_score:.3f} vs thresholds E={thresholds['execute']:.2f}/H={thresholds['hold']:.2f}",
                    f"Market direction: {market_direction}",
                    f"Institutional flow conviction: {signal_strength:.3f}"
                ]
                
                if liquidity_score < self.liquidity_gates['good']:
                    reasoning.append(f"Elevated thresholds due to moderate liquidity: {liquidity_score:.3f}")
            
            # Final decision assembly
            decision.update({
                'action': action,
                'confidence': final_confidence,
                'composite_score': composite_score,
                'liquidity_score': liquidity_score,
                'market_direction': market_direction if 'market_direction' in locals() else 'neutral',
                'reasoning': reasoning,
                'components': {
                    'liquidity': liquidity_score,
                    'signal_confidence': signal_confidence,
                    'signal_strength': signal_strength,
                    'execution_rec': execution_rec,
                    'math_accuracy': math_accuracy,
                    'math_precision': math_precision
                },
                'thresholds_used': thresholds if 'thresholds' in locals() else self.thresholds,
                'decision_method': 'enhanced_balanced'
            })
            
        except Exception as e:
            logger.error(f"Enhanced prediction failed: {e}")
            decision.update({
                'action': 'avoid',
                'confidence': 0.0,
                'error': str(e),
                'reasoning': [f"Prediction error: {str(e)} - defaulting to avoid"],
                'decision_method': 'error_fallback'
            })
        
        # Calculate execution time
        execution_time = (datetime.now() - prediction_start).total_seconds() * 1000
        decision['execution_time_ms'] = round(execution_time, 2)
        
        return decision
    
    def log_training_data(self, signal_data: Dict, math_data: Dict, decision: Dict, 
                         outcome: float, market_context: Dict = None):
        """Log enhanced training data"""
        try:
            training_record = {
                'timestamp': datetime.now().isoformat(),
                'signal_data': signal_data,
                'math_data': math_data,
                'decision': decision,
                'outcome': outcome,
                'market_context': market_context or {},
                'agent_version': 'Enhanced_1.0_Balanced',
                'weight_structure': self.weights
            }
            
            # Generate filename
            timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"enhanced_training_{timestamp_str}.json"
            filepath = self.training_dir / filename
            
            # Save training data
            with open(filepath, 'w') as f:
                json.dump(training_record, f, indent=2, default=str)
            
            logger.info(f"Enhanced training data logged: {filename}")
                
        except Exception as e:
            logger.error(f"Failed to log enhanced training data: {e}")

# Backwards compatibility function
def get_enhanced_agent_zero():
    """Get enhanced Agent Zero instance"""
    return EnhancedAgentZeroAdvisor()

if __name__ == "__main__":
    # Test Enhanced Agent Zero
    agent = EnhancedAgentZeroAdvisor()
    
    print("Enhanced Agent Zero - Balanced Liquidity-Aware Options Advisor")
    print(f"Weight Structure: {agent.weights}")
    
    # Test with sample data
    test_signal = {
        'confidence': 0.8, 
        'strength': 0.7,    # Institutional flow conviction
        'execution_recommendation': 'execute'
    }
    test_math = {'accuracy_score': 0.95, 'precision': 0.001}
    test_context = {
        'flow_analysis': {'momentum': 1.5, 'direction': 'bullish', 'strength': 0.8},
        'volume_analysis': {'relative_volume': 1.8},
        'iv_dynamics_analysis': {'iv_rank': 45, 'volatility_regime': 'normal'},
        'b_series_analysis': {'pattern_strength': 0.7, 'confidence': 0.8}
    }
    
    decision = agent.predict(test_signal, test_math, test_context)
    print(f"\nTest Decision: {decision['action']} (confidence: {decision['confidence']:.3f})")
    print(f"Market Direction: {decision['market_direction']}")
    print(f"Liquidity Score: {decision['liquidity_score']:.3f}")
    
    print("\nEnhanced Agent Zero Ready - Balanced and Directional!")
