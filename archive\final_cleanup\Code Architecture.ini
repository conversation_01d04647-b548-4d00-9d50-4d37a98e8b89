Code Architecture
 CORE MODULES
    flow_physics_adapter.py
    liquidity_sweep_analyzer.py
    enhanced_volume_analyzer.py
    enhanced_gex_analyzer.py

 INFRASTRUCTURE
    mcp_api_gateway.py
    data_structure_handler.py
    factor_specification.py
    base_analyzer.py

 ML LAYER
    pattern_recognition_ml.py
    adaptive_thresholds.py
    temporal_analysis.py
    success_rate_tracker.py

 ORCHESTRATION
    flow_orchestrator.py
    factor_confluence.py
    signal_generator.py
    performance_monitor.py

 OUTPUT SYSTEM
    chart_generator.py (3 charts only)
    unified_report.py (single report)
    validation_engine.py
    export_manager.py

 UTILITIES
     timeframe_optimizer.py
     range_calculator.py
     gex_calculator.py
     logging_system.py