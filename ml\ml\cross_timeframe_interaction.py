"""
Cross-Timeframe Interaction Models

This module implements models that explicitly account for interactions between timeframes,
including correlation analysis and methods to detect leading/lagging relationships.
"""

import os
import logging
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
from scipy import stats
from statsmodels.tsa.stattools import grangercausalitytests, adfuller
from statsmodels.tsa.vector_ar.vecm import coint_johansen

# Import ML components
from ml.ml_logging import get_logger

logger = get_logger(__name__)

class CrossTimeframeInteraction:
    """
    Models interactions between different timeframes.

    This class implements methods to analyze how different timeframes interact,
    including correlation analysis, lead-lag detection, and causal relationships.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the cross-timeframe interaction model.

        Args:
            config: Configuration dictionary with parameters
        """
        # Default configuration
        default_config = {
            # Timeframe parameters
            'timeframe_hierarchy': ['1m', '5m', '15m', '1h', '4h', '1d', '1w'],  # Ordered from lowest to highest

            # Correlation parameters
            'correlation_methods': ['pearson', 'spearman'],
            'correlation_window': 20,
            'min_correlation_threshold': 0.5,

            # Lead-lag parameters
            'max_lag': 10,
            'min_lag_correlation': 0.4,
            'granger_max_lag': 5,
            'granger_significance': 0.05,

            # Cointegration parameters
            'enable_cointegration': True,
            'coint_significance': 0.05,

            # Feature generation parameters
            'generate_interaction_features': True,
            'max_interaction_features': 50
        }

        # Initialize configuration
        self.config = default_config.copy()
        if config:
            self.config.update(config)

        # Initialize results containers
        self.correlations = {}
        self.lead_lag = {}
        self.causality = {}
        self.cointegration = {}
        self.interaction_features = {}

        logger.info("Initialized cross-timeframe interaction model")

    def analyze_timeframe_interactions(self,
                                      market_data: Dict[str, pd.DataFrame],
                                      feature_data: Optional[Dict[str, pd.DataFrame]] = None) -> Dict[str, Any]:
        """
        Analyze interactions between different timeframes.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames
            feature_data: Optional dictionary mapping timeframes to feature DataFrames

        Returns:
            Dictionary with interaction analysis results
        """
        if not market_data or len(market_data) < 2:
            logger.error("Insufficient data for timeframe interaction analysis")
            return {}

        logger.info("Analyzing timeframe interactions")
        start_time = time.time()

        try:
            # Step 1: Calculate correlations between timeframes
            self.correlations = self._calculate_timeframe_correlations(market_data)

            # Step 2: Detect lead-lag relationships
            self.lead_lag = self._detect_lead_lag_relationships(market_data)

            # Step 3: Test for Granger causality
            self.causality = self._test_granger_causality(market_data)

            # Step 4: Test for cointegration if enabled
            if self.config['enable_cointegration']:
                self.cointegration = self._test_cointegration(market_data)

            # Step 5: Generate interaction features if enabled and feature data is provided
            if self.config['generate_interaction_features'] and feature_data:
                self.interaction_features = self._generate_interaction_features(feature_data)

            # Calculate and log timing
            elapsed = time.time() - start_time
            logger.info(f"Completed timeframe interaction analysis in {elapsed:.2f} seconds")

            # Prepare results
            results = {
                'correlations': self.correlations,
                'lead_lag': self.lead_lag,
                'causality': self.causality,
                'cointegration': self.cointegration,
                'interaction_features': self.interaction_features
            }

            return results

        except Exception as e:
            logger.error(f"Error analyzing timeframe interactions: {e}")
            return {}

    def _calculate_timeframe_correlations(self, market_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, float]]:
        """
        Calculate correlations between different timeframes.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames

        Returns:
            Dictionary mapping timeframe pairs to correlation values
        """
        correlations = {}

        try:
            # Get ordered timeframes based on hierarchy
            ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in market_data]

            if len(ordered_timeframes) < 2:
                logger.warning("Not enough timeframes for correlation analysis")
                return correlations

            # Resample all data to the lowest timeframe for comparison
            lowest_tf = ordered_timeframes[0]
            resampled_data = {}

            for tf in ordered_timeframes:
                # Skip if data is empty
                if market_data[tf].empty:
                    continue

                # Use close prices for correlation
                if 'close' in market_data[tf].columns:
                    resampled_data[tf] = market_data[tf]['close']
                else:
                    logger.warning(f"No close prices found for timeframe {tf}")
                    continue

            # Calculate correlations for each timeframe pair
            for i, tf1 in enumerate(ordered_timeframes):
                if tf1 not in resampled_data:
                    continue

                correlations[tf1] = {}

                for j, tf2 in enumerate(ordered_timeframes):
                    if i == j or tf2 not in resampled_data:
                        continue

                    # Calculate correlation using different methods
                    corr_values = {}

                    for method in self.config['correlation_methods']:
                        if method == 'pearson':
                            corr = resampled_data[tf1].corr(resampled_data[tf2], method='pearson')
                        elif method == 'spearman':
                            corr = resampled_data[tf1].corr(resampled_data[tf2], method='spearman')
                        else:
                            continue

                        corr_values[method] = corr

                    # Calculate rolling correlation if window is specified
                    if self.config['correlation_window'] > 0:
                        # Align the series
                        aligned_data = pd.concat([resampled_data[tf1], resampled_data[tf2]], axis=1, join='inner')
                        aligned_data.columns = [tf1, tf2]

                        # Calculate rolling correlation
                        rolling_corr = aligned_data[tf1].rolling(
                            window=self.config['correlation_window']
                        ).corr(aligned_data[tf2])

                        # Get recent correlation
                        recent_corr = rolling_corr.iloc[-1] if not rolling_corr.empty else np.nan
                        corr_values['rolling'] = recent_corr

                    # Store correlation values
                    correlations[tf1][tf2] = corr_values

            return correlations

        except Exception as e:
            logger.error(f"Error calculating timeframe correlations: {e}")
            return {}

    def _detect_lead_lag_relationships(self, market_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Detect lead-lag relationships between timeframes.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames

        Returns:
            Dictionary with lead-lag relationship information
        """
        lead_lag = {}

        try:
            # Get ordered timeframes based on hierarchy
            ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in market_data]

            if len(ordered_timeframes) < 2:
                logger.warning("Not enough timeframes for lead-lag analysis")
                return lead_lag

            # Resample all data to the lowest timeframe for comparison
            resampled_data = {}

            for tf in ordered_timeframes:
                # Skip if data is empty
                if market_data[tf].empty:
                    continue

                # Use close prices for lead-lag analysis
                if 'close' in market_data[tf].columns:
                    resampled_data[tf] = market_data[tf]['close']
                else:
                    logger.warning(f"No close prices found for timeframe {tf}")
                    continue

            # Detect lead-lag for each timeframe pair
            for i, tf1 in enumerate(ordered_timeframes):
                if tf1 not in resampled_data:
                    continue

                lead_lag[tf1] = {}

                for j, tf2 in enumerate(ordered_timeframes):
                    if i == j or tf2 not in resampled_data:
                        continue

                    # Calculate cross-correlation with lags
                    lead_lag[tf1][tf2] = self._calculate_cross_correlation(
                        resampled_data[tf1], resampled_data[tf2]
                    )

            return lead_lag

        except Exception as e:
            logger.error(f"Error detecting lead-lag relationships: {e}")
            return {}

    def _calculate_cross_correlation(self, series1: pd.Series, series2: pd.Series) -> Dict[str, Any]:
        """
        Calculate cross-correlation between two time series with lags.

        Args:
            series1: First time series
            series2: Second time series

        Returns:
            Dictionary with cross-correlation results
        """
        results = {
            'max_correlation': 0.0,
            'optimal_lag': 0,
            'direction': 'none',
            'correlations': {}
        }

        try:
            # Align the series
            aligned = pd.concat([series1, series2], axis=1, join='inner')
            aligned.columns = ['series1', 'series2']

            if aligned.empty or len(aligned) < self.config['max_lag'] + 1:
                return results

            # Calculate cross-correlation for different lags
            max_corr = 0.0
            optimal_lag = 0

            for lag in range(-self.config['max_lag'], self.config['max_lag'] + 1):
                if lag < 0:
                    # series1 lags behind series2
                    s1 = aligned['series1'].shift(-lag)
                    s2 = aligned['series2']
                else:
                    # series2 lags behind series1
                    s1 = aligned['series1']
                    s2 = aligned['series2'].shift(lag)

                # Calculate correlation
                corr = s1.corr(s2)
                results['correlations'][lag] = corr

                # Update max correlation if higher
                if abs(corr) > abs(max_corr):
                    max_corr = corr
                    optimal_lag = lag

            # Determine lead-lag direction
            if abs(max_corr) >= self.config['min_lag_correlation']:
                if optimal_lag < 0:
                    # series1 lags behind series2 (series2 leads)
                    results['direction'] = 'lag'
                elif optimal_lag > 0:
                    # series2 lags behind series1 (series1 leads)
                    results['direction'] = 'lead'
                else:
                    # No lag (contemporaneous)
                    results['direction'] = 'contemporaneous'

                results['max_correlation'] = max_corr
                results['optimal_lag'] = optimal_lag

            return results

        except Exception as e:
            logger.error(f"Error calculating cross-correlation: {e}")
            return results

    def _test_granger_causality(self, market_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Test for Granger causality between timeframes.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames

        Returns:
            Dictionary with Granger causality test results
        """
        causality = {}

        try:
            # Get ordered timeframes based on hierarchy
            ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in market_data]

            if len(ordered_timeframes) < 2:
                logger.warning("Not enough timeframes for Granger causality testing")
                return causality

            # Resample all data to the lowest timeframe for comparison
            resampled_data = {}

            for tf in ordered_timeframes:
                # Skip if data is empty
                if market_data[tf].empty:
                    continue

                # Use close prices for causality testing
                if 'close' in market_data[tf].columns:
                    resampled_data[tf] = market_data[tf]['close']
                else:
                    logger.warning(f"No close prices found for timeframe {tf}")
                    continue

            # Test Granger causality for each timeframe pair
            for i, tf1 in enumerate(ordered_timeframes):
                if tf1 not in resampled_data:
                    continue

                causality[tf1] = {}

                for j, tf2 in enumerate(ordered_timeframes):
                    if i == j or tf2 not in resampled_data:
                        continue

                    # Align the series
                    aligned = pd.concat([resampled_data[tf1], resampled_data[tf2]], axis=1, join='inner')
                    aligned.columns = [tf1, tf2]

                    # Skip if not enough data
                    if len(aligned) <= self.config['granger_max_lag']:
                        continue

                    # Test if tf1 Granger-causes tf2
                    try:
                        gc_result = grangercausalitytests(
                            aligned[[tf2, tf1]],  # Order matters: y, x
                            maxlag=self.config['granger_max_lag'],
                            verbose=False
                        )

                        # Extract p-values for each lag
                        p_values = {lag: result[0]['ssr_chi2test'][1] for lag, result in gc_result.items()}

                        # Determine if there is Granger causality
                        min_p_value = min(p_values.values())
                        significant = min_p_value < self.config['granger_significance']

                        causality[tf1][tf2] = {
                            'p_values': p_values,
                            'min_p_value': min_p_value,
                            'significant': significant,
                            'optimal_lag': min(p_values, key=p_values.get) if significant else 0
                        }

                    except Exception as e:
                        logger.warning(f"Error in Granger causality test for {tf1} -> {tf2}: {e}")
                        causality[tf1][tf2] = {
                            'p_values': {},
                            'min_p_value': 1.0,
                            'significant': False,
                            'optimal_lag': 0,
                            'error': str(e)
                        }

            return causality

        except Exception as e:
            logger.error(f"Error testing Granger causality: {e}")
            return {}

    def _test_cointegration(self, market_data: Dict[str, pd.DataFrame]) -> Dict[str, Dict[str, Dict[str, Any]]]:
        """
        Test for cointegration between timeframes.

        Args:
            market_data: Dictionary mapping timeframes to price DataFrames

        Returns:
            Dictionary with cointegration test results
        """
        cointegration = {}

        try:
            # Get ordered timeframes based on hierarchy
            ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in market_data]

            if len(ordered_timeframes) < 2:
                logger.warning("Not enough timeframes for cointegration testing")
                return cointegration

            # Resample all data to the lowest timeframe for comparison
            resampled_data = {}

            for tf in ordered_timeframes:
                # Skip if data is empty
                if market_data[tf].empty:
                    continue

                # Use close prices for cointegration testing
                if 'close' in market_data[tf].columns:
                    resampled_data[tf] = market_data[tf]['close']
                else:
                    logger.warning(f"No close prices found for timeframe {tf}")
                    continue

            # Test cointegration for each timeframe pair
            for i, tf1 in enumerate(ordered_timeframes):
                if tf1 not in resampled_data:
                    continue

                cointegration[tf1] = {}

                for j, tf2 in enumerate(ordered_timeframes):
                    if i == j or tf2 not in resampled_data:
                        continue

                    # Align the series
                    aligned = pd.concat([resampled_data[tf1], resampled_data[tf2]], axis=1, join='inner')
                    aligned.columns = [tf1, tf2]

                    # Skip if not enough data
                    if len(aligned) < 30:  # Need sufficient data for reliable test
                        continue

                    # Test for cointegration
                    try:
                        # Engle-Granger two-step approach
                        # Step 1: Run regression
                        X = aligned[tf1].values.reshape(-1, 1)
                        y = aligned[tf2].values
                        X = np.hstack((np.ones_like(X), X))  # Add constant
                        beta = np.linalg.lstsq(X, y, rcond=None)[0]

                        # Step 2: Test residuals for stationarity
                        residuals = y - X.dot(beta)
                        adf_result = adfuller(residuals)

                        # Determine if cointegrated
                        p_value = adf_result[1]
                        significant = p_value < self.config['coint_significance']

                        cointegration[tf1][tf2] = {
                            'adf_statistic': adf_result[0],
                            'p_value': p_value,
                            'significant': significant,
                            'critical_values': adf_result[4],
                            'beta': beta[1]  # Slope coefficient
                        }

                    except Exception as e:
                        logger.warning(f"Error in cointegration test for {tf1} and {tf2}: {e}")
                        cointegration[tf1][tf2] = {
                            'adf_statistic': 0.0,
                            'p_value': 1.0,
                            'significant': False,
                            'critical_values': {},
                            'beta': 0.0,
                            'error': str(e)
                        }

            return cointegration

        except Exception as e:
            logger.error(f"Error testing cointegration: {e}")
            return {}

    def _generate_interaction_features(self, feature_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Generate features that capture interactions between timeframes.

        Args:
            feature_data: Dictionary mapping timeframes to feature DataFrames

        Returns:
            Dictionary mapping timeframes to interaction feature DataFrames
        """
        interaction_features = {}

        try:
            # Get ordered timeframes based on hierarchy
            ordered_timeframes = [tf for tf in self.config['timeframe_hierarchy'] if tf in feature_data]

            if len(ordered_timeframes) < 2:
                logger.warning("Not enough timeframes for interaction feature generation")
                return interaction_features

            # Generate interaction features for each timeframe
            for i, tf1 in enumerate(ordered_timeframes):
                if tf1 not in feature_data or feature_data[tf1].empty:
                    continue

                # Initialize interaction features for this timeframe
                interaction_features[tf1] = pd.DataFrame(index=feature_data[tf1].index)

                # Generate features based on interactions with other timeframes
                for j, tf2 in enumerate(ordered_timeframes):
                    if i == j or tf2 not in feature_data or feature_data[tf2].empty:
                        continue

                    # Select a subset of features from each timeframe
                    # (to avoid combinatorial explosion)
                    tf1_features = feature_data[tf1].select_dtypes(include=[np.number]).columns[:5]
                    tf2_features = feature_data[tf2].select_dtypes(include=[np.number]).columns[:5]

                    # Generate ratio features
                    for col1 in tf1_features:
                        for col2 in tf2_features:
                            # Skip if either column has all zeros
                            if (feature_data[tf1][col1] == 0).all() or (feature_data[tf2][col2] == 0).all():
                                continue

                            # Create ratio feature
                            feature_name = f"{tf1}_{col1}_to_{tf2}_{col2}_ratio"

                            # Use the mean of tf2 feature since it might have different frequency
                            tf2_value = feature_data[tf2][col2].mean()

                            if tf2_value != 0:
                                interaction_features[tf1][feature_name] = feature_data[tf1][col1] / tf2_value

                    # Generate correlation features if we have lead-lag information
                    if tf1 in self.lead_lag and tf2 in self.lead_lag[tf1]:
                        lead_lag_info = self.lead_lag[tf1][tf2]

                        if lead_lag_info['direction'] != 'none':
                            # Create feature based on lead-lag relationship
                            feature_name = f"{tf1}_leads_{tf2}" if lead_lag_info['direction'] == 'lead' else f"{tf2}_leads_{tf1}"
                            interaction_features[tf1][feature_name] = lead_lag_info['max_correlation']

                    # Generate causality features if we have causality information
                    if tf1 in self.causality and tf2 in self.causality[tf1]:
                        causality_info = self.causality[tf1][tf2]

                        if causality_info['significant']:
                            # Create feature based on Granger causality
                            feature_name = f"{tf1}_causes_{tf2}"
                            interaction_features[tf1][feature_name] = 1.0 - causality_info['min_p_value']

                    # Generate cointegration features if we have cointegration information
                    if self.config['enable_cointegration'] and tf1 in self.cointegration and tf2 in self.cointegration[tf1]:
                        coint_info = self.cointegration[tf1][tf2]

                        if coint_info['significant']:
                            # Create feature based on cointegration
                            feature_name = f"{tf1}_cointegrated_with_{tf2}"
                            interaction_features[tf1][feature_name] = coint_info['beta']

            # Limit number of interaction features if specified
            if self.config['max_interaction_features'] > 0:
                for tf in interaction_features:
                    if len(interaction_features[tf].columns) > self.config['max_interaction_features']:
                        # Select features based on variance
                        variances = interaction_features[tf].var().sort_values(ascending=False)
                        top_features = variances.index[:self.config['max_interaction_features']]
                        interaction_features[tf] = interaction_features[tf][top_features]

            return interaction_features

        except Exception as e:
            logger.error(f"Error generating interaction features: {e}")
            return {}

    def get_strongest_interactions(self, num_interactions: int = 5) -> List[Dict[str, Any]]:
        """
        Get the strongest interactions between timeframes.

        Args:
            num_interactions: Number of top interactions to return

        Returns:
            List of dictionaries with interaction information
        """
        interactions = []

        try:
            # Collect all interactions
            for tf1 in self.correlations:
                for tf2 in self.correlations[tf1]:
                    # Get correlation
                    corr_value = self.correlations[tf1][tf2].get('pearson', 0.0)

                    # Get lead-lag information
                    lead_lag_info = self.lead_lag.get(tf1, {}).get(tf2, {})
                    lead_lag_corr = lead_lag_info.get('max_correlation', 0.0)
                    lead_lag_dir = lead_lag_info.get('direction', 'none')

                    # Get causality information
                    causality_info = self.causality.get(tf1, {}).get(tf2, {})
                    causality_sig = causality_info.get('significant', False)

                    # Get cointegration information
                    coint_info = self.cointegration.get(tf1, {}).get(tf2, {})
                    coint_sig = coint_info.get('significant', False)

                    # Calculate overall interaction strength
                    strength = abs(corr_value) * 0.3 + abs(lead_lag_corr) * 0.3
                    strength += 0.2 if causality_sig else 0.0
                    strength += 0.2 if coint_sig else 0.0

                    interactions.append({
                        'timeframe1': tf1,
                        'timeframe2': tf2,
                        'correlation': corr_value,
                        'lead_lag': lead_lag_dir,
                        'lead_lag_correlation': lead_lag_corr,
                        'causality': causality_sig,
                        'cointegration': coint_sig,
                        'strength': strength
                    })

            # Sort by strength
            interactions.sort(key=lambda x: x['strength'], reverse=True)

            return interactions[:num_interactions]

        except Exception as e:
            logger.error(f"Error getting strongest interactions: {e}")
            return []
