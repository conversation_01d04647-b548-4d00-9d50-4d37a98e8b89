#!/usr/bin/env python3
"""
API Health Monitor - Mathematical health scoring
Statistical uptime analysis
Zero-downtime monitoring system
"""

import time
import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from collections import deque
import logging

@dataclass
class HealthCheck:
    """Individual health check result."""
    timestamp: float
    component: str
    status: str  # healthy, degraded, unhealthy
    response_time_ms: float
    error: Optional[str] = None
    
    def is_healthy(self) -> bool:
        """Check if health check passed."""
        return self.status == "healthy"

class ComponentMonitor:
    """Monitor individual API component."""
    
    def __init__(self, name: str, check_interval: float = 60.0):
        """Initialize component monitor."""
        self.name = name
        self.check_interval = check_interval
        self.history = deque(maxlen=1000)
        self.last_check = 0
        self.current_status = "unknown"
        self.logger = logging.getLogger(f"monitor.{name}")
    
    async def check_health(self, check_func) -> HealthCheck:
        """Execute health check with timing."""
        start_time = time.time()
        
        try:
            result = await check_func() if asyncio.iscoroutinefunction(check_func) else check_func()
            response_time = (time.time() - start_time) * 1000
            
            status = "healthy" if result else "degraded"
            
            health_check = HealthCheck(
                timestamp=time.time(),
                component=self.name,
                status=status,
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            health_check = HealthCheck(
                timestamp=time.time(),
                component=self.name,
                status="unhealthy",
                response_time_ms=response_time,
                error=str(e)
            )
        
        self.history.append(health_check)
        self.current_status = health_check.status
        self.last_check = time.time()
        
        return health_check
    
    def get_statistics(self) -> Dict[str, Any]:
        """Calculate component health statistics."""
        if not self.history:
            return {"status": "no_data"}
        
        recent_checks = list(self.history)[-100:]  # Last 100 checks
        
        healthy_count = sum(1 for check in recent_checks if check.is_healthy())
        uptime = healthy_count / len(recent_checks)
        
        response_times = [check.response_time_ms for check in recent_checks]
        avg_response_time = sum(response_times) / len(response_times)
        
        return {
            "component": self.name,
            "current_status": self.current_status,
            "uptime_percentage": uptime * 100,
            "avg_response_time_ms": avg_response_time,
            "last_check": self.last_check,
            "check_count": len(recent_checks)
        }

class APIHealthMonitor:
    """Comprehensive API health monitoring system."""
    
    def __init__(self):
        """Initialize health monitoring system."""
        self.monitors: Dict[str, ComponentMonitor] = {}
        self.overall_status = "unknown"
        self.monitoring_active = False
        self.logger = logging.getLogger(__name__)
    
    def add_component(self, name: str, check_func, interval: float = 60.0):
        """Add component for monitoring."""
        self.monitors[name] = ComponentMonitor(name, interval)
        setattr(self, f"_check_{name}", check_func)
        self.logger.info(f"Added health monitor for component: {name}")
    
    async def check_all_components(self) -> Dict[str, HealthCheck]:
        """Check health of all components."""
        results = {}
        
        for name, monitor in self.monitors.items():
            check_func = getattr(self, f"_check_{name}", None)
            if check_func:
                try:
                    result = await monitor.check_health(check_func)
                    results[name] = result
                except Exception as e:
                    self.logger.error(f"Health check failed for {name}: {e}")
        
        self._update_overall_status(results)
        return results
    
    def _update_overall_status(self, results: Dict[str, HealthCheck]):
        """Update overall system health status."""
        if not results:
            self.overall_status = "unknown"
            return
        
        unhealthy_count = sum(1 for check in results.values() if check.status == "unhealthy")
        degraded_count = sum(1 for check in results.values() if check.status == "degraded")
        
        if unhealthy_count > 0:
            self.overall_status = "unhealthy"
        elif degraded_count > 0:
            self.overall_status = "degraded"
        else:
            self.overall_status = "healthy"
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get comprehensive system health report."""
        component_stats = {}
        for name, monitor in self.monitors.items():
            component_stats[name] = monitor.get_statistics()
        
        # Calculate overall metrics
        uptimes = [stats.get("uptime_percentage", 0) for stats in component_stats.values()]
        avg_uptime = sum(uptimes) / len(uptimes) if uptimes else 0
        
        response_times = [stats.get("avg_response_time_ms", 0) for stats in component_stats.values()]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        return {
            "overall_status": self.overall_status,
            "overall_uptime_percentage": avg_uptime,
            "overall_avg_response_time_ms": avg_response_time,
            "component_count": len(self.monitors),
            "components": component_stats,
            "timestamp": time.time()
        }
    
    async def start_monitoring(self, interval: float = 300.0):
        """Start continuous health monitoring."""
        self.monitoring_active = True
        self.logger.info("Health monitoring started")
        
        while self.monitoring_active:
            try:
                await self.check_all_components()
                await asyncio.sleep(interval)
            except Exception as e:
                self.logger.error(f"Monitoring error: {e}")
                await asyncio.sleep(interval)
    
    def stop_monitoring(self):
        """Stop health monitoring."""
        self.monitoring_active = False
        self.logger.info("Health monitoring stopped")
