#!/usr/bin/env python3
"""
Agent Zero Data Integration Verification System
Verifies that Agent Zero is effectively receiving and processing data from all 27 agents
"""

import sys
import os
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List
import pandas as pd

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Import CORE system components
from ultimate_orchestrator import ultimate_trading_pipeline
from AGENT_ZERO_TREE.integration_hub.hub_controller import get_agent_zero_hub

class AgentZeroDataVerifier:
    """Comprehensive verification of Agent Zero data integration"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        self.verification_results = {}
        self.test_ticker = "SPY"  # Use SPY for testing
        
        # Expected agent data sources (27 total)
        self.expected_agents = {
            # Core Pipeline (7 agents)
            'data_agent': 'Single-source data from Schwab MCP',
            'b_series': 'B-Series feature analysis (52 features)',
            'a01_anomalies': 'Anomaly detection analysis',
            'c02_iv_dynamics': 'Implied volatility dynamics',
            'f01_csid': 'CSID institutional flow analysis',
            'f02_flow_physics': 'Flow physics analysis',
            
            # Specialized Army (11 agents)
            'accumulation_distribution': 'Enhanced accumulation/distribution',
            'breakout_validation': 'Breakout validation agent',
            'options_flow': 'Options flow decoder',
            'liquidity_agent': 'Liquidity analysis',
            'signal_convergence_orchestrator': 'Signal convergence',
            'math_validator_agent': 'Mathematical validation',
            'chart_generator_agent': 'Chart generation',
            'risk_guard_agent': 'Risk management',
            'output_coordinator_agent': 'Output coordination',
            'signal_quality_agent': 'Signal quality assessment',
            'options_intelligence_agent': 'Options intelligence',
            
            # Extended Analysis (6 agents)
            'fvg_specialist': 'Fair Value Gap analysis',
            'mean_reversion_specialist': 'Mean reversion analysis',
            'pivot_point_specialist': 'Pivot point analysis',
            'liquidity_specialist': 'Liquidity specialist',
            'signal_convergence': 'Signal convergence analysis',
            'math_validation': 'Math validation analysis',
            
            # Additional Agents (3 agents)
            'agent_zero_options_agent': 'Agent Zero options integration',
            'enhanced_agent_zero': 'Enhanced Agent Zero capabilities',
            'agent_zero_complete_system': 'Complete system integration'
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        return logging.getLogger("AgentZeroDataVerifier")
    
    def verify_data_integration(self) -> Dict[str, Any]:
        """Comprehensive verification of Agent Zero data integration"""
        
        self.logger.info("Starting Agent Zero Data Integration Verification")
        self.logger.info(f"Testing with ticker: {self.test_ticker}")
        self.logger.info(f"Expected agents: {len(self.expected_agents)}")
        
        # Step 1: Run complete pipeline
        self.logger.info("Step 1: Running complete ultimate trading pipeline...")
        try:
            pipeline_result = ultimate_trading_pipeline(self.test_ticker)
            self.verification_results['pipeline_execution'] = {
                'status': 'SUCCESS',
                'timestamp': datetime.now().isoformat(),
                'components': pipeline_result.get('pipeline_components', [])
            }
        except Exception as e:
            self.logger.error(f"Pipeline execution failed: {e}")
            self.verification_results['pipeline_execution'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            return self.verification_results
        
        # Step 2: Verify Agent Zero received data
        self.logger.info("Step 2: Verifying Agent Zero data reception...")
        agent_zero_data = self._verify_agent_zero_data(pipeline_result)
        
        # Step 3: Verify data completeness
        self.logger.info("Step 3: Verifying data completeness...")
        completeness_analysis = self._analyze_data_completeness(pipeline_result)
        
        # Step 4: Verify data quality
        self.logger.info("Step 4: Verifying data quality...")
        quality_analysis = self._analyze_data_quality(pipeline_result)
        
        # Step 5: Generate recommendations
        self.logger.info("Step 5: Generating recommendations...")
        recommendations = self._generate_recommendations(pipeline_result)
        
        # Compile final results
        self.verification_results.update({
            'agent_zero_data': agent_zero_data,
            'completeness_analysis': completeness_analysis,
            'quality_analysis': quality_analysis,
            'recommendations': recommendations,
            'verification_timestamp': datetime.now().isoformat()
        })
        
        return self.verification_results
    
    def _verify_agent_zero_data(self, pipeline_result: Dict[str, Any]) -> Dict[str, Any]:
        """Verify Agent Zero received data from all sources"""
        
        agent_zero_intel = pipeline_result.get('agent_zero_intelligence', {})
        ensemble_intel = pipeline_result.get('ensemble_intelligence', {})
        
        # Check if Agent Zero received data
        received_data = {
            'agent_zero_available': bool(agent_zero_intel),
            'ensemble_available': bool(ensemble_intel),
            'market_context_available': False,
            'extended_analysis_available': False,
            'data_sources_info': {}
        }
        
        # Check market context data
        if 'market_context' in str(agent_zero_intel):
            received_data['market_context_available'] = True
        
        # Check extended analysis
        if 'extended_analysis' in pipeline_result:
            received_data['extended_analysis_available'] = True
            extended_data = pipeline_result['extended_analysis']
            received_data['extended_analysis_components'] = list(extended_data.keys())
        
        # Check ensemble intelligence sources
        if ensemble_intel and 'intelligence_sources' in ensemble_intel:
            sources = ensemble_intel['intelligence_sources']
            received_data['data_sources_info'] = {
                'total_sources': len(sources),
                'source_list': sources,
                'expected_sources': len(self.expected_agents),
                'coverage_percentage': (len(sources) / len(self.expected_agents)) * 100
            }
        
        return received_data
    
    def _analyze_data_completeness(self, pipeline_result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze completeness of data integration"""
        
        completeness = {
            'core_pipeline': {},
            'specialized_army': {},
            'extended_analysis': {},
            'overall_score': 0.0
        }
        
        # Check core pipeline components
        core_components = ['b_series', 'a01_anomalies', 'c02_iv_dynamics', 'f01_csid', 'f02_flow_physics']
        core_present = sum(1 for comp in core_components if comp in pipeline_result)
        completeness['core_pipeline'] = {
            'expected': len(core_components),
            'present': core_present,
            'percentage': (core_present / len(core_components)) * 100,
            'missing': [comp for comp in core_components if comp not in pipeline_result]
        }
        
        # Check specialized army
        specialized_army = pipeline_result.get('specialized_army', {})
        army_components = ['accumulation_distribution', 'breakout_validation', 'options_flow']
        if isinstance(specialized_army, dict) and 'results' in specialized_army:
            army_results = specialized_army['results']
            army_present = sum(1 for comp in army_components if comp in army_results)
        else:
            army_present = 0

        completeness['specialized_army'] = {
            'expected': len(army_components),
            'present': army_present,
            'percentage': (army_present / len(army_components)) * 100,
            'missing': [comp for comp in army_components if comp not in (specialized_army.get('results', {}) if isinstance(specialized_army, dict) else {})]
        }
        
        # Check extended analysis
        extended_analysis = pipeline_result.get('extended_analysis', {})
        extended_components = ['fvg_analysis', 'mean_reversion_analysis', 'pivot_point_analysis', 
                              'signal_convergence', 'math_validation', 'signal_quality']
        extended_present = sum(1 for comp in extended_components if comp in extended_analysis)
        completeness['extended_analysis'] = {
            'expected': len(extended_components),
            'present': extended_present,
            'percentage': (extended_present / len(extended_components)) * 100,
            'missing': [comp for comp in extended_components if comp not in extended_analysis]
        }
        
        # Calculate overall score
        total_expected = (len(core_components) + len(army_components) + len(extended_components))
        total_present = core_present + army_present + extended_present
        completeness['overall_score'] = (total_present / total_expected) * 100
        
        return completeness
    
    def _analyze_data_quality(self, pipeline_result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze quality of data integration"""
        
        quality = {
            'agent_zero_decision_quality': {},
            'ensemble_intelligence_quality': {},
            'data_consistency': {},
            'error_analysis': {}
        }
        
        # Analyze Agent Zero decision quality
        agent_zero_intel = pipeline_result.get('agent_zero_intelligence', {})
        if agent_zero_intel:
            quality['agent_zero_decision_quality'] = {
                'has_action': 'action' in agent_zero_intel,
                'has_confidence': 'confidence' in agent_zero_intel,
                'has_reasoning': 'reasoning' in agent_zero_intel,
                'confidence_value': agent_zero_intel.get('confidence', 0),
                'decision_method': agent_zero_intel.get('decision_method', 'unknown'),
                'ml_available': agent_zero_intel.get('ml_available', False)
            }
        
        # Analyze ensemble intelligence quality
        ensemble_intel = pipeline_result.get('ensemble_intelligence', {})
        if ensemble_intel:
            quality['ensemble_intelligence_quality'] = {
                'has_final_decision': 'final_decision' in ensemble_intel,
                'has_confidence': 'confidence' in ensemble_intel,
                'has_ensemble_score': 'ensemble_score' in ensemble_intel,
                'confidence_value': ensemble_intel.get('confidence', 0),
                'ensemble_score': ensemble_intel.get('ensemble_score', 0),
                'intelligence_sources_count': len(ensemble_intel.get('intelligence_sources', []))
            }
        
        # Check for errors in components
        errors_found = []
        for key, value in pipeline_result.items():
            if isinstance(value, dict) and 'error' in value:
                errors_found.append({
                    'component': key,
                    'error': value['error']
                })
        
        quality['error_analysis'] = {
            'total_errors': len(errors_found),
            'error_details': errors_found
        }
        
        return quality
    
    def _generate_recommendations(self, pipeline_result: Dict[str, Any]) -> List[str]:
        """Generate recommendations for improving data integration"""
        
        recommendations = []
        
        # Check completeness
        completeness = self._analyze_data_completeness(pipeline_result)
        if completeness['overall_score'] < 90:
            recommendations.append(f"Data completeness is {completeness['overall_score']:.1f}% - investigate missing components")
        
        # Check for missing extended analysis
        if completeness['extended_analysis']['percentage'] < 100:
            missing = completeness['extended_analysis']['missing']
            recommendations.append(f"Missing extended analysis components: {', '.join(missing)}")
        
        # Check Agent Zero data reception
        agent_zero_data = self.verification_results.get('agent_zero_data', {})
        if not agent_zero_data.get('extended_analysis_available'):
            recommendations.append("Agent Zero is not receiving extended analysis data - update market context")
        
        # Check data sources coverage
        data_sources = agent_zero_data.get('data_sources_info', {})
        if data_sources.get('coverage_percentage', 0) < 80:
            recommendations.append(f"Agent Zero coverage is {data_sources.get('coverage_percentage', 0):.1f}% - integrate more agents")
        
        # Check for errors
        quality = self._analyze_data_quality(pipeline_result)
        if quality['error_analysis']['total_errors'] > 0:
            recommendations.append(f"Found {quality['error_analysis']['total_errors']} errors - investigate component failures")
        
        if not recommendations:
            recommendations.append("✓ Agent Zero data integration is functioning optimally")
        
        return recommendations
    
    def generate_report(self) -> str:
        """Generate comprehensive verification report"""
        
        if not self.verification_results:
            return "No verification results available. Run verify_data_integration() first."
        
        report = []
        report.append("AGENT ZERO DATA INTEGRATION VERIFICATION REPORT")
        report.append("=" * 60)
        report.append(f"Timestamp: {self.verification_results.get('verification_timestamp', 'Unknown')}")
        report.append(f"Test Ticker: {self.test_ticker}")
        report.append("")
        
        # Pipeline execution status
        pipeline_status = self.verification_results.get('pipeline_execution', {})
        report.append(f"Pipeline Execution: {pipeline_status.get('status', 'Unknown')}")
        if pipeline_status.get('status') == 'FAILED':
            report.append(f"Error: {pipeline_status.get('error', 'Unknown error')}")
            return "\n".join(report)
        
        # Data reception analysis
        agent_zero_data = self.verification_results.get('agent_zero_data', {})
        report.append("")
        report.append("AGENT ZERO DATA RECEPTION:")
        report.append(f"  Agent Zero Available: {agent_zero_data.get('agent_zero_available', False)}")
        report.append(f"  Ensemble Available: {agent_zero_data.get('ensemble_available', False)}")
        report.append(f"  Market Context Available: {agent_zero_data.get('market_context_available', False)}")
        report.append(f"  Extended Analysis Available: {agent_zero_data.get('extended_analysis_available', False)}")
        
        # Data sources coverage
        data_sources = agent_zero_data.get('data_sources_info', {})
        if data_sources:
            report.append(f"  Data Sources Coverage: {data_sources.get('coverage_percentage', 0):.1f}%")
            report.append(f"  Sources Integrated: {data_sources.get('total_sources', 0)}/{data_sources.get('expected_sources', 27)}")
        
        # Completeness analysis
        completeness = self.verification_results.get('completeness_analysis', {})
        report.append("")
        report.append("DATA COMPLETENESS ANALYSIS:")
        report.append(f"  Overall Score: {completeness.get('overall_score', 0):.1f}%")
        
        for component, data in completeness.items():
            if isinstance(data, dict) and 'percentage' in data:
                report.append(f"  {component.replace('_', ' ').title()}: {data['percentage']:.1f}%")
                if data.get('missing'):
                    report.append(f"    Missing: {', '.join(data['missing'])}")
        
        # Quality analysis
        quality = self.verification_results.get('quality_analysis', {})
        error_count = quality.get('error_analysis', {}).get('total_errors', 0)
        report.append("")
        report.append("DATA QUALITY ANALYSIS:")
        report.append(f"  Errors Found: {error_count}")
        
        # Recommendations
        recommendations = self.verification_results.get('recommendations', [])
        report.append("")
        report.append("RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            report.append(f"  {i}. {rec}")
        
        return "\n".join(report)

def main():
    """Main execution function"""
    verifier = AgentZeroDataVerifier()
    
    print("Agent Zero Data Integration Verification")
    print("=" * 50)
    
    # Run verification
    results = verifier.verify_data_integration()
    
    # Generate and display report
    report = verifier.generate_report()
    print(report)
    
    # Save results
    output_file = f"agent_zero_verification_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\nDetailed results saved to: {output_file}")

if __name__ == "__main__":
    main()
