"""
Optimize Liquidity Models Script

This script demonstrates how to use the hyperparameter optimization module
to optimize the hyperparameters of liquidity prediction models.
"""

import os
import sys
import logging
import argparse
import time
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error

# Add parent directory to path to allow imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# Import ML components
from ml.hyperparameter_optimization import HyperparameterOptimizer
from ml.generate_training_data import TrainingDataGenerator
from ml.liquidity_features import LiquidityFeatureExtractor
from ml.ml_logging import get_logger

# Configure logging
logger = get_logger('optimize_liquidity_models')

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimize liquidity models')
    
    parser.add_argument('--model-type', type=str, default='level_strength',
                        choices=['level_strength', 'price_reaction', 'anomaly_detection'],
                        help='Type of model to optimize')
    
    parser.add_argument('--search-strategy', type=str, default='bayesian',
                        choices=['grid', 'random', 'bayesian', 'evolutionary'],
                        help='Search strategy for hyperparameter optimization')
    
    parser.add_argument('--n-trials', type=int, default=50,
                        help='Number of trials for optimization')
    
    parser.add_argument('--output-dir', type=str, default='models',
                        help='Directory to save optimized models')
    
    parser.add_argument('--n-samples', type=int, default=100,
                        help='Number of training samples to generate')
    
    parser.add_argument('--test-size', type=float, default=0.2,
                        help='Fraction of data to use for testing')
    
    parser.add_argument('--random-state', type=int, default=42,
                        help='Random state for reproducibility')
    
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output')
    
    return parser.parse_args()

def generate_training_data(n_samples: int, random_state: int) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Generate synthetic training data for model optimization.
    
    Args:
        n_samples: Number of samples to generate
        random_state: Random state for reproducibility
        
    Returns:
        Tuple of (features, targets)
    """
    logger.info(f"Generating {n_samples} training samples")
    
    # Create data generator
    data_generator = TrainingDataGenerator(config={'seed': random_state})
    
    # Generate samples
    features_list = []
    targets_list = []
    
    for i in range(n_samples):
        if i % 10 == 0:
            logger.info(f"Generated {i}/{n_samples} samples")
        
        # Generate price data
        price_data = data_generator.generate_price_data(
            ticker=f"SAMPLE_{i}",
            start_date='2023-01-01',
            end_date='2023-12-31',
            timeframe='1d'
        )
        
        # Generate liquidity levels
        liquidity_levels = data_generator.generate_liquidity_levels(price_data)
        
        # Generate options data
        options_data = data_generator.generate_options_data(price_data)
        
        # Generate volume profile
        volume_profile = data_generator.generate_volume_profile(price_data)
        
        # Extract features
        feature_extractor = LiquidityFeatureExtractor()
        features = feature_extractor.extract_features(
            price_data=price_data,
            liquidity_levels=liquidity_levels,
            options_data=options_data,
            volume_profile=volume_profile
        )
        
        # Generate target values
        if features is not None and not features.empty:
            # Add some noise to make it more realistic
            targets = features.mean(axis=1) + np.random.normal(0, 0.1, size=len(features))
            targets = pd.DataFrame(targets, index=features.index, columns=['target'])
            
            features_list.append(features)
            targets_list.append(targets)
    
    # Combine all samples
    all_features = pd.concat(features_list)
    all_targets = pd.concat(targets_list)
    
    logger.info(f"Generated {len(all_features)} total feature rows")
    
    return all_features, all_targets

def get_param_space(model_type: str) -> Dict[str, Any]:
    """
    Get parameter space for the specified model type.
    
    Args:
        model_type: Type of model
        
    Returns:
        Parameter space dictionary
    """
    if model_type == 'level_strength':
        return {
            'n_estimators': (50, 500, 'int'),
            'max_depth': (3, 20, 'int'),
            'min_samples_split': (2, 20, 'int'),
            'min_samples_leaf': (1, 10, 'int'),
            'max_features': ['auto', 'sqrt', 'log2'],
            'bootstrap': [True, False]
        }
    elif model_type == 'price_reaction':
        return {
            'n_estimators': (50, 500, 'int'),
            'learning_rate': (0.01, 0.3, 'float'),
            'max_depth': (3, 10, 'int'),
            'min_samples_split': (2, 20, 'int'),
            'min_samples_leaf': (1, 10, 'int'),
            'subsample': (0.5, 1.0, 'float'),
            'max_features': ['auto', 'sqrt', 'log2']
        }
    elif model_type == 'anomaly_detection':
        return {
            'n_estimators': (50, 200, 'int'),
            'contamination': (0.01, 0.1, 'float'),
            'max_samples': (0.5, 1.0, 'float'),
            'bootstrap': [True, False],
            'max_features': (0.5, 1.0, 'float')
        }
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def get_model_class(model_type: str) -> Any:
    """
    Get model class for the specified model type.
    
    Args:
        model_type: Type of model
        
    Returns:
        Model class
    """
    if model_type == 'level_strength':
        return RandomForestRegressor
    elif model_type == 'price_reaction':
        return GradientBoostingRegressor
    elif model_type == 'anomaly_detection':
        from sklearn.ensemble import IsolationForest
        return IsolationForest
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def plot_optimization_history(history: List[Dict[str, Any]], output_path: str) -> None:
    """
    Plot optimization history.
    
    Args:
        history: Optimization history
        output_path: Path to save the plot
    """
    plt.figure(figsize=(10, 6))
    
    # Extract scores and iterations
    iterations = [entry['iteration'] for entry in history]
    scores = [entry['score'] for entry in history]
    
    # Plot scores
    plt.plot(iterations, scores, 'o-', alpha=0.6)
    
    # Add best score line
    best_score = max(scores)
    plt.axhline(y=best_score, color='r', linestyle='--', alpha=0.3, 
                label=f'Best score: {best_score:.4f}')
    
    # Add labels and title
    plt.xlabel('Iteration')
    plt.ylabel('Score')
    plt.title('Hyperparameter Optimization History')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Save plot
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()

def main():
    """Main function."""
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Generate training data
    features, targets = generate_training_data(args.n_samples, args.random_state)
    
    # Split data into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        features, targets, test_size=args.test_size, random_state=args.random_state
    )
    
    logger.info(f"Training data shape: {X_train.shape}")
    logger.info(f"Test data shape: {X_test.shape}")
    
    # Get parameter space and model class
    param_space = get_param_space(args.model_type)
    model_class = get_model_class(args.model_type)
    
    # Create optimizer
    optimizer = HyperparameterOptimizer(
        search_strategy=args.search_strategy,
        param_space=param_space,
        n_trials=args.n_trials,
        cv_folds=5,
        scoring_metric='neg_mean_squared_error',
        direction='maximize',
        random_state=args.random_state,
        verbose=args.verbose
    )
    
    # Run optimization
    logger.info(f"Starting hyperparameter optimization with {args.search_strategy} strategy")
    start_time = time.time()
    
    optimization_results = optimizer.optimize(
        model_class=model_class,
        X_train=X_train,
        y_train=y_train['target'],
        X_val=X_test,
        y_val=y_test['target']
    )
    
    elapsed_time = time.time() - start_time
    logger.info(f"Optimization completed in {elapsed_time:.2f} seconds")
    
    # Get best model and parameters
    best_model = optimization_results['best_model']
    best_params = optimization_results['best_params']
    best_score = optimization_results['best_score']
    
    logger.info(f"Best parameters: {best_params}")
    logger.info(f"Best score: {best_score:.6f}")
    
    # Evaluate on test set
    y_pred = best_model.predict(X_test)
    mse = mean_squared_error(y_test['target'], y_pred)
    r2 = r2_score(y_test['target'], y_pred)
    mae = mean_absolute_error(y_test['target'], y_pred)
    
    logger.info(f"Test set metrics:")
    logger.info(f"  MSE: {mse:.6f}")
    logger.info(f"  R: {r2:.6f}")
    logger.info(f"  MAE: {mae:.6f}")
    
    # Save model and results
    model_path = os.path.join(args.output_dir, f"{args.model_type}_model.pkl")
    results_path = os.path.join(args.output_dir, f"{args.model_type}_results.json")
    history_plot_path = os.path.join(args.output_dir, f"{args.model_type}_history.png")
    
    # Save model
    import joblib
    joblib.dump(best_model, model_path)
    logger.info(f"Model saved to {model_path}")
    
    # Save results
    results = {
        'model_type': args.model_type,
        'search_strategy': args.search_strategy,
        'n_trials': args.n_trials,
        'best_params': best_params,
        'best_score': best_score,
        'test_metrics': {
            'mse': mse,
            'r2': r2,
            'mae': mae
        },
        'optimization_time': elapsed_time,
        'timestamp': datetime.now().isoformat()
    }
    
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {results_path}")
    
    # Plot optimization history
    plot_optimization_history(optimization_results['optimization_history'], history_plot_path)
    logger.info(f"Optimization history plot saved to {history_plot_path}")

if __name__ == "__main__":
    main()
