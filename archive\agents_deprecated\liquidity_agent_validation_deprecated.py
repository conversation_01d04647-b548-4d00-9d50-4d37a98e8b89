#!/usr/bin/env python3
"""
Liquidity Agent Validation Suite
Pre-onboarding standards compliance check
"""

import sys
import os
import json
import unittest
from datetime import datetime
from typing import Dict, Any

# Add the agents directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from liquidity_agent import LiquidityAgent, LiquidityEvent, LiquidityRecommendation
except ImportError as e:
    print(f"CRITICAL: Cannot import liquidity_agent: {e}")
    sys.exit(1)

class LiquidityAgentValidation(unittest.TestCase):
    """Comprehensive validation of Liquidity Agent"""
    
    def setUp(self):
        """Set up test environment"""
        self.agent = LiquidityAgent()
        self.test_market_data = {
            'current_price': 150.25,
            'volume_profile': {'current': 2500000, 'average': 1200000},
            'price_action': {'high': 151.0, 'low': 149.5},
            'atr': 2.5,
            'trend': 'uptrend',
            'volume_ratio': 2.08
        }
        
        self.test_signal_data = {
            'direction': 'bullish',
            'strength': 0.75,
            'confidence': 0.8
        }
    
    def test_agent_initialization(self):
        """Test agent initialization and configuration"""
        self.assertEqual(self.agent.name, "LiquidityAgent")
        self.assertEqual(self.agent.version, "1.0.0")
        self.assertIsInstance(self.agent.confidence_threshold, float)
        self.assertGreaterEqual(self.agent.confidence_threshold, 0.0)
        self.assertLessEqual(self.agent.confidence_threshold, 1.0)
        
    def test_liquidity_event_detection(self):
        """Test liquidity event detection accuracy"""
        event = self.agent.analyze_liquidity_event('TEST', self.test_market_data, self.test_signal_data)
        
        self.assertIsNotNone(event)
        self.assertIsInstance(event, LiquidityEvent)
        self.assertEqual(event.ticker, 'TEST')
        self.assertIn(event.direction, ['bullish', 'bearish', 'neutral'])
        self.assertIn(event.urgency, ['immediate', 'short_term', 'medium_term'])
        self.assertGreaterEqual(event.confidence, 0.0)
        self.assertLessEqual(event.confidence, 1.0)
        
    def test_liquidity_sweep_detection(self):
        """Test liquidity sweep detection algorithm"""
        sweep_confidence = self.agent._detect_liquidity_sweep(self.test_market_data, self.test_signal_data)
        
        self.assertIsInstance(sweep_confidence, float)
        self.assertGreaterEqual(sweep_confidence, 0.0)
        self.assertLessEqual(sweep_confidence, 1.0)
        
    def test_volume_absorption_analysis(self):
        """Test volume absorption pattern analysis"""
        absorption_score = self.agent._analyze_volume_absorption(self.test_market_data['volume_profile'])
        
        self.assertIsInstance(absorption_score, float)
        self.assertGreaterEqual(absorption_score, 0.0)
        self.assertLessEqual(absorption_score, 1.0)
        
    def test_smart_money_flow_detection(self):
        """Test smart money flow detection"""
        flow_direction = self.agent._detect_smart_money_flow(self.test_market_data, self.test_signal_data)
        
        self.assertIsInstance(flow_direction, float)
        self.assertGreaterEqual(flow_direction, -1.0)
        self.assertLessEqual(flow_direction, 1.0)
        
    def test_recommendation_generation(self):
        """Test trading recommendation generation"""
        event = self.agent.analyze_liquidity_event('TEST', self.test_market_data, self.test_signal_data)
        if event:
            recommendation = self.agent.generate_recommendation(event, self.test_market_data)
            
            self.assertIsInstance(recommendation, LiquidityRecommendation)
            self.assertIn(recommendation.action, ['buy', 'sell', 'hold', 'avoid'])
            self.assertGreaterEqual(recommendation.confidence, 0.0)
            self.assertLessEqual(recommendation.confidence, 1.0)
            self.assertGreaterEqual(recommendation.position_size, 0.0)
            self.assertLessEqual(recommendation.position_size, 0.02)  # Max 2% risk
            
    def test_signal_processing(self):
        """Test main signal processing function"""
        result = self.agent.process_signal('TEST', self.test_market_data, self.test_signal_data)
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['agent'], 'LiquidityAgent')
        self.assertEqual(result['ticker'], 'TEST')
        self.assertIn('timestamp', result)
        self.assertIn('recommendation', result)
        
        recommendation = result['recommendation']
        self.assertIn('action', recommendation)
        self.assertIn('confidence', recommendation)
        self.assertIn('reasoning', recommendation)
        
    def test_error_handling(self):
        """Test error handling for invalid inputs"""
        # Test with empty market data
        result = self.agent.process_signal('TEST', {}, {})
        self.assertIsInstance(result, dict)
        self.assertEqual(result['recommendation']['action'], 'hold')
        
        # Test with invalid data types
        result = self.agent.process_signal('TEST', None, None)
        self.assertIsInstance(result, dict)
        
    def test_performance_tracking(self):
        """Test performance tracking functionality"""
        initial_trades = self.agent.performance_stats['total_trades']
        
        # Process a signal to trigger logging
        self.agent.process_signal('TEST', self.test_market_data, self.test_signal_data)
        
        self.assertGreater(self.agent.performance_stats['total_trades'], initial_trades)
        
        # Test performance summary
        summary = self.agent.get_performance_summary()
        self.assertIsInstance(summary, dict)
        self.assertIn('agent', summary)
        self.assertIn('total_trades', summary)
        self.assertIn('win_rate', summary)
        
    def test_data_validation(self):
        """Test input data validation"""
        # Test valid data formats
        valid_market_data = {
            'current_price': 100.0,
            'volume_profile': {'current': 1000, 'average': 500},
            'atr': 2.0
        }
        
        valid_signal_data = {
            'direction': 'bullish',
            'strength': 0.8,
            'confidence': 0.75
        }
        
        result = self.agent.process_signal('VALID', valid_market_data, valid_signal_data)
        self.assertIsInstance(result, dict)
        self.assertNotIn('error', result)

class PerformanceTest:
    """Performance testing for agent operations"""
    
    def __init__(self):
        self.agent = LiquidityAgent()
        
    def test_processing_speed(self, iterations=1000):
        """Test processing speed under load"""
        import time
        
        start_time = time.time()
        
        for i in range(iterations):
            market_data = {
                'current_price': 100 + i * 0.01,
                'volume_profile': {'current': 1000 + i, 'average': 500},
                'atr': 2.0,
                'volume_ratio': 1.5
            }
            
            signal_data = {
                'direction': 'bullish' if i % 2 == 0 else 'bearish',
                'strength': 0.5 + (i % 50) / 100,
                'confidence': 0.6 + (i % 40) / 100
            }
            
            self.agent.process_signal(f'TEST_{i}', market_data, signal_data)
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = total_time / iterations
        
        print(f"Performance Test Results:")
        print(f"Total iterations: {iterations}")
        print(f"Total time: {total_time:.4f} seconds")
        print(f"Average time per signal: {avg_time:.6f} seconds")
        print(f"Signals per second: {1/avg_time:.2f}")
        
        return {
            'total_time': total_time,
            'avg_time': avg_time,
            'signals_per_second': 1/avg_time
        }

def run_validation():
    """Run complete validation suite"""
    print("Starting Liquidity Agent Validation...")
    print("=" * 50)
    
    # Run unit tests
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Run performance tests
    print("\n" + "=" * 50)
    print("Running Performance Tests...")
    
    perf_test = PerformanceTest()
    perf_results = perf_test.test_processing_speed(100)
    
    # Validate performance requirements
    if perf_results['signals_per_second'] >= 50:
        print("PASS: Performance meets requirements (>50 signals/sec)")
    else:
        print("FAIL: Performance below requirements (<50 signals/sec)")
    
    print("\nValidation Complete.")

if __name__ == "__main__":
    run_validation()
