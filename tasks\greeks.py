#!/usr/bin/env python3
"""
Greeks Calculation Module
Real-time calculation of options Greeks using Black-Scholes-Merton model
"""

import numpy as np
import pandas as pd
from scipy.stats import norm
from typing import Dict


def d1(S, K, r, q, sigma, T):
    """Calculate d1 parameter for BSM model"""
    return (np.log(S/K) + (r - q + 0.5*sigma**2)*T) / (sigma*np.sqrt(T))


def d2(d1_val, sigma, T):
    """Calculate d2 parameter for BSM model"""
    return d1_val - sigma*np.sqrt(T)


def vanna(S, K, r, q, sigma, T, cp_flag=1):
    """
    Calculate Vanna (V/S)
    Second-order Greek measuring sensitivity of delta to volatility changes
    
    Args:
        S: Underlying price
        K: Strike price
        r: Risk-free rate
        q: Dividend yield
        sigma: Implied volatility
        T: Time to expiration (years)
        cp_flag: +1 for calls, -1 for puts
    
    Returns:
        Vanna value
    """
    d1_val = d1(S, K, r, q, sigma, T)
    d2_val = d2(d1_val, sigma, T)
    
    return -np.exp(-q*T) * d2_val * norm.pdf(d1_val) / sigma * cp_flag


def charm(S, K, r, q, sigma, T, cp_flag=1):
    """
    Calculate Charm (/T)
    Rate of change of delta with respect to time (delta decay)
    
    Args:
        S: Underlying price
        K: Strike price
        r: Risk-free rate
        q: Dividend yield
        sigma: Implied volatility
        T: Time to expiration (years)
        cp_flag: +1 for calls, -1 for puts
    
    Returns:
        Charm value
    """
    d1_val = d1(S, K, r, q, sigma, T)
    d2_val = d2(d1_val, sigma, T)
    
    term1 = q * np.exp(-q*T) * norm.cdf(cp_flag * d1_val) * cp_flag
    term2 = (np.exp(-q*T) * norm.pdf(d1_val) * 
             (2*(r - q)*T - d2_val*sigma*np.sqrt(T))) / (2*T*sigma*np.sqrt(T))
    
    return term1 - term2


def gamma(S, K, r, q, sigma, T):
    """
    Calculate Gamma (V/S)
    Second derivative of option value with respect to underlying price
    
    Args:
        S: Underlying price
        K: Strike price
        r: Risk-free rate
        q: Dividend yield
        sigma: Implied volatility
        T: Time to expiration (years)
    
    Returns:
        Gamma value
    """
    d1_val = d1(S, K, r, q, sigma, T)
    
    return np.exp(-q*T) * norm.pdf(d1_val) / (S * sigma * np.sqrt(T))


def theta(S, K, r, q, sigma, T, cp_flag=1):
    """
    Calculate Theta (V/T)
    Rate of change of option value with respect to time
    
    Args:
        S: Underlying price
        K: Strike price
        r: Risk-free rate
        q: Dividend yield
        sigma: Implied volatility
        T: Time to expiration (years)
        cp_flag: +1 for calls, -1 for puts
    
    Returns:
        Theta value
    """
    d1_val = d1(S, K, r, q, sigma, T)
    d2_val = d2(d1_val, sigma, T)
    
    term1 = -(S * np.exp(-q*T) * norm.pdf(d1_val) * sigma) / (2 * np.sqrt(T))
    term2 = cp_flag * r * K * np.exp(-r*T) * norm.cdf(cp_flag * d2_val)
    term3 = cp_flag * q * S * np.exp(-q*T) * norm.cdf(cp_flag * d1_val)
    
    return term1 - term2 + term3


def delta(S, K, r, q, sigma, T, cp_flag=1):
    """
    Calculate Delta (V/S)
    First derivative of option value with respect to underlying price
    
    Args:
        S: Underlying price
        K: Strike price
        r: Risk-free rate
        q: Dividend yield
        sigma: Implied volatility
        T: Time to expiration (years)
        cp_flag: +1 for calls, -1 for puts
    
    Returns:
        Delta value
    """
    d1_val = d1(S, K, r, q, sigma, T)
    
    return cp_flag * np.exp(-q*T) * norm.cdf(cp_flag * d1_val)


def validate_greeks(S, K, r, q, sigma, T):
    """
    Validate Greek calculations with sanity checks
    
    Returns:
        dict: Validation results
    """
    try:
        cp_flag = 1  # Call option for validation
        
        delta_val = delta(S, K, r, q, sigma, T, cp_flag)
        gamma_val = gamma(S, K, r, q, sigma, T)
        theta_val = theta(S, K, r, q, sigma, T, cp_flag)
        vanna_val = vanna(S, K, r, q, sigma, T, cp_flag)
        charm_val = charm(S, K, r, q, sigma, T, cp_flag)
        
        # Basic sanity checks
        validation_results = {
            'delta_range': 0 <= delta_val <= 1,  # Call delta should be [0,1]
            'gamma_positive': gamma_val >= 0,    # Gamma always positive
            'theta_negative': theta_val <= 0,    # Call theta usually negative
            'vanna_finite': np.isfinite(vanna_val),
            'charm_finite': np.isfinite(charm_val),
            'all_finite': all(np.isfinite([delta_val, gamma_val, theta_val, vanna_val, charm_val]))
        }
        
        return validation_results
        
    except Exception as e:
        return {'error': str(e), 'all_finite': False}


if __name__ == "__main__":
    # Test calculations
    S, K, r, q, sigma, T = 200, 200, 0.015, 0.00, 0.25, 30/365
    
    print("Greek Calculations Test:")
    print(f"Underlying: ${S}, Strike: ${K}")
    print(f"Rate: {r:.3f}, Div Yield: {q:.3f}")
    print(f"IV: {sigma:.3f}, DTE: {T*365:.0f} days")
    print("-" * 40)
    
    print(f"Delta:  {delta(S, K, r, q, sigma, T):.6f}")
    print(f"Gamma:  {gamma(S, K, r, q, sigma, T):.6f}")
    print(f"Theta:  {theta(S, K, r, q, sigma, T):.6f}")
    print(f"Vanna:  {vanna(S, K, r, q, sigma, T):.6f}")
    print(f"Charm:  {charm(S, K, r, q, sigma, T):.6f}")
    
    # Validation
    validation = validate_greeks(S, K, r, q, sigma, T)
    print(f"\nValidation: {validation}")


class GreeksCalculator:
    """Black-Scholes-Merton Greeks Calculator"""
    
    def __init__(self):
        self.available = True
    
    def calculate_greeks(self, S, K, r, q, sigma, T, cp_flag):
        """
        Calculate all Greeks for given parameters
        
        Args:
            S: Stock price
            K: Strike price
            r: Risk-free rate
            q: Dividend yield
            sigma: Volatility
            T: Time to expiration
            cp_flag: 1 for call, -1 for put
        """
        try:
            d1_val = d1(S, K, r, q, sigma, T)
            d2_val = d2(d1_val, sigma, T)
            
            results = {
                'delta': delta(S, K, r, q, sigma, T, cp_flag),
                'gamma': gamma(S, K, r, q, sigma, T),
                'theta': theta(S, K, r, q, sigma, T, cp_flag),
                'vega': vega(S, K, r, q, sigma, T),
                'rho': rho(S, K, r, q, sigma, T, cp_flag)
            }
            
            return results
            
        except Exception as e:
            return {'error': str(e)}
    
    def bulk_calculate(self, options_data):
        """Calculate Greeks for multiple options"""
        results = []
        
        for option in options_data:
            greeks = self.calculate_greeks(
                option['S'], option['K'], option['r'], 
                option['q'], option['sigma'], option['T'], 
                option['cp_flag']
            )
            
            option.update(greeks)
            results.append(option)
        
        return results
    
    def calculate_synthetic_greeks(self, ticker: str) -> Dict:
        """Calculate synthetic Greeks for a ticker using synthetic option chain"""
        try:
            # Use default market parameters for synthetic calculation
            S = 100.0  # Default stock price
            r = 0.05   # 5% risk-free rate
            q = 0.01   # 1% dividend yield
            sigma = 0.25  # 25% implied volatility
            
            # Generate synthetic option chain - ATM and nearby strikes
            strikes = [S * mult for mult in [0.95, 0.97, 1.0, 1.03, 1.05]]
            expirations = [30, 60, 90]  # Days to expiration
            
            synthetic_greeks = {
                'calls': {},
                'puts': {},
                'summary': {}
            }
            
            total_delta = 0
            total_gamma = 0
            total_theta = 0
            total_vega = 0
            
            for expiry_days in expirations:
                T = expiry_days / 365.0
                
                for K in strikes:
                    # Calculate call Greeks
                    call_greeks = self.calculate_greeks(S, K, r, q, sigma, T, 1)
                    # Calculate put Greeks  
                    put_greeks = self.calculate_greeks(S, K, r, q, sigma, T, -1)
                    
                    key = f"{expiry_days}d_{K:.1f}"
                    synthetic_greeks['calls'][key] = call_greeks
                    synthetic_greeks['puts'][key] = put_greeks
                    
                    # Accumulate for summary
                    if 'error' not in call_greeks:
                        total_delta += call_greeks.get('delta', 0)
                        total_gamma += call_greeks.get('gamma', 0)
                        total_theta += call_greeks.get('theta', 0)
                        total_vega += call_greeks.get('vega', 0)
            
            # Summary statistics
            synthetic_greeks['summary'] = {
                'total_delta': total_delta,
                'total_gamma': total_gamma,
                'total_theta': total_theta,
                'total_vega': total_vega,
                'ticker': ticker,
                'calculation_method': 'synthetic_bsm',
                'timestamp': str(pd.Timestamp.now())
            }
            
            return synthetic_greeks
            
        except Exception as e:
            return {'error': str(e)}
    
    def calculate_chain_greeks(self, ticker: str, option_chain: Dict) -> Dict:
        """Calculate Greeks for provided option chain"""
        try:
            if not option_chain:
                return self.calculate_synthetic_greeks(ticker)
                
            chain_greeks = {
                'calls': {},
                'puts': {},
                'summary': {}
            }
            
            # Process provided option chain
            for option_type in ['calls', 'puts']:
                if option_type in option_chain:
                    cp_flag = 1 if option_type == 'calls' else -1
                    
                    for option in option_chain[option_type]:
                        try:
                            greeks = self.calculate_greeks(
                                option.get('underlying_price', 100),
                                option.get('strike', 100),
                                option.get('risk_free_rate', 0.05),
                                option.get('dividend_yield', 0.01),
                                option.get('implied_volatility', 0.25),
                                option.get('time_to_expiry', 30/365),
                                cp_flag
                            )
                            
                            option_key = f"{option.get('expiration', 'unknown')}_{option.get('strike', 0)}"
                            chain_greeks[option_type][option_key] = greeks
                            
                        except Exception as e:
                            continue
            
            # Calculate summary
            chain_greeks['summary'] = {
                'ticker': ticker,
                'calculation_method': 'chain_bsm',
                'timestamp': str(pd.Timestamp.now()),
                'options_processed': len(chain_greeks.get('calls', {})) + len(chain_greeks.get('puts', {}))
            }
            
            return chain_greeks
            
        except Exception as e:
            return {'error': str(e)}
