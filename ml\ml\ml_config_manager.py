"""
ML Configuration Manager

This module manages the configuration for ML components.
"""

import os
import json
import logging
from typing import Dict, Any, Optional

# Configure logging
logger = logging.getLogger("ml.ml_config_manager")

# Global configuration
_global_config = {}

# Singleton instance
_config_manager_instance = None

class ConfigManager:
    """Configuration manager for ML components."""
    
    def __init__(self, config_path: str = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_path = config_path or os.path.join("src", "ml", "config", "ml_config.json")
        self.config = {}
        
        # Load configuration
        self.load_config()
        
        logger.info("Configuration manager initialized")
    
    def load_config(self) -> bool:
        """
        Load configuration from file.
        
        Returns:
            True if successful, False otherwise
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        
        # Check if file exists
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    self.config = json.load(f)
                
                logger.info("Loaded configuration from file")
                return True
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                return False
        else:
            logger.info("Configuration file not found, using defaults")
            self.config = {}
            return False
    
    def save_config(self) -> bool:
        """
        Save configuration to file.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=4)
            
            logger.info("Saved configuration to file")
            return True
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
    
    def get_config(self) -> Dict:
        """
        Get the current configuration.
        
        Returns:
            Current configuration
        """
        return self.config
    
    def update_config(self, config_updates: Dict) -> bool:
        """
        Update the configuration.
        
        Args:
            config_updates: Dictionary with configuration updates
            
        Returns:
            True if successful, False otherwise
        """
        # Deep update
        self._deep_update(self.config, config_updates)
        
        # Save configuration
        return self.save_config()
    
    def _deep_update(self, d: Dict, u: Dict) -> Dict:
        """
        Deep update a dictionary.
        
        Args:
            d: Dictionary to update
            u: Dictionary with updates
            
        Returns:
            Updated dictionary
        """
        for k, v in u.items():
            if isinstance(v, dict) and k in d and isinstance(d[k], dict):
                self._deep_update(d[k], v)
            else:
                d[k] = v
        return d
    
    def get_value(self, key_path: str, default: Any = None) -> Any:
        """
        Get a value from the configuration.
        
        Args:
            key_path: Path to the key (e.g. 'components.alert_system.enabled')
            default: Default value to return if key doesn't exist
            
        Returns:
            Value from configuration or default
        """
        # Split key path
        keys = key_path.split('.')
        
        # Start with the root configuration
        value = self.config
        
        # Traverse the configuration
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        
        return value
    
    def set_value(self, key_path: str, value: Any) -> bool:
        """
        Set a value in the configuration.
        
        Args:
            key_path: Path to the key (e.g. 'components.alert_system.enabled')
            value: Value to set
            
        Returns:
            True if successful, False otherwise
        """
        # Split key path
        keys = key_path.split('.')
        
        # Start with the root configuration
        config = self.config
        
        # Traverse the configuration
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            elif not isinstance(config[key], dict):
                config[key] = {}
            
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
        
        # Save configuration
        return self.save_config()

def get_config_manager() -> ConfigManager:
    """
    Get the singleton instance of the configuration manager.
    
    Returns:
        ConfigManager instance
    """
    global _config_manager_instance
    
    if _config_manager_instance is None:
        _config_manager_instance = ConfigManager()
    
    return _config_manager_instance

def get_global_config() -> Dict:
    """
    Get the global configuration.
    
    Returns:
        Global configuration
    """
    global _global_config
    
    if not _global_config:
        # Load from configuration manager
        config_manager = get_config_manager()
        _global_config = config_manager.get_config()
        logger.info("Loaded global configuration")
    
    return _global_config

def get_component_config(component_name: str) -> Dict[str, Any]:
    """
    Get configuration for a specific component.
    
    Args:
        component_name: Name of the component
        
    Returns:
        Component configuration or empty dict if not found
    """
    global_config = get_global_config()
    
    # Check if component configuration exists
    components = global_config.get('components', {})
    component_config = components.get(component_name, {})
    
    logger.debug(f"Retrieved configuration for component {component_name}")
    
    return component_config

def update_global_config(config_updates: Dict) -> bool:
    """
    Update the global configuration.
    
    Args:
        config_updates: Dictionary with configuration updates
        
    Returns:
        True if successful, False otherwise
    """
    # Update global configuration
    global _global_config
    
    # Get configuration manager
    config_manager = get_config_manager()
    
    # Update configuration
    success = config_manager.update_config(config_updates)
    
    if success:
        # Update global configuration
        _global_config = config_manager.get_config()
        logger.info("Updated global configuration")
    
    return success
