#!/usr/bin/env python3
"""
CORE Flow Detection System - Mathematical Constants & Thresholds

Enhanced constants file with CSID optimization and swing trading parameters.
All mathematical values validated for institutional intelligence.
"""

# FLOW PHYSICS CONSTANTS - ENHANCED WITH CSID OPTIMIZATION 
FLOW_PHYSICS = {
    'MIN_VELOCITY_THRESHOLD': 0.1,
    'MAX_VELOCITY_THRESHOLD': 2.0,
    'ACCELERATION_STABILITY_THRESHOLD': 0.05,
    'JERK_NOISE_THRESHOLD': 0.1,
    'STEADY_FLOW_VARIANCE_LIMIT': 0.02,
    'INSTITUTIONAL_VELOCITY_THRESHOLD': 0.3,
    'REGIME_CHANGE_THRESHOLD': 0.5,
    
    # CSID OPTIMIZATION PARAMETERS (Research-Based) 
    'CSID_SNR_MINIMUM': 3.0,              # High signal-to-noise patterns
    'CSID_Z_SCORE_FILTER': 2.0,           # 95% statistical confidence
    'CSID_PERCENTILE_THRESHOLD': 0.95,    # Top 5% pattern events
    'CSID_DECAY_CONSTANT_INTRADAY': 0.125, # 15-period EWMA
    'CSID_DECAY_CONSTANT_DAILY': 0.05,    # Longer signal decay
    'CSID_DELTA_CORRELATION_MIN': 0.4,    # Minimum delta correlation
    'CSID_VEGA_CORRELATION_MIN': 0.5,     # Minimum vega correlation
    'CSID_THETA_RISK_THRESHOLD': 0.1,     # Theta exposure limit
    'CSID_REGIME_ALPHA_THRESHOLD': 0.15,  # Minimum alpha for regime
    'CSID_OVERRIDE_Z_THRESHOLD': 2.0,     # Override z-score requirement
    'CSID_MAX_CROSS_CORRELATION': 0.3,    # Anti-crowding measure
    'CSID_MIN_INFORMATION_COEFFICIENT': 0.05  # Minimum IC for inclusion
}

# SWING TRADING CONSTANTS - OPTIONS OPTIMIZED 
SWING_TRADING = {
    'IMBALANCE_RATIO_MIN': 2.0,          # bid > 2x ask for calls
    'DECAY_CONSTANT': 2.5,               # 2-3 day decay for swings
    'Z_SCORE_THRESHOLD': 2.0,            # Entry threshold for calls
    'PERCENTILE_LIQUIDITY': 0.90,        # Top 10% liquidity events
    'VIX_ADAPTIVE_HIGH': 20.0,           # High volatility threshold
    'VIX_THRESHOLD_TIGHT': 2.5,          # Tight threshold for VIX>20
    'DELTA_TARGET_MIN': 0.5,             # ATM call delta minimum
    'DELTA_TARGET_MAX': 0.7,             # ATM call delta maximum
    'THETA_RISK_MAX': 0.1,               # Maximum theta exposure
    'EXPIRY_DAYS_MIN': 30,               # Minimum expiration days
    'EXPIRY_DAYS_MAX': 60,               # Maximum expiration days
    'POSITION_ALLOCATION': 0.075,        # 7.5% capital per trade
    'CONFLUENCE_MINIMUM': 3,             # Minimum factors for signal
    'CONFLUENCE_STRONG': 4               # Strong signal confluence
}

# FACTOR CONFLUENCE SYSTEM - BINARY DECISION LOGIC 
FACTOR_CONFLUENCE = {
    'SIGNAL_STRENGTH_THRESHOLD': 2.0,    # Z-score for strong signals
    'REGIME_VIX_THRESHOLD': 20.0,        # Low volatility threshold
    'GREEKS_DELTA_MIN': 0.5,             # ATM call delta minimum
    'GREEKS_DELTA_MAX': 0.7,             # ATM call delta maximum
    'GREEKS_THETA_MAX': 0.1,             # Maximum theta exposure
    'TIMING_FRESH_HOURS': 24,            # Signal freshness limit
    'UNIQUENESS_CORRELATION_MAX': 0.3,   # Cross-correlation limit
    'CONFLUENCE_FACTORS_COUNT': 5,       # Total factor count
    'MODERATE_CONFLUENCE_MIN': 3,        # Minimum moderate confluence
    'STRONG_CONFLUENCE_MIN': 4           # Minimum strong confluence
}

# VOLUME ANALYSIS CONSTANTS
VOLUME_ANALYSIS = {
    'POC_CALCULATION_BINS': 100,
    'VALUE_AREA_PERCENTAGE': 0.70,
    'HIGH_VOLUME_NODE_THRESHOLD': 1.5,
    'VOLUME_CONCENTRATION_THRESHOLD': 0.8,
    'MIN_VOLUME_FOR_ANALYSIS': 1000,
    'VOLUME_QUALITY_THRESHOLD': 0.7
}

# LIQUIDITY ANALYSIS CONSTANTS
LIQUIDITY_ANALYSIS = {
    'ACCUMULATION_THRESHOLD': 0.65,
    'DISTRIBUTION_THRESHOLD': 0.65,
    'ABSORPTION_EFFICIENCY_THRESHOLD': 0.75,
    'CAMPAIGN_STAGE_CONFIDENCE': 0.6,
    'LEVEL_STRENGTH_THRESHOLD': 0.5,
    'INSTITUTIONAL_BIAS_THRESHOLD': 0.7
}

# GEX ANALYSIS CONSTANTS
GEX_ANALYSIS = {
    'GAMMA_CONCENTRATION_THRESHOLD': 1000000,
    'ZERO_GAMMA_TOLERANCE': 50000,
    'DEALER_POSITIONING_THRESHOLD': 0.6,
    'OPTIONS_MIN_OPEN_INTEREST': 100,
    'STRIKE_RANGE_PERCENTAGE': 0.15,
    'EXPIRATION_DAYS_FILTER': 90
}

# CONFLUENCE ENGINE CONSTANTS - ENHANCED WITH FACTOR LOGIC 
CONFLUENCE_ENGINE = {
    'MIN_AGREEMENT_COUNT': 3,
    'MIN_FACTOR_STRENGTH': 0.4,
    'MIN_QUALITY_THRESHOLD': 0.6,
    'DIRECTION_CONFIDENCE_THRESHOLD': 0.7,
    
    # FACTOR CONFLUENCE ENHANCEMENT 
    'FACTOR_CONFLUENCE_ENABLED': True,
    'BINARY_FACTOR_LOGIC': True,
    'WEIGHTED_DECISIONS_DISABLED': True,  # Pure factor confluence
    'CONFLUENCE_STATEMENT_FORMAT': True
}

# SIGNAL GENERATOR CONSTANTS - ENHANCED FOR SWING TRADING 
SIGNAL_GENERATOR = {
    'MIN_SIGNAL_CONFIDENCE': 0.6,
    'STRONG_SIGNAL_THRESHOLD': 0.75,
    'EXTREME_SIGNAL_THRESHOLD': 0.9,
    'MIN_AGREEMENT_FOR_SIGNAL': 3,
    'RISK_LEVEL_THRESHOLDS': {
        'LOW': 0.8,
        'MEDIUM': 0.6,
        'HIGH': 0.0
    },
    
    # SWING TRADING SIGNAL ENHANCEMENT 
    'SWING_SIGNAL_ENABLED': True,
    'ORDER_BOOK_IMBALANCE_THRESHOLD': 2.0,
    'LIQUIDITY_SWEEP_PERCENTILE': 0.90,
    'DEPTH_SPIKE_DETECTION': True,
    'POST_EVENT_MOMENTUM_DETECTION': True,
    'INSTITUTIONAL_ACCUMULATION_DETECTION': True
}

# DATA QUALITY CONSTANTS
DATA_QUALITY = {
    'MIN_DATA_QUALITY_SCORE': 0.7,
    'MIN_BARS_REQUIRED': 50,
    'MAX_MISSING_DATA_PERCENTAGE': 0.05,
    'OUTLIER_DETECTION_THRESHOLD': 3.0,
    'DATA_FRESHNESS_LIMIT_MINUTES': 30
}

# TIMEFRAME CONSTANTS
TIMEFRAMES = {
    'SUPPORTED_TIMEFRAMES': ['1m', '5m', '15m', '30m', '1h', '4h', '1d'],
    'PRIMARY_TIMEFRAME': '15m',
    'DEFAULT_LOOKBACK_PERIODS': 100,
    'MAX_TIMEFRAME_SPAN_HOURS': 168
}

# ML CONSTANTS - ENHANCED FOR CSID TRAINING 
ML_CONSTANTS = {
    'PATTERN_SIMILARITY_THRESHOLD': 0.75,
    'MIN_TRAINING_SAMPLES': 100,
    'MODEL_REFRESH_THRESHOLD': 0.1,
    'ADAPTIVE_LEARNING_RATE': 0.05,
    'CONFIDENCE_CALIBRATION_THRESHOLD': 0.8,
    
    # CSID ML ENHANCEMENT 
    'CSID_PATTERN_RECOGNITION': True,
    'INSTITUTIONAL_VS_RETAIL_CLASSIFICATION': True,
    'FLOW_REGIME_DETECTION': True,
    'FACTOR_CONFLUENCE_LEARNING': True,
    'SWING_TRADING_OPTIMIZATION': True,
    'GREEKS_CORRELATION_ANALYSIS': True
}

# PERFORMANCE THRESHOLDS - ENHANCED WITH CSID METRICS 
PERFORMANCE = {
    'MAX_RESPONSE_TIME_MS': 200,
    'MIN_SIGNAL_ACCURACY': 0.8,
    'MAX_FALSE_POSITIVE_RATE': 0.1,
    'MIN_TIMING_ACCURACY': 0.7,
    'TARGET_SHARPE_RATIO': 1.5,
    
    # CSID PERFORMANCE TARGETS 
    'FLOW_PHYSICS_EXECUTION_TIME_MS': 1000,
    'CSID_PATTERN_ACCURACY': 0.85,
    'FACTOR_CONFLUENCE_PRECISION': 0.90,
    'SWING_TRADE_WIN_RATE': 0.65,
    'INSTITUTIONAL_DETECTION_ACCURACY': 0.80,
    'SIGNAL_DECAY_PREDICTION_ACCURACY': 0.75
}

# VALIDATION CONSTANTS - ENHANCED WITH CSID VALIDATION 
VALIDATION = {
    'MATHEMATICAL_PRECISION': 1e-10,
    'CALCULATION_TOLERANCE': 1e-8,
    'CHART_ACCURACY_THRESHOLD': 0.99,
    'REPORT_CONSISTENCY_THRESHOLD': 0.95,
    
    # CSID VALIDATION ENHANCEMENT 
    'FLOW_PHYSICS_VALIDATION_ENABLED': True,
    'TYPE_SAFETY_VALIDATION': True,
    'STATISTICAL_SIGNIFICANCE_THRESHOLD': 0.95,  # 95% confidence
    'CORRELATION_VALIDATION_THRESHOLD': 0.5,
    'FACTOR_CONFLUENCE_VALIDATION': True,
    'SWING_TRADING_PARAMETER_VALIDATION': True,
    'UNICODE_COMPATIBILITY_REQUIRED': True,
    'ERROR_FREE_EXECUTION_REQUIRED': True
}

# CSID PATTERN RECOGNITION CONSTANTS 
CSID_PATTERNS = {
    'HIGH_SNR_MOMENTUM_THRESHOLD': 3.0,
    'LIQUIDITY_IMBALANCE_THRESHOLD': 2.0,
    'MEAN_REVERSION_SIGNAL_THRESHOLD': 2.0,
    'VOLATILITY_SPIKE_THRESHOLD': 1.5,
    'REGIME_TRANSITION_THRESHOLD': 0.8,
    'SIGNAL_DECAY_WARNING_THRESHOLD': 0.7,
    'CROSS_CORRELATION_ALERT_THRESHOLD': 0.3,
    'ALPHA_DECAY_RISK_THRESHOLD': 0.6,
    'BID_ASK_IMBALANCE_SWING_THRESHOLD': 2.0,
    'LIQUIDITY_SWEEP_DETECTION_THRESHOLD': 0.9,
    'DEPTH_SPIKE_BREAKOUT_THRESHOLD': 1.5,
    'CONSOLIDATION_RANGE_BREAK_THRESHOLD': 0.85,
    'POST_EVENT_MOMENTUM_THRESHOLD': 1.2,
    'INSTITUTIONAL_ACCUMULATION_THRESHOLD': 0.8,
    'FACTOR_CONFLUENCE_STRONG_THRESHOLD': 4,
    'FACTOR_CONFLUENCE_MODERATE_THRESHOLD': 3
}
