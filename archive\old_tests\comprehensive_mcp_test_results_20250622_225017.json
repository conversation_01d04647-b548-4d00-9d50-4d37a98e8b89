{"test_summary": {"total_tests": 16, "passed_tests": 11, "partial_tests": 1, "failed_tests": 4, "skipped_tests": 0, "effective_tests": 16, "success_rate": 68.8, "total_execution_time": 19.989, "timestamp": "2025-06-22T22:50:17.891361"}, "detailed_results": {"Enhanced Data Agent": {"status": "PASS", "execution_time": 16.987, "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": true, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Signal Generator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Chart Generator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Risk Guard": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Order Router": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Anomaly Detector": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "IV Dynamics": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Flow Physics": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Math Validator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Output Coordinator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Data Ingestion": {"status": "FAIL", "execution_time": "N/A", "has_real_time_capability": false, "has_real_time_agent": false, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": false, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>, MCP: <PERSON>"}, "Schwab Data": {"status": "FAIL", "execution_time": "N/A", "has_real_time_capability": false, "has_real_time_agent": false, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": false, "has_real_time_attribute": false, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>, MCP: <PERSON>"}, "Auto Broker Adapter": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": false, "has_real_time_agent": false, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": false, "has_real_time_attribute": false, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>, MCP: <PERSON><PERSON><PERSON>"}, "Signal Quality": {"status": "PARTIAL", "execution_time": "N/A", "has_real_time_capability": false, "has_real_time_agent": false, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": false, "has_real_time_attribute": false, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>, MCP: <PERSON><PERSON><PERSON>"}, "Agent Zero": {"status": "FAIL", "error": "Agent Zero test failed: module 'agents.agent_zero' has no attribute 'Agent<PERSON><PERSON>'", "traceback": "Traceback (most recent call last):\n  File \"D:\\script-work\\CORE\\test_comprehensive_mcp_integration.py\", line 77, in test_agent_mcp_integration\n    agent_class = getattr(module, class_name)\nAttributeError: module 'agents.agent_zero' has no attribute 'AgentZ<PERSON>'\n", "details": "Agent test execution failed"}, "Order Router V2": {"status": "FAIL", "error": "Order Router V2 test failed: module 'agents.order_router_agent_v2' has no attribute 'OrderRouterAgentV2'", "traceback": "Traceback (most recent call last):\n  File \"D:\\script-work\\CORE\\test_comprehensive_mcp_integration.py\", line 77, in test_agent_mcp_integration\n    agent_class = getattr(module, class_name)\nAttributeError: module 'agents.order_router_agent_v2' has no attribute 'OrderRouterAgentV2'\n", "details": "Agent test execution failed"}}}