"""
Train Liquidity Models

This module provides functionality to train custom models for liquidity analysis,
replacing the fallback models with trained models.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import sys
import logging
import numpy as np
import pandas as pd
import pickle
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Union, Tuple
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import joblib

# Import ML components
from ml_logging import get_logger
from liquidity_features import LiquidityFeatureExtractor

# Setup logger
logger = get_logger('train_liquidity_models')

class LiquidityModelTrainer:
    """
    Trains models for liquidity analysis.
    
    This class provides methods to train models for level strength prediction
    and price reaction prediction using historical data.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the model trainer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.feature_extractor = LiquidityFeatureExtractor(config)
        self.models_dir = self.config.get('models_dir', os.path.join(os.path.dirname(__file__), 'models'))
        
        # Create models directory if it doesn't exist
        os.makedirs(self.models_dir, exist_ok=True)
        
        # Initialize models
        self.level_strength_model = None
        self.price_reaction_model = None
        
        logger.info("Initialized LiquidityModelTrainer")
    
    def prepare_training_data(self,
                             price_data: pd.DataFrame,
                             liquidity_levels: Dict[str, List[Dict[str, Any]]],
                             options_data: Optional[pd.DataFrame] = None,
                             volume_profile: Optional[Dict[str, Any]] = None,
                             gex_data: Optional[Dict[str, Any]] = None) -> Tuple[pd.DataFrame, Dict[str, np.ndarray]]:
        """
        Prepare training data for model training.
        
        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data
            
        Returns:
            Tuple of (features, targets)
        """
        logger.info("Preparing training data")
        
        # Extract features
        features = self.feature_extractor.extract_features(
            price_data=price_data,
            options_data=options_data,
            volume_profile=volume_profile,
            gex_data=gex_data,
            liquidity_levels=liquidity_levels
        )
        
        if features.empty:
            logger.error("Failed to extract features for training")
            return pd.DataFrame(), {}
        
        # Prepare targets
        targets = {}
        
        # Level strength targets
        level_strength_targets = []
        for level_type in ['support', 'resistance']:
            for level in liquidity_levels.get(level_type, []):
                level_strength_targets.append(level.get('strength', 0.5))
        
        if level_strength_targets:
            targets['level_strength'] = np.array(level_strength_targets)
        
        # Price reaction targets
        price_reaction_targets = []
        for level_type in ['support', 'resistance']:
            for level in liquidity_levels.get(level_type, []):
                # For support levels, positive reaction means bounce
                # For resistance levels, negative reaction means rejection
                if level_type == 'support':
                    reaction = level.get('reaction', 0.0)
                else:
                    reaction = -level.get('reaction', 0.0)
                
                price_reaction_targets.append(reaction)
        
        if price_reaction_targets:
            targets['price_reaction'] = np.array(price_reaction_targets)
        
        logger.info(f"Prepared training data with {len(features)} samples")
        return features, targets
    
    def train_level_strength_model(self,
                                  features: pd.DataFrame,
                                  targets: np.ndarray,
                                  test_size: float = 0.2,
                                  random_state: int = 42) -> Dict[str, Any]:
        """
        Train a model for level strength prediction.
        
        Args:
            features: DataFrame with features
            targets: Array of level strength targets
            test_size: Proportion of data to use for testing
            random_state: Random state for reproducibility
            
        Returns:
            Dictionary with training results
        """
        logger.info("Training level strength model")
        
        # Split data into train and test sets
        X_train, X_test, y_train, y_test = train_test_split(
            features, targets, test_size=test_size, random_state=random_state
        )
        
        # Initialize model
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=random_state
        )
        
        # Train model
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Evaluate model
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        
        # Save model
        self.level_strength_model = model
        model_path = os.path.join(self.models_dir, 'level_strength_model.pkl')
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        logger.info(f"Trained and saved level strength model to {model_path}")
        
        # Return training results
        return {
            'model': model,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'mae': mae,
            'training_time': training_time,
            'feature_importance': dict(zip(features.columns, model.feature_importances_))
        }
    
    def train_price_reaction_model(self,
                                  features: pd.DataFrame,
                                  targets: np.ndarray,
                                  test_size: float = 0.2,
                                  random_state: int = 42) -> Dict[str, Any]:
        """
        Train a model for price reaction prediction.
        
        Args:
            features: DataFrame with features
            targets: Array of price reaction targets
            test_size: Proportion of data to use for testing
            random_state: Random state for reproducibility
            
        Returns:
            Dictionary with training results
        """
        logger.info("Training price reaction model")
        
        # Split data into train and test sets
        X_train, X_test, y_train, y_test = train_test_split(
            features, targets, test_size=test_size, random_state=random_state
        )
        
        # Initialize model
        model = GradientBoostingRegressor(
            n_estimators=100,
            max_depth=5,
            learning_rate=0.1,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=random_state
        )
        
        # Train model
        start_time = time.time()
        model.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Evaluate model
        y_pred = model.predict(X_test)
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        r2 = r2_score(y_test, y_pred)
        mae = mean_absolute_error(y_test, y_pred)
        
        # Save model
        self.price_reaction_model = model
        model_path = os.path.join(self.models_dir, 'price_reaction_model.pkl')
        with open(model_path, 'wb') as f:
            pickle.dump(model, f)
        
        logger.info(f"Trained and saved price reaction model to {model_path}")
        
        # Return training results
        return {
            'model': model,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'mae': mae,
            'training_time': training_time,
            'feature_importance': dict(zip(features.columns, model.feature_importances_))
        }
    
    def visualize_feature_importance(self, feature_importance: Dict[str, float], title: str, top_n: int = 20) -> plt.Figure:
        """
        Visualize feature importance.
        
        Args:
            feature_importance: Dictionary mapping feature names to importance scores
            title: Title for the plot
            top_n: Number of top features to show
            
        Returns:
            Matplotlib figure
        """
        # Sort features by importance
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        # Get top N features
        top_features = sorted_features[:top_n]
        
        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # Plot feature importance
        feature_names = [f[0] for f in top_features]
        feature_scores = [f[1] for f in top_features]
        
        y_pos = np.arange(len(feature_names))
        ax.barh(y_pos, feature_scores, align='center')
        ax.set_yticks(y_pos)
        ax.set_yticklabels(feature_names)
        ax.invert_yaxis()  # Labels read top-to-bottom
        ax.set_xlabel('Importance')
        ax.set_title(title)
        
        plt.tight_layout()
        return fig
    
    def train_models(self,
                    price_data: pd.DataFrame,
                    liquidity_levels: Dict[str, List[Dict[str, Any]]],
                    options_data: Optional[pd.DataFrame] = None,
                    volume_profile: Optional[Dict[str, Any]] = None,
                    gex_data: Optional[Dict[str, Any]] = None,
                    visualize: bool = True) -> Dict[str, Any]:
        """
        Train all models for liquidity analysis.
        
        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data
            visualize: Whether to generate visualizations
            
        Returns:
            Dictionary with training results
        """
        # Prepare training data
        features, targets = self.prepare_training_data(
            price_data=price_data,
            liquidity_levels=liquidity_levels,
            options_data=options_data,
            volume_profile=volume_profile,
            gex_data=gex_data
        )
        
        if features.empty or not targets:
            logger.error("Failed to prepare training data")
            return {}
        
        results = {}
        
        # Train level strength model if targets are available
        if 'level_strength' in targets:
            level_strength_results = self.train_level_strength_model(
                features=features,
                targets=targets['level_strength']
            )
            results['level_strength'] = level_strength_results
            
            # Visualize feature importance
            if visualize:
                fig = self.visualize_feature_importance(
                    level_strength_results['feature_importance'],
                    'Level Strength Model - Feature Importance'
                )
                
                # Save visualization
                fig_path = os.path.join(self.models_dir, 'level_strength_feature_importance.png')
                fig.savefig(fig_path, dpi=300, bbox_inches='tight')
                plt.close(fig)
                
                logger.info(f"Saved level strength feature importance visualization to {fig_path}")
        
        # Train price reaction model if targets are available
        if 'price_reaction' in targets:
            price_reaction_results = self.train_price_reaction_model(
                features=features,
                targets=targets['price_reaction']
            )
            results['price_reaction'] = price_reaction_results
            
            # Visualize feature importance
            if visualize:
                fig = self.visualize_feature_importance(
                    price_reaction_results['feature_importance'],
                    'Price Reaction Model - Feature Importance'
                )
                
                # Save visualization
                fig_path = os.path.join(self.models_dir, 'price_reaction_feature_importance.png')
                fig.savefig(fig_path, dpi=300, bbox_inches='tight')
                plt.close(fig)
                
                logger.info(f"Saved price reaction feature importance visualization to {fig_path}")
        
        return results
