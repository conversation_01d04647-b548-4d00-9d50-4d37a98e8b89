#!/usr/bin/env python3
"""
Agent Zero Integration Hub
Central coordination point for all Agent Zero integrations
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path

# Import Agent Zero components
from ..core.decision_processor import AgentZeroCore, DecisionInput, DecisionOutput
from ..ml_integration.ml_layer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MLFeatureExtractor
from ..training_pipeline.data_collector import TrainingDataCollector
from ..advanced_capabilities.enhancement_engine import EnhancementEngine

logger = logging.getLogger(__name__)

class AgentZeroHub:
    """
    Agent Zero Integration Hub
    
    Central coordination point providing:
    - Unified API for all Agent Zero functionality
    - Decision processing pipeline
    - ML integration management
    - Training data collection
    - Advanced capability enhancement
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Initialize core components
        self.core_processor = AgentZeroCore(self.config.get('core', {}))
        self.ml_layer = MLIntegrationLayer(self.config.get('ml', {}))
        self.training_collector = TrainingDataCollector(self.config.get('training', {}))
        self.enhancement_engine = EnhancementEngine(self.config.get('enhancements', {}))
        
        # Hub configuration
        self.enable_ml = self.config.get('enable_ml', True)
        self.enable_enhancements = self.config.get('enable_enhancements', True)
        self.enable_training = self.config.get('enable_training', True)
        
        # Performance tracking
        self.prediction_count = 0
        self.total_processing_time = 0.0
        
        logger.info("Agent Zero Hub initialized")
    
    def predict(self, signal_data: Dict[str, Any], math_data: Dict[str, Any], 
                market_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Main prediction interface
        
        Unified entry point for all Agent Zero decision making
        """
        start_time = datetime.now()
        market_context = market_context or {}
        
        try:
            # Step 1: Core decision processing
            decision_input = self._prepare_decision_input(signal_data, math_data, market_context)
            core_decision = self.core_processor.process_decision(decision_input)
            
            # Step 2: ML enhancement (if enabled)
            if self.enable_ml and self.ml_layer.ml_available:
                ml_features = MLFeatureExtractor.extract_features(signal_data, math_data, market_context)
                ml_prediction = self.ml_layer.predict(ml_features)
                
                # Blend core and ML decisions
                enhanced_decision = self._blend_decisions(core_decision, ml_prediction)
            else:
                enhanced_decision = core_decision
            
            # Step 3: Advanced capability enhancement (if enabled)
            if self.enable_enhancements:
                final_decision = self.enhancement_engine.enhance_decision(
                    enhanced_decision, signal_data, math_data, market_context
                )
            else:
                final_decision = enhanced_decision
            
            # Step 4: Convert to output format
            output = self._format_output(final_decision, start_time)
            
            # Step 5: Collect training data (if enabled)
            if self.enable_training:
                self.training_collector.collect_decision_data(
                    signal_data, math_data, market_context, output
                )
            
            # Update performance tracking
            self.prediction_count += 1
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.total_processing_time += processing_time
            
            return output
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return self._emergency_fallback(str(e))
    
    def _prepare_decision_input(self, signal_data: Dict, math_data: Dict, 
                               market_context: Dict) -> DecisionInput:
        """Prepare structured input for core processor"""
        
        return DecisionInput(
            signal_confidence=signal_data.get('confidence', 0.5),
            signal_strength=signal_data.get('strength', 0.5),
            execution_recommendation=signal_data.get('execution_recommendation', 'hold'),
            math_accuracy=math_data.get('accuracy_score', 0.5),
            math_precision=math_data.get('precision', 0.01),
            market_context=market_context,
            timestamp=datetime.now()
        )
    
    def _blend_decisions(self, core_decision: DecisionOutput, ml_prediction) -> DecisionOutput:
        """Blend core and ML decisions intelligently"""
        
        # Weight decisions based on confidence
        core_weight = 0.6  # Core processor gets more weight by default
        ml_weight = 0.4
        
        # Adjust weights based on ML confidence
        if hasattr(ml_prediction, 'confidence') and ml_prediction.confidence > 0.8:
            core_weight = 0.4
            ml_weight = 0.6
        
        # Blend confidences
        blended_confidence = (
            core_decision.confidence * core_weight + 
            ml_prediction.confidence * ml_weight
        )
        
        # Choose action (prefer ML if high confidence, otherwise core)
        if hasattr(ml_prediction, 'confidence') and ml_prediction.confidence > 0.8:
            blended_action = ml_prediction.action
        else:
            blended_action = core_decision.action
        
        # Create blended decision
        blended_decision = DecisionOutput(
            action=blended_action,
            confidence=min(blended_confidence, 1.0),
            composite_score=core_decision.composite_score,
            reasoning=core_decision.reasoning + [f"ML-enhanced decision (ML confidence: {ml_prediction.confidence:.3f})"],
            execution_time_ms=core_decision.execution_time_ms,
            decision_method="core_ml_blended",
            timestamp=datetime.now()
        )
        
        return blended_decision
    
    def _format_output(self, decision: DecisionOutput, start_time: datetime) -> Dict[str, Any]:
        """Format decision output for external consumption"""
        
        total_time = (datetime.now() - start_time).total_seconds() * 1000
        
        return {
            'timestamp': decision.timestamp.isoformat(),
            'agent_zero_version': '2.0_Hub',
            'action': decision.action,
            'confidence': decision.confidence,
            'reasoning': decision.reasoning,
            'execution_time_ms': total_time,
            'decision_method': decision.decision_method,
            'composite_score': decision.composite_score,
            'ml_enhanced': self.enable_ml and self.ml_layer.ml_available,
            'capabilities_enhanced': self.enable_enhancements,
            'training_collected': self.enable_training
        }
    
    def _emergency_fallback(self, error: str) -> Dict[str, Any]:
        """Emergency fallback when all systems fail"""
        
        return {
            'timestamp': datetime.now().isoformat(),
            'agent_zero_version': '2.0_Hub',
            'action': 'avoid',
            'confidence': 0.0,
            'reasoning': [f"Emergency fallback due to error: {error}"],
            'execution_time_ms': 0.0,
            'decision_method': 'emergency_fallback',
            'error': error
        }
    
    def log_training_data(self, signal_data: Dict, math_data: Dict, decision: Dict, 
                         outcome: float, market_context: Optional[Dict] = None):
        """Log training data for model improvement"""
        
        if self.enable_training:
            self.training_collector.log_outcome(
                signal_data, math_data, decision, outcome, market_context
            )
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        
        avg_processing_time = (
            self.total_processing_time / self.prediction_count 
            if self.prediction_count > 0 else 0.0
        )
        
        return {
            'hub_status': 'operational',
            'prediction_count': self.prediction_count,
            'avg_processing_time_ms': avg_processing_time,
            'ml_available': self.ml_layer.ml_available if self.enable_ml else False,
            'enhancements_enabled': self.enable_enhancements,
            'training_enabled': self.enable_training,
            'core_processor_stats': self.core_processor.get_performance_stats(),
            'ml_model_info': self.ml_layer.get_model_info() if self.enable_ml else None
        }
    
    def update_configuration(self, new_config: Dict[str, Any]):
        """Update hub configuration dynamically"""
        
        self.config.update(new_config)
        
        # Update component configurations
        if 'enable_ml' in new_config:
            self.enable_ml = new_config['enable_ml']
        
        if 'enable_enhancements' in new_config:
            self.enable_enhancements = new_config['enable_enhancements']
        
        if 'enable_training' in new_config:
            self.enable_training = new_config['enable_training']
        
        logger.info("Hub configuration updated")

# Global hub instance for backwards compatibility
_global_hub = None

def get_agent_zero_hub(config: Optional[Dict] = None):
    """Get global Agent Zero hub instance"""
    global _global_hub
    if _global_hub is None:
        _global_hub = AgentZeroHub(config)
    return _global_hub

if __name__ == "__main__":
    # Test hub functionality
    hub = AgentZeroHub()
    
    test_signal = {'confidence': 0.8, 'strength': 0.7, 'execution_recommendation': 'execute'}
    test_math = {'accuracy_score': 0.9, 'precision': 0.001}
    test_context = {'volatility': 0.02, 'trend_strength': 0.6}
    
    decision = hub.predict(test_signal, test_math, test_context)
    status = hub.get_system_status()
    
    print("Agent Zero Hub Test Results:")
    print(f"Decision: {decision['action']}")
    print(f"Confidence: {decision['confidence']:.3f}")
    print(f"Processing time: {decision['execution_time_ms']:.3f}ms")
    print(f"Hub status: {status['hub_status']}")
