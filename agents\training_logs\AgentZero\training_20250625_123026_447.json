{"timestamp": "2025-06-25T12:30:26.447878", "signal_data": {"confidence": 0.855, "strength": 0.782, "execution_recommendation": "execute"}, "math_data": {"accuracy_score": 0.9229999999999999, "precision": 0.001, "validation_score": 0.95}, "decision": {"timestamp": "2025-06-25T12:30:26.447840", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.02, "action": "execute", "confidence": 0.79145, "reasoning": ["Rule-based decision: composite score 0.791", "Signal confidence: 0.855", "Signal strength: 0.782", "Execution recommendation: execute", "Math accuracy: 0.923", "ML system not available - using rule-based logic"], "composite_score": 0.79145, "decision_method": "rule_based", "ml_enhanced": false}, "outcome": 0.85, "market_context": {"risk_level": "LOW", "decision_strength": "STRONG", "participating_agents": 3}, "agent_version": "1.5_Integrated"}