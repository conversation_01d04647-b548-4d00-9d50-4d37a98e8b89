#!/usr/bin/env python3
"""
Command Validator - Validation layer for commands
"""

from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

class ValidationType(Enum):
    PARAMETER = "parameter"
    SYNTAX = "syntax"
    BUSINESS_RULE = "business_rule"
    SECURITY = "security"

@dataclass
class ValidationRule:
    rule_id: str
    rule_type: ValidationType
    description: str
    validator_function: callable

@dataclass
class ValidationResult:
    valid: bool
    errors: List[str]
    warnings: List[str]
    validated_parameters: Dict[str, Any]

class CommandValidator:
    def __init__(self):
        self.rules = {}
    
    def add_rule(self, rule: ValidationRule):
        self.rules[rule.rule_id] = rule
    
    def validate_command(self, command) -> ValidationResult:
        return ValidationResult(
            valid=True,
            errors=[],
            warnings=[],
            validated_parameters=command.parameters
        )
