"""
Feature Imputer Module

This module provides robust imputation for missing values in ML features.
It handles NaN values in a way that preserves the statistical properties
of the data while ensuring that models can still make predictions.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any

# Configure logging
logger = logging.getLogger("Liquidity_Reports.feature_imputer")

class FeatureImputer:
    """
    Robust imputer for handling missing values in ML features.
    
    This class provides multiple strategies for imputing missing values
    based on the type of feature and the amount of available data.
    """
    
    def __init__(self, strategy: str = 'auto'):
        """
        Initialize the FeatureImputer.
        
        Args:
            strategy: Imputation strategy to use
                'auto': Automatically choose the best strategy based on the data
                'mean': Use mean imputation for all features
                'median': Use median imputation for all features
                'zero': Fill all missing values with 0
                'domain': Use domain-specific defaults based on feature names
        """
        self.strategy = strategy
        self.imputation_stats = {}
        self.feature_defaults = {}
        
        # Define domain-specific defaults for different feature types
        self.domain_defaults = {
            'ratio': 0.0,        # Ratios are typically centered around 0
            'correlation': 0.0,  # Correlations are typically between -1 and 1
            'skew': 0.0,         # Skew measures are typically centered around 0
            'distance': 1.0,     # Distances are typically positive
            'strength': 0.5,     # Strengths are typically between 0 and 1
            'score': 0.5,        # Scores are typically between 0 and 1
            'count': 0,          # Counts are non-negative integers
            'volatility': 0.2,   # Volatility is typically positive
            'volume': 1.0,       # Volume-related features are typically positive
            'price': 0.0,        # Price-related features
            'return': 0.0,       # Return-related features
            'momentum': 0.0,     # Momentum-related features
            'oscillator': 0.0,   # Oscillator-related features
            'default': 0.0       # Default for other features
        }
    
    def fit(self, X: pd.DataFrame) -> 'FeatureImputer':
        """
        Fit the imputer to the data.
        
        This calculates statistics for each feature that will be used for imputation.
        
        Args:
            X: DataFrame with features
            
        Returns:
            Self for method chaining
        """
        # Calculate statistics for each feature
        for col in X.columns:
            # Skip columns with no missing values
            if not X[col].isna().any():
                continue
            
            # Calculate statistics
            stats = {
                'mean': X[col].mean(),
                'median': X[col].median(),
                'min': X[col].min(),
                'max': X[col].max(),
                'std': X[col].std(),
                'missing_ratio': X[col].isna().mean()
            }
            
            # Determine default value based on feature name
            default_value = self._get_domain_default(col)
            
            # Store statistics and default value
            self.imputation_stats[col] = stats
            self.feature_defaults[col] = default_value
        
        return self
    
    def transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Impute missing values in the data.
        
        Args:
            X: DataFrame with features
            
        Returns:
            DataFrame with imputed values
        """
        # Make a copy to avoid modifying the original
        result = X.copy()
        
        # Check for NaN values
        nan_columns = result.columns[result.isna().any()].tolist()
        if not nan_columns:
            return result
        
        # Log NaN statistics
        nan_percentage = result.isna().sum().sum() / (result.shape[0] * result.shape[1]) * 100
        logger.info(f"Imputing {len(nan_columns)} columns with NaN values ({nan_percentage:.2f}% of all values)")
        
        # Impute each column
        for col in nan_columns:
            # Get non-NaN ratio
            non_nan_ratio = result[col].notna().mean()
            logger.info(f"Column '{col}' has {(1-non_nan_ratio)*100:.1f}% NaN values")
            
            # Choose imputation strategy
            if self.strategy == 'auto':
                # If we have at least 30% non-NaN values, use statistical imputation
                if non_nan_ratio >= 0.3:
                    # For skewed features, use median
                    if any(term in col.lower() for term in ['volume', 'count', 'size']):
                        value = result[col].median()
                        result[col] = result[col].fillna(value)
                        logger.info(f"Imputed column '{col}' with median value: {value:.4f}")
                    # For more normally distributed features, use mean
                    else:
                        value = result[col].mean()
                        result[col] = result[col].fillna(value)
                        logger.info(f"Imputed column '{col}' with mean value: {value:.4f}")
                else:
                    # For columns with insufficient data, use domain knowledge
                    value = self._get_domain_default(col)
                    result[col] = result[col].fillna(value)
                    logger.info(f"Imputed column '{col}' with domain default: {value}")
            elif self.strategy == 'mean':
                # Use mean imputation
                value = result[col].mean()
                if pd.isna(value):  # If mean is NaN (all values are NaN)
                    value = self._get_domain_default(col)
                result[col] = result[col].fillna(value)
                logger.info(f"Imputed column '{col}' with mean value: {value}")
            elif self.strategy == 'median':
                # Use median imputation
                value = result[col].median()
                if pd.isna(value):  # If median is NaN (all values are NaN)
                    value = self._get_domain_default(col)
                result[col] = result[col].fillna(value)
                logger.info(f"Imputed column '{col}' with median value: {value}")
            elif self.strategy == 'zero':
                # Fill with zeros
                result[col] = result[col].fillna(0.0)
                logger.info(f"Imputed column '{col}' with zero")
            elif self.strategy == 'domain':
                # Use domain-specific defaults
                value = self._get_domain_default(col)
                result[col] = result[col].fillna(value)
                logger.info(f"Imputed column '{col}' with domain default: {value}")
        
        # Verify all NaNs have been handled
        if result.isna().any().any():
            remaining_nan_cols = result.columns[result.isna().any()].tolist()
            logger.warning(f"Still have NaN values in columns after imputation: {remaining_nan_cols}")
            # Last resort: fill any remaining NaNs with 0
            result = result.fillna(0.0)
            logger.info("Filled all remaining NaN values with 0.0")
        else:
            logger.info("All NaN values successfully imputed")
        
        return result
    
    def fit_transform(self, X: pd.DataFrame) -> pd.DataFrame:
        """
        Fit the imputer to the data and impute missing values.
        
        Args:
            X: DataFrame with features
            
        Returns:
            DataFrame with imputed values
        """
        return self.fit(X).transform(X)
    
    def _get_domain_default(self, feature_name: str) -> float:
        """
        Get domain-specific default value for a feature.
        
        Args:
            feature_name: Name of the feature
            
        Returns:
            Default value for the feature
        """
        feature_lower = feature_name.lower()
        
        # Check for each domain type
        for domain, default in self.domain_defaults.items():
            if domain in feature_lower:
                return default
        
        # Return default value if no match found
        return self.domain_defaults['default']
