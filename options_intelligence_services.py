#!/usr/bin/env python3
"""
OPTIONS INTELLIGENCE SERVICES - PHASE 2 IMPLEMENTATION
Core services for sophisticated options analysis using validated Greek Engine integration
"""

import sys
import numpy as np
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import logging

# Add CORE to path for imports
sys.path.append(str(Path(__file__).parent.parent))

# Import validated Greek Engine integration
from validate_greek_integration import GreekEngineAdapter, OptionsDataContract, GreeksContract

logger = logging.getLogger(__name__)

@dataclass
class MarketContextContract:
    """Market environment data for options analysis"""
    iv_rank: float                    # IV percentile (0-100)
    iv_trend: str                     # 'rising', 'falling', 'stable'
    price_trend: str                  # 'bullish', 'bearish', 'neutral'
    volatility_regime: str            # 'low_vol', 'normal_vol', 'high_vol'
    days_to_earnings: Optional[int] = None
    support_level: Optional[float] = None
    resistance_level: Optional[float] = None
    historical_vol_20d: Optional[float] = None

@dataclass
class TradeAnalysisResult:
    """Complete trade analysis result"""
    trade_classification: str
    risk_profile: str
    leverage_factor: float
    daily_theta_cost: float
    gamma_acceleration: float
    iv_sensitivity: float
    probability_estimate: float
    breakeven_move_required: float
    max_profit_potential: str
    risk_reward_ratio: float

@dataclass
class PositionSizingResult:
    """Position sizing analysis result"""
    recommended_size: float
    size_reasoning: List[str]
    risk_adjustments: Dict[str, float]
    maximum_size_limit: float
    minimum_size_threshold: float
    confidence_scaling: float

@dataclass
class ExitStrategyResult:
    """Complete exit strategy analysis"""
    max_days_in_trade: int
    profit_targets: List[Dict[str, Any]]
    stop_losses: List[Dict[str, Any]]
    time_exits: List[Dict[str, Any]]
    iv_exits: List[Dict[str, Any]]
    emergency_exits: List[Dict[str, Any]]
    priority_exit_sequence: List[str]
    exit_monitoring_alerts: List[str]

class OptionsIntelligenceService:
    """
    Core Options Intelligence Service using validated Greek Engine
    Provides sophisticated options analysis for Agent Zero
    """
    
    def __init__(self):
        self.greek_adapter = GreekEngineAdapter()
        
        # Position sizing parameters
        self.base_position_size = 1.0
        self.max_position_size = 10.0
        self.min_position_size = 0.1
        
        # Risk adjustment matrices
        self.iv_position_adjustments = {
            (90, 100): 0.3,   # Very expensive IV
            (70, 90): 0.5,    # Expensive IV
            (30, 70): 1.0,    # Normal IV
            (10, 30): 1.2,    # Cheap IV
            (0, 10): 1.5      # Very cheap IV
        }
        
        self.delta_risk_adjustments = {
            (0.65, 1.0): 0.9,   # ITM - conservative sizing
            (0.40, 0.65): 1.0,  # ATM - balanced sizing  
            (0.20, 0.40): 1.1,  # OTM - slightly higher leverage
            (0.0, 0.20): 0.0    # Far OTM - rejected (no trading)
        }
        
        logger.info("Options Intelligence Service initialized with Greek Engine integration")
    
    def analyze_trade_type(self, option_data: OptionsDataContract, 
                          greeks: GreeksContract, 
                          market_context: MarketContextContract) -> TradeAnalysisResult:
        """
        Comprehensive trade analysis using actual Greek data
        """
        try:
            delta = abs(greeks.delta)
            gamma = greeks.gamma
            theta = greeks.theta
            vega = greeks.vega
            
            # Trade classification - ITM/ATM/OTM only (no far OTM)
            if delta > 0.65:
                trade_type = 'itm_high_probability'
                risk_profile = 'conservative_high_probability'
                probability = 0.80
            elif 0.40 <= delta <= 0.65:
                trade_type = 'atm_balanced'
                risk_profile = 'balanced_risk_reward'
                probability = 0.60
            elif 0.20 <= delta < 0.40:
                trade_type = 'otm_leverage'
                risk_profile = 'moderate_leverage_play'
                probability = 0.35
            else:
                # Reject far OTM - outside trading criteria
                trade_type = 'rejected_far_otm'
                risk_profile = 'outside_trading_criteria'
                probability = 0.0
            
            # Calculate key metrics using real Greek data
            leverage_factor = self._calculate_leverage_factor(option_data, greeks)
            breakeven_move = self._calculate_breakeven_move(option_data, greeks)
            max_profit = self._determine_max_profit_potential(option_data, greeks)
            risk_reward = self._calculate_risk_reward_ratio(option_data, greeks, market_context)
            
            # ROI Validation - minimum 1.5x required
            roi_valid = self._validate_minimum_roi(option_data, greeks, market_context)
            
            # Reject trade if ROI doesn't meet minimum criteria
            if not roi_valid:
                trade_type = 'rejected_insufficient_roi'
                risk_profile = 'below_minimum_roi_threshold'
                probability = 0.0
            
            return TradeAnalysisResult(
                trade_classification=trade_type,
                risk_profile=risk_profile,
                leverage_factor=leverage_factor,
                daily_theta_cost=abs(theta),
                gamma_acceleration=gamma,
                iv_sensitivity=vega,
                probability_estimate=probability,
                breakeven_move_required=breakeven_move,
                max_profit_potential=max_profit,
                risk_reward_ratio=risk_reward
            )
            
        except Exception as e:
            logger.error(f"Trade analysis failed: {e}")
            raise
    
    def calculate_position_size(self, base_size: float, 
                              option_data: OptionsDataContract,
                              greeks: GreeksContract,
                              market_context: MarketContextContract,
                              agent_zero_confidence: float) -> PositionSizingResult:
        """
        Sophisticated position sizing using complete Greek analysis
        """
        try:
            # Start with base size (from Agent Zero confidence)
            adjusted_size = base_size * agent_zero_confidence
            size_reasoning = [f"Base size {base_size} * Agent Zero confidence {agent_zero_confidence:.2f}"]
            
            risk_adjustments = {}
            
            # ROI Validation - minimum 1.5x required
            roi_valid = self._validate_minimum_roi(option_data, greeks, market_context)
            if not roi_valid:
                return PositionSizingResult(
                    recommended_size=0.0,
                    size_reasoning=["Trade rejected: Insufficient ROI potential (min 1.5x required)"],
                    risk_adjustments={'roi_rejection': 0.0},
                    maximum_size_limit=0.0,
                    minimum_size_threshold=0.0,
                    confidence_scaling=0.0
                )
            
            # IV Environment Adjustment
            iv_multiplier = self._get_iv_position_multiplier(market_context.iv_rank)
            adjusted_size *= iv_multiplier
            risk_adjustments['iv_adjustment'] = iv_multiplier
            size_reasoning.append(f"IV rank {market_context.iv_rank} -> {iv_multiplier:.2f}x multiplier")
            
            # Delta Risk Adjustment - reject far OTM
            delta_multiplier = self._get_delta_position_multiplier(abs(greeks.delta))
            if delta_multiplier == 0.0:  # Far OTM rejection
                return PositionSizingResult(
                    recommended_size=0.0,
                    size_reasoning=["Trade rejected: Far OTM outside trading criteria"],
                    risk_adjustments={'delta_rejection': 0.0},
                    maximum_size_limit=0.0,
                    minimum_size_threshold=0.0,
                    confidence_scaling=0.0
                )
            
            adjusted_size *= delta_multiplier
            risk_adjustments['delta_adjustment'] = delta_multiplier
            size_reasoning.append(f"Delta {greeks.delta:.3f} -> {delta_multiplier:.2f}x multiplier")
            
            # Gamma Risk Adjustment
            gamma_multiplier = self._get_gamma_adjustment(greeks.gamma, market_context)
            adjusted_size *= gamma_multiplier
            risk_adjustments['gamma_adjustment'] = gamma_multiplier
            size_reasoning.append(f"Gamma {greeks.gamma:.4f} -> {gamma_multiplier:.2f}x multiplier")
            
            # Theta Decay Adjustment
            theta_multiplier = self._get_theta_adjustment(greeks.theta, option_data)
            adjusted_size *= theta_multiplier
            risk_adjustments['theta_adjustment'] = theta_multiplier
            size_reasoning.append(f"Daily theta {greeks.theta:.3f} -> {theta_multiplier:.2f}x multiplier")
            
            # Vega Risk Adjustment (IV sensitivity)
            vega_multiplier = self._get_vega_adjustment(greeks.vega, market_context)
            adjusted_size *= vega_multiplier
            risk_adjustments['vega_adjustment'] = vega_multiplier
            size_reasoning.append(f"Vega {greeks.vega:.4f} -> {vega_multiplier:.2f}x multiplier")
            
            # Higher-order Greek adjustments if available
            if greeks.vanna is not None:
                vanna_multiplier = self._get_vanna_adjustment(greeks.vanna, market_context)
                adjusted_size *= vanna_multiplier
                risk_adjustments['vanna_adjustment'] = vanna_multiplier
                size_reasoning.append(f"Vanna {greeks.vanna:.6f} -> {vanna_multiplier:.2f}x multiplier")
            
            # Apply bounds
            final_size = max(self.min_position_size, min(adjusted_size, self.max_position_size))
            if final_size != adjusted_size:
                size_reasoning.append(f"Bounded to [{self.min_position_size}, {self.max_position_size}]")
            
            return PositionSizingResult(
                recommended_size=final_size,
                size_reasoning=size_reasoning,
                risk_adjustments=risk_adjustments,
                maximum_size_limit=self.max_position_size,
                minimum_size_threshold=self.min_position_size,
                confidence_scaling=agent_zero_confidence
            )
            
        except Exception as e:
            logger.error(f"Position sizing calculation failed: {e}")
            raise
    
    def generate_exit_strategy(self, option_data: OptionsDataContract,
                             greeks: GreeksContract,
                             market_context: MarketContextContract,
                             trade_analysis: TradeAnalysisResult) -> ExitStrategyResult:
        """
        Generate comprehensive exit strategy using Greek analysis
        """
        try:
            # Calculate maximum hold time based on theta decay
            max_days = self._calculate_max_hold_time(greeks, option_data, market_context)
            
            # Generate profit targets using delta/gamma dynamics
            profit_targets = self._calculate_profit_targets(option_data, greeks, trade_analysis)
            
            # Generate stop losses using Greek risk metrics
            stop_losses = self._calculate_stop_losses(option_data, greeks, trade_analysis)
            
            # Time-based exits using theta acceleration
            time_exits = self._calculate_time_exits(greeks, option_data, market_context)
            
            # IV-based exits using vega analysis
            iv_exits = self._calculate_iv_exits(greeks, market_context, option_data)
            
            # Emergency exits using comprehensive Greek analysis
            emergency_exits = self._calculate_emergency_exits(greeks, option_data, market_context)
            
            # Prioritize exits based on current market conditions
            priority_sequence = self._rank_exit_priorities(
                greeks, option_data, market_context, trade_analysis
            )
            
            # Generate monitoring alerts
            monitoring_alerts = self._generate_exit_monitoring_alerts(
                greeks, option_data, market_context
            )
            
            return ExitStrategyResult(
                max_days_in_trade=max_days,
                profit_targets=profit_targets,
                stop_losses=stop_losses,
                time_exits=time_exits,
                iv_exits=iv_exits,
                emergency_exits=emergency_exits,
                priority_exit_sequence=priority_sequence,
                exit_monitoring_alerts=monitoring_alerts
            )
            
        except Exception as e:
            logger.error(f"Exit strategy generation failed: {e}")
            raise
    
    def _validate_minimum_roi(self, option_data: OptionsDataContract,
                            greeks: GreeksContract,
                            market_context: MarketContextContract) -> bool:
        """
        Validate minimum ROI requirement (1.5-1.75x)
        Only trade if expected move provides sufficient ROI potential
        """
        try:
            premium = self._estimate_option_premium(option_data)
            expected_move = self._estimate_expected_move(option_data, market_context)
            delta = abs(greeks.delta)
            
            # Calculate potential profit from expected move
            potential_profit = delta * expected_move
            
            # Calculate ROI ratio
            if premium > 0:
                roi_ratio = potential_profit / premium
                
                # Apply minimum ROI threshold (1.5x minimum, prefer 1.75x+)
                min_roi_threshold = 1.5
                preferred_roi_threshold = 1.75
                
                # Additional ROI boost for high probability trades
                if abs(greeks.delta) > 0.6:  # ITM trades
                    min_roi_threshold = 1.5  # Conservative ITM threshold
                elif abs(greeks.delta) > 0.4:  # ATM trades  
                    min_roi_threshold = 1.6  # Slightly higher for ATM
                else:  # OTM trades
                    min_roi_threshold = 1.75  # Higher threshold for OTM
                
                return roi_ratio >= min_roi_threshold
            
            return False
            
        except Exception:
            return False

    def _calculate_leverage_factor(self, option_data: OptionsDataContract, 
                                 greeks: GreeksContract) -> float:
        """Calculate effective leverage using delta and premium"""
        try:
            delta_exposure = abs(greeks.delta) * option_data.underlying_price
            premium_cost = self._estimate_option_premium(option_data)
            
            if premium_cost > 0:
                return delta_exposure / premium_cost
            return 1.0
            
        except Exception:
            return 1.0
    
    def _calculate_breakeven_move(self, option_data: OptionsDataContract,
                                greeks: GreeksContract) -> float:
        """Calculate required underlying move for breakeven"""
        try:
            premium = self._estimate_option_premium(option_data)
            delta = abs(greeks.delta)
            
            if delta > 0:
                breakeven_move = premium / delta
                return (breakeven_move / option_data.underlying_price) * 100  # Percentage
            return 100.0  # Large move required if no delta
            
        except Exception:
            return 50.0  # Default reasonable estimate
    
    def _determine_max_profit_potential(self, option_data: OptionsDataContract,
                                      greeks: GreeksContract) -> str:
        """Determine maximum profit potential"""
        if option_data.option_type.lower() == 'call':
            if option_data.strike <= option_data.underlying_price:
                return 'unlimited'  # ITM or ATM calls
            else:
                return 'high_but_limited'  # OTM calls
        else:  # Put
            max_profit = option_data.strike
            return f'limited_to_{max_profit:.0f}'
    
    def _calculate_risk_reward_ratio(self, option_data: OptionsDataContract,
                                   greeks: GreeksContract,
                                   market_context: MarketContextContract) -> float:
        """Calculate risk/reward ratio"""
        try:
            premium = self._estimate_option_premium(option_data)
            expected_move = self._estimate_expected_move(option_data, market_context)
            delta = abs(greeks.delta)
            
            potential_profit = delta * expected_move
            risk = premium
            
            if risk > 0:
                return potential_profit / risk
            return 1.0
            
        except Exception:
            return 1.0
    
    def _estimate_option_premium(self, option_data: OptionsDataContract) -> float:
        """Estimate option premium (simplified)"""
        # For validation - in production this would come from market data
        if option_data.option_type.lower() == 'call':
            intrinsic = max(0, option_data.underlying_price - option_data.strike)
        else:
            intrinsic = max(0, option_data.strike - option_data.underlying_price)
        
        # Add time value estimate
        time_value = option_data.implied_volatility * np.sqrt(30/365) * option_data.underlying_price * 0.1
        
        return intrinsic + time_value
    
    def _estimate_expected_move(self, option_data: OptionsDataContract,
                              market_context: MarketContextContract) -> float:
        """Estimate expected price move"""
        # Simple expected move calculation
        return option_data.underlying_price * option_data.implied_volatility * np.sqrt(30/365)
    
    def _get_iv_position_multiplier(self, iv_rank: float) -> float:
        """Get position size multiplier based on IV environment"""
        for (low, high), multiplier in self.iv_position_adjustments.items():
            if low <= iv_rank < high:
                return multiplier
        return 1.0  # Default for edge cases
    
    def _get_delta_position_multiplier(self, delta: float) -> float:
        """Get position size multiplier based on delta"""
        for (low, high), multiplier in self.delta_risk_adjustments.items():
            if low <= delta < high:
                return multiplier
        return 1.0  # Default for edge cases
    
    def _get_gamma_adjustment(self, gamma: float, market_context: MarketContextContract) -> float:
        """Adjust position size based on gamma risk"""
        if market_context.volatility_regime == 'high_vol':
            # High volatility = gamma risk more dangerous
            return 1.0 - (gamma * 2.0)  # Reduce size for high gamma in volatile markets
        elif market_context.volatility_regime == 'low_vol':
            # Low volatility = gamma less risky
            return 1.0 - (gamma * 0.5)
        else:
            return 1.0 - gamma  # Normal adjustment
    
    def _get_theta_adjustment(self, theta: float, option_data: OptionsDataContract) -> float:
        """Adjust position size based on theta decay"""
        time_to_exp = self._calculate_time_to_expiry_days(option_data.expiration_date)
        
        if time_to_exp <= 7:
            # Near expiration - theta accelerates
            return 1.0 + (theta * 0.1)  # Theta is negative, so this reduces size
        elif time_to_exp <= 21:
            # Theta acceleration zone
            return 1.0 + (theta * 0.05)
        else:
            # Normal time decay
            return 1.0 + (theta * 0.02)
    
    def _get_vega_adjustment(self, vega: float, market_context: MarketContextContract) -> float:
        """Adjust position size based on vega risk"""
        if market_context.iv_trend == 'falling':
            # IV falling = bad for long options
            return 1.0 - (vega * 0.1)
        elif market_context.iv_trend == 'rising':
            # IV rising = good for long options
            return 1.0 + (vega * 0.05)
        else:
            return 1.0  # Stable IV
    
    def _get_vanna_adjustment(self, vanna: float, market_context: MarketContextContract) -> float:
        """Adjust for vanna (delta-volatility cross-sensitivity)"""
        if market_context.iv_trend == 'rising' and vanna > 0:
            return 1.1  # Positive vanna + rising IV = good
        elif market_context.iv_trend == 'falling' and vanna > 0:
            return 0.9  # Positive vanna + falling IV = bad
        else:
            return 1.0
    
    def _calculate_time_to_expiry_days(self, expiration_date: str) -> int:
        """Calculate days to expiration"""
        try:
            exp_date = datetime.strptime(expiration_date, "%Y-%m-%d")
            today = datetime.now()
            return max(1, (exp_date - today).days)
        except:
            return 21  # Default
    
    def _calculate_max_hold_time(self, greeks: GreeksContract, 
                               option_data: OptionsDataContract,
                               market_context: MarketContextContract) -> int:
        """Calculate maximum recommended hold time"""
        days_to_exp = self._calculate_time_to_expiry_days(option_data.expiration_date)
        delta = abs(greeks.delta)
        theta = abs(greeks.theta)
        
        # Base time limits by option characteristics
        if delta > 0.7:  # Deep ITM
            max_time_pct = 0.75
        elif delta > 0.4:  # ATM
            max_time_pct = 0.60
        elif delta > 0.2:  # OTM
            max_time_pct = 0.50
        else:  # Far OTM
            max_time_pct = 0.30
        
        # Adjust for IV environment
        if market_context.iv_rank > 80:
            max_time_pct *= 0.7  # Exit faster in expensive IV
        elif market_context.iv_rank < 20:
            max_time_pct *= 1.2  # Can hold longer in cheap IV
        
        # Adjust for theta magnitude
        if theta > 2.0:  # High theta decay
            max_time_pct *= 0.8
        
        return max(1, int(days_to_exp * max_time_pct))
    
    def _calculate_profit_targets(self, option_data: OptionsDataContract,
                                greeks: GreeksContract,
                                trade_analysis: TradeAnalysisResult) -> List[Dict[str, Any]]:
        """Calculate profit target levels"""
        premium = self._estimate_option_premium(option_data)
        
        targets = [
            {
                'target_name': 'quick_profit',
                'profit_percentage': 25,
                'target_value': premium * 1.25,
                'timeframe': 'within_3_days',
                'reasoning': 'Quick profit capture to reduce risk'
            },
            {
                'target_name': 'primary_target', 
                'profit_percentage': 50,
                'target_value': premium * 1.50,
                'timeframe': 'primary_target',
                'reasoning': 'Primary profit objective'
            },
            {
                'target_name': 'extended_target',
                'profit_percentage': 100,
                'target_value': premium * 2.0,
                'timeframe': 'if_big_move',
                'reasoning': 'Home run profit if thesis plays out strongly'
            }
        ]
        
        return targets
    
    def _calculate_stop_losses(self, option_data: OptionsDataContract,
                             greeks: GreeksContract,
                             trade_analysis: TradeAnalysisResult) -> List[Dict[str, Any]]:
        """Calculate stop loss levels"""
        premium = self._estimate_option_premium(option_data)
        delta = abs(greeks.delta)
        
        # Stop loss percentage based on delta (option type)
        if delta > 0.6:
            stop_loss_pct = 0.25  # ITM options - tighter stop
        elif delta > 0.4:
            stop_loss_pct = 0.35  # ATM options
        else:
            stop_loss_pct = 0.50  # OTM options - wider stop
        
        stops = [
            {
                'stop_name': 'premium_stop',
                'stop_percentage': stop_loss_pct * 100,
                'stop_value': premium * (1 - stop_loss_pct),
                'reasoning': f'Risk management - limit loss to {stop_loss_pct:.0%}'
            },
            {
                'stop_name': 'time_decay_stop',
                'condition': 'daily_theta_exceeds_expected',
                'reasoning': 'Exit if time decay accelerates beyond normal'
            }
        ]
        
        return stops
    
    def _calculate_time_exits(self, greeks: GreeksContract,
                            option_data: OptionsDataContract,
                            market_context: MarketContextContract) -> List[Dict[str, Any]]:
        """Calculate time-based exit rules"""
        days_to_exp = self._calculate_time_to_expiry_days(option_data.expiration_date)
        
        exits = [
            {
                'exit_name': 'theta_acceleration_zone',
                'trigger_days': 14,
                'condition': f'days_to_expiration <= 14',
                'reasoning': 'Theta acceleration begins - time decay risk increases'
            },
            {
                'exit_name': 'weekend_risk_zone',
                'trigger_days': 3,
                'condition': f'days_to_expiration <= 3 AND approaching_friday',
                'reasoning': 'Weekend time decay risk near expiration'
            }
        ]
        
        return exits
    
    def _calculate_iv_exits(self, greeks: GreeksContract,
                          market_context: MarketContextContract,
                          option_data: OptionsDataContract) -> List[Dict[str, Any]]:
        """Calculate IV-based exit rules"""
        current_iv_rank = market_context.iv_rank
        
        exits = [
            {
                'exit_name': 'iv_crush_protection',
                'trigger_condition': f'iv_rank_drops_below_{max(10, current_iv_rank - 30)}',
                'reasoning': 'Protect against IV crush'
            },
            {
                'exit_name': 'iv_expansion_profit',
                'trigger_condition': f'iv_rank_rises_above_{min(90, current_iv_rank + 20)}',
                'reasoning': 'Take profits on IV expansion'
            }
        ]
        
        if market_context.days_to_earnings and market_context.days_to_earnings <= 7:
            exits.append({
                'exit_name': 'earnings_iv_crush',
                'trigger_condition': 'earnings_announcement_passed',
                'reasoning': 'Eliminate post-earnings IV crush risk'
            })
        
        return exits
    
    def _calculate_emergency_exits(self, greeks: GreeksContract,
                                 option_data: OptionsDataContract,
                                 market_context: MarketContextContract) -> List[Dict[str, Any]]:
        """Calculate emergency exit conditions"""
        return [
            {
                'exit_name': 'liquidity_crisis',
                'condition': 'bid_ask_spread > 20% OR volume < 10',
                'reasoning': 'Execution risk too high - exit immediately'
            },
            {
                'exit_name': 'extreme_favorable_move',
                'condition': f'underlying_move > {abs(greeks.delta) * 3:.1f}% in single day',
                'reasoning': 'Outsized move may reverse - take profits'
            },
            {
                'exit_name': 'market_regime_change',
                'condition': 'market_down > 3% AND vix_spike > 20%',
                'reasoning': 'Market stress - reassess all positions'
            }
        ]
    
    def _rank_exit_priorities(self, greeks: GreeksContract,
                            option_data: OptionsDataContract,
                            market_context: MarketContextContract,
                            trade_analysis: TradeAnalysisResult) -> List[str]:
        """Rank exit conditions by priority"""
        days_to_exp = self._calculate_time_to_expiry_days(option_data.expiration_date)
        
        if days_to_exp <= 7:
            return ['weekend_risk_zone', 'quick_profit', 'premium_stop']
        elif days_to_exp <= 21:
            return ['theta_acceleration_zone', 'primary_target', 'iv_crush_protection']
        else:
            return ['primary_target', 'premium_stop', 'iv_expansion_profit']
    
    def _generate_exit_monitoring_alerts(self, greeks: GreeksContract,
                                       option_data: OptionsDataContract,
                                       market_context: MarketContextContract) -> List[str]:
        """Generate monitoring alerts for exit conditions"""
        alerts = [
            'Monitor daily theta decay vs expected',
            'Track IV rank changes',
            'Watch underlying price vs support/resistance'
        ]
        
        if market_context.days_to_earnings and market_context.days_to_earnings <= 14:
            alerts.append('Monitor earnings approach - IV crush risk')
        
        if abs(greeks.delta) < 0.3:
            alerts.append('Monitor delta - OTM option needs significant move')
        
        if greeks.vega > 0.05:
            alerts.append('High vega exposure - monitor IV environment closely')
        
        return alerts

# Example usage and validation
def validate_options_intelligence_service():
    """
    Validate the Options Intelligence Service implementation
    """
    print("=" * 60)
    print("OPTIONS INTELLIGENCE SERVICE VALIDATION")
    print("=" * 60)
    
    try:
        # Initialize service
        service = OptionsIntelligenceService()
        print("[OK] Options Intelligence Service initialized")
        
        # Create test data - ITM scenario with good ROI potential
        option_data = OptionsDataContract(
            underlying_price=455.0,
            strike=445.0,  # ITM call
            expiration_date="2025-01-17",
            option_type="call",
            implied_volatility=0.30,  # Higher IV for better ROI
            symbol="SPY"
        )
        
        market_context = MarketContextContract(
            iv_rank=45.0,
            iv_trend='stable',
            price_trend='bullish',
            volatility_regime='normal_vol',
            days_to_earnings=None,
            historical_vol_20d=0.22
        )
        
        # Get Greeks through validated adapter
        greeks = service.greek_adapter.calculate_greeks(option_data)
        print("[OK] Greeks calculated through validated adapter")
        
        # Test trade analysis
        trade_analysis = service.analyze_trade_type(option_data, greeks, market_context)
        print(f"[OK] Trade Analysis: {trade_analysis.trade_classification}")
        print(f"     Risk Profile: {trade_analysis.risk_profile}")
        print(f"     Leverage: {trade_analysis.leverage_factor:.2f}x")
        print(f"     Probability: {trade_analysis.probability_estimate:.1%}")
        
        # Test position sizing
        position_result = service.calculate_position_size(
            base_size=2.0,
            option_data=option_data,
            greeks=greeks,
            market_context=market_context,
            agent_zero_confidence=0.75
        )
        print(f"[OK] Position Sizing: {position_result.recommended_size:.1f} contracts")
        print(f"     Reasoning: {len(position_result.size_reasoning)} factors considered")
        
        # Test exit strategy
        exit_strategy = service.generate_exit_strategy(
            option_data, greeks, market_context, trade_analysis
        )
        print(f"[OK] Exit Strategy: {exit_strategy.max_days_in_trade} max days")
        print(f"     Targets: {len(exit_strategy.profit_targets)} profit levels")
        print(f"     Stops: {len(exit_strategy.stop_losses)} stop loss rules")
        print(f"     Alerts: {len(exit_strategy.exit_monitoring_alerts)} monitoring items")
        
        print("\n[SUCCESS] All Options Intelligence Service tests passed")
        return True
        
    except Exception as e:
        print(f"[ERROR] Validation failed: {e}")
        return False

if __name__ == "__main__":
    validate_options_intelligence_service()
