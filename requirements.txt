# CORE Trading Analytics Platform - Complete Dependencies
# Mathematical rigor and production-grade requirements
# SCHWAB MCP ONLY - No external API dependencies

# Core Data Processing
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0

# Machine Learning and Statistics
scikit-learn>=1.3.0
joblib>=1.3.0

# Visualization
matplotlib>=3.7.0

# Web Framework and APIs
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
pydantic>=2.0.0
requests>=2.31.0
aiohttp>=3.8.0

# Configuration Management
python-dotenv>=1.0.0
pyyaml>=6.0.0

# System Monitoring
psutil>=5.9.0

# Development and Testing
pytest>=7.4.0

# REMOVED: External Financial APIs - Schwab MCP only
# yfinance - REMOVED (external dependency)
# polygon-api-client - REMOVED (external dependency)  
# alpha-vantage - REMOVED (external dependency)

# Additional useful packages for trading systems
# streamlit>=1.25.0  # For dashboards
# plotly>=5.15.0     # Advanced plotting
# redis>=4.6.0       # Caching
# sqlalchemy>=2.0.0  # Database ORM
