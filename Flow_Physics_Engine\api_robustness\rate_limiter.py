"""
Advanced Rate Limiting Module for External Flow Physics Engine

Mathematical precision rate limiting with adaptive algorithms:
- Token bucket with configurable burst capacity
- Exponential backoff for error handling
- Performance-based rate adjustment
- Statistical optimization for AI training
"""

import time
import threading
import math
from collections import deque, defaultdict
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class RateLimitTier(Enum):
    """Polygon.io subscription tiers with rate limits."""
    FREE = "free"
    STARTER = "starter"
    DEVELOPER = "developer"
    PROFESSIONAL = "professional"


@dataclass
class RateLimitConfig:
    """Rate limit configuration based on subscription tier."""
    requests_per_minute: int
    requests_per_second: int
    burst_capacity: int
    tier: RateLimitTier
    
    @classmethod
    def from_tier(cls, tier: RateLimitTier) -> 'RateLimitConfig':
        """Create config from subscription tier."""
        configs = {
            RateLimitTier.FREE: cls(5, 0.083, 5, tier),
            RateLimitTier.STARTER: cls(300, 5, 10, tier),
            RateLimitTier.DEVELOPER: cls(3000, 50, 100, tier),
            RateLimitTier.PROFESSIONAL: cls(6000, 100, 200, tier)
        }
        return configs.get(tier, configs[RateLimitTier.FREE])


class TokenBucketAdvanced:
    """Advanced token bucket with mathematical optimization."""
    
    def __init__(self, capacity: int, refill_rate: float, initial_tokens: Optional[int] = None):
        """Initialize advanced token bucket."""
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = initial_tokens if initial_tokens is not None else capacity
        self.last_refill = time.time()
        self.lock = threading.Lock()
        
        # Performance tracking
        self.total_requests = 0
        self.granted_requests = 0
        self.denied_requests = 0
        
    def acquire(self, tokens: int = 1, blocking: bool = False, timeout: float = 10.0) -> bool:
        """Acquire tokens with optional blocking."""
        start_time = time.time()
        
        while True:
            with self.lock:
                self._refill()
                self.total_requests += 1
                
                if self.tokens >= tokens:
                    self.tokens -= tokens
                    self.granted_requests += 1
                    return True
                else:
                    self.denied_requests += 1
                    
                    if not blocking:
                        return False
                    
                    wait_time = self._calculate_wait_time(tokens)
                    
            if time.time() - start_time + wait_time > timeout:
                return False
                
            time.sleep(min(wait_time, 0.1))
    
    def _refill(self):
        """Refill tokens based on elapsed time."""
        now = time.time()
        time_passed = now - self.last_refill
        
        if time_passed > 0:
            new_tokens = time_passed * self.refill_rate
            self.tokens = min(self.capacity, self.tokens + new_tokens)
            self.last_refill = now
    
    def _calculate_wait_time(self, tokens: int) -> float:
        """Calculate minimum wait time for tokens to be available."""
        needed_tokens = tokens - self.tokens
        if needed_tokens <= 0:
            return 0.0
        return needed_tokens / self.refill_rate
    
    def get_stats(self) -> Dict[str, float]:
        """Get bucket performance statistics."""
        with self.lock:
            if self.total_requests == 0:
                return {
                    'total_requests': 0,
                    'success_rate': 0.0,
                    'current_tokens': self.tokens,
                    'capacity': self.capacity,
                    'refill_rate': self.refill_rate
                }
            
            return {
                'total_requests': self.total_requests,
                'granted_requests': self.granted_requests,
                'denied_requests': self.denied_requests,
                'success_rate': self.granted_requests / self.total_requests,
                'current_tokens': self.tokens,
                'capacity': self.capacity,
                'refill_rate': self.refill_rate
            }


class AdaptiveRateLimiterPro:
    """Professional adaptive rate limiter with mathematical optimization."""
    
    def __init__(self, config: RateLimitConfig):
        """Initialize adaptive rate limiter."""
        self.config = config
        
        # Primary token bucket
        self.bucket = TokenBucketAdvanced(
            capacity=config.burst_capacity,
            refill_rate=config.requests_per_second
        )
        
        # Adaptive parameters
        self.adaptive_factor = 1.0
        self.lock = threading.RLock()
        
        logger.info(f"Rate limiter initialized: {config.tier.value} tier")
    
    def acquire_permit(self, tokens: int = 1, timeout: float = 10.0) -> bool:
        """Acquire rate limiting permit."""
        with self.lock:
            return self.bucket.acquire(tokens, blocking=True, timeout=timeout)
    
    def record_response(self, response_time: float, status_code: int):
        """Record response for adaptive rate adjustment."""
        if status_code == 429:
            # Rate limited - reduce rate
            self.adaptive_factor = max(0.1, self.adaptive_factor * 0.5)
            self.bucket.refill_rate = self.config.requests_per_second * self.adaptive_factor
            logger.info(f"Rate limited, reducing adaptive factor to {self.adaptive_factor}")
        elif status_code == 200 and response_time < 0.5:
            # Fast successful response - potentially increase rate
            self.adaptive_factor = min(2.0, self.adaptive_factor * 1.1)
            self.bucket.refill_rate = self.config.requests_per_second * self.adaptive_factor
    
    def get_stats(self) -> Dict[str, any]:
        """Get rate limiter statistics."""
        bucket_stats = self.bucket.get_stats()
        return {
            'rate_limiting': {
                'config': {
                    'tier': self.config.tier.value,
                    'base_rate': self.config.requests_per_second,
                    'burst_capacity': self.config.burst_capacity
                },
                'current_state': {
                    'adaptive_factor': self.adaptive_factor,
                    'effective_rate': self.bucket.refill_rate,
                    'current_tokens': self.bucket.tokens
                }
            },
            'bucket_stats': bucket_stats
        }


def create_rate_limiter_for_tier(tier_name: str) -> AdaptiveRateLimiterPro:
    """Factory function to create rate limiter for subscription tier."""
    try:
        tier = RateLimitTier(tier_name.lower())
    except ValueError:
        logger.warning(f"Unknown tier '{tier_name}', defaulting to FREE")
        tier = RateLimitTier.FREE
    
    config = RateLimitConfig.from_tier(tier)
    return AdaptiveRateLimiterPro(config)


if __name__ == "__main__":
    # Test the rate limiter
    logging.basicConfig(level=logging.INFO)
    
    limiter = create_rate_limiter_for_tier("starter")
    
    print("Testing rate limiting...")
    for i in range(5):
        if limiter.acquire_permit():
            response_time = 0.1 + (i * 0.05)
            status_code = 200
            limiter.record_response(response_time, status_code)
            print(f"Request {i+1}: Granted")
        else:
            print(f"Request {i+1}: Denied")
        
        time.sleep(0.1)
    
    stats = limiter.get_stats()
    print(f"\nStats: {stats}")
