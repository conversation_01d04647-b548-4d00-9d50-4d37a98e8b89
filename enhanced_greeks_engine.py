#!/usr/bin/env python3
"""
Enhanced Greeks Engine - MCP Primary + Calculated Fallback
Production implementation with quality validation and cross-verification
"""

import numpy as np
import pandas as pd
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
import time

class EnhancedGreeksEngine:
    """
    Advanced Greeks engine with MCP primary + calculated fallback
    Implements quality validation and cross-verification
    """
    
    def __init__(self):
        self.logger = logging.getLogger("enhanced_greeks")
        
        # Initialize real-time data agent for MCP Greeks
        try:
            from enhanced_data_agent_broker_integration import EnhancedDataAgent
            self.mcp_agent = EnhancedDataAgent()
            self.mcp_available = True
            self.logger.info("Enhanced Greeks Engine: MCP primary + BSM fallback initialized")
        except ImportError:
            self.mcp_agent = None
            self.mcp_available = False
            self.logger.warning("MCP unavailable - calculated Greeks only mode")
        
        # Initialize BSM calculator for fallback
        try:
            # Try to import the BSM calculator from tasks.greeks
            sys.path.append(str(Path(__file__).parent / 'tasks'))
            from tasks.greeks import GreeksCalculator
            self.bsm_calculator = GreeksCalculator()
            self.bsm_available = True
            self.logger.info("BSM Greeks calculator available for fallback")
        except ImportError as e:
            self.bsm_calculator = None
            self.bsm_available = False
            self.logger.warning(f"BSM calculator unavailable: {e} - MCP-only mode")
        
        # Quality thresholds for validation
        self.quality_thresholds = {
            'delta_tolerance': 0.05,      # 5% delta difference acceptable
            'gamma_tolerance': 0.10,      # 10% gamma difference acceptable  
            'vanna_tolerance': 0.15,      # 15% vanna difference acceptable
            'charm_tolerance': 0.20,      # 20% charm difference acceptable
            'min_options_for_mcp': 5,     # Minimum options needed for MCP
            'staleness_limit_seconds': 60, # Max age for MCP data
            'mcp_quality_threshold': 0.6   # Minimum quality to use MCP
        }
        
        # Performance tracking
        self.performance_stats = {
            'mcp_requests': 0,
            'mcp_successes': 0,
            'calculated_fallbacks': 0,
            'cross_validations': 0,
            'quality_failures': 0
        }
    
    def get_enhanced_greeks(self, ticker: str, option_chain: Optional[Dict] = None) -> Dict:
        """
        Get Greeks with hierarchical sourcing and quality validation
        
        Data Flow Priority:
        1. MCP real-time Greeks (Primary)
        2. Validate MCP data quality  
        3. BSM calculated Greeks (Fallback)
        4. Cross-validate and score results
        """
        
        start_time = time.time()
        
        result = {
            'ticker': ticker,
            'timestamp': datetime.now().isoformat(),
            'data_source': 'none',
            'quality_score': 0.0,
            'greeks': {},
            'validation': {},
            'fallback_used': False,
            'performance': {
                'execution_time_ms': 0,
                'mcp_attempted': False,
                'mcp_successful': False
            }
        }
        
        # Step 1: Attempt MCP real-time Greeks
        mcp_greeks = None
        mcp_quality = 0.0
        
        if self.mcp_available:
            self.performance_stats['mcp_requests'] += 1
            result['performance']['mcp_attempted'] = True
            
            mcp_greeks = self._get_mcp_greeks(ticker)
            
            if mcp_greeks:
                mcp_quality = self._validate_mcp_greeks(mcp_greeks)
                result['performance']['mcp_successful'] = True
                self.performance_stats['mcp_successes'] += 1
        
        # Step 2: Get calculated Greeks (always for validation/fallback)
        calculated_greeks = {}
        if self.bsm_available:
            calculated_greeks = self._get_calculated_greeks(ticker, option_chain)
        
        # Step 3: Choose primary source based on quality
        if (mcp_quality >= self.quality_thresholds['mcp_quality_threshold'] and 
            mcp_greeks and len(mcp_greeks) > 0):
            
            # Use MCP as primary source
            result['greeks'] = mcp_greeks
            result['data_source'] = 'mcp_real_time'
            result['quality_score'] = mcp_quality
            result['fallback_used'] = False
            
            # Cross-validate with calculated if available
            if calculated_greeks:
                validation = self._cross_validate_greeks(mcp_greeks, calculated_greeks)
                result['validation'] = validation
                self.performance_stats['cross_validations'] += 1
            
            self.logger.info(f"[{ticker}] Using MCP Greeks - Quality: {mcp_quality:.2f}")
            
        elif calculated_greeks and len(calculated_greeks) > 0:
            # Fallback to calculated Greeks
            result['greeks'] = calculated_greeks
            result['data_source'] = 'calculated_bsm'
            result['quality_score'] = 0.8
            result['fallback_used'] = True
            self.performance_stats['calculated_fallbacks'] += 1
            
            # Log specific fallback reason with clear messaging
            if not mcp_greeks:
                reason = "MCP Greeks data unavailable"
                self.logger.warning(f"[{ticker}] {reason} - using BSM calculated Greeks")
            elif mcp_quality < self.quality_thresholds['mcp_quality_threshold']:
                reason = f"MCP Greeks quality insufficient ({mcp_quality:.2f} < {self.quality_thresholds['mcp_quality_threshold']:.2f})"
                self.logger.warning(f"[{ticker}] {reason} - using BSM calculated Greeks")
                self.performance_stats['quality_failures'] += 1
            else:
                reason = "MCP Greeks validation failed"
                self.logger.warning(f"[{ticker}] {reason} - using BSM calculated Greeks")
        
        else:
            # No Greeks available from either source
            result['greeks'] = {}
            result['data_source'] = 'none'
            result['quality_score'] = 0.0
            result['fallback_used'] = True
            
            error_msg = "No Greeks available from MCP or BSM calculator"
            self.logger.error(f"[{ticker}] {error_msg}")
            
            # Add error details to result
            result['error'] = error_msg
            result['mcp_attempted'] = self.mcp_available
            result['bsm_attempted'] = self.bsm_available
        
        # Record performance metrics
        execution_time = (time.time() - start_time) * 1000
        result['performance']['execution_time_ms'] = round(execution_time, 2)
        
        return result
    
    def _get_mcp_greeks(self, ticker: str) -> Optional[Dict]:
        """Get real-time Greeks from MCP/Schwab broker data"""
        
        try:
            # Request real options data from MCP agent  
            options_result = self.mcp_agent.get_market_data([ticker])
            
            if not options_result or 'data' not in options_result:
                self.logger.warning(f"[{ticker}] MCP server unavailable - no data returned")
                return None
                
            ticker_data = options_result['data'].get(ticker, {})
            
            # Check if real options chain is available from MCP
            options_chain = ticker_data.get('options_chain', [])
            
            if not options_chain:
                self.logger.warning(f"[{ticker}] MCP has no options chain data - falling back to BSM calculations")
                return None
            
            if len(options_chain) < self.quality_thresholds['min_options_for_mcp']:
                self.logger.warning(f"[{ticker}] MCP options chain insufficient ({len(options_chain)} options) - falling back to BSM")
                return None
            
            # Extract and aggregate Greeks from real MCP options chain
            aggregated_greeks = self._aggregate_mcp_greeks(options_chain, ticker_data)
            
            if not aggregated_greeks:
                self.logger.warning(f"[{ticker}] No valid Greeks found in MCP options chain - falling back to BSM")
                return None
            
            # Add metadata
            aggregated_greeks['mcp_timestamp'] = datetime.now().isoformat()
            aggregated_greeks['mcp_options_count'] = len(options_chain)
            aggregated_greeks['mcp_source'] = options_result.get('source', 'unknown')
            
            return aggregated_greeks
            
        except Exception as e:
            self.logger.error(f"[{ticker}] MCP Greeks retrieval failed: {e} - falling back to BSM calculations")
            return None
    
    def _aggregate_mcp_greeks(self, options_chain: List[Dict], ticker_data: Dict) -> Dict:
        """Aggregate Greeks from MCP options chain data"""
        
        if not options_chain:
            return {}
        
        # Initialize aggregated data structure
        greeks = {}
        
        # Separate calls and puts for analysis
        calls = [opt for opt in options_chain if opt.get('type', '').lower() == 'call']
        puts = [opt for opt in options_chain if opt.get('type', '').lower() == 'put']
        
        # Get current underlying price for moneyness calculations
        current_price = ticker_data.get('last_price', ticker_data.get('close', 100))
        
        # Process each option type
        for option_type, options in [('call', calls), ('put', puts)]:
            if not options:
                continue
                
            # Extract Greeks with validation
            valid_options = []
            for opt in options:
                if self._is_valid_option_data(opt):
                    opt['moneyness'] = opt.get('strike', 0) / current_price if current_price > 0 else 1.0
                    valid_options.append(opt)
            
            if not valid_options:
                continue
            
            # Calculate weighted aggregates
            self._calculate_option_aggregates(valid_options, option_type, greeks)
        
        # Calculate cross-option metrics
        all_valid_options = []
        for opt in options_chain:
            if self._is_valid_option_data(opt):
                opt['moneyness'] = opt.get('strike', 0) / current_price if current_price > 0 else 1.0
                all_valid_options.append(opt)
        
        if all_valid_options:
            self._calculate_overall_metrics(all_valid_options, greeks)
        
        # Add derived metrics
        self._calculate_derived_greeks(greeks)
        
        return greeks
    
    def _is_valid_option_data(self, option: Dict) -> bool:
        """Validate individual option data quality"""
        
        required_fields = ['strike', 'type']
        if not all(field in option for field in required_fields):
            return False
        
        # Check for reasonable values
        strike = option.get('strike', 0)
        if strike <= 0 or strike > 10000:  # Reasonable strike range
            return False
        
        # Check Greeks if present
        delta = option.get('delta')
        if delta is not None:
            if not isinstance(delta, (int, float)) or abs(delta) > 1:
                return False
        
        gamma = option.get('gamma')
        if gamma is not None:
            if not isinstance(gamma, (int, float)) or gamma < 0:
                return False
        
        return True
    
    def _calculate_option_aggregates(self, options: List[Dict], option_type: str, greeks: Dict):
        """Calculate aggregated metrics for option type"""
        
        # Volume-weighted calculations where possible
        total_volume = sum(opt.get('volume', 1) for opt in options)
        
        greek_fields = ['delta', 'gamma', 'vanna', 'charm', 'vega', 'theta']
        
        for greek_field in greek_fields:
            values = [opt.get(greek_field) for opt in options if opt.get(greek_field) is not None]
            
            if not values:
                continue
            
            # Basic statistics
            greeks[f'{greek_field}_{option_type}_mean'] = np.mean(values)
            greeks[f'{greek_field}_{option_type}_sum'] = np.sum(values)
            greeks[f'{greek_field}_{option_type}_std'] = np.std(values)
            
            # Volume-weighted average if volume data available
            if total_volume > 0:
                weighted_values = []
                weights = []
                for opt in options:
                    if opt.get(greek_field) is not None:
                        weighted_values.append(opt[greek_field])
                        weights.append(opt.get('volume', 1))
                
                if weighted_values:
                    greeks[f'{greek_field}_{option_type}_weighted'] = np.average(weighted_values, weights=weights)
    
    def _calculate_overall_metrics(self, all_options: List[Dict], greeks: Dict):
        """Calculate overall cross-option metrics"""
        
        # Overall Greeks aggregates
        all_deltas = [opt.get('delta') for opt in all_options if opt.get('delta') is not None]
        all_gammas = [opt.get('gamma') for opt in all_options if opt.get('gamma') is not None]
        all_vannas = [opt.get('vanna') for opt in all_options if opt.get('vanna') is not None]
        
        if all_deltas:
            greeks['delta_overall_mean'] = np.mean(all_deltas)
            greeks['delta_overall_sum'] = np.sum(all_deltas)
            greeks['delta_skew'] = np.std(all_deltas)
            
        if all_gammas:
            greeks['gamma_overall_mean'] = np.mean(all_gammas)
            greeks['gamma_overall_sum'] = np.sum(all_gammas)
            greeks['gamma_concentration'] = np.sum(np.array(all_gammas) ** 2)
            
        if all_vannas:
            greeks['vanna_overall_mean'] = np.mean(all_vannas)
            greeks['vanna_overall_sum'] = np.sum(all_vannas)
        
        # Moneyness analysis
        atm_options = [opt for opt in all_options if 0.95 <= opt.get('moneyness', 1) <= 1.05]
        if atm_options:
            atm_deltas = [opt.get('delta') for opt in atm_options if opt.get('delta') is not None]
            atm_gammas = [opt.get('gamma') for opt in atm_options if opt.get('gamma') is not None]
            
            if atm_deltas:
                greeks['delta_atm_mean'] = np.mean(atm_deltas)
            if atm_gammas:
                greeks['gamma_atm_mean'] = np.mean(atm_gammas)
    
    def _calculate_derived_greeks(self, greeks: Dict):
        """Calculate derived Greek metrics"""
        
        # Net delta exposure
        call_delta = greeks.get('delta_call_sum', 0)
        put_delta = greeks.get('delta_put_sum', 0)
        greeks['net_delta_exposure'] = call_delta + put_delta
        
        # Gamma-adjusted delta
        gamma_sum = greeks.get('gamma_overall_sum', 0)
        delta_mean = greeks.get('delta_overall_mean', 0)
        if gamma_sum != 0:
            greeks['gamma_adjusted_delta'] = delta_mean * gamma_sum
        
        # Vanna-charm correlation proxy
        vanna_mean = greeks.get('vanna_overall_mean', 0)
        charm_call = greeks.get('charm_call_mean', 0)
        if vanna_mean != 0 and charm_call != 0:
            greeks['vanna_charm_ratio'] = vanna_mean / charm_call
    
    def _get_calculated_greeks(self, ticker: str, option_chain: Optional[Dict] = None) -> Dict:
        """Calculate Greeks using BSM formulas as fallback"""
        
        if not self.bsm_available:
            self.logger.warning(f"[{ticker}] BSM calculator unavailable")
            return {}
        
        try:
            if option_chain:
                # Calculate from provided option chain
                calculated = self.bsm_calculator.calculate_chain_greeks(ticker, option_chain)
            else:
                # Generate synthetic option chain and calculate
                calculated = self.bsm_calculator.calculate_synthetic_greeks(ticker)
            
            # Add metadata
            calculated['calculated_timestamp'] = datetime.now().isoformat()
            calculated['calculation_method'] = 'bsm_synthetic' if not option_chain else 'bsm_chain'
            
            return calculated
            
        except Exception as e:
            self.logger.error(f"[{ticker}] BSM Greeks calculation failed: {e}")
            return {}
    
    def _validate_mcp_greeks(self, mcp_greeks: Dict) -> float:
        """Validate MCP Greeks data quality with comprehensive scoring"""
        
        if not mcp_greeks:
            return 0.0
        
        quality_score = 0.0
        total_weight = 0.0
        
        # Check 1: Data completeness (40% weight)
        completeness_weight = 0.4
        essential_fields = ['delta_overall_mean', 'gamma_overall_mean']
        important_fields = ['delta_call_mean', 'delta_put_mean', 'gamma_call_mean', 'gamma_put_mean']
        optional_fields = ['vanna_overall_mean', 'charm_call_mean']
        
        essential_present = sum(1 for field in essential_fields if field in mcp_greeks and mcp_greeks[field] is not None)
        important_present = sum(1 for field in important_fields if field in mcp_greeks and mcp_greeks[field] is not None)
        optional_present = sum(1 for field in optional_fields if field in mcp_greeks and mcp_greeks[field] is not None)
        
        completeness = (
            (essential_present / len(essential_fields)) * 0.6 +
            (important_present / len(important_fields)) * 0.3 +
            (optional_present / len(optional_fields)) * 0.1
        )
        
        quality_score += completeness * completeness_weight
        total_weight += completeness_weight
        
        # Check 2: Value reasonableness (35% weight)
        reasonableness_weight = 0.35
        reasonable_checks = 0
        total_checks = 0
        
        # Delta reasonableness (-1 <= delta <= 1)
        for key, value in mcp_greeks.items():
            if 'delta' in key and isinstance(value, (int, float)):
                total_checks += 1
                if -1 <= value <= 1:
                    reasonable_checks += 1
        
        # Gamma reasonableness (gamma >= 0)
        for key, value in mcp_greeks.items():
            if 'gamma' in key and isinstance(value, (int, float)):
                total_checks += 1
                if value >= 0:
                    reasonable_checks += 1
        
        # Vanna reasonableness (reasonable range)
        for key, value in mcp_greeks.items():
            if 'vanna' in key and isinstance(value, (int, float)):
                total_checks += 1
                if -10 <= value <= 10:  # Reasonable vanna range
                    reasonable_checks += 1
        
        reasonableness = reasonable_checks / total_checks if total_checks > 0 else 0
        quality_score += reasonableness * reasonableness_weight
        total_weight += reasonableness_weight
        
        # Check 3: Data freshness (15% weight)
        freshness_weight = 0.15
        freshness_score = 1.0  # Default to fresh if no timestamp
        
        mcp_timestamp = mcp_greeks.get('mcp_timestamp')
        if mcp_timestamp:
            try:
                timestamp_dt = datetime.fromisoformat(mcp_timestamp.replace('Z', '+00:00'))
                age_seconds = (datetime.now() - timestamp_dt.replace(tzinfo=None)).total_seconds()
                
                if age_seconds <= self.quality_thresholds['staleness_limit_seconds']:
                    freshness_score = 1.0
                else:
                    # Gradual degradation for older data
                    freshness_score = max(0, 1.0 - (age_seconds - self.quality_thresholds['staleness_limit_seconds']) / 300)
            except:
                freshness_score = 0.5  # Partial credit for unparseable timestamp
        
        quality_score += freshness_score * freshness_weight
        total_weight += freshness_weight
        
        # Check 4: Options chain depth (10% weight)
        depth_weight = 0.1
        options_count = mcp_greeks.get('mcp_options_count', 0)
        
        if options_count >= 20:
            depth_score = 1.0
        elif options_count >= 10:
            depth_score = 0.8
        elif options_count >= 5:
            depth_score = 0.6
        else:
            depth_score = 0.3
        
        quality_score += depth_score * depth_weight
        total_weight += depth_weight
        
        final_quality = quality_score / total_weight if total_weight > 0 else 0.0
        
        return min(1.0, max(0.0, final_quality))
    
    def _cross_validate_greeks(self, mcp_greeks: Dict, calculated_greeks: Dict) -> Dict:
        """Cross-validate MCP Greeks against calculated Greeks"""
        
        validation = {
            'correlation_score': 0.0,
            'agreements': {},
            'discrepancies': [],
            'validation_timestamp': datetime.now().isoformat()
        }
        
        # Validation pairs to check
        validation_pairs = [
            ('delta_overall_mean', 'delta_tolerance'),
            ('gamma_overall_mean', 'gamma_tolerance'),
            ('vanna_overall_mean', 'vanna_tolerance'),
            ('charm_call_mean', 'charm_tolerance')
        ]
        
        total_validations = 0
        successful_validations = 0
        
        for greek_field, tolerance_key in validation_pairs:
            mcp_value = mcp_greeks.get(greek_field)
            calc_value = calculated_greeks.get(greek_field)
            
            if mcp_value is not None and calc_value is not None:
                total_validations += 1
                tolerance = self.quality_thresholds[tolerance_key]
                
                # Calculate relative difference
                if abs(calc_value) > 0.001:  # Avoid division by very small numbers
                    relative_diff = abs(mcp_value - calc_value) / abs(calc_value)
                else:
                    relative_diff = abs(mcp_value - calc_value)
                
                is_agreement = relative_diff <= tolerance
                validation['agreements'][greek_field] = is_agreement
                
                if is_agreement:
                    successful_validations += 1
                else:
                    validation['discrepancies'].append(
                        f"{greek_field}: MCP={mcp_value:.6f}, Calc={calc_value:.6f}, "
                        f"RelDiff={relative_diff:.3f} > {tolerance:.3f}"
                    )
        
        # Calculate overall correlation score
        if total_validations > 0:
            validation['correlation_score'] = successful_validations / total_validations
        
        validation['total_comparisons'] = total_validations
        validation['successful_comparisons'] = successful_validations
        
        return validation
    
    def get_greeks_with_roc(self, ticker: str, lookback_periods: int = 5) -> Dict:
        """
        Get Greeks with Rate-of-Change calculations
        Combines MCP real-time with historical ROC analysis
        """
        
        # Get current enhanced Greeks
        current_result = self.get_enhanced_greeks(ticker)
        
        # Calculate ROC derivatives (placeholder for historical integration)
        try:
            roc_greeks = self._calculate_greeks_roc(ticker, lookback_periods)
            current_result['roc_derivatives'] = roc_greeks
        except Exception as e:
            self.logger.error(f"[{ticker}] ROC calculation failed: {e}")
            current_result['roc_derivatives'] = {}
        
        return current_result
    
    def _calculate_greeks_roc(self, ticker: str, periods: int) -> Dict:
        """Calculate Rate-of-Change for Greeks over specified periods"""
        
        # Placeholder implementation - would integrate with historical storage
        # This would fetch historical Greeks and calculate ROC
        
        roc_data = {
            'gamma_roc': 0.0,
            'gamma_roc_2': 0.0,  # Second order ROC
            'vanna_roc': 0.0,
            'vanna_roc_2': 0.0,
            'charm_roc': 0.0,
            'charm_roc_2': 0.0,
            'delta_roc': 0.0,
            'iv_roc': 0.0,
            'periods_analyzed': periods,
            'roc_timestamp': datetime.now().isoformat()
        }
        
        self.logger.debug(f"[{ticker}] ROC calculation placeholder - {periods} periods")
        
        return roc_data
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics for monitoring"""
        
        total_requests = self.performance_stats['mcp_requests']
        
        stats = self.performance_stats.copy()
        
        if total_requests > 0:
            stats['mcp_success_rate'] = self.performance_stats['mcp_successes'] / total_requests
            stats['fallback_rate'] = self.performance_stats['calculated_fallbacks'] / total_requests
            stats['quality_failure_rate'] = self.performance_stats['quality_failures'] / total_requests
        else:
            stats['mcp_success_rate'] = 0.0
            stats['fallback_rate'] = 0.0
            stats['quality_failure_rate'] = 0.0
        
        return stats


# Enhanced Agent Mixin for Greeks Integration
class GreeksEnhancedMixin:
    """
    Enhanced agent mixin for Greeks integration
    Use this pattern in IV Dynamics, Anomaly Detector, Math Validator, etc.
    """
    
    def __init__(self, greeks_engine: Optional[Any] = None):
        if not hasattr(self, '_greeks_engine'):
            if greeks_engine:
                self._greeks_engine = greeks_engine
            else:
                self._greeks_engine = EnhancedGreeksEngine()
            if hasattr(self, 'logger'):
                self.logger.info("Agent enhanced with MCP+Calculated Greeks capability")
    
    def get_agent_greeks(self, ticker: str) -> Dict:
        """Get Greeks optimized for agent use with quality scoring"""
        
        # Get enhanced Greeks with quality validation
        greeks_result = self._greeks_engine.get_enhanced_greeks(ticker)
        
        # Log data source and quality for transparency
        source = greeks_result['data_source']
        quality = greeks_result['quality_score']
        execution_time = greeks_result['performance']['execution_time_ms']
        
        if hasattr(self, 'logger'):
            self.logger.info(f"[{ticker}] Greeks: {source}, Quality: {quality:.2f}, Time: {execution_time}ms")
        
        return greeks_result['greeks']
    
    def get_agent_greeks_with_roc(self, ticker: str, periods: int = 5) -> Dict:
        """Get Greeks with ROC derivatives for advanced analysis"""
        
        full_result = self._greeks_engine.get_greeks_with_roc(ticker, periods)
        
        # Combine current Greeks with ROC derivatives
        combined = full_result['greeks'].copy()
        combined.update(full_result.get('roc_derivatives', {}))
        
        # Add metadata for agent use
        combined['_data_source'] = full_result['data_source']
        combined['_quality_score'] = full_result['quality_score']
        combined['_fallback_used'] = full_result['fallback_used']
        
        return combined
    
    def get_greeks_quality_info(self, ticker: str) -> Dict:
        """Get detailed quality information about Greeks data"""
        
        result = self._greeks_engine.get_enhanced_greeks(ticker)
        
        return {
            'data_source': result['data_source'],
            'quality_score': result['quality_score'],
            'fallback_used': result['fallback_used'],
            'validation': result.get('validation', {}),
            'performance': result['performance']
        }
