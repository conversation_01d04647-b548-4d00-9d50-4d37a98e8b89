#!/usr/bin/env python3
"""
Real-Time System Health Monitor
Monitors agent performance, data pipelines, execution systems, and API connectivity
"""

import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import deque
import json
import logging

@dataclass
class HealthMetrics:
    """System health metrics snapshot"""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_latency: float
    active_threads: int
    error_rate: float
    response_time_avg: float

@dataclass
class AgentHealthStatus:
    """Individual agent health status"""
    agent_id: str
    status: str  # 'healthy', 'warning', 'critical', 'offline'
    last_heartbeat: datetime
    response_time: float
    error_count: int
    success_rate: float
    memory_usage: float
    cpu_usage: float

@dataclass
class APIConnectionStatus:
    """API connection health status"""
    api_name: str
    endpoint: str
    status: str  # 'connected', 'degraded', 'failed', 'timeout'
    response_time: float
    last_successful_call: datetime
    error_count: int
    success_rate: float
    rate_limit_remaining: int

class SystemHealthMonitor:
    """
    Real-time system health monitoring
    Tracks all critical system components with mathematical precision
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.monitoring_interval = self.config.get('monitoring_interval', 5)  # seconds
        self.history_size = self.config.get('history_size', 1000)
        self.alert_thresholds = self.config.get('alert_thresholds', {
            'cpu_critical': 85.0,
            'memory_critical': 90.0,
            'disk_critical': 95.0,
            'response_time_critical': 5.0,
            'error_rate_critical': 0.05,
            'api_timeout': 10.0
        })
        
        # Monitoring data storage
        self.health_history = deque(maxlen=self.history_size)
        self.agent_statuses = {}
        self.api_statuses = {}
        self.data_pipeline_status = {}
        self.execution_system_status = {}
        
        # Monitoring flags
        self.monitoring_active = False
        self.monitor_thread = None
        
        # Performance tracking
        self.performance_counters = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0
        }
        
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self):
        """Start real-time system monitoring"""
        if self.monitoring_active:
            return
            
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("System health monitoring started")
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=10)
        self.logger.info("System health monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                # Collect system metrics
                health_metrics = self._collect_system_metrics()
                self.health_history.append(health_metrics)
                
                # Update agent statuses
                self._update_agent_health()
                
                # Update API connection statuses
                self._update_api_health()
                
                # Update data pipeline health
                self._update_data_pipeline_health()
                
                # Update execution system health
                self._update_execution_system_health()
                
                # Check for alerts
                self._check_alert_conditions()
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
            
            time.sleep(self.monitoring_interval)
    
    def _collect_system_metrics(self) -> HealthMetrics:
        """Collect current system metrics"""
        try:
            # System resource usage
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network metrics (simplified)
            network_latency = self._measure_network_latency()
            
            # Thread count
            active_threads = threading.active_count()
            
            # Calculate error rate and response time
            error_rate = self._calculate_error_rate()
            response_time = self._calculate_avg_response_time()
            
            return HealthMetrics(
                timestamp=datetime.now(),
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                network_latency=network_latency,
                active_threads=active_threads,
                error_rate=error_rate,
                response_time_avg=response_time
            )
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            return HealthMetrics(
                timestamp=datetime.now(),
                cpu_usage=0.0, memory_usage=0.0, disk_usage=0.0,
                network_latency=0.0, active_threads=0, error_rate=1.0,
                response_time_avg=0.0
            )
    
    def _measure_network_latency(self) -> float:
        """Measure network latency (simplified ping)"""
        try:
            import subprocess
            result = subprocess.run(['ping', '-c', '1', '8.8.8.8'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # Parse ping output for latency
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'time=' in line:
                        time_part = line.split('time=')[1].split()[0]
                        return float(time_part)
            return 0.0
        except Exception:
            return 0.0
    
    def _calculate_error_rate(self) -> float:
        """Calculate current error rate"""
        total = self.performance_counters['total_requests']
        failed = self.performance_counters['failed_requests']
        return failed / total if total > 0 else 0.0
    
    def _calculate_avg_response_time(self) -> float:
        """Calculate average response time"""
        return self.performance_counters['avg_response_time']
    
    def register_agent(self, agent_id: str):
        """Register an agent for health monitoring"""
        self.agent_statuses[agent_id] = AgentHealthStatus(
            agent_id=agent_id,
            status='healthy',
            last_heartbeat=datetime.now(),
            response_time=0.0,
            error_count=0,
            success_rate=1.0,
            memory_usage=0.0,
            cpu_usage=0.0
        )
        self.logger.info(f"Registered agent for monitoring: {agent_id}")
    
    def update_agent_heartbeat(self, agent_id: str, response_time: float = 0.0, 
                              success: bool = True):
        """Update agent heartbeat and performance metrics"""
        if agent_id not in self.agent_statuses:
            self.register_agent(agent_id)
        
        agent = self.agent_statuses[agent_id]
        agent.last_heartbeat = datetime.now()
        agent.response_time = response_time
        
        if success:
            agent.success_rate = min(1.0, agent.success_rate * 0.95 + 0.05)
        else:
            agent.error_count += 1
            agent.success_rate = max(0.0, agent.success_rate * 0.95)
        
        # Update status based on metrics
        agent.status = self._determine_agent_status(agent)
    
    def _determine_agent_status(self, agent: AgentHealthStatus) -> str:
        """Determine agent status based on metrics"""
        time_since_heartbeat = (datetime.now() - agent.last_heartbeat).total_seconds()
        
        if time_since_heartbeat > 300:  # 5 minutes
            return 'offline'
        elif agent.success_rate < 0.5 or agent.response_time > 10.0:
            return 'critical'
        elif agent.success_rate < 0.8 or agent.response_time > 5.0:
            return 'warning'
        else:
            return 'healthy'
    
    def _update_agent_health(self):
        """Update all agent health statuses"""
        for agent_id, agent in self.agent_statuses.items():
            agent.status = self._determine_agent_status(agent)
    
    def register_api(self, api_name: str, endpoint: str):
        """Register API for connection monitoring"""
        self.api_statuses[api_name] = APIConnectionStatus(
            api_name=api_name,
            endpoint=endpoint,
            status='connected',
            response_time=0.0,
            last_successful_call=datetime.now(),
            error_count=0,
            success_rate=1.0,
            rate_limit_remaining=1000
        )
        self.logger.info(f"Registered API for monitoring: {api_name}")
    
    def update_api_status(self, api_name: str, response_time: float, 
                         success: bool, rate_limit_remaining: int = None):
        """Update API connection status"""
        if api_name not in self.api_statuses:
            return
        
        api = self.api_statuses[api_name]
        api.response_time = response_time
        
        if success:
            api.last_successful_call = datetime.now()
            api.success_rate = min(1.0, api.success_rate * 0.95 + 0.05)
        else:
            api.error_count += 1
            api.success_rate = max(0.0, api.success_rate * 0.95)
        
        if rate_limit_remaining is not None:
            api.rate_limit_remaining = rate_limit_remaining
        
        # Update status
        api.status = self._determine_api_status(api)
    
    def _determine_api_status(self, api: APIConnectionStatus) -> str:
        """Determine API status based on metrics"""
        time_since_success = (datetime.now() - api.last_successful_call).total_seconds()
        
        if time_since_success > 600:  # 10 minutes
            return 'failed'
        elif api.response_time > self.alert_thresholds['api_timeout']:
            return 'timeout'
        elif api.success_rate < 0.8 or api.rate_limit_remaining < 10:
            return 'degraded'
        else:
            return 'connected'
    
    def _update_api_health(self):
        """Update all API health statuses"""
        for api_name, api in self.api_statuses.items():
            api.status = self._determine_api_status(api)
    
    def _update_data_pipeline_health(self):
        """Update data pipeline health status"""
        # Monitor data flow components
        self.data_pipeline_status = {
            'market_data_feed': self._check_market_data_health(),
            'options_data_feed': self._check_options_data_health(),
            'data_validation': self._check_data_validation_health(),
            'data_storage': self._check_data_storage_health()
        }
    
    def _update_execution_system_health(self):
        """Update execution system health status"""
        # Monitor execution components
        self.execution_system_status = {
            'position_sizer': self._check_position_sizer_health(),
            'execution_optimizer': self._check_execution_optimizer_health(),
            'roi_calculator': self._check_roi_calculator_health(),
            'broker_connections': self._check_broker_connections_health()
        }
    
    def _check_market_data_health(self) -> str:
        """Check market data feed health"""
        # Simplified health check - in production would check actual data feeds
        return 'healthy'
    
    def _check_options_data_health(self) -> str:
        """Check options data feed health"""
        return 'healthy'
    
    def _check_data_validation_health(self) -> str:
        """Check data validation health"""
        return 'healthy'
    
    def _check_data_storage_health(self) -> str:
        """Check data storage health"""
        return 'healthy'
    
    def _check_position_sizer_health(self) -> str:
        """Check position sizer health"""
        try:
            from execution.position_sizer import PositionSizer
            sizer = PositionSizer()
            return 'healthy'
        except Exception:
            return 'critical'
    
    def _check_execution_optimizer_health(self) -> str:
        """Check execution optimizer health"""
        try:
            from execution.execution_optimizer import ExecutionOptimizer
            optimizer = ExecutionOptimizer()
            return 'healthy'
        except Exception:
            return 'critical'
    
    def _check_roi_calculator_health(self) -> str:
        """Check ROI calculator health"""
        try:
            from execution.roi_calculator import ROICalculator
            calculator = ROICalculator()
            return 'healthy'
        except Exception:
            return 'critical'
    
    def _check_broker_connections_health(self) -> str:
        """Check broker connection health"""
        # Check registered APIs
        broker_apis = [api for api in self.api_statuses if 'broker' in api.lower()]
        if not broker_apis:
            return 'warning'
        
        healthy_count = sum(1 for api in broker_apis 
                           if self.api_statuses[api].status == 'connected')
        
        if healthy_count == 0:
            return 'critical'
        elif healthy_count < len(broker_apis):
            return 'warning'
        else:
            return 'healthy'
    
    def _check_alert_conditions(self):
        """Check for alert conditions"""
        if not self.health_history:
            return
        
        latest = self.health_history[-1]
        alerts = []
        
        # System resource alerts
        if latest.cpu_usage > self.alert_thresholds['cpu_critical']:
            alerts.append(f"CRITICAL: CPU usage {latest.cpu_usage:.1f}%")
        
        if latest.memory_usage > self.alert_thresholds['memory_critical']:
            alerts.append(f"CRITICAL: Memory usage {latest.memory_usage:.1f}%")
        
        if latest.disk_usage > self.alert_thresholds['disk_critical']:
            alerts.append(f"CRITICAL: Disk usage {latest.disk_usage:.1f}%")
        
        if latest.response_time_avg > self.alert_thresholds['response_time_critical']:
            alerts.append(f"CRITICAL: Response time {latest.response_time_avg:.2f}s")
        
        if latest.error_rate > self.alert_thresholds['error_rate_critical']:
            alerts.append(f"CRITICAL: Error rate {latest.error_rate:.1%}")
        
        # Agent alerts
        for agent_id, agent in self.agent_statuses.items():
            if agent.status in ['critical', 'offline']:
                alerts.append(f"AGENT {agent.status.upper()}: {agent_id}")
        
        # API alerts
        for api_name, api in self.api_statuses.items():
            if api.status in ['failed', 'timeout']:
                alerts.append(f"API {api.status.upper()}: {api_name}")
        
        # Log alerts
        for alert in alerts:
            self.logger.warning(alert)
    
    def get_system_health_summary(self) -> Dict[str, Any]:
        """Get comprehensive system health summary"""
        if not self.health_history:
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_status': 'unknown',
                'system_metrics': {},
                'agent_health': {aid: asdict(agent) for aid, agent in self.agent_statuses.items()},
                'api_health': {aid: asdict(api) for aid, api in self.api_statuses.items()},
                'data_pipeline_health': self.data_pipeline_status,
                'execution_system_health': self.execution_system_status,
                'performance_counters': self.performance_counters
            }
        
        latest = self.health_history[-1]
        
        return {
            'timestamp': latest.timestamp.isoformat(),
            'overall_status': self._calculate_overall_status(),
            'system_metrics': asdict(latest),
            'agent_health': {aid: asdict(agent) for aid, agent in self.agent_statuses.items()},
            'api_health': {aid: asdict(api) for aid, api in self.api_statuses.items()},
            'data_pipeline_health': self.data_pipeline_status,
            'execution_system_health': self.execution_system_status,
            'performance_counters': self.performance_counters
        }
    
    def _calculate_overall_status(self) -> str:
        """Calculate overall system status"""
        if not self.health_history:
            return 'unknown'
        
        latest = self.health_history[-1]
        
        # Check critical thresholds
        if (latest.cpu_usage > self.alert_thresholds['cpu_critical'] or
            latest.memory_usage > self.alert_thresholds['memory_critical'] or
            latest.error_rate > self.alert_thresholds['error_rate_critical']):
            return 'critical'
        
        # Check agent statuses
        agent_critical = any(agent.status == 'critical' for agent in self.agent_statuses.values())
        if agent_critical:
            return 'critical'
        
        # Check API statuses
        api_failed = any(api.status == 'failed' for api in self.api_statuses.values())
        if api_failed:
            return 'degraded'
        
        # Check warning conditions
        agent_warning = any(agent.status == 'warning' for agent in self.agent_statuses.values())
        api_warning = any(api.status in ['degraded', 'timeout'] for api in self.api_statuses.values())
        
        if agent_warning or api_warning:
            return 'warning'
        
        return 'healthy'

if __name__ == "__main__":
    # Test the system health monitor
    monitor = SystemHealthMonitor()
    
    # Register test components
    monitor.register_agent("test_agent")
    monitor.register_api("schwab_api", "https://api.schwabapi.com")
    
    # Start monitoring
    monitor.start_monitoring()
    
    # Simulate some activity
    time.sleep(10)
    
    # Get health summary
    summary = monitor.get_system_health_summary()
    print(json.dumps(summary, indent=2, default=str))
    
    # Stop monitoring
    monitor.stop_monitoring()
