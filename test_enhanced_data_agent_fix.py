#!/usr/bin/env python3
"""
Test Enhanced Data Agent Fix
Verify that endpoint mismatch is resolved and data flows correctly
"""

import sys
import json
from pathlib import Path

# Add current directory to path
sys.path.append(str(Path(__file__).parent))

def test_enhanced_data_agent():
    """Test the enhanced data agent after endpoint fix"""
    print("TESTING ENHANCED DATA AGENT FIX - TICKER AGNOSTIC")
    print("=" * 50)
    
    try:
        from enhanced_data_agent_broker_integration import EnhancedDataAgent
        
        # Create agent instance
        agent = EnhancedDataAgent()
        print("PASS - EnhancedDataAgent imported successfully")
        
        # Test multiple tickers
        test_tickers = ["SPY", "AAPL", "MSFT", "RANDOM123", "TEST"]
        
        for ticker in test_tickers:
            print(f"\nTesting data fetch for {ticker}...")
            data = agent.get_market_data(ticker)
            
            if data:
                print(f"PASS - Data fetch successful for {ticker}!")
                data_info = data.get('data', {})
                print(f"PASS - Symbol: {data_info.get('ticker', 'N/A')}")
                print(f"PASS - Last Price: ${data_info.get('last_price', 'N/A')}")
                print(f"PASS - Bid: ${data_info.get('bid', 'N/A')}")
                print(f"PASS - Ask: ${data_info.get('ask', 'N/A')}")
                volume = data_info.get('volume', 'N/A')
                if volume != 'N/A':
                    print(f"PASS - Volume: {volume:,}")
                else:
                    print(f"PASS - Volume: {volume}")
                print(f"PASS - Source: {data.get('source', 'N/A')}")
                
                # Check if we're getting real-time data
                source = data.get('source', '')
                if source == 'schwab_broker':
                    print(f"EXCELLENT - {ticker} using real-time broker data!")
                else:
                    print(f"WARN - {ticker} using fallback data: {source}")
                    
            else:
                print(f"FAIL - Data fetch failed for {ticker} - no data returned")
        
        return True
        
    except Exception as e:
        print(f"FAIL - Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_data_agent()
    if success:
        print("\nSUCCESS - ALL TESTS PASSED - Enhanced Data Agent Fix Successful!")
    else:
        print("\nFAILED - TESTS FAILED - Need further investigation")
