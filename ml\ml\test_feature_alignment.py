"""
ML Feature Alignment Test - Determine Exact 97 Features

This script will test the ML models to determine exactly which 97 features
they expect, then fix the feature extractor accordingly.
"""

import os
import sys
import pandas as pd
import numpy as np
import joblib
import logging

# Add the parent directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_ml_model_features():
    """Test ML models to determine exact feature requirements."""
    
    # Try multiple possible model directories
    possible_dirs = [
        os.path.join(os.path.dirname(__file__), 'models'),
        os.path.join(os.path.dirname(__file__), '..', 'models'),
        os.path.join(os.path.dirname(__file__), '..', 'ml', 'models')
    ]
    
    models_dir = None
    for dir_path in possible_dirs:
        if os.path.exists(dir_path):
            models_dir = dir_path
            logger.info(f"Found models directory: {models_dir}")
            break
    
    if not models_dir:
        logger.error("No models directory found")
        return {'models_tested': [], 'feature_counts': {}, 'feature_names': {}, 'alignment_issues': ['No models directory found']}
    
    results = {
        'models_tested': [],
        'feature_counts': {},
        'feature_names': {},
        'alignment_issues': []
    }
    
    # Test level_strength_model.pkl
    try:
        model_path = os.path.join(models_dir, 'level_strength_model.pkl')
        if os.path.exists(model_path):
            logger.info("Testing level_strength_model.pkl")
            model = joblib.load(model_path)
            
            # Try to get feature names
            feature_names = None
            feature_count = None
            
            if hasattr(model, 'feature_names_in_'):
                feature_names = list(model.feature_names_in_)
                feature_count = len(feature_names)
            elif hasattr(model, 'n_features_in_'):
                feature_count = model.n_features_in_
            
            results['models_tested'].append('level_strength_model.pkl')
            results['feature_counts']['level_strength_model'] = feature_count
            results['feature_names']['level_strength_model'] = feature_names
            
            logger.info(f"level_strength_model expects {feature_count} features")
            
        else:
            logger.warning("level_strength_model.pkl not found")
            
    except Exception as e:
        logger.error(f"Error testing level_strength_model.pkl: {e}")
        results['alignment_issues'].append(f"level_strength_model error: {e}")
    
    # Test price_reaction_model.pkl
    try:
        model_path = os.path.join(models_dir, 'price_reaction_model.pkl')
        if os.path.exists(model_path):
            logger.info("Testing price_reaction_model.pkl")
            model = joblib.load(model_path)
            
            # Try to get feature names
            feature_names = None
            feature_count = None
            
            if hasattr(model, 'feature_names_in_'):
                feature_names = list(model.feature_names_in_)
                feature_count = len(feature_names)
            elif hasattr(model, 'n_features_in_'):
                feature_count = model.n_features_in_
            
            results['models_tested'].append('price_reaction_model.pkl')
            results['feature_counts']['price_reaction_model'] = feature_count
            results['feature_names']['price_reaction_model'] = feature_names
            
            logger.info(f"price_reaction_model expects {feature_count} features")
            
        else:
            logger.warning("price_reaction_model.pkl not found")
            
    except Exception as e:
        logger.error(f"Error testing price_reaction_model.pkl: {e}")
        results['alignment_issues'].append(f"price_reaction_model error: {e}")
    
    # Test with current_features.txt
    try:
        features_file = os.path.join(models_dir, 'current_features.txt')
        if os.path.exists(features_file):
            with open(features_file, 'r') as f:
                features_from_file = [line.strip() for line in f if line.strip()]
            
            results['feature_counts']['current_features.txt'] = len(features_from_file)
            results['feature_names']['current_features.txt'] = features_from_file
            
            logger.info(f"current_features.txt contains {len(features_from_file)} features")
            
        else:
            logger.warning("current_features.txt not found")
            
    except Exception as e:
        logger.error(f"Error reading current_features.txt: {e}")
        results['alignment_issues'].append(f"current_features.txt error: {e}")
    
    return results

def create_test_feature_dataframe(feature_count: int):
    """Create a test DataFrame with the specified number of features."""
    try:
        # Create test data
        test_data = np.random.rand(1, feature_count)
        feature_names = [f"feature_{i}" for i in range(feature_count)]
        test_df = pd.DataFrame(test_data, columns=feature_names)
        
        return test_df
    except Exception as e:
        logger.error(f"Error creating test DataFrame: {e}")
        return None

def main():
    logger.info("Starting ML Feature Alignment Test")
    logger.info("=" * 50)
    
    # Test models
    results = test_ml_model_features()
    
    # Print results
    print("\nML MODEL FEATURE ANALYSIS")
    print("=" * 50)
    
    for model_name in results['models_tested']:
        feature_count = results['feature_counts'].get(model_name, 'Unknown')
        print(f"{model_name}: {feature_count} features")
        
        feature_names = results['feature_names'].get(model_name)
        if feature_names and len(feature_names) <= 10:
            print(f"  Features: {feature_names}")
        elif feature_names:
            print(f"  First 10 features: {feature_names[:10]}")
            print(f"  Last 10 features: {feature_names[-10:]}")
    
    # Check current_features.txt
    txt_count = results['feature_counts'].get('current_features.txt', 'Unknown')
    print(f"\ncurrent_features.txt: {txt_count} features")
    
    # Summary
    print("\nSUMMARY")
    print("-" * 30)
    
    feature_counts = [count for count in results['feature_counts'].values() if isinstance(count, int)]
    if feature_counts:
        if len(set(feature_counts)) == 1:
            print(f"+ All sources agree: {feature_counts[0]} features")
        else:
            print(f"X Misalignment detected!")
            for source, count in results['feature_counts'].items():
                print(f"  {source}: {count}")
    
    # Check for issues
    if results['alignment_issues']:
        print("\nISSUES DETECTED:")
        for issue in results['alignment_issues']:
            print(f"  - {issue}")
    
    # Recommendations
    print("\nRECOMMENDATIONS:")
    
    model_counts = []
    for model in ['level_strength_model', 'price_reaction_model']:
        if model in results['feature_counts']:
            model_counts.append(results['feature_counts'][model])
    
    if model_counts and len(set(model_counts)) == 1:
        expected_count = model_counts[0]
        txt_count = results['feature_counts'].get('current_features.txt', 0)
        
        if txt_count == expected_count:
            print(f"  + Perfect alignment - all sources expect {expected_count} features")
        else:
            print(f"  X Fix current_features.txt - models expect {expected_count}, file has {txt_count}")
    else:
        print("  ! Model alignment issues detected - investigate model training")
    
    return results

if __name__ == "__main__":
    results = main()
