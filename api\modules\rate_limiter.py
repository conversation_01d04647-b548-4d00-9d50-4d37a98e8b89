#!/usr/bin/env python3
"""
Rate Limiter Module - Mathematical precision for API throttling
Statistical analysis of rate limit compliance
Zero-tolerance failure policy
"""

import time
import asyncio
from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass
import logging

class RateLimitTier(Enum):
    """API tier enumeration with mathematical constraints."""
    FREE = "free"
    BASIC = "basic" 
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"

@dataclass
class RateLimitConfig:
    """Rate limit configuration with mathematical validation."""
    tier: RateLimitTier
    requests_per_minute: int
    requests_per_hour: int
    burst_capacity: int
    
    def __post_init__(self):
        """Mathematical validation of rate limits."""
        if self.requests_per_minute <= 0:
            raise ValueError("Requests per minute must be positive")
        if self.requests_per_hour <= 0:
            raise ValueError("Requests per hour must be positive")
        if self.burst_capacity <= 0:
            raise ValueError("Burst capacity must be positive")
        
        # Validate mathematical consistency
        if self.requests_per_hour < self.requests_per_minute:
            raise ValueError("Hour limit cannot be less than minute limit")

class TokenBucket:
    """Token bucket algorithm for precise rate limiting."""
    
    def __init__(self, capacity: int, refill_rate: float):
        """Initialize token bucket with mathematical precision."""
        self.capacity = capacity
        self.tokens = capacity
        self.refill_rate = refill_rate  # tokens per second
        self.last_refill = time.time()
        self._lock = asyncio.Lock()
    
    async def consume(self, tokens: int = 1) -> bool:
        """Consume tokens with thread-safe precision."""
        async with self._lock:
            self._refill()
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    def _refill(self):
        """Refill tokens based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_refill
        
        # Calculate tokens to add
        tokens_to_add = elapsed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now
    
    def get_status(self) -> Dict[str, Any]:
        """Get current bucket status."""
        self._refill()
        return {
            'tokens': self.tokens,
            'capacity': self.capacity,
            'refill_rate': self.refill_rate,
            'utilization': 1.0 - (self.tokens / self.capacity)
        }

class RateLimiter:
    """Production rate limiter with mathematical precision."""
    
    TIER_CONFIGS = {
        RateLimitTier.FREE: RateLimitConfig(
            tier=RateLimitTier.FREE,
            requests_per_minute=5,
            requests_per_hour=100,
            burst_capacity=3
        ),
        RateLimitTier.BASIC: RateLimitConfig(
            tier=RateLimitTier.BASIC,
            requests_per_minute=100,
            requests_per_hour=5000,
            burst_capacity=50
        ),
        RateLimitTier.PROFESSIONAL: RateLimitConfig(
            tier=RateLimitTier.PROFESSIONAL,
            requests_per_minute=1000,
            requests_per_hour=50000,
            burst_capacity=500
        )
    }
    
    def __init__(self, tier: RateLimitTier):
        """Initialize rate limiter for specified tier."""
        self.config = self.TIER_CONFIGS[tier]
        self.minute_bucket = TokenBucket(
            capacity=self.config.burst_capacity,
            refill_rate=self.config.requests_per_minute / 60.0
        )
        self.hour_bucket = TokenBucket(
            capacity=self.config.requests_per_hour,
            refill_rate=self.config.requests_per_hour / 3600.0
        )
        self.logger = logging.getLogger(__name__)
    
    async def acquire(self, tokens: int = 1) -> bool:
        """Acquire rate limit tokens."""
        minute_ok = await self.minute_bucket.consume(tokens)
        hour_ok = await self.hour_bucket.consume(tokens)
        
        success = minute_ok and hour_ok
        
        if not success:
            self.logger.warning(f"Rate limit exceeded for tier {self.config.tier.value}")
        
        return success
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get rate limiting statistics."""
        return {
            'tier': self.config.tier.value,
            'config': {
                'requests_per_minute': self.config.requests_per_minute,
                'requests_per_hour': self.config.requests_per_hour,
                'burst_capacity': self.config.burst_capacity
            },
            'minute_bucket': self.minute_bucket.get_status(),
            'hour_bucket': self.hour_bucket.get_status()
        }
