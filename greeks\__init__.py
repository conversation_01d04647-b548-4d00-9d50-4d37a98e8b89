"""
Greeks Package
Advanced Black-Scholes Greeks calculation with ROC analysis and anomaly detection
"""

from .black_scholes_engine import BlackScholesEngine
from .roc_calculator import ROCCalculator
from .greeks_result import GreeksResult, GreeksPortfolioResult
from .validation_framework import GreeksValidation<PERSON>ramework
from .constants import *

__all__ = [
    'BlackScholesEngine',
    'ROCCalculator', 
    'GreeksResult',
    'GreeksPortfolioResult',
    'GreeksValidationFramework',
    'GreekEnhancementEngine'
]

__version__ = '1.0.0'


class GreekEnhancementEngine:
    """
    Unified Greeks Enhancement Engine
    
    Combines Black-Scholes calculations, ROC analysis, and anomaly detection
    into a single high-level interface following the established agent patterns.
    """
    
    def __init__(self, lookback_periods: int = 20, significance_level: float = 0.95):
        self.bs_engine = BlackScholesEngine()
        self.roc_calculator = ROCCalculator(lookback_periods, significance_level)
        self.validation_framework = GreeksValidationFramework()
        
    def calculate_greeks(self, 
                        spot_price: float,
                        strike_price: float,
                        time_to_expiry: float,
                        risk_free_rate: float,
                        volatility: float,
                        option_type: str = 'call',
                        dividend_yield: float = 0.0,
                        symbol: str = 'UNKNOWN',
                        historical_data: list = None) -> GreeksResult:
        """
        Calculate complete Greeks analysis with ROC and anomaly detection
        
        Args:
            spot_price: Current underlying price
            strike_price: Option strike price
            time_to_expiry: Time to expiration in years
            risk_free_rate: Risk-free interest rate
            volatility: Implied volatility
            option_type: 'call' or 'put'
            dividend_yield: Dividend yield
            symbol: Symbol for identification
            historical_data: List of historical GreeksResult for ROC/anomaly analysis
            
        Returns:
            GreeksResult: Complete analysis with validation
        """
        
        # Calculate base Greeks using Black-Scholes
        result = self.bs_engine.calculate_greeks(
            spot_price, strike_price, time_to_expiry,
            risk_free_rate, volatility, option_type,
            dividend_yield, symbol
        )
        
        # Calculate ROC if historical data available
        if historical_data and len(historical_data) > 1:
            # Convert historical data to DataFrame for ROC calculation
            historical_df = self._convert_historical_to_dataframe(historical_data)
            
            # Calculate ROC derivatives
            roc_values = self.roc_calculator.calculate_roc_derivatives(historical_df, symbol)
            
            # Update result with ROC values
            result.delta_roc = roc_values.get('delta_roc', 0.0)
            result.gamma_roc = roc_values.get('gamma_roc', 0.0)
            result.theta_roc = roc_values.get('theta_roc', 0.0)
            result.vega_roc = roc_values.get('vega_roc', 0.0)
            result.rho_roc = roc_values.get('rho_roc', 0.0)
            
            # Detect anomalies
            anomalies, significance = self.roc_calculator.detect_statistical_anomalies(
                result, historical_data
            )
            
            result.greek_anomalies = anomalies
            result.anomaly_count = len(anomalies)
            result.statistical_significance = significance
            
            # Determine anomaly severity
            if anomalies:
                max_severity = max(anomaly.get('severity', 'none') for anomaly in anomalies)
                result.anomaly_severity = max_severity
            else:
                result.anomaly_severity = 'none'
        
        # Validate final result
        self.validation_framework.validate_complete_result(result)
        
        return result
    
    def _convert_historical_to_dataframe(self, historical_data: list):
        """Convert list of GreeksResult to DataFrame for ROC calculation"""
        import pandas as pd
        
        data_rows = []
        for greeks_result in historical_data:
            data_rows.append({
                'timestamp': greeks_result.timestamp,
                'delta': greeks_result.delta,
                'gamma': greeks_result.gamma,
                'theta': greeks_result.theta,
                'vega': greeks_result.vega,
                'rho': greeks_result.rho
            })
        
        return pd.DataFrame(data_rows)

