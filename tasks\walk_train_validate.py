#!/usr/bin/env python3
"""
B-03: Walk-Forward Training and Validation
Implements time-series cross-validation for ML model training
"""

import os
import sys
import logging
import argparse
import pandas as pd
import numpy as np
import joblib
from pathlib import Path
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, precision_score, recall_score
from sklearn.preprocessing import StandardScaler

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))


class WalkForwardValidator:
    """Implements walk-forward validation for time series ML models"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.scaler = StandardScaler()
    
    def run(self, ticker, in_sample=252, out_sample=21, model_type="random_forest", 
            target_horizon=5, input_file=None):
        """
        Run walk-forward validation
        
        Args:
            ticker (str): Stock symbol
            in_sample (int): Training window size in bars
            out_sample (int): Testing window size in bars
            model_type (str): Model type ('random_forest' or 'logistic')
            target_horizon (int): Forward-looking periods for target
            input_file (str): Optional custom input file
        
        Returns:
            dict: Validation results and model performance metrics
        """
        try:
            # Determine input file path
            if input_file is None:
                input_file = f"data/features/{ticker}_features.parquet"
            
            self.logger.info(f"Starting walk-forward validation for {ticker}")
            self.logger.info(f"Parameters: in_sample={in_sample}, out_sample={out_sample}")
            
            # Load features
            if not os.path.exists(input_file):
                raise FileNotFoundError(f"Features file not found: {input_file}")
            
            df = pd.read_parquet(input_file)
            
            # Prepare features and target
            feature_cols = self._select_features(df)
            df_clean = self._prepare_data(df, feature_cols, target_horizon)
            
            # Run walk-forward validation
            results = self._walk_forward_validation(
                df_clean, feature_cols, in_sample, out_sample, model_type
            )
            
            # Calculate final metrics
            metrics = self._calculate_metrics(results)
            
            # Shadow mode logging - capture validation results
            self.log_validation_results(ticker, metrics, results)
            
            # Save final model
            final_model = self._train_final_model(df_clean, feature_cols, model_type)
            self._save_model(final_model, ticker)
            
            return {
                "status": "SUCCESS",
                "ticker": ticker,
                "model_type": model_type,
                "metrics": metrics,
                "model_file": f"models/backtest_model.pkl",
                "validation_results": results
            }
            
        except Exception as e:
            self.logger.error(f"Walk-forward validation failed for {ticker}: {e}")
            return {
                "status": "ERROR",
                "ticker": ticker,
                "error": str(e)
            }
    
    def log_validation_results(self, ticker, metrics, results):
        """Log walk-forward validation results for shadow mode learning"""
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            # Convert validation metrics to signal format
            signal_data = {
                'confidence': metrics.get('mean_accuracy', 0.5),
                'strength': abs(metrics.get('mean_sharpe', 0.0)) / 2.0,  # Normalize Sharpe
                'execution_recommendation': 'execute' if metrics.get('mean_sharpe', 0) > 0 else 'avoid'
            }
            
            math_data = {
                'accuracy_score': metrics.get('mean_accuracy', 0.5),
                'precision': metrics.get('mean_precision', 0.001)
            }
            
            market_context = {
                'system': 'walk_forward_validator',
                'ticker': ticker,
                'validation_folds': len(results),
                'mean_sharpe': metrics.get('mean_sharpe', 0),
                'mean_accuracy': metrics.get('mean_accuracy', 0),
                'validation_timestamp': self.logger.handlers[0].baseFilename if self.logger.handlers else 'unknown'
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'validation_complete', 'metrics': metrics},
                outcome=metrics.get('mean_sharpe', 0.0),  # Sharpe as outcome
                market_context=market_context
            )
            self.logger.info(f"Shadow mode: Walk-forward validation results logged for {ticker}")
            
        except Exception as e:
            self.logger.warning(f"Shadow mode logging failed: {e}")
    
    def _select_features(self, df):
        """Select relevant Greek and IV derivative features for training"""
        # Exclude timestamp and identifier columns
        exclude_cols = ['t', 'ticker', 'o', 'h', 'l', 'c', 'v', 'date']
        
        # Primary Greek and IV features we want
        primary_features = [
            'iv_rank', 'iv_roc',
            'gamma_calc_mean', 'gamma_calc_mean_roc', 'gamma_calc_mean_roc_2',
            'vanna_calc_mean', 'vanna_calc_mean_roc', 'vanna_calc_mean_roc_2', 
            'charm_calc_mean', 'charm_calc_mean_roc', 'charm_calc_mean_roc_2',
            'gamma_calc_sum', 'gamma_calc_sum_roc',
            'vanna_calc_sum', 'vanna_calc_sum_roc',
            'charm_calc_sum', 'charm_calc_sum_roc'
        ]
        
        # Find available features in the dataframe
        available_features = []
        for feature in primary_features:
            if feature in df.columns:
                available_features.append(feature)
        
        # If no Greek features available, fall back to price/volume features
        if not available_features:
            self.logger.warning("No Greek features found, using price/volume features")
            all_cols = [col for col in df.columns if col not in exclude_cols]
            # Remove target-related columns
            available_features = [col for col in all_cols if not col.startswith('target')]
        
        self.logger.info(f"Selected {len(available_features)} features for training: {available_features}")
        return available_features
    
    def _prepare_data(self, df, feature_cols, target_horizon):
        """Prepare data for training with target generation"""
        df = df.copy()
        
        # Generate forward-looking return target
        df['future_return'] = df['c'].shift(-target_horizon) / df['c'] - 1
        
        # Create binary classification target (1 if positive return, 0 otherwise)
        df['target'] = (df['future_return'] > 0).astype(int)
        
        # Remove rows where target cannot be calculated
        df = df.dropna(subset=['target'])
        
        # Ensure features are numeric
        for col in feature_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Drop rows with missing features
        df = df.dropna(subset=feature_cols)
        
        self.logger.info(f"Prepared {len(df)} samples for validation")
        return df
    
    def _walk_forward_validation(self, df, feature_cols, in_sample, out_sample, model_type):
        """Perform walk-forward validation"""
        results = []
        total_samples = len(df)
        
        # Calculate number of validation windows
        min_samples_needed = in_sample + out_sample
        if total_samples < min_samples_needed:
            raise ValueError(f"Insufficient data: need {min_samples_needed}, have {total_samples}")
        
        max_start = total_samples - min_samples_needed
        step_size = out_sample  # Non-overlapping windows
        
        for start in range(0, max_start + 1, step_size):
            train_end = start + in_sample
            test_end = min(train_end + out_sample, total_samples)
            
            if test_end - train_end < out_sample:
                break  # Skip incomplete final window
            
            # Split data
            train_data = df.iloc[start:train_end]
            test_data = df.iloc[train_end:test_end]
            
            # Prepare features and targets
            X_train = train_data[feature_cols]
            y_train = train_data['target']
            X_test = test_data[feature_cols]
            y_test = test_data['target']
            
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Train model
            model = self._create_model(model_type)
            model.fit(X_train_scaled, y_train)
            
            # Make predictions
            y_pred = model.predict(X_test_scaled)
            y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
            
            # Calculate returns for this fold
            actual_returns = test_data['future_return'].values
            strategy_returns = actual_returns * (y_pred * 2 - 1)  # Long/short based on prediction
            
            # Store results
            fold_result = {
                'fold': len(results) + 1,
                'train_start': start,
                'train_end': train_end,
                'test_start': train_end,
                'test_end': test_end,
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred, zero_division=0),
                'recall': recall_score(y_test, y_pred, zero_division=0),
                'mean_return': np.mean(strategy_returns),
                'std_return': np.std(strategy_returns),
                'sharpe': np.mean(strategy_returns) / np.std(strategy_returns) if np.std(strategy_returns) > 0 else 0,
                'hit_rate': np.mean((strategy_returns > 0).astype(int)),
                'predictions': y_pred.tolist(),
                'probabilities': y_pred_proba.tolist(),
                'returns': strategy_returns.tolist()
            }
            
            results.append(fold_result)
            
            self.logger.info(f"Fold {fold_result['fold']}: "
                           f"Acc={fold_result['accuracy']:.3f}, "
                           f"Sharpe={fold_result['sharpe']:.3f}")
        
        return results
    
    def _create_model(self, model_type):
        """Create ML model based on type"""
        if model_type == "random_forest":
            return RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                min_samples_split=20,
                min_samples_leaf=10,
                random_state=42
            )
        elif model_type == "logistic":
            return LogisticRegression(
                random_state=42,
                max_iter=1000
            )
        else:
            raise ValueError(f"Unknown model type: {model_type}")
    
    def _calculate_metrics(self, results):
        """Calculate aggregate metrics across all folds"""
        if not results:
            return {}
        
        # Aggregate metrics
        avg_accuracy = np.mean([r['accuracy'] for r in results])
        avg_precision = np.mean([r['precision'] for r in results])
        avg_recall = np.mean([r['recall'] for r in results])
        
        # Combine all returns for overall Sharpe
        all_returns = []
        for r in results:
            all_returns.extend(r['returns'])
        
        overall_sharpe = (np.mean(all_returns) / np.std(all_returns) * np.sqrt(252) 
                         if len(all_returns) > 0 and np.std(all_returns) > 0 else 0)
        
        avg_hit_rate = np.mean([r['hit_rate'] for r in results])
        
        return {
            'avg_accuracy': avg_accuracy,
            'avg_precision': avg_precision,
            'avg_recall': avg_recall,
            'overall_sharpe': overall_sharpe,
            'avg_hit_rate': avg_hit_rate,
            'num_folds': len(results),
            'total_predictions': len(all_returns)
        }
    
    def _train_final_model(self, df, feature_cols, model_type):
        """Train final model on all available data"""
        X = df[feature_cols]
        y = df['target']
        
        X_scaled = self.scaler.fit_transform(X)
        
        model = self._create_model(model_type)
        model.fit(X_scaled, y)
        
        return {
            'model': model,
            'scaler': self.scaler,
            'features': feature_cols,
            'model_type': model_type
        }
    
    def _save_model(self, model_dict, ticker):
        """Save trained model to disk"""
        model_dir = Path("models")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        model_file = model_dir / "backtest_model.pkl"
        joblib.dump(model_dict, model_file)
        
        # Generate feature importance plot
        self._plot_feature_importance(model_dict, ticker)
        
        self.logger.info(f"Saved model to {model_file}")
    
    def _plot_feature_importance(self, model_dict, ticker):
        """Generate feature importance plot for Greek/IV features"""
        try:
            model = model_dict['model']
            features = model_dict['features']
            
            # Check if model has feature_importances_
            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_
                
                # Create simple text-based importance report
                importance_data = list(zip(features, importances))
                importance_data.sort(key=lambda x: x[1], reverse=True)
                
                # Save to text file (fallback for environments without plotting)
                report_dir = Path("reports")
                report_dir.mkdir(parents=True, exist_ok=True)
                
                importance_file = report_dir / f"feature_importance_{ticker}.txt"
                with open(importance_file, 'w') as f:
                    f.write(f"Feature Importance Report - {ticker}\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for i, (feature, importance) in enumerate(importance_data, 1):
                        f.write(f"{i:2d}. {feature:<30} {importance:8.6f}\n")
                    
                    f.write(f"\nTop 5 Features:\n")
                    for feature, importance in importance_data[:5]:
                        f.write(f"  {feature}: {importance:.6f}\n")
                
                self.logger.info(f"Feature importance saved to {importance_file}")
                
                # Try to create visual plot if matplotlib available
                try:
                    import matplotlib.pyplot as plt
                    
                    # Create bar plot
                    top_features = importance_data[:10]  # Top 10 features
                    feature_names = [f[0] for f in top_features]
                    feature_importances = [f[1] for f in top_features]
                    
                    plt.figure(figsize=(12, 8))
                    plt.barh(range(len(feature_names)), feature_importances)
                    plt.yticks(range(len(feature_names)), feature_names)
                    plt.xlabel('Feature Importance')
                    plt.title(f'Greek/IV Feature Importance - {ticker}')
                    plt.gca().invert_yaxis()
                    plt.tight_layout()
                    
                    plot_file = report_dir / f"feature_importance_{ticker}.png"
                    plt.savefig(plot_file, dpi=150, bbox_inches='tight')
                    plt.close()
                    
                    self.logger.info(f"Feature importance plot saved to {plot_file}")
                    
                except ImportError:
                    self.logger.info("Matplotlib not available, text report generated instead")
                except Exception as e:
                    self.logger.warning(f"Could not create plot: {e}")
            
        except Exception as e:
            self.logger.warning(f"Could not generate feature importance: {e}")


def main():
    """Command line interface for walk-forward validation"""
    parser = argparse.ArgumentParser(description="Walk-forward validation for ML models")
    parser.add_argument("--ticker", required=True, help="Stock ticker symbol")
    parser.add_argument("--in-sample", type=int, default=252, help="Training window size")
    parser.add_argument("--out-sample", type=int, default=21, help="Testing window size")
    parser.add_argument("--model", choices=["random_forest", "logistic"], 
                       default="random_forest", help="Model type")
    parser.add_argument("--horizon", type=int, default=5, help="Target horizon in periods")
    parser.add_argument("--input", help="Custom input file path")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Execute validation
    validator = WalkForwardValidator()
    result = validator.run(
        ticker=args.ticker.upper(),
        in_sample=args.in_sample,
        out_sample=args.out_sample,
        model_type=args.model,
        target_horizon=args.horizon,
        input_file=args.input
    )
    
    # Output results
    if result["status"] == "SUCCESS":
        print("SUCCESS: Walk-forward validation completed")
        print(f"Ticker: {result['ticker']}")
        print(f"Model: {result['model_type']}")
        print(f"Model file: {result['model_file']}")
        
        metrics = result['metrics']
        print(f"\nPerformance Metrics:")
        print(f"  Accuracy: {metrics['avg_accuracy']:.3f}")
        print(f"  Precision: {metrics['avg_precision']:.3f}")
        print(f"  Recall: {metrics['avg_recall']:.3f}")
        print(f"  Sharpe: {metrics['overall_sharpe']:.3f}")
        print(f"  Hit Rate: {metrics['avg_hit_rate']:.3f}")
        print(f"  Folds: {metrics['num_folds']}")
        
        return 0
    else:
        print(f"ERROR: {result['error']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
