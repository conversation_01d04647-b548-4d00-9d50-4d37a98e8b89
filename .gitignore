# CRITICAL: Functional secrets - NEVER commit these files
schwab_token.json
**/schwab_token.json
config/schwab_config.json
**/config/*_config.json

# Environment files with secrets
.env
.env.local
.env.production
.env.development

# API Keys and tokens
*.key
*.pem
*.p12
*.pfx
*_token.json
*_secret.json
*_credentials.json

# Tradier and broker credentials
tradier_*.json
*_broker_config.json

# Database credentials
database.json
db_config.json

# Python cache and temporary files
__pycache__/
*.py[cod]
*$py.class
*.so
.pytest_cache/
.coverage
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
*.log

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Trading data that may contain sensitive info
fills/*/
guarded/*/
logs/*.log
risk_state/*.json
training_logs/*.json

# Temporary test files
debug_*.py
test_temp_*.py
temp_*.json

# Backup files
*.bak
*.backup
*.old

# Output directories that may contain sensitive results
output/*/
outputs/*/
reports/private/

# Redis dumps
dump.rdb
