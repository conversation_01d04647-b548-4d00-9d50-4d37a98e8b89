{"greek_enhancement_analysis": {"agent_type": "GreekEnhancementAgent", "priority": "CRITICAL", "phase": "1", "description": "Advanced Black-Scholes Greeks calculation with ROC derivatives and statistical anomaly detection", "inputs": {"required": ["ticker", "current_price", "options_chain", "market_data"], "optional": ["risk_free_rate", "dividend_yield", "lookback_periods", "precision_tolerance"]}, "outputs": {"greeks_file": "Complete Greeks analysis with Delta, Gamma, Theta, Vega calculations", "roc_derivatives": "Rate of change analysis for all Greeks with trend detection", "anomaly_detection": "Statistical anomalies in Greeks behavior with Z-score analysis"}, "workflow_file": "greek_enhancement_workflow.md", "quality_standards": "greeks_precision_standards.md", "performance_targets": {"max_execution_time": "200ms", "calculation_accuracy": "0.999", "mathematical_precision": "1e-12"}, "dependencies": [], "training_data_tags": ["greeks_calculations", "anomaly_detection", "roc_analysis", "black_scholes_precision"]}, "enhanced_csid_analysis": {"agent_type": "EnhancedCSIDAgent", "priority": "HIGH", "phase": "2", "description": "Enhanced CSID institutional flow detection with stealth accumulation/distribution analysis", "inputs": {"required": ["ticker", "price_df_parquet"], "optional": ["lookback_periods", "quality_threshold"]}, "outputs": {"csid_file": "Enhanced CSID analysis results with institutional bias and smart money index", "institutional_signals": "Trading signals from institutional flow patterns", "flow_regime": "Stealth accumulation, distribution, or mixed regime classification"}, "workflow_file": "enhanced_csid_workflow.md", "quality_standards": "csid_quality_standards.md", "performance_targets": {"max_execution_time": "80ms", "data_quality_min": "0.6", "institutional_detection_accuracy": "0.8"}, "dependencies": ["data_acquisition"], "training_data_tags": ["institutional_patterns", "csid_optimization", "stealth_detection"]}, "flow_physics_analysis": {"agent_type": "FlowPhysicsAgent", "priority": "HIGH", "phase": "2", "description": "Advanced flow physics analysis with velocity, acceleration, jerk calculations and regime detection", "inputs": {"required": ["ticker", "price_5m_parquet"], "optional": ["lookback_periods", "quality_threshold"]}, "outputs": {"flowphysics_file": "Complete flow physics analysis with derivatives and regime classification", "institutional_activity": "Institutional flow detection and strength assessment", "regime_analysis": "Flow regime classification with confidence scoring"}, "workflow_file": "flow_physics_workflow.md", "quality_standards": "flow_physics_standards.md", "performance_targets": {"max_execution_time": "120ms", "quality_score_min": "0.6", "mathematical_precision": "1e-10"}, "dependencies": ["data_acquisition"], "training_data_tags": ["flow_derivatives", "regime_classification", "institutional_detection"]}, "chart_generation": {"agent_type": "ChartGeneratorAgent", "priority": "HIGH", "phase": "3", "description": "Generate 3 precise validation charts from analysis results", "inputs": {"required": ["confluence_result", "market_data", "analysis_factors"], "optional": ["chart_preferences", "display_options"]}, "outputs": {"chart_1": "Volume Profile + Flow Concentration", "chart_2": "Liquidity Walls + GEX Analysis", "chart_3": "Factor Confluence Dashboard"}, "workflow_file": "chart_generation_workflow.md", "quality_standards": "output_quality_standards.md", "performance_targets": {"max_execution_time": "30_seconds", "accuracy_requirement": "99%", "visual_precision": "pixel_perfect"}, "dependencies": ["mathematical_validation", "confluence_analysis"], "training_data_tags": ["chart_decisions", "layout_optimization", "visual_accuracy"]}, "mathematical_validation": {"agent_type": "MathValidatorAgent", "priority": "CRITICAL", "phase": "2", "description": "Validate mathematical precision of all calculations", "inputs": {"required": ["raw_calculations", "flow_derivatives", "volume_profiles", "gex_calculations"], "optional": ["validation_tolerance"]}, "outputs": {"validation_result": "Pass/Fail with precision metrics", "error_report": "Detailed calculation discrepancies", "quality_score": "Overall mathematical accuracy"}, "workflow_file": "mathematical_validation_workflow.md", "quality_standards": "mathematical_precision_standards.md", "performance_targets": {"precision_requirement": "99.9%", "validation_time": "5_seconds", "error_tolerance": "1e-10"}, "dependencies": [], "training_data_tags": ["precision_standards", "error_detection", "quality_gates"]}, "signal_generation": {"agent_type": "ENGINE_SignalGenerator", "priority": "CRITICAL", "phase": "2", "description": "Generate final trading signal from confluence analysis using engine implementation", "note": "Handled by engine/signal_generator.py - SignalGeneratorAgent removed as redundant", "inputs": {"required": ["confluence_result", "factor_analysis", "current_price"], "optional": ["market_context", "risk_parameters"]}, "outputs": {"trading_signal": "Final signal with direction and confidence", "risk_assessment": "Risk level and position sizing guidance", "timing_estimate": "Entry/exit timing recommendations"}, "implementation": "engine/signal_generator.py", "performance_targets": {"signal_accuracy": "80%", "response_time": "200ms", "confidence_precision": "0.01"}, "dependencies": ["confluence_analysis", "mathematical_validation"], "training_data_tags": ["signal_decisions", "confidence_scoring", "risk_assessment"]}, "confluence_analysis": {"agent_type": "ConfluenceAgent", "priority": "HIGH", "phase": "2", "description": "Analyze factor agreement using 3-of-4 logic", "inputs": {"required": ["flow_factors", "volume_factors", "liquidity_factors", "gex_factors"], "optional": ["weight_adjustments", "confidence_modifiers"]}, "outputs": {"confluence_result": "Direction consensus with agreement count", "factor_matrix": "4x4 analyzer agreement analysis", "confidence_score": "Overall confluence confidence"}, "workflow_file": "confluence_analysis_workflow.md", "quality_standards": "confluence_quality_standards.md", "performance_targets": {"min_agreement_count": "3", "analysis_time": "100ms", "accuracy_requirement": "95%"}, "dependencies": ["core_analysis"], "training_data_tags": ["agreement_logic", "confidence_calculation", "consensus_building"]}, "core_analysis": {"agent_type": "CoreAnalysisOrchestrator", "priority": "HIGH", "phase": "2", "description": "Orchestrate 4 core analyzers in parallel", "inputs": {"required": ["market_data", "current_price", "ticker"], "optional": ["timeframe_preferences", "analysis_depth"]}, "outputs": {"flow_factors": "Flow physics analysis results", "volume_factors": "Volume profile analysis results", "liquidity_factors": "Liquidity sweep analysis results", "gex_factors": "GEX analysis results"}, "workflow_file": "core_analysis_workflow.md", "quality_standards": "analysis_quality_standards.md", "performance_targets": {"parallel_execution": "true", "total_analysis_time": "2_seconds", "factor_generation_rate": "minimum_3_factors"}, "dependencies": ["data_acquisition"], "training_data_tags": ["parallel_coordination", "analyzer_optimization", "factor_generation"]}, "data_acquisition": {"agent_type": "DataAcquisitionAgent", "priority": "CRITICAL", "phase": "1", "description": "Acquire and validate market data via MCP", "inputs": {"required": ["ticker", "timeframes"], "optional": ["lookback_periods", "data_quality_requirements"]}, "outputs": {"market_data": "Multi-timeframe OHLCV data", "current_price": "Real-time price data", "data_quality_report": "Data completeness and accuracy metrics"}, "workflow_file": "data_acquisition_workflow.md", "quality_standards": "data_quality_standards.md", "performance_targets": {"data_freshness": "under_30_seconds", "completeness": "95%", "acquisition_time": "1_second"}, "dependencies": [], "training_data_tags": ["data_validation", "mcp_integration", "quality_assessment"]}, "output_coordination": {"agent_type": "OutputCoordinatorAgent", "priority": "HIGH", "phase": "3", "description": "Coordinate chart generation and report creation", "inputs": {"required": ["analysis_complete", "validation_passed", "signal_generated"], "optional": ["output_preferences", "format_requirements"]}, "outputs": {"unified_report": "Complete intelligence report", "chart_package": "3 validation charts", "output_validation": "Quality assurance results"}, "workflow_file": "output_coordination_workflow.md", "quality_standards": "output_quality_standards.md", "performance_targets": {"coordination_time": "10_seconds", "output_accuracy": "99%", "format_compliance": "100%"}, "dependencies": ["chart_generation", "signal_generation"], "training_data_tags": ["coordination_patterns", "output_optimization", "quality_control"]}, "performance_monitoring": {"agent_type": "PerformanceMonitorAgent", "priority": "NORMAL", "phase": "ongoing", "description": "Monitor system performance and agent health", "inputs": {"required": ["agent_metrics", "system_performance", "error_logs"], "optional": ["performance_thresholds", "alert_settings"]}, "outputs": {"performance_report": "System performance analysis", "agent_health_status": "Individual agent status", "optimization_recommendations": "Performance improvement suggestions"}, "workflow_file": "performance_monitoring_workflow.md", "quality_standards": "performance_standards.md", "performance_targets": {"monitoring_frequency": "real_time", "alert_response_time": "1_second", "accuracy_tracking": "continuous"}, "dependencies": [], "training_data_tags": ["performance_patterns", "optimization_strategies", "health_monitoring"]}, "error_recovery": {"agent_type": "ErrorRecoveryAgent", "priority": "CRITICAL", "phase": "ongoing", "description": "Handle system errors and recovery procedures", "inputs": {"required": ["error_event", "system_state", "failure_context"], "optional": ["recovery_preferences", "escalation_rules"]}, "outputs": {"recovery_action": "Specific recovery steps taken", "system_status": "Post-recovery system state", "prevention_recommendations": "Future error prevention guidance"}, "workflow_file": "error_recovery_workflow.md", "quality_standards": "recovery_standards.md", "performance_targets": {"recovery_time": "under_5_seconds", "success_rate": "95%", "data_preservation": "100%"}, "dependencies": [], "training_data_tags": ["error_patterns", "recovery_strategies", "failure_analysis"]}, "agent_zero_training": {"agent_type": "AgentZeroTrainer", "priority": "HIGH", "phase": "5", "description": "Train Agent Zero on complete system orchestration", "inputs": {"required": ["workflow_examples", "decision_trees", "success_patterns"], "optional": ["training_parameters", "learning_objectives"]}, "outputs": {"trained_model": "Agent Zero autonomous intelligence", "training_metrics": "Learning progress and accuracy", "deployment_readiness": "Agent Zero capability assessment"}, "workflow_file": "agent_zero_training_workflow.md", "quality_standards": "training_quality_standards.md", "performance_targets": {"training_accuracy": "95%", "autonomy_level": "full_independence", "decision_precision": "matches_claude_orchestration"}, "dependencies": ["complete_system_validation"], "training_data_tags": ["orchestration_mastery", "autonomous_operation", "decision_making"]}}