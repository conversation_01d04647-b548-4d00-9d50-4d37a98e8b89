
import unittest
import pandas as pd
from pathlib import Path
import json
from unittest import mock
import sys
import os
from datetime import datetime

# Add the parent directory to the sys.path to allow imports from the 'agents' directory
sys.path.insert(0, str(Path(__file__).parent.parent))

from agents.enhanced_csid_agent import EnhancedCSIDAgent

class TestEnhancedCSIDAgent(unittest.TestCase):

    TEST_DATA_DIR = Path(__file__).parent / "test_data"
    SAMPLE_PARQUET_PATH = TEST_DATA_DIR / "sample_csid_data.parquet"
    OUTPUT_DIR = Path(__file__).parent.parent / "flow_phys" # Agent creates this

    @classmethod
    def setUpClass(cls):
        """Set up test data and directories once for all tests."""
        cls.TEST_DATA_DIR.mkdir(parents=True, exist_ok=True)
        cls.OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        # Create a dummy Parquet file for testing
        data = {
            'timestamp': pd.to_datetime([f'2024-01-01 {i:02d}:00:00' for i in range(21)]),
            'open': [100 + i for i in range(21)],
            'high': [102 + i for i in range(21)],
            'low': [99 + i for i in range(21)],
            'close': [101 + i for i in range(21)],
            'volume': [1000 + i * 10 for i in range(21)]
        }
        df = pd.DataFrame(data)
        df.to_parquet(cls.SAMPLE_PARQUET_PATH, index=False)
        print(f"Created dummy parquet file at: {cls.SAMPLE_PARQUET_PATH}")

    @classmethod
    def tearDownClass(cls):
        """Clean up test data and directories after all tests."""
        # Clean up the dummy parquet file
        if cls.SAMPLE_PARQUET_PATH.exists():
            cls.SAMPLE_PARQUET_PATH.unlink()
        # Clean up the test data directory if empty
        if cls.TEST_DATA_DIR.exists() and not any(cls.TEST_DATA_DIR.iterdir()):
            cls.TEST_DATA_DIR.rmdir()
        
        # Clean up agent output directory
        today_str = datetime.now().strftime("%Y-%m-%d")
        output_file_path = cls.OUTPUT_DIR / today_str / "TEST_TICKER_csid.json"
        if output_file_path.exists():
            output_file_path.unlink()
        
        # Remove the daily output directory if empty
        daily_output_dir = cls.OUTPUT_DIR / today_str
        if daily_output_dir.exists() and not any(daily_output_dir.iterdir()):
            daily_output_dir.rmdir()

    def test_execute_csid_analysis_fallback(self):
        """Test the execute method of EnhancedCSIDAgent using the fallback analyzer."""
        
        agent = EnhancedCSIDAgent()
        ticker = "TEST_TICKER"
        
        # Ensure we have a proper CSID analyzer (either Enhanced or Flow Physics)
        self.assertIsNotNone(agent.analyzer)
        analyzer_type = str(type(agent.analyzer))
        self.assertTrue(
            "EnhancedCSIDAnalyzer" in analyzer_type or "FlowPhysicsAnalyzer" in analyzer_type,
            f"Expected proper CSID analyzer, got: {analyzer_type}"
        )

        # Execute the agent
        output_file_path = agent.execute(ticker, str(self.SAMPLE_PARQUET_PATH))

        # Assert that an output file was created
        self.assertTrue(Path(output_file_path).exists())
        self.assertTrue(output_file_path.endswith(f"{ticker}_csid.json"))

        # Load the output file and verify its content
        with open(output_file_path, 'r') as f:
            result_data = json.load(f)
        
        self.assertEqual(result_data['ticker'], ticker)
        self.assertGreater(result_data['data_quality_score'], 0.6)
        self.assertIn(result_data['flow_regime'], ["stealth_accumulation", "stealth_distribution", "retail_fomo", "mixed"])
        self.assertIn('smart_money_index', result_data)
        self.assertIn('institutional_bias', result_data)

        # Test with insufficient data to trigger low quality score
        insufficient_data = {
            'timestamp': pd.to_datetime([f'2024-01-01 0{i}:00:00' for i in range(5)]),
            'open': [100, 101, 102, 103, 104],
            'high': [102, 103, 104, 105, 106],
            'low': [99, 100, 101, 102, 103],
            'close': [101, 102, 103, 104, 105],
            'volume': [1000, 1010, 1020, 1030, 1040]
        }
        insufficient_df = pd.DataFrame(insufficient_data)
        insufficient_parquet_path = self.TEST_DATA_DIR / "insufficient_data.parquet"
        insufficient_df.to_parquet(insufficient_parquet_path, index=False)

        # Test with insufficient data - should process but with lower quality score
        output_file_path_low = agent.execute("TEST_TICKER_LOW", str(insufficient_parquet_path))
        
        # Verify the file was created and contains reasonable data
        self.assertTrue(Path(output_file_path_low).exists())
        with open(output_file_path_low, 'r') as f:
            low_quality_data = json.load(f)
        
        # The result should still be valid but may have different characteristics
        self.assertEqual(low_quality_data['ticker'], "TEST_TICKER_LOW")
        self.assertIn('data_quality_score', low_quality_data)
        
        # Clean up the insufficient data file
        if insufficient_parquet_path.exists():
            insufficient_parquet_path.unlink()

if __name__ == '__main__':
    unittest.main()
