# Security Standards for Agent Development

## HTTP Client Standards

### Required Headers
All HTTP clients MUST include:
```python
headers = {
    'X-Correlation-ID': str(uuid.uuid4()),
    'User-Agent': 'CORE-Flow-Detection/1.0',
    'Content-Type': 'application/json'
}
```

### Secret Management
Environment variables ONLY - never hardcode:
```python
import os
API_KEY = os.getenv('SCHWAB_API_KEY')
if not API_KEY:
    raise EnvironmentError("SCHWAB_API_KEY not found")
```

### Required .env Template
Copy `.env.example`  `.env` and populate:
```
SCHWAB_API_KEY=your_key_here
SCHWAB_API_SECRET=your_secret_here
REDIS_URL=redis://localhost:6379
```

## Implementation Pattern
```python
import uuid
import os
import requests

def make_api_call(endpoint, data=None):
    headers = {
        'X-Correlation-ID': str(uuid.uuid4()),
        'Authorization': f"Bearer {os.getenv('API_TOKEN')}",
        'User-Agent': 'CORE-Flow-Detection/1.0'
    }
    response = requests.post(endpoint, json=data, headers=headers)
    return response.json()
```

## Compliance Check
Run: `python ci/security_check.py` to validate agent compliance
