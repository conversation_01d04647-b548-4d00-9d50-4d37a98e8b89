#!/usr/bin/env python3
"""
Command Monitor - Performance monitoring for commands
"""

from typing import Dict, Any, List
from dataclasses import dataclass
from datetime import datetime

@dataclass
class CommandMetrics:
    command_id: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    success: bool

@dataclass
class PerformanceTracker:
    total_commands: int
    average_execution_time: float
    success_rate: float
    peak_memory: float

class CommandMonitor:
    def __init__(self):
        self.metrics = []
        self.performance_data = {}
    
    def record_execution(self, metrics: CommandMetrics):
        self.metrics.append(metrics)
    
    def get_performance_summary(self) -> PerformanceTracker:
        if not self.metrics:
            return PerformanceTracker(0, 0.0, 0.0, 0.0)
        
        total = len(self.metrics)
        avg_time = sum(m.execution_time for m in self.metrics) / total
        success_count = sum(1 for m in self.metrics if m.success)
        success_rate = success_count / total
        peak_mem = max(m.memory_usage for m in self.metrics)
        
        return PerformanceTracker(total, avg_time, success_rate, peak_mem)
