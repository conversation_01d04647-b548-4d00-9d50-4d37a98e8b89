#!/usr/bin/env python3
"""
CORE System Deep Dive Analysis & Cleanup Plan
============================================
Comprehensive identification and removal of redundant test/diagnostic scripts.

ANALYSIS FINDINGS:
=================
1. MASSIVE SCRIPT PROLIFERATION: 400+ Python files, majority are test/diagnostic scripts
2. REDUNDANT PATTERNS:
   - test_* (85+ scripts)
   - *_validation* (45+ scripts) 
   - *_diagnostic* (25+ scripts)
   - agent_zero_* (35+ scripts)
   - comprehensive_* (15+ scripts)
   - quick_* (12+ scripts)
   - fix_* (8+ scripts)

3. CORE FUNCTIONAL SCRIPTS TO PRESERVE:
   - main.py (entry point)
   - ultimate_orchestrator.py (comprehensive pipeline)
   - enhanced_greeks_engine.py (production Greeks)
   - SCHWAB_MCP_PRODUCTION/ (production API)
   - agents/ (agent modules)
   - utils/ (utilities)
   - tasks/ (core tasks)
   - ml/ml/ (ML components)

CLEANUP STRATEGY:
================
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Set, Tuple

class CoreSystemAnalyzer:
    def __init__(self, core_path: str = "D:/script-work/CORE"):
        self.core_path = Path(core_path)
        self.analysis_results = {}
        self.cleanup_plan = {}
        
        # Core functional scripts that MUST be preserved
        self.core_functional = {
            'main.py',
            'ultimate_orchestrator.py', 
            'enhanced_greeks_engine.py',
            'enhanced_data_agent_broker_integration.py',
            'agent_zero_integration_hub.py',
            'run_core_win.ps1'
        }
        
        # Core directories that contain production code
        self.core_directories = {
            'SCHWAB_MCP_PRODUCTION',
            'agents',
            'utils', 
            'tasks',
            'ml',
            'engine',
            'greeks',
            'Flow_Physics_Engine',
            'data',
            'config',
            'training_logs'  # Contains strategy implementations
        }
        
        # Patterns indicating test/diagnostic/temporary scripts
        self.removal_patterns = {
            'test_': 'Test scripts',
            'testing_': 'Testing scripts', 
            'diagnostic': 'Diagnostic scripts',
            'validation': 'Validation scripts',
            'comprehensive_test': 'Comprehensive test scripts',
            'quick_': 'Quick test scripts',
            'fix_': 'Temporary fix scripts',
            'agent_zero_data_maximizer': 'Agent Zero test variations',
            'agent_zero_diagnostic': 'Agent Zero diagnostics',
            'agent_zero_performance': 'Agent Zero performance tests',
            'agent_zero_final_validation': 'Agent Zero validation',
            'agent_zero_quick_diag': 'Agent Zero quick diagnostics',
            'signal_flow_': 'Signal flow test scripts',
            'live_test_': 'Live testing scripts',
            'live_market_test': 'Market testing scripts',
            'multi_ticker_batch_test': 'Batch testing scripts',
            'ticker_agnostic_system_fix': 'System fix scripts',
            'proper_schwab_api_validation': 'API validation scripts',
            'quick_architecture_validation': 'Architecture validation',
            'mcp_agent_zero_test': 'MCP testing scripts',
            'production_test_suite': 'Production test suites',
            'essential_system_test': 'System testing scripts',
            'final_validation': 'Final validation scripts',
            'comprehensive_system_test': 'System test suites',
            'unicode_removal_': 'Unicode cleanup scripts',
            'cleanup_training_directories': 'Directory cleanup scripts',
            'consolidate_training_logs': 'Log consolidation scripts',
            'analyze_dependencies': 'Dependency analysis scripts'
        }
        
        # Backup file patterns to remove
        self.backup_patterns = {
            '.backup',
            '.unicode_backup', 
            '.ticker_backup',
            '_BACKUP',
            '_backup',
            '_original_backup'
        }

    def analyze_directory_structure(self) -> Dict:
        """Analyze the entire CORE directory structure"""
        print("Analyzing CORE directory structure...")
        
        total_files = 0
        python_files = 0
        removal_candidates = []
        preservation_candidates = []
        
        for root, dirs, files in os.walk(self.core_path):
            # Skip certain directories entirely
            if any(skip in root for skip in ['.git', '__pycache__', '.pytest_cache']):
                continue
                
            rel_path = Path(root).relative_to(self.core_path)
            
            for file in files:
                total_files += 1
                file_path = Path(root) / file
                rel_file_path = rel_path / file
                
                if file.endswith('.py'):
                    python_files += 1
                    
                    # Check if this is a core functional script
                    if self._is_core_functional(rel_file_path):
                        preservation_candidates.append(str(rel_file_path))
                    elif self._should_remove(file, str(rel_file_path)):
                        removal_candidates.append({
                            'path': str(rel_file_path),
                            'full_path': str(file_path),
                            'reason': self._get_removal_reason(file, str(rel_file_path)),
                            'size': file_path.stat().st_size if file_path.exists() else 0
                        })
                    else:
                        preservation_candidates.append(str(rel_file_path))
        
        self.analysis_results = {
            'total_files': total_files,
            'python_files': python_files,
            'removal_candidates': removal_candidates,
            'preservation_candidates': preservation_candidates,
            'cleanup_summary': self._generate_cleanup_summary(removal_candidates)
        }
        
        return self.analysis_results
    
    def _is_core_functional(self, rel_path: Path) -> bool:
        """Check if file is core functional and should be preserved"""
        file_name = rel_path.name
        
        # Core functional scripts
        if file_name in self.core_functional:
            return True
            
        # Files in core directories (but not in archive subdirs)
        if any(core_dir in rel_path.parts for core_dir in self.core_directories):
            if 'archive' not in rel_path.parts and 'old_' not in str(rel_path):
                return True
                
        return False
    
    def _should_remove(self, filename: str, rel_path: str) -> bool:
        """Determine if a file should be removed"""
        
        # Remove backup files
        if any(pattern in filename for pattern in self.backup_patterns):
            return True
            
        # Remove files in archive directories
        if 'archive' in rel_path or 'old_' in rel_path:
            return True
            
        # Remove based on filename patterns
        for pattern in self.removal_patterns:
            if pattern in filename.lower() or pattern in rel_path.lower():
                return True
                
        # Remove specific problematic files
        problematic_files = {
            'agent_zero_data_maximizer_BACKUP.py',
            'agent_zero_data_maximizer_FIXED.py', 
            'main_original_backup.py',
            'main_enhanced_agent_zero_maximized.py',
            'create_sample_data.py',
            'requirements_backup.txt',
            'requirements_lock.txt'
        }
        
        if filename in problematic_files:
            return True
            
        return False
    
    def _get_removal_reason(self, filename: str, rel_path: str) -> str:
        """Get the reason for removing a file"""
        
        if any(pattern in filename for pattern in self.backup_patterns):
            return "Backup file"
            
        if 'archive' in rel_path:
            return "Archived file"
            
        for pattern, reason in self.removal_patterns.items():
            if pattern in filename.lower() or pattern in rel_path.lower():
                return reason
                
        return "General cleanup candidate"
    
    def _generate_cleanup_summary(self, removal_candidates: List[Dict]) -> Dict:
        """Generate summary statistics for cleanup"""
        total_size = sum(item['size'] for item in removal_candidates)
        
        reasons = {}
        for item in removal_candidates:
            reason = item['reason']
            if reason not in reasons:
                reasons[reason] = {'count': 0, 'size': 0}
            reasons[reason]['count'] += 1
            reasons[reason]['size'] += item['size']
        
        return {
            'total_candidates': len(removal_candidates),
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'breakdown_by_reason': reasons
        }
    
    def execute_cleanup(self, dry_run: bool = True) -> Dict:
        """Execute the cleanup plan"""
        if not self.analysis_results:
            self.analyze_directory_structure()
        
        removal_log = []
        errors = []
        
        print(f"{'DRY RUN: ' if dry_run else ''}Executing cleanup plan...")
        print(f"Removing {len(self.analysis_results['removal_candidates'])} files...")
        
        for candidate in self.analysis_results['removal_candidates']:
            try:
                file_path = Path(candidate['full_path'])
                
                if file_path.exists():
                    if not dry_run:
                        if file_path.is_file():
                            file_path.unlink()
                        elif file_path.is_dir():
                            shutil.rmtree(file_path)
                    
                    removal_log.append({
                        'path': candidate['path'],
                        'reason': candidate['reason'],
                        'size': candidate['size'],
                        'action': 'removed' if not dry_run else 'would_remove'
                    })
                    
            except Exception as e:
                errors.append({
                    'path': candidate['path'],
                    'error': str(e)
                })
        
        # Generate cleanup report
        cleanup_report = {
            'timestamp': datetime.now().isoformat(),
            'dry_run': dry_run,
            'removed_files': len(removal_log),
            'preserved_files': len(self.analysis_results['preservation_candidates']),
            'errors': len(errors),
            'total_size_freed_mb': round(sum(item['size'] for item in removal_log) / (1024 * 1024), 2),
            'removal_log': removal_log,
            'errors': errors,
            'preserved_files': self.analysis_results['preservation_candidates']
        }
        
        return cleanup_report
    
    def generate_report(self) -> str:
        """Generate comprehensive cleanup report"""
        if not self.analysis_results:
            self.analyze_directory_structure()
        
        summary = self.analysis_results['cleanup_summary']
        
        report = f"""
CORE SYSTEM CLEANUP ANALYSIS REPORT
==================================
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

CURRENT STATE:
- Total Files: {self.analysis_results['total_files']}
- Python Files: {self.analysis_results['python_files']}
- Removal Candidates: {summary['total_candidates']}
- Preservation Candidates: {len(self.analysis_results['preservation_candidates'])}
- Disk Space to Free: {summary['total_size_mb']} MB

REMOVAL BREAKDOWN BY CATEGORY:
"""
        
        for reason, stats in summary['breakdown_by_reason'].items():
            size_mb = round(stats['size'] / (1024 * 1024), 2)
            report += f"- {reason}: {stats['count']} files ({size_mb} MB)\n"
        
        report += f"""

KEY PRESERVED COMPONENTS:
- main.py (system entry point)
- ultimate_orchestrator.py (comprehensive pipeline)  
- enhanced_greeks_engine.py (production Greeks engine)
- SCHWAB_MCP_PRODUCTION/ (production API server)
- agents/ (specialized agent modules)
- utils/ (core utilities)
- tasks/ (data processing tasks)
- ml/ml/ (machine learning components)
- Flow_Physics_Engine/ (flow physics analysis)
- training_logs/ (strategy implementations)

MATHEMATICAL ANALYSIS:
Current Efficiency Ratio: {len(self.analysis_results['preservation_candidates'])}/{self.analysis_results['python_files']} = {round(len(self.analysis_results['preservation_candidates'])/self.analysis_results['python_files']*100, 1)}%
Post-Cleanup Efficiency: {len(self.analysis_results['preservation_candidates'])}/{len(self.analysis_results['preservation_candidates'])} = 100%
Cleanup Effectiveness: {round(summary['total_candidates']/self.analysis_results['python_files']*100, 1)}% reduction in script count

NEXT STEPS:
1. Execute dry run to validate cleanup plan
2. Backup critical files if needed  
3. Execute actual cleanup
4. Update documentation
5. Validate system functionality post-cleanup
"""
        
        return report

def main():
    """Execute the comprehensive CORE system analysis and cleanup"""
    analyzer = CoreSystemAnalyzer()
    
    # Analyze current state
    print("Starting CORE system deep dive analysis...")
    analysis = analyzer.analyze_directory_structure()
    
    # Generate comprehensive report
    report = analyzer.generate_report()
    print(report)
    
    # Execute dry run
    print("\nExecuting DRY RUN cleanup...")
    dry_run_results = analyzer.execute_cleanup(dry_run=True)
    
    # Save results
    results_file = analyzer.core_path / "CORE_CLEANUP_ANALYSIS.json"
    with open(results_file, 'w') as f:
        json.dump({
            'analysis': analysis,
            'dry_run_results': dry_run_results,
            'report': report
        }, f, indent=2)
    
    print(f"\nAnalysis complete. Results saved to: {results_file}")
    print(f"Ready to remove {len(analysis['removal_candidates'])} redundant files")
    print(f"This will free {dry_run_results['total_size_freed_mb']} MB of disk space")
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
