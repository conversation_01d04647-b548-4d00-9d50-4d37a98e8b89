# Agent Zero Tree
from .core.decision_processor import <PERSON><PERSON><PERSON><PERSON><PERSON>, DecisionInput, DecisionOutput
from .ml_integration.ml_layer import M<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, M<PERSON>eatures, M<PERSON>rediction, MLFeatureExtractor
from .training_pipeline.data_collector import TrainingDataCollector, TrainingRecord
from .advanced_capabilities.enhancement_engine import EnhancementEng<PERSON>, EnhancementConfig
from .integration_hub.hub_controller import Agent<PERSON><PERSON><PERSON><PERSON>, get_agent_zero_hub

__all__ = [
    # Core Decision Processing
    'AgentZeroCore',
    'DecisionInput', 
    'DecisionOutput',
    
    # ML Integration
    'MLIntegrationLayer',
    'MLFeatures',
    'MLPrediction',
    'MLFeatureExtractor',
    
    # Training Pipeline
    'TrainingDataCollector',
    'TrainingRecord',
    
    # Advanced Capabilities
    'EnhancementEngine',
    'EnhancementConfig',
    
    # Integration Hub
    'AgentZeroHub',
    'get_agent_zero_hub'
]

# Tree Structure Information
TREE_INFO = {
    'name': 'Agent Zero Tree',
    'version': '2.0',
    'components': {
        'core': 'Core decision processing with mathematical precision',
        'ml_integration': 'Seamless ML system integration with fallback',
        'training_pipeline': 'Automated training data collection and management',
        'advanced_capabilities': 'Enhanced decision capabilities and optimizations',
        'integration_hub': 'Central coordination and unified API'
    },
    'performance': {
        'decision_time_target': '< 1ms',
        'initialization_time_target': '< 10ms',
        'ml_integration_overhead': '< 0.5ms',
        'training_data_logging': '< 1ms'
    },
    'features': [
        'Sub-millisecond decision making',
        'Mathematical precision (IEEE 754 compliant)',
        'ML system integration with graceful fallback',
        'Automated training data collection',
        'Advanced market regime detection',
        'Multi-timeframe analysis',
        'Dynamic risk adaptation',
        'Confidence calibration',
        'Performance optimization'
    ]
}
