"""
Feature Engineering for Machine Learning Models

This module provides feature engineering capabilities for ML models,
including feature extraction, transformation, and selection.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import time
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import os
import pickle
from functools import wraps

# Conditional imports for optional dependencies
try:
    import talib
    HAS_TALIB = True
except ImportError:
    HAS_TALIB = False

# Internal imports
from ml_logging import get_logger, measure_performance

# Setup logger
logger = get_logger('feature_engineering')

class FeatureEngineer:
    """Creates and manages features for ML models."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the feature engineer.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        self.transformer = None
        self.selector = None

        # Set up feature groups to extract
        self.feature_groups = self.config.get('feature_groups', [
            'price', 'volume', 'volatility', 'momentum', 'mean_reversion',
            'liquidity', 'market_impact', 'market_regime', 'correlation', 'sentiment'
        ])

        # Performance tracking
        self.performance_data = {}

        logger.info(f"Initialized FeatureEngineer with {len(self.feature_groups)} feature groups")

    def _validate_data(self, data: pd.DataFrame) -> bool:
        """
        Validate input data.

        Args:
            data: DataFrame to validate

        Returns:
            True if data is valid, False otherwise
        """
        if not isinstance(data, pd.DataFrame):
            logger.error("Input is not a pandas DataFrame")
            return False

        if data.empty:
            logger.error("Empty DataFrame provided")
            return False

        # Check for required columns
        required_columns = ['open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in data.columns]

        if missing_columns:
            logger.warning(f"Missing required columns: {missing_columns}")

            # Add missing columns with default values
            for col in missing_columns:
                if col == 'open' and 'close' in data.columns:
                    data[col] = data['close']
                    logger.warning(f"Added missing column '{col}' using 'close' values")
                elif col == 'high' and 'close' in data.columns:
                    data[col] = data['close'] * 1.001  # Slightly higher than close
                    logger.warning(f"Added missing column '{col}' using 'close' * 1.001")
                elif col == 'low' and 'close' in data.columns:
                    data[col] = data['close'] * 0.999  # Slightly lower than close
                    logger.warning(f"Added missing column '{col}' using 'close' * 0.999")
                elif col == 'close' and 'open' in data.columns:
                    data[col] = data['open']
                    logger.warning(f"Added missing column '{col}' using 'open' values")
                else:
                    # If we can't derive from other columns, use a constant value
                    data[col] = 100.0  # Default value
                    logger.warning(f"Added missing column '{col}' with default value 100.0")

        # Check for NaN values
        nan_columns = data.columns[data.isna().any()].tolist()
        if nan_columns:
            logger.warning(f"NaN values found in columns: {nan_columns}")
            logger.warning("NaN values will be filled with forward fill")

            # Fill NaN values with forward fill and then backward fill
            data.fillna(method='ffill', inplace=True)
            data.fillna(method='bfill', inplace=True)

            # If still have NaNs (e.g., all NaN column), fill with zeros
            if data.isna().any().any():
                logger.warning("Some NaN values remain after forward/backward fill, filling with zeros")
                data.fillna(0, inplace=True)

        return True

    def mean_reversion_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract mean reversion features.

        Args:
            data: DataFrame with OHLCV price data

        Returns:
            DataFrame with extracted features
        """
        if not self._validate_data(data):
            return pd.DataFrame()

        result = pd.DataFrame(index=data.index)

        # Distance from moving averages
        for window in [10, 20, 50, 100]:
            if len(data) >= window:
                ma = data['close'].rolling(window=window).mean()
                result[f'reversion_ma_dist_{window}'] = (data['close'] - ma) / ma

                # Z-score from moving average
                std = data['close'].rolling(window=window).std()
                result[f'reversion_ma_zscore_{window}'] = (data['close'] - ma) / std

                # Extreme deviations indicating mean reversion potential
                result[f'reversion_ma_extreme_{window}'] = (np.abs(result[f'reversion_ma_zscore_{window}']) > 2).astype(int)

                # Overbought/oversold indicators
                result[f'reversion_overbought_{window}'] = (result[f'reversion_ma_zscore_{window}'] > 2).astype(int)
                result[f'reversion_oversold_{window}'] = (result[f'reversion_ma_zscore_{window}'] < -2).astype(int)

        # Bollinger Bands mean reversion
        for window in [20]:
            if len(data) >= window:
                ma = data['close'].rolling(window=window).mean()
                std = data['close'].rolling(window=window).std()

                upper_band = ma + 2 * std
                lower_band = ma - 2 * std

                # Position within bands (0 to 1, 0.5 is at the middle)
                band_pos = (data['close'] - lower_band) / (upper_band - lower_band)
                result[f'reversion_bb_pos_{window}'] = band_pos

                # Mean reversion potential
                result[f'reversion_bb_upper_{window}'] = (band_pos > 1).astype(int)
                result[f'reversion_bb_lower_{window}'] = (band_pos < 0).astype(int)

                # Return to mean indicator
                result[f'reversion_to_mean_{window}'] = ((band_pos > 0.95) & (band_pos.shift(1) > 0.95) & (band_pos < band_pos.shift(1))).astype(int)
                result[f'reversion_to_mean_{window}'] = result[f'reversion_to_mean_{window}'] - ((band_pos < 0.05) & (band_pos.shift(1) < 0.05) & (band_pos > band_pos.shift(1))).astype(int)

        # Oscillation detection
        for window in [5, 10, 20]:
            if len(data) >= window * 2:
                # Detect price oscillations
                diff = data['close'].diff()
                sign_changes = np.abs(np.diff(np.sign(diff)))
                sign_changes = np.append(0, sign_changes)  # Add zero for the first point

                # Rolling sum of sign changes
                result[f'reversion_oscillations_{window}'] = pd.Series(sign_changes).rolling(window=window).sum()

            # Hurst exponent estimation (simplified)
            for window in [50, 100]:
                if len(data) >= window:
                    result[f'reversion_hurst_{window}'] = np.nan

                    # Calculate Hurst for each window
                    for i in range(window, len(data)):
                        series = data['close'].iloc[i-window:i]

                        try:
                            # Simplified Hurst estimation using log-log regression
                            lags = range(2, min(11, window // 4))
                            tau = [np.sqrt(np.std(np.subtract(series[lag:], series[:-lag]))) for lag in lags]

                            if all(t > 0 for t in tau):
                                reg = np.polyfit(np.log(lags), np.log(tau), 1)
                                result.loc[data.index[i], f'reversion_hurst_{window}'] = reg[0] / 2
                        except:
                            # Keep NaN on failure
                            pass

            # Mean reversion metrics
            for window in [5, 10, 20]:
                if len(data) >= window:
                    # Half-life of mean reversion (Ornstein-Uhlenbeck)
                    # Y(t) - Y(t-1) = a * Y(t-1) + e(t)
                    # half_life = log(2) / abs(a)

                    # Simplification: we use price's distance from its moving average
                    y = data['close'] - data['close'].rolling(window=window).mean()
                    y_lag = y.shift(1)

                    # Avoid initial NaN values
                    valid_data = [PARTIAL](y.isna() | y_lag.isna())
                    if valid_data.sum() > 10:  # Need enough data points
                        try:
                            # Simple linear regression coefficient
                            y_valid = y[valid_data]
                            y_lag_valid = y_lag[valid_data]

                            beta = np.cov(y_valid, y_lag_valid)[0, 1] / np.var(y_lag_valid)
                            beta = max(min(beta, 0.999), -0.999)  # Constrain to prevent extreme values

                            # Calculate half-life
                            half_life = -np.log(2) / np.log(abs(beta))
                            result[f'reversion_half_life_{window}'] = half_life
                        except:
                            result[f'reversion_half_life_{window}'] = np.nan

        # Mean reversion indicators if TALib is available
        if HAS_TALIB:
            try:
                # Percentage Price Oscillator
                result['reversion_ppo'] = talib.PPO(
                    data['close'].values, fastperiod=12, slowperiod=26, matype=0
                )

                # TRIX (Triple Exponential Moving Average)
                result['reversion_trix'] = talib.TRIX(data['close'].values, timeperiod=30)
            except Exception as e:
                logger.warning(f"Failed to compute mean reversion indicators with TALib: {e}")

        # Log results
        logger.debug(f"Extracted {result.shape[1]} mean reversion features")
        return result

    def extract_all_features(self, data: pd.DataFrame,
                           related_data: Optional[Dict[str, pd.DataFrame]] = None,
                           sentiment_data: Optional[pd.DataFrame] = None,
                           transform: bool = True,
                           transform_method: str = 'standard',
                           target: Optional[pd.Series] = None,
                           select_features: bool = False,
                           selection_method: str = 'importance',
                           n_features: Optional[int] = None,
                           required_features: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Extract all features and optionally transform and select features.
        This is the main public interface for feature engineering.

        Args:
            data: DataFrame with OHLCV price data
            related_data: Optional dictionary mapping asset IDs to their OHLCV DataFrames
            sentiment_data: Optional DataFrame with sentiment data
            transform: Whether to transform features
            transform_method: Transformation method ('standard', 'minmax', 'robust')
            target: Optional target variable for feature selection
            select_features: Whether to perform feature selection
            selection_method: Feature selection method
            n_features: Number of features to select
            required_features: List of feature names that must be present in the output

        Returns:
            DataFrame with extracted, transformed, and selected features
        """
        logger.info("Extracting all features with transformation and selection")

        # Extract all features
        all_features = self.create_all_features(data, related_data, sentiment_data)

        if all_features.empty:
            logger.error("Failed to extract features")
            # Create a minimal DataFrame with default values if we have required features
            if required_features:
                logger.warning(f"Creating minimal feature set with {len(required_features)} required features")
                all_features = pd.DataFrame(0, index=data.index, columns=required_features)
            else:
                return pd.DataFrame()

        # Check for required features and add them if missing
        if required_features:
            missing_features = [f for f in required_features if f not in all_features.columns]
            if missing_features:
                logger.warning(f"Adding {len(missing_features)} missing required features with default values")
                for feature in missing_features:
                    all_features[feature] = 0.0

        # Apply transformation if requested
        if transform:
            try:
                if self.transformer is None or self.transformer.method != transform_method:
                    logger.info(f"Creating new transformer with method '{transform_method}'")
                    # Import here to avoid circular imports
                    from ml_transformers import FeatureTransformer
                    self.transformer = FeatureTransformer(method=transform_method)

                logger.info("Transforming features")
                all_features = self.transformer.fit_transform(all_features)
            except Exception as e:
                logger.error(f"Error during feature transformation: {e}")
                # Continue with untransformed features

        # Apply feature selection if requested and target is provided
        if select_features and target is not None:
            try:
                if self.selector is None or self.selector.method != selection_method:
                    logger.info(f"Creating new selector with method '{selection_method}'")
                    # Import here to avoid circular imports
                    from ml_selectors import FeatureSelector
                    self.selector = FeatureSelector(method=selection_method, n_features=n_features)

                logger.info(f"Selecting features using '{selection_method}' method")

                # Make sure target has the same index as features
                if not target.index.equals(all_features.index):
                    logger.warning("Target index doesn't match features index, aligning")
                    target = target.reindex(all_features.index)

                # Remove NaN values from target
                valid_idx = [PARTIAL]target.isna()
                if not valid_idx.all():
                    logger.warning(f"Removing {([PARTIAL]valid_idx).sum()} NaN values from target")
                    target = target[valid_idx]
                    all_features = all_features.loc[valid_idx]

                all_features = self.selector.fit_transform(all_features, target)

                # Log top features
                top_features = self.selector.get_top_features(10)
                logger.info(f"Top 10 features: {[f[0] for f in top_features]}")

                # Make sure required features are still present after selection
                if required_features:
                    missing_after_selection = [f for f in required_features if f not in all_features.columns]
                    if missing_after_selection:
                        logger.warning(f"Re-adding {len(missing_after_selection)} required features lost during selection")
                        for feature in missing_after_selection:
                            all_features[feature] = 0.0
            except Exception as e:
                logger.error(f"Error during feature selection: {e}")
                # Continue with all features

        # Replace NaN values with 0
        all_features = all_features.fillna(0)

        logger.info(f"Final feature set contains {all_features.shape[1]} features")
        return all_features

    def create_all_features(self, data: pd.DataFrame,
                           related_data: Optional[Dict[str, pd.DataFrame]] = None,
                           sentiment_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Create all features from all feature groups.

        Args:
            data: DataFrame with OHLCV price data
            related_data: Optional dictionary mapping asset IDs to their OHLCV DataFrames
            sentiment_data: Optional DataFrame with sentiment data

        Returns:
            DataFrame with all features
        """
        if not self._validate_data(data):
            logger.error("Invalid data provided for feature extraction")
            return pd.DataFrame()

        logger.info(f"Creating all features from {len(self.feature_groups)} feature groups")

        # Time extraction
        start_time = time.time()

        # Track features from each group
        feature_groups = {}

        # Extract features from each group based on configuration
        for group in self.feature_groups:
            if group == 'price':
                feature_groups['price'] = self.price_features(data)

            elif group == 'volume':
                feature_groups['volume'] = self.volume_features(data)

            elif group == 'volatility':
                feature_groups['volatility'] = self.volatility_features(data)

            elif group == 'momentum':
                feature_groups['momentum'] = self.momentum_features(data)

            elif group == 'mean_reversion':
                feature_groups['mean_reversion'] = self.mean_reversion_features(data)

            elif group == 'liquidity':
                feature_groups['liquidity'] = self.liquidity_features(data)

            elif group == 'market_impact':
                feature_groups['market_impact'] = self.market_impact_features(data)

            elif group == 'market_regime':
                feature_groups['market_regime'] = self.market_regime_features(data)

            elif group == 'correlation':
                feature_groups['correlation'] = self.correlation_features(data, related_data)

            elif group == 'sentiment':
                feature_groups['sentiment'] = self.sentiment_features(data, sentiment_data)

        # Combine all feature groups
        all_features = pd.DataFrame(index=data.index)
        total_features = 0

        for group, features in feature_groups.items():
            if features is not None and not features.empty:
                # Prefix feature names with group if not already done
                if not all(col.startswith(f"{group}_") for col in features.columns):
                    features = features.rename(columns={col: f"{group}_{col}" for col in features.columns if not col.startswith(f"{group}_")})

                all_features = pd.concat([all_features, features], axis=1)
                total_features += len(features.columns)
                logger.debug(f"Added {len(features.columns)} features from {group} group")

        # Calculate and log timing
        elapsed = time.time() - start_time
        logger.info(f"Created {total_features} features in {elapsed:.2f} seconds")

        return all_features
