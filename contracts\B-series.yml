task_id: B-STACK
name: Historical Back-test & ML Walk-Forward
version: 1.0.0
description: Four-stage batch job run manually or via cron
stages:
  - id: B-01_fetch_history
    module: tasks.fetch_history
    function: run
    output: data/history/{{ticker}}_bars.parquet
    timeout_min: 10
    retry_count: 3
  - id: B-02_build_features
    module: tasks.build_features
    function: run
    output: data/features/{{ticker}}_features.parquet
    timeout_min: 5
    retry_count: 2
  - id: B-03_walk_train_val
    module: tasks.walk_train_validate
    function: run
    output: models/backtest_model.pkl
    timeout_min: 15
    retry_count: 1
  - id: B-04_simulate_trades
    module: tasks.backtest_simulator
    function: run
    output: reports/backtest_{{date}}.md
    timeout_min: 5
    retry_count: 1
success_criteria:
  perf_budget:
    max_runtime_min: 30
  sharpe_min: 1.0
  max_drawdown_pct: 15
  min_trades: 10
  min_accuracy: 0.55
validation:
  output_required: true
  data_freshness_hours: 24
  model_accuracy_threshold: 0.50
dependencies:
  - pandas>=1.5.0
  - numpy>=1.21.0
  - scikit-learn>=1.1.0
  - joblib>=1.2.0
  - talib-binary>=0.4.0
