task_id: BREAKOUT_VALIDATION_SPECIALIST
name: Breakout Validation Specialist Agent
version: 1.0.0
description: Specialized agent for validating breakout authenticity with 90%+ accuracy
            through volume confirmation, support/resistance analysis, and false pattern detection.

inputs:
  - price_data: np.ndarray        # OHLC or close price data
  - volume_data: np.ndarray       # Volume data array
  - resistance_level: float       # Key resistance level (optional)
  - support_level: float          # Key support level (optional)
  - symbol: str                   # Ticker symbol
  - breakout_direction: str       # 'up' or 'down'

outputs:
  data:
    type: Dict[str, Any]
    schema: schemas/breakout_validation_specialist_output_v1.json
    
success_criteria:
  perf_budget:
    max_runtime_ms: 5000
  precision_threshold: 0.001
  code_coverage_min: 0.90
  output_completeness: 1.00
  accuracy_target: 0.90
  dependency_constraints:
    max_additional_deps: 2
    forbidden_packages:
      - tensorflow
      - torch
      - opencv-python
    allowed_packages:
      - scikit-learn  # ML operations
      - scipy         # Statistical calculations

mathematical_requirements:
  - numerical_stability: true
  - precision_validation: required
  - statistical_rigor: enforced
  - breakout_accuracy: ">= 90%"
  - false_positive_rate: "<= 10%"

specialization:
  focus: "THE ONE THING - Breakout Validation and False Signal Detection"
  methodology: "Volume Confirmation + Level Strength + Pattern Recognition"
  expertise: "Distinguishing real breakouts from false breakouts"
  
features:
  volume_analysis:
    - expansion_ratio
    - volume_trend
    - volume_price_correlation
    - confirmation_thresholds
  level_analysis:
    - support_resistance_strength
    - touch_count_analysis
    - level_volume_analysis
    - retest_quality_assessment
  pattern_detection:
    - false_breakout_patterns
    - immediate_reversal_detection
    - low_volume_continuation
    - gap_closure_analysis
  momentum_analysis:
    - sustainability_metrics
    - follow_through_strength
    - momentum_consistency
    - breakout_speed

validation_criteria:
  breakout_strength_threshold: 0.02    # 2% minimum breakout
  volume_expansion_threshold: 1.5      # 1.5x volume expansion
  retest_tolerance: 0.03               # 3% retest tolerance
  false_signal_tolerance: 0.15         # Max 15% false signals

data_sources:
  primary: schwab_mcp
  required_fields:
    - OHLC_data
    - volume_data
    - support_resistance_levels
    - timestamp
  
schwab_integration:
  mcp_server: "api/schwab_mcp_server.py"
  data_agent: "agents/schwab_data_agent.py"
  real_time: true
  level_detection: automated
  
validation_framework:
  input_validation: comprehensive
  output_validation: probability_bounds_check
  precision_monitoring: continuous
  performance_tracking: enabled
  false_positive_monitoring: enabled
