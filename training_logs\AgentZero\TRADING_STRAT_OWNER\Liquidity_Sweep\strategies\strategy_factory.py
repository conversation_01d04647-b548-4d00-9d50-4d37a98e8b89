"""
Strategy Factory - Factory for creating and managing trading strategies.

This module provides a factory pattern implementation for creating strategy instances
and managing strategy lifecycle. It ensures proper initialization and configuration
of all strategy types.

Author: VIRT Trading Team
Date: December 2024
"""

import logging
from typing import Dict, List, Optional, Type, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from pathlib import Path
import sys
import os

# Ensure package root is in Python path
package_root = Path(__file__).parent.parent
if str(package_root) not in sys.path:
    sys.path.insert(0, str(package_root))

from strategies.base_strategy import BaseStrategy
from strategies.gamma_squeeze_strategy import GammaSqueezeStrategy
from strategies.flow_momentum_strategy import FlowMomentumStrategy
from strategies.liquidity_sweep_strategy import LiquiditySweepStrategy
from strategies.pure_liquidity_strategy import PureLiquidityStrategy
from strategies.liquidity_void_strategy import LiquidityVoidStrategy
from strategies.vanna_charm_squeeze_strategy import VannaCharmSqueezeStrategy
from strategies.enhanced_flow_physics_strategy import EnhancedFlowPhysicsStrategy

logger = logging.getLogger(__name__)


class StrategyFactory:
    """
    Factory class for creating and managing trading strategies.

    This class provides:
    - Strategy registration and discovery
    - Dynamic strategy instantiation
    - Configuration management
    - Batch strategy execution
    - Performance tracking
    """

    # Registry of available strategies
    _strategies: Dict[str, Type[BaseStrategy]] = {
        'gamma_squeeze': GammaSqueezeStrategy,
        'flow_momentum': FlowMomentumStrategy,
        'liquidity_sweep': LiquiditySweepStrategy,
        # 'divergence': DivergenceStrategy,  # Not implemented - removed
        'pure_liquidity': PureLiquidityStrategy,
        'liquidity_void': LiquidityVoidStrategy,
        'vanna_charm_squeeze': VannaCharmSqueezeStrategy,
        'enhanced_flow_physics': EnhancedFlowPhysicsStrategy
    }

    # Default strategy configurations
    _default_configs: Dict[str, Dict[str, Any]] = {
        'gamma_squeeze': {
            'min_gex_threshold': 1000000,
            'min_volume_ratio': 1.5,
            'consolidation_periods': 10,
            'breakout_threshold': 0.02
        },
        'flow_momentum': {
            'momentum_window': 20,
            'volume_window': 20,
            'min_flow_velocity': 1.5,
            'divergence_threshold': 0.1
        },
        'liquidity_sweep': {
            'sweep_threshold': 0.005,
            'rejection_speed': 0.8,
            'volume_spike': 2.0,
            'lookback_periods': 50
        },
        # 'divergence': {  # Not implemented - removed
        #     'divergence_window': 14,
        #     'swing_threshold': 0.02,
        #     'min_divergence_periods': 5,
        #     'confluence_weight': 1.5
        # },
        'enhanced_flow_physics': {
            'min_confidence': 0.75,
            'flow_velocity_threshold': 0.5,
            'flow_acceleration_threshold': 0.3,
            'institutional_flow_threshold': 0.7,
            'liquidity_weight': 0.3,
            'greeks_weight': 0.25,
            'physics_weight': 0.45,
            'min_component_agreement': 2,
            'confluence_boost': 0.2
        },
        'liquidity_void': {
            'min_confidence': 0.70,
            'min_void_size': 0.015,
            'max_void_distance': 0.008,
            'min_flow_velocity': 0.4,
            'volume_threshold_ratio': 0.3,
            'stop_loss_atr': 1.5,
            'void_stop_multiplier': 1.2,
            'primary_timeframe': '15m',
            'analysis_lookback': 100
        },
        'vanna_charm_squeeze': {
            'min_confidence': 0.75,
            'min_vanna_exposure': 50000,
            'min_charm_exposure': 10000,
            'vanna_pin_threshold': 100000,
            'charm_acceleration_threshold': 25000,
            'min_iv_skew': 0.03,
            'max_dte': 45,
            'min_dte': 1,
            'breakout_probability_threshold': 0.65,
            'base_position_size': 0.6,
            'max_loss_per_trade': 0.03
        }
    }

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the strategy factory.

        Args:
            config_path: Optional path to custom configuration file
        """
        self.config_path = config_path
        self.custom_configs = self._load_custom_configs()
        self.performance_stats: Dict[str, Dict[str, Any]] = {}

        logger.info(f"StrategyFactory initialized with {len(self._strategies)} strategies")

    def _load_custom_configs(self) -> Dict[str, Dict[str, Any]]:
        """Load custom strategy configurations from file."""
        if not self.config_path or not Path(self.config_path).exists():
            return {}

        try:
            with open(self.config_path, 'r') as f:
                configs = json.load(f)
                logger.info(f"Loaded custom configurations from {self.config_path}")
                return configs.get('strategies', {})
        except Exception as e:
            logger.error(f"Error loading custom configs: {e}")
            return {}

    @classmethod
    def register_strategy(cls, name: str, strategy_class: Type[BaseStrategy]) -> None:
        """
        Register a new strategy with the factory.

        Args:
            name: Strategy identifier
            strategy_class: Strategy class to register
        """
        if not issubclass(strategy_class, BaseStrategy):
            raise ValueError(f"{strategy_class} must inherit from BaseStrategy")

        cls._strategies[name] = strategy_class
        logger.info(f"Registered strategy: {name}")

    @classmethod
    def get_available_strategies(cls) -> List[str]:
        """Get list of available strategy names."""
        return list(cls._strategies.keys())

    def create_strategy(self,
                       name: str,
                       custom_config: Optional[Dict[str, Any]] = None) -> BaseStrategy:
        """
        Create a strategy instance.

        Args:
            name: Strategy name
            custom_config: Optional custom configuration

        Returns:
            Initialized strategy instance

        Raises:
            ValueError: If strategy name is not registered
        """
        if name not in self._strategies:
            raise ValueError(f"Unknown strategy: {name}. Available: {self.get_available_strategies()}")

        # Merge configurations: default -> file config -> custom config
        config = self._default_configs.get(name, {}).copy()

        if name in self.custom_configs:
            config.update(self.custom_configs[name])

        if custom_config:
            config.update(custom_config)

        # Create strategy instance
        strategy_class = self._strategies[name]
        strategy = strategy_class(config=config)

        logger.info(f"Created {name} strategy with config: {config}")
        return strategy

    def create_all_strategies(self,
                            strategies: Optional[List[str]] = None,
                            custom_configs: Optional[Dict[str, Dict[str, Any]]] = None) -> Dict[str, BaseStrategy]:
        """
        Create multiple strategy instances.

        Args:
            strategies: List of strategy names (None for all)
            custom_configs: Custom configurations per strategy

        Returns:
            Dictionary mapping strategy names to instances
        """
        if strategies is None:
            strategies = self.get_available_strategies()

        custom_configs = custom_configs or {}
        instances = {}

        for name in strategies:
            try:
                custom_config = custom_configs.get(name)
                instances[name] = self.create_strategy(name, custom_config)
            except Exception as e:
                logger.error(f"Failed to create strategy {name}: {e}")

        logger.info(f"Created {len(instances)} strategy instances")
        return instances

    def run_strategies(self,
                      data: Dict[str, Any],
                      strategies: Optional[List[str]] = None,
                      parallel: bool = True,
                      max_workers: int = 4) -> Dict[str, Any]:
        """
        Run multiple strategies on data.

        Args:
            data: Market data for analysis
            strategies: List of strategies to run (None for all)
            parallel: Whether to run strategies in parallel
            max_workers: Maximum parallel workers

        Returns:
            Dictionary with results from each strategy
        """
        # Create strategy instances
        strategy_instances = self.create_all_strategies(strategies)

        if not strategy_instances:
            logger.warning("No strategies to run")
            return {}

        results = {}

        if parallel and len(strategy_instances) > 1:
            # Run strategies in parallel
            with ThreadPoolExecutor(max_workers=min(max_workers, len(strategy_instances))) as executor:
                future_to_strategy = {
                    executor.submit(self._run_single_strategy, name, strategy, data): name
                    for name, strategy in strategy_instances.items()
                }

                for future in as_completed(future_to_strategy):
                    strategy_name = future_to_strategy[future]
                    try:
                        result = future.result()
                        results[strategy_name] = result
                    except Exception as e:
                        logger.error(f"Strategy {strategy_name} failed: {e}")
                        results[strategy_name] = {'error': str(e)}
        else:
            # Run strategies sequentially
            for name, strategy in strategy_instances.items():
                try:
                    result = self._run_single_strategy(name, strategy, data)
                    results[name] = result
                except Exception as e:
                    logger.error(f"Strategy {name} failed: {e}")
                    results[name] = {'error': str(e)}

        # Update performance statistics
        self._update_performance_stats(results)

        return results

    def _run_single_strategy(self,
                           name: str,
                           strategy: BaseStrategy,
                           data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Run a single strategy and track performance.

        Args:
            name: Strategy name
            strategy: Strategy instance
            data: Market data

        Returns:
            Strategy analysis results
        """
        import time

        start_time = time.time()

        try:
            # Run strategy analysis
            signals = strategy.analyze_multi(data)

            # Calculate execution time
            execution_time = time.time() - start_time

            # Prepare results
            result = {
                'signals': signals,
                'execution_time': execution_time,
                'signal_count': len(signals) if signals else 0,
                'strategy_name': name,
                'timestamp': time.time()
            }

            # Calculate signal statistics
            if signals:
                confidences = [s.confidence for s in signals]
                result['avg_confidence'] = sum(confidences) / len(confidences)
                result['max_confidence'] = max(confidences)
                result['min_confidence'] = min(confidences)

            logger.info(f"Strategy {name} completed in {execution_time:.3f}s, "
                       f"generated {result['signal_count']} signals")

            return result

        except Exception as e:
            logger.error(f"Error running strategy {name}: {e}")
            raise

    def _update_performance_stats(self, results: Dict[str, Any]) -> None:
        """Update performance statistics for strategies."""
        for strategy_name, result in results.items():
            if 'error' in result:
                continue

            if strategy_name not in self.performance_stats:
                self.performance_stats[strategy_name] = {
                    'total_runs': 0,
                    'total_signals': 0,
                    'total_execution_time': 0,
                    'avg_confidence': 0,
                    'signal_history': []
                }

            stats = self.performance_stats[strategy_name]
            stats['total_runs'] += 1
            stats['total_signals'] += result.get('signal_count', 0)
            stats['total_execution_time'] += result.get('execution_time', 0)

            # Update rolling average confidence
            if 'avg_confidence' in result:
                prev_avg = stats['avg_confidence']
                n = stats['total_runs']
                stats['avg_confidence'] = (prev_avg * (n - 1) + result['avg_confidence']) / n

            # Keep last 100 signal counts for trend analysis
            stats['signal_history'].append(result.get('signal_count', 0))
            if len(stats['signal_history']) > 100:
                stats['signal_history'].pop(0)

    def get_performance_report(self) -> Dict[str, Any]:
        """
        Get performance report for all strategies.

        Returns:
            Performance statistics and analysis
        """
        report = {
            'strategy_stats': self.performance_stats,
            'summary': {
                'total_strategies': len(self._strategies),
                'active_strategies': len(self.performance_stats),
                'total_signals_generated': sum(
                    stats['total_signals']
                    for stats in self.performance_stats.values()
                )
            }
        }

        # Calculate best performing strategies
        if self.performance_stats:
            # By signal count
            by_signals = sorted(
                self.performance_stats.items(),
                key=lambda x: x[1]['total_signals'],
                reverse=True
            )
            report['summary']['most_active_strategy'] = by_signals[0][0] if by_signals else None

            # By confidence
            by_confidence = sorted(
                [(k, v) for k, v in self.performance_stats.items() if v['avg_confidence'] > 0],
                key=lambda x: x[1]['avg_confidence'],
                reverse=True
            )
            report['summary']['highest_confidence_strategy'] = by_confidence[0][0] if by_confidence else None

            # By speed
            by_speed = sorted(
                self.performance_stats.items(),
                key=lambda x: x[1]['total_execution_time'] / max(x[1]['total_runs'], 1)
            )
            report['summary']['fastest_strategy'] = by_speed[0][0] if by_speed else None

        return report

    def optimize_strategy_params(self,
                               strategy_name: str,
                               data_samples: List[Dict[str, Any]],
                               param_ranges: Dict[str, tuple],
                               metric: str = 'signal_count') -> Dict[str, Any]:
        """
        Optimize strategy parameters using grid search.

        Args:
            strategy_name: Strategy to optimize
            data_samples: Sample data for testing
            param_ranges: Parameter ranges to test
            metric: Optimization metric

        Returns:
            Optimal parameters and performance metrics
        """
        logger.info(f"Starting parameter optimization for {strategy_name}")

        best_params = None
        best_score = -float('inf')
        results = []

        # Generate parameter combinations
        import itertools

        param_names = list(param_ranges.keys())
        param_values = [param_ranges[name] for name in param_names]

        for values in itertools.product(*param_values):
            params = dict(zip(param_names, values))

            try:
                # Test parameters
                strategy = self.create_strategy(strategy_name, params)
                total_score = 0

                for data in data_samples:
                    signals = strategy.analyze_multi(data)

                    if metric == 'signal_count':
                        score = len(signals) if signals else 0
                    elif metric == 'avg_confidence':
                        score = sum(s.confidence for s in signals) / len(signals) if signals else 0
                    else:
                        score = len(signals) if signals else 0

                    total_score += score

                avg_score = total_score / len(data_samples)
                results.append((params, avg_score))

                if avg_score > best_score:
                    best_score = avg_score
                    best_params = params

            except Exception as e:
                logger.error(f"Error testing params {params}: {e}")

        logger.info(f"Optimization complete. Best params: {best_params}, Score: {best_score}")

        return {
            'best_params': best_params,
            'best_score': best_score,
            'all_results': sorted(results, key=lambda x: x[1], reverse=True)[:10]
        }


# Strategy Factory Singleton
_factory_instance: Optional[StrategyFactory] = None


def get_strategy_factory(config_path: Optional[str] = None) -> StrategyFactory:
    """
    Get or create the strategy factory singleton.

    Args:
        config_path: Optional configuration file path

    Returns:
        StrategyFactory instance
    """
    global _factory_instance

    if _factory_instance is None:
        _factory_instance = StrategyFactory(config_path)

    return _factory_instance