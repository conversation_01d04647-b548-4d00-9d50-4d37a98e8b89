#!/usr/bin/env python3
"""
Schwab Token Refresh Utility
Automated token management for production deployment
"""

import os
import sys
import json
import time
import logging
from pathlib import Path

# Add core directory to path
script_dir = Path(__file__).parent.parent
sys.path.append(str(script_dir / "core"))

from schwab_production_api import TokenManager, SchwabAPIError

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def refresh_token():
    """Refresh Schwab authentication token"""
    try:
        config_path = script_dir / "config" / "config.json"
        token_path = script_dir / "config" / "schwab_token.json"
        
        # Initialize token manager
        token_manager = TokenManager(str(config_path), str(token_path))
        
        # Check current token status
        try:
            current_token = token_manager.get_valid_token()
            logger.info("Token refresh successful")
            logger.info("Schwab API authentication: ACTIVE")
            return True
            
        except SchwabAPIError as e:
            logger.error(f"Token refresh failed: {e}")
            return False
            
    except Exception as e:
        logger.error(f"Unexpected error during token refresh: {e}")
        return False

def check_token_status():
    """Check current token status and expiry"""
    try:
        token_path = script_dir / "config" / "schwab_token.json"
        
        with open(token_path, 'r') as f:
            token_data = json.load(f)
        
        current_time = time.time()
        expires_at = token_data.get('expires_at', 0)
        time_remaining = expires_at - current_time
        
        logger.info(f"Token expires in: {time_remaining / 3600:.2f} hours")
        
        if time_remaining < 300:  # Less than 5 minutes
            logger.warning("Token expires soon - refresh recommended")
            return False
        else:
            logger.info("Token status: VALID")
            return True
            
    except FileNotFoundError:
        logger.error("Token file not found - re-authentication required")
        return False
    except Exception as e:
        logger.error(f"Error checking token status: {e}")
        return False

if __name__ == "__main__":
    print("Schwab Token Refresh Utility")
    print("="*30)
    
    # Check current status
    if check_token_status():
        logger.info("Token is valid, no refresh needed")
    else:
        logger.info("Attempting token refresh...")
        if refresh_token():
            logger.info("Token refresh completed successfully")
        else:
            logger.error("Token refresh failed - manual re-authentication may be required")
