"""
Liquidity Sweep Detector - Enhanced Factor-Based Analyzer

Refactored from LiquiditySweepStrategy v3 to focus on institutional footprint detection
and factor generation rather than direct signal creation.

Key Philosophy Changes:
- From candle pattern recognition  institutional accumulation/distribution detection
- From volume spike requirements  quiet systematic activity preference
- From reactive signals  proactive factor generation for confluence engine
- From single analyzer approach  specialized component in factor ecosystem

This analyzer generates FactorData objects that capture:
1. Range containment quality factors
2. Absorption efficiency factors  
3. Net flow bias factors (integration with Enhanced Flow Physics)
4. Institutional footprint factors
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging
from typing import Dict, List, Optional, Any, Tuple
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import math

# Import factor specification
try:
    from .factor_specification import (
        FactorData, DirectionBias, TimeFrame,
        validate_factor, normalize_strength_score
    )
    from base_analyzer import BaseAnalyzer
except ImportError:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    sys.path.insert(0, parent_dir)
    from factor_specification import (
        FactorData, DirectionBias, TimeFrame,
        validate_factor, normalize_strength_score
    )
    from analyzers.base_analyzer import BaseAnalyzer

# API Gateway Import
try:
    from .api_robustness.unified_api_gateway import get_api_gateway
    UNIFIED_GATEWAY_AVAILABLE = True
except ImportError:
    try:
        import sys
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        api_path = os.path.join(current_dir, '..', 'api_robustness')
        if api_path not in sys.path:
            sys.path.insert(0, api_path)
        from api_robustness.unified_api_gateway import get_api_gateway
        UNIFIED_GATEWAY_AVAILABLE = True
    except ImportError:
        UNIFIED_GATEWAY_AVAILABLE = False
        get_api_gateway = None

logger = logging.getLogger(__name__)

class LiquiditySweepDetector(BaseAnalyzer):
    """
    Enhanced Liquidity Sweep Detector for institutional footprint detection.
    
    Generates factors related to:
    - Institutional accumulation/distribution within ranges
    - Liquidity level absorption efficiency
    - Range quality and containment metrics
    - Net flow bias determination (integrates with flow physics)
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, system_config=None, api_gateway_instance=None):
        """Initialize the detector with enhanced institutional focus."""
        super().__init__(config, system_config, api_gateway_instance)
        self.name = "LiquiditySweepDetector"
        
        # Initialize API gateway if available
        self.api_gateway = None
        if UNIFIED_GATEWAY_AVAILABLE and get_api_gateway:
            try:
                self.api_gateway = get_api_gateway()
                logger.info(f"[{self.name}] Initialized API Gateway successfully")
            except Exception as e:
                logger.warning(f"[{self.name}] Failed to initialize API Gateway: {e}")
        
        # Enhanced configuration with institutional focus
        self.config = self._get_enhanced_config(config)
        
        logger.info(f"{self.name} initialized with institutional footprint detection enabled")

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration for this analyzer (required by BaseAnalyzer)."""
        return self._get_enhanced_config({})

    def _get_enhanced_config(self, user_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Get enhanced configuration optimized for institutional detection."""
        default_config = {
            'enabled': True,
            'min_factor_strength': 0.3,  # Lower threshold for factor generation
            'max_factors_per_timeframe': 5,
            
            # Level identification (from robust strategy)
            'level_identification': {
                'lookback_bars': 80,
                'min_touches_for_level': 2,
                'clustering_tolerance_pct': 0.003,
                'min_level_strength_final': 0.40,  # Lowered for factor generation
                'dynamic_precision_enabled': True,
                'weighted_hits_significance': 2.0,
                'failed_breakout_bonus_weight': 0.3,
            },
            
            # Enhanced institutional analysis (NEW FOCUS)
            'institutional_analysis': {
                'enabled': True,
                'quiet_accumulation_detection': True,
                'range_based_analysis': True,
                'net_liquidity_analysis': True,
                'systematic_pattern_detection': True,
                'min_campaign_duration_hours': 4,   # Lowered for sensitivity
                'max_campaign_duration_hours': 96,
                'accumulation_threshold': 0.6,
                'distribution_threshold': 0.6,
            },
            
            # Flow physics integration (ENHANCED)
            'flow_physics_integration': {
                'enabled': True,
                'use_net_flow_bias': True,
                'flow_strength_threshold': 0.4,    # Lowered threshold
                'quiet_flow_preference': True,
                'systematic_flow_detection': True,
                'accumulation_flow_patterns': True,
                'distribution_flow_patterns': True,
            },
            
            # Range analysis (CORE CAPABILITY)
            'range_analysis': {
                'enabled': True,
                'min_range_quality': 0.4,          # Lowered for factor generation
                'accumulation_detection': True,
                'distribution_detection': True,
                'campaign_stage_detection': True,
                'range_compression_analysis': True,
                'absorption_efficiency_tracking': True,
            },
            
            # Absorption quality analysis (NEW)
            'absorption_analysis': {
                'enabled': True,
                'efficiency_tracking': True,
                'volume_concentration_analysis': True,
                'time_at_level_analysis': True,
                'systematic_absorption_detection': True,
                'absorption_trend_analysis': True,
            },
            
            # Volume analysis (MODIFIED APPROACH)
            'volume_analysis': {
                'require_volume_spikes': False,     # REMOVED requirement
                'quiet_volume_preference': True,    # NEW preference
                'systematic_volume_patterns': True,
                'absorption_volume_analysis': True,
                'volume_distribution_analysis': True,
                'normal_volume_multiplier': 1.2,   # Low threshold for systematic activity
            },
            
            # Factor generation settings
            'factor_generation': {
                'range_containment_factors': True,
                'absorption_quality_factors': True,
                'flow_bias_factors': True,
                'institutional_footprint_factors': True,
                'campaign_stage_factors': True,
                'strength_normalization': True,
                'confidence_scaling': True,
            },
            
            # Market regime awareness
            'market_regime': {
                'volatility_lookback': 20,
                'trend_lookback': 15,
                'high_vol_threshold': 0.025,
                'low_vol_threshold': 0.008,
                'trending_threshold': 0.015,
                'ranging_threshold': 0.005,
            }
        }
        
        # Merge with user config
        merged_config = default_config.copy()
        if user_config:
            self._deep_merge_config(merged_config, user_config)
        
        return merged_config

    def _deep_merge_config(self, base: Dict, override: Dict):
        """Deep merge configuration dictionaries."""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._deep_merge_config(base[key], value)
            else:
                base[key] = value

    def get_enabled_timeframes(self) -> List[TimeFrame]:
        """Get list of enabled timeframes for analysis."""
        # Default timeframes for liquidity sweep analysis
        return [TimeFrame.MIN_15, TimeFrame.HOUR_1, TimeFrame.HOUR_4]

    def _get_timeframe_data(self, mtf_data: Dict[TimeFrame, pd.DataFrame], timeframe: TimeFrame) -> Optional[pd.DataFrame]:
        """Extract timeframe data from multi-timeframe data package."""
        return mtf_data.get(timeframe)

    def analyze_factors(self, **kwargs) -> List[FactorData]:
        """
        Standardized factor analysis method for orchestrator interface.
        
        Expected kwargs:
            ticker: str
            current_price: float  
            mtf_data or mtf_market_data: Dict[str, pd.DataFrame] or Dict[TimeFrame, pd.DataFrame]
            
        Returns:
            List[FactorData]: Generated liquidity sweep factors
        """
        if not self.config.get('enabled', True):
            return []
            
        # Extract parameters from kwargs
        ticker = kwargs.get('ticker')
        current_price = kwargs.get('current_price', 0)
        
        # Handle different mtf_data parameter names
        mtf_data = kwargs.get('mtf_data') or kwargs.get('mtf_market_data') or {}
        
        # Handle data_package format
        data_package = kwargs.get('data_package')
        if data_package:
            ticker = data_package.get('ticker') or ticker
            current_price = data_package.get('current_price') or current_price
            mtf_data = data_package.get('mtf_market_data') or data_package.get('mtf_data') or mtf_data
        
        if not ticker:
            logger.warning("No ticker provided to LiquiditySweepDetector")
            return []
            
        if not mtf_data or current_price <= 0:
            logger.warning(f"[{ticker}] Invalid data - mtf_data: {bool(mtf_data)}, price: {current_price}")
            return []
        
        # Convert string-keyed mtf_data to TimeFrame-keyed for backward compatibility
        timeframe_mtf_data = {}
        
        # Mapping from string to TimeFrame enum
        tf_mapping = {
            '1m': TimeFrame.MIN_1, '5m': TimeFrame.MIN_5, '15m': TimeFrame.MIN_15,
            '30m': TimeFrame.MIN_30, '1h': TimeFrame.HOUR_1, '4h': TimeFrame.HOUR_4,
            '1d': TimeFrame.DAY_1, '1w': TimeFrame.WEEK_1
        }
        
        for tf_key, data in mtf_data.items():
            if hasattr(tf_key, 'value'):  # Already TimeFrame enum
                timeframe_mtf_data[tf_key] = data
            elif tf_key in tf_mapping:  # String key
                timeframe_mtf_data[tf_mapping[tf_key]] = data
            else:
                logger.warning(f"[{ticker}] Unknown timeframe key: {tf_key}")
        
        # Call the legacy analyze method with converted data
        return self.analyze(
            ticker=ticker,
            mtf_data=timeframe_mtf_data,
            current_price=current_price,
            **{k: v for k, v in kwargs.items() if k not in ['ticker', 'current_price', 'mtf_data', 'mtf_market_data', 'data_package']}
        )

    def analyze(self, 
               ticker: str, 
               mtf_data: Dict[TimeFrame, pd.DataFrame], 
               current_price: float,
               **kwargs) -> List[FactorData]:
        """
        Analyze multi-timeframe data for institutional liquidity sweep patterns.
        
        Args:
            ticker: Stock ticker symbol
            mtf_data: Multi-timeframe price data
            current_price: Current market price
            **kwargs: Additional data (flow_physics_data, options_data, etc.)
            
        Returns:
            List[FactorData]: Institutional footprint factors detected
        """
        if not self.config.get('enabled', True):
            return []
        
        factors = []
        
        try:
            # Extract additional data
            flow_physics_data = kwargs.get('flow_physics_data', {})
            analysis_results = kwargs.get('analysis_results', {})
            
            # Process each enabled timeframe
            enabled_timeframes = self.get_enabled_timeframes()
            logger.debug(f"[{ticker}] Enabled timeframes: {[tf.value for tf in enabled_timeframes]}")
            logger.debug(f"[{ticker}] Available data timeframes: {list(mtf_data.keys())}")

            for timeframe in enabled_timeframes:
                timeframe_data = self._get_timeframe_data(mtf_data, timeframe)
                if timeframe_data is None:
                    logger.debug(f"[{ticker}] No data for timeframe {timeframe.value}")
                    continue

                logger.debug(f"[{ticker}] Processing timeframe {timeframe.value} with {len(timeframe_data)} bars")
                
                # Generate factors for this timeframe
                tf_factors = self._analyze_timeframe(
                    ticker, timeframe, timeframe_data, current_price,
                    flow_physics_data, analysis_results
                )
                
                factors.extend(tf_factors)
                
                # Limit factors per timeframe
                max_factors = self.config.get('max_factors_per_timeframe', 5)
                if len(tf_factors) >= max_factors:
                    logger.debug(f"[{ticker}] {timeframe.value}: Generated {len(tf_factors)} factors (max {max_factors})")
            
            # Filter and validate factors
            factors = self._filter_and_validate_factors(factors)
            
            logger.info(f"[{ticker}] Generated {len(factors)} institutional footprint factors across timeframes")
            
        except Exception as e:
            logger.error(f"[{ticker}] Error in liquidity sweep analysis: {e}")
            
        return factors

    def _analyze_timeframe(self, 
                          ticker: str, 
                          timeframe: TimeFrame, 
                          price_data: pd.DataFrame,
                          current_price: float,
                          flow_physics_data: Dict[str, Any],
                          analysis_results: Dict[str, Any]) -> List[FactorData]:
        """Analyze single timeframe for institutional patterns."""
        factors = []
        
        try:
            # 1. Identify liquidity levels
            levels = self._identify_liquidity_levels(price_data, current_price)
            if not levels:
                return factors
            
            # 2. Identify active ranges from levels
            ranges = self._identify_active_ranges(price_data, levels, current_price)
            if not ranges:
                return factors
            
            # 3. Analyze institutional activity within ranges
            for range_info in ranges[:3]:  # Top 3 ranges
                range_factors = self._analyze_range_institutional_activity(
                    ticker, timeframe, price_data, range_info, 
                    flow_physics_data, current_price
                )
                factors.extend(range_factors)
            
            # 4. Generate absorption efficiency factors
            absorption_factors = self._generate_absorption_factors(
                ticker, timeframe, price_data, levels, current_price
            )
            factors.extend(absorption_factors)
            
            # 5. Generate flow bias factors (if flow physics data available)
            if flow_physics_data:
                flow_factors = self._generate_flow_bias_factors(
                    ticker, timeframe, flow_physics_data, ranges
                )
                factors.extend(flow_factors)
                
        except Exception as e:
            logger.error(f"[{ticker}] Error analyzing {timeframe.value}: {e}")
            
        return factors

    def _analyze_range_institutional_activity(self,
                                            ticker: str,
                                            timeframe: TimeFrame,
                                            price_data: pd.DataFrame,
                                            range_info: Dict[str, Any],
                                            flow_physics_data: Dict[str, Any],
                                            current_price: float) -> List[FactorData]:
        """Analyze institutional accumulation/distribution within a range."""
        factors = []
        
        try:
            # Extract range parameters
            support_price = range_info['support_level']['price']
            resistance_price = range_info['resistance_level']['price']
            range_quality = range_info['range_quality']
            
            # Determine net bias within range
            net_bias = self._determine_range_net_bias(
                price_data, range_info, flow_physics_data
            )
            
            # Assess campaign stage
            campaign_stage = self._assess_campaign_stage(
                price_data, range_info, net_bias
            )
            
            # Calculate institutional footprint strength
            institutional_strength = self._calculate_institutional_footprint_strength(
                price_data, range_info, net_bias, campaign_stage
            )
            
            # Generate range containment factor
            if range_quality >= self.config['range_analysis']['min_range_quality']:
                containment_factor = self._create_factor(
                    factor_name=f"Range_Containment_{timeframe.value}",
                    ticker=ticker,
                    timeframe=timeframe,
                    direction_bias=DirectionBias.NEUTRAL,
                    strength_score=range_quality,
                    details={
                        'support_price': support_price,
                        'resistance_price': resistance_price,
                        'range_width_pct': range_info['range_width_pct'],
                        'current_position_pct': range_info['current_position_pct'],
                        'combined_level_strength': range_info['combined_strength'],
                        'campaign_stage': campaign_stage
                    },
                    reason_short=f"Range containment quality {range_quality:.2f}"
                )
                factors.append(containment_factor)
            
            # Generate institutional campaign factor if significant activity detected
            if net_bias != 'neutral' and institutional_strength >= 0.5:
                direction = DirectionBias.BULLISH if net_bias == 'bullish' else DirectionBias.BEARISH
                
                campaign_factor = self._create_factor(
                    factor_name=f"Institutional_Campaign_{net_bias.title()}_{timeframe.value}",
                    ticker=ticker,
                    timeframe=timeframe,
                    direction_bias=direction,
                    strength_score=institutional_strength,
                    details={
                        'campaign_stage': campaign_stage,
                        'net_bias': net_bias,
                        'range_data': range_info,
                        'systematic_activity': True,
                        'quiet_accumulation': net_bias == 'bullish',
                        'quiet_distribution': net_bias == 'bearish'
                    },
                    reason_short=f"Institutional {net_bias} campaign in {campaign_stage} stage"
                )
                factors.append(campaign_factor)
            
        except Exception as e:
            logger.error(f"Error analyzing range institutional activity: {e}")
            
        return factors

    def _generate_absorption_factors(self,
                                   ticker: str,
                                   timeframe: TimeFrame,
                                   price_data: pd.DataFrame,
                                   levels: List[Dict[str, Any]],
                                   current_price: float) -> List[FactorData]:
        """Generate factors based on level absorption efficiency."""
        factors = []
        
        try:
            for level in levels[:5]:  # Top 5 levels
                level_price = level['price']
                level_type = level['type']
                
                # Analyze absorption efficiency for this level
                absorption_analysis = self._analyze_level_absorption_trend(
                    price_data, level_price
                )
                
                absorption_efficiency = absorption_analysis['absorption_efficiency']
                
                # Only generate factor if absorption is significant
                if absorption_efficiency >= 0.6:
                    direction = (DirectionBias.BULLISH if level_type == 'support' 
                               else DirectionBias.BEARISH)
                    
                    absorption_factor = self._create_factor(
                        factor_name=f"Absorption_Efficiency_{level_type.title()}_{timeframe.value}",
                        ticker=ticker,
                        timeframe=timeframe,
                        direction_bias=direction,
                        strength_score=absorption_efficiency,
                        details={
                            'level_price': level_price,
                            'level_type': level_type,
                            'level_strength': level['strength'],
                            'absorption_efficiency': absorption_efficiency,
                            'getting_stronger': absorption_analysis['getting_stronger'],
                            'trend_improvement': absorption_analysis['trend_improvement'],
                            'distance_pct': abs(level_price - current_price) / current_price
                        },
                        reason_short=f"{level_type.title()} absorption efficiency {absorption_efficiency:.2f}",
                        key_level_price=level_price
                    )
                    factors.append(absorption_factor)
                    
        except Exception as e:
            logger.error(f"Error generating absorption factors: {e}")
            
        return factors

    def _generate_flow_bias_factors(self,
                                  ticker: str,
                                  timeframe: TimeFrame,
                                  flow_physics_data: Dict[str, Any],
                                  ranges: List[Dict[str, Any]]) -> List[FactorData]:
        """Generate factors based on flow physics bias within ranges."""
        factors = []
        
        try:
            # Extract flow data
            net_flow_direction = flow_physics_data.get('net_flow_direction', 'neutral')
            flow_strength = flow_physics_data.get('flow_strength', 0.5)
            flow_consistency = flow_physics_data.get('flow_consistency', 0.5)
            
            # Only generate factor if flow is significant
            if (flow_strength >= self.config['flow_physics_integration']['flow_strength_threshold'] and
                net_flow_direction != 'neutral'):
                
                direction = (DirectionBias.BULLISH if net_flow_direction == 'bullish' 
                           else DirectionBias.BEARISH)
                
                # Calculate factor strength based on flow metrics
                factor_strength = (flow_strength * 0.7 + flow_consistency * 0.3)
                
                flow_factor = self._create_factor(
                    factor_name=f"Net_Flow_Bias_{net_flow_direction.title()}_{timeframe.value}",
                    ticker=ticker,
                    timeframe=timeframe,
                    direction_bias=direction,
                    strength_score=factor_strength,
                    details={
                        'net_flow_direction': net_flow_direction,
                        'flow_strength': flow_strength,
                        'flow_consistency': flow_consistency,
                        'systematic_flow': True,
                        'quiet_flow_detected': flow_physics_data.get('quiet_flow', False),
                        'range_context': len(ranges) > 0,
                        'primary_range': ranges[0] if ranges else None
                    },
                    reason_short=f"Net {net_flow_direction} flow bias strength {factor_strength:.2f}"
                )
                factors.append(flow_factor)
                
        except Exception as e:
            logger.error(f"Error generating flow bias factors: {e}")
            
        return factors

    def _identify_liquidity_levels(self, price_data: pd.DataFrame, current_price: float) -> List[Dict[str, Any]]:
        """Identify significant liquidity levels (from original strategy)."""
        levels = []
        cfg_level = self.config['level_identification']
        lookback = cfg_level['lookback_bars']

        if len(price_data) < lookback:
            return levels

        data_for_levels = price_data.tail(lookback)
        price_precision = self._get_dynamic_precision(current_price)

        # Find swing points
        raw_highs = self._find_swing_points(data_for_levels['high'], 'high')
        raw_lows = self._find_swing_points(data_for_levels['low'], 'low')

        candidate_levels_swing = [(h, 'resistance') for h in raw_highs] + \
                                [(l, 'support') for l in raw_lows]

        # Find weighted price hits
        hit_frequency = self._find_weighted_price_hits(data_for_levels, price_precision)
        candidate_levels_hits = []
        for price, hit_data in hit_frequency.items():
            if hit_data['weighted_score'] >= cfg_level['weighted_hits_significance']:
                tf_current_price = data_for_levels['close'].iloc[-1]
                level_type = 'resistance' if price > tf_current_price else 'support'
                candidate_levels_hits.append((price, level_type))

        all_raw_levels = candidate_levels_swing + candidate_levels_hits
        if not all_raw_levels:
            return levels

        # Cluster nearby levels
        clustered_levels = self._cluster_levels_enhanced(
            all_raw_levels, price_precision, cfg_level['clustering_tolerance_pct']
        )

        # Validate and score levels
        for level_price, level_type in clustered_levels:
            touches = self._count_level_touches(data_for_levels, level_price, price_precision * 2)
            if touches < cfg_level['min_touches_for_level']:
                continue

            # Calculate level strength
            level_strength = self._calculate_level_strength(
                data_for_levels, level_price, level_type, touches, hit_frequency, price_precision
            )

            if level_strength >= cfg_level['min_level_strength_final']:
                levels.append({
                    'price': level_price,
                    'type': level_type,
                    'touches': touches,
                    'strength': level_strength
                })

        return sorted(levels, key=lambda x: x['strength'], reverse=True)

    def _calculate_level_strength(self, data_for_levels, level_price, level_type, touches, hit_frequency, price_precision):
        """Calculate comprehensive level strength score."""
        # Base strength from touches
        base_strength = min(1.0, touches / 5.0)
        
        # Time weighted score
        time_weighted_score = 0
        closest_hit_price = min(hit_frequency.keys(), key=lambda p: abs(p-level_price), default=None)
        if closest_hit_price and abs(closest_hit_price - level_price) < price_precision * 2:
            time_weighted_score = hit_frequency[closest_hit_price]['weighted_score']
        
        norm_time_weighted = min(1.0, time_weighted_score / (self.config['level_identification']['weighted_hits_significance'] * 2))
        
        # Failed breakout bonus
        failed_breakout_bonus = self._check_failed_breakout(data_for_levels, level_price, level_type, price_precision)
        
        # Volume context
        volume_context = self._get_volume_context(data_for_levels, level_price)
        
        # Combine components
        level_strength = base_strength + norm_time_weighted * 0.2 + failed_breakout_bonus * 0.3
        level_strength *= volume_context.get('volume_score', 1.0)
        
        return min(1.0, level_strength)

    # Helper methods (simplified versions from original strategy)
    def _find_swing_points(self, series: pd.Series, point_type: str, swing_padding: int = 2) -> List[float]:
        """Find swing highs or lows."""
        points = []
        if len(series) < (2 * swing_padding + 1):
            return points

        for i in range(swing_padding, len(series) - swing_padding):
            is_swing = True
            current_val = series.iloc[i]
            for j in range(1, swing_padding + 1):
                if point_type == 'high':
                    if not (current_val > series.iloc[i-j] and current_val > series.iloc[i+j]):
                        is_swing = False
                        break
                else:
                    if not (current_val < series.iloc[i-j] and current_val < series.iloc[i+j]):
                        is_swing = False
                        break
            if is_swing:
                points.append(current_val)
        return list(set(points))

    def _get_dynamic_precision(self, price: float) -> float:
        """Calculate dynamic price precision."""
        if price <= 0: return 0.01
        if price < 1: return 0.005
        elif price < 10: return 0.01
        elif price < 50: return 0.05
        elif price < 100: return 0.10
        elif price < 500: return 0.25
        else: return 0.50

    def _find_weighted_price_hits(self, bars_df: pd.DataFrame, price_precision: float) -> Dict[float, Dict]:
        """Find price levels with weighted hit frequency."""
        from collections import defaultdict
        
        price_hits = defaultdict(lambda: {'count': 0, 'weighted_score': 0.0})
        if bars_df.empty: return price_hits

        total_bars = len(bars_df)
        for idx, (timestamp, bar) in enumerate(bars_df.iterrows()):
            time_weight = math.exp(-(total_bars - 1 - idx) / (total_bars * 0.2))
            
            prices_to_check = [bar['high'], bar['low'], bar['open'], bar['close']]
            unique_rounded_prices = set()
            
            for p_val in prices_to_check:
                if pd.isna(p_val): continue
                rounded_p = round(p_val / price_precision) * price_precision if price_precision > 0 else p_val
                unique_rounded_prices.add(rounded_p)

            for rounded_p in unique_rounded_prices:
                price_hits[rounded_p]['count'] += 1
                price_hits[rounded_p]['weighted_score'] += time_weight
                
        return price_hits

    def _cluster_levels_enhanced(self, raw_levels: List[Tuple[float, str]], price_precision: float, clustering_tolerance_pct: float) -> List[Tuple[float, str]]:
        """Cluster nearby price levels."""
        if not raw_levels: return []
        
        sorted_levels = sorted(raw_levels, key=lambda x: x[0])
        clustered = []
        
        current_cluster_prices = [sorted_levels[0][0]]
        current_cluster_types = [sorted_levels[0][1]]

        for i in range(1, len(sorted_levels)):
            price, level_type = sorted_levels[i]
            avg_cluster_price = np.mean(current_cluster_prices)
            
            absolute_tolerance = avg_cluster_price * clustering_tolerance_pct
            
            if abs(price - avg_cluster_price) <= absolute_tolerance:
                current_cluster_prices.append(price)
                current_cluster_types.append(level_type)
            else:
                # Finalize current cluster
                final_cluster_price = np.mean(current_cluster_prices)
                final_cluster_type = max(set(current_cluster_types), key=current_cluster_types.count)
                clustered.append((final_cluster_price, final_cluster_type))
                
                # Start new cluster
                current_cluster_prices = [price]
                current_cluster_types = [level_type]

        # Finalize last cluster
        if current_cluster_prices:
            final_cluster_price = np.mean(current_cluster_prices)
            final_cluster_type = max(set(current_cluster_types), key=current_cluster_types.count)
            clustered.append((final_cluster_price, final_cluster_type))

        return clustered

    def _count_level_touches(self, price_data: pd.DataFrame, level_price: float, tolerance: float) -> int:
        """Count touches of a level within tolerance."""
        touches = 0
        for _, bar in price_data.iterrows():
            if bar['low'] <= level_price + tolerance and bar['high'] >= level_price - tolerance:
                touches += 1
        return touches

    def _check_failed_breakout(self, price_data: pd.DataFrame, level_price: float, level_type: str, price_precision: float) -> float:
        """Check for failed breakouts to boost level strength."""
        if len(price_data) < 5: return 0.0
        
        failed_breakout_score = 0.0
        penetration_tolerance = price_precision * 3
        relevant_bars = price_data.tail(15)

        for i in range(len(relevant_bars) - 2):
            breakout_bar = relevant_bars.iloc[i]
            confirm1 = relevant_bars.iloc[i+1]
            confirm2 = relevant_bars.iloc[i+2]

            if level_type == 'resistance':
                if breakout_bar['high'] > level_price + penetration_tolerance:
                    if (breakout_bar['close'] < level_price or 
                        confirm1['close'] < level_price or 
                        confirm2['close'] < level_price):
                        failed_breakout_score += 0.1
            else:
                if breakout_bar['low'] < level_price - penetration_tolerance:
                    if (breakout_bar['close'] > level_price or 
                        confirm1['close'] > level_price or 
                        confirm2['close'] > level_price):
                        failed_breakout_score += 0.1

        return min(0.5, failed_breakout_score)

    def _get_volume_context(self, price_data: pd.DataFrame, level_price: float) -> Dict[str, Any]:
        """Get volume context for level validation."""
        try:
            avg_volume = price_data['volume'].rolling(20).mean().iloc[-1]
            if pd.isna(avg_volume) or avg_volume == 0:
                return {'volume_score': 1.0}
            
            # Find bars that tested the level
            tolerance = level_price * 0.005
            level_bars = price_data[
                (price_data['low'] <= level_price + tolerance) & 
                (price_data['high'] >= level_price - tolerance)
            ]
            
            if level_bars.empty:
                return {'volume_score': 1.0}
            
            avg_level_volume = level_bars['volume'].mean()
            volume_ratio = avg_level_volume / avg_volume if avg_volume > 0 else 1.0
            
            # Prefer normal to moderate volume (institutional preference)
            if 0.8 <= volume_ratio <= 2.0:
                volume_score = 1.1  # Slight bonus for normal volume
            elif volume_ratio > 2.0:
                volume_score = 0.9  # Slight penalty for high volume
            else:
                volume_score = 0.8  # Penalty for very low volume
                
            return {'volume_score': min(1.2, volume_score)}
            
        except Exception:
            return {'volume_score': 1.0}

    def _identify_active_ranges(self, price_data: pd.DataFrame, levels: List[Dict[str, Any]], current_price: float) -> List[Dict[str, Any]]:
        """Identify active ranges from liquidity levels."""
        ranges = []
        
        support_levels = [l for l in levels if l['type'] == 'support' and l['price'] < current_price]
        resistance_levels = [l for l in levels if l['type'] == 'resistance' and l['price'] > current_price]
        
        for support in support_levels:
            for resistance in resistance_levels:
                if support['price'] < current_price < resistance['price']:
                    range_width_pct = (resistance['price'] - support['price']) / support['price']
                    
                    if 0.005 <= range_width_pct <= 0.12:  # 0.5% to 12%
                        range_info = {
                            'support_level': support,
                            'resistance_level': resistance,
                            'range_width_pct': range_width_pct,
                            'current_position_pct': (current_price - support['price']) / (resistance['price'] - support['price']),
                            'combined_strength': (support['strength'] + resistance['strength']) / 2,
                            'range_quality': self._assess_range_quality(price_data, support, resistance)
                        }
                        ranges.append(range_info)
        
        return sorted(ranges, key=lambda x: x['combined_strength'], reverse=True)

    def _assess_range_quality(self, price_data: pd.DataFrame, support: Dict[str, Any], resistance: Dict[str, Any]) -> float:
        """Assess range quality for institutional activity."""
        strength_score = (support['strength'] + resistance['strength']) / 2
        
        range_touches = support['touches'] + resistance['touches']
        duration_score = min(1.0, range_touches / 8.0)
        
        range_width_pct = abs(resistance['price'] - support['price']) / support['price']
        if 0.01 <= range_width_pct <= 0.06:
            width_score = 1.0
        elif 0.005 <= range_width_pct <= 0.10:
            width_score = 0.7
        else:
            width_score = 0.3
            
        quality_score = strength_score * 0.5 + duration_score * 0.3 + width_score * 0.2
        return min(1.0, quality_score)

    def _determine_range_net_bias(self, price_data: pd.DataFrame, range_info: Dict[str, Any], flow_physics_data: Dict[str, Any]) -> str:
        """Determine net accumulation vs distribution bias."""
        
        # Primary: Use flow physics if available
        if flow_physics_data:
            net_flow = flow_physics_data.get('net_flow_direction', 'neutral')
            flow_strength = flow_physics_data.get('flow_strength', 0.5)
            
            if flow_strength > self.config['flow_physics_integration']['flow_strength_threshold']:
                return net_flow
        
        # Secondary: Analyze absorption trends
        support_level = range_info['support_level']
        resistance_level = range_info['resistance_level']
        
        recent_data = price_data.tail(20)
        support_absorption = self._analyze_level_absorption_trend(recent_data, support_level['price'])
        resistance_absorption = self._analyze_level_absorption_trend(recent_data, resistance_level['price'])
        
        if support_absorption['getting_stronger'] and not resistance_absorption['getting_stronger']:
            return 'bullish'
        elif resistance_absorption['getting_stronger'] and not support_absorption['getting_stronger']:
            return 'bearish'
        else:
            return 'neutral'

    def _analyze_level_absorption_trend(self, price_data: pd.DataFrame, level_price: float) -> Dict[str, Any]:
        """Analyze if level absorption is improving over time."""
        tolerance = level_price * 0.003
        
        level_tests = []
        for i, (timestamp, bar) in enumerate(price_data.iterrows()):
            if abs(bar['low'] - level_price) <= tolerance or abs(bar['high'] - level_price) <= tolerance:
                level_tests.append({
                    'index': i,
                    'timestamp': timestamp,
                    'held_level': (bar['close'] > level_price - tolerance) if level_price < bar['close'] else (bar['close'] < level_price + tolerance)
                })
        
        if len(level_tests) < 3:
            return {'getting_stronger': False, 'absorption_efficiency': 0.0, 'trend_improvement': 0.0}
        
        recent_tests = level_tests[-3:]
        early_tests = level_tests[:-3] if len(level_tests) > 3 else level_tests[:1]
        
        recent_success_rate = sum(1 for test in recent_tests if test['held_level']) / len(recent_tests)
        early_success_rate = sum(1 for test in early_tests if test['held_level']) / len(early_tests) if early_tests else 0.5
        
        return {
            'getting_stronger': recent_success_rate > early_success_rate,
            'absorption_efficiency': recent_success_rate,
            'trend_improvement': recent_success_rate - early_success_rate
        }

    def _assess_campaign_stage(self, price_data: pd.DataFrame, range_info: Dict[str, Any], net_bias: str) -> str:
        """Assess institutional campaign stage."""
        
        # Check range compression
        recent_ranges = []
        lookback = min(20, len(price_data))
        
        for i in range(lookback):
            bar = price_data.iloc[-(i+1)]
            bar_range = (bar['high'] - bar['low']) / bar['close']
            recent_ranges.append(bar_range)
        
        avg_recent_range = np.mean(recent_ranges[:5])
        avg_earlier_range = np.mean(recent_ranges[5:]) if len(recent_ranges) > 5 else avg_recent_range
        
        range_compression = (avg_earlier_range - avg_recent_range) / avg_earlier_range if avg_earlier_range > 0 else 0
        
        # Campaign stage assessment
        if range_compression > 0.3 and net_bias != 'neutral':
            return 'completion'
        elif range_compression > 0.15 and net_bias != 'neutral':
            return 'acceleration'
        elif net_bias != 'neutral':
            return 'building'
        else:
            return 'initiation'

    def _calculate_institutional_footprint_strength(self, price_data: pd.DataFrame, range_info: Dict[str, Any], net_bias: str, campaign_stage: str) -> float:
        """Calculate overall institutional footprint strength."""
        
        # Base strength from range quality
        base_strength = range_info['range_quality']
        
        # Bias strength modifier
        bias_modifier = 1.0 if net_bias != 'neutral' else 0.6
        
        # Campaign stage modifier
        stage_modifiers = {
            'completion': 1.2,
            'acceleration': 1.0,
            'building': 0.8,
            'initiation': 0.6
        }
        stage_modifier = stage_modifiers.get(campaign_stage, 0.6)
        
        # Systematic activity detection
        systematic_score = self._detect_systematic_activity(price_data, range_info)
        
        # Combine components
        institutional_strength = base_strength * bias_modifier * stage_modifier * (0.7 + 0.3 * systematic_score)
        
        return min(1.0, institutional_strength)

    def _detect_systematic_activity(self, price_data: pd.DataFrame, range_info: Dict[str, Any]) -> float:
        """Detect systematic institutional activity patterns."""
        try:
            # Look for consistent volume patterns
            recent_volume = price_data['volume'].tail(20)
            volume_consistency = 1.0 - (recent_volume.std() / recent_volume.mean()) if recent_volume.mean() > 0 else 0.0
            
            # Look for controlled price action (low volatility within range)
            support_price = range_info['support_level']['price']
            resistance_price = range_info['resistance_level']['price']
            
            recent_prices = price_data['close'].tail(20)
            range_normalized_volatility = (recent_prices.std() / (resistance_price - support_price)) if (resistance_price - support_price) > 0 else 1.0
            
            control_score = max(0.0, 1.0 - range_normalized_volatility * 5)  # Lower volatility = higher control
            
            # Combine systematic indicators
            systematic_score = (volume_consistency * 0.6 + control_score * 0.4)
            
            return min(1.0, systematic_score)
            
        except Exception:
            return 0.5

    def _filter_and_validate_factors(self, factors: List[FactorData]) -> List[FactorData]:
        """Filter and validate generated factors."""
        validated_factors = []
        
        for factor in factors:
            # Validate factor
            is_valid, reason = validate_factor(factor)
            if not is_valid:
                logger.warning(f"Invalid factor {factor.factor_name}: {reason}")
                continue
            
            # Check minimum strength threshold
            if factor.strength_score < self.config.get('min_factor_strength', 0.3):
                continue
            
            validated_factors.append(factor)
        
        # Sort by strength and return top factors
        validated_factors.sort(key=lambda f: f.strength_score, reverse=True)
        
        return validated_factors

    def generate_factors(self, 
                        ticker: str, 
                        mtf_data: Dict[TimeFrame, pd.DataFrame], 
                        current_price: float,
                        **kwargs) -> List[FactorData]:
        """
        Public method for factor generation (alias for analyze).
        
        This method provides the standard interface expected by the factor system.
        """
        return self.analyze(ticker, mtf_data, current_price, **kwargs)
