# PowerShell MCP Server Launcher
# Start the Liquidity Sweep MCP Server

Write-Host ""
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host "              Liquidity Sweep MCP Server" -ForegroundColor Yellow
Write-Host "            Production-Ready API Integration" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Cyan
Write-Host ""

$MCPDir = "D:\script-work\CORE\api"
$ConfigFile = "$MCPDir\mcp_installation\config\config.json"

Write-Host "Starting MCP Server..." -ForegroundColor Green
Write-Host "Directory: $MCPDir" -ForegroundColor Gray
Write-Host "Configuration: $ConfigFile" -ForegroundColor Gray
Write-Host ""

# Change to the MCP directory
Set-Location $MCPDir

# Check if config file exists
if (-not (Test-Path $ConfigFile)) {
    Write-Host "ERROR: Configuration file not found: $ConfigFile" -ForegroundColor Red
    Write-Host "Please check the configuration file path." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Start the MCP server
try {
    py mcp_server_production.py --config $ConfigFile --log-level INFO
} catch {
    Write-Host "ERROR: Failed to start MCP server: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}

Write-Host ""
Write-Host "MCP Server stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
