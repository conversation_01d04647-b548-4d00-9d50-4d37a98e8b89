"""
Market Regime Adapter Module

This module provides a unified interface for adapting models and parameters
based on detected market regimes.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import logging
import time
from datetime import datetime
import threading
import json
import os

# Internal imports
from ml_logging import get_logger
from market_regime_detection import MarketRegimeDetector, MarketRegime
from ml_model_registry import ModelRegistry
from market_regime_adaptation import ModelSelector, ParameterAdapter

# Setup logger
logger = get_logger('market_regime_adapter')


class MarketRegimeAdapter:
    """
    Market regime adapter for liquidity analysis.

    This class provides a unified interface for adapting models and parameters
    based on detected market regimes.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the market regime adapter.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Initialize components
        self.model_selector = ModelSelector(self.config.get('model_selector_config'))
        self.parameter_adapter = ParameterAdapter(self.config.get('parameter_adapter_config'))
        self.regime_detector = MarketRegimeDetector(self.config.get('regime_detector_config'))

        # Initialize model registry
        self.model_registry = ModelRegistry(self.config.get('model_registry_config'))

        # Set default confidence threshold for testing
        self.confidence_threshold = self.config.get('confidence_threshold', 0.1)

        logger.info("Market regime adapter initialized")

    def adapt(self,
             price_data: pd.DataFrame,
             base_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Adapt models and parameters based on detected regime.

        Args:
            price_data: DataFrame with OHLCV price data
            base_params: Optional base parameter dictionary

        Returns:
            Dictionary with adapted model and parameters
        """
        start_time = time.time()

        try:
            # Detect market regime
            regime_result = self.regime_detector.detect_regime(price_data)
            regime = regime_result['regime']
            confidence = regime_result['confidence']

            # Select model
            model_result = self.model_selector.select_model(price_data)
            model_id = model_result['model_id']
            model_version = model_result['model_version']

            # Adapt parameters
            if base_params is None:
                base_params = {}

            param_result = self.parameter_adapter.adapt_params(price_data, base_params)
            adapted_params = param_result['params']

            # Extract regime-specific features
            regime_features = self.regime_detector.extract_regime_features(price_data)

            elapsed = time.time() - start_time
            logger.info(f"Adapted for regime {regime} with confidence {confidence:.2f} in {elapsed:.2f} seconds")

            return {
                'regime': regime,
                'confidence': confidence,
                'model_id': model_id,
                'model_version': model_version,
                'params': adapted_params,
                'regime_features': regime_features.get('features', {}),
                'processing_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error in market regime adaptation: {str(e)}")

            # Return default values on error
            return {
                'regime': MarketRegime.UNKNOWN.value,
                'confidence': 0.0,
                'model_id': self.model_selector.default_model_id,
                'model_version': self.model_selector.default_model_version,
                'params': base_params or {},
                'regime_features': {},
                'error': str(e)
            }

    def enhance_liquidity_analysis(self,
                                  price_data: pd.DataFrame,
                                  liquidity_levels: Dict[str, List[Dict[str, Any]]],
                                  options_data: Optional[pd.DataFrame] = None,
                                  volume_profile: Optional[Dict[str, Any]] = None,
                                  gex_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Enhance liquidity analysis with regime-specific adaptations.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data

        Returns:
            Dictionary with enhanced liquidity analysis
        """
        start_time = time.time()

        try:
            # Adapt for current regime
            adaptation_result = self.adapt(price_data)
            regime = adaptation_result['regime']
            model_id = adaptation_result['model_id']
            model_version = adaptation_result['model_version']
            params = adaptation_result['params']
            regime_features = adaptation_result['regime_features']

            # Get model from registry
            model = self.model_registry.get_model(model_id, model_version)

            if model is None:
                logger.warning(f"Model {model_id} (version {model_version}) not found, using default analysis")
                return {
                    'enhanced_levels': liquidity_levels,
                    'regime': regime,
                    'confidence': adaptation_result['confidence'],
                    'regime_features': regime_features
                }

            # Enhance liquidity levels based on regime
            enhanced_levels = self._enhance_levels_for_regime(
                liquidity_levels, regime, regime_features, model, params
            )

            elapsed = time.time() - start_time
            logger.info(f"Enhanced liquidity analysis for regime {regime} in {elapsed:.2f} seconds")

            return {
                'enhanced_levels': enhanced_levels,
                'regime': regime,
                'confidence': adaptation_result['confidence'],
                'regime_features': regime_features,
                'model_id': model_id,
                'model_version': model_version,
                'processing_time': elapsed
            }

        except Exception as e:
            logger.error(f"Error enhancing liquidity analysis: {str(e)}")

            # Return original levels on error
            return {
                'enhanced_levels': liquidity_levels,
                'regime': MarketRegime.UNKNOWN.value,
                'confidence': 0.0,
                'error': str(e)
            }

    def _enhance_levels_for_regime(self,
                                  liquidity_levels: Dict[str, List[Dict[str, Any]]],
                                  regime: str,
                                  regime_features: Dict[str, Any],
                                  model: Any,
                                  params: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Enhance liquidity levels for a specific regime.

        Args:
            liquidity_levels: Dictionary with liquidity levels
            regime: Market regime
            regime_features: Regime-specific features
            model: Model object
            params: Parameter dictionary

        Returns:
            Dictionary with enhanced liquidity levels
        """
        # Create a deep copy of liquidity levels
        enhanced_levels = {
            'support': [level.copy() for level in liquidity_levels.get('support', [])],
            'resistance': [level.copy() for level in liquidity_levels.get('resistance', [])]
        }

        try:
            # Apply regime-specific enhancements
            if regime == MarketRegime.TRENDING_UP.value:
                # In uptrend, support levels are more important
                for level in enhanced_levels['support']:
                    # Increase strength of support levels
                    level['strength'] = min(1.0, level['strength'] * 1.2)
                    level['regime_adjusted'] = True

                # Add regime-specific metadata
                for level in enhanced_levels['support'] + enhanced_levels['resistance']:
                    level['regime'] = regime
                    level['trend_strength'] = regime_features.get('trend_strength', 0.0)

            elif regime == MarketRegime.TRENDING_DOWN.value:
                # In downtrend, resistance levels are more important
                for level in enhanced_levels['resistance']:
                    # Increase strength of resistance levels
                    level['strength'] = min(1.0, level['strength'] * 1.2)
                    level['regime_adjusted'] = True

                # Add regime-specific metadata
                for level in enhanced_levels['support'] + enhanced_levels['resistance']:
                    level['regime'] = regime
                    level['trend_strength'] = regime_features.get('trend_strength', 0.0)

            elif regime == MarketRegime.RANGING.value:
                # In ranging market, both support and resistance are important
                # but levels near the edges of the range are more significant
                range_position = regime_features.get('range_position', 0.5)

                for level in enhanced_levels['support']:
                    # Adjust strength based on position in range
                    position_factor = 1.0 + (1.0 - range_position) * 0.5
                    level['strength'] = min(1.0, level['strength'] * position_factor)
                    level['regime_adjusted'] = True

                for level in enhanced_levels['resistance']:
                    # Adjust strength based on position in range
                    position_factor = 1.0 + range_position * 0.5
                    level['strength'] = min(1.0, level['strength'] * position_factor)
                    level['regime_adjusted'] = True

                # Add regime-specific metadata
                for level in enhanced_levels['support'] + enhanced_levels['resistance']:
                    level['regime'] = regime
                    level['range_position'] = range_position

            elif regime == MarketRegime.VOLATILE.value:
                # In volatile market, levels need higher strength to be significant
                volatility_factor = regime_features.get('volatility_intensity', 1.0)

                for level in enhanced_levels['support'] + enhanced_levels['resistance']:
                    # Adjust strength based on volatility
                    level['strength'] = level['strength'] / (1.0 + (volatility_factor - 1.0) * 0.5)
                    level['regime_adjusted'] = True
                    level['regime'] = regime
                    level['volatility_intensity'] = volatility_factor

            # Apply model-specific enhancements if available
            if hasattr(model, 'enhance_levels_for_regime'):
                model_enhanced = model.enhance_levels_for_regime(
                    enhanced_levels, regime, regime_features, params
                )
                if model_enhanced is not None:
                    enhanced_levels = model_enhanced

            return enhanced_levels

        except Exception as e:
            logger.error(f"Error enhancing levels for regime {regime}: {str(e)}")
            return liquidity_levels
