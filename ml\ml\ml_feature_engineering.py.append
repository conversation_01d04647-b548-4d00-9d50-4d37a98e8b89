            autocorr = np.zeros(len(data))
            for i in range(window, len(data)):
                r = returns.iloc[i-window:i].values
                
                # Check for missing data
                if not np.isnan(r).any() and np.std(r) > 0:
                    # Calculate lag-1 autocorrelation
                    autocorr[i] = np.corrcoef(r[1:], r[:-1])[0, 1]
            
            result[f'regime_autocorr_{window}'] = autocorr
            
            # Autocorrelation sign (positive = trending, negative = mean reverting)
            result[f'regime_is_trend_{window}'] = (autocorr > 0).astype(int)
        
        # Volatility clustering
        for window in [20, 50]:
            returns = data['close'].pct_change()
            abs_returns = np.abs(returns)
            
            # Autocorrelation of absolute returns (volatility clustering)
            autocorr_abs = np.zeros(len(data))
            for i in range(window, len(data)):
                ar = abs_returns.iloc[i-window:i].values
                
                # Check for missing data
                if not np.isnan(ar).any() and np.std(ar) > 0:
                    # Calculate lag-1 autocorrelation of absolute returns
                    autocorr_abs[i] = np.corrcoef(ar[1:], ar[:-1])[0, 1]
            
            result[f'regime_vol_cluster_{window}'] = autocorr_abs
        
        # Regime change detection
        for window in [20, 50]:
            if len(data) >= window * 2:
                # Detect changes in volatility
                vol = data['close'].pct_change().rolling(window=window).std()
                vol_ratio = vol / vol.shift(window)
                result[f'regime_vol_change_{window}'] = vol_ratio
                
                # Detect changes in trend
                ma_short = data['close'].rolling(window=window).mean()
                ma_long = data['close'].rolling(window=window*2).mean()
                ma_ratio = ma_short / ma_long
                result[f'regime_trend_change_{window}'] = ma_ratio
                
                # Regime change indicator (combined trend and volatility)
                regime_change = (np.abs(vol_ratio - 1) > 0.2) | (np.abs(ma_ratio - 1) > 0.05)
                result[f'regime_change_indicator_{window}'] = regime_change.astype(int)
        
        # Hidden Markov Model proxies (simplified)
        for window in [50]:
            if len(data) >= window:
                returns = data['close'].pct_change()
                
                # Simple regime classification using volatility and trend
                vol = returns.rolling(window=window).std()
                trend = returns.rolling(window=window).mean()
                
                # Create regime classes (simplified):
                # 0: Low vol, low/negative return (range-bound)
                # 1: Low vol, positive return (bullish trend)
                # 2: High vol, low/negative return (bearish/crash)
                # 3: High vol, positive return (recovery/volatile bull)
                
                # Get rolling percentiles for vol and trend
                if len(returns) >= 100:
                    vol_rank = vol.rolling(window=100).apply(
                        lambda x: pd.Series(x).rank(pct=True).iloc[-1]
                    )
                    
                    trend_rank = trend.rolling(window=100).apply(
                        lambda x: pd.Series(x).rank(pct=True).iloc[-1]
                    )
                    
                    # Classify regimes
                    regime = np.zeros(len(data))
                    high_vol = vol_rank > 0.7
                    high_trend = trend_rank > 0.5
                    
                    regime[~high_vol & ~high_trend] = 0  # Low vol, low trend
                    regime[~high_vol & high_trend] = 1   # Low vol, high trend
                    regime[high_vol & ~high_trend] = 2   # High vol, low trend
                    regime[high_vol & high_trend] = 3    # High vol, high trend
                    
                    result[f'regime_class_{window}'] = regime
        
        # Log results
        logger.debug(f"Extracted {result.shape[1]} market regime features")
        return result
    
    @measure_performance("feature_engineering", "correlation_features")
    def correlation_features(self, data: pd.DataFrame, 
                             related_data: Optional[Dict[str, pd.DataFrame]] = None) -> pd.DataFrame:
        """
        Extract correlation-based features.
        
        Args:
            data: Primary DataFrame with OHLCV price data
            related_data: Optional dictionary mapping asset IDs to their OHLCV DataFrames
            
        Returns:
            DataFrame with extracted features
        """
        if not self._validate_data(data):
            return pd.DataFrame()
        
        result = pd.DataFrame(index=data.index)
        
        # If no related data provided, use autocorrelation features
        if related_data is None or len(related_data) == 0:
            # Autocorrelation features
            returns = data['close'].pct_change()
            
            # Serial autocorrelation at different lags
            for lag in [1, 2, 3, 5, 10]:
                autocorr = np.zeros(len(data))
                for i in range(20, len(data)):
                    r = returns.iloc[max(0, i-50):i].values
                    
                    # Remove NaN values
                    r = r[~np.isnan(r)]
                    
                    if len(r) > lag + 5:
                        try:
                            autocorr[i] = np.corrcoef(r[lag:], r[:-lag])[0, 1]
                        except:
                            pass
                
                result[f'correlation_autocorr_lag{lag}'] = autocorr
            
            # Return the autocorrelation features
            logger.debug(f"Extracted {result.shape[1]} autocorrelation features")
            return result
        
        # If related data provided, compute cross-asset correlations
        # Process related assets
        related_returns = {}
        
        # Convert price data to returns
        primary_returns = data['close'].pct_change()
        
        # Process related assets returns
        for asset_id, asset_data in related_data.items():
            if self._validate_data(asset_data) and len(asset_data) > 0:
                related_returns[asset_id] = asset_data['close'].pct_change()
        
        # Compute rolling correlations for different window sizes
        for window in [10, 20, 50, 100]:
            if len(data) >= window:
                # For each related asset
                for asset_id, asset_returns in related_returns.items():
                    # Ensure data is aligned
                    aligned_data = pd.concat([primary_returns, asset_returns], axis=1, join='inner')
                    if len(aligned_data) > window:
                        # Replace NaN values with 0
                        aligned_data = aligned_data.fillna(0)
                        
                        # Calculate rolling correlation
                        corr = aligned_data.iloc[:, 0].rolling(window=window).corr(aligned_data.iloc[:, 1])
                        result[f'correlation_{asset_id}_{window}'] = corr
        
        # Create correlation regime features
        if len(related_returns) > 0:
            # Average correlation across all assets
            for window in [20, 50]:
                if len(data) >= window:
                    avg_corr = np.zeros(len(data))
                    corr_columns = [col for col in result.columns if f'correlation_' in col and f'_{window}' in col]
                    
                    if corr_columns:
                        avg_corr = result[corr_columns].mean(axis=1)
                        result[f'correlation_avg_{window}'] = avg_corr
                        
                        # Correlation regime (high/low)
                        if len(data) >= window * 3:
                            long_avg_corr = avg_corr.rolling(window=window * 3).mean()
                            result[f'correlation_regime_{window}'] = (avg_corr > long_avg_corr).astype(int)
        
        # Cross-sectional dispersion (if we have enough related assets)
        if len(related_returns) >= 5:
            # Calculate cross-sectional standard deviation of returns
            for window in [1, 5, 20]:
                cs_std = np.zeros(len(data))
                
                for i in range(window, len(data)):
                    # Get cross-sectional returns for the window
                    cs_returns = []
                    
                    for asset_id, asset_returns in related_returns.items():
                        if i < len(asset_returns):
                            if window == 1:
                                # Single period return
                                cs_returns.append(asset_returns.iloc[i])
                            else:
                                # Multi-period return
                                if i >= window and i - window < len(asset_returns):
                                    period_return = (asset_returns.iloc[i] / asset_returns.iloc[i - window] - 1)
                                    cs_returns.append(period_return)
                    
                    # Calculate cross-sectional standard deviation
                    if len(cs_returns) >= 3:  # Need at least a few assets
                        cs_std[i] = np.std(cs_returns)
                
                result[f'correlation_dispersion_{window}'] = cs_std
        
        # Factor-like correlation features
        # Simple sector or style factor proxies based on correlations
        if len(related_returns) >= 3:
            # Create simple factor loadings based on correlation patterns
            factor_proxies = {
                'market': np.zeros(len(data)),  # Market beta proxy
                'momentum': np.zeros(len(data)),  # Momentum factor proxy
                'volatility': np.zeros(len(data)),  # Volatility factor proxy
            }
            
            # Compute simple factor proxies based on correlation profiles
            for i in range(50, len(data)):
                # Get correlation values for this time point
                corr_values = {}
                
                for asset_id, asset_returns in related_returns.items():
                    if i < len(asset_returns):
                        # Calculate 50-day correlation
                        if i >= 50 and i - 50 < len(asset_returns):
                            corr = np.corrcoef(
                                primary_returns.iloc[i-50:i].fillna(0),
                                asset_returns.iloc[i-50:i].fillna(0)
                            )[0, 1]
                            corr_values[asset_id] = corr
                
                if corr_values:
                    # Market beta: average correlation
                    factor_proxies['market'][i] = np.mean(list(corr_values.values()))
                    
                    # Get asset volatilities
                    vol_values = {}
                    for asset_id, asset_returns in related_returns.items():
                        if i >= 50 and i - 50 < len(asset_returns):
                            vol = asset_returns.iloc[i-50:i].std()
                            vol_values[asset_id] = vol
                    
                    # Correlation with asset volatility
                    if vol_values:
                        # Calculate correlation between asset correlations and volatilities
                        corr_array = np.array(list(corr_values.values()))
                        vol_array = np.array([vol_values.get(asset_id, 0) for asset_id in corr_values.keys()])
                        
                        if len(corr_array) > 3 and np.std(corr_array) > 0 and np.std(vol_array) > 0:
                            vol_corr = np.corrcoef(corr_array, vol_array)[0, 1]
                            factor_proxies['volatility'][i] = vol_corr
            
            # Add factor proxy features
            for factor_name, factor_values in factor_proxies.items():
                result[f'correlation_factor_{factor_name}'] = factor_values
        
        # Log results
        logger.debug(f"Extracted {result.shape[1]} correlation features")
        return result
    
    @measure_performance("feature_engineering", "sentiment_features")
    def sentiment_features(self, data: pd.DataFrame, 
                          sentiment_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Extract sentiment-based features.
        
        Args:
            data: DataFrame with OHLCV price data
            sentiment_data: Optional DataFrame with sentiment data
            
        Returns:
            DataFrame with extracted features
        """
        if not self._validate_data(data):
            return pd.DataFrame()
        
        result = pd.DataFrame(index=data.index)
        
        # If sentiment data provided, use it
        if sentiment_data is not None and not sentiment_data.empty:
            # Align indices
            sentiment_data = sentiment_data.reindex(data.index, method='ffill')
            
            # Add raw sentiment indicators if they exist
            for col in ['sentiment_score', 'sentiment_bullish', 'sentiment_bearish', 'sentiment_neutral']:
                if col in sentiment_data.columns:
                    result[f'sentiment_{col}'] = sentiment_data[col]
            
            # Create derived sentiment features
            for window in [5, 10, 20]:
                for col in ['sentiment_score', 'sentiment_bullish', 'sentiment_bearish', 'sentiment_neutral']:
                    if col in sentiment_data.columns:
                        # Moving average
                        result[f'sentiment_{col}_ma_{window}'] = sentiment_data[col].rolling(window=window).mean()
                        
                        # Rate of change
                        result[f'sentiment_{col}_roc_{window}'] = sentiment_data[col].pct_change(periods=window)
                        
                        # z-score
                        result[f'sentiment_{col}_zscore_{window}'] = (
                            (sentiment_data[col] - sentiment_data[col].rolling(window=window).mean()) / 
                            sentiment_data[col].rolling(window=window).std()
                        )
            
            # Bull-bear spread
            if 'sentiment_bullish' in sentiment_data.columns and 'sentiment_bearish' in sentiment_data.columns:
                result['sentiment_bull_bear_spread'] = sentiment_data['sentiment_bullish'] - sentiment_data['sentiment_bearish']
                
                # Bull-bear spread moving average
                for window in [5, 10, 20]:
                    result[f'sentiment_bull_bear_spread_ma_{window}'] = result['sentiment_bull_bear_spread'].rolling(window=window).mean()
            
            # Sentiment extremes
            if 'sentiment_score' in sentiment_data.columns:
                for window in [20, 50]:
                    # Calculate percentile rank of current sentiment
                    result[f'sentiment_rank_{window}'] = sentiment_data['sentiment_score'].rolling(window=window).apply(
                        lambda x: pd.Series(x).rank(pct=True).iloc[-1]
                    )
                    
                    # Extreme sentiment indicator (top or bottom 20%)
                    extreme_bullish = result[f'sentiment_rank_{window}'] > 0.8
                    extreme_bearish = result[f'sentiment_rank_{window}'] < 0.2
                    result[f'sentiment_extreme_{window}'] = np.where(extreme_bullish, 1, np.where(extreme_bearish, -1, 0))
            
            # Sentiment change acceleration
            if 'sentiment_score' in sentiment_data.columns:
                sentiment_change = sentiment_data['sentiment_score'].diff()
                result['sentiment_acceleration'] = sentiment_change.diff()
                
                # Sentiment change direction
                for window in [5, 10]:
                    sentiment_direction = np.sign(sentiment_change.rolling(window=window).mean())
                    result[f'sentiment_direction_{window}'] = sentiment_direction
            
            # Sentiment and price divergence
            if 'sentiment_score' in sentiment_data.columns:
                for window in [10, 20]:
                    # Price trend
                    price_trend = data['close'].pct_change(periods=window)
                    
                    # Sentiment trend
                    sentiment_trend = sentiment_data['sentiment_score'].diff(periods=window)
                    
                    # Divergence indicator (1 for positive divergence, -1 for negative)
                    divergence = np.zeros(len(data))
                    
                    # Positive divergence: price down, sentiment up
                    pos_divergence = (price_trend < 0) & (sentiment_trend > 0)
                    divergence[pos_divergence] = 1
                    
                    # Negative divergence: price up, sentiment down
                    neg_divergence = (price_trend > 0) & (sentiment_trend < 0)
                    divergence[neg_divergence] = -1
                    
                    result[f'sentiment_divergence_{window}'] = divergence
        else:
            # If no sentiment data, use price-based proxies for sentiment
            
            # RSI as a proxy for market sentiment
            for window in [14, 21]:
                # Calculate RSI
                delta = data['close'].diff()
                gain = delta.copy()
                loss = delta.copy()
                gain[gain < 0] = 0
                loss[loss > 0] = 0
                loss = abs(loss)
                
                avg_gain = gain.rolling(window=window).mean()
                avg_loss = loss.rolling(window=window).mean()
                
                # Avoid division by zero
                rs = avg_gain / np.maximum(avg_loss, 1e-10)
                rsi = 100 - (100 / (1 + rs))
                
                result[f'sentiment_rsi_proxy_{window}'] = rsi
                
                # RSI regimes
                result[f'sentiment_overbought_{window}'] = (rsi > 70).astype(int)
                result[f'sentiment_oversold_{window}'] = (rsi < 30).astype(int)
            
            # Bollinger Band positions as sentiment proxy
            for window in [20]:
                # Calculate Bollinger Bands
                ma = data['close'].rolling(window=window).mean()
                std = data['close'].rolling(window=window).std()
                upper_band = ma + 2 * std
                lower_band = ma - 2 * std
                
                # Position within bands
                band_position = (data['close'] - lower_band) / (upper_band - lower_band)
                result[f'sentiment_bb_position_{window}'] = band_position
                
                # Extreme positions
                result[f'sentiment_bb_overbought_{window}'] = (band_position > 0.95).astype(int)
                result[f'sentiment_bb_oversold_{window}'] = (band_position < 0.05).astype(int)
            
            # Price momentum as sentiment proxy
            for window in [10, 20]:
                price_roc = data['close'].pct_change(periods=window)
                
                # Normalized momentum
                result[f'sentiment_momentum_proxy_{window}'] = price_roc / price_roc.rolling(window=window*2).std()
                
                # Extreme momentum
                result[f'sentiment_extreme_momentum_{window}'] = (np.abs(result[f'sentiment_momentum_proxy_{window}']) > 2).astype(int)
        
        # Log results
        logger.debug(f"Extracted {result.shape[1]} sentiment features")
        return result
    
    def create_all_features(self, data: pd.DataFrame, 
                           related_data: Optional[Dict[str, pd.DataFrame]] = None,
                           sentiment_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Create all features from all feature groups.
        
        Args:
            data: DataFrame with OHLCV price data
            related_data: Optional dictionary mapping asset IDs to their OHLCV DataFrames
            sentiment_data: Optional DataFrame with sentiment data
            
        Returns:
            DataFrame with all features
        """
        if not self._validate_data(data):
            logger.error("Invalid data provided for feature extraction")
            return pd.DataFrame()
        
        logger.info(f"Creating all features from {len(self.feature_groups)} feature groups")
        
        # Time extraction
        start_time = time.time()
        
        # Track features from each group
        feature_groups = {}
        
        # Extract features from each group based on configuration
        for group in self.feature_groups:
            if group == 'price':
                feature_groups['price'] = self.price_features(data)
            
            elif group == 'volume':
                feature_groups['volume'] = self.volume_features(data)
            
            elif group == 'volatility':
                feature_groups['volatility'] = self.volatility_features(data)
            
            elif group == 'momentum':
                feature_groups['momentum'] = self.momentum_features(data)
            
            elif group == 'mean_reversion':
                feature_groups['mean_reversion'] = self.mean_reversion_features(data)
            
            elif group == 'liquidity':
                feature_groups['liquidity'] = self.liquidity_features(data)
            
            elif group == 'market_impact':
                feature_groups['market_impact'] = self.market_impact_features(data)
            
            elif group == 'market_regime':
                feature_groups['market_regime'] = self.market_regime_features(data)
            
            elif group == 'correlation':
                feature_groups['correlation'] = self.correlation_features(data, related_data)
            
            elif group == 'sentiment':
                feature_groups['sentiment'] = self.sentiment_features(data, sentiment_data)
        
        # Combine all feature groups
        all_features = pd.DataFrame(index=data.index)
        total_features = 0
        
        for group, features in feature_groups.items():
            if features is not None and not features.empty:
                # Prefix feature names with group if not already done
                if not all(col.startswith(f"{group}_") for col in features.columns):
                    features = features.rename(columns={col: f"{group}_{col}" for col in features.columns if not col.startswith(f"{group}_")})
                
                all_features = pd.concat([all_features, features], axis=1)
                total_features += len(features.columns)
                logger.debug(f"Added {len(features.columns)} features from {group} group")
        
        # Calculate and log timing
        elapsed = time.time() - start_time
        logger.info(f"Created {total_features} features in {elapsed:.2f} seconds")
        
        return all_features
    
    def extract_all_features(self, data: pd.DataFrame, 
                            related_data: Optional[Dict[str, pd.DataFrame]] = None,
                            sentiment_data: Optional[pd.DataFrame] = None,
                            transform: bool = True,
                            transform_method: str = 'standard',
                            target: Optional[pd.Series] = None,
                            select_features: bool = False,
                            selection_method: str = 'importance',
                            n_features: Optional[int] = None) -> pd.DataFrame:
        """
        Extract all features and optionally transform and select features.
        This is the main public interface for feature engineering.
        
        Args:
            data: DataFrame with OHLCV price data
            related_data: Optional dictionary mapping asset IDs to their OHLCV DataFrames
            sentiment_data: Optional DataFrame with sentiment data
            transform: Whether to transform features
            transform_method: Transformation method ('standard', 'minmax', 'robust')
            target: Optional target variable for feature selection
            select_features: Whether to perform feature selection
            selection_method: Feature selection method
            n_features: Number of features to select
            
        Returns:
            DataFrame with extracted, transformed, and selected features
        """
        logger.info("Extracting all features with transformation and selection")
        
        # Extract all features
        all_features = self.create_all_features(data, related_data, sentiment_data)
        
        if all_features.empty:
            logger.error("Failed to extract features")
            return pd.DataFrame()
        
        # Apply transformation if requested
        if transform:
            if self.transformer is None or self.transformer.method != transform_method:
                logger.info(f"Creating new transformer with method '{transform_method}'")
                self.transformer = FeatureTransformer(method=transform_method)
            
            logger.info("Transforming features")
            all_features = self.transformer.fit_transform(all_features)
        
        # Apply feature selection if requested and target is provided
        if select_features and target is not None:
            if self.selector is None or self.selector.method != selection_method:
                logger.info(f"Creating new selector with method '{selection_method}'")
                self.selector = FeatureSelector(method=selection_method, n_features=n_features)
            
            logger.info(f"Selecting features using '{selection_method}' method")
            all_features = self.selector.fit_transform(all_features, target)
            
            # Log top features
            top_features = self.selector.get_top_features(10)
            logger.info(f"Top 10 features: {[f[0] for f in top_features]}")
        
        # Replace NaN values with 0
        all_features = all_features.fillna(0)
        
        logger.info(f"Final feature set contains {all_features.shape[1]} features")
        return all_features
    
    def save_transformer(self, path: str) -> None:
        """
        Save the feature transformer to disk.
        
        Args:
            path: Path to save the transformer
        """
        if self.transformer is not None:
            self.transformer.save(path)
            logger.info(f"Saved feature transformer to {path}")
        else:
            logger.warning("No transformer to save")
    
    def load_transformer(self, path: str) -> None:
        """
        Load a feature transformer from disk.
        
        Args:
            path: Path to load the transformer from
        """
        self.transformer = FeatureTransformer.load(path)
        logger.info(f"Loaded feature transformer from {path}")
    
    def save_selector(self, path: str) -> None:
        """
        Save the feature selector to disk.
        
        Args:
            path: Path to save the selector
        """
        if self.selector is not None:
            self.selector.save(path)
            logger.info(f"Saved feature selector to {path}")
        else:
            logger.warning("No selector to save")
    
    def load_selector(self, path: str) -> None:
        """
        Load a feature selector from disk.
        
        Args:
            path: Path to load the selector from
        """
        self.selector = FeatureSelector.load(path)
        logger.info(f"Loaded feature selector from {path}")
