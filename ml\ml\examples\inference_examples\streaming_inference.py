"""
Streaming Inference Service Example

This script demonstrates real-time streaming inference with the ML inference service.
"""

import sys
import os
import time
import threading
from datetime import datetime
import random

# Add parent directory to path to import ml modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

# Import inference service
from ml.ml_inference_service import (
    InferenceRequest,
    InferenceResult,
    create_inference_service
)

# Constants
SYMBOLS = ["AAPL", "MSFT", "GOOGL", "AMZN", "META"]
TIMEFRAMES = ["1m", "5m", "15m", "1h", "4h"]
MODEL_TYPES = ["pattern_recognition_model", "price_prediction_model", "regime_detection_model"]
RUNTIME_SECONDS = 30


class StreamingDemo:

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Demonstration of streaming inference."""
    
    def __init__(self):
        """Initialize the demo."""
        self.service = create_inference_service()
        self.running = False
        self.thread = None
        self.start_time = None
        
        # Statistics
        self.stats = {
            'requests_submitted': 0,
            'results_received': 0,
            'pattern_detections': 0,
            'price_predictions': 0,
            'regime_detections': 0,
            'errors': 0,
            'patterns_by_symbol': {symbol: 0 for symbol in SYMBOLS}
        }
        self.stats_lock = threading.RLock()
    
    def start(self, ticker: str = None):
        """Start the demo."""
        print("Starting Streaming Inference Demo")
        
        # Start inference service
        print("Starting inference service...")
        if not self.service.start():
            print("Failed to start inference service")
            return False
        
        # Start streaming thread
        self.running = True
        self.thread = threading.Thread(target=self._streaming_loop)
        self.thread.daemon = True
        self.thread.start()
        
        self.start_time = datetime.now()
        
        print("Streaming started")
        return True
    
    def stop(self, ticker: str = None):
        """Stop the demo."""
        self.running = False
        
        if self.thread:
            self.thread.join(timeout=5.0)
            self.thread = None
        
        # Stop inference service
        self.service.stop()
        
        print("Streaming stopped")
    
    def _streaming_loop(self, ticker: str = None):
        """Main streaming loop."""
        while self.running:
            # Submit a random request
            symbol = random.choice(SYMBOLS)
            timeframe = random.choice(TIMEFRAMES)
            model_type = random.choice(MODEL_TYPES)
            
            request = InferenceRequest(
                model_name=model_type,
                symbol=symbol,
                timeframe=timeframe,
                additional_params={
                    "real_time": True,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            self.service.submit_request(request, self._handle_result)
            
            with self.stats_lock:
                self.stats['requests_submitted'] += 1
            
            # Random delay between requests (50-200ms)
            time.sleep(random.uniform(0.05, 0.2))
    
    def _handle_result(self, result: InferenceResult):
        """Handle inference result."""
        with self.stats_lock:
            self.stats['results_received'] += 1
            
            if result.is_error():
                self.stats['errors'] += 1
                return
            
            model_type = result.request.model_name
            symbol = result.request.symbol
            
            if 'pattern' in model_type:
                self.stats['pattern_detections'] += 1
                patterns = result.predictions.get('detected_patterns', [])
                if patterns:
                    self.stats['patterns_by_symbol'][symbol] += 1
                    
                    # Print important patterns with high confidence
                    for pattern in patterns:
                        if pattern.get('confidence', 0) > 0.7:
                            pattern_type = pattern.get('type', 'unknown')
                            confidence = pattern.get('confidence', 0)
                            print(f" High confidence {pattern_type} detected in {symbol} ({confidence:.2f})")
            
            elif 'price' in model_type:
                self.stats['price_predictions'] += 1
            
            elif 'regime' in model_type:
                self.stats['regime_detections'] += 1
                
                # Print regime changes
                regime = result.predictions.get('current_regime')
                if regime and regime != self.stats.get(f'last_regime_{symbol}', ''):
                    self.stats[f'last_regime_{symbol}'] = regime
                    print(f" Regime change detected in {symbol}: {regime}")
    
    def print_stats(self, ticker: str = None):
        """Print current statistics."""
        with self.stats_lock:
            stats = self.stats.copy()
        
        elapsed = (datetime.now() - self.start_time).total_seconds()
        requests_per_second = stats['requests_submitted'] / elapsed if elapsed > 0 else 0
        results_per_second = stats['results_received'] / elapsed if elapsed > 0 else 0
        
        print("\n=== Streaming Statistics ===")
        print(f"Runtime: {elapsed:.1f} seconds")
        print(f"Requests submitted: {stats['requests_submitted']} ({requests_per_second:.2f}/sec)")
        print(f"Results received: {stats['results_received']} ({results_per_second:.2f}/sec)")
        print(f"Pattern detections: {stats['pattern_detections']}")
        print(f"Price predictions: {stats['price_predictions']}")
        print(f"Regime detections: {stats['regime_detections']}")
        print(f"Errors: {stats['errors']}")
        
        print("\nPatterns by symbol:")
        for symbol, count in stats['patterns_by_symbol'].items():
            print(f"  {symbol}: {count}")
        
        # Get service stats
        service_stats = self.service.get_service_stats()
        
        print("\nService Statistics:")
        print(f"Queue size: {service_stats.get('queue_size', 'N/A')}")
        print(f"Average processing time: {service_stats.get('avg_processing_time', 0):.3f} seconds")


def main():
    """Main function."""
    demo = StreamingDemo()
    
    # Start demo
    if not demo.start():
        return
    
    # Run for specified time
    try:
        for i in range(RUNTIME_SECONDS):
            # Print stats every 5 seconds
            if i > 0 and i % 5 == 0:
                demo.print_stats()
            
            # Wait 1 second
            time.sleep(1)
            
            # Print progress
            dots = "." * (i % 4)
            print(f"\rRunning{dots}    ", end="")
    
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    
    finally:
        # Final stats
        print("\n\nFinal Statistics:")
        demo.print_stats()
        
        # Stop demo
        demo.stop()
        
        print("\nDemo complete!")


if __name__ == "__main__":
    main()
