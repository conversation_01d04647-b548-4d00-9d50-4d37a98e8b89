"""
Pytest configuration and shared fixtures for CORE system tests
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import tempfile
import shutil


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path)


@pytest.fixture
def sample_ohlcv_data():
    """Create sample OHLCV data for testing"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    data = pd.DataFrame({
        'timestamp': dates,
        'open': np.random.uniform(100, 105, 100),
        'high': np.random.uniform(105, 110, 100),
        'low': np.random.uniform(95, 100, 100),
        'close': np.random.uniform(100, 105, 100),
        'volume': np.random.randint(1000, 10000, 100)
    })
    return data


@pytest.fixture
def sample_orderflow_data():
    """Create sample order flow data for testing"""
    timestamps = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    data = pd.DataFrame({
        'timestamp': timestamps,
        'buy_volume': np.random.randint(100, 1000, 100),
        'sell_volume': np.random.randint(100, 1000, 100),
        'buy_orders': np.random.randint(10, 100, 100),
        'sell_orders': np.random.randint(10, 100, 100),
        'delta': np.random.randint(-500, 500, 100),
        'cumulative_delta': np.random.randint(-2000, 2000, 100)
    })
    return data


@pytest.fixture
def mock_config():
    """Provide mock configuration for agents"""
    return {
        'symbol': 'TEST',
        'lookback_periods': {
            'csid': 20,
            'flow_velocity': 14,
            'institutional': 50
        },
        'thresholds': {
            'quality_min': 0.8,
            'institutional_size': 1000
        }
    }