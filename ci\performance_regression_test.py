#!/usr/bin/env python3
"""
Performance Regression Budget - Global Envelope Test
Validates orchestrator wall-time stays within acceptable limits
"""

import time
import subprocess
import sys
from pathlib import Path

# Performance budget constants
MAX_ORCHESTRATOR_WALL_TIME = 45.0  # seconds
TEST_TICKERS = ['SPY', 'QQQ', 'AAPL']

def run_orchestrator_performance_test():
    """Execute orchestrator with test tickers and measure wall time"""
    results = {}
    
    for ticker in TEST_TICKERS:
        print(f"Testing orchestrator performance for {ticker}...")
        
        start_time = time.time()
        
        try:
            # Run orchestrator with specific ticker
            cmd = [sys.executable, 'orchestrator.py', '--ticker', ticker, '--test-mode']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            wall_time = time.time() - start_time
            results[ticker] = {
                'wall_time': wall_time,
                'success': result.returncode == 0,
                'output': result.stdout if result.returncode == 0 else result.stderr
            }
            
            print(f"  {ticker}: {wall_time:.2f}s {'' if result.returncode == 0 else ''}")
            
        except subprocess.TimeoutExpired:
            wall_time = time.time() - start_time
            results[ticker] = {
                'wall_time': wall_time,
                'success': False,
                'output': 'TIMEOUT'
            }
            print(f"  {ticker}: TIMEOUT after {wall_time:.2f}s")
    
    return results

def validate_performance_budget(results):
    """Check if results meet performance budget"""
    total_wall_time = sum(r['wall_time'] for r in results.values())
    failures = [t for t, r in results.items() if not r['success']]
    
    print(f"\nPerformance Budget Analysis:")
    print(f"Total wall time: {total_wall_time:.2f}s")
    print(f"Budget limit: {MAX_ORCHESTRATOR_WALL_TIME}s")
    print(f"Budget status: {' PASS' if total_wall_time <= MAX_ORCHESTRATOR_WALL_TIME else ' FAIL'}")
    
    if failures:
        print(f"Failed tickers: {', '.join(failures)}")
        return False
    
    if total_wall_time > MAX_ORCHESTRATOR_WALL_TIME:
        print(f"PERFORMANCE REGRESSION: Exceeds budget by {total_wall_time - MAX_ORCHESTRATOR_WALL_TIME:.2f}s")
        return False
    
    print("Performance budget: PASSED")
    return True

if __name__ == "__main__":
    print("Running performance regression test...")
    results = run_orchestrator_performance_test()
    success = validate_performance_budget(results)
    
    if not success:
        print("\nERROR: Performance regression detected")
        print("Review recent changes that may impact orchestrator performance")
    
    sys.exit(0 if success else 1)
