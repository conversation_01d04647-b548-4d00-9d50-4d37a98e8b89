#!/usr/bin/env python3
"""
CORE Flow Detection System - Engine Package

Clean, stripped engine components for signal generation:
- Confluence Engine: 3-of-4 analyzer agreement logic
- Signal Generator: Confluence results to strategy signals

Mathematical precision with minimal complexity for AI training.
"""

from engine.confluence_engine import ConfluenceEngine, ConfluenceResult
from engine.signal_generator import SignalGenerator, StrategySignal, SignalType, SignalStrength

__all__ = [
    'ConfluenceEngine',
    'ConfluenceResult', 
    'SignalGenerator',
    'StrategySignal',
    'SignalType',
    'SignalStrength'
]

# Engine factory for easy instantiation
def create_engine_system(config: dict = None):
    """
    Factory function to create complete engine system.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Tuple of (ConfluenceEngine, SignalGenerator)
    """
    confluence_config = config.get('confluence', {}) if config else {}
    signal_config = config.get('signal_generator', {}) if config else {}
    
    confluence_engine = ConfluenceEngine(confluence_config)
    signal_generator = SignalGenerator(signal_config)
    
    return confluence_engine, signal_generator

def get_engine_components():
    """Get list of available engine components."""
    return ['confluence_engine', 'signal_generator']
