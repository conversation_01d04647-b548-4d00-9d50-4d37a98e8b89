"""
ML Integration Module

This module provides the integration layer for machine learning components,
implementing a service-oriented architecture for model deployment, monitoring,
and real-time operation.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import logging
import time
import json
from typing import Dict, List, Optional, Union, Any, Tuple
import threading
import numpy as np
import pandas as pd
from dataclasses import dataclass, field

from ml_logging import get_logger
from ml_model_registry import ModelRegistry
from ml_feature_engineering import FeatureEngineer
from ml_alert_manager import AlertManager
from ml_config_manager import ConfigManager

logger = get_logger(__name__)

@dataclass
class ModelMetrics:
    """Container for model performance metrics."""
    inference_time_ms: float = 0.0
    memory_usage_mb: float = 0.0
    batch_size: int = 0
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    timestamp: float = field(default_factory=time.time)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "inference_time_ms": self.inference_time_ms,
            "memory_usage_mb": self.memory_usage_mb,
            "batch_size": self.batch_size,
            "accuracy": self.accuracy,
            "precision": self.precision,
            "recall": self.recall,
            "f1_score": self.f1_score,
            "timestamp": self.timestamp
        }

class MLIntegration:
    """
    Machine Learning Integration Layer
    
    This class serves as the main integration point for all ML components,
    providing a unified interface for model deployment, monitoring and management.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the ML integration layer.
        
        Args:
            config_path: Path to configuration file
        """
        self.config_manager = ConfigManager(config_path)
        self.model_registry = ModelRegistry(self.config_manager.get_registry_path())
        self.feature_engineer = FeatureEngineer()
        self.alert_manager = AlertManager()
        
        # Model monitoring storage
        self.metrics_history: Dict[str, List[ModelMetrics]] = {}
        self.active_models: Dict[str, Any] = {}
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Service resources
        self.model_service_ready = False
        self.inference_queue = []
        
        logger.info("ML Integration layer initialized")
    
    def initialize_services(self) -> bool:
        """
        Initialize all required services for the ML integration.
        
        Returns:
            bool: True if initialization was successful
        """
        try:
            # Initialize model registry
            self.model_registry.initialize()
            
            # Load model configurations
            model_configs = self.config_manager.get_model_configs()
            
            # Preload default models if configured
            preload_models = self.config_manager.get("preload_models", [])
            for model_id in preload_models:
                if model_id in model_configs:
                    self._load_model(model_id)
            
            # Start monitoring if enabled
            if self.config_manager.get("enable_monitoring", False):
                self.start_monitoring()
            
            self.model_service_ready = True
            logger.info("ML services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing ML services: {str(e)}")
            return False
    
    def _load_model(self, model_id: str) -> bool:
        """
        Load a model from the registry.
        
        Args:
            model_id: ID of the model to load
            
        Returns:
            bool: True if model was loaded successfully
        """
        try:
            model = self.model_registry.load_model(model_id)
            self.active_models[model_id] = model
            self.metrics_history[model_id] = []
            logger.info(f"Model {model_id} loaded successfully")
            return True
        except Exception as e:
            logger.error(f"Error loading model {model_id}: {str(e)}")
            return False
    
    def unload_model(self, model_id: str) -> bool:
        """
        Unload a model from memory.
        
        Args:
            model_id: ID of the model to unload
            
        Returns:
            bool: True if model was unloaded successfully
        """
        if model_id in self.active_models:
            del self.active_models[model_id]
            logger.info(f"Model {model_id} unloaded")
            return True
        return False
    
    def get_active_models(self) -> List[str]:
        """
        Get the list of currently active models.
        
        Returns:
            List[str]: List of active model IDs
        """
        return list(self.active_models.keys())
    
    def analyze_market_data(
        self, 
        data: pd.DataFrame, 
        model_id: str, 
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Analyze market data using the specified model.
        
        Args:
            data: DataFrame containing market data
            model_id: ID of model to use for analysis
            request_id: Optional request identifier for tracking
            
        Returns:
            Dict containing analysis results
        """
        if not self.model_service_ready:
            logger.error("ML services not initialized")
            return {"error": "ML services not initialized"}
        
        if model_id not in self.active_models:
            # Try to load the model if not already loaded
            success = self._load_model(model_id)
            if not success:
                return {"error": f"Model {model_id} not available"}
        
        try:
            # Start timing for performance metrics
            start_time = time.time()
            
            # Process features
            features = self.feature_engineer.process_data(data)
            
            # Run inference
            model = self.active_models[model_id]
            results = model.predict(features)
            
            # Record metrics
            end_time = time.time()
            inference_time = (end_time - start_time) * 1000  # convert to ms
            
            metrics = ModelMetrics(
                inference_time_ms=inference_time,
                memory_usage_mb=self._get_memory_usage(),
                batch_size=len(data)
            )
            
            # Update metrics history
            if model_id in self.metrics_history:
                self.metrics_history[model_id].append(metrics)
                # Keep only the last 100 metrics
                if len(self.metrics_history[model_id]) > 100:
                    self.metrics_history[model_id] = self.metrics_history[model_id][-100:]
            
            # Process results for alerts if configured
            if self.config_manager.get("auto_alerts", False):
                self._process_alerts(results, model_id)
            
            # Package results with metadata
            response = {
                "model_id": model_id,
                "request_id": request_id,
                "timestamp": time.time(),
                "metrics": metrics.to_dict(),
                "results": results
            }
            
            return response
            
        except Exception as e:
            logger.error(f"Error analyzing market data with model {model_id}: {str(e)}")
            return {
                "error": f"Analysis failed: {str(e)}",
                "model_id": model_id,
                "request_id": request_id
            }
    
    def _process_alerts(self, results: Dict[str, Any], model_id: str) -> None:
        """
        Process model results and generate alerts based on rules.
        
        Args:
            results: Model inference results
            model_id: ID of the model used
        """
        try:
            # Process alerts based on model results
            self.alert_manager.process_model_results(results, model_id)
        except Exception as e:
            logger.error(f"Error processing alerts: {str(e)}")
    
    def get_model_metrics(self, model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get performance metrics for models.
        
        Args:
            model_id: Optional model ID to get metrics for a specific model
            
        Returns:
            Dict containing metrics data
        """
        if model_id:
            if model_id in self.metrics_history:
                # Return metrics for specific model
                metrics_list = [m.to_dict() for m in self.metrics_history[model_id]]
                return {
                    "model_id": model_id,
                    "metrics_count": len(metrics_list),
                    "metrics": metrics_list
                }
            else:
                return {"error": f"No metrics available for model {model_id}"}
        else:
            # Return summary metrics for all models
            summary = {}
            for mid, metrics in self.metrics_history.items():
                if metrics:
                    avg_inference = sum(m.inference_time_ms for m in metrics) / len(metrics)
                    avg_memory = sum(m.memory_usage_mb for m in metrics) / len(metrics)
                    summary[mid] = {
                        "count": len(metrics),
                        "avg_inference_time_ms": avg_inference,
                        "avg_memory_usage_mb": avg_memory,
                        "last_update": metrics[-1].timestamp
                    }
            
            return {
                "models_count": len(summary),
                "summary": summary
            }
    
    def _get_memory_usage(self) -> float:
        """
        Get current memory usage in MB.
        
        Returns:
            float: Memory usage in MB
        """
        # This is a simplified implementation
        # In a production system, use a proper memory profiler
        import psutil
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return memory_info.rss / (1024 * 1024)  # Convert to MB
    
    def start_monitoring(self) -> bool:
        """
        Start the model monitoring thread.
        
        Returns:
            bool: True if monitoring was started successfully
        """
        if self.monitoring_active:
            logger.info("Monitoring already active")
            return True
        
        try:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(
                target=self._monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            logger.info("Model monitoring started")
            return True
        except Exception as e:
            logger.error(f"Error starting monitoring: {str(e)}")
            self.monitoring_active = False
            return False
    
    def stop_monitoring(self) -> bool:
        """
        Stop the model monitoring thread.
        
        Returns:
            bool: True if monitoring was stopped successfully
        """
        if not self.monitoring_active:
            return True
        
        try:
            self.monitoring_active = False
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=5.0)
            logger.info("Model monitoring stopped")
            return True
        except Exception as e:
            logger.error(f"Error stopping monitoring: {str(e)}")
            return False
    
    def _monitoring_loop(self) -> None:
        """Background thread for model monitoring."""
        logger.info("Monitoring thread started")
        interval = self.config_manager.get("monitoring_interval_seconds", 60)
        
        while self.monitoring_active:
            try:
                # Check model health
                for model_id, model in list(self.active_models.items()):
                    # Implement health checks as needed
                    pass
                
                # Log monitoring stats
                memory_usage = self._get_memory_usage()
                logger.debug(f"Monitoring stats - Memory usage: {memory_usage:.2f} MB, Active models: {len(self.active_models)}")
                
                # Sleep for the configured interval
                time.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
                time.sleep(10)  # Sleep briefly to prevent rapid logging on error
    
    def get_active_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get active alerts from the alert manager.
        
        Args:
            limit: Maximum number of alerts to return
            
        Returns:
            List of active alerts
        """
        return self.alert_manager.get_active_alerts(limit)
    
    def acknowledge_alert(self, alert_id: str, by: str) -> bool:
        """
        Acknowledge an alert.
        
        Args:
            alert_id: ID of the alert to acknowledge
            by: Name of user/system acknowledging the alert
            
        Returns:
            bool: True if alert was acknowledged successfully
        """
        return self.alert_manager.acknowledge_alert(alert_id, by)
    
    def create_pattern_rule(
        self, 
        patterns: List[str], 
        confidence: float, 
        description: str
    ) -> Dict[str, Any]:
        """
        Create a pattern detection rule.
        
        Args:
            patterns: List of patterns to detect
            confidence: Minimum confidence threshold
            description: Rule description
            
        Returns:
            Dict containing rule information
        """
        return self.alert_manager.create_pattern_rule(
            patterns, confidence, description
        )
    
    def shutdown(self) -> None:
        """Safely shut down all ML services."""
        logger.info("Shutting down ML services")
        
        # Stop monitoring
        self.stop_monitoring()
        
        # Unload all models
        for model_id in list(self.active_models.keys()):
            self.unload_model(model_id)
        
        # Clean up resources
        self.model_service_ready = False
        logger.info("ML services shut down successfully")


# Global integration instance
_integration = None

def get_ml_integration() -> MLIntegration:
    """Get the global ML integration instance."""
    global _integration
    if _integration is None:
        _integration = MLIntegration()
    return _integration

def initialize_ml_services() -> bool:
    """Initialize all ML services."""
    integration = get_ml_integration()
    return integration.initialize_services()

def shutdown_ml_services() -> None:
    """Safely shut down all ML services."""
    global _integration
    if _integration is not None:
        _integration.shutdown()
        _integration = None
