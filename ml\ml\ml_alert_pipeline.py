"""
Alert Pipeline for Machine Learning Components

This module implements a pipeline for processing ML inference results,
generating alerts, and coordinating notification delivery.
"""

import os
import time
import threading
import queue
from typing import Dict, List, Tuple, Optional, Any, Union, Callable
from datetime import datetime
import json

# Import custom modules
from src.ml.ml_inference_service import InferenceResult, get_inference_service
from src.ml.ml_alert_manager import <PERSON><PERSON>, get_alert_manager
from src.ml.ml_config_manager import get_component_config
from src.ml.ml_logging import get_logger, measure_performance, handle_errors
from src.ml.ml_base import MLComponent

# Set up logger
logger = get_logger("ml_alert_pipeline")


class AlertRule:
    """
    Rule for generating alerts from inference results.
    """
    
    def __init__(self, rule_id: str, rule_type: str, model_type: str, 
                conditions: Dict[str, Any], actions: Dict[str, Any],
                enabled: bool = True, description: str = None):
        """
        Initialize the alert rule.
        
        Args:
            rule_id: Unique identifier for the rule
            rule_type: Type of rule (e.g., 'pattern', 'price', 'strategy')
            model_type: Type of model the rule applies to
            conditions: Dictionary of conditions for triggering the rule
            actions: Dictionary of actions to take when triggered
            enabled: Whether the rule is enabled
            description: Optional human-readable description
        """
        self.rule_id = rule_id
        self.rule_type = rule_type
        self.model_type = model_type
        self.conditions = conditions
        self.actions = actions
        self.enabled = enabled
        self.description = description or f"Alert rule {rule_id}"
        self.last_triggered = None
        self.trigger_count = 0
    
    def matches(self, inference_result: InferenceResult) -> bool:
        """
        Check if the inference result matches this rule's conditions.
        
        Args:
            inference_result: Inference result to check
            
        Returns:
            True if the rule matches, False otherwise
        """
        # Skip if rule is disabled
        if not self.enabled:
            return False
        
        # Skip if model type doesn't match
        if self.model_type != inference_result.model_type:
            return False
        
        # Check conditions based on rule type
        if self.rule_type == 'pattern':
            return self._match_pattern_rule(inference_result)
        elif self.rule_type == 'price':
            return self._match_price_rule(inference_result)
        elif self.rule_type == 'strategy':
            return self._match_strategy_rule(inference_result)
        else:
            logger.warning(f"Unknown rule type: {self.rule_type}")
            return False
    
    def _match_pattern_rule(self, inference_result: InferenceResult) -> bool:
        """
        Check if a pattern recognition result matches conditions.
        
        Args:
            inference_result: Inference result to check
            
        Returns:
            True if the rule matches, False otherwise
        """
        predictions = inference_result.predictions
        if not isinstance(predictions, dict):
            return False
        
        # Check if patterns exist
        if 'pattern_names' not in predictions or 'pattern_indices' not in predictions:
            return False
        
        pattern_names = predictions.get('pattern_names', [])
        pattern_indices = predictions.get('pattern_indices', [])
        confidence_levels = predictions.get('confidence_levels', [])
        
        # Get target patterns
        target_patterns = self.conditions.get('patterns', [])
        if not target_patterns:
            # If no specific patterns specified, match any pattern
            return any(idx > 0 for idx in pattern_indices)
        
        # Check if any of the target patterns is detected
        for i, name in enumerate(pattern_names):
            if name in target_patterns:
                # Check confidence if specified
                min_confidence = self.conditions.get('min_confidence')
                if min_confidence is not None:
                    if 'pattern_probabilities' in predictions:
                        probabilities = predictions['pattern_probabilities']
                        if i < len(probabilities) and i < len(pattern_indices):
                            idx = pattern_indices[i]
                            if idx < len(probabilities[i]):
                                confidence = probabilities[i][idx]
                                if confidence < min_confidence:
                                    continue
                
                # Check confidence level if specified
                required_level = self.conditions.get('confidence_level')
                if required_level is not None and i < len(confidence_levels):
                    if confidence_levels[i] != required_level:
                        continue
                
                return True
        
        return False
    
    def _match_price_rule(self, inference_result: InferenceResult) -> bool:
        """
        Check if a price prediction result matches conditions.
        
        Args:
            inference_result: Inference result to check
            
        Returns:
            True if the rule matches, False otherwise
        """
        predictions = inference_result.predictions
        if not isinstance(predictions, dict):
            return False
        
        # Check if predictions exist
        if 'price_predictions' not in predictions:
            return False
        
        price_predictions = predictions.get('price_predictions', [])
        horizons = predictions.get('horizons', [1, 5, 10, 20])
        
        # Ensure predictions and horizons have the same length
        if len(horizons) > len(price_predictions):
            horizons = horizons[:len(price_predictions)]
        
        # Get target horizon
        target_horizon = self.conditions.get('horizon')
        if target_horizon is None:
            # Use first horizon if not specified
            if not horizons:
                return False
            target_horizon = horizons[0]
        
        # Find prediction for target horizon
        horizon_idx = None
        for i, h in enumerate(horizons):
            if h == target_horizon:
                horizon_idx = i
                break
        
        if horizon_idx is None or horizon_idx >= len(price_predictions):
            return False
        
        # Get prediction for target horizon
        prediction = price_predictions[horizon_idx]
        
        # Calculate percent change from current price
        percent_change = prediction - 1.0  # Assuming normalized prediction
        
        # Check direction if specified
        direction = self.conditions.get('direction')
        if direction is not None:
            if direction == 'up' and percent_change <= 0:
                return False
            elif direction == 'down' and percent_change >= 0:
                return False
        
        # Check threshold if specified
        threshold_pct = self.conditions.get('threshold_percent')
        if threshold_pct is not None:
            threshold = threshold_pct / 100.0
            if abs(percent_change) < threshold:
                return False
        
        return True
    
    def _match_strategy_rule(self, inference_result: InferenceResult) -> bool:
        """
        Check if a strategy result matches conditions.
        
        Args:
            inference_result: Inference result to check
            
        Returns:
            True if the rule matches, False otherwise
        """
        predictions = inference_result.predictions
        if not isinstance(predictions, dict):
            return False
        
        # Check if signals exist
        if 'signals' not in predictions:
            return False
        
        signals = predictions.get('signals', [])
        
        if not signals:
            return False
        
        # Get most recent signal
        latest_signal = signals[-1]
        
        # Check signal type if specified
        signal_type = self.conditions.get('signal_type')
        if signal_type is not None:
            if signal_type == 'buy' and latest_signal <= 0:
                return False
            elif signal_type == 'sell' and latest_signal >= 0:
                return False
            elif signal_type == 'hold' and latest_signal != 0:
                return False
        
        # Check minimum confidence if specified
        min_confidence = self.conditions.get('min_confidence')
        if min_confidence is not None:
            confidence = 0.5  # Default confidence
            if 'signal_probabilities' in predictions:
                probs = predictions['signal_probabilities']
                if probs and len(probs) > 0:
                    latest_probs = probs[-1]
                    if latest_signal > 0 and len(latest_probs) >= 3:
                        confidence = latest_probs[2]  # Buy probability
                    elif latest_signal < 0 and len(latest_probs) >= 3:
                        confidence = latest_probs[0]  # Sell probability
            
            if confidence < min_confidence:
                return False
        
        # Check minimum return if specified
        min_return = self.conditions.get('min_return')
        if min_return is not None and 'predicted_returns' in predictions:
            returns = predictions['predicted_returns']
            if returns and len(returns) > 0:
                predicted_return = returns[-1]
                if abs(predicted_return) < min_return:
                    return False
        
        return True
    
    def execute(self, inference_result: InferenceResult) -> Optional[Alert]:
        """
        Execute the rule's actions when it matches.
        
        Args:
            inference_result: Matching inference result
            
        Returns:
            Created alert or None
        """
        # Update trigger stats
        self.last_triggered = datetime.now()
        self.trigger_count += 1
        
        # Get alert manager
        alert_manager = get_alert_manager()
        
        # Create an alert for the inference result
        alert_config = {
            'confidence_threshold': self.actions.get('confidence_threshold', 0.7),
            'threshold_percent': self.actions.get('threshold_percent', 5.0),
            'time_horizon': self.actions.get('time_horizon', 5),
            'confidence': self.actions.get('confidence', 0.7),
            'expiration_hours': self.actions.get('expiration_hours', 24)
        }
        
        try:
            alert = alert_manager.create_alert_from_inference(inference_result, alert_config)
            
            if alert:
                logger.info(f"Rule {self.rule_id} triggered, created alert {alert.id}")
            else:
                logger.debug(f"Rule {self.rule_id} matched but no alert was created")
            
            return alert
        
        except Exception as e:
            logger.error(f"Error executing rule {self.rule_id}: {e}")
            return None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert rule to a dictionary.
        
        Returns:
            Dictionary representation of the rule
        """
        result = {
            'rule_id': self.rule_id,
            'rule_type': self.rule_type,
            'model_type': self.model_type,
            'conditions': self.conditions,
            'actions': self.actions,
            'enabled': self.enabled,
            'description': self.description,
            'trigger_count': self.trigger_count
        }
        
        if self.last_triggered:
            result['last_triggered'] = self.last_triggered.isoformat()
        
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AlertRule':
        """
        Create a rule from a dictionary.
        
        Args:
            data: Dictionary with rule data
            
        Returns:
            AlertRule instance
        """
        rule = cls(
            rule_id=data.get('rule_id', str(id(data))),
            rule_type=data.get('rule_type', 'unknown'),
            model_type=data.get('model_type', 'unknown'),
            conditions=data.get('conditions', {}),
            actions=data.get('actions', {}),
            enabled=data.get('enabled', True),
            description=data.get('description')
        )
        
        rule.trigger_count = data.get('trigger_count', 0)
        
        if 'last_triggered' in data:
            try:
                rule.last_triggered = datetime.fromisoformat(data['last_triggered'])
            except (ValueError, TypeError):
                pass
        
        return rule


class AlertPipeline(MLComponent):
    """
    Pipeline for processing inference results and generating alerts.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the alert pipeline.
        
        Args:
            config: Optional configuration dictionary
        """
        # Get default configuration if not provided
        if config is None:
            config = get_component_config("alert_pipeline")
        
        super().__init__("alert_pipeline", config)
        
        # Initialize rule registry
        self.rules = {}
        
        # Load default rules
        self._load_default_rules()
        
        # Load custom rules if specified
        rules_path = self.config.get('rules_path')
        if rules_path:
            self._load_rules_from_path(rules_path)
        
        # Initialize processing queue
        self.async_processing = self.config.get('async_processing', True)
        if self.async_processing:
            self.queue = queue.Queue()
            self.processing_thread = threading.Thread(target=self._process_worker, daemon=True)
            self.processing_thread.start()
        
        logger.info(f"Alert pipeline initialized with {len(self.rules)} rules")
    
    def _load_default_rules(self) -> None:
        """Load default alert rules from configuration."""
        default_rules = self.config.get('default_rules', [])
        
        for rule_data in default_rules:
            try:
                rule = AlertRule.from_dict(rule_data)
                self.rules[rule.rule_id] = rule
                logger.debug(f"Loaded default rule: {rule.rule_id}")
            except Exception as e:
                logger.error(f"Error loading default rule: {e}")
    
    def _load_rules_from_path(self, path: str) -> None:
        """
        Load alert rules from a directory.
        
        Args:
            path: Directory path containing rule files
        """
        if not os.path.exists(path):
            logger.warning(f"Rules path not found: {path}")
            return
        
        # Look for rule files
        if os.path.isdir(path):
            # Load rules from directory
            rule_files = [f for f in os.listdir(path) 
                        if f.endswith('.json') or f.endswith('.rule')]
            
            for file_name in rule_files:
                try:
                    file_path = os.path.join(path, file_name)
                    with open(file_path, 'r') as f:
                        rule_data = json.load(f)
                    
                    # Handle single rule or multiple rules
                    if isinstance(rule_data, list):
                        for rule_item in rule_data:
                            rule = AlertRule.from_dict(rule_item)
                            self.rules[rule.rule_id] = rule
                    else:
                        rule = AlertRule.from_dict(rule_data)
                        self.rules[rule.rule_id] = rule
                    
                    logger.debug(f"Loaded rule(s) from file: {file_name}")
                
                except Exception as e:
                    logger.error(f"Error loading rules from {file_name}: {e}")
        
        elif os.path.isfile(path) and (path.endswith('.json') or path.endswith('.rule')):
            # Load rules from single file
            try:
                with open(path, 'r') as f:
                    rule_data = json.load(f)
                
                # Handle single rule or multiple rules
                if isinstance(rule_data, list):
                    for rule_item in rule_data:
                        rule = AlertRule.from_dict(rule_item)
                        self.rules[rule.rule_id] = rule
                else:
                    rule = AlertRule.from_dict(rule_data)
                    self.rules[rule.rule_id] = rule
                
                logger.debug(f"Loaded rule(s) from file: {path}")
            
            except Exception as e:
                logger.error(f"Error loading rules from {path}: {e}")
    
    def add_rule(self, rule: AlertRule) -> None:
        """
        Add a rule to the pipeline.
        
        Args:
            rule: Alert rule to add
        """
        self.rules[rule.rule_id] = rule
        logger.info(f"Added rule: {rule.rule_id}")
    
    def remove_rule(self, rule_id: str) -> bool:
        """
        Remove a rule from the pipeline.
        
        Args:
            rule_id: ID of the rule to remove
            
        Returns:
            True if successful, False otherwise
        """
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"Removed rule: {rule_id}")
            return True
        
        logger.warning(f"Rule not found: {rule_id}")
        return False
    
    def get_rule(self, rule_id: str) -> Optional[AlertRule]:
        """
        Get a rule by ID.
        
        Args:
            rule_id: ID of the rule
            
        Returns:
            AlertRule if found, None otherwise
        """
        return self.rules.get(rule_id)
    
    def get_rules(self, rule_type: Optional[str] = None, 
                model_type: Optional[str] = None,
                enabled_only: bool = True) -> List[AlertRule]:
        """
        Get rules with optional filtering.
        
        Args:
            rule_type: Optional filter by rule type
            model_type: Optional filter by model type
            enabled_only: Whether to include only enabled rules
            
        Returns:
            List of matching rules
        """
        result = list(self.rules.values())
        
        # Apply filters
        if enabled_only:
            result = [r for r in result if r.enabled]
        
        if rule_type:
            result = [r for r in result if r.rule_type == rule_type]
        
        if model_type:
            result = [r for r in result if r.model_type == model_type]
        
        return result
    
    def enable_rule(self, rule_id: str) -> bool:
        """
        Enable a rule.
        
        Args:
            rule_id: ID of the rule
            
        Returns:
            True if successful, False otherwise
        """
        rule = self.rules.get(rule_id)
        if rule:
            rule.enabled = True
            logger.info(f"Enabled rule: {rule_id}")
            return True
        
        logger.warning(f"Rule not found: {rule_id}")
        return False
    
    def disable_rule(self, rule_id: str) -> bool:
        """
        Disable a rule.
        
        Args:
            rule_id: ID of the rule
            
        Returns:
            True if successful, False otherwise
        """
        rule = self.rules.get(rule_id)
        if rule:
            rule.enabled = False
            logger.info(f"Disabled rule: {rule_id}")
            return True
        
        logger.warning(f"Rule not found: {rule_id}")
        return False
    
    @measure_performance("alert_pipeline", "process_inference")
    def process_inference(self, inference_result: InferenceResult) -> List[Alert]:
        """
        Process an inference result and generate alerts if needed.
        
        Args:
            inference_result: Inference result to process
            
        Returns:
            List of created alerts
        """
        if self.async_processing:
            # Add to processing queue
            self.queue.put(inference_result)
            return []
        else:
            # Process synchronously
            return self._process_inference(inference_result)
    
    def _process_inference(self, inference_result: InferenceResult) -> List[Alert]:
        """
        Internal method to process an inference result.
        
        Args:
            inference_result: Inference result to process
            
        Returns:
            List of created alerts
        """
        created_alerts = []
        
        # Get model type
        model_type = inference_result.model_type
        
        # Get rules for this model type
        matching_rules = [r for r in self.rules.values() 
                        if r.enabled and r.model_type == model_type]
        
        if not matching_rules:
            logger.debug(f"No rules found for model type: {model_type}")
            return []
        
        # Apply each matching rule
        for rule in matching_rules:
            try:
                if rule.matches(inference_result):
                    alert = rule.execute(inference_result)
                    if alert:
                        created_alerts.append(alert)
            except Exception as e:
                logger.error(f"Error processing rule {rule.rule_id}: {e}")
        
        return created_alerts
    
    def _process_worker(self) -> None:
        """Worker thread for asynchronous processing."""
        logger.info("Alert processing worker started")
        
        while True:
            try:
                # Get inference result from queue
                inference_result = self.queue.get()
                
                # Process the inference result
                self._process_inference(inference_result)
                
                # Mark task as done
                self.queue.task_done()
            
            except Exception as e:
                logger.error(f"Error in alert processing worker: {e}")
    
    def save_rules(self, path: str) -> bool:
        """
        Save rules to a file.
        
        Args:
            path: File path to save rules
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create directory if it doesn't exist
            directory = os.path.dirname(path)
            if directory:
                os.makedirs(directory, exist_ok=True)
            
            # Convert rules to dictionaries
            rules_data = [rule.to_dict() for rule in self.rules.values()]
            
            # Write to file
            with open(path, 'w') as f:
                json.dump(rules_data, f, indent=2)
            
            logger.info(f"Saved {len(rules_data)} rules to {path}")
            return True
        
        except Exception as e:
            logger.error(f"Error saving rules to {path}: {e}")
            return False
    
    def save(self, path: str) -> str:
        """
        Save the alert pipeline state.
        
        Args:
            path: Directory path to save the state
            
        Returns:
            Path to the saved state
        """
        # Create directory if it doesn't exist
        os.makedirs(path, exist_ok=True)
        
        # Save metadata and config
        metadata_path = os.path.join(path, 'alert_pipeline_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(self.metadata, f, indent=2)
        
        config_path = os.path.join(path, 'alert_pipeline_config.json')
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=2)
        
        # Save rules to a file
        rules_path = os.path.join(path, 'alert_rules.json')
        self.save_rules(rules_path)
        
        logger.info(f"Alert pipeline state saved to {path}")
        return path
    
    @classmethod
    def load(cls, path: str) -> 'AlertPipeline':
        """
        Load an alert pipeline from saved state.
        
        Args:
            path: Path to the saved state
            
        Returns:
            Loaded alert pipeline
        """
        # Check if path exists
        if not os.path.exists(path):
            logger.error(f"Path not found: {path}")
            return None
        
        try:
            # Load config
            config_path = os.path.join(path, 'alert_pipeline_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
            else:
                logger.warning(f"Config file not found at {config_path}, using defaults")
                config = None
            
            # Create pipeline
            pipeline = cls(config)
            
            # Load metadata
            metadata_path = os.path.join(path, 'alert_pipeline_metadata.json')
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r') as f:
                    metadata = json.load(f)
                pipeline.metadata = metadata
            
            # Load rules
            rules_path = os.path.join(path, 'alert_rules.json')
            if os.path.exists(rules_path):
                with open(rules_path, 'r') as f:
                    rules_data = json.load(f)
                
                for rule_data in rules_data:
                    try:
                        rule = AlertRule.from_dict(rule_data)
                        pipeline.rules[rule.rule_id] = rule
                    except Exception as e:
                        logger.error(f"Error loading rule: {e}")
            
            logger.info(f"Alert pipeline loaded from {path} with {len(pipeline.rules)} rules")
            return pipeline
        
        except Exception as e:
            logger.error(f"Error loading alert pipeline: {e}")
            return None


class RuleBuilder:
    """
    Helper class for building alert rules with a fluent interface.
    """
    
    def __init__(self, rule_id: Optional[str] = None):
        """
        Initialize the rule builder.
        
        Args:
            rule_id: Optional rule ID (auto-generated if not provided)
        """
        self.rule_id = rule_id or f"rule_{int(time.time())}_{id(self)}"
        self.rule_type = None
        self.model_type = None
        self.conditions = {}
        self.actions = {}
        self.enabled = True
        self.description = None
    
    def for_pattern_detection(self) -> 'RuleBuilder':
        """
        Set rule type to pattern detection.
        
        Returns:
            Self for method chaining
        """
        self.rule_type = 'pattern'
        self.model_type = 'pattern_recognition'
        return self
    
    def for_price_prediction(self) -> 'RuleBuilder':
        """
        Set rule type to price prediction.
        
        Returns:
            Self for method chaining
        """
        self.rule_type = 'price'
        self.model_type = 'predictive_analytics'
        return self
    
    def for_strategy_signals(self) -> 'RuleBuilder':
        """
        Set rule type to strategy signals.
        
        Returns:
            Self for method chaining
        """
        self.rule_type = 'strategy'
        self.model_type = 'strategy_optimizer'
        return self
    
    def with_patterns(self, patterns: List[str]) -> 'RuleBuilder':
        """
        Add pattern names condition.
        
        Args:
            patterns: List of pattern names to match
            
        Returns:
            Self for method chaining
        """
        self.conditions['patterns'] = patterns
        return self
    
    def with_min_confidence(self, confidence: float) -> 'RuleBuilder':
        """
        Add minimum confidence condition.
        
        Args:
            confidence: Minimum confidence threshold (0.0 to 1.0)
            
        Returns:
            Self for method chaining
        """
        self.conditions['min_confidence'] = max(0.0, min(1.0, confidence))
        return self
    
    def with_confidence_level(self, level: str) -> 'RuleBuilder':
        """
        Add confidence level condition.
        
        Args:
            level: Confidence level ('high', 'medium', 'low', 'very_low')
            
        Returns:
            Self for method chaining
        """
        valid_levels = ['high', 'medium', 'low', 'very_low']
        if level.lower() not in valid_levels:
            logger.warning(f"Invalid confidence level: {level}, using 'medium'")
            level = 'medium'
        
        self.conditions['confidence_level'] = level.lower()
        return self
    
    def with_price_direction(self, direction: str) -> 'RuleBuilder':
        """
        Add price direction condition.
        
        Args:
            direction: Price direction ('up' or 'down')
            
        Returns:
            Self for method chaining
        """
        valid_directions = ['up', 'down']
        if direction.lower() not in valid_directions:
            logger.warning(f"Invalid price direction: {direction}, using 'up'")
            direction = 'up'
        
        self.conditions['direction'] = direction.lower()
        return self
    
    def with_price_threshold(self, percent: float) -> 'RuleBuilder':
        """
        Add price threshold condition.
        
        Args:
            percent: Threshold percentage (e.g., 5.0 for 5%)
            
        Returns:
            Self for method chaining
        """
        self.conditions['threshold_percent'] = max(0.0, percent)
        return self
    
    def with_horizon(self, periods: int) -> 'RuleBuilder':
        """
        Add time horizon condition.
        
        Args:
            periods: Number of time periods
            
        Returns:
            Self for method chaining
        """
        self.conditions['horizon'] = max(1, periods)
        return self
    
    def with_signal_type(self, signal_type: str) -> 'RuleBuilder':
        """
        Add signal type condition.
        
        Args:
            signal_type: Signal type ('buy', 'sell', or 'hold')
            
        Returns:
            Self for method chaining
        """
        valid_types = ['buy', 'sell', 'hold']
        if signal_type.lower() not in valid_types:
            logger.warning(f"Invalid signal type: {signal_type}, using 'buy'")
            signal_type = 'buy'
        
        self.conditions['signal_type'] = signal_type.lower()
        return self
    
    def with_min_return(self, return_value: float) -> 'RuleBuilder':
        """
        Add minimum return condition.
        
        Args:
            return_value: Minimum return value (e.g., 0.05 for 5%)
            
        Returns:
            Self for method chaining
        """
        self.conditions['min_return'] = max(0.0, return_value)
        return self
    
    def with_confidence_threshold(self, threshold: float) -> 'RuleBuilder':
        """
        Add confidence threshold for alert generation.
        
        Args:
            threshold: Confidence threshold (0.0 to 1.0)
            
        Returns:
            Self for method chaining
        """
        self.actions['confidence_threshold'] = max(0.0, min(1.0, threshold))
        return self
    
    def with_alert_confidence(self, confidence: float) -> 'RuleBuilder':
        """
        Add confidence value for generated alerts.
        
        Args:
            confidence: Confidence value (0.0 to 1.0)
            
        Returns:
            Self for method chaining
        """
        self.actions['confidence'] = max(0.0, min(1.0, confidence))
        return self
    
    def with_threshold_percent(self, percent: float) -> 'RuleBuilder':
        """
        Add threshold percentage for alert generation.
        
        Args:
            percent: Threshold percentage (e.g., 5.0 for 5%)
            
        Returns:
            Self for method chaining
        """
        self.actions['threshold_percent'] = max(0.0, percent)
        return self
    
    def with_time_horizon(self, periods: int) -> 'RuleBuilder':
        """
        Add time horizon for alert generation.
        
        Args:
            periods: Number of time periods
            
        Returns:
            Self for method chaining
        """
        self.actions['time_horizon'] = max(1, periods)
        return self
    
    def with_expiration(self, hours: float) -> 'RuleBuilder':
        """
        Add expiration time for generated alerts.
        
        Args:
            hours: Expiration time in hours
            
        Returns:
            Self for method chaining
        """
        self.actions['expiration_hours'] = max(0.0, hours)
        return self
    
    def with_description(self, description: str) -> 'RuleBuilder':
        """
        Add a human-readable description.
        
        Args:
            description: Rule description
            
        Returns:
            Self for method chaining
        """
        self.description = description
        return self
    
    def enabled(self, enabled: bool = True) -> 'RuleBuilder':
        """
        Set whether the rule is enabled.
        
        Args:
            enabled: Whether the rule is enabled
            
        Returns:
            Self for method chaining
        """
        self.enabled = enabled
        return self
    
    def build(self) -> AlertRule:
        """
        Build the alert rule.
        
        Returns:
            Constructed AlertRule instance
        """
        if not self.rule_type or not self.model_type:
            raise ValueError("Rule type must be specified")
        
        return AlertRule(
            rule_id=self.rule_id,
            rule_type=self.rule_type,
            model_type=self.model_type,
            conditions=self.conditions,
            actions=self.actions,
            enabled=self.enabled,
            description=self.description
        )


# Global instance
_alert_pipeline = None

def get_alert_pipeline(config: Optional[Dict[str, Any]] = None) -> AlertPipeline:
    """
    Get the global alert pipeline instance.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        AlertPipeline instance
    """
    global _alert_pipeline
    if _alert_pipeline is None:
        _alert_pipeline = AlertPipeline(config)
    return _alert_pipeline

def process_inference(inference_result: InferenceResult) -> List[Alert]:
    """
    Process an inference result using the global alert pipeline.
    
    Args:
        inference_result: Inference result to process
        
    Returns:
            List of created alerts if processing synchronously, empty list if async
    """
    pipeline = get_alert_pipeline()
    return pipeline.process_inference(inference_result)

def create_rule() -> RuleBuilder:
    """
    Create a new rule builder.
    
    Returns:
        RuleBuilder instance
    """
    return RuleBuilder()

def add_rule(rule: AlertRule) -> None:
    """
    Add a rule to the global alert pipeline.
    
    Args:
        rule: Alert rule to add
    """
    pipeline = get_alert_pipeline()
    pipeline.add_rule(rule)
