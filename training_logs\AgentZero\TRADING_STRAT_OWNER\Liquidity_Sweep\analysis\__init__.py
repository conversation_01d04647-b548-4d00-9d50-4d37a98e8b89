"""
Analysis module for Liquidity Reports System.

This package provides imports from the working analyzers directory.
All components are imported from the correct locations to eliminate import issues.
"""

# Import working components from the analyzers directory using absolute imports
try:
    from Liquidity_Reports.analyzers.gex_analyzer import GammaExposureAnalyzer
except ImportError:
    try:
        from Liquidity_Reports.analyzers.optimized_gex_analyzer import OptimizedGammaExposureAnalyzer as GammaExposureAnalyzer
    except ImportError:
        class GammaExposureAnalyzer:
            def __init__(self, *args, **kwargs):
                raise ImportError("GammaExposureAnalyzer could not be imported")

try:
    from Liquidity_Reports.analyzers.optimized_gex_analyzer import OptimizedGammaExposureAnalyzer
except ImportError:
    class OptimizedGammaExposureAnalyzer:
        def __init__(self, *args, **kwargs):
            raise ImportError("OptimizedGammaExposureAnalyzer could not be imported")

try:
    from Liquidity_Reports.analyzers.volume_analyzer import VolumeAnalyzer
except ImportError:
    class VolumeAnalyzer:
        def __init__(self, *args, **kwargs):
            raise ImportError("VolumeAnalyzer could not be imported")

try:
    from Liquidity_Reports.analyzers.enhanced_volume_analyzer import EnhancedVolumeAnalyzer
except ImportError:
    class EnhancedVolumeAnalyzer:
        def __init__(self, *args, **kwargs):
            raise ImportError("EnhancedVolumeAnalyzer could not be imported")

# Options Analyzers
try:
    from Liquidity_Reports.analyzers.oi_liquidity_analyzer import OILiquidityAnalyzer
except ImportError:
    class OILiquidityAnalyzer:
        def __init__(self, *args, **kwargs):
            raise ImportError("OILiquidityAnalyzer could not be imported")

try:
    from Liquidity_Reports.analyzers.options_oi_analyzer import OptionsOIAnalyzer
except ImportError:
    class OptionsOIAnalyzer:
        def __init__(self, *args, **kwargs):
            raise ImportError("OptionsOIAnalyzer could not be imported")

# Price Action Analyzer
try:
    from Liquidity_Reports.analyzers.price_action_analyzer import PriceActionAnalyzer
except ImportError:
    class PriceActionAnalyzer:
        def __init__(self, *args, **kwargs):
            raise ImportError("PriceActionAnalyzer could not be imported")

# Trap Pattern Detector
try:
    from Liquidity_Reports.analyzers.trap_pattern_detector import TrapPatternDetector
except ImportError:
    class TrapPatternDetector:
        def __init__(self, *args, **kwargs):
            raise ImportError("TrapPatternDetector could not be imported")

# Consensus Builder removed

# All imports completed

# Export key analyzers
__all__ = [
    'GammaExposureAnalyzer',
    'OptimizedGammaExposureAnalyzer',
    'VolumeAnalyzer',
    'EnhancedVolumeAnalyzer',
    'OILiquidityAnalyzer',  # Add the robust OI Liquidity Analyzer
    'OptionsOIAnalyzer',    # This is now the adapter by default
    'PriceActionAnalyzer',
    'TrapPatternDetector'
]
