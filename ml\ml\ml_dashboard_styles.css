/* ML Dashboard Tab Styles */

/* General ML tab styles */
.ml-tab {
    font-family: sans-serif;
    padding: 15px;
    background-color: #f8f9fa;
}

.ml-tab-header {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
}

/* Section styles */
.ml-section {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px;
    margin-bottom: 20px;
}

.ml-section-header {
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 15px;
    font-weight: 500;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 10px;
}

/* Control section styles */
.ml-control-section {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.ml-control-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 20px;
}

.ml-control-group {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.ml-control-label {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.ml-control-buttons {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    gap: 10px;
}

.ml-control-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.ml-primary-btn {
    background-color: #007bff;
    color: white;
}

.ml-primary-btn:hover {
    background-color: #0069d9;
}

.ml-success-btn {
    background-color: #28a745;
    color: white;
}

.ml-success-btn:hover {
    background-color: #218838;
}

.ml-control-btn:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
}

/* Model info section styles */
.ml-info-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
}

.ml-info-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ml-info-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #ecf0f1;
}

.ml-info-label {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.ml-info-value {
    font-size: 14px;
    color: #7f8c8d;
}

.ml-metric {
    font-weight: 600;
    color: #2c3e50;
}

/* Content container and columns */
.ml-tab-content-container {
    display: flex;
    flex-direction: row;
    gap: 20px;
}

.ml-tab-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Visualization container */
.ml-visualization-container {
    width: 100%;
    overflow: hidden;
}

/* Pattern section styles */
.ml-pattern-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.ml-pattern-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-left: 4px solid #3498db;
    background-color: #f8f9fa;
    margin-bottom: 8px;
    border-radius: 0 4px 4px 0;
}

.ml-pattern-name {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
}

.ml-pattern-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.ml-pattern-prob {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.ml-pattern-confidence {
    font-size: 12px;
    color: #7f8c8d;
}

.ml-no-patterns {
    padding: 10px;
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
}

/* Prediction section styles */
.ml-prediction-summary {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 10px;
}

.ml-prediction-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.ml-prediction-horizon {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
}

.ml-prediction-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.ml-prediction-value {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

.ml-prediction-change {
    font-size: 12px;
}

.ml-positive {
    color: #28a745;
}

.ml-negative {
    color: #dc3545;
}

.ml-no-predictions {
    padding: 10px;
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
}

/* Performance metrics styles */
.ml-metrics-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ml-metric-row {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.ml-metric-label {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 500;
}

.ml-metric-value {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
}

/* Responsive styles */
@media (max-width: 992px) {
    .ml-tab-content-container {
        flex-direction: column;
    }
    
    .ml-control-row {
        flex-direction: column;
    }
    
    .ml-info-container {
        flex-direction: column;
    }
}
