#!/usr/bin/env python3
"""
Data Flow Tree Validation Test
Tests all critical data paths and transformations
"""

import sys
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, Any

def test_data_ingestion():
    """Test data ingestion layer"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[DATA INGESTION LAYER]")
    
    # Test EnhancedDataAgent
    try:
        from enhanced_data_agent_broker_integration import EnhancedDataAgent
        agent = EnhancedDataAgent()
        results['passed'] += 1
        print("+ EnhancedDataAgent: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"EnhancedDataAgent: {str(e)}")
        print(f"- EnhancedDataAgent: Import FAILED - {str(e)}")
    
    # Test DataHandler
    try:
        from data.data_handler import DataHandler
        handler = DataHandler()
        results['passed'] += 1
        print("+ DataHandler: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"DataHandler: {str(e)}")
        print(f"- DataHandler: Import FAILED - {str(e)}")
    
    # Test APIGateway
    try:
        from data.api_gateway import APIGateway
        gateway = APIGateway()
        results['passed'] += 1
        print("+ APIGateway: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"APIGateway: {str(e)}")
        print(f"- APIGateway: Import FAILED - {str(e)}")
    
    return results

def test_feature_engineering():
    """Test feature engineering pipeline"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[FEATURE ENGINEERING PIPELINE]")
    
    # Test FeatureBuilder
    try:
        from tasks.build_features import FeatureBuilder
        builder = FeatureBuilder()
        results['passed'] += 1
        print("+ FeatureBuilder: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"FeatureBuilder: {str(e)}")
        print(f"- FeatureBuilder: Import FAILED - {str(e)}")
    
    # Test GreeksEngine
    try:
        from enhanced_greeks_engine import EnhancedGreeksEngine
        engine = EnhancedGreeksEngine()
        results['passed'] += 1
        print("+ GreeksEngine: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"GreeksEngine: {str(e)}")
        print(f"- GreeksEngine: Import FAILED - {str(e)}")
    
    # Test GreeksCalculator
    try:
        from tasks.greeks import GreeksCalculator
        calc = GreeksCalculator()
        results['passed'] += 1
        print("+ GreeksCalculator: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"GreeksCalculator: {str(e)}")
        print(f"- GreeksCalculator: Import FAILED - {str(e)}")
    
    return results

def test_specialized_army_data_flow():
    """Test specialized agent army data flow"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[SPECIALIZED ARMY DATA FLOW]")
    
    # Test AccumulationDistributionAgent
    try:
        from agents.accumulation_distribution_detector.accumulation_distribution_detector import AccumulationDistributionAgent
        agent = AccumulationDistributionAgent("test_agent")
        results['passed'] += 1
        print("+ AccumulationDistributionAgent: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"AccumulationDistributionAgent: {str(e)}")
        print(f"- AccumulationDistributionAgent: Import FAILED - {str(e)}")
    
    # Test BreakoutValidationAgent
    try:
        from agents.breakout_validation_specialist.breakout_validation_specialist import BreakoutValidationAgent
        agent = BreakoutValidationAgent("test_agent")
        results['passed'] += 1
        print("+ BreakoutValidationAgent: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"BreakoutValidationAgent: {str(e)}")
        print(f"- BreakoutValidationAgent: Import FAILED - {str(e)}")
    
    # Test OptionsFlowDecoderAgent
    try:
        from agents.options_flow_decoder.options_flow_decoder import OptionsFlowDecoderAgent
        agent = OptionsFlowDecoderAgent("test_agent")
        results['passed'] += 1
        print("+ OptionsFlowDecoderAgent: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"OptionsFlowDecoderAgent: {str(e)}")
        print(f"- OptionsFlowDecoderAgent: Import FAILED - {str(e)}")
    
    return results

def test_ultimate_orchestrator_integration():
    """Test ultimate orchestrator integration"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[ULTIMATE ORCHESTRATOR INTEGRATION]")
    
    # Test ultimate_orchestrator import
    try:
        import ultimate_orchestrator
        results['passed'] += 1
        print("+ UltimateOrchestrator: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"UltimateOrchestrator: {str(e)}")
        print(f"- UltimateOrchestrator: Import FAILED - {str(e)}")
    
    # Test Agent Zero integration
    try:
        from agents.agent_zero import get_agent_zero_hub
        hub = get_agent_zero_hub()
        results['passed'] += 1
        print("+ AgentZeroHub: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"AgentZeroHub: {str(e)}")
        print(f"- AgentZeroHub: Import FAILED - {str(e)}")
    
    return results

def test_data_validation_pipeline():
    """Test data validation and quality systems"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[DATA VALIDATION PIPELINE]")
    
    # Test MathValidatorAgent
    try:
        from agents.math_validator_agent import MathValidatorAgent
        validator = MathValidatorAgent("test_validator")
        results['passed'] += 1
        print("+ MathValidatorAgent: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"MathValidatorAgent: {str(e)}")
        print(f"- MathValidatorAgent: Import FAILED - {str(e)}")
    
    # Test data quality metrics calculation
    try:
        from enhanced_data_agent_broker_integration import DataQualityMetrics
        # Create with sample values since it's a dataclass
        metrics = DataQualityMetrics(
            completeness_ratio=0.95,
            accuracy_score=0.90,
            consistency_index=0.85,
            timeliness_factor=0.92,
            overall_quality=0.90
        )
        results['passed'] += 1
        print("+ DataQualityMetrics: Import SUCCESS")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"DataQualityMetrics: {str(e)}")
        print(f"- DataQualityMetrics: Import FAILED - {str(e)}")
    
    return results

def test_synthetic_data_generation():
    """Test synthetic data generation capabilities"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[SYNTHETIC DATA GENERATION]")
    
    # Test synthetic Greeks generation
    try:
        from tasks.greeks import GreeksCalculator
        calc = GreeksCalculator()
        synthetic_greeks = calc.calculate_synthetic_greeks("SPY")
        if 'summary' in synthetic_greeks:
            results['passed'] += 1
            print("+ SyntheticGreeks: Generation SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("SyntheticGreeks: Invalid output format")
            print("- SyntheticGreeks: Generation FAILED - Invalid format")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"SyntheticGreeks: {str(e)}")
        print(f"- SyntheticGreeks: Generation FAILED - {str(e)}")
    
    return results

def test_data_flow_performance():
    """Test critical data flow paths"""
    results = {'passed': 0, 'failed': 0, 'errors': []}
    
    print("\n[DATA FLOW PERFORMANCE]")
    
    # Test data package creation
    try:
        from data.data_handler import DataHandler
        handler = DataHandler()
        
        # Create sample data
        sample_data = pd.DataFrame({
            'open': [100, 101, 102],
            'high': [101, 102, 103], 
            'low': [99, 100, 101],
            'close': [100.5, 101.5, 102.5],
            'volume': [1000, 1100, 1200]
        })
        
        mtf_data = {'15m': sample_data}
        data_package = handler.create_data_package("SPY", 102.5, mtf_data)
        
        if 'ticker' in data_package and 'data_quality' in data_package:
            results['passed'] += 1
            print("+ DataPackageCreation: SUCCESS")
        else:
            results['failed'] += 1
            results['errors'].append("DataPackageCreation: Invalid package format")
            print("- DataPackageCreation: FAILED - Invalid format")
    except Exception as e:
        results['failed'] += 1
        results['errors'].append(f"DataPackageCreation: {str(e)}")
        print(f"- DataPackageCreation: FAILED - {str(e)}")
    
    return results

def combine_results(*result_sets):
    """Combine multiple test result sets"""
    combined = {'passed': 0, 'failed': 0, 'errors': []}
    
    for result_set in result_sets:
        combined['passed'] += result_set['passed']
        combined['failed'] += result_set['failed']
        combined['errors'].extend(result_set['errors'])
    
    return combined

def main():
    """Run complete data flow validation"""
    print("CORE SYSTEM DATA FLOW VALIDATION")
    print("=" * 50)
    
    # Run all test suites
    ingestion_results = test_data_ingestion()
    feature_results = test_feature_engineering()
    army_results = test_specialized_army_data_flow()
    orchestrator_results = test_ultimate_orchestrator_integration()
    validation_results = test_data_validation_pipeline()
    synthetic_results = test_synthetic_data_generation()
    performance_results = test_data_flow_performance()
    
    # Combine all results
    total_results = combine_results(
        ingestion_results, feature_results, army_results,
        orchestrator_results, validation_results, 
        synthetic_results, performance_results
    )
    
    # Summary
    print("\n" + "=" * 50)
    print("DATA FLOW VALIDATION SUMMARY")
    print("=" * 50)
    print(f"PASSED: {total_results['passed']}")
    print(f"FAILED: {total_results['failed']}")
    
    if total_results['errors']:
        print("\nERRORS:")
        for error in total_results['errors']:
            print(f"  {error}")
    
    success_rate = (total_results['passed'] / (total_results['passed'] + total_results['failed'])) * 100
    print(f"\nSUCCESS RATE: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("\n+ DATA FLOW: OPERATIONAL")
    elif success_rate >= 75:
        print("\n~ DATA FLOW: FUNCTIONAL (some components failed)")
    else:
        print("\n- DATA FLOW: NEEDS ATTENTION")
    
    print(f"\nDATA PIPELINE STATUS: {'READY' if success_rate >= 85 else 'REQUIRES FIXES'}")
    
    return success_rate >= 85

if __name__ == "__main__":
    main()
