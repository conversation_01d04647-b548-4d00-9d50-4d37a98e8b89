# Chart Generation Agent Workflow

## Agent: ChartGeneratorAgent
**Phase**: 3 | **Priority**: HIGH | **Max Time**: 30 seconds

---

## Objective
Generate 3 mathematically precise validation charts following engineering excellence standards with >99% accuracy.

## Input Requirements Validation
Before starting, verify:
-  Confluence result with 3 agreeing analyzers  
-  Market data with 50 bars minimum
-  Current price validation passed
-  Mathematical precision >99.9% confirmed
-  Factor strength scores 0.4

## Step-by-Step Execution

### **Step 1: Data Preparation & Validation**
```
Duration: 2-3 seconds
Success Criteria: All inputs validated, data quality >95%
```

1. **Load Analysis Results**
   - Parse confluence result JSON
   - Extract factor data from 4 analyzers
   - Validate mathematical precision markers

2. **Validate Market Data**
   - Check OHLCV completeness (no gaps >5%)  
   - Verify timeframe consistency
   - Confirm price data accuracy

3. **Prepare Chart Data Structures**
   - Normalize price ranges
   - Calculate optimal chart scales
   - Prepare color schemes for clarity

**Quality Gate**: Data validation must pass before proceeding

---

### **Step 2: Generate Chart 1 - Volume Profile + Flow Concentration**
```
Duration: 8-10 seconds  
Success Criteria: POC/VAH/VAL accuracy >99%, flow overlay precise
```

1. **Calculate Volume Profile Components**
   ```
   Precision Requirements:
   - POC (Point of Control): Highest volume price level
   - VAH (Value Area High): 70% volume area upper bound  
   - VAL (Value Area Low): 70% volume area lower bound
   - Mathematical accuracy: >99.9%
   ```

2. **Generate Flow Velocity Overlay**
   - Calculate flow derivatives (velocity, acceleration, jerk)
   - Create directional flow arrows
   - Color-code by flow strength (green=bullish, red=bearish)
   - Scale arrows proportional to flow magnitude

3. **Mark Accumulation/Distribution Zones**
   - Identify institutional activity zones
   - Highlight campaign stages (building/accumulation/distribution)
   - Show absorption efficiency levels

4. **Add Current Price Marker**
   - Bright marker at current price level
   - Show relationship to POC/VAH/VAL
   - Display price as percentage of value area

**Validation**: Verify POC calculation matches manual verification

---

### **Step 3: Generate Chart 2 - Liquidity Walls + GEX Analysis**
```
Duration: 10-12 seconds
Success Criteria: Level accuracy verified, GEX calculations precise
```

1. **Plot Bid/Ask Wall Visualization**
   - Show liquidity concentration levels
   - Display wall thickness proportional to volume
   - Color-code support (green) vs resistance (red)

2. **Mark Support/Resistance Levels**
   - Identify key institutional levels
   - Show level strength based on touch count
   - Display absorption efficiency at each level

3. **Display GEX Zero-Gamma Levels**
   - Calculate gamma exposure across strike range
   - Mark zero-gamma inflection points
   - Show dealer positioning indicators

4. **Add Gamma Concentration Zones**
   - Highlight high gamma concentration areas
   - Show positive vs negative gamma zones
   - Mark dealer hedging levels

**Validation**: Cross-check GEX calculations with Black-Scholes verification

---

### **Step 4: Generate Chart 3 - Factor Confluence Dashboard**
```
Duration: 6-8 seconds
Success Criteria: Agreement matrix accurate, gauges functional
```

1. **Create 4x4 Analyzer Agreement Matrix**
   ```
   Matrix Layout:
   
                 Flow  Vol   Liquidity  GEX 
   
    Direction                           
    Strength     0.8   0.7      0.75    0.6  
    Confidence   0.85  0.8      0.9     0.75
    Agreement                           
   
   ```

2. **Display Signal Strength Gauge**
   - Circular gauge showing overall signal strength
   - Color zones: Green (strong), Yellow (moderate), Red (weak)
   - Numerical display of confidence percentage

3. **Show Flow Trends (Velocity/Acceleration)**
   - Time series plot of flow derivatives
   - Trend arrows showing momentum direction
   - Magnitude indicators for flow strength

4. **Add ML Confidence Indicator (Placeholder)**
   - Reserved space for future ML enhancement
   - Currently shows "ML Module: Ready for Training"
   - Placeholder confidence score display

**Validation**: Verify matrix calculations match confluence engine output

---

### **Step 5: Final Validation & Quality Assurance**
```
Duration: 2-3 seconds
Success Criteria: All charts pass accuracy validation >99%
```

1. **Mathematical Precision Check**
   - Verify all calculations match source data
   - Cross-reference chart values with raw analysis
   - Confirm no rounding errors >1e-10

2. **Visual Quality Validation**  
   - Check chart rendering completeness
   - Verify color schemes and readability
   - Confirm all required elements present

3. **Performance Validation**
   - Verify execution time <30 seconds
   - Check memory usage within limits
   - Confirm chart file sizes reasonable

4. **Export Chart Package**
   - Save charts in specified formats (PNG, SVG)
   - Generate chart metadata JSON
   - Create chart validation report

---

## Error Handling Procedures

### **Data Quality Issues**
```
If input data quality <95%:
1. Log specific data quality issues
2. Request fresh data pull from MCP
3. If retry fails: Generate charts with quality warnings
4. Notify orchestrator of data limitations
```

### **Calculation Errors**
```
If mathematical precision fails:
1. Log calculation discrepancy details
2. Retry calculation with increased precision
3. If retry fails: Fall back to simplified calculation
4. Mark charts with accuracy warnings
```

### **Rendering Timeouts**
```
If chart generation exceeds 30 seconds:
1. Generate simplified charts (remove complex overlays)
2. Prioritize Chart 3 (most critical for decision making)
3. Log performance issue for optimization
4. Complete within 45 second absolute limit
```

### **Missing Data Elements**
```
If required data missing:
1. Generate charts with available data
2. Mark missing elements clearly
3. Provide estimation where mathematically sound
4. Document limitations in chart metadata
```

---

## Agent Zero Training Data Generated

This workflow generates critical training data for Agent Zero:

### **Decision Points Captured**
- When to prioritize accuracy vs speed
- How to handle incomplete data gracefully  
- Chart layout optimization strategies
- Error recovery decision trees

### **Quality Standards Demonstrated**
- Mathematical precision requirements (>99.9%)
- Visual accuracy standards  
- Performance benchmarks (<30 seconds)
- Error tolerance levels

### **Success Patterns Recorded**
- Optimal chart generation sequences
- Efficient data processing methods
- Effective validation strategies
- Quality assurance best practices

---

## Output Standards

### **Chart Requirements**
- **Resolution**: Minimum 1920x1080 pixels
- **Format**: PNG for display, SVG for scalability
- **Color Scheme**: Consistent across all charts
- **Mathematical Accuracy**: >99% precision verified

### **Metadata Requirements**
- Generation timestamp
- Data source verification
- Accuracy metrics
- Performance statistics

### **Quality Assurance**
- All calculations cross-validated
- Visual elements properly scaled
- Charts render correctly across platforms
- Metadata complete and accurate

---

**Agent Success Criteria**: Charts generated within time limit, accuracy >99%, all validation gates passed, training data captured for Agent Zero learning.
