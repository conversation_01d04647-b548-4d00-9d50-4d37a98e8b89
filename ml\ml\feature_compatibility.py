"""
Feature Compatibility Layer

This module provides compatibility between different feature sets used by ML models
and the current feature extraction system. It ensures that models trained with one
feature set can work with data that has a different feature set.

Key functions:
- align_features: Aligns features between model requirements and available features
- create_missing_features: Creates missing features with reasonable defaults
- map_similar_features: Maps similar features when exact matches aren't available
- impute_missing_values: Robust imputation for NaN values in features
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import logging
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional, Set, Tuple
from sklearn.impute import SimpleImputer, KNNImputer

# Configure logging
logger = logging.getLogger("Liquidity_Reports.feature_compatibility")


def align_features(available_features: pd.DataFrame, required_features: List[str]) -> pd.DataFrame:
    """
    Align available features with the required feature set.

    This function ensures that the output DataFrame has exactly the columns
    specified in required_features, in the same order. It will:
    1. Keep features that exist in both sets
    2. Create missing features with reasonable defaults
    3. Map similar features when possible
    4. Drop extra features that aren't required
    5. Handle NaN values with imputation

    Args:
        available_features: DataFrame with available features
        required_features: List of feature names required by the model

    Returns:
        DataFrame with aligned features matching the required feature set
    """
    if available_features.empty:
        logger.warning("Empty feature DataFrame provided")
        # Create a DataFrame with zeros for all required features
        return pd.DataFrame(0, index=[0], columns=required_features)

    # Get the set of available and required features
    available_set = set(available_features.columns)
    required_set = set(required_features)

    # Find features that exist in both sets
    common_features = available_set.intersection(required_set)
    logger.info(f"Found {len(common_features)} common features")

    # Find missing features
    missing_features = required_set - available_set
    if missing_features:
        logger.warning(f"Missing {len(missing_features)} required features")

        # Try to map similar features
        mapped_features = map_similar_features(available_features, list(missing_features))

        # Create any remaining missing features
        remaining_missing = missing_features - set(mapped_features.keys())
        if remaining_missing:
            logger.warning(f"Creating {len(remaining_missing)} missing features with default values")
            default_features = create_missing_features(available_features, list(remaining_missing))

            # Combine available features with mapped and default features
            result = pd.concat([
                available_features[list(common_features)],
                mapped_features,
                default_features
            ], axis=1)
        else:
            # Combine available features with mapped features
            result = pd.concat([
                available_features[list(common_features)],
                mapped_features
            ], axis=1)
    else:
        # All required features are available
        result = available_features[list(common_features)]

    # Ensure the result has exactly the required columns in the right order
    result = result[required_features]

    # Check for NaN values and impute them
    if result.isna().any().any():
        nan_columns = result.columns[result.isna().any()].tolist()
        nan_percentage = result.isna().sum().sum() / (result.shape[0] * result.shape[1]) * 100
        logger.warning(f"NaN values found in {len(nan_columns)} columns after alignment: {nan_columns}")
        logger.warning(f"Overall NaN percentage: {nan_percentage:.2f}%")
        logger.info("Applying robust imputation to ensure model compatibility")

        # Try to use the new MLImputer
        try:
            from imputer import MLImputer
            logger.info("Using MLImputer for robust imputation")

            # Create imputer with auto strategy
            imputer = MLImputer(strategy='auto')

            # Impute missing values
            result = imputer.fit_transform(result)

            logger.info("Successfully imputed missing values with MLImputer")
        except ImportError:
            logger.warning("MLImputer not available, falling back to basic imputation")

            # First try statistical imputation for columns with sufficient data
            for col in result.columns:
                if result[col].isna().any():
                    non_nan_ratio = result[col].notna().mean()
                    logger.info(f"Column '{col}' has {(1-non_nan_ratio)*100:.1f}% NaN values")

                    # If we have at least 30% non-NaN values, use statistical imputation
                    if non_nan_ratio >= 0.3:
                        # For skewed features, use median
                        if 'volume' in col or 'count' in col or 'size' in col:
                            median_val = result[col].median()
                            result[col] = result[col].fillna(median_val)
                            logger.info(f"Imputed column '{col}' with median value: {median_val:.4f}")
                        # For more normally distributed features, use mean
                        else:
                            mean_val = result[col].mean()
                            result[col] = result[col].fillna(mean_val)
                            logger.info(f"Imputed column '{col}' with mean value: {mean_val:.4f}")
                    else:
                        # For columns with insufficient data, use domain knowledge
                        if 'ratio' in col or 'correlation' in col or 'skew' in col:
                            # Ratios and correlations typically centered around 0
                            result[col] = result[col].fillna(0.0)
                            logger.info(f"Imputed column '{col}' with default value: 0.0")
                        elif 'distance' in col:
                            # Distances are typically positive
                            result[col] = result[col].fillna(1.0)
                            logger.info(f"Imputed column '{col}' with default value: 1.0")
                        elif 'strength' in col or 'score' in col:
                            # Strengths and scores typically in 0-1 range
                            result[col] = result[col].fillna(0.5)
                            logger.info(f"Imputed column '{col}' with default value: 0.5")
                        elif 'count' in col:
                            # Counts are non-negative integers
                            result[col] = result[col].fillna(0)
                            logger.info(f"Imputed column '{col}' with default value: 0")
                        elif 'volatility' in col:
                            # Volatility is typically positive
                            result[col] = result[col].fillna(0.2)
                            logger.info(f"Imputed column '{col}' with default value: 0.2")
                        elif 'volume' in col:
                            # Volume-related features
                            result[col] = result[col].fillna(1.0)
                            logger.info(f"Imputed column '{col}' with default value: 1.0")
                        else:
                            # Default strategy: use 0 for numerical features
                            result[col] = result[col].fillna(0.0)
                            logger.info(f"Imputed column '{col}' with default value: 0.0")
        except Exception as e:
            logger.error(f"Error using MLImputer: {str(e)}")
            logger.warning("Falling back to basic imputation")

            # Last resort: fill any remaining NaNs with 0
            result = result.fillna(0.0)
            logger.info("Filled all NaN values with 0.0 using basic imputation")

        # Verify all NaNs have been handled
        if result.isna().any().any():
            remaining_nan_cols = result.columns[result.isna().any()].tolist()
            logger.warning(f"Still have NaN values in columns after imputation: {remaining_nan_cols}")
            # Last resort: fill any remaining NaNs with 0
            result = result.fillna(0.0)
            logger.info("Filled all remaining NaN values with 0.0")
        else:
            logger.info("All NaN values successfully imputed")

    return result


def create_missing_features(base_features: pd.DataFrame, missing_features: List[str]) -> pd.DataFrame:
    """
    Create missing features with reasonable default values.

    Args:
        base_features: DataFrame with existing features
        missing_features: List of missing feature names

    Returns:
        DataFrame with the missing features filled with default values
    """
    result = pd.DataFrame(index=base_features.index)

    for feature in missing_features:
        # Use different defaults based on feature name patterns
        if 'ratio' in feature or 'correlation' in feature or 'skew' in feature:
            # Ratios and correlations are typically between -1 and 1
            result[feature] = 0.0
        elif 'distance' in feature:
            # Distances are typically positive
            result[feature] = 1.0
        elif 'strength' in feature or 'score' in feature:
            # Strengths and scores are typically between 0 and 1
            result[feature] = 0.5
        elif 'count' in feature:
            # Counts are integers
            result[feature] = 0
        elif 'volatility' in feature:
            # Volatility is typically positive
            result[feature] = 0.2
        elif 'volume' in feature:
            # Volume-related features
            result[feature] = 1.0
        else:
            # Default for other features
            result[feature] = 0.0

    return result


def map_similar_features(available_features: pd.DataFrame, missing_features: List[str]) -> pd.DataFrame:
    """
    Map similar features when exact matches aren't available.

    Args:
        available_features: DataFrame with available features
        missing_features: List of missing feature names

    Returns:
        DataFrame with mapped features
    """
    result = pd.DataFrame(index=available_features.index)
    mapped_count = 0

    # Define feature similarity mappings
    similarity_mappings = {
        'volume': ['volume', 'vol', 'size'],
        'price': ['price', 'close', 'value'],
        'volatility': ['volatility', 'vol', 'std'],
        'liquidity': ['liquidity', 'depth', 'flow'],
        'strength': ['strength', 'score', 'power'],
        'distance': ['distance', 'dist', 'proximity'],
        'ratio': ['ratio', 'proportion', 'rel'],
        'correlation': ['correlation', 'corr', 'coef'],
        'support': ['support', 'buy', 'bid'],
        'resistance': ['resistance', 'sell', 'ask'],
        'gex': ['gex', 'gamma', 'exposure'],
        'iv': ['iv', 'implied_vol', 'volatility'],
        'oi': ['oi', 'open_interest', 'interest'],
        'skew': ['skew', 'asymmetry', 'tilt']
    }

    # For each missing feature, try to find a similar available feature
    for missing in missing_features:
        mapped = False

        # Extract feature components
        components = missing.split('_')

        # Try to find similar features
        for available in available_features.columns:
            avail_components = available.split('_')

            # Check for similarity
            similarity_score = calculate_feature_similarity(components, avail_components, similarity_mappings)

            if similarity_score > 0.7:  # Threshold for similarity
                result[missing] = available_features[available]
                logger.info(f"Mapped missing feature '{missing}' to similar feature '{available}'")
                mapped_count += 1
                mapped = True
                break

        if not mapped:
            # If no similar feature found, it will be created with defaults later
            pass

    logger.info(f"Mapped {mapped_count} of {len(missing_features)} missing features to similar available features")
    return result


def calculate_feature_similarity(components1: List[str], components2: List[str],
                               similarity_mappings: Dict[str, List[str]]) -> float:
    """
    Calculate similarity score between two feature name component lists.

    Args:
        components1: Components of first feature name
        components2: Components of second feature name
        similarity_mappings: Dictionary mapping feature concepts to similar terms

    Returns:
        Similarity score between 0 and 1
    """
    if not components1 or not components2:
        return 0.0

    # Count matching components
    matches = 0
    total = max(len(components1), len(components2))

    # Check each component
    for c1 in components1:
        for c2 in components2:
            # Direct match
            if c1 == c2:
                matches += 1
                break

            # Check for similar concepts
            for concept, terms in similarity_mappings.items():
                if c1 in terms and c2 in terms:
                    matches += 0.8  # Partial match
                    break

    return matches / total
