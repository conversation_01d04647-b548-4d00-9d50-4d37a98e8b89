#!/usr/bin/env python3
"""
SIGNAL GENERATOR USAGE VERIFICATION
Verify if SignalGeneratorAgent is actually used in the system
"""

import os
import ast
import sys
from pathlib import Path

def check_signal_generator_usage():
    """Check actual usage of SignalGeneratorAgent in codebase"""
    
    core_path = Path("D:/script-work/CORE")
    
    # Files to check for actual usage
    active_files = [
        "main.py",
        "ultimate_orchestrator.py", 
        "agents/agent_orchestrator/agent_orchestrator.py"
    ]
    
    usage_found = {
        "imports": [],
        "instantiations": [], 
        "method_calls": [],
        "engine_usage": []
    }
    
    print("=== SIGNAL GENERATOR USAGE VERIFICATION ===")
    
    for file_rel in active_files:
        file_path = core_path / file_rel
        if not file_path.exists():
            continue
            
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                
            # Check for SignalGeneratorAgent
            if "SignalGeneratorAgent" in content:
                print(f"Found SignalGeneratorAgent reference in {file_rel}")
                usage_found["imports"].append(file_rel)
            
            # Check for engine SignalGenerator  
            if "SignalGenerator" in content and "engine" in content:
                print(f"Found engine SignalGenerator in {file_rel}")
                usage_found["engine_usage"].append(file_rel)
                
        except Exception as e:
            print(f"Error reading {file_rel}: {e}")
    
    print(f"\n=== RESULTS ===")
    print(f"SignalGeneratorAgent imports: {len(usage_found['imports'])}")
    print(f"Engine SignalGenerator usage: {len(usage_found['engine_usage'])}")
    
    if len(usage_found['imports']) == 0 and len(usage_found['engine_usage']) > 0:
        print("\nCONCLUSION: SignalGeneratorAgent appears UNUSED")
        print("   Engine SignalGenerator handles all signal generation")
        print("   Agent can likely be eliminated as redundant")
    
    return usage_found

if __name__ == "__main__":
    check_signal_generator_usage()
