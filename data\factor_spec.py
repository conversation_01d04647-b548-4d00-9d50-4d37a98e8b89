#!/usr/bin/env python3
"""
CORE Flow Detection System - Factor Specifications

Clean factor data definitions with minimal validation.
Essential data structures only - no bloat.
"""

from dataclasses import dataclass
from enum import Enum
from typing import Optional, Dict, Any, List
from datetime import datetime

class DirectionBias(Enum):
    """Factor direction bias enumeration."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"

class TimeFrame(Enum):
    """Supported timeframe enumeration."""
    MIN_1 = "1m"
    MIN_5 = "5m"
    MIN_15 = "15m"
    MIN_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAY_1 = "1d"
    WEEK_1 = "1w"

class SignalStrength(Enum):
    """Signal strength classification."""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"

class SignalDirection(Enum):
    """Signal direction enumeration."""
    LONG = "long"
    SHORT = "short"
    NEUTRAL = "neutral"

@dataclass
class FactorData:
    """
    Core factor data structure.
    Clean, minimal design for flow detection factors.
    """
    # Core identification
    factor_name: str
    analyzer_name: str
    timeframe: TimeFrame
    timestamp: datetime
    
    # Factor characteristics
    direction_bias: DirectionBias
    strength_score: float  # 0.0 to 1.0
    confidence_score: float  # 0.0 to 1.0
    
    # Quality metrics
    data_quality_score: float  # 0.0 to 1.0
    significance_level: float  # Statistical significance
    
    # Additional data
    raw_value: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Validate factor data after initialization."""
        self._validate_scores()
    
    def _validate_scores(self):
        """Ensure all scores are within valid ranges."""
        scores = [self.strength_score, self.confidence_score, self.data_quality_score, self.significance_level]
        for score in scores:
            if not 0.0 <= score <= 1.0:
                raise ValueError(f"Score {score} must be between 0.0 and 1.0")

@dataclass 
class StrategySignal:
    """
    Strategy signal output structure.
    Result of confluence analysis and signal generation.
    """
    # Core signal
    ticker: str
    direction: SignalDirection
    strength: SignalStrength
    timestamp: datetime
    
    # Confidence and timing
    confidence_range: tuple  # (min, max) confidence
    timing_estimate: tuple   # (min, max) days
    
    # Supporting evidence
    supporting_factors: List[str]
    factor_count: int
    agreement_score: float  # 0.0 to 1.0
    
    # Analysis details
    entry_price: Optional[float] = None
    analysis_summary: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

def validate_factor(factor: FactorData) -> tuple[bool, str]:
    """
    Validate factor data quality and consistency.
    
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        # Check required fields
        if not factor.factor_name or not factor.analyzer_name:
            return False, "Missing required factor identification"
        
        # Check enum values
        if not isinstance(factor.direction_bias, DirectionBias):
            return False, "Invalid direction bias"
        
        if not isinstance(factor.timeframe, TimeFrame):
            return False, "Invalid timeframe"
        
        # Check score ranges (already validated in __post_init__)
        # Check timestamp
        if not isinstance(factor.timestamp, datetime):
            return False, "Invalid timestamp"
        
        return True, ""
    
    except Exception as e:
        return False, f"Validation error: {str(e)}"

def validate_signal(signal: StrategySignal) -> tuple[bool, str]:
    """
    Validate strategy signal data.
    
    Returns:
        tuple: (is_valid, error_message)  
    """
    try:
        # Check required fields
        if not signal.ticker:
            return False, "Missing ticker"
        
        # Check enum values
        if not isinstance(signal.direction, SignalDirection):
            return False, "Invalid signal direction"
        
        if not isinstance(signal.strength, SignalStrength):
            return False, "Invalid signal strength"
        
        # Check ranges
        if not 0.0 <= signal.agreement_score <= 1.0:
            return False, "Agreement score must be 0.0-1.0"
        
        # Check confidence range
        if len(signal.confidence_range) != 2 or signal.confidence_range[0] > signal.confidence_range[1]:
            return False, "Invalid confidence range"
        
        # Check timing estimate
        if len(signal.timing_estimate) != 2 or signal.timing_estimate[0] > signal.timing_estimate[1]:
            return False, "Invalid timing estimate"
        
        return True, ""
    
    except Exception as e:
        return False, f"Signal validation error: {str(e)}"

def create_factor(analyzer_name: str, factor_name: str, direction: DirectionBias, 
                 strength: float, timeframe: TimeFrame, **kwargs) -> FactorData:
    """
    Factory function to create validated FactorData.
    
    Args:
        analyzer_name: Name of analyzer generating factor
        factor_name: Descriptive name of factor
        direction: Bullish/Bearish/Neutral bias
        strength: Factor strength (0.0-1.0)
        timeframe: Analysis timeframe
        **kwargs: Additional optional parameters
        
    Returns:
        FactorData: Validated factor object
    """
    return FactorData(
        factor_name=factor_name,
        analyzer_name=analyzer_name,
        timeframe=timeframe,
        timestamp=kwargs.get('timestamp', datetime.now()),
        direction_bias=direction,
        strength_score=strength,
        confidence_score=kwargs.get('confidence_score', strength),
        data_quality_score=kwargs.get('data_quality_score', 1.0),
        significance_level=kwargs.get('significance_level', 0.95),
        raw_value=kwargs.get('raw_value'),
        metadata=kwargs.get('metadata')
    )

def create_signal(ticker: str, direction: SignalDirection, strength: SignalStrength,
                 confidence_range: tuple, timing_estimate: tuple, 
                 supporting_factors: List[str], **kwargs) -> StrategySignal:
    """
    Factory function to create validated StrategySignal.
    
    Args:
        ticker: Asset ticker symbol
        direction: Long/Short/Neutral
        strength: Weak/Moderate/Strong
        confidence_range: (min, max) confidence percentages
        timing_estimate: (min, max) days estimate
        supporting_factors: List of supporting factor descriptions
        **kwargs: Additional optional parameters
        
    Returns:
        StrategySignal: Validated signal object
    """
    return StrategySignal(
        ticker=ticker,
        direction=direction,
        strength=strength,
        timestamp=kwargs.get('timestamp', datetime.now()),
        confidence_range=confidence_range,
        timing_estimate=timing_estimate,
        supporting_factors=supporting_factors,
        factor_count=len(supporting_factors),
        agreement_score=kwargs.get('agreement_score', 0.75),
        entry_price=kwargs.get('entry_price'),
        analysis_summary=kwargs.get('analysis_summary'),
        metadata=kwargs.get('metadata')
    )
