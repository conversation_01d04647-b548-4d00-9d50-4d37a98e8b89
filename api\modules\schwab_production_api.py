#!/usr/bin/env python3
"""
Schwab Production API Module
Direct interface to Schwab API for real-time data access
Mathematical rigor: 100% error handling, zero-tolerance failure
"""

import json
import logging
import time
import requests
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
import os
from pathlib import Path

logger = logging.getLogger(__name__)

class SchwabAPIError(Exception):

    def get_default_ticker(self) -> str:
        """Get default ticker from environment or config"""
        return os.getenv('DEFAULT_TICKER', 'SPY')
        
    def get_active_tickers(self) -> List[str]:
        """Get list of active tickers from config"""
        config_tickers = os.getenv('ACTIVE_TICKERS', 'SPY,QQQ,AAPL').split(',')
        return [t.strip() for t in config_tickers if t.strip()]
        
    def validate_ticker(self, ticker: str) -> bool:
        """Validate ticker format and availability"""
        if not ticker or len(ticker) < 1 or len(ticker) > 5:
            return False
        return ticker.upper().isalpha()
        
    def get_data_path(self, ticker: str, suffix: str = '') -> str:
        """Generate standardized data path for ticker"""
        base_path = os.getenv('DATA_PATH', 'data')
        return f"{base_path}/{ticker}_{suffix}" if suffix else f"{base_path}/{ticker}"
    """Custom exception for Schwab API errors"""
    def __init__(self, message: str, error_code: str = None, status_code: int = None):
        super().__init__(message)
        self.error_code = error_code
        self.status_code = status_code

class SchwabAPI:
    """
    Production-grade Schwab API client
    Handles authentication, rate limiting, and error recovery
    """
    
    def __init__(self, token_path: str = None):
        """Initialize Schwab API client"""
        self.base_url = "https://api.schwabapi.com"
        self.token_path = token_path or self._get_default_token_path()
        self.session = requests.Session()
        self._setup_session()
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
        # Circuit breaker
        self.consecutive_failures = 0
        self.max_failures = 5
        self.circuit_open_until = 0
        
    def _get_default_token_path(self) -> str:
        """Get default token path from configuration"""
        config_path = Path("D:/script-work/CORE/config/schwab_config.json")
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get("token_path", "D:/python projects/schwab_token.json")
            except Exception as e:
                logger.warning(f"Could not read config file: {e}")
        
        return "D:/python projects/schwab_token.json"
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration with environment variable support"""
        config_path = Path("D:/script-work/CORE/config/schwab_config.json")
        if not config_path.exists():
            return {}
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Handle environment variable references
            for key, value in config.items():
                if isinstance(value, str) and value.startswith("ENV:"):
                    env_var = value[4:]  # Remove "ENV:" prefix
                    env_value = os.getenv(env_var)
                    if env_value:
                        config[key] = env_value
                    else:
                        logger.warning(f"Environment variable {env_var} not found")
            
            return config
            
        except Exception as e:
            logger.warning(f"Could not load config: {e}")
            return {}
    
    def _setup_session(self, ticker: str = None):
        """Setup requests session with proper headers"""
        self.session.headers.update({
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def _load_token(self) -> Dict[str, Any]:
        """Load and validate authentication token"""
        try:
            if not os.path.exists(self.token_path):
                raise SchwabAPIError(f"Token file not found: {self.token_path}")
            
            with open(self.token_path, 'r') as f:
                token_data = json.load(f)
            
            # Validate required fields
            required_fields = ['access_token', 'expires_at']
            for field in required_fields:
                if field not in token_data:
                    raise SchwabAPIError(f"Missing required field in token: {field}")
            
            # Check if token is expired
            current_time = time.time()
            if current_time >= token_data['expires_at']:
                raise SchwabAPIError("Access token has expired")
            
            return token_data
            
        except json.JSONDecodeError as e:
            raise SchwabAPIError(f"Invalid token file format: {e}")
        except Exception as e:
            raise SchwabAPIError(f"Failed to load token: {e}")
    
    def _rate_limit(self, ticker: str = None):
        """Enforce rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _check_circuit_breaker(self, ticker: str = None):
        """Check if circuit breaker is open"""
        if self.consecutive_failures >= self.max_failures:
            if time.time() < self.circuit_open_until:
                raise SchwabAPIError("Circuit breaker open - too many consecutive failures")
            else:
                # Reset circuit breaker
                self.consecutive_failures = 0
                logger.info("Circuit breaker reset")
    
    def _make_request(self, method: str, endpoint: str, params: Dict = None, 
                     data: Dict = None) -> Dict[str, Any]:
        """Make authenticated request to Schwab API"""
        self._check_circuit_breaker()
        self._rate_limit()
        
        try:
            # Load fresh token for each request
            token_data = self._load_token()
            
            # Set authorization header with required UUID correlation ID
            headers = {
                'Authorization': f'Bearer {token_data["access_token"]}',
                'Schwab-Client-CorrelId': str(uuid.uuid4()),
                'Accept': 'application/json'
            }
            
            url = f"{self.base_url}{endpoint}"
            
            # Make request
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                params=params,
                json=data,
                timeout=30
            )
            
            # Handle HTTP errors
            if response.status_code == 401:
                raise SchwabAPIError("Authentication failed - token may be expired", 
                                   status_code=401)
            elif response.status_code == 429:
                raise SchwabAPIError("Rate limit exceeded", status_code=429)
            elif response.status_code >= 400:
                try:
                    error_data = response.json()
                    error_msg = error_data.get('message', f'HTTP {response.status_code}')
                except:
                    error_msg = f'HTTP {response.status_code}: {response.text}'
                
                raise SchwabAPIError(error_msg, status_code=response.status_code)
            
            # Parse response
            try:
                result = response.json()
            except json.JSONDecodeError:
                raise SchwabAPIError("Invalid JSON response from API")
            
            # Reset failure counter on success
            self.consecutive_failures = 0
            
            return result
            
        except SchwabAPIError:
            # Count API errors as failures
            self.consecutive_failures += 1
            if self.consecutive_failures >= self.max_failures:
                self.circuit_open_until = time.time() + 300  # 5 minute cooldown
                logger.error("Circuit breaker opened due to consecutive failures")
            raise
        except Exception as e:
            # Count any exception as failure
            self.consecutive_failures += 1
            if self.consecutive_failures >= self.max_failures:
                self.circuit_open_until = time.time() + 300
                logger.error("Circuit breaker opened due to consecutive failures")
            raise SchwabAPIError(f"Request failed: {e}")
    
    def get_price_history(self, symbol: str, period_type: str = "day", 
                         period: int = 2, frequency_type: str = "minute", 
                         frequency: int = 1, need_extended_hours_data: bool = True) -> List[Dict]:
        """
        Get price history for a symbol
        
        Args:
            symbol: Stock symbol (e.g., 'AAPL')
            period_type: 'day', 'month', 'year', 'ytd'
            period: Number of periods
            frequency_type: 'minute', 'daily', 'weekly', 'monthly'
            frequency: Frequency interval
            need_extended_hours_data: Include extended hours data
            
        Returns:
            List of candle data dictionaries
        """
        endpoint = f"/marketdata/v1/pricehistory"
        
        params = {
            'symbol': symbol.upper(),
            'periodType': period_type,
            'period': period,
            'frequencyType': frequency_type,
            'frequency': frequency,
            'needExtendedHoursData': need_extended_hours_data
        }
        
        try:
            logger.info(f"Fetching price history for {symbol} with params: {params}")
            response = self._make_request('GET', endpoint, params=params)
            
            # Extract candles from response
            candles = response.get('candles', [])
            
            # Convert to standardized format
            result = []
            for candle in candles:
                result.append({
                    'datetime': candle.get('datetime'),
                    'open': candle.get('open'),
                    'high': candle.get('high'),
                    'low': candle.get('low'),
                    'close': candle.get('close'),
                    'volume': candle.get('volume')
                })
            
            logger.info(f"Retrieved {len(result)} candles for {symbol}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to get price history for {symbol}: {e}")
            raise
    
    def get_quotes(self, symbols: Union[str, List[str]]) -> Dict[str, Dict]:
        """
        Get current quotes for one or more symbols
        
        Args:
            symbols: Single symbol or list of symbols
            
        Returns:
            Dictionary mapping symbols to quote data
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        symbols_param = ','.join(symbol.upper() for symbol in symbols)
        endpoint = f"/marketdata/v1/quotes"
        
        params = {'symbols': symbols_param}
        
        try:
            logger.info(f"Fetching quotes for: {symbols_param}")
            response = self._make_request('GET', endpoint, params=params)
            
            logger.info(f"Retrieved quotes for {len(response)} symbols")
            return response
            
        except Exception as e:
            logger.error(f"Failed to get quotes for {symbols_param}: {e}")
            raise
    
    def get_option_chains(self, symbol: str, contract_type: str = "ALL", 
                         strike_count: int = 10, include_quotes: bool = True) -> Dict[str, Any]:
        """
        Get option chains for a symbol (legacy method for backward compatibility)
        
        Args:
            symbol: Underlying symbol
            contract_type: 'CALL', 'PUT', or 'ALL'
            strike_count: Number of strikes to return
            include_quotes: Include quote data
            
        Returns:
            Option chain data
        """
        endpoint = f"/marketdata/v1/chains"
        
        params = {
            'symbol': symbol.upper(),
            'contractType': contract_type,
            'strikeCount': strike_count,
            'includeQuotes': include_quotes
        }
        
        try:
            logger.info(f"Fetching option chains for {symbol}")
            response = self._make_request('GET', endpoint, params=params)
            
            logger.info(f"Retrieved option chains for {symbol}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to get option chains for {symbol}: {e}")
            raise
    
    def option_chain(self, symbol: str, strikes: int = 10, days: int = 15) -> Dict[str, Any]:
        """
        Enhanced option chain fetcher with Greeks support
        Drop-in replacement for options data with better performance
        
        Args:
            symbol: Underlying symbol (e.g., 'AAPL')
            strikes: Number of strikes around ATM (default: 10)
            days: Days to expiration filter (default: 15)
            
        Returns:
            Full option chain data with Greeks, IV, and pricing
        """
        # Use trader API endpoint for enhanced data
        endpoint = f"/trader/v1/markets/options/chains/{symbol.upper()}"
        
        params = {
            'range': strikes,
            'fromDate': '',  # Use default date range
            'toDate': ''     # Use default date range
        }
        
        try:
            logger.info(f"Fetching enhanced option chain for {symbol} (strikes={strikes}, days={days})")
            
            # Make request with correlation ID
            response = self._make_request('GET', endpoint, params=params)
            
            # Validate response structure
            if not response or 'status' in response:
                if response.get('status') == 'FAILED':
                    raise SchwabAPIError(f"Option chain request failed: {response.get('message', 'Unknown error')}")
            
            logger.info(f"Retrieved enhanced option chain for {symbol}")
            return response
            
        except Exception as e:
            logger.error(f"Failed to get enhanced option chain for {symbol}: {e}")
            raise
    
    def get_account_info(self, account_number: str = None) -> Dict[str, Any]:
        """
        Get account information
        
        Args:
            account_number: Specific account number (optional)
            
        Returns:
            Account information
        """
        if account_number:
            endpoint = f"/trader/v1/accounts/{account_number}"
        else:
            endpoint = "/trader/v1/accounts"
        
        try:
            logger.info("Fetching account information")
            response = self._make_request('GET', endpoint)
            
            logger.info("Retrieved account information")
            return response
            
        except Exception as e:
            logger.error(f"Failed to get account info: {e}")
            raise
    
    def health_check(self) -> bool:
        """
        Check if API is accessible and authenticated
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            # Try to get account info as health check
            self.get_account_info()
            return True
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False

# Convenience functions for backward compatibility
def get_price_history(*args, **kwargs):
    """Convenience function for price history"""
    api = SchwabAPI()
    return api.get_price_history(*args, **kwargs)

def get_quotes(*args, **kwargs):
    """Convenience function for quotes"""
    api = SchwabAPI()
    return api.get_quotes(*args, **kwargs)

def get_option_chains(*args, **kwargs):
    """Convenience function for option chains"""
    api = SchwabAPI()
    return api.get_option_chains(*args, **kwargs)

if __name__ == "__main__":
    # Test the API
    try:
        api = SchwabAPI()
        
        # Health check
        if api.health_check():
            print(" Schwab API is healthy")
            
            # Test price history
            print("Testing price history...")
            history = api.get_price_history("AAPL", period=1, frequency=5)
            print(f"Retrieved {len(history)} candles for AAPL")
            
        else:
            print(" Schwab API health check failed")
            
    except Exception as e:
        print(f" Error testing Schwab API: {e}")
