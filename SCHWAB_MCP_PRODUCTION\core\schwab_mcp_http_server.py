#!/usr/bin/env python3
"""
Schwab MCP HTTP Server - Agent-Accessible Implementation
Proper MCP JSON-RPC over HTTP with historical data exposure
"""

import asyncio
import json
import logging
import sys
import time
from typing import Dict, List, Optional, Any
import signal
import os
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import urllib.parse

# Get absolute paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
LOG_DIR = os.path.join(BASE_DIR, "logs")
CONFIG_DIR = os.path.join(BASE_DIR, "config")

os.makedirs(LOG_DIR, exist_ok=True)
os.makedirs(CONFIG_DIR, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(LOG_DIR, 'schwab_mcp_server.log')),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Import production API client
sys.path.append(os.path.join(BASE_DIR, "core"))
from schwab_production_api import SchwabProductionClient, SchwabAPIError

class SchwabMCPHandler(BaseHTTPRequestHandler):
    """HTTP request handler for MCP JSON-RPC protocol"""
    
    def __init__(self, *args, schwab_client=None, **kwargs):
        self.schwab_client = schwab_client or SchwabProductionClient()
        super().__init__(*args, **kwargs)
    
    def do_POST(self):
        """Handle MCP JSON-RPC requests"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self._send_error(400, "No content")
                return
                
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))
            
            logger.info(f"MCP Request: {request_data}")
            
            # Handle MCP methods
            method = request_data.get('method', '')
            params = request_data.get('params', {})
            request_id = request_data.get('id', 1)
            
            if method == "initialize":
                response = self._handle_initialize(params, request_id)
            elif method == "tools/list":
                response = self._handle_tools_list(request_id)
            elif method == "tools/call":
                response = self._handle_tool_call(params, request_id)
            else:
                response = self._create_error_response(request_id, -32601, f"Method not found: {method}")
            
            self._send_json_response(response)
            
        except json.JSONDecodeError as e:
            self._send_error(400, f"Invalid JSON: {e}")
        except Exception as e:
            logger.error(f"Request handling error: {e}")
            self._send_error(500, f"Internal error: {e}")
    
    def do_GET(self):
        """Handle GET requests for basic info"""
        if self.path == "/":
            self._send_json_response({
                "name": "Schwab MCP Server",
                "version": "1.0.0", 
                "protocol": "MCP JSON-RPC",
                "historical_data": "Available",
                "status": "Operational"
            })
        else:
            self._send_error(404, "Not found")
    
    def _handle_initialize(self, params: Dict, request_id: int) -> Dict:
        """Handle MCP initialize request"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "serverInfo": {
                    "name": "schwab-mcp-server",
                    "version": "1.0.0"
                }
            }
        }
    
    def _handle_tools_list(self, request_id: int) -> Dict:
        """Handle tools/list request - expose historical data tools"""
        tools = [
            {
                "name": "get_price_history",
                "description": "Get historical OHLCV price data with mathematical precision",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "Stock ticker"},
                        "period_type": {"type": "string", "enum": ["day", "month", "year", "ytd"], "default": "year"},
                        "period": {"type": "integer", "default": 1, "description": "Number of periods"},
                        "frequency_type": {"type": "string", "enum": ["minute", "daily", "weekly", "monthly"], "default": "daily"},
                        "frequency": {"type": "integer", "default": 1, "description": "Frequency interval"}
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "get_real_time_quote", 
                "description": "Get real-time quote with bid/ask/volume",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "Stock ticker"}
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "get_multiple_quotes",
                "description": "Get quotes for multiple symbols efficiently", 
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "symbols": {"type": "array", "items": {"type": "string"}, "description": "Stock tickers"}
                    },
                    "required": ["symbols"]
                }
            },
            {
                "name": "get_option_chain",
                "description": "Get options chain data for symbol with strikes and Greeks",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "symbol": {"type": "string", "description": "Underlying stock ticker"},
                        "contract_type": {"type": "string", "enum": ["CALL", "PUT", "ALL"], "default": "ALL"},
                        "strike_count": {"type": "integer", "default": 10, "description": "Number of strikes to return"},
                        "include_quotes": {"type": "boolean", "default": True, "description": "Include quote data"}
                    },
                    "required": ["symbol"]
                }
            },
            {
                "name": "health_check",
                "description": "System health and performance metrics",
                "inputSchema": {"type": "object", "properties": {}}
            }
        ]
        
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": {"tools": tools}
        }
    
    def _handle_tool_call(self, params: Dict, request_id: int) -> Dict:
        """Handle tools/call request - execute actual API calls"""
        try:
            tool_name = params.get("name", "")
            arguments = params.get("arguments", {})
            
            logger.info(f"Executing tool: {tool_name} with args: {arguments}")
            
            if tool_name == "get_price_history":
                result = self._get_price_history(arguments)
            elif tool_name == "get_real_time_quote":
                result = self._get_real_time_quote(arguments)
            elif tool_name == "get_multiple_quotes":
                result = self._get_multiple_quotes(arguments)
            elif tool_name == "get_option_chain":
                result = self._get_option_chain(arguments)
            elif tool_name == "health_check":
                result = self._health_check()
            else:
                return self._create_error_response(request_id, -32602, f"Unknown tool: {tool_name}")
            
            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(result, indent=2)
                        }
                    ]
                }
            }
            
        except Exception as e:
            logger.error(f"Tool execution error: {e}")
            return self._create_error_response(request_id, -32603, f"Tool execution failed: {e}")
    
    def _get_price_history(self, args: Dict) -> Dict:
        """Execute price history API call with corrected parameters"""
        symbol = args["symbol"].upper()
        period_type = args.get("period_type", "year")
        period = args.get("period", 1)
        frequency_type = args.get("frequency_type", "daily")
        frequency = args.get("frequency", 1)
        
        # Validate Schwab API parameter combinations
        if period_type == "day" and frequency_type not in ["minute"]:
            frequency_type = "minute"
            frequency = 1
        elif period_type == "month" and frequency_type not in ["daily", "weekly"]:
            frequency_type = "daily"
            frequency = 1
        elif period_type == "year" and frequency_type not in ["daily", "weekly", "monthly"]:
            frequency_type = "daily"
            frequency = 1
        elif period_type == "ytd" and frequency_type not in ["daily", "weekly"]:
            frequency_type = "daily"
            frequency = 1
        
        candles = self.schwab_client.api.get_price_history(
            symbol=symbol,
            period_type=period_type,
            period=period,
            frequency_type=frequency_type,
            frequency=frequency
        )
        
        return {
            "symbol": symbol,
            "period_type": period_type,
            "period": period,
            "frequency_type": frequency_type,
            "frequency": frequency,
            "candles": candles,
            "count": len(candles),
            "status": "success",
            "timestamp": time.time()
        }
    
    def _get_real_time_quote(self, args: Dict) -> Dict:
        """Execute real-time quote API call"""
        symbol = args["symbol"].upper()
        return self.schwab_client.get_real_time_quote(symbol)
    
    def _get_multiple_quotes(self, args: Dict) -> Dict:
        """Execute multiple quotes API call"""
        symbols = [s.upper() for s in args["symbols"]]
        quotes = self.schwab_client.api.get_multiple_quotes(symbols)
        
        # Convert Quote objects to dicts
        result = {}
        for symbol, quote in quotes.items():
            result[symbol] = {
                "symbol": quote.symbol,
                "price": quote.price,
                "bid": quote.bid,
                "ask": quote.ask,
                "volume": quote.volume,
                "change": quote.change,
                "change_percent": quote.change_percent,
                "timestamp": quote.timestamp
            }
        
        return {"quotes": result, "count": len(result)}
    
    def _health_check(self) -> Dict:
        """Execute health check"""
        return self.schwab_client.health_check()
    
    def _get_option_chain(self, args: Dict) -> Dict:
        """Execute option chain API call"""
        symbol = args["symbol"].upper()
        contract_type = args.get("contract_type", "ALL")
        strike_count = args.get("strike_count", 10)
        include_quotes = args.get("include_quotes", True)
        
        try:
            option_data = self.schwab_client.api.get_option_chains(
                symbol=symbol,
                contract_type=contract_type,
                strike_count=strike_count,
                include_quotes=include_quotes
            )
            
            return {
                "symbol": symbol,
                "contract_type": contract_type,
                "strike_count": strike_count,
                "option_data": option_data,
                "status": "success",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Option chain error for {symbol}: {e}")
            raise
    
    def _create_error_response(self, request_id: int, code: int, message: str) -> Dict:
        """Create JSON-RPC error response"""
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": code,
                "message": message
            }
        }
    
    def _send_json_response(self, data: Dict):
        """Send JSON response"""
        response = json.dumps(data).encode('utf-8')
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(response)
    
    def _send_error(self, code: int, message: str):
        """Send HTTP error response"""
        self.send_response(code)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(message.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")

def create_handler_class(schwab_client):
    """Create handler class with schwab client"""
    class Handler(SchwabMCPHandler):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, schwab_client=schwab_client, **kwargs)
    return Handler

def main():
    """Main MCP HTTP server entry point"""
    HOST = "localhost"
    PORT = 8005
    
    # Initialize Schwab client
    schwab_client = SchwabProductionClient()
    
    # Create HTTP server
    handler_class = create_handler_class(schwab_client)
    server = HTTPServer((HOST, PORT), handler_class)
    
    logger.info("="*60)
    logger.info("SCHWAB MCP HTTP SERVER - AGENT ACCESSIBLE")
    logger.info("="*60)
    logger.info(f"Server running on http://{HOST}:{PORT}")
    logger.info("MCP Protocol: JSON-RPC 2.0 over HTTP")
    logger.info("Historical Data: FULLY EXPOSED TO AGENTS")
    logger.info("Available Tools:")
    logger.info("  - get_price_history: Historical OHLCV data")
    logger.info("  - get_real_time_quote: Real-time quotes")
    logger.info("  - get_multiple_quotes: Batch quotes")
    logger.info("  - get_option_chain: Options chains with Greeks")
    logger.info("  - health_check: System status")
    logger.info("="*60)
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server shutdown requested")
        server.shutdown()
        server.server_close()

if __name__ == "__main__":
    main()
