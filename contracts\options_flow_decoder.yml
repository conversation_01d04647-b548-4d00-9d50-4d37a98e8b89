task_id: OPTIONS_FLOW_DECODER
name: Options Flow Decoder Agent
version: 1.0.0
description: Specialized agent for decoding institutional options activity and gamma positioning
            to determine directional bias and identify key price levels.

inputs:
  - options_chain: Dict           # Complete options chain data
  - underlying_price: float       # Current underlying asset price
  - iv_data: Dict                 # Implied volatility data
  - volume_data: Dict             # Options volume data
  - symbol: str                   # Ticker symbol
  - expiration_dates: List        # Available expiration dates

outputs:
  data:
    type: Dict[str, Any]
    schema: schemas/options_flow_decoder_output_v1.json
    
success_criteria:
  perf_budget:
    max_runtime_ms: 5000
  precision_threshold: 0.001
  code_coverage_min: 0.90
  output_completeness: 1.00
  flow_accuracy_target: 0.75
  dependency_constraints:
    max_additional_deps: 2
    forbidden_packages:
      - tensorflow
      - torch
      - opencv-python
    allowed_packages:
      - scikit-learn  # ML operations
      - scipy         # Statistical calculations

mathematical_requirements:
  - numerical_stability: true
  - precision_validation: required
  - statistical_rigor: enforced
  - gamma_calculation_precision: 1e-6
  - delta_calculation_precision: 1e-4

specialization:
  focus: "THE ONE THING - Institutional Options Flow and Gamma Positioning"
  methodology: "Volume Analysis + Gamma Exposure + Delta Positioning + Pattern Recognition"
  expertise: "Decoding institutional sentiment from options activity"
  
features:
  unusual_activity_detection:
    - volume_oi_ratio_analysis
    - large_trade_identification
    - dollar_volume_thresholds
    - moneyness_analysis
  call_put_analysis:
    - volume_weighted_ratios
    - strike_distribution_analysis
    - moneyness_weighting
    - directional_bias_calculation
  gamma_exposure_calculation:
    - net_gamma_exposure
    - gamma_by_strike
    - gamma_flip_point_identification
    - market_maker_positioning
  delta_positioning:
    - net_delta_exposure
    - expiration_analysis
    - hedging_flow_detection
    - institutional_positioning
  institutional_patterns:
    - otm_option_activity
    - volatility_skew_analysis
    - calendar_spread_detection
    - defensive_hedging_patterns
  volatility_flow:
    - iv_skew_analysis
    - volatility_regime_detection
    - iv_change_monitoring

analysis_thresholds:
  unusual_volume_threshold: 3.0          # 3x normal volume
  large_trade_threshold: 10000           # $10k minimum trade size
  gamma_exposure_threshold: 1000000      # $1M gamma exposure
  significant_skew_threshold: 0.05       # 5% IV skew
  
key_calculations:
  gamma_estimation: "Simplified Black-Scholes approximation"
  delta_estimation: "Moneyness-based delta curves"
  time_decay: "Continuous time-to-expiry calculation"
  volatility_impact: "IV surface analysis"

data_sources:
  primary: schwab_mcp
  required_fields:
    - options_chain_data
    - volume_data
    - open_interest
    - implied_volatility
    - bid_ask_spreads
    - timestamp
  
schwab_integration:
  mcp_server: "api/schwab_mcp_server.py"
  options_parser: "api/modules/schwab_option_parser.py"
  real_time: true
  chain_refresh_rate: "1_minute"
  
validation_framework:
  input_validation: comprehensive
  output_validation: gamma_delta_bounds_check
  precision_monitoring: continuous
  performance_tracking: enabled
  flow_accuracy_monitoring: enabled

gamma_levels_identification:
  support_levels: "Positive gamma concentration points"
  resistance_levels: "Negative gamma concentration points"
  flip_point: "Net zero gamma exposure level"
  
directional_bias_factors:
  call_put_ratio_weight: 0.25
  gamma_positioning_weight: 0.20
  delta_positioning_weight: 0.20
  institutional_patterns_weight: 0.20
  unusual_activity_weight: 0.15
