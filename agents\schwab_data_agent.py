#!/usr/bin/env python3
"""
Schwab Data Agent - Direct API Integration
Primary data source for Schwab broker integration
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional

# Add production path
sys.path.append("D:/script-work/SCHWAB_MCP_PRODUCTION/core")
from schwab_production_api import SchwabAPI

from .agent_base import BaseAgent

class SchwabDataAgent(BaseAgent):
    """Direct Schwab API data agent"""
    
    task_id = "SD-01"
    
    def __init__(self, agent_id="schwab_data_agent"):
        super().__init__(agent_id)
        self.logger = logging.getLogger(agent_id)
        
        try:
            self.api_client = SchwabAPI()
            self.operational = True
            self.logger.info("Schwab Data Agent initialized successfully")
        except Exception as e:
            self.operational = False
            self.logger.error(f"Failed to initialize Schwab API: {e}")
    
    def execute_task(self, task):
        """Execute data retrieval task"""
        ticker_list = task.inputs.get("ticker_list", [])
        data_type = task.inputs.get("data_type", "quotes")
        
        return self.execute(ticker_list, data_type=data_type)
    
    def execute(self, ticker_list: List[str], data_type: str = "quotes") -> Dict[str, Any]:
        """Execute data retrieval"""
        if not self.operational:
            return {
                "status": "FAILED",
                "error": "Schwab API not operational",
                "data": {}
            }
        
        try:
            results = {}
            
            if data_type == "quotes":
                for ticker in ticker_list:
                    quote = self.api_client.get_quote(ticker)
                    results[ticker] = {
                        "price": quote.price,
                        "bid": quote.bid,
                        "ask": quote.ask,
                        "volume": quote.volume,
                        "change": quote.change,
                        "change_percent": quote.change_percent
                    }
            
            return {
                "status": "OK",
                "data": results,
                "source": "schwab_direct_api",
                "timestamp": self.get_timestamp()
            }
            
        except Exception as e:
            self.logger.error(f"Data retrieval failed: {e}")
            return {
                "status": "FAILED",
                "error": str(e),
                "data": {}
            }
