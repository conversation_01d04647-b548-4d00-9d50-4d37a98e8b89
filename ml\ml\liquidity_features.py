"""
Liquidity-Specific Feature Engineering Module

This module provides specialized feature extraction for liquidity analysis,
focusing on features relevant to market liquidity, order flow, and price impact.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
import logging
import os
import importlib.util

# Internal imports
from ml_logging import get_logger

# Setup logger
logger = get_logger('liquidity_features')

# Try to import order book features
try:
    # Check if the order book features module exists
    features_dir = os.path.join(os.path.dirname(__file__), 'features')
    order_book_path = os.path.join(features_dir, 'order_book_features.py')

    if os.path.exists(order_book_path):
        # Import the module dynamically
        spec = importlib.util.spec_from_file_location("order_book_features", order_book_path)
        order_book_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(order_book_module)
        OrderBookFeatureExtractor = order_book_module.OrderBookFeatureExtractor
        HAS_ORDER_BOOK_FEATURES = True
    else:
        HAS_ORDER_BOOK_FEATURES = False
except Exception as e:
    logger.warning(f"Could not import order book features: {str(e)}")
    HAS_ORDER_BOOK_FEATURES = False

class LiquidityFeatureExtractor:
    """Extracts liquidity-specific features for ML models."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the liquidity feature extractor.

        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}

        # Initialize order book feature extractor if available
        self.order_book_extractor = None
        if HAS_ORDER_BOOK_FEATURES:
            try:
                self.order_book_extractor = OrderBookFeatureExtractor(config)
                logger.info("Order book feature extractor initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize order book feature extractor: {str(e)}")

        logger.info("Initialized LiquidityFeatureExtractor")

    def extract_features(self,
                        price_data: pd.DataFrame,
                        options_data: Optional[pd.DataFrame] = None,
                        volume_profile: Optional[Dict[str, Any]] = None,
                        gex_data: Optional[Dict[str, Any]] = None,
                        liquidity_levels: Optional[Dict[str, List[Dict[str, Any]]]] = None,
                        order_book_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Extract liquidity-specific features from various data sources.

        Args:
            price_data: DataFrame with OHLCV price data
            options_data: Optional DataFrame with options chain data
            volume_profile: Optional volume profile data
            gex_data: Optional gamma exposure data
            liquidity_levels: Optional dictionary with liquidity levels
            order_book_data: Optional DataFrame with order book data
                Expected columns: timestamp, price, bid_size, ask_size

        Returns:
            DataFrame with liquidity features
        """
        if price_data.empty:
            logger.error("Empty price data provided")
            return pd.DataFrame()

        logger.info("Extracting liquidity features")

        # Initialize result DataFrame
        result = pd.DataFrame(index=price_data.index)



        # Extract basic price-volume liquidity features
        result = pd.concat([result, self._extract_price_volume_features(price_data)], axis=1)

        # Extract options-based liquidity features if options data is available
        if options_data is not None and not options_data.empty:
            options_features = self._extract_options_liquidity_features(price_data, options_data)
            result = pd.concat([result, options_features], axis=1)

        # Extract volume profile features if available
        if volume_profile is not None:
            vp_features = self._extract_volume_profile_features(price_data, volume_profile)
            result = pd.concat([result, vp_features], axis=1)

        # Extract GEX-based liquidity features if available
        if gex_data is not None:
            gex_features = self._extract_gex_features(price_data, gex_data)
            result = pd.concat([result, gex_features], axis=1)

        # Extract features from liquidity levels if available
        if liquidity_levels is not None:
            level_features = self._extract_liquidity_level_features(price_data, liquidity_levels)
            result = pd.concat([result, level_features], axis=1)

        # Extract order book features if available
        if order_book_data is not None and not order_book_data.empty and self.order_book_extractor is not None:
            try:
                # Get current price
                current_price = price_data['close'].iloc[-1]

                # Extract order book features
                order_book_features = self.order_book_extractor.extract_features(
                    order_book_data=order_book_data,
                    price_data=price_data,
                    current_price=current_price
                )

                # Add to result
                if not order_book_features.empty:
                    # Broadcast to match the index of result
                    for col in order_book_features.columns:
                        result[col] = order_book_features.iloc[0][col]

                    logger.info(f"Added {len(order_book_features.columns)} order book features")
            except Exception as e:
                logger.error(f"Error extracting order book features: {str(e)}")



        logger.info(f"Extracted {result.shape[1]} liquidity features")
        return result

    def _extract_price_volume_features(self, price_data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract basic price-volume based liquidity features.

        Args:
            price_data: DataFrame with OHLCV price data

        Returns:
            DataFrame with price-volume liquidity features
        """
        result = pd.DataFrame(index=price_data.index)

        # Ensure we have volume data
        if 'volume' not in price_data.columns:
            logger.warning("Volume data not available for liquidity feature extraction")
            return result

        # Volume-based features
        # 1. Relative volume (compared to moving average)
        for window in [5, 10, 20, 50]:
            if len(price_data) >= window:
                vol_ma = price_data['volume'].rolling(window=window).mean()
                result[f'rel_volume_{window}'] = price_data['volume'] / vol_ma

        # 2. Volume trend
        result['volume_change'] = price_data['volume'].pct_change()

        # 3. Volume acceleration (change in volume change)
        result['volume_acceleration'] = result['volume_change'].diff()

        # 4. Volume volatility
        for window in [5, 10, 20]:
            if len(price_data) >= window:
                result[f'volume_volatility_{window}'] = price_data['volume'].rolling(window=window).std() / price_data['volume'].rolling(window=window).mean()

        # 5. Price-volume correlation
        for window in [5, 10, 20]:
            if len(price_data) >= window:
                price_returns = price_data['close'].pct_change()
                result[f'price_volume_corr_{window}'] = price_returns.rolling(window=window).corr(price_data['volume'])

        # 6. Volume-weighted price metrics
        result['vwap_daily'] = (price_data['close'] * price_data['volume']).rolling(window=1).sum() / price_data['volume'].rolling(window=1).sum()

        # 7. Liquidity ratio (close-to-close price change per unit of volume)
        result['liquidity_ratio'] = price_data['close'].pct_change().abs() / (price_data['volume'] / price_data['volume'].rolling(window=20).mean())

        # 8. Amihud illiquidity measure (absolute return divided by volume)
        result['amihud_illiquidity'] = price_data['close'].pct_change().abs() / price_data['volume']

        # 9. Volume profile skew (using high-low range)
        result['volume_skew'] = (price_data['close'] - price_data['low']) / (price_data['high'] - price_data['low'])

        return result

    def _extract_options_liquidity_features(self, price_data: pd.DataFrame, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract liquidity features from options data.

        Args:
            price_data: DataFrame with OHLCV price data
            options_data: DataFrame with options chain data

        Returns:
            DataFrame with options-based liquidity features
        """
        result = pd.DataFrame(index=price_data.index)

        # Check if we have the necessary options data
        required_columns = ['strike_price', 'expiration_date', 'call_oi', 'put_oi']
        missing_columns = [col for col in required_columns if col not in options_data.columns]

        if missing_columns:
            logger.warning(f"Missing required options data columns: {missing_columns}")

            # Try to derive missing columns from available data
            if 'strike_price' not in options_data.columns and 'strike' in options_data.columns:
                logger.info("Using 'strike' column as 'strike_price'")
                options_data['strike_price'] = options_data['strike']
                missing_columns.remove('strike_price') if 'strike_price' in missing_columns else None

            if 'expiration_date' not in options_data.columns and 'expiration' in options_data.columns:
                logger.info("Using 'expiration' column as 'expiration_date'")
                options_data['expiration_date'] = options_data['expiration']
                missing_columns.remove('expiration_date') if 'expiration_date' in missing_columns else None

            # Try to derive call_oi and put_oi from type and open_interest columns
            if ('call_oi' not in options_data.columns or 'put_oi' not in options_data.columns) and 'open_interest' in options_data.columns and 'type' in options_data.columns:
                logger.info("Deriving call_oi and put_oi from type and open_interest columns")

                # Create call_oi and put_oi columns
                options_data['call_oi'] = 0
                options_data['put_oi'] = 0

                # Fill in values based on type
                call_mask = options_data['type'].str.lower() == 'call'
                put_mask = options_data['type'].str.lower() == 'put'

                options_data.loc[call_mask, 'call_oi'] = options_data.loc[call_mask, 'open_interest']
                options_data.loc[put_mask, 'put_oi'] = options_data.loc[put_mask, 'open_interest']

                missing_columns = [col for col in missing_columns if col not in ['call_oi', 'put_oi']]

            # If still missing required columns, return empty result
            if missing_columns:
                logger.warning(f"Still missing required options data columns after derivation: {missing_columns}")
                return result

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Filter options near current price (within 20%)
        price_range = 0.2
        relevant_options = options_data[
            (options_data['strike_price'] >= current_price * (1 - price_range)) &
            (options_data['strike_price'] <= current_price * (1 + price_range))
        ]

        if relevant_options.empty:
            logger.warning("No relevant options found near current price")
            return result

        # 1. Put/Call OI ratio
        total_call_oi = relevant_options['call_oi'].sum()
        total_put_oi = relevant_options['put_oi'].sum()

        if total_call_oi > 0:
            put_call_ratio = total_put_oi / total_call_oi
        else:
            put_call_ratio = np.nan

        # Add as constant feature for all rows
        result['options_put_call_ratio'] = put_call_ratio

        # 2. OI concentration
        # Calculate OI concentration at each strike
        strikes = relevant_options['strike_price'].unique()

        # Find strikes with highest OI
        call_oi_by_strike = relevant_options.groupby('strike_price')['call_oi'].sum()
        put_oi_by_strike = relevant_options.groupby('strike_price')['put_oi'].sum()

        # Top 3 strikes by OI
        top_call_strikes = call_oi_by_strike.nlargest(3).index.tolist()
        top_put_strikes = put_oi_by_strike.nlargest(3).index.tolist()

        # 3. Distance to highest OI strikes
        if top_call_strikes:
            nearest_call_strike = min(top_call_strikes, key=lambda x: abs(x - current_price))
            result['distance_to_call_oi'] = (nearest_call_strike - current_price) / current_price

        if top_put_strikes:
            nearest_put_strike = min(top_put_strikes, key=lambda x: abs(x - current_price))
            result['distance_to_put_oi'] = (nearest_put_strike - current_price) / current_price

        # 4. OI imbalance around current price
        # Find strikes just above and below current price
        strikes_above = relevant_options[relevant_options['strike_price'] > current_price]['strike_price'].unique()
        strikes_below = relevant_options[relevant_options['strike_price'] < current_price]['strike_price'].unique()

        # Initialize default values for required features
        result['call_oi_imbalance'] = 1.0  # Default to neutral
        result['put_oi_imbalance'] = 1.0   # Default to neutral

        if len(strikes_above) > 0 and len(strikes_below) > 0:
            nearest_above = min(strikes_above)
            nearest_below = max(strikes_below)

            call_oi_above = relevant_options[relevant_options['strike_price'] == nearest_above]['call_oi'].sum()
            put_oi_above = relevant_options[relevant_options['strike_price'] == nearest_above]['put_oi'].sum()

            call_oi_below = relevant_options[relevant_options['strike_price'] == nearest_below]['call_oi'].sum()
            put_oi_below = relevant_options[relevant_options['strike_price'] == nearest_below]['put_oi'].sum()

            # Calculate imbalance ratios
            if call_oi_below > 0:
                result['call_oi_imbalance'] = call_oi_above / call_oi_below

            if put_oi_above > 0:
                result['put_oi_imbalance'] = put_oi_below / put_oi_above

        # 5. OI liquidity score
        # Simple score based on total OI relative to average
        if 'average_oi' in self.config:
            average_oi = self.config['average_oi']
            total_oi = total_call_oi + total_put_oi
            result['options_liquidity_score'] = total_oi / average_oi

        # 6. IV skew features if available
        if 'call_iv' in options_data.columns and 'put_iv' in options_data.columns:
            # Calculate IV skew (put IV - call IV) for ATM options
            atm_options = relevant_options[
                (relevant_options['strike_price'] >= current_price * 0.95) &
                (relevant_options['strike_price'] <= current_price * 1.05)
            ]

            if not atm_options.empty:
                avg_call_iv = atm_options['call_iv'].mean()
                avg_put_iv = atm_options['put_iv'].mean()

                if not np.isnan(avg_call_iv) and not np.isnan(avg_put_iv):
                    result['iv_skew'] = avg_put_iv - avg_call_iv
                    result['iv_skew_ratio'] = avg_put_iv / avg_call_iv if avg_call_iv > 0 else np.nan

        return result

    def _extract_volume_profile_features(self, price_data: pd.DataFrame, volume_profile: Dict[str, Any]) -> pd.DataFrame:
        """
        Extract features from volume profile data.

        Args:
            price_data: DataFrame with OHLCV price data
            volume_profile: Dictionary with volume profile data

        Returns:
            DataFrame with volume profile features
        """
        result = pd.DataFrame(index=price_data.index)

        # Initialize default values for required volume profile features
        result['distance_to_poc'] = 0.0
        result['in_value_area'] = False
        result['distance_to_value_area'] = 0.0
        result['value_area_width'] = 0.1  # Default 10% of price
        result['nearby_volume_density'] = 0.0
        result['volume_profile_skew'] = 0.0
        result['volume_liquidity_score'] = 0.0
        result['distance_to_volume_gap'] = 0.0
        result['gap_direction'] = 0

        # Check if we have the necessary volume profile data
        if not volume_profile or 'nodes' not in volume_profile:
            logger.warning("Missing required volume profile data")
            return result

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Extract volume nodes
        nodes = volume_profile.get('nodes', [])
        if not nodes:
            logger.warning("No volume nodes found in volume profile data")
            return result

        # Convert nodes to DataFrame for easier processing
        nodes_df = pd.DataFrame(nodes)
        if 'price' not in nodes_df.columns or 'volume' not in nodes_df.columns:
            logger.warning("Volume nodes missing required columns")
            return result

        # 1. Distance to point of control (POC)
        if 'profile_metrics' in volume_profile and 'poc_price' in volume_profile['profile_metrics']:
            poc_price = volume_profile['profile_metrics']['poc_price']
            result['distance_to_poc'] = (current_price - poc_price) / current_price
        elif not nodes_df.empty:
            # Calculate POC as the price level with highest volume
            poc_price = nodes_df.loc[nodes_df['volume'].idxmax(), 'price']
            result['distance_to_poc'] = (current_price - poc_price) / current_price

        # 2. Value area metrics
        if 'profile_metrics' in volume_profile:
            metrics = volume_profile['profile_metrics']
            if 'value_area_high' in metrics and 'value_area_low' in metrics:
                va_high = metrics['value_area_high']
                va_low = metrics['value_area_low']

                # Check if price is inside value area
                result['in_value_area'] = (current_price >= va_low) & (current_price <= va_high)

                # Distance to value area boundaries
                if current_price > va_high:
                    result['distance_to_value_area'] = (current_price - va_high) / current_price
                elif current_price < va_low:
                    result['distance_to_value_area'] = (current_price - va_low) / current_price
                else:
                    result['distance_to_value_area'] = 0

                # Value area width as percentage of price
                result['value_area_width'] = (va_high - va_low) / current_price

        # 3. Volume node density
        # Calculate volume node density around current price
        if not nodes_df.empty:
            # Filter nodes near current price (within 5%)
            price_range = 0.05
            nearby_nodes = nodes_df[
                (nodes_df['price'] >= current_price * (1 - price_range)) &
                (nodes_df['price'] <= current_price * (1 + price_range))
            ]

            if not nearby_nodes.empty:
                # Calculate volume density
                total_nearby_volume = nearby_nodes['volume'].sum()
                total_volume = nodes_df['volume'].sum()

                if total_volume > 0:
                    result['nearby_volume_density'] = total_nearby_volume / total_volume

                # Calculate node count density
                result['nearby_node_count'] = len(nearby_nodes)
                result['nearby_node_density'] = len(nearby_nodes) / len(nodes_df)

        # 4. Volume voids/gaps
        if not nodes_df.empty:
            # Sort nodes by price
            sorted_nodes = nodes_df.sort_values('price')

            # Calculate gaps between adjacent nodes
            price_diffs = sorted_nodes['price'].diff()

            # Find significant gaps (e.g., 3x median gap)
            median_gap = price_diffs.median()
            if not np.isnan(median_gap) and median_gap > 0:
                significant_gaps = sorted_nodes[price_diffs > 3 * median_gap]

                if not significant_gaps.empty:
                    # Find nearest gap to current price
                    gap_prices = significant_gaps['price'].values
                    nearest_gap_idx = np.abs(gap_prices - current_price).argmin()
                    nearest_gap = gap_prices[nearest_gap_idx]

                    # Distance to nearest volume gap
                    result['distance_to_volume_gap'] = (current_price - nearest_gap) / current_price

                    # Direction to gap (above or below)
                    result['gap_direction'] = np.sign(current_price - nearest_gap)

        # 5. Volume profile shape features
        if not nodes_df.empty:
            # Calculate volume distribution skew
            lower_half = nodes_df[nodes_df['price'] < current_price]
            upper_half = nodes_df[nodes_df['price'] >= current_price]

            lower_volume = lower_half['volume'].sum() if not lower_half.empty else 0
            upper_volume = upper_half['volume'].sum() if not upper_half.empty else 0

            total_profile_volume = lower_volume + upper_volume

            if total_profile_volume > 0:
                # Volume skew (positive means more volume below current price)
                result['volume_profile_skew'] = (lower_volume - upper_volume) / total_profile_volume

        # 6. Volume profile liquidity score
        # Simple score based on nearby volume relative to total
        if not nodes_df.empty:
            # Calculate volume within 2% of current price
            close_range = 0.02
            very_close_nodes = nodes_df[
                (nodes_df['price'] >= current_price * (1 - close_range)) &
                (nodes_df['price'] <= current_price * (1 + close_range))
            ]

            very_close_volume = very_close_nodes['volume'].sum() if not very_close_nodes.empty else 0

            if total_profile_volume > 0:
                result['volume_liquidity_score'] = very_close_volume / total_profile_volume

        return result

    def _extract_gex_features(self, price_data: pd.DataFrame, gex_data: Dict[str, Any]) -> pd.DataFrame:
        """
        Extract features from gamma exposure data.

        Args:
            price_data: DataFrame with OHLCV price data
            gex_data: Dictionary with gamma exposure data

        Returns:
            DataFrame with GEX-based features
        """
        result = pd.DataFrame(index=price_data.index)

        # Check if we have the necessary GEX data
        if not gex_data:
            logger.warning("Missing GEX data")
            return result

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Initialize default values for required GEX features
        result['current_gex'] = 0.0
        result['normalized_gex'] = 0.0
        result['gex_impact'] = 0
        result['distance_to_negative_gamma'] = 0.0
        result['distance_to_positive_gamma'] = 0.0
        result['distance_to_zero_gamma'] = 0.0
        result['zero_gamma_direction'] = 0

        # 1. Current GEX value
        if 'gex_at_current' in gex_data:
            gex_at_current = gex_data['gex_at_current']
            result['current_gex'] = gex_at_current

            # Normalized GEX (relative to price)
            result['normalized_gex'] = gex_at_current / current_price

        # 2. GEX impact
        if 'impact' in gex_data:
            impact = gex_data['impact']
            if isinstance(impact, str):
                # Convert string impact to numeric
                if impact.lower() == 'positive':
                    result['gex_impact'] = 1
                elif impact.lower() == 'negative':
                    result['gex_impact'] = -1
                else:
                    result['gex_impact'] = 0
            else:
                # Assume numeric impact
                result['gex_impact'] = impact

        # 3. Distance to zero-gamma level
        if 'liquidity_levels' in gex_data:
            levels = gex_data['liquidity_levels']

            # Find zero-crossing points
            zero_crossings = [level for level in levels if level.get('type') == 'zero_gamma']

            if zero_crossings:
                # Find nearest zero-crossing
                nearest_zero = min(zero_crossings, key=lambda x: abs(x.get('price', 0) - current_price))
                zero_price = nearest_zero.get('price', current_price)

                # Calculate distance
                result['distance_to_zero_gamma'] = (current_price - zero_price) / current_price

                # Direction to zero-gamma (above or below)
                result['zero_gamma_direction'] = np.sign(current_price - zero_price)

        # 4. GEX profile features
        if 'gex_profile' in gex_data:
            profile = gex_data['gex_profile']

            if isinstance(profile, dict) and 'prices' in profile and 'values' in profile:
                prices = profile['prices']
                values = profile['values']

                if len(prices) > 0 and len(prices) == len(values):
                    # Convert to numpy arrays
                    prices = np.array(prices)
                    values = np.array(values)

                    # Find index of current price
                    current_idx = np.abs(prices - current_price).argmin()

                    # Calculate GEX slope around current price
                    if current_idx > 0 and current_idx < len(prices) - 1:
                        left_idx = current_idx - 1
                        right_idx = current_idx + 1

                        left_slope = (values[current_idx] - values[left_idx]) / (prices[current_idx] - prices[left_idx])
                        right_slope = (values[right_idx] - values[current_idx]) / (prices[right_idx] - prices[current_idx])

                        result['gex_left_slope'] = left_slope
                        result['gex_right_slope'] = right_slope

                        # GEX convexity (change in slope)
                        result['gex_convexity'] = right_slope - left_slope

                    # Calculate GEX skew (asymmetry around current price)
                    window = min(5, len(prices) // 4)  # Use up to 5 points or 1/4 of the profile

                    if current_idx >= window and current_idx + window < len(prices):
                        left_sum = np.sum(values[current_idx-window:current_idx])
                        right_sum = np.sum(values[current_idx:current_idx+window])

                        if left_sum + right_sum != 0:
                            result['gex_skew'] = (right_sum - left_sum) / (right_sum + left_sum)

        # 5. GEX accumulation zones
        if 'liquidity_levels' in gex_data:
            levels = gex_data['liquidity_levels']

            # Find accumulation zones
            positive_zones = [level for level in levels if level.get('type') == 'positive_gamma']
            negative_zones = [level for level in levels if level.get('type') == 'negative_gamma']

            # Distance to nearest positive gamma zone
            if positive_zones:
                nearest_positive = min(positive_zones, key=lambda x: abs(x.get('price', 0) - current_price))
                positive_price = nearest_positive.get('price', current_price)

                result['distance_to_positive_gamma'] = (current_price - positive_price) / current_price
                result['positive_gamma_strength'] = nearest_positive.get('strength', 0)

            # Distance to nearest negative gamma zone
            if negative_zones:
                nearest_negative = min(negative_zones, key=lambda x: abs(x.get('price', 0) - current_price))
                negative_price = nearest_negative.get('price', current_price)

                result['distance_to_negative_gamma'] = (current_price - negative_price) / current_price
                result['negative_gamma_strength'] = nearest_negative.get('strength', 0)

        # 6. Dealer positioning
        if 'dealer_positioning' in gex_data:
            positioning = gex_data['dealer_positioning']

            if isinstance(positioning, str):
                # Convert string positioning to numeric
                if positioning.lower() == 'long':
                    result['dealer_positioning'] = 1
                elif positioning.lower() == 'short':
                    result['dealer_positioning'] = -1
                else:
                    result['dealer_positioning'] = 0
            elif isinstance(positioning, (int, float)):
                result['dealer_positioning'] = positioning

        return result

    def _extract_liquidity_level_features(self, price_data: pd.DataFrame, liquidity_levels: Dict[str, List[Dict[str, Any]]]) -> pd.DataFrame:
        """
        Extract features from liquidity levels.

        Args:
            price_data: DataFrame with OHLCV price data
            liquidity_levels: Dictionary with liquidity levels

        Returns:
            DataFrame with liquidity level features
        """
        result = pd.DataFrame(index=price_data.index)

        # Initialize default values for required liquidity level features
        result['distance_to_support'] = -0.05  # Default 5% below current price
        result['distance_to_resistance'] = 0.05  # Default 5% above current price
        result['support_strength'] = 0.5  # Default medium strength
        result['resistance_strength'] = 0.5  # Default medium strength
        result['support_source_count'] = 1
        result['resistance_source_count'] = 1
        result['support_confluence'] = 0.0
        result['resistance_confluence'] = 0.0
        result['support_resistance_ratio'] = 1.0  # Default balanced
        result['sr_range_position'] = 0.5  # Default middle of range
        result['liquidity_imbalance'] = 0.0  # Default balanced
        result['trap_score'] = 0.0
        result['trap_position'] = 0.5  # Default middle of trap

        # Check if we have the necessary liquidity levels data
        if not liquidity_levels:
            logger.warning("Missing liquidity levels data")
            return result

        # Get current price (last close)
        current_price = price_data['close'].iloc[-1]

        # Extract support and resistance levels
        support_levels = liquidity_levels.get('support', [])
        resistance_levels = liquidity_levels.get('resistance', [])

        if not support_levels and not resistance_levels:
            logger.warning("No support or resistance levels found")
            return result

        # 1. Distance to nearest support level
        if support_levels:
            # Sort by distance to current price
            nearest_supports = sorted(support_levels, key=lambda x: abs(x.get('price', 0) - current_price))

            if nearest_supports:
                nearest_support = nearest_supports[0]
                support_price = nearest_support.get('price', current_price)

                # Calculate distance (negative means price is below support)
                result['distance_to_support'] = (current_price - support_price) / current_price

                # Support strength
                result['support_strength'] = nearest_support.get('strength', 0)

                # Support sources count
                result['support_source_count'] = len(nearest_support.get('sources', []))

        # 2. Distance to nearest resistance level
        if resistance_levels:
            # Sort by distance to current price
            nearest_resistances = sorted(resistance_levels, key=lambda x: abs(x.get('price', 0) - current_price))

            if nearest_resistances:
                nearest_resistance = nearest_resistances[0]
                resistance_price = nearest_resistance.get('price', current_price)

                # Calculate distance (positive means price is below resistance)
                result['distance_to_resistance'] = (current_price - resistance_price) / current_price

                # Resistance strength
                result['resistance_strength'] = nearest_resistance.get('strength', 0)

                # Resistance sources count
                result['resistance_source_count'] = len(nearest_resistance.get('sources', []))

        # 3. Support/resistance ratio
        if 'support_strength' in result.columns and 'resistance_strength' in result.columns:
            support_strength = result['support_strength'].iloc[0] if not result.empty else 0
            resistance_strength = result['resistance_strength'].iloc[0] if not result.empty else 0

            # Calculate ratio (>1 means stronger support, <1 means stronger resistance)
            if resistance_strength > 0:
                result['support_resistance_ratio'] = support_strength / resistance_strength

        # 4. Price position within support/resistance range
        if 'distance_to_support' in result.columns and 'distance_to_resistance' in result.columns:
            # Convert distances to absolute values
            abs_support_dist = abs(result['distance_to_support'].iloc[0]) if not result.empty else 0
            abs_resistance_dist = abs(result['distance_to_resistance'].iloc[0]) if not result.empty else 0

            # Calculate position (0 = at support, 1 = at resistance)
            total_dist = abs_support_dist + abs_resistance_dist
            if total_dist > 0:
                result['sr_range_position'] = abs_support_dist / total_dist

        # 5. Liquidity level density
        # Count levels within 5% of current price
        price_range = 0.05
        nearby_supports = [level for level in support_levels
                          if abs(level.get('price', 0) - current_price) / current_price <= price_range]

        nearby_resistances = [level for level in resistance_levels
                             if abs(level.get('price', 0) - current_price) / current_price <= price_range]

        result['nearby_support_count'] = len(nearby_supports)
        result['nearby_resistance_count'] = len(nearby_resistances)
        result['nearby_level_ratio'] = len(nearby_supports) / max(1, len(nearby_resistances))

        # 6. Confluence score
        # Calculate average source count for nearby levels
        if nearby_supports:
            avg_support_sources = sum(len(level.get('sources', [])) for level in nearby_supports) / len(nearby_supports)
            result['support_confluence'] = avg_support_sources

        if nearby_resistances:
            avg_resistance_sources = sum(len(level.get('sources', [])) for level in nearby_resistances) / len(nearby_resistances)
            result['resistance_confluence'] = avg_resistance_sources

        # 7. Liquidity imbalance
        # Calculate total strength of nearby supports vs resistances
        if nearby_supports and nearby_resistances:
            total_support_strength = sum(level.get('strength', 0) for level in nearby_supports)
            total_resistance_strength = sum(level.get('strength', 0) for level in nearby_resistances)

            if total_resistance_strength > 0:
                result['liquidity_imbalance'] = total_support_strength / total_resistance_strength - 1

        # 8. Trapped price zones
        # Check if price is between two strong levels
        if len(support_levels) >= 1 and len(resistance_levels) >= 1:
            # Find strongest nearby support and resistance
            strongest_support = max(nearby_supports, key=lambda x: x.get('strength', 0)) if nearby_supports else None
            strongest_resistance = max(nearby_resistances, key=lambda x: x.get('strength', 0)) if nearby_resistances else None

            if strongest_support and strongest_resistance:
                support_price = strongest_support.get('price', 0)
                resistance_price = strongest_resistance.get('price', 0)

                # Check if price is between support and resistance
                if support_price < current_price < resistance_price:
                    # Calculate trap score based on level strengths and range width
                    range_width = (resistance_price - support_price) / current_price
                    support_strength = strongest_support.get('strength', 0)
                    resistance_strength = strongest_resistance.get('strength', 0)

                    # Higher score = stronger trap (narrow range between strong levels)
                    if range_width > 0:
                        result['trap_score'] = (support_strength + resistance_strength) / range_width

                        # Position within trap (0 = at support, 1 = at resistance)
                        result['trap_position'] = (current_price - support_price) / (resistance_price - support_price)

        return result
