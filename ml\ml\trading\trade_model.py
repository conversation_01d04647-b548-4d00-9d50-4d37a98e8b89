"""
Trading Model for Options Liquidity Analyzer

This module implements the core trade model that integrates pattern recognition,
liquidity analysis, position sizing, and execution optimization to generate
trading decisions aligned with the ROI strategy.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any, Callable
import logging
import datetime
import json
import torch  # Use PyTorch instead of TensorFlow
from enum import Enum

# Import custom modules
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from ml.ml_model_registry import ModelRegistry, get_model_registry
from ml.ml_strategy_optimizer import StrategyOptimizer
from utils.timeframe_utils import TimeFrame

# Set up logging
logger = logging.getLogger(__name__)

class MarketRegime(Enum):
    """Enum representing different market regimes."""
    BULL = "bull"
    BEAR = "bear"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    TRENDING = "trending"
    REVERSAL = "reversal"
    UNKNOWN = "unknown"


class TradeSignal:
    """Class representing a trade signal with relevant parameters."""
    
    def __init__(self, 
                 symbol: str, 
                 direction: str,
                 entry_price: float,
                 stop_price: float,
                 target_price: float,
                 position_size: float,
                 probability: float,
                 timeframe: TimeFrame,
                 pattern_name: str,
                 liquidity_score: float,
                 timestamp: Optional[datetime.datetime] = None,
                 metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize a trade signal.
        
        Args:
            symbol: Trading symbol
            direction: "long" or "short"
            entry_price: Recommended entry price
            stop_price: Recommended stop loss price
            target_price: Recommended target price
            position_size: Recommended position size as percentage of portfolio
            probability: Estimated probability of success
            timeframe: Timeframe for the signal
            pattern_name: Name of the pattern that triggered the signal
            liquidity_score: Score of liquidity at the trading level
            timestamp: Signal generation timestamp (default: current time)
            metadata: Additional metadata for the signal
        """
        self.symbol = symbol
        self.direction = direction
        self.entry_price = entry_price
        self.stop_price = stop_price
        self.target_price = target_price
        self.position_size = position_size
        self.probability = probability
        self.timeframe = timeframe
        self.pattern_name = pattern_name
        self.liquidity_score = liquidity_score
        self.timestamp = timestamp or datetime.datetime.now()
        self.metadata = metadata or {}
        
        # Calculate additional metrics
        self.risk = abs(entry_price - stop_price)
        self.reward = abs(target_price - entry_price)
        self.risk_reward_ratio = self.reward / self.risk if self.risk != 0 else 0
        self.expected_value = (self.probability * self.reward) - ((1 - self.probability) * self.risk)
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert the signal to a dictionary."""
        return {
            "symbol": self.symbol,
            "direction": self.direction,
            "entry_price": self.entry_price,
            "stop_price": self.stop_price,
            "target_price": self.target_price,
            "position_size": self.position_size,
            "probability": self.probability,
            "timeframe": self.timeframe.value,
            "pattern_name": self.pattern_name,
            "liquidity_score": self.liquidity_score,
            "timestamp": self.timestamp.isoformat(),
            "risk": self.risk,
            "reward": self.reward,
            "risk_reward_ratio": self.risk_reward_ratio,
            "expected_value": self.expected_value,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradeSignal':
        """Create a signal from a dictionary."""
        metadata = data.pop("metadata", {})
        
        # Handle timestamp (use current time if not provided)
        timestamp = datetime.datetime.now()
        if "timestamp" in data:
            timestamp_str = data.pop("timestamp")
            if timestamp_str is not None:
                timestamp = datetime.datetime.fromisoformat(timestamp_str)
        
        # Handle timeframe (use D1 if not provided)
        # from src.utils.timeframe_utils import TimeFrame  # Commented out - external dependency
        if "timeframe" in data:
            timeframe_value = data.pop("timeframe")
            try:
                timeframe = TimeFrame(timeframe_value)
            except (ValueError, TypeError):
                timeframe = TimeFrame.D1
        else:
            timeframe = TimeFrame.D1
        
        # Remove derived properties
        data.pop("risk", None)
        data.pop("reward", None)
        data.pop("risk_reward_ratio", None)
        data.pop("expected_value", None)
        
        return cls(
            **data,
            timeframe=timeframe,
            timestamp=timestamp,
            metadata=metadata
        )


class TradeModel:
    """
    Main class for the trading model that integrates various components to
    generate trading signals and manage trades.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the trade model.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Set default configuration values
        self.config.setdefault('min_probability', 0.65)
        self.config.setdefault('min_risk_reward', 1.5)
        self.config.setdefault('min_liquidity_score', 0.7)
        self.config.setdefault('max_position_size', 0.05)  # 5% of portfolio
        self.config.setdefault('max_risk_per_trade', 0.02)  # 2% of portfolio
        self.config.setdefault('kelly_fraction', 0.5)  # Half-Kelly for safety
        self.config.setdefault('use_regime_detection', True)
        self.config.setdefault('use_ml_predictions', True)
        
        # Initialize model components
        self.model_registry = get_model_registry()
        self.strategy_optimizer = None
        self.edge_detector = None
        self.position_sizer = None
        self.trade_selector = None
        self.execution_optimizer = None
        
        # Initialize state
        self.current_market_regime = MarketRegime.UNKNOWN
        self.active_signals = []
        self.active_trades = []
        self.trade_history = []
        
        # Load components if specified in config
        if self.config.get('load_edge_detector'):
            self._load_edge_detector()
        
        if self.config.get('load_position_sizer'):
            self._load_position_sizer()
        
        if self.config.get('load_trade_selector'):
            self._load_trade_selector()
        
        if self.config.get('load_execution_optimizer'):
            self._load_execution_optimizer()
        
        if self.config.get('load_strategy_optimizer'):
            self._load_strategy_optimizer()
        
        logger.info("TradeModel initialized with configuration: %s", self.config)
    
    def _load_edge_detector(self):
        """Load the edge detector component."""
        from edge_detection import create_edge_detector
        detector_type = self.config.get('edge_detector_type', 'combined')
        self.edge_detector = create_edge_detector(detector_type, self.config)
        logger.info("Loaded edge detector of type: %s", detector_type)
    
    def _load_position_sizer(self):
        """Load the position sizer component."""
        from position_sizing import create_position_sizer
        sizer_type = self.config.get('position_sizer_type', 'kelly')
        self.position_sizer = create_position_sizer(sizer_type, self.config)
        logger.info("Loaded position sizer of type: %s", sizer_type)
    
    def _load_trade_selector(self):
        """Load the trade selector component."""
        from trade_selection import create_trade_selector
        selector_type = self.config.get('trade_selector_type', 'probability')
        self.trade_selector = create_trade_selector(selector_type, self.config)
        logger.info("Loaded trade selector of type: %s", selector_type)
    
    def _load_execution_optimizer(self):
        """Load the execution optimizer component."""
        from execution_optimizer import create_execution_optimizer
        optimizer_type = self.config.get('execution_optimizer_type', 'entry_exit')
        self.execution_optimizer = create_execution_optimizer(optimizer_type, self.config)
        logger.info("Loaded execution optimizer of type: %s", optimizer_type)
    
    def _load_strategy_optimizer(self):
        """Load the strategy optimizer component."""
        agent_type = self.config.get('agent_type', 'ppo')
        self.strategy_optimizer = StrategyOptimizer({
            'agent_type': agent_type,
            **self.config.get('optimizer_config', {})
        })
        logger.info("Loaded strategy optimizer with agent type: %s", agent_type)
    
    def detect_market_regime(self, market_data: pd.DataFrame) -> MarketRegime:
        """
        Detect the current market regime based on market data.
        
        Args:
            market_data: DataFrame with market data
            
        Returns:
            Detected market regime
        """
        if not self.config.get('use_regime_detection', True):
            return MarketRegime.UNKNOWN
        
        # Simple regime detection based on price action
        # In a real implementation, this would be much more sophisticated
        
        # Extract price data
        if 'close' not in market_data.columns:
            logger.warning("Market data missing 'close' column. Cannot detect regime.")
            return MarketRegime.UNKNOWN
        
        closes = market_data['close'].values
        
        # Calculate returns
        returns = np.diff(closes) / closes[:-1]
        
        # Calculate metrics
        mean_return = np.mean(returns)
        volatility = np.std(returns)
        
        # Detect trend
        if len(closes) >= 20:
            sma20 = np.mean(closes[-20:])
            sma50 = np.mean(closes[-50:]) if len(closes) >= 50 else sma20
            
            # Trending up
            if mean_return > 0 and closes[-1] > sma20 > sma50:
                if volatility > 0.015:  # High volatility
                    regime = MarketRegime.VOLATILE
                else:
                    regime = MarketRegime.BULL
            
            # Trending down
            elif mean_return < 0 and closes[-1] < sma20 < sma50:
                if volatility > 0.015:  # High volatility
                    regime = MarketRegime.VOLATILE
                else:
                    regime = MarketRegime.BEAR
            
            # Sideways
            else:
                if volatility < 0.008:  # Low volatility
                    regime = MarketRegime.SIDEWAYS
                else:
                    regime = MarketRegime.VOLATILE
        else:
            # Not enough data
            regime = MarketRegime.UNKNOWN
        
        self.current_market_regime = regime
        logger.info("Detected market regime: %s", regime)
        return regime
    
    def generate_trade_signals(self, market_data: pd.DataFrame) -> List[TradeSignal]:
        """
        Generate trade signals based on market data and current market conditions.
        
        Args:
            market_data: DataFrame with market data
            
        Returns:
            List of generated trade signals
        """
        # Ensure we have all necessary components
        if self.edge_detector is None:
            self._load_edge_detector()
        
        if self.position_sizer is None:
            self._load_position_sizer()
        
        if self.trade_selector is None:
            self._load_trade_selector()
        
        if self.execution_optimizer is None:
            self._load_execution_optimizer()
        
        # Detect market regime
        regime = self.detect_market_regime(market_data)
        
        # Generate signals using edge detector
        raw_signals = self.edge_detector.detect_edges(market_data, regime)
        
        # Filter signals using trade selector
        filtered_signals = self.trade_selector.select_trades(raw_signals, market_data, regime)
        
        # Optimize execution parameters
        optimized_signals = self.execution_optimizer.optimize_execution(filtered_signals, market_data)
        
        # Calculate position sizes
        signals_with_sizing = []
        for signal in optimized_signals:
            position_size = self.position_sizer.calculate_position_size(
                signal, market_data, self.active_trades, regime
            )
            signal.position_size = position_size
            signals_with_sizing.append(signal)
        
        # Update active signals
        self.active_signals = signals_with_sizing
        
        logger.info("Generated %d trade signals", len(signals_with_sizing))
        return signals_with_sizing
    
    def execute_trade(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Execute a trade based on a trade signal.
        
        In a real implementation, this would connect to a broker API.
        For this implementation, we simulate execution.
        
        Args:
            signal: Trade signal to execute
            
        Returns:
            Dictionary with trade execution details
        """
        # In a real implementation, this would connect to a broker API
        # For now, we simulate execution with a slight slippage
        
        # Apply random slippage between -0.1% and +0.1%
        slippage_factor = 1 + (np.random.random() * 0.002 - 0.001)
        executed_price = signal.entry_price * slippage_factor
        
        # Create trade record
        trade = {
            "signal": signal.to_dict(),
            "execution_price": executed_price,
            "execution_timestamp": datetime.datetime.now().isoformat(),
            "execution_slippage": (executed_price - signal.entry_price) / signal.entry_price,
            "status": "active",
            "exit_price": None,
            "exit_timestamp": None,
            "profit_loss": None,
            "trade_id": f"{signal.symbol}_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
        }
        
        # Add to active trades
        self.active_trades.append(trade)
        
        # Shadow mode logging - capture trade execution
        try:
            from agents.agent_zero import AgentZeroAdvisor
            shadow_agent = AgentZeroAdvisor()
            
            signal_data = {
                'confidence': signal.confidence,
                'strength': signal.strength,
                'execution_recommendation': signal.direction.lower()
            }
            
            math_data = {
                'accuracy_score': 0.89,  # Trade model accuracy
                'precision': 0.001
            }
            
            market_context = {
                'system': 'trade_model_execution',
                'symbol': signal.symbol,
                'direction': signal.direction,
                'entry_price': signal.entry_price,
                'executed_price': executed_price,
                'slippage': (executed_price - signal.entry_price) / signal.entry_price,
                'trade_id': trade['trade_id']
            }
            
            shadow_agent.log_training_data(
                signal_data=signal_data,
                math_data=math_data,
                decision={'action': 'trade_execution', 'trade': trade},
                outcome=0.0,  # Will be updated when trade closes
                market_context=market_context
            )
            logger.info("Shadow mode: Trade execution logged")
            
        except Exception as e:
            logger.warning(f"Shadow mode logging failed: {e}")
        
        logger.info("Executed trade for %s %s at price %.2f", 
                    signal.symbol, signal.direction, executed_price)
        
        return trade
    
    def update_trades(self, market_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Update the status of active trades based on current market data.
        
        Args:
            market_data: Current market data
            
        Returns:
            List of updated trades
        """
        updated_trades = []
        still_active_trades = []
        
        for trade in self.active_trades:
            signal = TradeSignal.from_dict(trade["signal"])
            symbol = signal.symbol
            
            # Find the current price for this symbol
            if 'symbol' in market_data.columns:
                symbol_data = market_data[market_data['symbol'] == symbol]
                if len(symbol_data) == 0:
                    # Symbol not found in market data, keep trade as is
                    still_active_trades.append(trade)
                    continue
                
                current_price = symbol_data.iloc[-1]['close']
            else:
                # Assume market data is for the symbol
                current_price = market_data.iloc[-1]['close']
            
            # Check if stop loss or target has been hit
            executed_price = trade["execution_price"]
            
            if signal.direction == "long":
                # Check stop loss
                if current_price <= signal.stop_price:
                    trade["status"] = "closed"
                    trade["exit_price"] = signal.stop_price
                    trade["exit_timestamp"] = datetime.datetime.now().isoformat()
                    trade["profit_loss"] = (signal.stop_price - executed_price) / executed_price
                    trade["exit_reason"] = "stop_loss"
                    self.trade_history.append(trade)
                    updated_trades.append(trade)
                    
                # Check target
                elif current_price >= signal.target_price:
                    trade["status"] = "closed"
                    trade["exit_price"] = signal.target_price
                    trade["exit_timestamp"] = datetime.datetime.now().isoformat()
                    trade["profit_loss"] = (signal.target_price - executed_price) / executed_price
                    trade["exit_reason"] = "target"
                    self.trade_history.append(trade)
                    updated_trades.append(trade)
                    
                else:
                    # Update unrealized P&L
                    trade["current_price"] = current_price
                    trade["unrealized_pl"] = (current_price - executed_price) / executed_price
                    still_active_trades.append(trade)
            
            else:  # short
                # Check stop loss
                if current_price >= signal.stop_price:
                    trade["status"] = "closed"
                    trade["exit_price"] = signal.stop_price
                    trade["exit_timestamp"] = datetime.datetime.now().isoformat()
                    trade["profit_loss"] = (executed_price - signal.stop_price) / executed_price
                    trade["exit_reason"] = "stop_loss"
                    self.trade_history.append(trade)
                    updated_trades.append(trade)
                    
                # Check target
                elif current_price <= signal.target_price:
                    trade["status"] = "closed"
                    trade["exit_price"] = signal.target_price
                    trade["exit_timestamp"] = datetime.datetime.now().isoformat()
                    trade["profit_loss"] = (executed_price - signal.target_price) / executed_price
                    trade["exit_reason"] = "target"
                    self.trade_history.append(trade)
                    updated_trades.append(trade)
                    
                else:
                    # Update unrealized P&L
                    trade["current_price"] = current_price
                    trade["unrealized_pl"] = (executed_price - current_price) / executed_price
                    still_active_trades.append(trade)
        
        # Update active trades list
        self.active_trades = still_active_trades
        
        logger.info("Updated trades: %d closed, %d active", 
                   len(updated_trades), len(still_active_trades))
        
        return updated_trades
    
    def exit_trade(self, trade_id: str, price: Optional[float] = None) -> Dict[str, Any]:
        """
        Manually exit a trade at the current or specified price.
        
        Args:
            trade_id: ID of the trade to exit
            price: Exit price (if None, use current market price)
            
        Returns:
            Updated trade record
        """
        # Find the trade
        trade = None
        for t in self.active_trades:
            if t.get("trade_id") == trade_id:
                trade = t
                break
        
        if trade is None:
            logger.warning("Trade with ID %s not found", trade_id)
            return None
        
        # If no price specified, use last known price
        if price is None:
            price = trade.get("current_price")
            if price is None:
                logger.warning("No price specified and no current price available")
                return None
        
        # Update trade
        signal = TradeSignal.from_dict(trade["signal"])
        executed_price = trade["execution_price"]
        
        trade["status"] = "closed"
        trade["exit_price"] = price
        trade["exit_timestamp"] = datetime.datetime.now().isoformat()
        trade["exit_reason"] = "manual"
        
        if signal.direction == "long":
            trade["profit_loss"] = (price - executed_price) / executed_price
        else:  # short
            trade["profit_loss"] = (executed_price - price) / executed_price
        
        # Move to history
        self.trade_history.append(trade)
        
        # Remove from active trades
        self.active_trades = [t for t in self.active_trades if t.get("trade_id") != trade_id]
        
        logger.info("Manually exited trade %s at price %.2f with P&L %.2f%%", 
                   trade_id, price, trade["profit_loss"] * 100)
        
        return trade
    
    def calculate_performance_metrics(self) -> Dict[str, Any]:
        """
        Calculate performance metrics based on trade history.
        
        Returns:
            Dictionary of performance metrics
        """
        if not self.trade_history:
            return {
                "total_trades": 0,
                "win_rate": 0,
                "profit_factor": 0,
                "average_win": 0,
                "average_loss": 0,
                "largest_win": 0,
                "largest_loss": 0,
                "total_profit_loss": 0,
                "roi": 0
            }
        
        # Extract profit/loss from closed trades
        closed_trades = [trade for trade in self.trade_history if trade["status"] == "closed"]
        profits = [trade["profit_loss"] for trade in closed_trades if trade["profit_loss"] > 0]
        losses = [abs(trade["profit_loss"]) for trade in closed_trades if trade["profit_loss"] <= 0]
        
        # Calculate metrics
        total_trades = len(closed_trades)
        winning_trades = len(profits)
        losing_trades = len(losses)
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_profit = sum(profits)
        total_loss = sum(losses)
        
        profit_factor = total_profit / total_loss if total_loss > 0 else float('inf')
        
        average_win = np.mean(profits) if profits else 0
        average_loss = np.mean(losses) if losses else 0
        
        largest_win = max(profits) if profits else 0
        largest_loss = max(losses) if losses else 0
        
        total_profit_loss = total_profit - total_loss
        
        # Calculate ROI (simplified)
        # In a real implementation, this would consider position sizes and portfolio value
        roi = total_profit_loss  # This is the sum of percentage returns
        
        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "average_win": average_win,
            "average_loss": average_loss,
            "largest_win": largest_win,
            "largest_loss": largest_loss,
            "total_profit": total_profit,
            "total_loss": total_loss,
            "total_profit_loss": total_profit_loss,
            "roi": roi
        }
    
    def train_strategy(self, training_data: pd.DataFrame, validation_data: Optional[pd.DataFrame] = None) -> Dict[str, List[float]]:
        """
        Train the strategy optimizer using historical data.
        
        Args:
            training_data: Training data as DataFrame
            validation_data: Optional validation data
            
        Returns:
            Dictionary of training metrics
        """
        if self.strategy_optimizer is None:
            self._load_strategy_optimizer()
        
        # Prepare environment configuration
        optimizer_config = self.config.get('optimizer_config', {})
        
        # Train the strategy optimizer
        metrics = self.strategy_optimizer.train(training_data, validation_data)
        
        # Save the trained model
        model_id = self.strategy_optimizer.save("latest")
        logger.info("Trained and saved strategy optimizer with model ID: %s", model_id)
        
        return metrics
    
    def load_strategy(self, model_id: str) -> bool:
        """
        Load a trained strategy from the model registry.
        
        Args:
            model_id: ID of the model to load
            
        Returns:
            True if successful, False otherwise
        """
        if self.strategy_optimizer is None:
            self._load_strategy_optimizer()
        
        success = self.strategy_optimizer.load(model_id)
        
        if success:
            logger.info("Successfully loaded strategy optimizer with model ID: %s", model_id)
        else:
            logger.error("Failed to load strategy optimizer with model ID: %s", model_id)
        
        return success
    
    def save(self, version: Optional[str] = None) -> str:
        """
        Save the trade model to the model registry.
        
        Args:
            version: Optional version string
            
        Returns:
            Model ID in the registry
        """
        # Prepare state for saving
        state = {
            "config": self.config,
            "active_signals": [signal.to_dict() for signal in self.active_signals],
            "active_trades": self.active_trades,
            "trade_history": self.trade_history,
            "current_market_regime": self.current_market_regime.value
        }
        
        # Save model components if they exist
        component_states = {}
        
        if self.edge_detector:
            component_states["edge_detector"] = self.edge_detector.save()
        
        if self.position_sizer:
            component_states["position_sizer"] = self.position_sizer.save()
        
        if self.trade_selector:
            component_states["trade_selector"] = self.trade_selector.save()
        
        if self.execution_optimizer:
            component_states["execution_optimizer"] = self.execution_optimizer.save()
        
        if self.strategy_optimizer:
            component_states["strategy_optimizer"] = self.strategy_optimizer.save(version)
        
        state["component_states"] = component_states
        
        # Register model
        model_id = self.model_registry.register_model(
            model=state,
            model_name="trade_model",
            model_type="trade_model",
            metadata={"config": self.config},
            version=version
        )
        
        logger.info("Saved trade model with ID: %s", model_id)
        return model_id
    
    def load(self, model_id: str) -> bool:
        """
        Load a trade model from the model registry.
        
        Args:
            model_id: ID of the model to load
            
        Returns:
            True if successful, False otherwise
        """
        try:
            state, metadata = self.model_registry.load_model(model_id, "trade_model")
            
            # Update configuration
            self.config = state.get("config", self.config)
            
            # Load state
            self.active_signals = [TradeSignal.from_dict(signal) for signal in state.get("active_signals", [])]
            self.active_trades = state.get("active_trades", [])
            self.trade_history = state.get("trade_history", [])
            self.current_market_regime = MarketRegime(state.get("current_market_regime", MarketRegime.UNKNOWN.value))
            
            # Load components
            component_states = state.get("component_states", {})
            
            if "edge_detector" in component_states:
                self._load_edge_detector()
                self.edge_detector.load(component_states["edge_detector"])
            
            if "position_sizer" in component_states:
                self._load_position_sizer()
                self.position_sizer.load(component_states["position_sizer"])
            
            if "trade_selector" in component_states:
                self._load_trade_selector()
                self.trade_selector.load(component_states["trade_selector"])
            
            if "execution_optimizer" in component_states:
                self._load_execution_optimizer()
                self.execution_optimizer.load(component_states["execution_optimizer"])
            
            if "strategy_optimizer" in component_states:
                self._load_strategy_optimizer()
                self.strategy_optimizer.load(component_states["strategy_optimizer"])
            
            logger.info("Successfully loaded trade model with ID: %s", model_id)
            return True
            
        except Exception as e:
            logger.error("Error loading trade model: %s", e)
            return False


def create_trade_model(config: Optional[Dict[str, Any]] = None) -> TradeModel:
    """
    Create a trade model with the specified configuration.
    
    Args:
        config: Optional configuration dictionary
        
    Returns:
        Initialized trade model
    """
    if config is None:
        config = {}
    
    return TradeModel(config)
