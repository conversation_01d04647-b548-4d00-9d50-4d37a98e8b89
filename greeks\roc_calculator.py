#!/usr/bin/env python3
"""
ROC Calculator
Rate of Change calculator for Greeks time series analysis with statistical significance
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging

from .constants import *
from .greeks_result import GreeksResult

logger = logging.getLogger(__name__)


class ROCCalculator:
    """
    Rate of Change calculator for Greeks time series analysis
    
    Mathematical Foundation:
    - Central difference approximation for ROC calculations
    - Statistical significance testing using Z-scores
    - Trend detection algorithms with configurable sensitivity
    - Anomaly identification using statistical thresholds
    
    Statistical Methods:
    - Z-score calculation: (value - mean) / std_dev
    - Confidence intervals: 95% default with configurable levels
    - Trend significance: Statistical tests for directional bias
    - Outlier detection: Multiple standard deviation thresholds
    """
    
    def __init__(self, lookback_periods: int = 20, significance_level: float = 0.95):
        self.lookback_periods = lookback_periods
        self.significance_level = significance_level
        self.z_threshold = stats.norm.ppf((1 + significance_level) / 2)
        self.calculation_history = {}
        
    def calculate_roc_derivatives(self, 
                                 greeks_history: pd.DataFrame,
                                 symbol: str = 'UNKNOWN') -> Dict[str, float]:
        """
        Calculate rate of change for all Greeks with statistical validation
        
        Args:
            greeks_history: DataFrame with historical Greeks values
            symbol: Symbol for tracking and logging
            
        Returns:
            Dict with ROC values for each Greek
            
        Mathematical Formula:
        ROC = (current_value - previous_value) / previous_value * 100
        """
        
        try:
            if len(greeks_history) < 2:
                logger.warning(f"Insufficient data for ROC calculation: {len(greeks_history)} periods")
                return self._create_zero_roc_result()
            
            # Ensure we have required columns
            required_columns = ['delta', 'gamma', 'theta', 'vega', 'rho']
            missing_columns = [col for col in required_columns if col not in greeks_history.columns]
            
            if missing_columns:
                raise ValueError(f"Missing required columns for ROC calculation: {missing_columns}")
            
            # Sort by timestamp if available
            if 'timestamp' in greeks_history.columns:
                greeks_history = greeks_history.sort_values('timestamp')
            
            # Calculate ROC for each Greek using central difference
            roc_values = {}
            
            for greek in required_columns:
                roc_values[f'{greek}_roc'] = self._calculate_single_roc(
                    greeks_history[greek].values, greek
                )
            
            # Store calculation history for trend analysis
            self.calculation_history[symbol] = {
                'timestamp': datetime.now(),
                'roc_values': roc_values,
                'data_points': len(greeks_history)
            }
            
            logger.debug(f"ROC calculation completed for {symbol}: {len(greeks_history)} data points")
            return roc_values
            
        except Exception as e:
            logger.error(f"ROC calculation failed for {symbol}: {e}")
            return self._create_zero_roc_result()
    
    def _calculate_single_roc(self, values: np.ndarray, greek_name: str) -> float:
        """
        Calculate ROC for a single Greek using optimized method
        
        Args:
            values: Array of historical Greek values
            greek_name: Name of the Greek for error reporting
            
        Returns:
            Rate of change as percentage
        """
        
        try:
            # Remove NaN values
            clean_values = values[~np.isnan(values)]
            
            if len(clean_values) < 2:
                return 0.0
            
            # Use the last two values for most recent ROC
            current_value = clean_values[-1]
            previous_value = clean_values[-2]
            
            # Handle division by zero
            if abs(previous_value) < 1e-12:
                if abs(current_value) < 1e-12:
                    return 0.0  # Both values near zero
                else:
                    return np.sign(current_value) * 100.0  # Infinite percentage change, cap at 100%
            
            # Calculate percentage change
            roc = ((current_value - previous_value) / abs(previous_value)) * 100.0
            
            # Cap extreme ROC values to prevent numerical issues
            roc = np.clip(roc, -1000.0, 1000.0)
            
            # Validate finite result
            if not np.isfinite(roc):
                logger.warning(f"Non-finite ROC for {greek_name}: {roc}")
                return 0.0
            
            return float(roc)
            
        except Exception as e:
            logger.error(f"Single ROC calculation failed for {greek_name}: {e}")
            return 0.0
    
    def detect_statistical_anomalies(self, 
                                   current_greeks: GreeksResult,
                                   historical_greeks: List[GreeksResult]) -> Tuple[List[Dict[str, Any]], Dict[str, float]]:
        """
        Detect statistical anomalies in Greeks using Z-score analysis
        
        Args:
            current_greeks: Current Greeks calculation
            historical_greeks: List of historical Greeks for baseline
            
        Returns:
            Tuple of (anomaly_list, statistical_significance)
        """
        
        try:
            if len(historical_greeks) < 5:
                logger.warning("Insufficient historical data for anomaly detection")
                return [], {}
            
            # Extract historical values for each Greek
            historical_data = self._extract_historical_values(historical_greeks)
            
            # Current Greek values
            current_values = {
                'delta': current_greeks.delta,
                'gamma': current_greeks.gamma,
                'theta': current_greeks.theta,
                'vega': current_greeks.vega,
                'rho': current_greeks.rho
            }
            
            # Detect anomalies and calculate significance
            anomalies = []
            significance_scores = {}
            
            for greek_name, current_value in current_values.items():
                if greek_name in historical_data:
                    anomaly_result = self._detect_single_anomaly(
                        current_value, historical_data[greek_name], greek_name
                    )
                    
                    if anomaly_result['is_anomaly']:
                        anomalies.append(anomaly_result)
                    
                    significance_scores[f'{greek_name}_significance'] = anomaly_result['significance']
            
            # Sort anomalies by severity
            anomalies.sort(key=lambda x: abs(x['z_score']), reverse=True)
            
            return anomalies, significance_scores
            
        except Exception as e:
            logger.error(f"Anomaly detection failed: {e}")
            return [], {}
    
    def _extract_historical_values(self, historical_greeks: List[GreeksResult]) -> Dict[str, np.ndarray]:
        """Extract historical values for each Greek"""
        
        historical_data = {
            'delta': np.array([g.delta for g in historical_greeks]),
            'gamma': np.array([g.gamma for g in historical_greeks]),
            'theta': np.array([g.theta for g in historical_greeks]),
            'vega': np.array([g.vega for g in historical_greeks]),
            'rho': np.array([g.rho for g in historical_greeks])
        }
        
        # Remove NaN values
        for greek_name in historical_data:
            historical_data[greek_name] = historical_data[greek_name][
                ~np.isnan(historical_data[greek_name])
            ]
        
        return historical_data
    
    def _detect_single_anomaly(self, current_value: float, historical_values: np.ndarray, 
                             greek_name: str) -> Dict[str, Any]:
        """
        Detect anomaly for a single Greek using Z-score analysis
        
        Args:
            current_value: Current Greek value
            historical_values: Historical values for comparison
            greek_name: Name of the Greek
            
        Returns:
            Dict with anomaly analysis results
        """
        
        try:
            if len(historical_values) < 3:
                return {
                    'is_anomaly': False,
                    'greek_name': greek_name,
                    'z_score': 0.0,
                    'significance': 0.0,
                    'severity': 'insufficient_data'
                }
            
            # Calculate statistical measures
            mean_value = np.mean(historical_values)
            std_value = np.std(historical_values, ddof=1)  # Sample standard deviation
            
            # Handle zero standard deviation
            if std_value < 1e-12:
                # All historical values are essentially the same
                if abs(current_value - mean_value) < 1e-12:
                    z_score = 0.0
                    is_anomaly = False
                else:
                    z_score = np.sign(current_value - mean_value) * 10.0  # Large but finite Z-score
                    is_anomaly = True
            else:
                # Standard Z-score calculation
                z_score = (current_value - mean_value) / std_value
                is_anomaly = abs(z_score) > self.z_threshold
            
            # Calculate statistical significance (p-value)
            p_value = 2 * (1 - stats.norm.cdf(abs(z_score)))  # Two-tailed test
            significance = 1 - p_value
            
            # Determine severity
            severity = self._classify_anomaly_severity(abs(z_score))
            
            return {
                'is_anomaly': is_anomaly,
                'greek_name': greek_name,
                'current_value': current_value,
                'historical_mean': mean_value,
                'historical_std': std_value,
                'z_score': z_score,
                'p_value': p_value,
                'significance': significance,
                'severity': severity,
                'threshold_used': self.z_threshold,
                'interpretation': self._interpret_anomaly(greek_name, z_score)
            }
            
        except Exception as e:
            logger.error(f"Single anomaly detection failed for {greek_name}: {e}")
            return {
                'is_anomaly': False,
                'greek_name': greek_name,
                'z_score': 0.0,
                'significance': 0.0,
                'severity': 'calculation_error',
                'error': str(e)
            }
    
    def _classify_anomaly_severity(self, z_score_abs: float) -> str:
        """Classify anomaly severity based on Z-score magnitude"""
        
        if z_score_abs < 2.0:
            return 'none'
        elif z_score_abs < 2.5:
            return 'low'
        elif z_score_abs < 3.0:
            return 'moderate'
        elif z_score_abs < 4.0:
            return 'high'
        else:
            return 'critical'
    
    def _interpret_anomaly(self, greek_name: str, z_score: float) -> str:
        """Provide trading interpretation of Greek anomaly"""
        
        interpretations = {
            'delta': {
                'positive': 'Delta spike indicates increased directional sensitivity',
                'negative': 'Delta drop suggests reduced directional exposure'
            },
            'gamma': {
                'positive': 'Gamma surge indicates increased convexity and hedging needs',
                'negative': 'Gamma reduction suggests decreased sensitivity to delta changes'
            },
            'theta': {
                'positive': 'Theta anomaly indicates unusual time decay patterns',
                'negative': 'Accelerated theta decay detected'
            },
            'vega': {
                'positive': 'Vega spike indicates high volatility sensitivity',
                'negative': 'Vega drop suggests reduced volatility exposure'
            },
            'rho': {
                'positive': 'Rho anomaly indicates sensitivity to interest rate changes',
                'negative': 'Unusual interest rate sensitivity detected'
            }
        }
        
        direction = 'positive' if z_score > 0 else 'negative'
        return interpretations.get(greek_name, {}).get(direction, f'{greek_name} anomaly detected')
    
    def _create_zero_roc_result(self) -> Dict[str, float]:
        """Create zero ROC result for error cases"""
        return {
            'delta_roc': 0.0,
            'gamma_roc': 0.0,
            'theta_roc': 0.0,
            'vega_roc': 0.0,
            'rho_roc': 0.0
        }
    
    def calculate_trend_significance(self, greeks_history: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """
        Calculate trend significance for each Greek using statistical tests
        
        Args:
            greeks_history: DataFrame with historical Greeks values
            
        Returns:
            Dict with trend analysis for each Greek
        """
        
        try:
            trend_results = {}
            required_columns = ['delta', 'gamma', 'theta', 'vega', 'rho']
            
            for greek in required_columns:
                if greek not in greeks_history.columns:
                    continue
                
                values = greeks_history[greek].dropna().values
                
                if len(values) < 5:
                    trend_results[greek] = {
                        'trend_direction': 'insufficient_data',
                        'trend_strength': 0.0,
                        'statistical_significance': 0.0
                    }
                    continue
                
                # Linear regression for trend
                x = np.arange(len(values))
                slope, intercept, r_value, p_value, std_err = stats.linregress(x, values)
                
                # Determine trend direction and strength
                trend_direction = 'upward' if slope > 0 else 'downward' if slope < 0 else 'flat'
                trend_strength = abs(r_value)  # Correlation coefficient as strength measure
                statistical_significance = 1 - p_value  # Convert p-value to significance
                
                trend_results[greek] = {
                    'trend_direction': trend_direction,
                    'trend_strength': trend_strength,
                    'statistical_significance': statistical_significance,
                    'slope': slope,
                    'r_squared': r_value ** 2,
                    'p_value': p_value
                }
            
            return trend_results
            
        except Exception as e:
            logger.error(f"Trend significance calculation failed: {e}")
            return {}
    
    def get_calculation_summary(self) -> Dict[str, Any]:
        """Get summary of ROC calculations performed"""
        
        total_calculations = len(self.calculation_history)
        
        if total_calculations == 0:
            return {
                'total_calculations': 0,
                'average_data_points': 0,
                'last_calculation': None
            }
        
        # Calculate average data points
        total_data_points = sum(calc['data_points'] for calc in self.calculation_history.values())
        average_data_points = total_data_points / total_calculations
        
        # Get most recent calculation
        latest_calc = max(self.calculation_history.values(), key=lambda x: x['timestamp'])
        
        return {
            'total_calculations': total_calculations,
            'average_data_points': average_data_points,
            'last_calculation': latest_calc['timestamp'].isoformat(),
            'symbols_calculated': list(self.calculation_history.keys()),
            'z_threshold_used': self.z_threshold,
            'significance_level': self.significance_level
        }
