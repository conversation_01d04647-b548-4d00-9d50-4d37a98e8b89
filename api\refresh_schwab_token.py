#!/usr/bin/env python3
"""
Quick Schwab Token Refresh
Uses existing refresh token to get new access token
"""

import json
import urllib.request
import urllib.parse
import base64
import time

def refresh_schwab_token():
    """Refresh Schwab access token using refresh token"""
    
    # Load current token
    with open('D:/python projects/schwab_token.json', 'r') as f:
        token_data = json.load(f)
    
    # Load config
    with open('D:/script-work/CORE/config/schwab_config.json', 'r') as f:
        config = json.load(f)
    
    app_key = config['app_key']
    app_secret = config['app_secret']
    
    print("Refreshing Schwab access token...")
    
    try:
        token_url = "https://api.schwabapi.com/v1/oauth/token"
        credentials = base64.b64encode(f"{app_key}:{app_secret}".encode()).decode()
        
        headers = {
            'Authorization': f'Basic {credentials}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        data = urllib.parse.urlencode({
            'grant_type': 'refresh_token',
            'refresh_token': token_data['refresh_token']
        })
        
        request = urllib.request.Request(token_url, data=data.encode(), headers=headers, method='POST')
        
        with urllib.request.urlopen(request, timeout=30) as response:
            if response.getcode() == 200:
                new_token_data = json.loads(response.read().decode())
                
                # Update token data
                token_data.update({
                    'access_token': new_token_data['access_token'],
                    'expires_at': time.time() + new_token_data.get('expires_in', 1800),
                    'expires_in': new_token_data.get('expires_in', 1800)
                })
                
                # Save updated token
                with open('D:/python projects/schwab_token.json', 'w') as f:
                    json.dump(token_data, f, indent=2)
                
                print("[SUCCESS] Token refreshed successfully!")
                print(f"New expiration: {new_token_data.get('expires_in', 1800)} seconds")
                return True
            else:
                print(f"[FAILED] Token refresh failed: HTTP {response.getcode()}")
                return False
                
    except urllib.error.HTTPError as e:
        error_body = e.read().decode()
        print(f"[FAILED] Token refresh HTTP error: {e.code}")
        print(f"Response: {error_body}")
        return False
    except Exception as e:
        print(f"[FAILED] Token refresh error: {e}")
        return False

if __name__ == "__main__":
    success = refresh_schwab_token()
    if not success:
        print("\n[WARNING] Token refresh failed!")
        print("You may need to re-authenticate manually:")
        print("1. cd 'D:/python projects'")
        print("2. py get_auth_url.py")
        print("3. Complete browser auth")
        print("4. py token_exchange.py")
