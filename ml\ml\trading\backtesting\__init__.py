"""
Backtesting framework for Liquidity Strategy.

This package provides comprehensive backtesting capabilities including:
- Data pipeline for data collection and preprocessing
- Training pipeline for model training and validation
- Backtesting for realistic execution simulation
- Statistical analysis of backtest results
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

from data_pipeline import DataPipeline, FinancialDataset, TimeSeriesSplit
from training_pipeline import (
    ModelTrainer, EarlyStopping, LRSchedulerHandler, HyperparamOptimizer, train_model
)
from backtesting import (
    MarketSimulator, StrategyBacktester, Order, OrderType, OrderSide, OrderStatus,
    Position, Trade
)
from statistical_analysis import (
    BacktestAnalyzer, analyze_multiple_backtests
)

__all__ = [
    # Data Pipeline
    'DataPipeline',
    'FinancialDataset',
    'TimeSeriesSplit',
    
    # Training Pipeline
    'ModelTrainer',
    'EarlyStopping',
    'LRSchedulerHandler',
    'HyperparamOptimizer',
    'train_model',
    
    # Backtesting
    'MarketSimulator',
    'StrategyBacktester',
    'Order',
    'OrderType',
    'OrderSide',
    'OrderStatus',
    'Position',
    'Trade',
    
    # Statistical Analysis
    'BacktestAnalyzer',
    'analyze_multiple_backtests'
]
