{"timestamp": "2025-06-26T10:02:13.090943", "signal_data": {"confidence": 0.64, "strength": 0.42, "execution_recommendation": "hold"}, "math_data": {"accuracy_score": 0.8797, "precision": 0.003656}, "decision": {"timestamp": "2025-06-26T10:02:13.090921", "agent_zero_version": "1.5_Integrated", "ml_available": false, "execution_time_ms": 0.01, "action": "hold", "confidence": 0.5326110000000001, "reasoning": ["Rule-based decision: composite score 0.533", "Signal confidence: 0.640", "Signal strength: 0.420", "Execution recommendation: hold", "Math accuracy: 0.880", "ML system not available - using rule-based logic"], "composite_score": 0.5326110000000001, "decision_method": "rule_based", "ml_enhanced": false}, "outcome": 0.0, "market_context": {"b_series_analysis": {"features": {}, "confidence": 0.8, "pattern_strength": 0.7}, "flow_analysis": {"momentum": 0.5, "direction": "bullish", "strength": 0.8}, "anomaly_analysis": {"anomaly_detected": false, "anomaly_score": 0.0}, "iv_dynamics_analysis": {"iv_rank": 50.0, "iv_expansion": false, "volatility_regime": "normal"}, "market_regime": {"trend": "sideways", "volatility": "medium"}, "analysis_source": "test_system", "timestamp": "2025-06-26T10:02:13.090731", "ticker": "TEST"}, "agent_version": "1.5_Integrated"}