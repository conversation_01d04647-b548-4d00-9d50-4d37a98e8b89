# AGENT CONFIGURATION UPDATE SCRIPT
# Updates all agents to use Schwab MCP (localhost:8005) as primary

import os
import re
from pathlib import Path

def update_agent_mcp_endpoints():
    """Update all agents to use Schwab MCP as primary data source"""
    
    agents_dir = Path("D:/script-work/CORE/agents")
    updated_files = []
    
    # Pattern to find MCP URL configurations
    patterns_to_update = [
        (r'localhost:8004', 'localhost:8005'),  # Update old MCP to Schwab MCP
        (r'"mcp"', '"schwab"'),  # Update default source priority
        (r'"polygon"', '"mcp"'),  # Demote polygon to fallback
        (r'SOURCE_PRIORITY = \["schwab", "mcp", "polygon"\]', 
         'SOURCE_PRIORITY = ["schwab", "mcp", "polygon"]')  # Ensure correct priority
    ]
    
    for agent_file in agents_dir.glob("*.py"):
        if agent_file.name in ['__init__.py', 'agent_base.py']:
            continue
            
        try:
            with open(agent_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply all pattern updates
            for old_pattern, new_pattern in patterns_to_update:
                content = re.sub(old_pattern, new_pattern, content)
            
            # If content changed, write back
            if content != original_content:
                with open(agent_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                updated_files.append(agent_file.name)
                print(f"[UPDATED] {agent_file.name}")
        
        except Exception as e:
            print(f"[ERROR] updating {agent_file.name}: {e}")
    
    return updated_files

if __name__ == "__main__":
    print("="*60)
    print("AGENT MCP ENDPOINT UPDATE")
    print("="*60)
    print("Updating all agents to use Schwab MCP (localhost:8005)")
    print("Priority: Schwab > Legacy MCP > Polygon")
    print("="*60)
    
    updated = update_agent_mcp_endpoints()
    
    print("="*60)
    print(f"UPDATE COMPLETE: {len(updated)} files updated")
    if updated:
        print("Updated files:")
        for file in updated:
            print(f"  - {file}")
    else:
        print("All agents already configured correctly")
    print("="*60)
