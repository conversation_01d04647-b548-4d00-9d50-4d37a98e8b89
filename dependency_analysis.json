{
  "analysis_timestamp": "2025-06-24T18:33:06.271441",
  "files_analyzed": 112,
  "total_imports": 110,
  "categorized_imports": {
    "stdlib": [
      "typing",
      "random",
      "queue",
      "http",
      "hashlib",
      "traceback",
      "json",
      "datetime",
      "pathlib",
      "collections",
      "shutil",
      "re",
      "pickle",
      "gc",
      "threading",
      "enum",
      "functools",
      "subprocess",
      "uuid",
      "concurrent",
      "textwrap",
      "time",
      "sys",
      "logging",
      "asyncio",
      "urllib",
      "warnings",
      "math",
      "glob",
      "itertools",
      "os",
      "dataclasses"
    ],
    "third_party": [
      "math_validator_agent",
      "winreg",
      "orchestrator",
      "agent_zero_advanced_capabilities",
      "ml_system",
      "enhanced_csid_analyzer",
      "joblib",
      "endpoint_registry",
      "flask",
      "enhanced_csid_agent",
      "signal_quality_agent",
      "api_health_monitor",
      "pandas",
      "ml_enhanced_feature_engine",
      "aiohttp",
      "agent_base",
      "ast",
      "advanced_market_intelligence",
      "requests",
      "fetch_history",
      "commands",
      "greek_enhancement_agent",
      "abc",
      "backtest_simulator",
      "pydantic",
      "rate_limiter",
      "weakref",
      "argparse",
      "main",
      "yaml",
      "psutil",
      "greeks",
      "urllib3",
      "enhanced_greeks_engine",
      "data",
      "output_coordinator_agent",
      "flowphysics",
      "ml_ensemble_engine",
      "chart_generator_agent",
      "streamlit",
      "__future__",
      "signal",
      "risk_guard_agent",
      "numpy",
      "api_cache",
      "enhanced_data_agent_broker_integration",
      "flow_physics_agent",
      "unified_api_gateway",
      "redis",
      "decimal",
      "dynamic_threshold_engine",
      "mcp_server_production",
      "sklearn",
      "walk_train_validate",
      "uvicorn",
      "agent_zero_integration_hub",
      "build_features",
      "data_ingestion_agent",
      "base64",
      "matplotlib",
      "scipy",
      "fastapi",
      "comprehensive_api_tester",
      "signal_generator_agent",
      "schwab_production_api",
      "dotenv",
      "polygon",
      "modules",
      "agent_zero_performance_analytics",
      "ai_training_config",
      "api_robustness"
    ],
    "local": [
      "config",
      "utils",
      "agents",
      "api",
      "tasks",
      "analyzers",
      "engine"
    ]
  },
  "package_status": {
    "available": [
      "winreg",
      "agent_zero_advanced_capabilities",
      "joblib",
      "flask",
      "pandas",
      "requests",
      "ast",
      "abc",
      "pydantic",
      "weakref",
      "argparse",
      "yaml",
      "greeks",
      "psutil",
      "urllib3",
      "data",
      "flowphysics",
      "streamlit",
      "__future__",
      "signal",
      "numpy",
      "enhanced_data_agent_broker_integration",
      "decimal",
      "sklearn",
      "uvicorn",
      "agent_zero_integration_hub",
      "base64",
      "matplotlib",
      "scipy",
      "fastapi",
      "dotenv",
      "polygon",
      "aiohttp",
      "agent_zero_performance_analytics"
    ],
    "missing": [
      "math_validator_agent",
      "orchestrator",
      "ml_system",
      "enhanced_csid_analyzer",
      "endpoint_registry",
      "enhanced_csid_agent",
      "signal_quality_agent",
      "api_health_monitor",
      "ml_enhanced_feature_engine",
      "fetch_history",
      "agent_base",
      "advanced_market_intelligence",
      "commands",
      "greek_enhancement_agent",
      "backtest_simulator",
      "rate_limiter",
      "main",
      "enhanced_greeks_engine",
      "output_coordinator_agent",
      "ml_ensemble_engine",
      "chart_generator_agent",
      "risk_guard_agent",
      "api_cache",
      "flow_physics_agent",
      "unified_api_gateway",
      "redis",
      "dynamic_threshold_engine",
      "mcp_server_production",
      "walk_train_validate",
      "build_features",
      "data_ingestion_agent",
      "comprehensive_api_tester",
      "signal_generator_agent",
      "schwab_production_api",
      "modules",
      "ai_training_config",
      "api_robustness"
    ]
  },
  "file_imports": {
    "agent_zero_advanced_capabilities.py": 