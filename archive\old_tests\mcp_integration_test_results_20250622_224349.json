{"test_summary": {"total_tests": 10, "passed_tests": 10, "failed_tests": 0, "success_rate": 100.0, "total_execution_time": 36.758, "timestamp": "2025-06-22T22:43:49.085287"}, "detailed_results": {"Enhanced Data Agent": {"status": "PASS", "execution_time": 16.872, "has_data": true, "has_source": true, "is_real_time": false, "current_candle_access": false, "data_source": "none", "details": "Real-time: <PERSON><PERSON><PERSON>, Current candle: <PERSON><PERSON><PERSON>"}, "Signal Generator": {"status": "PASS", "execution_time": 16.865, "has_real_time_capability": true, "has_enhancement": false, "signal_structure_valid": true, "confidence_value": 75.0, "details": "Real-time capable: True, Enhanced: False"}, "Chart Generator": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "has_enhanced_color_scheme": true, "details": "Real-time capable: True, Agent: True"}, "Risk Guard": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Order Router": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Anomaly Detector": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "IV Dynamics": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Flow Physics": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Math Validator": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}, "Output Coordinator": {"status": "PASS", "has_real_time_capability": true, "has_real_time_agent": true, "details": "Real-time capable: True, Agent: True"}}}