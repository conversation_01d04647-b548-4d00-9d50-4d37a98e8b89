#!/usr/bin/env python3
"""
Core Position Sizing Module
Mathematical position sizing algorithms for the CORE execution system
"""

import numpy as np
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class PositionSizeResult:
    """Result from position sizing calculation"""
    position_size: float
    risk_amount: float
    max_loss: float
    confidence: float
    method: str

class PositionSizer:
    """
    Mathematical position sizing for trade execution
    Implements Kelly Criterion, Fixed Fractional, and VaR-based sizing
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.max_position_size = self.config.get('max_position_size', 0.02)  # 2% max
        self.min_position_size = self.config.get('min_position_size', 0.001)  # 0.1% min
        self.kelly_multiplier = self.config.get('kelly_multiplier', 0.25)  # <PERSON> Kelly
        
    def calculate_kelly_position(self, win_prob: float, avg_win: float, avg_loss: float) -> float:
        """
        <PERSON> Criterion: f* = (bp - q) / b
        where b = avg_win/avg_loss, p = win_prob, q = 1-p
        """
        if avg_loss <= 0 or win_prob <= 0 or win_prob >= 1:
            return 0.0
            
        b = avg_win / avg_loss  # Payoff ratio
        p = win_prob  # Win probability
        q = 1 - p  # Loss probability
        
        kelly_fraction = (b * p - q) / b
        
        # Apply multiplier for conservative sizing
        kelly_fraction *= self.kelly_multiplier
        
        return max(0.0, min(kelly_fraction, self.max_position_size))
    
    def calculate_fixed_fractional(self, account_value: float, risk_per_trade: float) -> float:
        """
        Fixed fractional position sizing
        """
        risk_fraction = min(risk_per_trade, self.max_position_size)
        return max(self.min_position_size, risk_fraction)
    
    def calculate_var_based_position(self, portfolio_var: float, target_var: float) -> float:
        """
        VaR-based position sizing
        """
        if portfolio_var <= 0:
            return self.min_position_size
            
        var_ratio = target_var / portfolio_var
        position_size = min(var_ratio, self.max_position_size)
        return max(self.min_position_size, position_size)
    
    def calculate_optimal_size(self, 
                             signal_strength: float,
                             confidence: float,
                             account_value: float,
                             stop_loss_distance: float,
                             historical_performance: Optional[Dict] = None) -> PositionSizeResult:
        """
        Calculate optimal position size using multiple methods
        """
        
        # Base risk per trade (percentage of account)
        base_risk = 0.01  # 1%
        
        # Adjust based on signal strength and confidence
        confidence_multiplier = confidence * signal_strength
        adjusted_risk = base_risk * confidence_multiplier
        
        # Method 1: Fixed fractional with confidence adjustment
        fixed_frac_size = self.calculate_fixed_fractional(account_value, adjusted_risk)
        
        # Method 2: Kelly if historical performance available
        kelly_size = 0.0
        if historical_performance:
            win_rate = historical_performance.get('win_rate', 0.5)
            avg_win = historical_performance.get('avg_win', 0.02)
            avg_loss = historical_performance.get('avg_loss', 0.01)
            kelly_size = self.calculate_kelly_position(win_rate, avg_win, avg_loss)
        
        # Method 3: Stop loss based sizing
        if stop_loss_distance > 0:
            max_risk_amount = account_value * adjusted_risk
            stop_loss_size = max_risk_amount / (stop_loss_distance * account_value)
            stop_loss_size = min(stop_loss_size, self.max_position_size)
        else:
            stop_loss_size = fixed_frac_size
        
        # Choose most conservative approach
        position_sizes = [fixed_frac_size, kelly_size, stop_loss_size]
        position_sizes = [size for size in position_sizes if size > 0]
        
        if not position_sizes:
            final_size = self.min_position_size
            method = "minimum_fallback"
        else:
            final_size = min(position_sizes)
            if final_size == fixed_frac_size:
                method = "fixed_fractional"
            elif final_size == kelly_size:
                method = "kelly_criterion"
            else:
                method = "stop_loss_based"
        
        # Calculate risk metrics
        risk_amount = final_size * account_value
        max_loss = risk_amount if stop_loss_distance > 0 else risk_amount * 0.5
        
        return PositionSizeResult(
            position_size=final_size,
            risk_amount=risk_amount,
            max_loss=max_loss,
            confidence=confidence,
            method=method
        )

if __name__ == "__main__":
    # Test the position sizer
    sizer = PositionSizer()
    
    result = sizer.calculate_optimal_size(
        signal_strength=0.8,
        confidence=0.75,
        account_value=100000,
        stop_loss_distance=0.02,
        historical_performance={
            'win_rate': 0.6,
            'avg_win': 0.025,
            'avg_loss': 0.015
        }
    )
    
    print(f"Position Size: {result.position_size:.3f} ({result.position_size*100:.1f}%)")
    print(f"Risk Amount: ${result.risk_amount:.2f}")
    print(f"Max Loss: ${result.max_loss:.2f}")
    print(f"Method: {result.method}")
