#!/usr/bin/env python3
"""
Endpoint Registry - Mathematical endpoint management
Zero-configuration endpoint discovery
Statistical endpoint performance tracking
"""

import time
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging

class EndpointType(Enum):
    """API endpoint types with mathematical classification."""
    MARKET_DATA = "market_data"
    OPTIONS_DATA = "options_data" 
    ANALYSIS = "analysis"
    SYSTEM = "system"

@dataclass
class EndpointMetadata:
    """Endpoint metadata with performance tracking."""
    name: str
    endpoint_type: EndpointType
    method: Callable
    required_params: List[str]
    optional_params: List[str]
    response_type: str
    cache_ttl: float
    rate_limit_tier: str
    
    # Performance metrics
    call_count: int = 0
    total_response_time: float = 0.0
    error_count: int = 0
    last_called: Optional[float] = None
    
    def record_call(self, response_time: float, success: bool):
        """Record endpoint call statistics."""
        self.call_count += 1
        self.total_response_time += response_time
        self.last_called = time.time()
        
        if not success:
            self.error_count += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Calculate performance statistics."""
        avg_response_time = (
            self.total_response_time / self.call_count 
            if self.call_count > 0 else 0
        )
        
        error_rate = (
            self.error_count / self.call_count 
            if self.call_count > 0 else 0
        )
        
        return {
            "name": self.name,
            "type": self.endpoint_type.value,
            "call_count": self.call_count,
            "avg_response_time_ms": avg_response_time * 1000,
            "error_rate": error_rate,
            "last_called": self.last_called,
            "response_type": self.response_type
        }

class EndpointRegistry:
    """Central registry for API endpoints."""
    
    def __init__(self):
        """Initialize endpoint registry."""
        self.endpoints: Dict[str, EndpointMetadata] = {}
        self.logger = logging.getLogger(__name__)
        self._register_default_endpoints()
    
    def _register_default_endpoints(self):
        """Register standard endpoints."""
        default_endpoints = [
            {
                "name": "get_spot_price",
                "endpoint_type": EndpointType.MARKET_DATA,
                "required_params": ["ticker"],
                "optional_params": [],
                "response_type": "float",
                "cache_ttl": 60.0,
                "rate_limit_tier": "basic"
            },
            {
                "name": "get_options_chain", 
                "endpoint_type": EndpointType.OPTIONS_DATA,
                "required_params": ["ticker"],
                "optional_params": ["expiry", "option_type"],
                "response_type": "DataFrame",
                "cache_ttl": 300.0,
                "rate_limit_tier": "professional"
            },
            {
                "name": "get_market_depth",
                "endpoint_type": EndpointType.MARKET_DATA,
                "required_params": ["ticker"],
                "optional_params": ["levels"],
                "response_type": "DataFrame", 
                "cache_ttl": 30.0,
                "rate_limit_tier": "professional"
            },
            {
                "name": "get_price_data",
                "endpoint_type": EndpointType.MARKET_DATA,
                "required_params": ["ticker"],
                "optional_params": ["timespan", "multiplier", "from_date", "to_date"],
                "response_type": "DataFrame",
                "cache_ttl": 3600.0,
                "rate_limit_tier": "basic"
            },
            {
                "name": "analyze_liquidity",
                "endpoint_type": EndpointType.ANALYSIS,
                "required_params": ["ticker"],
                "optional_params": ["timeframe"],
                "response_type": "dict",
                "cache_ttl": 900.0,
                "rate_limit_tier": "professional"
            },
            {
                "name": "health_check",
                "endpoint_type": EndpointType.SYSTEM,
                "required_params": [],
                "optional_params": [],
                "response_type": "dict",
                "cache_ttl": 0.0,
                "rate_limit_tier": "free"
            }
        ]
        
        for endpoint_config in default_endpoints:
            self.register_endpoint(**endpoint_config)
    
    def register_endpoint(self, name: str, endpoint_type: EndpointType, 
                         required_params: List[str], optional_params: List[str],
                         response_type: str, cache_ttl: float, 
                         rate_limit_tier: str, method: Optional[Callable] = None):
        """Register new endpoint."""
        metadata = EndpointMetadata(
            name=name,
            endpoint_type=endpoint_type,
            method=method,
            required_params=required_params,
            optional_params=optional_params,
            response_type=response_type,
            cache_ttl=cache_ttl,
            rate_limit_tier=rate_limit_tier
        )
        
        self.endpoints[name] = metadata
        self.logger.info(f"Registered endpoint: {name}")
    
    def get_endpoint(self, name: str) -> Optional[EndpointMetadata]:
        """Get endpoint metadata."""
        return self.endpoints.get(name)
    
    def list_endpoints(self, endpoint_type: Optional[EndpointType] = None) -> List[str]:
        """List available endpoints."""
        if endpoint_type:
            return [
                name for name, metadata in self.endpoints.items()
                if metadata.endpoint_type == endpoint_type
            ]
        return list(self.endpoints.keys())
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get comprehensive registry statistics."""
        endpoint_stats = {}
        total_calls = 0
        total_errors = 0
        
        for name, metadata in self.endpoints.items():
            stats = metadata.get_performance_stats()
            endpoint_stats[name] = stats
            total_calls += metadata.call_count
            total_errors += metadata.error_count
        
        overall_error_rate = total_errors / total_calls if total_calls > 0 else 0
        
        return {
            "total_endpoints": len(self.endpoints),
            "total_calls": total_calls,
            "overall_error_rate": overall_error_rate,
            "endpoints": endpoint_stats,
            "types": {
                endpoint_type.value: len([
                    e for e in self.endpoints.values() 
                    if e.endpoint_type == endpoint_type
                ]) for endpoint_type in EndpointType
            }
        }

# Global registry instance
_endpoint_registry = None

def get_endpoint_registry() -> EndpointRegistry:
    """Get global endpoint registry instance."""
    global _endpoint_registry
    if _endpoint_registry is None:
        _endpoint_registry = EndpointRegistry()
    return _endpoint_registry
