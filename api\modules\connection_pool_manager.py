#!/usr/bin/env python3
"""
Connection Pool Manager for API Robustness

This module provides connection pool management to prevent "connection pool is full" errors
by properly managing HTTP connections, implementing connection limits, and providing
connection cleanup mechanisms.
"""

import logging
import time
import threading
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from urllib3.poolmanager import PoolManager
from typing import Dict, Any, Optional, List
import weakref
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class ConnectionPoolManager:
    """
    Manages HTTP connection pools to prevent connection pool exhaustion.
    
    Features:
    - Configurable pool sizes
    - Connection cleanup
    - Pool monitoring
    - Automatic pool recycling
    """
    
    def __init__(self, 
                 pool_connections: int = 10,
                 pool_maxsize: int = 20,
                 max_retries: int = 3,
                 pool_block: bool = False,
                 connection_timeout: int = 30,
                 read_timeout: int = 60):
        """
        Initialize connection pool manager.
        
        Args:
            pool_connections: Number of connection pools to cache
            pool_maxsize: Maximum number of connections in each pool
            max_retries: Maximum number of retries per request
            pool_block: Whether to block when pool is full
            connection_timeout: Connection timeout in seconds
            read_timeout: Read timeout in seconds
        """
        self.pool_connections = pool_connections
        self.pool_maxsize = pool_maxsize
        self.max_retries = max_retries
        self.pool_block = pool_block
        self.connection_timeout = connection_timeout
        self.read_timeout = read_timeout
        
        # Track active sessions
        self._active_sessions: Dict[str, requests.Session] = {}
        self._session_lock = threading.RLock()
        self._cleanup_thread = None
        self._stop_cleanup = threading.Event()
        
        # Pool statistics
        self.stats = {
            'sessions_created': 0,
            'sessions_closed': 0,
            'connections_active': 0,
            'pool_full_errors': 0,
            'cleanup_runs': 0
        }
        
        logger.info(f"ConnectionPoolManager initialized: pool_size={pool_maxsize}, "
                   f"pool_connections={pool_connections}, timeout={connection_timeout}s")
    
    def create_session(self, session_id: Optional[str] = None) -> requests.Session:
        """
        Create a new HTTP session with proper connection pool configuration.
        
        Args:
            session_id: Optional session identifier
            
        Returns:
            Configured requests.Session object
        """
        if session_id is None:
            session_id = f"session_{int(time.time())}_{threading.current_thread().ident}"
        
        with self._session_lock:
            # Check if session already exists
            if session_id in self._active_sessions:
                logger.debug(f"Reusing existing session: {session_id}")
                return self._active_sessions[session_id]
            
            # Create new session
            session = requests.Session()
            
            # Configure retry strategy
            retry_strategy = Retry(
                total=self.max_retries,
                status_forcelist=[429, 500, 502, 503, 504],
                method_whitelist=["HEAD", "GET", "OPTIONS", "POST"],
                backoff_factor=1,
                raise_on_status=False
            )
            
            # Configure HTTP adapter with connection pooling
            adapter = HTTPAdapter(
                pool_connections=self.pool_connections,
                pool_maxsize=self.pool_maxsize,
                pool_block=self.pool_block,
                max_retries=retry_strategy
            )
            
            # Mount adapter for both HTTP and HTTPS
            session.mount("http://", adapter)
            session.mount("https://", adapter)
            
            # Set timeouts
            session.timeout = (self.connection_timeout, self.read_timeout)
            
            # Store session
            self._active_sessions[session_id] = session
            self.stats['sessions_created'] += 1
            
            logger.debug(f"Created new session: {session_id}")
            return session
    
    def close_session(self, session_id: str) -> bool:
        """
        Close and remove a session.
        
        Args:
            session_id: Session identifier
            
        Returns:
            True if session was found and closed
        """
        with self._session_lock:
            if session_id in self._active_sessions:
                session = self._active_sessions[session_id]
                try:
                    session.close()
                    del self._active_sessions[session_id]
                    self.stats['sessions_closed'] += 1
                    logger.debug(f"Closed session: {session_id}")
                    return True
                except Exception as e:
                    logger.warning(f"Error closing session {session_id}: {e}")
                    return False
            return False
    
    def close_all_sessions(self):
        """Close all active sessions."""
        with self._session_lock:
            session_ids = list(self._active_sessions.keys())
            for session_id in session_ids:
                self.close_session(session_id)
            logger.info(f"Closed {len(session_ids)} sessions")
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get session statistics.
        
        Returns:
            Dictionary with session statistics
        """
        with self._session_lock:
            return {
                **self.stats,
                'active_sessions': len(self._active_sessions),
                'session_ids': list(self._active_sessions.keys())
            }
    
    def start_cleanup_thread(self, cleanup_interval: int = 300):
        """
        Start background thread for connection cleanup.
        
        Args:
            cleanup_interval: Cleanup interval in seconds
        """
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            logger.warning("Cleanup thread already running")
            return
        
        self._stop_cleanup.clear()
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_worker,
            args=(cleanup_interval,),
            daemon=True,
            name="ConnectionPoolCleanup"
        )
        self._cleanup_thread.start()
        logger.info(f"Started connection pool cleanup thread (interval: {cleanup_interval}s)")
    
    def stop_cleanup_thread(self):
        """Stop the cleanup thread."""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._stop_cleanup.set()
            self._cleanup_thread.join(timeout=5)
            logger.info("Stopped connection pool cleanup thread")
    
    def _cleanup_worker(self, interval: int):
        """Background worker for connection cleanup."""
        while not self._stop_cleanup.wait(interval):
            try:
                self._cleanup_stale_sessions()
                self.stats['cleanup_runs'] += 1
            except Exception as e:
                logger.error(f"Error in cleanup worker: {e}")
    
    def _cleanup_stale_sessions(self):
        """Clean up stale or unused sessions."""
        with self._session_lock:
            # For now, just log session count
            # In a more advanced implementation, we could track session usage
            # and close sessions that haven't been used recently
            active_count = len(self._active_sessions)
            if active_count > 0:
                logger.debug(f"Connection pool cleanup: {active_count} active sessions")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - cleanup all sessions."""
        self.stop_cleanup_thread()
        self.close_all_sessions()


class PooledAPIClient:
    """
    API client that uses connection pool manager.
    """
    
    def __init__(self, base_url: str, pool_manager: Optional[ConnectionPoolManager] = None):
        """
        Initialize pooled API client.
        
        Args:
            base_url: Base URL for API
            pool_manager: Connection pool manager (creates default if None)
        """
        self.base_url = base_url.rstrip('/')
        self.pool_manager = pool_manager or ConnectionPoolManager()
        self.session_id = f"api_client_{int(time.time())}"
        self.session = self.pool_manager.create_session(self.session_id)
        
        logger.info(f"PooledAPIClient initialized for {base_url}")
    
    def request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """
        Make HTTP request using pooled connection.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            **kwargs: Additional request parameters
            
        Returns:
            Response object
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except Exception as e:
            # Check if it's a connection pool error
            if "pool is full" in str(e).lower() or "connection pool" in str(e).lower():
                self.pool_manager.stats['pool_full_errors'] += 1
                logger.warning(f"Connection pool error detected: {e}")
                
                # Try to create a new session
                try:
                    self.pool_manager.close_session(self.session_id)
                    self.session = self.pool_manager.create_session(self.session_id)
                    response = self.session.request(method, url, **kwargs)
                    logger.info("Successfully recovered from connection pool error")
                    return response
                except Exception as retry_error:
                    logger.error(f"Failed to recover from connection pool error: {retry_error}")
                    raise
            else:
                raise
    
    def get(self, endpoint: str, **kwargs) -> requests.Response:
        """Make GET request."""
        return self.request('GET', endpoint, **kwargs)
    
    def post(self, endpoint: str, **kwargs) -> requests.Response:
        """Make POST request."""
        return self.request('POST', endpoint, **kwargs)
    
    def close(self):
        """Close the client and its session."""
        self.pool_manager.close_session(self.session_id)


# Global connection pool manager instance
_global_pool_manager = None
_pool_manager_lock = threading.Lock()

def get_global_pool_manager() -> ConnectionPoolManager:
    """Get or create global connection pool manager."""
    global _global_pool_manager
    
    with _pool_manager_lock:
        if _global_pool_manager is None:
            _global_pool_manager = ConnectionPoolManager(
                pool_connections=15,  # Increased from default
                pool_maxsize=30,      # Increased from default
                max_retries=3,
                pool_block=False,     # Don't block when pool is full
                connection_timeout=30,
                read_timeout=60
            )
            _global_pool_manager.start_cleanup_thread(cleanup_interval=300)  # 5 minutes
            logger.info("Created global connection pool manager")
        
        return _global_pool_manager

def create_pooled_client(base_url: str) -> PooledAPIClient:
    """
    Create a pooled API client using the global pool manager.
    
    Args:
        base_url: Base URL for the API
        
    Returns:
        PooledAPIClient instance
    """
    pool_manager = get_global_pool_manager()
    return PooledAPIClient(base_url, pool_manager)
