{"test_summary": {"total_tests": 16, "passed_tests": 14, "partial_tests": 1, "failed_tests": 1, "skipped_tests": 0, "effective_tests": 16, "success_rate": 87.5, "total_execution_time": 19.633, "timestamp": "2025-06-22T22:52:40.232910"}, "detailed_results": {"Enhanced Data Agent": {"status": "PASS", "execution_time": 16.828, "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": true, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Signal Generator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Chart Generator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Risk Guard": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Order Router": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Anomaly Detector": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "IV Dynamics": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Flow Physics": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Math Validator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Output Coordinator": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Data Ingestion": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Schwab Data": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": true, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}, "Auto Broker Adapter": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": false, "has_real_time_agent": false, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": false, "has_real_time_attribute": false, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>, MCP: <PERSON><PERSON><PERSON>"}, "Signal Quality": {"status": "PARTIAL", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: True, Agent: True, MCP: True"}, "Agent Zero": {"status": "FAIL", "execution_time": "N/A", "has_real_time_capability": false, "has_real_time_agent": false, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": false, "has_real_time_attribute": false, "has_schwab_reference": false, "has_mcp_reference": false}, "details": "Real-time: <PERSON><PERSON><PERSON>, Agent: <PERSON><PERSON><PERSON>, MCP: <PERSON><PERSON><PERSON>"}, "Order Router V2": {"status": "PASS", "execution_time": "N/A", "has_real_time_capability": true, "has_real_time_agent": true, "data_test_passed": false, "mcp_patterns": {"has_enhanced_data_import": true, "has_real_time_attribute": true, "has_schwab_reference": false, "has_mcp_reference": true}, "details": "Real-time: True, Agent: True, MCP: True"}}}