task_id: C-02
name: IV Dynamics & ROC Detection Agent
version: 1.0.0
description: >
  Advanced implied volatility analysis focusing on rate-of-change patterns,
  slope dynamics, and regime shift detection. Specializes in IV momentum,
  acceleration, and cross-asset volatility relationships.

inputs:
  - iv_timeseries_data      # IV data across strikes and expirations
  - underlying_price_data   # Spot price for correlation analysis
  - vol_surface_data        # Complete volatility surface

outputs:
  files:
    - path: iv_analysis/{{date}}/{{ticker}}_iv_dynamics.json
      must_exist: true
    - path: iv_analysis/{{date}}/{{ticker}}_vol_regime.json
      must_exist: true

success_criteria:
  perf_budget:
    max_runtime_ms: 75
  detection_accuracy_min: 0.88
  regime_prediction_accuracy: 0.75

validation:
  iv_roc_calculation: true
  slope_analysis: true
  regime_detection: true
  cross_correlation: true

iv_roc_analysis:
  timeframes:
    - 1_period: "Intraday momentum"
    - 5_period: "Short-term trend"
    - 21_period: "Monthly regime"
  thresholds:
    extreme_roc: 0.15      # 15% IV change threshold
    acceleration: 0.05     # ROC of ROC threshold
    regime_shift: 2.5      # Z-score for regime detection

slope_analysis:
  measurements:
    term_structure: "IV slope across time to expiration"
    strike_skew: "IV slope across moneyness"
    temporal_slope: "IV slope evolution over time"
  thresholds:
    steep_term_structure: 0.08    # Steep contango/backwardation
    extreme_skew: 0.15           # Put/call IV differential
    slope_acceleration: 0.03      # Rate of slope change

regime_detection:
  indicators:
    - iv_rank_momentum
    - cross_asset_correlation
    - term_structure_inversion
    - skew_collapse
    - volatility_clustering
  confidence_levels:
    high: 0.85
    medium: 0.70
    low: 0.55

interpretations:
  iv_roc_high: "IV momentum accelerating - volatility expansion incoming"
  iv_roc_negative: "IV collapsing - potential volatility compression"
  slope_steepening: "Term structure steepening - event risk pricing"
  slope_flattening: "Term structure flattening - normalization phase"
  skew_extreme: "Put premium extreme - tail risk pricing"
  regime_shift_bullish: "Volatility regime shifting lower - risk-on environment"
  regime_shift_bearish: "Volatility regime shifting higher - risk-off environment"
  cross_correlation_breakdown: "Asset correlations breaking down - market stress"

dependencies:
  - pandas>=1.5.0
  - numpy>=1.21.0
  - scipy>=1.9.0
