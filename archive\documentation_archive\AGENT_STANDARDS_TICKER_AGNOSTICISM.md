# CORE Agent Standards - Ticker Agnosticism Requirement

##  **CRITICAL MANDATORY STANDARD**

**Effective Date**: June 24, 2025  
**Compliance**: 100% Required for All Agents  
**Status**: Established and Validated  

---

##  **TICKER AGNOSTICISM STANDARD**

### **Mandatory Requirement**
All CORE agents **MUST** be 100% ticker-agnostic with **ZERO hardcoding** of:
- Specific ticker symbols (AAPL, TSLA, SPY, etc.)
- Market-specific parameters (rates, dividends, etc.) 
- Data paths or directory structures
- Market assumptions or biases

### **Compliance Definition**
An agent is considered **ticker-agnostic compliant** when:
1.  **Works identically** with any ticker symbol
2.  **Same mathematical precision** across all markets
3.  **Configurable parameters** for different environments
4.  **Zero hardcoded assumptions** about specific stocks or markets

---

##  **IMPLEMENTATION REQUIREMENTS**

### **1. Configuration-Based Design**
```python
# REQUIRED: Agent must accept configuration
class AgentClass(BaseAgent):
    def __init__(self, agent_id: str = None, config: dict = None):
        super().__init__(agent_id)
        self.config = config or {}
        
        # All market parameters must be configurable
        self.risk_free_rate = self.config.get('risk_free_rate', DEFAULT_RATE)
        self.dividend_yield = self.config.get('dividend_yield', DEFAULT_DIVIDEND)
        self.data_directory = self.config.get('data_directory', DEFAULT_PATH)
```

### **2. Parameter Hierarchy**
**REQUIRED**: Three-tier parameter inheritance:
```
1. Input Data (Highest Priority)
    option.get('riskFreeRate') or market_data.get('rate')
2. Agent Configuration (Medium Priority)  
    self.config.get('risk_free_rate', DEFAULT)
3. Constants Defaults (Lowest Priority)
    DEFAULT_RISK_FREE_RATE from constants
```

### **3. Constants File Standards**
```python
# constants.py - REQUIRED structure
# Market parameter defaults (ticker-agnostic)
DEFAULT_RISK_FREE_RATE = 0.045  # Configurable default
DEFAULT_DIVIDEND_YIELD = 0.0    # Configurable default
DEFAULT_VOLATILITY = 0.20       # Market baseline

# NO hardcoded ticker-specific values allowed
```

### **4. Factory Pattern Support**
```python
# REQUIRED: Factory function must support configuration
def create_agent(agent_type: str, config: dict = None):
    if config:
        return AGENT_REGISTRY[agent_type](config=config)
    else:
        return AGENT_REGISTRY[agent_type]()

# Usage examples
us_agent = create_agent('AgentType')  # US defaults
eu_agent = create_agent('AgentType', config={'risk_free_rate': 0.02})
```

---

##  **VALIDATION REQUIREMENTS**

### **Mandatory Testing**
Every agent **MUST** pass these ticker agnosticism tests:

#### **1. Multi-Market Testing**
```python
# Test with different market configurations
test_markets = [
    None,  # Default (US)
    {'risk_free_rate': 0.02, 'dividend_yield': 0.025},  # EU
    {'risk_free_rate': 0.005, 'dividend_yield': 0.0},   # JP
    {'risk_free_rate': 0.08, 'dividend_yield': 0.01}    # Emerging
]

for config in test_markets:
    agent = AgentClass(config=config)
    result = agent.execute_task(task)
    assert result.status == 'completed'  # Must work with all configs
```

#### **2. Ticker Format Testing**
```python
# Test with various ticker formats
test_tickers = [
    'AAPL',        # Standard US
    'ASML.AS',     # European
    'NESN.SW',     # Swiss
    'A',           # Single letter
    'ABCDE',       # Long ticker
    '123',         # Numeric
    'TEST-123',    # With dash
    'STOCK.TO'     # Toronto format
]

for ticker in test_tickers:
    result = agent.execute_with_ticker(ticker)
    assert result.quality_score > 0.95  # Same quality required
```

#### **3. Code Scan Validation**
```bash
# REQUIRED: Automated hardcoding detection
grep -r "AAPL\|TSLA\|SPY\|MSFT" agent_file.py  # Must return empty
grep -r "0\.04[0-9]" agent_file.py  # Check for hardcoded rates
grep -r "data/.*/" agent_file.py  # Check for hardcoded paths
```

---

##  **COMPLIANCE EXAMPLES**

### ** COMPLIANT Implementation (B-01 Standard)**
```python
class GreekEnhancementAgent(BaseAgent):
    def __init__(self, config: dict = None):
        self.config = config or {}
        # All parameters configurable
        self.default_risk_free_rate = self.config.get('risk_free_rate', DEFAULT_RISK_FREE_RATE)
        self.data_directory = self.config.get('data_directory', 'data/features')
    
    def calculate_greeks(self, ticker, options_data):
        # ticker is just a string identifier - no special handling
        for option in options_data:
            rate = option.get('risk_free_rate', self.default_risk_free_rate)
            # Use ticker generically, no hardcoded assumptions
```

### ** NON-COMPLIANT Examples**
```python
# FORBIDDEN: Hardcoded ticker logic
if ticker == 'AAPL':
    risk_free_rate = 0.045
elif ticker == 'TSLA':
    risk_free_rate = 0.04

# FORBIDDEN: Hardcoded paths
data_path = f"data/us_equities/{ticker}_data.json"

# FORBIDDEN: Hardcoded market assumptions
dividend_yield = 0.02 if ticker in NYSE_STOCKS else 0.0
```

---

##  **DEPLOYMENT CONFIGURATIONS**

### **US Market (Default)**
```python
us_agent = AgentClass()  # Uses DEFAULT_RISK_FREE_RATE = 0.045
```

### **European Market**
```python
eu_agent = AgentClass(config={
    'risk_free_rate': 0.02,      # ECB rate
    'dividend_yield': 0.025,     # EU dividend environment
    'data_directory': 'eu_data'  # European data storage
})
```

### **Japanese Market**
```python
jp_agent = AgentClass(config={
    'risk_free_rate': 0.005,     # BOJ rate
    'dividend_yield': 0.0,       # Low dividend environment
    'data_directory': 'jp_data'  # Japanese data storage
})
```

### **Development Environment**
```python
dev_agent = AgentClass(config={
    'data_directory': 'test_data',
    'risk_free_rate': 0.045
})
```

---

##  **ENFORCEMENT AND COMPLIANCE**

### **Mandatory Checklist**
Before any agent deployment, verify:
- [ ] Works with any ticker symbol (including edge cases)
- [ ] All market parameters configurable via agent config  
- [ ] No hardcoded rates, paths, or ticker-specific logic
- [ ] Passes multi-market configuration testing
- [ ] Maintains same quality across all ticker/market combinations
- [ ] Code scan shows zero hardcoded assumptions
- [ ] Factory pattern supports configuration parameter

### **Compliance Verification Tools**
```bash
# REQUIRED validation scripts
py test_ticker_agnostic.py          # Multi-market testing
py validate_agent_compliance.py     # Code scanning
py test_edge_case_tickers.py        # Edge case validation
```

### **Non-Compliance Consequences**
-  **Deployment blocked** until compliance achieved
-  **Code review rejection** for hardcoded assumptions
-  **Integration testing failure** if ticker-specific logic detected

---

##  **BENEFITS OF COMPLIANCE**

### **Operational Benefits**
-  **Global Deployment**: Same code works in any market
-  **Environment Flexibility**: Dev/test/prod separation
-  **Market Expansion**: Easy addition of new markets
-  **Configuration Management**: Centralized parameter control

### **Technical Benefits**
-  **Code Reusability**: One implementation for all markets
-  **Testing Efficiency**: Same test suite for all configurations  
-  **Maintenance Reduction**: No ticker-specific code branches
-  **Quality Assurance**: Consistent behavior across markets

---

##  **IMPLEMENTATION TIMELINE**

### **Immediate Requirements** (Effective Now)
- All new agents MUST be ticker-agnostic from day one
- Existing agents MUST be audited for compliance
- Configuration framework MUST be implemented

### **Validation Requirements** (Mandatory)
- Multi-market testing MUST pass before deployment
- Code scanning MUST show zero hardcoded assumptions  
- Factory pattern MUST support configuration

---

##  **CONCLUSION**

**Ticker agnosticism is now a CRITICAL MANDATORY STANDARD** for all CORE agents. This requirement ensures:

- **Universal Deployment**: Same code works globally
- **Mathematical Purity**: No market biases or assumptions
- **Configuration Flexibility**: Environment-specific parameters
- **Quality Consistency**: Same precision across all markets

**All future agent implementations MUST comply with this standard.**

---

**Standard Established**: June 24, 2025  
**Compliance Required**: 100% for all agents  
**Validation**: B-01 Greek Enhancement Agent (exemplary implementation)  
**Status**:  **ACTIVE AND ENFORCED**

*Ticker Agnosticism - Critical Standard for Universal Market Deployment*
