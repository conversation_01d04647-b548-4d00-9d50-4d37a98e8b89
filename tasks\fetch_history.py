#!/usr/bin/env python3
"""
B-01: Historical Data Fetcher
Fetches historical bar data for backtesting using existing data ingestion agent
"""

import os
import sys
import logging
import argparse
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from agents.data_ingestion_agent import LiveDataGatewayAgent


class HistoricalDataFetcher:
    """Handles historical data fetching for backtesting"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.data_agent = LiveDataGatewayAgent()
    
    def run(self, ticker, start_date="2022-01-01", end_date=None, source="polygon"):
        """
        Fetch historical data for backtesting
        
        Args:
            ticker (str): Stock symbol
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format (default: today)
            source (str): Data source ('polygon' or 'mcp')
        
        Returns:
            dict: Status and metadata about fetched data
        """
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")
        
        self.logger.info(f"Fetching historical data for {ticker} from {start_date} to {end_date}")
        
        try:
            # For now, use the existing data ingestion agent
            # In production, this would be extended to handle date ranges
            result = self.data_agent.execute(
                ticker_list=[ticker],
                source=source,
                bar_tf="1"
            )
            
            if result["status"] == "OK" and ticker in result["successful_tickers"]:
                # Move data to history directory with proper naming
                self._move_to_history(ticker, result)
                
                return {
                    "status": "SUCCESS",
                    "ticker": ticker,
                    "start_date": start_date,
                    "end_date": end_date,
                    "source": source,
                    "output_file": f"data/history/{ticker}_bars.parquet"
                }
            else:
                raise Exception(f"Data ingestion failed for {ticker}")
                
        except Exception as e:
            self.logger.error(f"Failed to fetch historical data for {ticker}: {e}")
            return {
                "status": "ERROR",
                "ticker": ticker,
                "error": str(e)
            }
    
    def _move_to_history(self, ticker, ingestion_result):
        """Move fetched data from live to history directory"""
        try:
            meta = ingestion_result["meta"][ticker]
            source_bars_file = meta["bars_file"]
            
            # Create target path
            target_dir = Path("data/history")
            target_dir.mkdir(parents=True, exist_ok=True)
            target_file = target_dir / f"{ticker}_bars.parquet"
            
            # Read and save to history location
            df = pd.read_parquet(source_bars_file)
            df.to_parquet(target_file, index=False)
            
            self.logger.info(f"Moved {len(df)} bars to {target_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to move data to history: {e}")
            raise


def validate_date_format(date_string):
    """Validate date string format"""
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def main():
    """Command line interface for historical data fetching"""
    parser = argparse.ArgumentParser(description="Fetch historical data for backtesting")
    parser.add_argument("--ticker", required=True, help="Stock ticker symbol")
    parser.add_argument("--start", default="2022-01-01", help="Start date (YYYY-MM-DD)")
    parser.add_argument("--end", help="End date (YYYY-MM-DD), default: today")
    parser.add_argument("--source", choices=["polygon", "mcp"], default="polygon", 
                       help="Data source")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    # Setup logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Validate inputs
    if not validate_date_format(args.start):
        print(f"ERROR: Invalid start date format: {args.start}")
        return 1
    
    if args.end and not validate_date_format(args.end):
        print(f"ERROR: Invalid end date format: {args.end}")
        return 1
    
    # Execute fetching
    fetcher = HistoricalDataFetcher()
    result = fetcher.run(
        ticker=args.ticker.upper(),
        start_date=args.start,
        end_date=args.end,
        source=args.source
    )
    
    # Output results
    if result["status"] == "SUCCESS":
        print("SUCCESS: Historical data fetch completed")
        print(f"Ticker: {result['ticker']}")
        print(f"Date Range: {result['start_date']} to {result['end_date']}")
        print(f"Source: {result['source']}")
        print(f"Output: {result['output_file']}")
        return 0
    else:
        print(f"ERROR: {result['error']}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
