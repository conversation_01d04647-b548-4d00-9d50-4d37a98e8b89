# Commands Tree
from .command_processor.core_processor import CommandProcessor, Command, CommandResult, CommandPriority, CommandStatus
from .command_registry.registry_manager import CommandRegistry, CommandDefinition, CommandCategory, get_command_registry
from .validation_layer.command_validator import CommandValidator, ValidationRule, ValidationResult
from .security_layer.security_manager import SecurityManager, SecurityPolicy, AccessLevel
from .execution_engine.command_executor import CommandExecutor, ExecutionContext, ExecutionResult
from .monitoring_hooks.command_monitor import CommandMonitor, CommandMetrics, PerformanceTracker

__all__ = [
    # Command Processing
    'CommandProcessor',
    'Command',
    'CommandResult', 
    'CommandPriority',
    'CommandStatus',
    
    # Command Registry
    'CommandRegistry',
    'CommandDefinition',
    'CommandCategory',
    'get_command_registry',
    
    # Validation Layer
    'CommandValidator',
    'ValidationRule',
    'ValidationResult',
    
    # Security Layer
    'SecurityManager',
    'SecurityPolicy',
    'AccessLevel',
    
    # Execution Engine
    'CommandExecutor',
    'ExecutionContext',
    'ExecutionResult',
    
    # Monitoring Hooks
    'CommandMonitor',
    'CommandMetrics',
    'PerformanceTracker'
]

# Tree Structure Information
TREE_INFO = {
    'name': 'Commands Tree',
    'version': '1.0',
    'components': {
        'command_processor': 'Core command parsing and processing engine',
        'command_registry': 'Central registry for all available commands',
        'validation_layer': 'Command validation and parameter checking',
        'security_layer': 'Security policies and access control',
        'execution_engine': 'Command execution with context management',
        'monitoring_hooks': 'Performance monitoring and metrics collection'
    },
    'performance': {
        'command_parsing_target': '< 1ms',
        'validation_time_target': '< 2ms',
        'security_check_target': '< 1ms',
        'execution_overhead_target': '< 0.5ms'
    },
    'features': [
        'Natural language command parsing',
        'Comprehensive command registry',
        'Multi-layer validation system',
        'Role-based security model',
        'Execution context management',
        'Real-time performance monitoring',
        'Command usage analytics',
        'Audit trail generation',
        'Error handling and recovery'
    ]
}
