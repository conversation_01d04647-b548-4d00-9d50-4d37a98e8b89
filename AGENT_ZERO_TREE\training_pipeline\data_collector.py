#!/usr/bin/env python3
"""
Agent Zero Training Data Collector
Automated collection and management of training data
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class TrainingRecord:
    """Structured training data record"""
    record_id: str
    timestamp: datetime
    signal_data: Dict[str, Any]
    math_data: Dict[str, Any] 
    market_context: Dict[str, Any]
    decision: Dict[str, Any]
    outcome: Optional[float]
    features: Dict[str, float]
    labels: Dict[str, Any]

class TrainingDataCollector:
    """
    Training Data Collection System
    
    Automated collection, validation, and storage of training data
    for Agent Zero model improvement
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        
        # Setup training directory
        self.training_dir = Path(self.config.get('training_dir', 'training_logs/AgentZero'))
        self.training_dir.mkdir(parents=True, exist_ok=True)
        
        # Collection configuration
        self.max_records_per_file = self.config.get('max_records_per_file', 1000)
        self.auto_archive_days = self.config.get('auto_archive_days', 30)
        
        # Data validation rules
        self.validation_rules = {
            'min_confidence': 0.0,
            'max_confidence': 1.0,
            'required_signal_fields': ['confidence', 'strength'],
            'required_math_fields': ['accuracy_score', 'precision']
        }
        
        # Performance tracking
        self.records_collected = 0
        self.validation_failures = 0
        
    def collect_decision_data(self, signal_data: Dict, math_data: Dict, 
                            market_context: Dict, decision: Dict) -> str:
        """
        Collect decision data for training
        
        Returns record ID for tracking
        """
        try:
            # Generate unique record ID
            record_id = self._generate_record_id()
            
            # Validate input data
            if not self._validate_input_data(signal_data, math_data, decision):
                self.validation_failures += 1
                logger.warning(f"Training data validation failed for record {record_id}")
                return record_id
            
            # Extract features
            features = self._extract_features(signal_data, math_data, market_context)
            
            # Create labels
            labels = self._create_labels(decision)
            
            # Create training record
            record = TrainingRecord(
                record_id=record_id,
                timestamp=datetime.now(),
                signal_data=signal_data,
                math_data=math_data,
                market_context=market_context,
                decision=decision,
                outcome=None,  # Will be updated when outcome is known
                features=features,
                labels=labels
            )
            
            # Store record
            self._store_record(record)
            
            self.records_collected += 1
            logger.debug(f"Training data collected: {record_id}")
            
            return record_id
            
        except Exception as e:
            logger.error(f"Failed to collect training data: {e}")
            return ""
    
    def log_outcome(self, signal_data: Dict, math_data: Dict, decision: Dict, 
                   outcome: float, market_context: Optional[Dict] = None):
        """
        Log outcome for existing training record
        """
        try:
            # Find existing record by decision characteristics
            record_id = self._find_record_by_decision(decision)
            
            if record_id:
                self._update_record_outcome(record_id, outcome)
                logger.debug(f"Outcome logged for record {record_id}: {outcome}")
            else:
                # Create new record with outcome
                market_context = market_context or {}
                record_id = self.collect_decision_data(signal_data, math_data, market_context, decision)
                if record_id:
                    self._update_record_outcome(record_id, outcome)
                    
        except Exception as e:
            logger.error(f"Failed to log outcome: {e}")
    
    def _validate_input_data(self, signal_data: Dict, math_data: Dict, decision: Dict) -> bool:
        """Validate input data meets requirements"""
        
        try:
            # Check required signal fields
            for field in self.validation_rules['required_signal_fields']:
                if field not in signal_data:
                    return False
            
            # Check required math fields  
            for field in self.validation_rules['required_math_fields']:
                if field not in math_data:
                    return False
            
            # Check confidence bounds
            confidence = decision.get('confidence', 0.0)
            if not (self.validation_rules['min_confidence'] <= confidence <= self.validation_rules['max_confidence']):
                return False
            
            # Check action is valid
            action = decision.get('action', '')
            if action not in ['execute', 'hold', 'avoid']:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _extract_features(self, signal_data: Dict, math_data: Dict, market_context: Dict) -> Dict[str, float]:
        """Extract numerical features for ML training"""
        
        features = {}
        
        # Signal features
        features['signal_confidence'] = float(signal_data.get('confidence', 0.0))
        features['signal_strength'] = float(signal_data.get('strength', 0.0))
        
        # Math features
        features['math_accuracy'] = float(math_data.get('accuracy_score', 0.0))
        features['math_precision'] = float(math_data.get('precision', 0.01))
        
        # Market context features
        features['market_volatility'] = float(market_context.get('volatility', 0.02))
        features['trend_strength'] = float(market_context.get('trend_strength', 0.0))
        features['volume_profile'] = float(market_context.get('volume_profile', 1.0))
        
        # Execution recommendation encoding
        exec_rec = signal_data.get('execution_recommendation', 'hold')
        features['exec_rec_execute'] = 1.0 if exec_rec == 'execute' else 0.0
        features['exec_rec_hold'] = 1.0 if exec_rec == 'hold' else 0.0
        features['exec_rec_avoid'] = 1.0 if exec_rec == 'avoid' else 0.0
        
        return features
    
    def _create_labels(self, decision: Dict) -> Dict[str, Any]:
        """Create training labels from decision"""
        
        action = decision.get('action', 'hold')
        confidence = decision.get('confidence', 0.0)
        
        labels = {
            'action_execute': 1 if action == 'execute' else 0,
            'action_hold': 1 if action == 'hold' else 0,
            'action_avoid': 1 if action == 'avoid' else 0,
            'confidence_score': float(confidence),
            'high_confidence': 1 if confidence > 0.8 else 0,
            'low_confidence': 1 if confidence < 0.3 else 0
        }
        
        return labels
    
    def _generate_record_id(self) -> str:
        """Generate unique record ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        return f"training_{timestamp}_{self.records_collected:06d}"
    
    def _store_record(self, record: TrainingRecord):
        """Store training record to file"""
        
        # Determine file path
        date_str = record.timestamp.strftime("%Y%m%d")
        filename = f"training_data_{date_str}.jsonl"
        filepath = self.training_dir / filename
        
        # Convert record to JSON
        record_dict = asdict(record)
        record_dict['timestamp'] = record.timestamp.isoformat()
        
        # Append to file
        with open(filepath, 'a') as f:
            f.write(json.dumps(record_dict, default=str) + '\n')
    
    def _find_record_by_decision(self, decision: Dict) -> Optional[str]:
        """Find existing record by decision characteristics"""
        # Simplified implementation - in production would use proper indexing
        return None  # Placeholder
    
    def _update_record_outcome(self, record_id: str, outcome: float):
        """Update existing record with outcome"""
        # Simplified implementation - in production would update in-place
        logger.debug(f"Would update record {record_id} with outcome {outcome}")
    
    def get_training_stats(self) -> Dict[str, Any]:
        """Get training data collection statistics"""
        
        return {
            'records_collected': self.records_collected,
            'validation_failures': self.validation_failures,
            'success_rate': (self.records_collected / (self.records_collected + self.validation_failures)) 
                           if (self.records_collected + self.validation_failures) > 0 else 0.0,
            'training_dir': str(self.training_dir),
            'files_created': len(list(self.training_dir.glob('training_data_*.jsonl')))
        }
    
    def export_training_data(self, start_date: Optional[datetime] = None, 
                           end_date: Optional[datetime] = None) -> List[Dict]:
        """Export training data for ML model training"""
        
        training_data = []
        
        # Get all training files
        training_files = list(self.training_dir.glob('training_data_*.jsonl'))
        
        for file_path in training_files:
            try:
                with open(file_path, 'r') as f:
                    for line in f:
                        record = json.loads(line.strip())
                        
                        # Apply date filtering if specified
                        if start_date or end_date:
                            record_time = datetime.fromisoformat(record['timestamp'])
                            if start_date and record_time < start_date:
                                continue
                            if end_date and record_time > end_date:
                                continue
                        
                        training_data.append(record)
                        
            except Exception as e:
                logger.error(f"Error reading training file {file_path}: {e}")
        
        return training_data

if __name__ == "__main__":
    # Test training data collector
    collector = TrainingDataCollector()
    
    test_signal = {'confidence': 0.8, 'strength': 0.7, 'execution_recommendation': 'execute'}
    test_math = {'accuracy_score': 0.9, 'precision': 0.001}
    test_context = {'volatility': 0.02, 'trend_strength': 0.6}
    test_decision = {'action': 'execute', 'confidence': 0.85}
    
    record_id = collector.collect_decision_data(test_signal, test_math, test_context, test_decision)
    collector.log_outcome(test_signal, test_math, test_decision, 0.15)  # 15% return
    
    stats = collector.get_training_stats()
    print(f"Training data collected: {stats['records_collected']}")
    print(f"Success rate: {stats['success_rate']:.1%}")
