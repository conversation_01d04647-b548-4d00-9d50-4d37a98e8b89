#!/bin/bash
# API Key Rotation Script - Execute if secrets detected in git history

echo "=== API KEY ROTATION REQUIRED ==="
echo "Secrets detected in git history - rotating all keys"

# 1. Schwab API - Regenerate keys
echo "1. Log into https://developer.schwab.com/"
echo "2. Revoke current API key and secret"
echo "3. Generate new API credentials"
echo "4. Update .env file with new credentials"

# 2. Polygon API - Rotate key
echo "5. Log into https://polygon.io/dashboard"
echo "6. Regenerate API key"
echo "7. Update POLYGON_API_KEY in .env"

# 3. Tradier - New token
echo "8. Log into https://developer.tradier.com/"
echo "9. Generate new sandbox token"
echo "10. Update TRADIER_TOKEN in .env"

# 4. Clean git history (DESTRUCTIVE - use with caution)
echo "=== WARNING: Consider cleaning git history ==="
echo "git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch .env' --prune-empty --tag-name-filter cat -- --all"
echo "git push origin --force --all"

echo "=== MANUAL VERIFICATION ==="
echo "Test all API connections after rotation"
