"""
Real-Time Prediction Module

This module provides functionality for real-time prediction updates
in the liquidity analysis system.
"""

import sys
import os
from pathlib import Path

# Ensure package root is in sys.path for reliable imports
_package_root = Path(__file__).parent
while _package_root.name != "Liquidity_Sweep" and _package_root.parent != _package_root:
    _package_root = _package_root.parent
if str(_package_root) not in sys.path:
    sys.path.insert(0, str(_package_root))

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple, Callable
import logging
import time
from datetime import datetime
import threading
import queue
from collections import deque
import uuid

# Set up logger
logger = logging.getLogger(__name__)

# Internal imports
from ml_logging import get_logger
from liquidity_prediction import LiquidityPredictionEngine

# Setup logger
logger = get_logger('real_time_prediction')


class PredictionRequest:
    """Class for storing prediction requests."""
    
    def __init__(self, 
                request_id: str,
                data: Any,
                callback: Optional[Callable[[str, Dict[str, Any]], None]] = None,
                metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize prediction request.
        
        Args:
            request_id: Unique request ID
            data: Data for prediction
            callback: Optional callback function to call with results
            metadata: Optional metadata
        """
        self.request_id = request_id
        self.data = data
        self.callback = callback
        self.metadata = metadata or {}
        self.timestamp = datetime.now()


class PredictionResult:
    """Class for storing prediction results."""
    
    def __init__(self,
                request_id: str,
                predictions: Dict[str, Any],
                processing_time: float,
                metadata: Optional[Dict[str, Any]] = None):
        """
        Initialize prediction result.
        
        Args:
            request_id: Request ID
            predictions: Prediction results
            processing_time: Processing time in seconds
            metadata: Optional metadata
        """
        self.request_id = request_id
        self.predictions = predictions
        self.processing_time = processing_time
        self.metadata = metadata or {}
        self.timestamp = datetime.now()


class RealTimePredictionEngine:
    """
    Real-time prediction engine for liquidity analysis.
    
    This class provides methods for real-time prediction updates,
    including asynchronous processing and streaming predictions.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the real-time prediction engine.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or {}
        
        # Set default configuration
        self.batch_size = self.config.get('batch_size', 10)
        self.max_queue_size = self.config.get('max_queue_size', 100)
        self.worker_count = self.config.get('worker_count', 1)
        self.update_interval = self.config.get('update_interval', 1.0)  # seconds
        self.cache_size = self.config.get('cache_size', 100)
        
        # Initialize prediction engine
        self.prediction_engine = LiquidityPredictionEngine(
            self.config.get('prediction_engine_config')
        )
        
        # Initialize request queue
        self.request_queue = queue.Queue(maxsize=self.max_queue_size)
        
        # Initialize result cache
        self.result_cache = {}
        self.result_timestamps = deque(maxlen=self.cache_size)
        
        # Initialize workers
        self.workers = []
        self.stop_event = threading.Event()
        
        # Initialize streaming
        self.streaming_callbacks = {}
        self.streaming_thread = None
        
        # Initialize update thread
        self.update_thread = None
        
        # Initialize metrics
        self.metrics = {
            'total_requests': 0,
            'total_predictions': 0,
            'avg_processing_time': 0.0,
            'queue_size_history': deque(maxlen=100),
            'processing_time_history': deque(maxlen=100)
        }
        
        logger.info("Real-time prediction engine initialized")
    
    def start(self):
        """Start the prediction engine."""
        if not self.prediction_engine.initialized:
            self.prediction_engine.initialize()
        
        # Start workers
        self.stop_event.clear()
        for i in range(self.worker_count):
            worker = threading.Thread(
                target=self._worker_loop,
                name=f"prediction-worker-{i}",
                daemon=True
            )
            worker.start()
            self.workers.append(worker)
        
        # Start update thread
        self.update_thread = threading.Thread(
            target=self._update_loop,
            name="prediction-updater",
            daemon=True
        )
        self.update_thread.start()
        
        logger.info(f"Started real-time prediction engine with {self.worker_count} workers")
    
    def stop(self):
        """Stop the prediction engine."""
        # Signal workers to stop
        self.stop_event.set()
        
        # Wait for workers to finish
        for worker in self.workers:
            worker.join(timeout=5.0)
        
        # Clear workers
        self.workers = []
        
        # Stop streaming
        self.streaming_callbacks = {}
        
        logger.info("Stopped real-time prediction engine")
    
    def predict(self, 
               data: Any, 
               callback: Optional[Callable[[str, Dict[str, Any]], None]] = None,
               metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Submit a prediction request.
        
        Args:
            data: Data for prediction
            callback: Optional callback function to call with results
            metadata: Optional metadata
            
        Returns:
            Request ID
        """
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Create request
        request = PredictionRequest(
            request_id=request_id,
            data=data,
            callback=callback,
            metadata=metadata
        )
        
        # Add to queue
        try:
            self.request_queue.put(request, block=False)
            self.metrics['total_requests'] += 1
            logger.debug(f"Queued prediction request {request_id}")
            return request_id
        except queue.Full:
            logger.warning("Prediction queue is full, request rejected")
            return None
    
    def get_result(self, request_id: str) -> Optional[PredictionResult]:
        """
        Get prediction result by request ID.
        
        Args:
            request_id: Request ID
            
        Returns:
            PredictionResult object or None if not found
        """
        return self.result_cache.get(request_id)
    
    def start_streaming(self, 
                       data_provider: Callable[[], Any],
                       callback: Callable[[str, Dict[str, Any]], None],
                       interval: float = 1.0,
                       stream_id: Optional[str] = None) -> str:
        """
        Start streaming predictions.
        
        Args:
            data_provider: Function that provides data for prediction
            callback: Function to call with prediction results
            interval: Update interval in seconds
            stream_id: Optional stream ID
            
        Returns:
            Stream ID
        """
        # Generate stream ID if not provided
        if stream_id is None:
            stream_id = str(uuid.uuid4())
        
        # Add to streaming callbacks
        self.streaming_callbacks[stream_id] = {
            'data_provider': data_provider,
            'callback': callback,
            'interval': interval,
            'last_update': 0.0
        }
        
        # Start streaming thread if not already running
        if self.streaming_thread is None or not self.streaming_thread.is_alive():
            self.streaming_thread = threading.Thread(
                target=self._streaming_loop,
                name="prediction-streamer",
                daemon=True
            )
            self.streaming_thread.start()
        
        logger.info(f"Started streaming predictions with ID {stream_id}")
        return stream_id
    
    def stop_streaming(self, stream_id: str) -> bool:
        """
        Stop streaming predictions.
        
        Args:
            stream_id: Stream ID
            
        Returns:
            True if successful, False otherwise
        """
        if stream_id in self.streaming_callbacks:
            del self.streaming_callbacks[stream_id]
            logger.info(f"Stopped streaming predictions with ID {stream_id}")
            return True
        else:
            logger.warning(f"Stream with ID {stream_id} not found")
            return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get engine metrics.
        
        Returns:
            Dictionary with metrics
        """
        # Update queue size
        self.metrics['queue_size_history'].append(self.request_queue.qsize())
        
        # Calculate average queue size
        avg_queue_size = sum(self.metrics['queue_size_history']) / len(self.metrics['queue_size_history']) \
            if self.metrics['queue_size_history'] else 0
        
        # Calculate average processing time
        avg_processing_time = sum(self.metrics['processing_time_history']) / len(self.metrics['processing_time_history']) \
            if self.metrics['processing_time_history'] else 0
        
        return {
            'total_requests': self.metrics['total_requests'],
            'total_predictions': self.metrics['total_predictions'],
            'current_queue_size': self.request_queue.qsize(),
            'avg_queue_size': avg_queue_size,
            'avg_processing_time': avg_processing_time,
            'cache_size': len(self.result_cache),
            'active_streams': len(self.streaming_callbacks),
            'worker_count': len(self.workers)
        }
    
    def _worker_loop(self):
        """Worker loop for processing prediction requests."""
        logger.info(f"Started prediction worker {threading.current_thread().name}")
        
        while not self.stop_event.is_set():
            try:
                # Get request from queue with timeout
                try:
                    request = self.request_queue.get(timeout=0.1)
                except queue.Empty:
                    continue
                
                # Process request
                start_time = time.time()
                
                try:
                    # Make prediction
                    if isinstance(request.data, dict) and 'price_data' in request.data:
                        # Assume this is a liquidity analysis request
                        predictions = self.prediction_engine.predict_level_strength(
                            price_data=request.data['price_data'],
                            liquidity_levels=request.data.get('liquidity_levels', {}),
                            options_data=request.data.get('options_data'),
                            volume_profile=request.data.get('volume_profile'),
                            gex_data=request.data.get('gex_data')
                        )
                    else:
                        # Generic prediction
                        predictions = {'error': 'Unsupported data format'}
                    
                    # Calculate processing time
                    processing_time = time.time() - start_time
                    
                    # Create result
                    result = PredictionResult(
                        request_id=request.request_id,
                        predictions=predictions,
                        processing_time=processing_time,
                        metadata=request.metadata
                    )
                    
                    # Add to cache
                    self._add_to_cache(result)
                    
                    # Call callback if provided
                    if request.callback:
                        try:
                            request.callback(request.request_id, predictions)
                        except Exception as e:
                            logger.error(f"Error in callback for request {request.request_id}: {str(e)}")
                    
                    # Update metrics
                    self.metrics['total_predictions'] += 1
                    self.metrics['processing_time_history'].append(processing_time)
                    
                    logger.debug(f"Processed prediction request {request.request_id} in {processing_time:.3f} seconds")
                    
                except Exception as e:
                    logger.error(f"Error processing prediction request {request.request_id}: {str(e)}")
                
                finally:
                    # Mark task as done
                    self.request_queue.task_done()
                
            except Exception as e:
                logger.error(f"Error in prediction worker: {str(e)}")
        
        logger.info(f"Stopped prediction worker {threading.current_thread().name}")
    
    def _streaming_loop(self):
        """Streaming loop for continuous predictions."""
        logger.info("Started prediction streaming thread")
        
        while not self.stop_event.is_set() and self.streaming_callbacks:
            try:
                current_time = time.time()
                
                # Check each stream
                for stream_id, stream_info in list(self.streaming_callbacks.items()):
                    # Check if it's time to update
                    if current_time - stream_info['last_update'] >= stream_info['interval']:
                        try:
                            # Get data from provider
                            data = stream_info['data_provider']()
                            
                            # Submit prediction request
                            request_id = self.predict(
                                data=data,
                                callback=stream_info['callback'],
                                metadata={'stream_id': stream_id}
                            )
                            
                            # Update last update time
                            stream_info['last_update'] = current_time
                            
                        except Exception as e:
                            logger.error(f"Error in streaming prediction for stream {stream_id}: {str(e)}")
                
                # Sleep briefly
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in prediction streaming thread: {str(e)}")
        
        logger.info("Stopped prediction streaming thread")
    
    def _update_loop(self):
        """Update loop for updating predictions."""
        logger.info("Started prediction update thread")
        
        while not self.stop_event.is_set():
            try:
                # Sleep for update interval
                time.sleep(self.update_interval)
                
                # Implement incremental model updates
                self._perform_incremental_update()
                
            except Exception as e:
                logger.error(f"Error in prediction update thread: {str(e)}")
        
        logger.info("Stopped prediction update thread")
    
    def _add_to_cache(self, result: PredictionResult):
        """
        Add result to cache.
        
        Args:
            result: PredictionResult object
        """
        # Add to cache
        self.result_cache[result.request_id] = result
        self.result_timestamps.append(result.request_id)
        
        # Remove old results if cache is full
        while len(self.result_cache) > self.cache_size:
            old_id = self.result_timestamps.popleft()
            if old_id in self.result_cache:
                del self.result_cache[old_id]

    def _perform_incremental_update(self):
        """
        Perform incremental model updates based on recent data and feedback.

        This method implements online learning to continuously improve model performance
        without requiring full retraining.
        """
        try:
            # Check if we have enough new data for an update
            if not self._should_perform_update():
                return

            logger.info("Starting incremental model update")

            # Collect recent prediction data for training
            recent_data = self._collect_recent_training_data()

            if recent_data is None or len(recent_data) == 0:
                logger.warning("No recent data available for incremental update")
                return

            # Perform incremental updates for each model
            for model_name, model in self.models.items():
                try:
                    if hasattr(model, 'partial_fit'):
                        # Use partial_fit for models that support incremental learning
                        self._incremental_update_sklearn_model(model_name, model, recent_data)
                    elif hasattr(model, 'train_on_batch'):
                        # Use train_on_batch for neural network models
                        self._incremental_update_neural_model(model_name, model, recent_data)
                    else:
                        # For models without incremental learning, collect data for batch update
                        self._schedule_batch_update(model_name, recent_data)

                except Exception as e:
                    logger.error(f"Error updating model {model_name}: {e}")

            # Update model performance metrics
            self._update_model_metrics()

            logger.info("Completed incremental model update")

        except Exception as e:
            logger.error(f"Error in incremental model update: {e}")

    def _should_perform_update(self) -> bool:
        """Check if incremental update should be performed."""
        try:
            # Check if enough time has passed since last update
            current_time = time.time()
            if hasattr(self, 'last_update_time'):
                time_since_update = current_time - self.last_update_time
                if time_since_update < self.config.get('min_update_interval', 3600):  # 1 hour default
                    return False

            # Check if we have enough new predictions
            if len(self.result_cache) < self.config.get('min_samples_for_update', 10):
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking update conditions: {e}")
            return False

    def _collect_recent_training_data(self) -> Optional[Dict[str, Any]]:
        """Collect recent prediction data for training."""
        try:
            # Collect features and targets from recent predictions
            features = []
            targets = []

            for result in list(self.result_cache.values())[-50:]:  # Last 50 predictions
                if hasattr(result, 'features') and hasattr(result, 'actual_outcome'):
                    features.append(result.features)
                    targets.append(result.actual_outcome)

            if len(features) == 0:
                return None

            return {
                'features': np.array(features),
                'targets': np.array(targets),
                'sample_count': len(features)
            }

        except Exception as e:
            logger.error(f"Error collecting training data: {e}")
            return None

    def _incremental_update_sklearn_model(self, model_name: str, model, training_data: Dict[str, Any]):
        """Update sklearn model using partial_fit."""
        try:
            features = training_data['features']
            targets = training_data['targets']

            # Perform partial fit
            model.partial_fit(features, targets)

            logger.info(f"Updated sklearn model {model_name} with {len(features)} samples")

        except Exception as e:
            logger.error(f"Error updating sklearn model {model_name}: {e}")

    def _incremental_update_neural_model(self, model_name: str, model, training_data: Dict[str, Any]):
        """Update neural network model using train_on_batch."""
        try:
            features = training_data['features']
            targets = training_data['targets']

            # Perform batch training
            loss = model.train_on_batch(features, targets)

            logger.info(f"Updated neural model {model_name} with {len(features)} samples, loss: {loss}")

        except Exception as e:
            logger.error(f"Error updating neural model {model_name}: {e}")

    def _schedule_batch_update(self, model_name: str, training_data: Dict[str, Any]):
        """Schedule batch update for models without incremental learning."""
        try:
            # Store data for later batch update
            if not hasattr(self, 'pending_batch_updates'):
                self.pending_batch_updates = {}

            if model_name not in self.pending_batch_updates:
                self.pending_batch_updates[model_name] = []

            self.pending_batch_updates[model_name].append(training_data)

            logger.info(f"Scheduled batch update for model {model_name}")

        except Exception as e:
            logger.error(f"Error scheduling batch update for {model_name}: {e}")

    def _update_model_metrics(self):
        """Update model performance metrics after incremental update."""
        try:
            # Update timestamp
            self.last_update_time = time.time()

            # Update metrics
            if not hasattr(self, 'update_count'):
                self.update_count = 0
            self.update_count += 1

            logger.debug(f"Updated model metrics, total updates: {self.update_count}")

        except Exception as e:
            logger.error(f"Error updating model metrics: {e}")
